# Kafka

## Instalação e Configuração

1. Para que o Kafka funcione corretamente, é preciso ter a biblioteca `librdkafka` que já será instalada automaticamente no build do projeto;
2. Assegure que sua configuração de conexão com o Kafka esteja configurada corretamente no `appsettings.json` da aplicação. Em caso de teste em desenvolvimento, a configuração é feita no `appsettings.Development.json`;
3. Não esqueça de configurar os IPs dos domínios utilizados na configuração `KafkaSettings.BootstrapServers` (appsettings.json) no `C:\Windows\System32\drivers\etc\hosts` (Windows) ou `/etc/hosts` (Linux).

```
********** tst-kafka-001.ons.org.br
********** tst-kafka-002.ons.org.br
********** tst-kafka-003.ons.org.br
```

## Utilização em código

Na implementação, é possível injetar o `IKafkaClient` que possui métodos para consumir e produzir mensagens para um determinado tópico pretendido.

Produzindo uma nova mensagem para um tópico:

```csharp
string topic = "ONS.SINAPSE.STATUS_SOLICITACAO.GERDIN";

var solicitacao = new SolicitacaoDto
{
    CodigoDaSolicitacao = "20231117_0001",
    StatusId = 1,
    Status = "Pendente",
};

string key = solicitacao.CodigoDaSolicitacao;
string message = JsonConvert.SerializeObject(solicitacao);

Dictionary<string, string> headers = new()
{
    { "type", "Solicitacao" }
};

await _kafkaClient.SendAsync(topic, key, message, headers, cancellationToken);
```

Consumindo última mensagem do tópico:

```csharp
string topic = "ONS.SINAPSE.STATUS_SOLICITACAO.GERDIN";

var message = await _kafkaClient.GetLastMessageAsync("ONS.SAGER.SINAPSE.STATUS_SOLICITACAO.GERDIN", cancellationToken);
```

## Tópicos disponíveis

| Tópico | Descrição                                                                                                                                                                                                                 |
|---|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| `ONS.SAGER.SINAPSE.STATUS_SOLICITACAO.{SISTEMA_ORIGEM}` | Notifica as trocas de status da solicitação para o sistema de origem, onde o SISTEMA_ORIGEM é o sistema que criou a solicitação e deseja acompanhar o status dela. Hoje somente só temos o GERDIN como sistema de origem. |
| `ONS.SAGER.SINAPSE.CADASTRO_SOLICITACAO}` | Recebe as solicitações para cadastro no SINapse. |
| `ONS.SAGER.SINAPSE.TROCA_STATUS_SOLICITACAO.{SISTEMA_ORIGEM}` | Mensagens de troca de status das solicições, onde o SISTEMA_ORIGEM é o sistema que criou a solicitação e deseja alterar o status dela. Hoje somente só temos o GERDIN como sistema de origem. |
| `ONS.SAGER.SINAPSE.STATUS_AGENTE` | Notifica a troca de status do agente, se ele está online ou offline. |