# Configuração de Dataset

## Padrão para datasets

| Campo               | Tipo              | Descrição                                                                    | Valores possíveis                                      |
|---------------------|-------------------|------------------------------------------------------------------------------|--------------------------------------------------------|
| **id**              | `string`          | Texto identificador único do item dentro da lista.                           | Qualquer string                                        |
| **destination**     | `ObjetoDeManobra` | Destino da solicitação.                                                      | Objeto De Manobra                                      |
| **description**     | `string`          | Texto usado para montar a  mensagem da solicitação.                          | Qualquer string                                        |
| **label**           | `string`          | Texto que o usuário enxerga na hora de selecionar.                           | Qualquer string                                        |
| **defaultValue**    | `bool`            | Informa que o item será selecionado por padrão.                              | `true` para selecionado; `false` para não selecionado. |
| **local**           | `ObjetoDeManobra` | Informa o local onde será executada a operação (usina, equipamento, uge...). | Objeto De Manobra                                      |
| **encaminharPara**  | `ObjetoDeManobra` | Informa para quem a solicitação deverá ser encaminhada.                      | Objeto De Manobra                                      |

#### Objeto De Manobra
| Campo            | Tipo     | Descrição                                                                    | Valores possíveis                                      |
|------------------|----------|------------------------------------------------------------------------------|--------------------------------------------------------|
| **Codigo**       | `string` | Código do Objeto.                                                            | Qualquer string                                        |
| **Nome**         | `string` | Nome do objeto.                                                              | Qualquer string                                        |

### Segue o padrão adotado:
```
{
    "id": "PIUBE",
    "destination": {
        "code": "NE",
        "name": "COSR-NE"
    },
    "description": "Boa Esperança",
    "label": "Boa Esperança - CHESF - COSR-NE - [Online]",
    "optional": "",
    "defaultValue": false,
    "local": {
        "code": "PIUBE",
        "name": "Boa Esperança"
    },
    "encaminharPara": {
        "code": "CHF",
        "name": "CHESF"
    }
}
```

## Datasets disponíveis

Segue os datasets disponíveis para configuração da propriedade `datasetUrl`:

| Nome                   | URL                                                                                                                      | Descrição                                                                                      |
| ---------------------- | ------------------------------------------------------------------------------------------------------------------------ | ---------------------------------------------------------------------------------------------- |
| Agentes                | `dataset/solicitacao/agente?query=origem_codigo==CODIGO_DO_CENTRO`                                                       | Retorna os agentes com base no centro CODIGO\_DO\_CENTRO informado.                            |
| Banco capacitor        | `dataset/solicitacao/equipamento?query=tipo==banco-capacitor;origem_codigo==CODIGO_DO_CENTRO`                            | Retorna os equipamentos do tipo banco capacitor com origem CODIGO\_DO\_CENTRO.                 |
| Compensador            | `dataset/solicitacao/equipamento?query=tipo==compensador;origem_codigo==CODIGO_DO_CENTRO`                                | Retorna os equipamentos do tipo compensador com origem CODIGO\_DO\_CENTRO.                     |
| Reator                 | `dataset/solicitacao/equipamento?query=tipo==reator;origem_codigo==CODIGO_DO_CENTRO`                                     | Retorna os equipamentos do tipo reator com origem CODIGO\_DO\_CENTRO.                          |
| Usinas por CAG         | `dataset/solicitacao/conjunto_usina?query=operacag==true;origem_codigo==CODIGO_DO_CENTRO;tipoFonte==TIPO_DE_FONTE`       | Retorna usinas operando por CAG com tipo de fonte TIPO\_DE\_FONTE e origem CODIGO\_DO\_CENTRO. |
| Conjunto de usinas     | `dataset/solicitacao/conjunto_usina?query=tipo==conjunto-usina;origem_codigo==CODIGO_DO_CENTRO;tipoFonte==TIPO_DE_FONTE` | Retorna conjuntos de usinas do tipo TIPO\_DE\_FONTE com origem CODIGO\_DO\_CENTRO.             |
| Unidades geradoras     | `dataset/solicitacao/unidade_geradora?query=origem_codigo==CODIGO_DO_CENTRO`                                             | Retorna as unidades geradoras com origem CODIGO\_DO\_CENTRO.                                   |
| Usina                  | `dataset/solicitacao/conjunto_usina?query=tipo==usina;origem_codigo==CODIGO_DO_CENTRO;tipoFonte==TIPO_DE_FONTE`          | Retorna usinas do tipo TIPO\_DE\_FONTE com origem CODIGO\_DO\_CENTRO.                          |
| Elo                    | `dataset/solicitacao/elo?query=origem_codigo==CODIGO_DO_CENTRO`                                                          | Retorna os elos com origem CODIGO\_DO\_CENTRO.                                                 |
| Instalação compensador | `dataset/solicitacao/instalacao_compensador?query=origem_codigo==CODIGO_DO_CENTRO`                                       | Retorna instalações de compensadores com origem CODIGO\_DO\_CENTRO.                            |
| Transformação          | `dataset/solicitacao/transformacao?query=origem_codigo==CODIGO_DO_CENTRO`                                                | Retorna dados de transformações com origem CODIGO\_DO\_CENTRO.                                 |
