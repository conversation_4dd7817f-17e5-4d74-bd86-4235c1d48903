# Padrão para criação de datasets através de views

## Convenção de nome

A convenção para criação de views é a seguinte:

vw_{nome_da_view}_dataset

Onde:
- `vw`: Prefixo que indica que a view será usada como dataset;
- `nome_da_view`: Nome da view que será criada. Exemplo: usina
- `dataset`: Palavra reservada que indica que a view será usada como dataset.

## Colunas obrigatórias para todas as views de dataset
Os campos abaixo são obrigatórios para todas as views, alguns deles podem ser nulos:
- `id`: Campo string, não nulo.
- `origem_codigo`: Campo string, não nulo.
- `origem_nome`: Campo string, não nulo.
- `destino_codigo`: Campo string, não nulo.
- `destino_nome`: Campo string, não nulo.
- `definirstatus`: Campo string, pode ser nulo se não for necessário definir status de ‘Online’ e ‘Offline’.
- `local_codigo`: Campo string, pode ser nulo se não for necessário informar o local da operação.
- `local_nome`: Campo string, pode ser nulo se não for necessário informar o local da operação.
- `label`: Campo string, não nulo.
- `descricao`: Campo string, não nulo.
- `encaminhar_codigo`: Campo string, pode ser nulo se não for necessário encaminhar para um destino específico.
- `encaminhar_nome`: Campo string, pode ser nulo se não for necessário encaminhar para um destino específico.
- `valorpadrao`: Campo string `true` ou `false` ou null.
- `opcional`: Campo string, pode ser nulo. Atualmente não utilizado pelo cadastro de solicitações.
- `ocultar_cadastro_visao`: Campo string `true` ou `false`. Indica se o item será exibido no cadastro de visão.
- `tipo`: Campo string, não nulo. Tipo de dataset. Exemplo: usina, conjunto-usina, elo, etc.
- `dataset_label`: Campo string, não nulo. Label que será exibido no front-end no combo de seleção de locais de operação.

### Exemplo

```sql
CREATE VIEW vw_agente_dataset AS
SELECT DISTINCT
    TRIM(age.age_id) AS id,
    TRIM(cos.cos_id) AS origem_codigo,
    TRIM(cos.nomecurto) AS origem_nome,
    TRIM(age.age_id) AS destino_codigo,
    TRIM(age.nomecurto) AS destino_nome,
    TRIM(age.age_id) AS definirstatus,
    null AS local_codigo,
    null AS local_nome,
    TRIM(age.nomecurto) AS label,
    TRIM(age.nomecurto) AS descricao,
    NULL AS encaminhar_codigo,
    NULL AS encaminhar_nome,
    NULL AS valorpadrao,
    NULL AS opcional,
    'false' AS ocultar_cadastro_visao,
    'agente' AS tipo,
    'Agentes' AS dataset_label
FROM age
         INNER JOIN eqp ON age.age_id = eqp.age_id_oper
         INNER JOIN cos ON eqp.cos_id = cos.cos_id
    AND (cos.dtdesativa IS NULL OR cos.dtdesativa > GETDATE())
    AND age.dtentrada IS NOT NULL
WHERE age.dtentrada IS NOT NULL AND (age.dtdesativa IS NULL OR age.dtdesativa > GETDATE()) and pais_id = 'BR'
```

## Sintaxe para criação de query Rsql ao consumir dataset

A query para consumo de dataset deve seguir a sintaxe abaixo:

`/dataset/solicitacao/conjunto_usina?query=tipo==usina;origem_codigo==CN;tipoFonte==UHE;opercag==false`

Onde:

* `dataset/solicitacao`:Nome do endpoint que retorna o dataset.
* `conjunto_usina`: Nome da view que será consumida.
* `query`: Parâmetro que indica que será realizada uma query na view.
* `tipo`: Tipo de dataset. Exemplo: usina, conjunto-usina, elo, etc.
* `origem_codigo`: Código da origem. Exemplo: CN, S, SE, N, NE.
* `tipofonte`: Tipo de fonte. Exemplo: UHE, UTE, etc.
* `opercag`: Indica se a usina opera por CAG. `true` ou `false`.

**IMPORTANTE**: O utilizador pode informar quantos parâmetros quiser, desde que siga a sintaxe acima e seja uma coluna existentes da view.

### Operadores lógicos RSQL

| Operador | Descrição        |
| -------- | ---------------- |
| `==`     | Igual a          |
| `!=`     | Diferente de     |
| `>`      | Maior que        |
| `<`      | Menor que        |
| `>=`     | Maior ou igual a |
| `<=`     | Menor ou igual a |
| `=in=`   | Contém           |
| `=out=`  | Não contém       |
| `=like=` | Similar a        |
| `;`      | E lógico         |
| `,`      | Ou lógico        |

### Exemplos de querys com operadores lógicos

* `?query=tipo==usina;origem_codigo==CN;tipoFonte==UHE;opercag==false`

### Exemplos adicionais de uso dos operadores

#### `==` (Igual a)

```
/dataset/solicitacao/usina?query=tipo==usina;origem_codigo==SE
```

#### `!=` (Diferente de)

```
/dataset/solicitacao/usina?query=tipo!=elo;origem_codigo!=NE
```

#### `>` (Maior que)

```
/dataset/solicitacao/usina?query=capacidadeInstalada>1000
```

#### `<` (Menor que)

```
/dataset/solicitacao/usina?query=anoEntradaServico<2010
```

#### `>=` (Maior ou igual a)

```
/dataset/solicitacao/usina?query=potenciaDisponivel>=500
```

#### `<=` (Menor ou igual a)

```
/dataset/solicitacao/usina?query=anoEntradaServico<=2020
```

#### `=in=` (Contém)

```
/dataset/solicitacao/usina?query=origem_codigo=in=(SE,S,NE)
```

#### `=out=` (Não contém)

```
/dataset/solicitacao/usina?query=tipoFonte=out=(UHE,PCH)
```

#### `=like=` (Similar a – com curinga `%`)

```
/dataset/solicitacao/usina?query=destino_nome=like=U%
```

#### `;` (E lógico)

```
/dataset/solicitacao/usina?query=tipo==usina;origem_codigo==N;tipoFonte==UTE
```

#### `,` (Ou lógico)

```
/dataset/solicitacao/usina?query=tipoFonte==UHE,tipoFonte==PCH
```

#### Exemplo completo com múltiplos operadores

```
/dataset/solicitacao/usina?query=tipo==usina;origem_codigo=in=(SE,S);capacidadeInstalada>500;tipoFonte!=UTE
```

