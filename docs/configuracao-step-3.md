# Configuração do step 3

- [Configuração do step 3](#configuração-do-step-3)
  - [Estrutura de pastas](#estrutura-de-pastas)
  - [Estrutura do arquivo](#estrutura-do-arquivo)
    - [Gerais](#gerais)
    - [Somente para Combos](#somente-para-combos)
    - [Exemplo](#exemplo)
  - [Estrutura de pastas](#estrutura-de-pastas-1)
    - [Exemplo](#exemplo-1)

## Estrutura de pastas
>>>>>>> sprints/sprint-29

Os arquivos de configuração do step 3 estão dispostos no mesmo nível dos demais steps, dentro da pasta do tipo de solicitação em que pertencem.

**IMPORTANTE**: A nomenclatura do step 3 precisa seguir a convensão do sufixo `step-3.json` no arquivo, conforme demostrado na imagem a seguir. Sem seguir essa convensão o sistema não identificará esse arquivo como sendo um template de step 3.

![Estrutura](images/diretorio-step-3.png)

## Estrutura do arquivo

Em relação a estrutura do arquivo, confira abaixo uma tabela com instruções do que é e como se configura cada campo do arquivo do step 3: 

### Gerais

|Campo | Tipo              | Descrição                                              | Valores possíveis                                                                                 |
|---|-------------------|--------------------------------------------------------|---------------------------------------------------------------------------------------------------|
|**fields** | `array`           | Array com as configurações dos campos.                 | Array de objetos                                                                                  |
|**label** | `string`          | Label do campo (texto que fica a cima do campo).       | Qualquer string                                                                                   |
|**fieldName** | `string`          | Nome/identificador que identifica o campo.             | Qualquer string                                                                                   |
|**description** | `string`          | Descrição do campo.       | Qualquer string                                                                                   |
|**component** | `string`          | Determina qual componente será usado na renderização.  | `label`, `input`, `combo` ou `expanded_combo`                                                      |
|**style** | `object`          | Determina os estilos que o campo/componente terão. Os estilos seguem o padrão do CSS. | Um objeto de propriedades CSS no formato chave/valor                                              |
|**type** | `string`          | Identifica o tipo do campo.                            | `select`, `number`, `text`, `time`                                                                |
|**required** | `bool`            | Habilita a obrigatoriedade do campo.                   | `true` para obrigatório; `false` para opcional.                                                   |
|**readonly** | `bool`            | Habilita campo para ser apenas leitura / não editável.                   | `true` para apenas leitura; `false` para habilitar edição do campo.                               |
|**defaultValue** | `string`, `number` | Valor padrão do campo                                  | Qualquer valor string ou número                                                                   |
|**min** | `number` | Mínimo valor válido                                    | Qualquer valor numérico. Utilizado com campos do tipo `input` e type `number`                     |
|**max** | `number` | Máximo valor válido                                    | Qualquer valor numérico. Utilizado com campos do tipo `input` e type `number`                     |
|**step** | `number` | Valor do incremento numérico do campo do tipo `number` | Qualquer valor numérico. Utilizado com campos do tipo `input` e type `number`                     |
|**minLength** | `number` | Validação do tamanho mínimo do texto digitado `number` | Qualquer valor numérico mior que zero. Utilizado com campos do tipo `input` e type `text`         |
|**maxLength** | `number` | Validação do tamanho máximo do texto digitado `number` | Qualquer valor numérico mior que zero. Utilizado com campos do tipo `input` e type `text`         |
| **datasetUrl**  | `string` | URL que retorna os dados do combo.                        | URL que retorna um array de objetos que seguem o [Padrão para datasets](/configuracao-dataset.md) |
| **dataset**     | `array`  | Lista que representa o dataset que será exibido no front. | Um array/lista que siga o [Padrão para datasets](/configuracao-dataset.md)                        |

| **datasetUrl**  | `string` | URL que retorna os dados do combo.                        | URL que retorna um array de objetos que seguem o [Padrão para datasets](/configuracao-dataset.md) |
| **dataset**     | `array`  | Lista que representa o dataset que será exibido no front. | Um array/lista que siga o [Padrão para datasets](/configuracao-dataset.md)           |

### Somente para Combos

| Campo           | Tipo     | Descrição                                                 | Valores possíveis                                                                              |
|-----------------|----------|-----------------------------------------------------------|------------------------------------------------------------------------------------------------|
| **multiple**    | `bool`   | Habilita a multipla seleção no combo.                     | `true` para habilitar; `false` para desabilitar.                                               |
| **searchable**  | `bool`   | Habilita campo de pesquisa em combos.                     | `true` para habilitar; `false` para desabilitar.                                               |


**IMPORTANTE**
|------------------|----------|-----------------------------------------------------|--------------------------------------------------------|
| **id**           | `string` | Texto identificador único do item dentro da lista.  | Qualquer string                                        |
| **destination**  | `string` | Destino da solicitação.                             | Qualquer string                                        |
| **description**  | `string` | Texto usado para montar a  mensagem da solicitação. | Qualquer string                                        |


### Exemplo

*Exemplo é meramente ilustrativo e não compreende a regra de negócio atual.*

{
    "step3": {
        "fields": [
              {
              "label": "Usina",
              "description": "",
              "fieldName": "usina-online",
              "component": "combo",
              "type": "select",
              "required": true,
              "multiple": true,
              "searchable": true,
              "readonly": false,
              "messageOutput": "",
              "defaultValue": "",
              "min": null,
              "max": null,
              "step": null,
              "minLength": null,
              "maxLength": null,
              "datasetUrl": null,
              "dataset": [
                    { 
                        "id": "NE",
                        "destination": "NE",
                        "description": "Nordeste",
                        "label": "COSR-NE"
                        "defaultValue": false
                    },
                    { 
                        "id": "S",
                        "destination": "S",
                        "description": "Sul",
                        "label": "COSR-S",
                        "defaultValue": false
                    }
                ]
            },
            {
                "label": "Usina",
                "description": "",
                "fieldName": "usina-online",
                "component": "combo",
                "type": "text",
                "required": true,
                "multiple": true,
                "searchable": true,
                "readonly": false,
                "messageOutput": "",
                "defaultValue": "",
                "min": null,
                "max": null,
                "step": null,
                "minLength": null,
                "style": {
                    "height": "100px",
                    "width": "100%"
                },
    - [Exemplo](#exemplo)

## Estrutura de pastas

|**style** | `object`          | Determina os estilos que o campo/componente terão. Os estilos seguem o padrão do CSS. |  Um objeto de propriedades CSS no formato chave/valor   |
| **datasetUrl**  | `string` | URL que retorna os dados do combo.                        | URL que retorna um array de objetos que seguem o [Padrão para datasets](/configuracao-dataset.md) |
| **dataset**     | `array`  | Lista que representa o dataset que será exibido no front. | Um array/lista que siga o [Padrão para datasets](/configuracao-dataset.md)           |
| **datasetUrl**  | `string` | URL que retorna os dados do combo.                        | URL que retorna um array de objetos que seguem o [Padrão para datasets](/configuracao-dataset.md) |
| **dataset**     | `array`  | Lista que representa o dataset que será exibido no front. | Um array/lista que siga o [Padrão para datasets](/configuracao-dataset.md)           |

### Exemplo

*Exemplo é meramente ilustrativo e não compreende a regra de negócio atual.*

```
              "type": "select",
                "style": {
                    "height": "100px",
                    "width": "100%"
                },
                "type": "select",
                "datasetUrl": "https://api.devsinapse.ons.org.br/api/usina-online?centro=NE",
                "dataset": []
            }

        ]
    }
}
```