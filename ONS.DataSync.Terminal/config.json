{"SyncDestino": {"Endereco": "https://sinapse-dados-api-tst.apps.ocpd.ons.org.br/api/sync?executor=DataSync", "Headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/108.0.0.0 Safari/537.36", "X-Token": "99C29281-8B0C-468C-92D2-660D0361FFC6"}}, "QuantidadeDeRegistrosPorRequest": 1000, "Comandos": {"CargaCompleta": "--completa", "Inclusao": "--inclus<PERSON>", "Alteracao": "--<PERSON><PERSON><PERSON>"}, "Databases": [{"Provider": "Informix", "Ativo": true, "ConnectionString": "Server=10.65.215.61:5303;Database=bd_tecn;User ID=ccardoso;PWD=teste123;Persist Security Info=True ProviderName=IBM.Data.DB2", "Tabelas": [{"NomeTabela": "age", "Query": "select * from age", "PropriedadesChave": ["age_id"]}, {"NomeTabela": "areareg", "Query": "select * from areareg", "PropriedadesChave": ["areareg_id"]}, {"NomeTabela": "cos", "Query": "select * from cos", "PropriedadesChave": ["cos_id"]}, {"NomeTabela": "tb_conjuntousina", "Query": "select * from tb_conjuntousina", "PropriedadesChave": ["id_conjuntousina"]}, {"NomeTabela": "eqp", "Query": "select * from eqp where util_id like '%T%' and tpeqp_id in ('TR2', 'TR3', 'CSI', 'CRE', 'BCP', 'REA', 'UGE', 'FIL') and dtdesativa is null", "PropriedadesChave": ["eqp_id", "tpeqp_id"]}, {"NomeTabela": "tr2", "Query": "select * from tr2 where id_tpfuncaotr = 'TR'", "PropriedadesChave": ["eqp_id", "tpeqp_id"]}, {"NomeTabela": "tr3", "Query": "select * from tr3 where id_tpfuncaotr = 'TR'", "PropriedadesChave": ["eqp_id", "tpeqp_id"]}, {"NomeTabela": "ins", "Query": "select * from ins", "PropriedadesChave": ["ins_id"]}, {"NomeTabela": "tb_nivel<PERSON><PERSON>", "Query": "select * from tb_nivel<PERSON>ao", "PropriedadesChave": ["id_niveltensao"]}, {"NomeTabela": "tb_us<PERSON><PERSON><PERSON><PERSON>", "Query": "select * from tb_usinivel<PERSON>ao", "PropriedadesChave": ["usi_id", "id_niveltensao"]}, {"NomeTabela": "tb_conju<PERSON><PERSON><PERSON><PERSON><PERSON>", "Query": "select * from tb_conjusinanivel<PERSON>ao", "PropriedadesChave": ["id_conjuntousina"]}, {"NomeTabela": "uge", "Query": "select * from uge", "PropriedadesChave": ["uge_id"]}, {"NomeTabela": "usi", "Query": "select * from usi", "PropriedadesChave": ["usi_id"]}, {"NomeTabela": "bacia", "Query": "select * from bacia", "PropriedadesChave": ["bacia_id"]}, {"NomeTabela": "ap<PERSON>", "Query": "select * from aprov", "PropriedadesChave": ["aprov_id"]}, {"NomeTabela": "tpuge", "Query": "select * from tpuge", "PropriedadesChave": ["tpuge_id"]}, {"NomeTabela": "res", "Query": "select * from res", "PropriedadesChave": ["res_id"]}, {"NomeTabela": "est", "Query": "select * from est where dtdesativa is null and util_id like '%T%'", "PropriedadesChave": ["est_id"]}, {"NomeTabela": "elo", "Query": "select * from elo where dtentrada is not null and dtdesativa is null and ido_ons not in ('RSGBI1_140_ELRSGBI12', 'RSGBI2_140_ELRSGBI24') and nopo like 'BP%'", "PropriedadesChave": ["elo_id"]}]}, {"Provider": "SqlServer", "Ativo": true, "ConnectionString": "database=Interlocutores;User Id=useretlsinapse;Password=********;server=tst-sql2019-07;MultipleActiveResultSets=true;TrustServerCertificate=true", "Tabelas": [{"NomeTabela": "tb_interlocutor", "Query": "select * from tb_interlocutor", "PropriedadesChave": ["id_interlocutor"]}, {"NomeTabela": "tb_centrooperacaoagente", "Query": "select * from tb_centrooperacaoagente", "PropriedadesChave": ["id_centrooperacaoagente"]}, {"NomeTabela": "tb_interlocutorins", "Query": "select * from tb_interlocutorins", "PropriedadesChave": ["id_interlocutorins"]}, {"NomeTabela": "tb_aux_ins", "Query": "select * from tb_aux_ins", "PropriedadesChave": ["ins_id"]}, {"NomeTabela": "tb_aux_agenteinstituicao", "Query": "select * from tb_aux_agenteinstituicao", "PropriedadesChave": ["id_agente"]}]}]}