@echo off
REM Navega até o diretório onde o script está localizado.
cd /d "%~dp0"

REM Define o caminho do diretório clidriver\bin.
set CLIDRIVER_BIN=%~dp0clidriver\bin

REM Adiciona o diretório clidriver\bin ao PATH apenas para esta execução.
set PATH=%CLIDRIVER_BIN%;%PATH%

REM Verifica se o executável existe.
if not exist "ONS.DataSync.Terminal.exe" (
    echo Erro: O arquivo ONS.DataSync.Terminal.exe não foi encontrado no diretório atual.
    exit /b 1
)

REM Verifica se o arquivo config.json existe.
if not exist "config.json" (
    echo Erro: O arquivo config.json não foi encontrado no diretório atual.
    exit /b 1
)

REM Executa o executável e passa todos os argumentos recebidos pelo script .bat.
"ONS.DataSync.Terminal.exe" %*

REM Opcional: Captura o código de saída do executável e o retorna como código de saída do script .bat.
exit /b %ERRORLEVEL%
