using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using ONS.DataSync.Clients;
using ONS.DataSync.Configs;
using ONS.DataSync.Constants;
using ONS.DataSync.Data;
using ONS.DataSync.Sync;

namespace ONS.DataSync.Terminal;

public class Program
{
    static async Task Main(string[] args)
    {
        var logger = CreateLogger();

        var configuration = await GetConfigurationAsync();
        
        if (!args.Any())
        {
            Console.WriteLine($"Para executar o DataSync é necessário passar por parâmetro os nomes das tabelas para realizar uma carga parcial ou o parâmetro {configuration.Comandos.CargaCompleta} para realizar a carga completa (consulte o README para mais detalhes).");
            Console.ReadKey();
            return;
        }

        var argumentos = ParseArgs(args);

        if (argumentos
                .Count(x => 
                    x.Key == configuration.Comandos.Alteracao || 
                    x.Key == configuration.Comandos.Inclusao) > 1)
        {
            Console.WriteLine("Apenas uma operação é permitida por vez.");
            Console.ReadKey();
            return;
        }

        var acao = GetAcao(argumentos, configuration);

        var hasProvider = argumentos.TryGetValue("provider", out var provider);
        
        var providersForExecute =
            hasProvider
                ? configuration
                    .Databases
                    .Where(x => ParseArgsProvider(provider!).Contains(x.Provider, StringComparer.OrdinalIgnoreCase))
                    .ToList()
                : configuration
                    .Databases
                    .Where(x => x.Ativo)
                    .ToList();
        
        logger.LogInformation("Serão usados os seguintes parâmetros para a execução do processo de sincronização de dados.\n - Acao: {0}\n - Providers: {1}", acao, provider);
        
        await ExecuteAsync(acao, argumentos, providersForExecute, configuration, logger);
    }

    private static async Task ExecuteAsync(string acao, IReadOnlyDictionary<string, string> argumentos, List<DatabaseConfiguration> providers, DataSyncConfig configuration, ILogger logger)
    {
        try
        {
            logger.LogInformation("Iniciando Sincronização.");
            foreach (var databaseConfiguration in providers)
            {
                var cargaCompleta =
                    argumentos.ContainsKey(configuration.Comandos.CargaCompleta) ||
                    !databaseConfiguration.Tabelas.Any(x => argumentos.ContainsKey(x.NomeTabela));
                
                var tables =
                    cargaCompleta
                        ? databaseConfiguration.Tabelas.ToList()
                        : databaseConfiguration.Tabelas.Where(x => argumentos.ContainsKey(x.NomeTabela)).ToList();

                if (!tables.Any())
                {
                    logger.LogInformation("Nenhuma tabela encontrada para atualização. Provider: {provider}", databaseConfiguration.Provider);
                    continue;
                };

                using ISyncClient client =
                    new SyncClient(configuration.SyncDestino.Endereco, configuration.SyncDestino.Headers, logger);
                
                using var databaseProvider =
                    new DatabaseProviderFactory(databaseConfiguration.Provider)
                        .Create(databaseConfiguration.ConnectionString);

                using IExecutor executor = new Executor(databaseProvider, logger, client);
                var action = new ActionExecutor(acao, configuration.QuantidadeDeRegistrosPorRequest, tables);

                logger.LogInformation("Iniciando processamento Acao: {0} Provider: {1} Tabelas: {2}", acao,
                    databaseConfiguration.Provider,
                    tables.Select(x => x.NomeTabela).Aggregate((seed, current) => $"{seed}, {current}"));
                
                await executor.ExecuteAsync(action, CancellationToken.None);
                logger.LogInformation("Finalizando processamento Acao: {0} Provider: {1}", acao,
                    databaseConfiguration.Provider);
            }
        }
        catch (Exception e)
        {
            logger.LogError(e, "Houve erros durante sincronização dos dados.");
            Console.ReadKey();
        }
        finally
        {
            logger.LogInformation("Finalizando Sincronização.");
        }   
    }
    
    private static async Task<DataSyncConfig> GetConfigurationAsync()
    {
        var configJson = await File.ReadAllTextAsync("config.json");
       
        var configuration = JsonConvert.DeserializeObject<DataSyncConfig>(configJson);
        
        return configuration ?? throw new ArgumentNullException(nameof(DataSyncConfig),
            "Não foi possível criar as configurações com base no arquivo de configuração config.json");
    }
    
    private static ILogger CreateLogger()
    {
        using var loggerFactory = LoggerFactory.Create(builder =>
        {
            builder
                .AddFilter("Microsoft", LogLevel.Warning)
                .AddFilter("System", LogLevel.Warning)
                .AddFilter("LoggingConsoleApp.Program", LogLevel.Debug)
                .AddSimpleConsole(options =>
                {
                    options.SingleLine = true;
                    options.IncludeScopes = true;
                    options.TimestampFormat = "dd/MM/yyyy HH:mm:ss[.fff] ";
                });
        });

        return loggerFactory.CreateLogger(nameof(DataSync));   
    }
    
    private static Dictionary<string, string> ParseArgs(string[] args)
    {
        var argumentos = new Dictionary<string, string>();

        foreach (var arg in args)
        {
            // Se o argumento é uma opção
            var partes = arg.Split('=');
            var chave = arg.StartsWith("--") ? partes[0][2..] : partes[0]; // Remove os '--'
            var valor = partes.Length > 1 ? partes[1] : string.Empty; // Verifica se há valor associado

            argumentos[chave] = valor;
        }

        return argumentos;
    }

    private static IEnumerable<string> ParseArgsProvider(string argsProvider)
    {
        if (argsProvider.Contains(','))
            return argsProvider
                .Split(',')
                .Select(x => x.Trim());
        
        if(argsProvider.Contains(';'))
            return argsProvider
            .Split(';')
            .Select(x => x.Trim());

        return new[] { argsProvider.Trim() };
    }

    private static string GetAcao(IReadOnlyDictionary<string, string> argumentos, DataSyncConfig configuration)
    {
        if (argumentos.ContainsKey(configuration.Comandos.Alteracao)) 
            return Acoes.Alteracao;
        
        if (argumentos.ContainsKey(configuration.Comandos.Inclusao)) 
            return Acoes.Inclusao;
        
        return Acoes.Automatico;
    }
}