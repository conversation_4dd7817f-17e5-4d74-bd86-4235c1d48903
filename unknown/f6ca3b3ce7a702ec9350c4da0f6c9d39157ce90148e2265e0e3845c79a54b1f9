<?xml version="1.0" encoding="utf-8"?>
<Aplicacao xmlns:xsd="http://www.w3.org/2001/XMLSchema"
           xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <Nome>SINAPSE</Nome>
    <Versao>1.0.0</Versao>
    <Descricao>SINAPSE</Descricao>
    <Definicoes>
        <Definicao Nome="API Integração" Descricao="Perfil para utilizar a integração" Tipo="3" />
        <Definicao Nome="Apurador Agente" Descricao="Responsável pela apuração agente" Tipo="3" />
        <Definicao Nome="Apurador Centros" Descricao="Responsável pela apuração centros" Tipo="3" />
        <Definicao Nome="Operador Agente" Descricao="Responsável pela operação do agente" Tipo="3" />
        <Definicao Nome="Operador Centros" Descricao="Responsável pela operação dos Regionais, não pode ter o escopo CNOS" Tipo="3" />
        <Definicao Nome="Operador CNOS" Descricao="Responsável pela operação do CNOS, somente para o escopo CNOS" Tipo="3" />
        <Definicao Nome="Sincronizador" Descricao="Responsável pela implantação e gerenciamento do sistema" Tipo="3" />
        <Definicao Nome="Administrador" Descricao="Administrador do sistema" Tipo="3" />
        <Definicao Nome="Apurador" Descricao="Responsável pela apuração" Tipo="3" />
        <Definicao Nome="ExecutarSyncDone" Descricao="Executar SyncDone dos dados com a BDSync" Tipo="1" />
        <Definicao Nome="SincronizarBDSync" Descricao="Sincronizar dados com a BDSync" Tipo="1" />
        <Definicao Nome="Administrar" Descricao="Usado para acessar recursos internos da aplicação" Tipo="1" />
        <Definicao Nome="IntegracaoConsultarColecaoDados" Descricao="Consultar dados entidades api de dados" Tipo="1" />
        <Definicao Nome="IntegracaoConsultarDataset" Descricao="Consultar datasets api de dados" Tipo="1" />
        <Definicao Nome="IntegracaoAgente" Descricao="Consultar Status de Agente" Tipo="1" />
        <Definicao Nome="IntegracaoCriarSolicitacao" Descricao="Criar Solicitação via Integração" Tipo="1" />
    </Definicoes>
    <Composicoes>
        <Composicao Superior="API Integração" Inferior="IntegracaoConsultarDataset" />
        <Composicao Superior="API Integração" Inferior="IntegracaoConsultarColecaoDados" />
        <Composicao Superior="API Integração" Inferior="IntegracaoAgente" />
        <Composicao Superior="API Integração" Inferior="IntegracaoCriarSolicitacao" />
        <Composicao Superior="Sincronizador" Inferior="SincronizarBDSync" />
        <Composicao Superior="Sincronizador" Inferior="ExecutarSyncDone" />
        <Composicao Superior="Sincronizador" Inferior="Administrar" />
    </Composicoes>
    <TipoEscopoPerfils>
        <TipoEscopoPerfil CodigoTipoEscopo="ONS" DefinicaoPerfil="Administrador" />
        <TipoEscopoPerfil CodigoTipoEscopo="ONS" DefinicaoPerfil="Sincronizador" />
        <TipoEscopoPerfil CodigoTipoEscopo="ONS" DefinicaoPerfil="API Integração" />
        <TipoEscopoPerfil CodigoTipoEscopo="AGENTES" DefinicaoPerfil="Apurador Agente" />
        <TipoEscopoPerfil CodigoTipoEscopo="CENTROS" DefinicaoPerfil="Apurador Centros" />
        <TipoEscopoPerfil CodigoTipoEscopo="AGENTES" DefinicaoPerfil="Operador Agente" />
        <TipoEscopoPerfil CodigoTipoEscopo="CENTROS" DefinicaoPerfil="Operador CNOS" />
        <TipoEscopoPerfil CodigoTipoEscopo="CENTROS" DefinicaoPerfil="Operador Centros" />
        <TipoEscopoPerfil CodigoTipoEscopo="ONS" DefinicaoPerfil="Apurador" />
        <TipoEscopoPerfil CodigoTipoEscopo="AGENTES" DefinicaoPerfil="Apurador" />
    </TipoEscopoPerfils>
    <TipoDeProvedorPorTpTokens>
        <Codigo CodigoTipoProvedor="http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier" Tipo="JWT" />
        <Codigo CodigoTipoProvedor="http://schemas.xmlsoap.org/ws/2005/05/identity/claims/givenname" Tipo="JWT" />
        <Codigo CodigoTipoProvedor="http://schemas.xmlsoap.org/ws/2005/05/identity/claims/sid" Tipo="JWT" />
        <Codigo CodigoTipoProvedor="http://schemas.microsoft.com/ws/2008/06/identity/claims/role" Tipo="JWT" />
        <Codigo CodigoTipoProvedor="http://schemas.xmlsoap.org/ws/2015/07/identity/claims/scope" Tipo="JWT" />
        <Codigo CodigoTipoProvedor="http://schemas.xmlsoap.org/ws/2015/07/identity/claims/scoperole" Tipo="JWT" />
        <Codigo CodigoTipoProvedor="http://schemas.xmlsoap.org/ws/2015/07/identity/claims/operation" Tipo="JWT" />
    </TipoDeProvedorPorTpTokens>
</Aplicacao>