![App Screenshot](docs/logo.png)

# SINAPSE

Solução tecnológica que garante o envio e recebimento de mensagens estruturadas
(solicitações) e não estruturadas (informações) entre usuários de centros de
Operação distintos, respeitando suas respectivas atribuições.

## Objetivo

Desonerar o operador de atividades rotineiras e repetitivas, através de novas
Ferramentas de comunicação dos centros de controle e supervisão, de forma
Segura, colaborativa, confiável e auditável, aumentando a disponibilidade de tempo
Para o monitoramento do sistema, com o propósito de ampliar a segurança
Operacional.

![App Screenshot](docs/fatores-considerados.png)

## Principais funcionalidades

- Enviar, cancelar, confirmar, impedir, informar ciência, e finalizar solicitações.
- Enviar, complementar e ler comunicados.
- Atualização de dados em tempo real.
- Notificações sonoras e visuais, notificações em segundo plano.
- Notificação de login em outro dispositivo e logout automático.
- Integração com o sistema GERDIN.
- Integração com sistema de gestão de usuários e permissões do cliente (POP)
- Consulta de histórico de solicitações.
- Exportações de dados de solicitações via CSV.
- Sincronização de dados com a base técnica ONS.
- Iterações via chat de mensagens instantâneas (não habilitado ainda).

# Ações necessárias
- Fazer clone do projeto
- Baixar docker e executar dockercompose do projeto
- Atualizar hosts
- Rodar o projeto
- Fazer as cargas de dados

## Rodando localmente

Clone o projeto

```bash
  git clone http://tfs.ons.org.br:8080/tfs/ons/ONS%20Messenger/_git/ONSMessengerBackend
```

Entre no diretório do projeto

```bash
  cd ONSMessengerBackend
```

Instale as dependências

```bash
  dotnet restore
```

Inicie o container do MongoDB

```bash
  docker compose up mongo
```

Inicie o servidor

```bash
  dotnet run --project .\src\ONS.Messenger.Api\ONS.Messenger.Api.csproj
```

## Docker

Tenha certeza que o docker está instalado na sua máquina.
Caso não esteja, baixe no site oficial: https://www.docker.com/


Problemas que podem ocorrer ao tentar roda o docker:

	1. WSL 2 installation is incomplete

	Execute os seguintes comandos no PowerShell em modo administrador:
	dism.exe /online /enable-feature /featurename:Microsoft-Windows-Subsystem-Linux /all /norestart
	dism.exe /online /enable-feature /featurename:VirtualMachinePlatform /all /norestart

	Se não funcionar reinicie sua máquina.

	Baixe o Kernel do WSL 2 neste link: https://docs.microsoft.com/pt-br/windows/wsl/wsl2-kernel e instale o pacote.


- Comandos para subir as imagens do projeto (rodar no diretório do projeto que tem o dockercompose.yml):
docker compose build
docker compose up

Acessar o sqlserver e criar todas as tabelas
	host=localhost,7433
	database=SagerGeracao_TST
	user=sa
	password=SaGer2022GeracaoDevPass

http://localhost:6070/swagger/index.html - api
http://localhost:6080/swagger/index.html - api integracao

- Docker Run Commands:
Para rodar individualmente cada docker

docker build -f ONS.Messenger.Api.Dockerfile -t sager-geracao-api .
docker run -it --rm -p 6070:80 sager-geracao-api

docker build -f ONS.Messenger.Api.Integracao.Dockerfile -t sager-geracao-api-integracao .
docker run -it --rm -p 6080:80 sager-geracao-api-integracao

## Atualizando os host

```bash
cat .\docs\hosts >> C:\Windows\System32\drivers\etc\hosts
```
### Pasta templates
É necessário clonar o projeto http://tfs.ons.org.br:8080/tfs/ONS/SINapse/_git/SINapseTemplates e copiar a pasta templates para o diretorio src/ONS.SINapse.Api/templates


## Cargas de dados

A carga de dados é feita através do endpoint api/sync da aplicação.

CURL da chamada local: 
curl --request POST \
  --url http://localhost:6070/api/sync \
  --header 'Content-Type: application/json' \
  --data 'JSON_DA_CARGA'

Os json das cargas se encontram na pasta "cargas" do projeto. O json é o body de cada request e corresponde a uma carga específica. É importante rodar todas as cargas presentes na pasta para que o sistema funcione corretamente.

## Desenvolvimento

O projeto é preparado para sobrepor configurações do appsettings.json conforme o valor indicado em [ASPNETCORE_ENVIRONMENT](https://docs.microsoft.com/pt-br/aspnet/core/fundamentals/environments?view=aspnetcore-6.0).


Para configurar o projeto em modo de desenvolvimento no seu ambiente, duplique o arquivo 'appsettings.json', renomei-o para 'appsettings.Development.json' e remova as key-values que não precisar em seu ambiente.

Garanta que a chave ASPNETCORE_ENVIRONMENT=Development esteja configurada propriamente na lista de variaveis de ambiente do seu sistema operacional ou IDE. 

# Detalhes técnicos
Informações sobre implementações e decisões técnicas do projeto.

## Notificação

O SINapse foi projetado para trabalhar com notificações em tempo real, visando alcançar este propósito,
a primeira implementação foi desenvolvida baseada em push notifications utilizando Firebase Cloud Messaging (FCM)
. Apesar de ser um serviço muito bom para cenários mais simples não se mostrou suficiente para atender o projeto SINapse
visto que algumas notificações não eram entregues ao destinatário.

### Notificações com Realtime Database

O SINapse utiliza o Real-time database para garantir as atualizações de informações em tempo real nos paineis de
solicitações e comunicados. O Real-time database se mostrou muito eficiente fazendo as atualizações realmente em tempo real.

Após uma prova de conceito utilizando o Real-time database e a API de Notificação da Web chegamos a conclusão de que seria
viável e eficiente notificar os usuários utilizando uma implementação baseada no Real-time database.

As informações que antes eram enviadas para o FCM agora são envidas para uma coleção no Real-time database, *c_notificacao*.
Quando o usuário realiza login na aplicação (Front-end) é enviado um token de identificação do browser, 
o serviço de notificações inicializa a coleção de notificações do usuário/navegador com uma notificação especial.

```json
{
	"body": "Você receberá notificações neste dispositivo a partir de agora",
	"expirationDate": 1675112402,
	"icon": "assets/icon/favicon.ico",
	"image": "",
	"renotify": false,
	"requireInteraction": true,
	"silent": true,
	"tag": "309508db-752d-456d-b701-0b78fafcbdf2",
	"timestamp": 1675112397,
	"title": "Atualização de dados de notificação",
	"vibrate": 3
}
```
No appsettings possui uma nova entrada de configuração do sistema de notificações.

```json
{
	"ConfiguracaoDeNotificacaoSettings": {
		"Imagem": null,
		"Icone" : "assets/icon/favicon.ico",
		"TempoExpiracaoEmSegundos": 59,
		"TotalDeVibracao": 3,
		"Sufixo": null,
		"Prefixo": "SINapse -"
	}
}
```
Um notificação pode conter uma propriedade opcional *data* conforme exmplo abaixo.
```json
{
	"body": "Reduzir em 30 MW a Geração Fotovoltaica",
	"data": {
		"dataAtualizacao": "2023-01-30T18:29:22.9286356+00:00",
		"dataCriacao": "2023-01-30T18:25:09.6980000Z",
		"emitente": "Sistema",
		"id": "63d80b85ef621e2553286689",
		"mensagem": "Reduzir em 30 MW a Geração Fotovoltaica",
		"sid": "12331ffdsfs4345",
		"somNotificacao": "Sucesso",
		"status": "Finalizada",
		"tipo": "Solicitacao"
	},
	"expirationDate": 1675103422,
	"icon": "assets/icon/favicon.ico",
	"image": "assets/imgs/logo_ons.png",
	"renotify": false,
	"requireInteraction": false,
	"silent": false,
	"tag": "0525ab20-cb52-45fa-a417-97c83bbf3bf4",
	"timestamp": 1675103363,
	"title": "Solicitação finalizada automaticamente pelo sistema.",
	"vibrate": 3
}
```
### Observações
 - Apesar do idioma pardão do código ser Português (pt_BR) os atributos de NotificacaoRealtimeDto
foram definidos em Inglês (en) para haver uma compatibilidade direta com a [Web API Notification](https://developer.mozilla.org/en-US/docs/Web/API/notification). 

# sonar

# Contribute
O ENTRYPOINT ["/bin/sh", "-c" , "echo ***********   nuget.ons.org.br >> /etc/hosts" ] 
é utilizado no docker para criar o host do nuget ons, pois o docker fora da rede ONS não resolve o DNS