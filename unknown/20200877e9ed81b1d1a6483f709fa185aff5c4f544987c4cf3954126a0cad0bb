# Documentação de Verificação de Saúde da API (Health Check)

## Visão Geral

Esta documentação fornece informações sobre o estado de saúde da sua API com base nos resultados da verificação de saúde no endpoint: `API_URL/health`.

## Endpoint de Verificação de Saúde

- **Endpoint:** `/health`
- **Exemplo:** `http://localhost:6070/health`
- **Método:** GET

## Formato da Resposta

O endpoint de verificação de saúde (health) retorna uma resposta em JSON com informações sobre o estado de saúde de vários componentes do SINapse. Abaixo está um exemplo de resposta:
```json
{
  "status": "Healthy",
  "totalDuration": "00:00:00.9089072",
  "entries": {
    "MongoDB": {
      "data": {},
      "description": "MongoDB connection is healthy. c_centrooperacao has 5 documents.",
      "duration": "00:00:00.0011784",
      "status": "Healthy",
      "tags": [
        "mongodb"
      ]
    },
    "Firebase": {
      "data": {},
      "description": "Firebase connection is healthy.",
      "duration": "00:00:00.0000196",
      "status": "Healthy",
      "tags": [
        "firebase"
      ]
    },
    "Kafka": {
      "data": {},
      "description": "Kafka connection is healthy.",
      "duration": "00:00:00.9088313",
      "status": "Healthy",
      "tags": [
        "kafka"
      ]
    },
    "FinalizarSolicitacoesAutomaticamente": {
      "data": {},
      "description": "FinalizarSolicitacoesAutomaticamenteJob is healthy.",
      "duration": "00:00:00.0000060",
      "status": "Healthy",
      "tags": [
        "finalizar-solicitacoes",
        "job"
      ]
    },
    "RemoverNotificacoesNoFirebase": {
      "data": {},
      "description": "RemoverNotificacoesNoFirebaseDeDadosJob is healthy.",
      "duration": "00:00:00.0000059",
      "status": "Healthy",
      "tags": [
        "remover-notificacoes",
        "job"
      ]
    },
    "RemoverSolicitacoesPresasNoFirebase": {
      "data": {},
      "description": "RemoverSolicitacoesPresasNoFirebaseDeDadosJob is healthy.",
      "duration": "00:00:00.0000040",
      "status": "Healthy",
      "tags": [
        "remover-solicitacoes",
        "job"
      ]
    },
    "SyncDataDelete": {
      "data": {},
      "description": "Service SyncDataDelete not executed yet.",
      "duration": "00:00:00.0000017",
      "status": "Healthy",
      "tags": [
        "sync",
        "staging",
        "delete"
      ]
    },
    "SyncDataAdd": {
      "data": {},
      "description": "Service SyncDataAdd not executed yet.",
      "duration": "00:00:00.0000014",
      "status": "Healthy",
      "tags": [
        "sync",
        "staging",
        "add"
      ]
    },
    "SyncDataSave": {
      "data": {},
      "description": "Service SyncDataSave not executed yet.",
      "duration": "00:00:00.0000008",
      "status": "Healthy",
      "tags": [
        "sync",
        "staging",
        "save"
      ]
    },
    "SyncDataUpdate": {
      "data": {},
      "description": "Service SyncDataUpdate is healthy. Last execution was at 12/12/2023 09:03:50.",
      "duration": "00:00:00.0000251",
      "status": "Healthy",
      "tags": [
        "sync",
        "staging",
        "update"
      ]
    }
  }
}
```

## Legenda de Status de Saúde

- **Status: "Healthy"** - O componente ou serviço está operando corretamente (Saudável).
- **Status: "Unhealthy"** - O componente ou serviço está enfrentando problemas ou está indisponível (Com Problemas).

## Detalhes dos Componentes

### 1. MongoDB

- **Descrição:** Estado da conexão com o MongoDB.
- **Status:** Saudável/Com Problemas
- **Tags:** mongodb

### 2. Firebase

- **Descrição:** Estado da conexão com o Firebase.
- **Status:** Saudável/Com Problemas
- **Tags:** firebase

### 3. Kafka

- **Descrição:** Estado da conexão com o Kafka.
- **Status:** Saudável/Com Problemas
- **Tags:** kafka

### 4. FinalizarSolicitacoesAutomaticamente

- **Descrição:** Estado do FinalizarSolicitacoesAutomaticamenteJob.
- **Status:** Saudável/Com Problemas
- **Tags:** finalizar-solicitacoes, job

### 5. RemoverNotificacoesNoFirebase

- **Descrição:** Estado do RemoverNotificacoesNoFirebaseDeDadosJob.
- **Status:** Saudável/Com Problemas
- **Tags:** remover-notificacoes, job

### 6. RemoverSolicitacoesPresasNoFirebase

- **Descrição:** Estado do RemoverSolicitacoesPresasNoFirebaseDeDadosJob.
- **Status:** Saudável/Com Problemas
- **Tags:** remover-solicitacoes, job

### 7. SyncDataDelete, SyncDataAdd, SyncDataSave, SyncDataUpdate

- **Descrição:** Estado dos serviços SyncData para operações de delete, add, save e update.
- **Status:** Saudável/Com Problemas
- **Tags:** sync, staging, delete/add/save/update

## Página de Monitoramento de Saúde da API
O monitoramento de saúde da API pode ser acessado através da URL: `API_URL/monitor`. Exemplo: `http://localhost:6070/monitor`.
![Monitor](images/health-check-monitor.png)

## Monitoramento de Saúde da API de Integração com SINapse
O monitoramento de saúde da API de Integração com SINapse é feito através de uma requisição GET para o endpoint `/health` ou através da página `monitor`.
- Exemplo: `https://localhost:7267/health`.
- Exemplo: `https://localhost:7267/monitor`.

Atualmente o monitoramento de saúde da API de Integração com SINapse possui health checks para o MongoDB, Firebase e Kafka.

## Conclusão

Esta documentação fornece uma visão detalhada do estado de saúde de vários componentes dentro da API do SINapse. Monitore regularmente esta verificação de saúde para garantir o funcionamento adequado do seu sistema.