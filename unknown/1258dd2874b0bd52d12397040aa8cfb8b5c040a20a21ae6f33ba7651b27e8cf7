{"_type": "export", "__export_format": 4, "__export_date": "2021-12-16T12:56:47.721Z", "__export_source": "insomnia.desktop.app:v2021.7.2", "resources": [{"_id": "req_ad0a226d549c489682b8da374361865c", "parentId": "wrk_3cb8eb5c298b45eba3dbccbc55167bae", "modified": 1639659350997, "created": 1639659096459, "url": "https://popdsv.ons.org.br/ons.pop.federation/oauth2/token", "name": "ObterToken popdsv", "description": "", "method": "GET", "body": {"mimeType": "application/x-www-form-urlencoded", "params": [{"name": "client_id", "value": "SAGER_Geracao", "id": "pair_cc71a377e48e49bb859fd9078eaf32c1"}, {"name": "grant_type", "value": "password", "id": "pair_1913184880914e76a83ff54b5654511b"}, {"name": "username", "value": "ons\\luizlanza.amcom", "id": "pair_ea20763f97ef44b7b0566f00b07ce81e"}, {"name": "password", "value": "", "id": "pair_436c89641c0d4ccfb7eaa9f806d1e40c"}]}, "parameters": [], "headers": [{"name": "Content-Type", "value": "application/x-www-form-urlencoded", "id": "pair_bdae72d084fa4551992878293b2a77c0"}, {"name": "Origin", "value": "http://popdsv.ons.org.br", "id": "pair_1a5d3ee6d76a4092a2698c949f063bc3"}, {"name": "<PERSON><PERSON>", "value": ".ONSAUTH_DSV=4BF0415520303C4496FF1CA8D485CBBE804C46B2D0B8FEEAB3528AB1B08816B860D5CCE4AC8A68002829D0DBC8A78EF7ECA25D494666ABFFE3AB319AE01BC607B88D77E3A6686BA803E33A8FE65509604E5AF6E0B8EFFCAC597343AEF14DB770F2BB76EB7F78B5811702304ED9F1FFA4D7217FDFA1E413CE5A5F1BB2CC46A3D2AD023861DDE9ED17639613F6163C5A5385766C5B", "id": "pair_7a353b9e3297434caa2d35f8a564bba7"}], "authentication": {}, "metaSortKey": -1639659096459, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "wrk_3cb8eb5c298b45eba3dbccbc55167bae", "parentId": null, "modified": 1639441311626, "created": 1639441311626, "name": "ONS.Sager.Geracao", "description": "", "scope": "collection", "_type": "workspace"}, {"_id": "req_65c8109c7cc949b0882e682647b08517", "parentId": "wrk_3cb8eb5c298b45eba3dbccbc55167bae", "modified": 1639659289279, "created": 1639441322314, "url": "http://localhost:7070/api/usuarios/autenticado", "name": "Autenticado", "description": "", "method": "GET", "body": {}, "parameters": [], "headers": [{"name": "Connection", "value": "keep-alive", "id": "pair_7fdf2517ede441468b25d7528b9c833d"}, {"name": "Pragma", "value": "no-cache", "id": "pair_3bbb69ff290340c49005afee4e62a3ec"}, {"name": "Cache-Control", "value": "no-cache", "id": "pair_7492b1bcb23d49c2908266bbe524167b"}, {"name": "Accept", "value": "application/json, text/plain, */*", "id": "pair_0b5ad6f9cd274299bfdeef4dc7f378cf"}, {"name": "Authorization", "value": "Bearer {% response 'body', 'req_ad0a226d549c489682b8da374361865c', 'b64::JC5hY2Nlc3NfdG9rZW4=::46b', 'when-expired', 600 %}", "id": "pair_2ccafa5e0d57430d979ffe8974d362b9"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.93 Safari/537.36 Edg/96.0.1054.53", "id": "pair_78b86ef67d2f42c789c2555eaf53fef0"}, {"name": "<PERSON><PERSON><PERSON>", "value": "http://tst-ons-039.ons.org.br/ONS.SAGER.Apuracao.WebSite/", "id": "pair_d75cbe6c5a054ccfac816c341a56348d"}, {"name": "Accept-Language", "value": "pt-BR,pt;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "id": "pair_6172d02051c14e98b2ea85acfb423fd3"}, {"name": "<PERSON><PERSON>", "value": ".ONSAUTH_DSV=B450038F5C75A42B38EABDAFCB4BE3D09B3D26A45C6E818005735E6DA581EE19EAAB0BE638570097DC73E34878EEEB0FE56DEDFDD28FF0A73DCBDCB658E560575F1347EC6476AD4DD8B806A60213868C7320ECEFD9E929E7A3BFE747F5211D9A233A679F86FEF8421071C1F5758202AAA16D8A142297355839B4068F68DDA8EC0D2D46F5", "id": "pair_5ff3db30e37f45e0a8c3ffb9a3977e9d"}, {"id": "pair_178ed4e3827f4a3bacdf8eef8ee37348", "name": "", "value": "", "description": ""}], "authentication": {}, "metaSortKey": -1639441322314, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_f41f149618614581befa7d1ebec01e28", "parentId": "wrk_3cb8eb5c298b45eba3dbccbc55167bae", "modified": 1639659293285, "created": 1639442562963, "url": "http://localhost:7070/api/Usuarios/usuariosPop", "name": "UsuariosPop", "description": "", "method": "GET", "body": {}, "parameters": [], "headers": [{"name": "Connection", "value": "keep-alive", "id": "pair_17d7d8505542430d9b46b90a808f0440"}, {"name": "Pragma", "value": "no-cache", "id": "pair_ec378b1c5cd2497eacf2595b7615316c"}, {"name": "Cache-Control", "value": "no-cache", "id": "pair_68f6be335ae64d5787e759655465a867"}, {"name": "Accept", "value": "application/json, text/plain, */*", "id": "pair_3f524e32444048f9b8ed62022dff389e"}, {"name": "Authorization", "value": "Bearer {% response 'body', 'req_ad0a226d549c489682b8da374361865c', 'b64::JC5hY2Nlc3NfdG9rZW4=::46b', 'when-expired', 600 %}", "id": "pair_4e7daeba141548979e061d6f6a20b6b8"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.93 Safari/537.36 Edg/96.0.1054.53", "id": "pair_af16503bd64d49d4af57bf7b9415902c"}, {"name": "Accept-Language", "value": "pt-BR,pt;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "id": "pair_409266787be8401aaff1d5d2c45f9529"}, {"name": "<PERSON><PERSON>", "value": ".ONSAUTH_DSV=E40663770ACE24A3CF96B6374C48678BF8E27835CE061B1371FCAA0C0FA375D154C43BDBFAEC0772D51565E721C499516BAAA6D7533773FBFD5D4DCAFD8CFF95805622D7AF5C0DE08107807FA6A69B1FC1C87DF18B7E787004219D7422195FBE61598B1234A6B2DEED6A910C7A3B236BB9CB3045794F7C53E2E083E24552CDD53926ACC0", "id": "pair_4ebec31c6080434f86b2387986a32a4e"}], "authentication": {}, "metaSortKey": -1638556685551, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "env_106156773383e832157cb1aa7247cf86301f6ae1", "parentId": "wrk_3cb8eb5c298b45eba3dbccbc55167bae", "modified": 1639659358114, "created": 1639441311642, "name": "Base Environment", "data": {}, "dataPropertyOrder": {}, "color": null, "isPrivate": false, "metaSortKey": 1639441311642, "_type": "environment"}, {"_id": "jar_106156773383e832157cb1aa7247cf86301f6ae1", "parentId": "wrk_3cb8eb5c298b45eba3dbccbc55167bae", "modified": 1639659283567, "created": 1639441311646, "name": "<PERSON><PERSON><PERSON>", "cookies": [{"key": ".ONSAUTH_DSV", "value": "46B61AFC82EE324F6AED3597C0425EDEFD03B6FD57C0EBC983E8A1D684FA9D5F4A27FBF479C8576BAC065D21AE6F459A4B9324ED014ED2A1A9747A6F4285D1EA883730E1000A4D7E5F4BAA7B4B4C2FAF752671890A7A82979C4E1FDD5F73F2D29C6FB01AF89F840EBBBEAE83614053890FE5F1DA4DC63D7DF3E975724E8EBFEC43AD7A7D", "expires": "2021-12-16T13:54:44.000Z", "domain": "ons.org.br", "path": "/", "httpOnly": true, "extensions": ["SameSite=Lax"], "hostOnly": false, "creation": "2021-12-16T11:45:28.386Z", "lastAccessed": "2021-12-16T12:54:43.566Z", "id": "0037233183532325853"}], "_type": "cookie_jar"}, {"_id": "spc_912fd16ad66f4f4ea9d69a6f4c64f5e6", "parentId": "wrk_3cb8eb5c298b45eba3dbccbc55167bae", "modified": 1639441311630, "created": 1639441311630, "fileName": "ONS.Sager.Geracao", "contents": "", "contentType": "yaml", "_type": "api_spec"}]}