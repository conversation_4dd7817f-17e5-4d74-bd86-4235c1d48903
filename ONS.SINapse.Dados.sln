
Microsoft Visual Studio Solution File, Format Version 12.00
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "01 - Services", "01 - Services", "{00AA29FB-5ED0-4473-9F21-00D9A827FA72}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "02 - Business", "02 - Business", "{1EF87205-4232-4293-B97A-0ACD3D83EA89}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "03 - Domain", "03 - Domain", "{519A51F6-CAA2-4471-9ED8-C3E3996111B3}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "04 - Infra", "04 - Infra", "{B35D2C7F-5ACB-4078-8B00-E0A2B8FD2031}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "05 - Integracoes", "05 - Integracoes", "{DECBFB3A-59A8-4A7E-83E1-DE20DE70BF22}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "98 - Shared", "98 - Shared", "{97E1A90C-233B-472B-AA05-B2DBD02A7E88}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "99 - Tests", "99 - Tests", "{AFEBE757-8038-4448-B302-D608E28E8BD2}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{A6F941C3-8705-466A-85D2-377B1B75D264}"
	ProjectSection(SolutionItems) = preProject
		docker-compose.yml = docker-compose.yml
		README.md = README.md
	EndProjectSection
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ONS.SINapse.Dados.Entities", "src\ONS.SINapse.Dados.Entities\ONS.SINapse.Dados.Entities.csproj", "{CB89B1D6-1A9A-4106-9635-610D222D0B67}"
	ProjectSection(ProjectDependencies) = postProject
		{4D080C75-649F-47AC-94FF-95BD07477DD7} = {4D080C75-649F-47AC-94FF-95BD07477DD7}
	EndProjectSection
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ONS.SINapse.Dados.Business", "src\ONS.SINapse.Dados.Business\ONS.SINapse.Dados.Business.csproj", "{426C0944-2280-4D91-851C-BDD1780CC1E1}"
	ProjectSection(ProjectDependencies) = postProject
		{4D080C75-649F-47AC-94FF-95BD07477DD7} = {4D080C75-649F-47AC-94FF-95BD07477DD7}
		{CB89B1D6-1A9A-4106-9635-610D222D0B67} = {CB89B1D6-1A9A-4106-9635-610D222D0B67}
		{AE39573B-9EAF-46BD-962D-21D599369374} = {AE39573B-9EAF-46BD-962D-21D599369374}
	EndProjectSection
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ONS.SINapse.Dados.Api", "src\ONS.SINapse.Dados.Api\ONS.SINapse.Dados.Api.csproj", "{F592A6EA-36B1-44F6-801F-771948667703}"
	ProjectSection(ProjectDependencies) = postProject
		{B91CDAFB-A067-47BC-9BF9-014BA5A7D100} = {B91CDAFB-A067-47BC-9BF9-014BA5A7D100}
		{426C0944-2280-4D91-851C-BDD1780CC1E1} = {426C0944-2280-4D91-851C-BDD1780CC1E1}
		{CB89B1D6-1A9A-4106-9635-610D222D0B67} = {CB89B1D6-1A9A-4106-9635-610D222D0B67}
		{AE39573B-9EAF-46BD-962D-21D599369374} = {AE39573B-9EAF-46BD-962D-21D599369374}
		{4D080C75-649F-47AC-94FF-95BD07477DD7} = {4D080C75-649F-47AC-94FF-95BD07477DD7}
		{0436BE69-DF5D-41BE-86FE-F14B46738A72} = {0436BE69-DF5D-41BE-86FE-F14B46738A72}
	EndProjectSection
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ONS.SINapse.Dados.Shared", "src\ONS.SINapse.Dados.Shared\ONS.SINapse.Dados.Shared.csproj", "{4D080C75-649F-47AC-94FF-95BD07477DD7}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ONS.SINapse.Dados.Repository", "src\ONS.SINapse.Dados.Repository\ONS.SINapse.Dados.Repository.csproj", "{AE39573B-9EAF-46BD-962D-21D599369374}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ONS.SINapse.Dados.CrossCutting", "src\ONS.SINapse.Dados.CrossCutting\ONS.SINapse.Dados.CrossCutting.csproj", "{B91CDAFB-A067-47BC-9BF9-014BA5A7D100}"
	ProjectSection(ProjectDependencies) = postProject
		{CB89B1D6-1A9A-4106-9635-610D222D0B67} = {CB89B1D6-1A9A-4106-9635-610D222D0B67}
		{AE39573B-9EAF-46BD-962D-21D599369374} = {AE39573B-9EAF-46BD-962D-21D599369374}
		{4D080C75-649F-47AC-94FF-95BD07477DD7} = {4D080C75-649F-47AC-94FF-95BD07477DD7}
		{0436BE69-DF5D-41BE-86FE-F14B46738A72} = {0436BE69-DF5D-41BE-86FE-F14B46738A72}
	EndProjectSection
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ONS.SINapse.Dados.SyncData", "src\ONS.SINapse.Dados.SyncData\ONS.SINapse.Dados.SyncData.csproj", "{0436BE69-DF5D-41BE-86FE-F14B46738A72}"
	ProjectSection(ProjectDependencies) = postProject
		{4D080C75-649F-47AC-94FF-95BD07477DD7} = {4D080C75-649F-47AC-94FF-95BD07477DD7}
		{CB89B1D6-1A9A-4106-9635-610D222D0B67} = {CB89B1D6-1A9A-4106-9635-610D222D0B67}
		{AE39573B-9EAF-46BD-962D-21D599369374} = {AE39573B-9EAF-46BD-962D-21D599369374}
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "97 - Jobs", "97 - Jobs", "{6FB76CE2-7E4A-49D9-9931-7A6E5A53953F}"
	ProjectSection(SolutionItems) = preProject
		jobs\data-sync-done.yaml = jobs\data-sync-done.yaml
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{CB89B1D6-1A9A-4106-9635-610D222D0B67} = {519A51F6-CAA2-4471-9ED8-C3E3996111B3}
		{426C0944-2280-4D91-851C-BDD1780CC1E1} = {1EF87205-4232-4293-B97A-0ACD3D83EA89}
		{F592A6EA-36B1-44F6-801F-771948667703} = {00AA29FB-5ED0-4473-9F21-00D9A827FA72}
		{4D080C75-649F-47AC-94FF-95BD07477DD7} = {97E1A90C-233B-472B-AA05-B2DBD02A7E88}
		{AE39573B-9EAF-46BD-962D-21D599369374} = {B35D2C7F-5ACB-4078-8B00-E0A2B8FD2031}
		{B91CDAFB-A067-47BC-9BF9-014BA5A7D100} = {1EF87205-4232-4293-B97A-0ACD3D83EA89}
		{0436BE69-DF5D-41BE-86FE-F14B46738A72} = {DECBFB3A-59A8-4A7E-83E1-DE20DE70BF22}
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{CB89B1D6-1A9A-4106-9635-610D222D0B67}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{CB89B1D6-1A9A-4106-9635-610D222D0B67}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{CB89B1D6-1A9A-4106-9635-610D222D0B67}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{CB89B1D6-1A9A-4106-9635-610D222D0B67}.Release|Any CPU.Build.0 = Release|Any CPU
		{426C0944-2280-4D91-851C-BDD1780CC1E1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{426C0944-2280-4D91-851C-BDD1780CC1E1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{426C0944-2280-4D91-851C-BDD1780CC1E1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{426C0944-2280-4D91-851C-BDD1780CC1E1}.Release|Any CPU.Build.0 = Release|Any CPU
		{F592A6EA-36B1-44F6-801F-771948667703}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F592A6EA-36B1-44F6-801F-771948667703}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F592A6EA-36B1-44F6-801F-771948667703}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F592A6EA-36B1-44F6-801F-771948667703}.Release|Any CPU.Build.0 = Release|Any CPU
		{4D080C75-649F-47AC-94FF-95BD07477DD7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4D080C75-649F-47AC-94FF-95BD07477DD7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4D080C75-649F-47AC-94FF-95BD07477DD7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4D080C75-649F-47AC-94FF-95BD07477DD7}.Release|Any CPU.Build.0 = Release|Any CPU
		{AE39573B-9EAF-46BD-962D-21D599369374}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{AE39573B-9EAF-46BD-962D-21D599369374}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{AE39573B-9EAF-46BD-962D-21D599369374}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{AE39573B-9EAF-46BD-962D-21D599369374}.Release|Any CPU.Build.0 = Release|Any CPU
		{B91CDAFB-A067-47BC-9BF9-014BA5A7D100}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B91CDAFB-A067-47BC-9BF9-014BA5A7D100}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B91CDAFB-A067-47BC-9BF9-014BA5A7D100}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B91CDAFB-A067-47BC-9BF9-014BA5A7D100}.Release|Any CPU.Build.0 = Release|Any CPU
		{0436BE69-DF5D-41BE-86FE-F14B46738A72}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0436BE69-DF5D-41BE-86FE-F14B46738A72}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0436BE69-DF5D-41BE-86FE-F14B46738A72}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0436BE69-DF5D-41BE-86FE-F14B46738A72}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
EndGlobal
