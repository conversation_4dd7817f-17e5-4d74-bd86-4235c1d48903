{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "DistributedTracing": {"ServiceVersion": "1.0.0", "ServiceName": "ONS.AFConnector"}, "KafkaSettings": {"SaslUsername": "PlaceHolderFor_KafkaSettings:SaslUsername", "SaslPassword": "PlaceHolderFor_ONS.AFConnector.Service.Password", "BootstrapServers": "PlaceHolderFor_KafkaSettings:BootstrapServers", "SaslMechanism": "PlaceHolderFor_KafkaSettings:SaslMechanism", "SecurityProtocol": "PlaceHolderFor_KafkaSettings:SecurityProtocol", "Topico": "PlaceHolderFor_KafkaSettings:TopicName", "SslCaPem": "PlaceHolderFor_KafkaSettings:SslCaPem", "SslKeystorePassword": "PlaceHolderFor_KafkaSettings:SslKeystorePassword", "ContingencyTopic": "PlaceHolderFor_KafkaSettings:ContingencyTopic"}, "RabbitMQSettings": {"ConnectionString": "PlaceHolderFor_ONS.AFConnector.RabbitMQSettings.ConnectionString", "AFConnectorAssetFrameworkEventQueue": "PlaceHolderFor_ONS.AFConnector.RabbitMQSettings.AFConnectorAssetFrameworkEventQueue"}}