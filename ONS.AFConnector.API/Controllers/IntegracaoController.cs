using Microsoft.AspNetCore.Mvc;
using ONS.AFConnector.CrossCutting.OpenTelemetry;
using ONS.AFConnector.CrossCutting.RabbitMQ;
using System.Diagnostics;
using System.Text.Json;

namespace ONS.AFConnector.Controllers;

[ApiController]
[Route("[controller]")]
public class IntegracaoController(IRabbitMQService rabbitMQService) : ControllerBase
{
    private readonly IRabbitMQService _rabbitMQService = rabbitMQService;

    [HttpPost("IntegracaoPI")]
    public Task<IActionResult> IntegracaoPIAsync([FromBody] object payload)
    {
        if (payload == null) return Task.FromResult<IActionResult>(BadRequest());

        using var activity = OTelConfig.ActivitySource?.StartActivity("Evento recebido do PI-AF.", ActivityKind.Producer);

        activity?.SetTag("Payload", payload);
        _rabbitMQService.Publish(JsonSerializer.Serialize(payload), activity!.Context);

        return Task.FromResult<IActionResult>(Ok());
    }
}