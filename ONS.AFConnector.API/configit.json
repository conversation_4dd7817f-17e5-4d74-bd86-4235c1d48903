{"KafkaSettings:SaslUsername": {"depara": "ONS.AFConnector.Service.Username", "arquivo": "appsettings.json", "kind": "JsonFile"}, "KafkaSettings:SaslPassword": {"depara": "ONS.AFConnector.Service.Password", "arquivo": "appsettings.json", "kind": "JsonFile"}, "KafkaSettings:TopicName": {"depara": "ONS.AFConnector.KafkaSettings.TopicName", "arquivo": "appsettings.json", "kind": "JsonFile"}, "KafkaSettings:BootstrapServers": {"depara": "Kafka_BootstrapServers_Openshift", "arquivo": "appsettings.json", "kind": "JsonFile"}, "KafkaSettings:SaslMechanism": {"depara": "Kafka_SaslMechanism", "arquivo": "appsettings.json", "kind": "JsonFile"}, "KafkaSettings:SecurityProtocol": {"depara": "Kafka_SecurityProtocol", "arquivo": "appsettings.json", "kind": "JsonFile"}, "KafkaSettings:SslCaPem": {"depara": "Kafka_SslCaPem", "arquivo": "appsettings.json", "kind": "JsonFile"}, "KafkaSettings:SslKeystorePassword": {"depara": "Kafka_SslKeystorePassword", "arquivo": "appsettings.json", "kind": "JsonFile"}, "KafkaSettings:ContingencyTopic": {"depara": "ONS.AFConnector.KafkaSettings.ContingencyTopicName", "arquivo": "appsettings.json", "kind": "JsonFile"}, "RabbitMQSettings:ConnectionString": {"depara": "ONS.AFConnector.RabbitMQSettings.ConnectionString", "arquivo": "appsettings.json", "kind": "JsonFile"}, "RabbitMQSettings:AFConnectorAssetFrameworkEventQueue": {"depara": "ONS.AFConnector.RabbitMQSettings.AFConnectorAssetFrameworkEventQueue", "arquivo": "appsettings.json", "kind": "JsonFile"}}