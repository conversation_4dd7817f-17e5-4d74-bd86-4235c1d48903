<Project>

  <!-- Central Package Management - Versões centralizadas de todos os pacotes NuGet -->

  <!-- AWS Packages -->
  <ItemGroup>
    <PackageVersion Include="Amazon.Extensions.Configuration.SystemsManager" Version="6.2.2" />
    <PackageVersion Include="AWSSDK.Athena" Version="3.7.403.75" />
    <PackageVersion Include="AWSSDK.S3" Version="3.7.410.6" />
    <PackageVersion Include="AWSSDK.SQS" Version="3.7.400.133" />
  </ItemGroup>

  <!-- ASP.NET Core HealthChecks -->
  <ItemGroup>
    <PackageVersion Include="AspNetCore.HealthChecks.Redis" Version="9.0.0" />
    <PackageVersion Include="AspNetCore.HealthChecks.UI" Version="9.0.0" />
    <PackageVersion Include="AspNetCore.HealthChecks.UI.Client" Version="9.0.0" />
    <PackageVersion Include="AspNetCore.HealthChecks.UI.Core" Version="9.0.0" />
    <PackageVersion Include="AspNetCore.HealthChecks.UI.InMemory.Storage" Version="9.0.0" />
  </ItemGroup>

  <!-- App Metrics -->
  <ItemGroup>
    <PackageVersion Include="App.Metrics" Version="4.3.0" />
    <PackageVersion Include="App.Metrics.AspNetCore" Version="4.3.0" />
    <PackageVersion Include="App.Metrics.AspNetCore.Reporting" Version="4.0.0" />
    <PackageVersion Include="App.Metrics.AspNetCore.Tracking" Version="4.3.0" />
    <PackageVersion Include="App.Metrics.Core" Version="4.3.0" />
    <PackageVersion Include="App.Metrics.Extensions.Configuration" Version="4.3.0" />
    <PackageVersion Include="App.Metrics.Extensions.HealthChecks" Version="4.3.0" />
    <PackageVersion Include="App.Metrics.Formatters.Json" Version="4.3.0" />
  </ItemGroup>

  <!-- Core Libraries -->
  <ItemGroup>
    <PackageVersion Include="AutoMapper" Version="13.0.1" />
    <PackageVersion Include="BenchmarkDotNet.Annotations" Version="0.14.0" />
    <PackageVersion Include="Bogus" Version="35.6.1" />
    <PackageVersion Include="Confluent.Kafka" Version="2.8.0" />
    <PackageVersion Include="CsvHelper" Version="33.0.1" />
    <PackageVersion Include="FluentValidation" Version="11.11.0" />
    <PackageVersion Include="GeoCoordinate.NetCore" Version="1.0.0.1" />
    <PackageVersion Include="HunspellSharp" Version="1.0.1" />
    <PackageVersion Include="librdkafka.redist" Version="2.8.0" />
    <PackageVersion Include="LinqKit.Core" Version="1.2.7" />
    <PackageVersion Include="LiteDB" Version="5.0.21" />
    <PackageVersion Include="Nanoid" Version="3.1.0" />
    <PackageVersion Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageVersion Include="Polly" Version="8.5.0" />
    <PackageVersion Include="Scrutor" Version="5.0.2" />
    <PackageVersion Include="Swashbuckle.AspNetCore" Version="7.1.0" />
    <PackageVersion Include="System.ComponentModel.Annotations" Version="5.0.0" />
    <PackageVersion Include="System.IdentityModel.Tokens.Jwt" Version="8.2.1" />
    <PackageVersion Include="System.Linq.Expressions" Version="4.3.0" />
  </ItemGroup>

  <!-- Firebase -->
  <ItemGroup>
    <PackageVersion Include="FirebaseAdmin" Version="3.1.0" />
    <PackageVersion Include="FirebaseAuthentication.net" Version="3.7.2" />
    <PackageVersion Include="FirebaseDatabase.net" Version="4.2.0" />
    <PackageVersion Include="Google.Apis.FirebaseCloudMessaging.v1" Version="1.68.0.3603" />
  </ItemGroup>

  <!-- MassTransit -->
  <ItemGroup>
    <PackageVersion Include="MassTransit" Version="8.3.2" />
    <PackageVersion Include="MassTransit.AmazonSQS" Version="8.3.2" />
    <PackageVersion Include="MassTransit.Newtonsoft" Version="8.3.2" />
  </ItemGroup>

  <!-- Microsoft Extensions -->
  <ItemGroup>
    <PackageVersion Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.11" />
    <PackageVersion Include="Microsoft.AspNetCore.Authentication.WsFederation" Version="8.0.11" />
    <PackageVersion Include="Microsoft.AspNetCore.Mvc.Testing" Version="8.0.15" />
    <PackageVersion Include="Microsoft.Extensions.Caching.Memory" Version="9.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Caching.StackExchangeRedis" Version="8.0.11" />
    <PackageVersion Include="Microsoft.Extensions.DependencyInjection" Version="8.0.1" />
    <PackageVersion Include="Microsoft.Extensions.Options" Version="9.0.0" />
    <PackageVersion Include="Microsoft.IdentityModel.Tokens" Version="8.2.1" />
    <PackageVersion Include="Microsoft.NET.Test.Sdk" Version="17.8.0" />
    <PackageVersion Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.21.0" />
  </ItemGroup>

  <!-- MongoDB -->
  <ItemGroup>
    <PackageVersion Include="MongoDB.Bson" Version="3.0.0" />
    <PackageVersion Include="MongoDB.Driver" Version="3.0.0" />
    <PackageVersion Include="MongoDB.Driver.Core.Extensions.DiagnosticSources" Version="2.0.0" />
  </ItemGroup>

  <!-- Testing -->
  <ItemGroup>
    <PackageVersion Include="coverlet.collector" Version="6.0.0" />
    <PackageVersion Include="Moq" Version="4.20.72" />
    <PackageVersion Include="Moq.AutoMock" Version="3.5.0" />
    <PackageVersion Include="Shouldly" Version="4.2.1" />
    <PackageVersion Include="xunit" Version="2.9.3" />
    <PackageVersion Include="xunit.runner.visualstudio" Version="3.0.0" />
  </ItemGroup>

  <!-- OpenTelemetry -->
  <ItemGroup>
    <PackageVersion Include="OpenTelemetry" Version="1.9.0" />
    <PackageVersion Include="OpenTelemetry.Api" Version="1.12.0" />
    <PackageVersion Include="OpenTelemetry.Exporter.Console" Version="1.9.0" />
    <PackageVersion Include="OpenTelemetry.Exporter.OpenTelemetryProtocol" Version="1.9.0" />
    <PackageVersion Include="OpenTelemetry.Exporter.Prometheus.AspNetCore" Version="1.9.0-beta.2" />
    <PackageVersion Include="OpenTelemetry.Extensions.Hosting" Version="1.9.0" />
    <PackageVersion Include="OpenTelemetry.Instrumentation.AspNetCore" Version="1.9.0" />
    <PackageVersion Include="OpenTelemetry.Instrumentation.Http" Version="1.9.0" />
    <PackageVersion Include="OpenTelemetry.Instrumentation.Process" Version="1.11.0-beta.2" />
    <PackageVersion Include="OpenTelemetry.Instrumentation.Runtime" Version="1.11.1" />
  </ItemGroup>

  <!-- Refit -->
  <ItemGroup>
    <PackageVersion Include="Refit" Version="8.0.0" />
    <PackageVersion Include="Refit.HttpClientFactory" Version="8.0.0" />
    <PackageVersion Include="Refit.Newtonsoft.Json" Version="8.0.0" />
  </ItemGroup>

  <!-- Logging -->
  <ItemGroup>
    <PackageVersion Include="Serilog.AspNetCore" Version="8.0.3" />
  </ItemGroup>

  <!-- Redis -->
  <ItemGroup>
    <PackageVersion Include="StackExchange.Redis" Version="2.7.27" />
  </ItemGroup>



</Project>