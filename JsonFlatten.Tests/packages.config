<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="Microsoft.TestPlatform.ObjectModel" version="17.10.0" targetFramework="net472" />
  <package id="Newtonsoft.Json" version="13.0.1" targetFramework="net472" />
  <package id="System.Buffers" version="4.5.1" targetFramework="net472" />
  <package id="System.Collections.Immutable" version="8.0.0" targetFramework="net472" />
  <package id="System.Memory" version="4.5.5" targetFramework="net472" />
  <package id="System.Numerics.Vectors" version="4.5.0" targetFramework="net472" />
  <package id="System.Reflection.Metadata" version="8.0.0" targetFramework="net472" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="6.0.0" targetFramework="net472" />
  <package id="xunit" version="2.9.0" targetFramework="net472" />
  <package id="xunit.abstractions" version="2.0.3" targetFramework="net472" />
  <package id="xunit.analyzers" version="1.15.0" targetFramework="net472" developmentDependency="true" />
  <package id="xunit.assert" version="2.9.0" targetFramework="net472" />
  <package id="xunit.core" version="2.9.0" targetFramework="net472" />
  <package id="xunit.extensibility.core" version="2.9.0" targetFramework="net472" />
  <package id="xunit.extensibility.execution" version="2.9.0" targetFramework="net472" />
  <package id="Xunit.Priority" version="1.1.6" targetFramework="net472" />
  <package id="xunit.runner.visualstudio" version="2.8.2" targetFramework="net472" developmentDependency="true" />
</packages>