using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using MongoDB.Bson.Serialization.Conventions;
using MongoDB.Driver;
using MongoDB.Driver.Core.Extensions.DiagnosticSources;
using ONS.SINapse.Repository.IContext;
using ONS.SINapse.Repository.Imp.Context;
using ONS.SINapse.Repository.Imp.Mapping;
using ONS.SINapse.Repository.Imp.Repositories;
using ONS.SINapse.Repository.Imp.Repositories.DadosCadastrais;
using ONS.SINapse.Repository.Imp.Repositories.Firebase;
using ONS.SINapse.Repository.IRepository;
using ONS.SINapse.Repository.IRepository.DadosCadastrais;
using ONS.SINapse.Repository.IRepository.Firebase;
using ONS.SINapse.Shared.Settings;

namespace ONS.SINapse.Repository.Imp;

public static class DependencyInjector
{
    public static IServiceCollection RegisterRepositoryLayer(this IServiceCollection services, IConfiguration configuration)
    {
        RegisterMongoConnection(services, configuration);
        RegisterRepositories(services);

        return services;
    }

    private static void RegisterMongoConnection(IServiceCollection services, IConfiguration configuration)
    {
        services.AddScoped<IMongoConnection, MongoConnection>();
        services.AddScoped<IMongoReadOnlyConnection, MongoReadOnlyConnection>();
        
        services.Configure<MongoDbSettings>(configuration.GetSection(nameof(MongoDbSettings)));
        
        services.AddSingleton<IMongoClient, MongoClient>(sp =>
        {
            var settings = sp.GetRequiredService<IOptions<MongoDbSettings>>().Value;
            var mongoClientSettings = MongoClientSettings.FromConnectionString(settings.ConnectionString);
            mongoClientSettings.ReadPreference = ReadPreference.Primary;
            mongoClientSettings.WriteConcern = WriteConcern.WMajority;
            mongoClientSettings.ReadConcern = ReadConcern.Majority;
            mongoClientSettings.ApplicationName = configuration.GetValue<string>("ApplicationName");
            var options = new InstrumentationOptions { CaptureCommandText = true };
            mongoClientSettings.ClusterConfigurator = builder =>
            {
                builder.Subscribe(new DiagnosticsActivityEventSubscriber(options));
                builder.ConfigureConnectionPool(pool => pool.With(maxConnections: settings.MaxConnections));
            };
            
            mongoClientSettings.ServerSelectionTimeout = TimeSpan.FromSeconds(60);
            mongoClientSettings.ConnectTimeout = TimeSpan.FromSeconds(30);
            
            var pack = new ConventionPack
            {
                new IgnoreExtraElementsConvention(true),
                new StringObjectIdIdGeneratorConvention(),
                new NamedIdMemberConvention("Id")
            };
            
            ConventionRegistry.Register(nameof(IgnoreExtraElementsConvention), pack, _ => true);
            
            return new MongoClient(mongoClientSettings);
        });
        
        services.AddScoped<IMongoDatabase>(sp =>
        {
            var settings = sp.GetRequiredService<IOptions<MongoDbSettings>>().Value;
            var client = sp.GetRequiredService<IMongoClient>();
            return client.GetDatabase(settings.DatabaseName);
        });
        
        MongoClassMapping.RegisterMappings();
    }

    private static void RegisterRepositories(IServiceCollection services)
    {
        services.AddScoped<IComunicadoRepository, ComunicadoRepository>();
        services.AddScoped<IAgenteRepository, AgenteRepository>();
        services.AddScoped<IExtracaoDeDadoRepository, ExtracaoDeDadoRepository>();
        services.AddScoped<IRascunhoSolicitacaoRepository, RascunhoSolicitacaoRepository>();
        services.AddScoped<ISolicitacaoRepository, SolicitacaoRepository>();
        services.AddScoped<ISolicitacaoFirebaseRepository, SolicitacaoFirebaseRepository>();
        services.AddScoped<IRespostaDaChamadaRepository, RespostaDaChamadaRepository>();
        services.AddScoped<IControleDeChamadaRepository, ControleDeChamadaRepository>();
        services.AddScoped<IHistoricoDeAcessoRepository, HistoricoDeAcessoRepository>();
        services.AddScoped<IVersaoFirebaseRepository, VersaoFirebaseRepository>();
        services.AddScoped<ICollectionFirebaseRepository, CollectionFirebaseRepository>();
        services.AddScoped<IStatusServiceRepository, StatusServiceRepository>();
        services.AddScoped<IRascunhoFavoritoSolicitacaoRepository, RascunhoFavoritoSolicitacaoRepository>();
    }
}