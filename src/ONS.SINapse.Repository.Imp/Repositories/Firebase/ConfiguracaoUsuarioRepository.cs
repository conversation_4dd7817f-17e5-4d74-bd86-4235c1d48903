using Firebase.Database;
using Firebase.Database.Query;
using ONS.SINapse.Entities.Entities;
using ONS.SINapse.Repository.IRepository;
using ONS.SINapse.Shared.Constants;

namespace ONS.SINapse.Repository.Imp.Repositories.Firebase;

public class ConfiguracaoUsuarioRepository : IConfiguracaoUsuarioRepository
{
    private readonly ChildQuery _firebaseCollection;

    public ConfiguracaoUsuarioRepository(FirebaseClient firebaseClient)
    {
        _firebaseCollection = firebaseClient.Child(ColecoesFirebase.ConfiguracaoUsuario);
    }

    public Task AddUpdateAsync(ConfiguracaoUsuario configuracao, string sid, CancellationToken cancellationToken)
    {
        return _firebaseCollection
            .Child(sid)
            .PutAsync(configuracao)
            .WaitAsync(cancellationToken);
    }
}