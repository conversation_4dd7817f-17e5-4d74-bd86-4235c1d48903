using System.Linq.Expressions;
using Firebase.Database;
using Firebase.Database.Query;
using ONS.SINapse.Entities.Entities;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Repository.IRepository.Firebase;
using ONS.SINapse.Shared.Constants;
using ONS.SINapse.Shared.Converters;
using ONS.SINapse.Shared.DTO.Solicitacao;

namespace ONS.SINapse.Repository.Imp.Repositories.Firebase;

public class SolicitacaoFirebaseRepository : ISolicitacaoFirebaseRepository
{
    private readonly FirebaseClient _firebaseClient;
    private readonly ChildQuery _firebaseCollection;
    
    public SolicitacaoFirebaseRepository(FirebaseClient firebaseClient)
    {
        _firebaseClient = firebaseClient;
        _firebaseCollection = firebaseClient.Child(ColecoesFirebase.CadastroSolicitacao);
    }

    public Task AddUpdateAsync(List<Entities.Entities.Solicitacao> solicitacoes, CancellationToken cancellationToken)
    {
        var collection = solicitacoes.ToDictionary(x => x.Id);
        return _firebaseCollection
            .PatchAsync(collection)
            .WaitAsync(cancellationToken);
    }

    public Task AddUpdateAsync(Dictionary<string, object?> solicitacoes, CancellationToken cancellationToken)
    {
        return _firebaseClient
            .Child(ColecoesFirebase.Root)
            .PatchAsync(solicitacoes)
            .WaitAsync(cancellationToken);
    }

    public Task PatchFlattenAsync<T>(IEnumerable<T> solicitacao, CancellationToken cancellationToken) where T : FlattenableJson
    {
        return _firebaseCollection
            .PatchAsync(solicitacao)
            .WaitAsync(cancellationToken);
    }

    public Task PatchFlattenAsync<T>(T solicitacao, CancellationToken cancellationToken) where T : FlattenableJson
    {
        return _firebaseCollection
            .PatchAsync(solicitacao)
            .WaitAsync(cancellationToken);
    }

    public Task RemoveAsync(IReadOnlyCollection<string> ids, CancellationToken cancellationToken)
    {
        var solicitacoes = ids.ToDictionary(k => k, _ => (string?)null);

        return _firebaseCollection
            .PatchAsync(solicitacoes)
            .WaitAsync(cancellationToken);
    }
    
    public async Task<Solicitacao?> GetByIdAsync(string id, CancellationToken cancellationToken = default)
    {
        var dto = await _firebaseCollection
            .Child(id)
            .OnceSingleAsync<SolicitacaoDto>()
            .WaitAsync(cancellationToken);

        return dto is null ? null : Solicitacao.FromDto(dto);
    }

    public async Task<List<Solicitacao>> GetByIdAsync(string[] ids, CancellationToken cancellationToken = default)
    {
        var tasks = ids.Select(id => GetByIdAsync(id, cancellationToken));

        var results = await Task.WhenAll(tasks);

        return results
            .Where(s => s is not null)
            .Select(s => s!)
            .ToList();
    }

    public Task<List<Solicitacao>> GetByStatusAsync(StatusDeSolicitacao status, CancellationToken cancellationToken)
    {
        return _firebaseCollection
            .OrderBy("statusId")
            .EqualTo((short)status)
            .OnceAsync<SolicitacaoDto>()
            .ContinueWith(t => t.Result.Select(x => Solicitacao.FromDto(x.Object)).ToList(), cancellationToken);
    }
}
