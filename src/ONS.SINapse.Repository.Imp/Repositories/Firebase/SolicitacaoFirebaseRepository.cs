using Firebase.Database;
using Firebase.Database.Query;
using ONS.SINapse.Entities.Entities;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Repository.IRepository.Firebase;
using ONS.SINapse.Shared.Constants;
using ONS.SINapse.Shared.DTO.Solicitacao;

namespace ONS.SINapse.Repository.Imp.Repositories.Firebase;

public class SolicitacaoFirebaseRepository : ISolicitacaoFirebaseRepository
{
    private readonly FirebaseClient _firebaseClient;
    private readonly ChildQuery _firebaseCollection;
    
    public SolicitacaoFirebaseRepository(FirebaseClient firebaseClient)
    {
        _firebaseClient = firebaseClient;
        _firebaseCollection = firebaseClient.Child(ColecoesFirebase.CadastroSolicitacao);
    }

    public Task AddUpdateAsync(List<Solicitacao> solicitacoes, CancellationToken cancellationToken)
    {
        var collection = solicitacoes.ToDictionary(x => x.Id);
        return _firebaseCollection
            .PatchAsync(collection)
            .WaitAsync(cancellationToken);
    }

    public Task AddUpdateAsync(Dictionary<string, object?> solicitacoes, CancellationToken cancellationToken)
    {
        return _firebaseClient
            .Child(ColecoesFirebase.Root)
            .PatchAsync(solicitacoes)
            .WaitAsync(cancellationToken);
    }

    public Task RemoveAsync(IReadOnlyCollection<string> ids, CancellationToken cancellationToken)
    {
        var solicitacoes = ids.ToDictionary(k => k, _ => (string?)null);

        return _firebaseCollection
            .PatchAsync(solicitacoes)
            .WaitAsync(cancellationToken);
    }
    
    public async Task<Solicitacao?> GetByIdAsync(string id, CancellationToken cancellationToken)
    {
        var dto = await _firebaseCollection
            .Child(id)
            .OnceSingleAsync<SolicitacaoDto>()
            .WaitAsync(cancellationToken);

        return dto is null ? null : Solicitacao.FromDto(dto);
    }

    public async Task<StatusDeSolicitacao> StatusAsync(string id, CancellationToken cancellationToken)
    {
        var dto = await _firebaseClient
            .Child(ColecoesFirebase.CadastroSolicitacao)
            .Child(id)
            .Child("status")
            .OnceSingleAsync<StatusDeSolicitacao?>()
            .WaitAsync(cancellationToken);
        return dto ?? StatusDeSolicitacao.Erro;
    }

    public async Task<List<Solicitacao>> GetByIdAsync(string[] ids, CancellationToken cancellationToken)
    {
        var tasks = ids.Select(id => GetByIdAsync(id, cancellationToken));

        var results = await Task.WhenAll(tasks);

        return results
            .Where(s => s is not null)
            .Select(s => s!)
            .ToList();
    }

    public async Task<List<Solicitacao>> GetAlldAsync(CancellationToken cancellationToken)
    {
        var dto = await _firebaseCollection
            .OnceAsync<SolicitacaoDto>()
            .WaitAsync(cancellationToken);

        return dto
            .Select(s => Solicitacao.FromDto(s.Object))
            .ToList();
    }
    
    public async Task<IReadOnlyCollection<Solicitacao>> GetSolicitacoesParaFinalizarAsync(DateTime updatedBefore, CancellationToken cancellationToken)
    {
        var updatedBeforeIso = updatedBefore.ToString("o");

        var firebaseData = await _firebaseCollection
            .OrderBy("updatedAt")
            .EndAt(updatedBeforeIso)
            .OnceAsync<SolicitacaoDto>()
            .WaitAsync(cancellationToken);

        return firebaseData
            .Select(f => Solicitacao.FromDto(f.Object))
            .Where(s => s.UpdatedAt <= updatedBefore && s.PodeFinalizar)
            .ToList()
            .AsReadOnly();
    }
}
