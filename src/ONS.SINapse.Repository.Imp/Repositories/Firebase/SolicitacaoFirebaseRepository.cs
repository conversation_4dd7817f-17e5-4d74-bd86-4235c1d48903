using Firebase.Database;
using Firebase.Database.Query;
using ONS.SINapse.Repository.IRepository.Firebase;
using ONS.SINapse.Shared.Constants;
using ONS.SINapse.Shared.Converters;

namespace ONS.SINapse.Repository.Imp.Repositories.Firebase;

public class SolicitacaoFirebaseRepository : ISolicitacaoFirebaseRepository
{
    private readonly ChildQuery _firebaseCollection;
    
    public SolicitacaoFirebaseRepository(FirebaseClient firebaseClient)
    {
        _firebaseCollection = firebaseClient.Child(ColecoesFirebase.Solicitacao);
    }

    public Task AddUpdateAsync(List<Entities.Entities.Solicitacao> solicitacoes, CancellationToken cancellationToken)
    {
        var collection = solicitacoes.ToDictionary(x => x.Id);
        return _firebaseCollection
            .PatchAsync(collection)
            .WaitAsync(cancellationToken);
    }

    public Task PatchFlattenAsync<T>(IEnumerable<T> solicitacao, CancellationToken cancellationToken) where T : FlattenableJson
    {
        return _firebaseCollection
            .PatchAsync(solicitacao)
            .WaitAsync(cancellationToken);
    }

    public Task PatchFlattenAsync<T>(T solicitacao, CancellationToken cancellationToken) where T : FlattenableJson
    {
        return _firebaseCollection
            .PatchAsync(solicitacao)
            .WaitAsync(cancellationToken);
    }

    public Task RemoverSolicitacoesAsync(IReadOnlyCollection<string> ids, CancellationToken cancellationToken)
    {
        var solicitacoes = ids.ToDictionary(k => k, _ => (string?)null);

        return _firebaseCollection
            .PatchAsync(solicitacoes)
            .WaitAsync(cancellationToken);
    }
}
