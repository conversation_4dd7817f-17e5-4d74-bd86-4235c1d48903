using Firebase.Database;
using Firebase.Database.Query;
using ONS.SINapse.Repository.IRepository;
using ONS.SINapse.Shared.Constants;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.DTO.Firebase;

namespace ONS.SINapse.Repository.Imp.Repositories.Firebase
{
    public class VersaoFirebaseRepository : IVersaoFirebaseRepository
    {
        private readonly ChildQuery _firebaseCollection;

        public VersaoFirebaseRepository(FirebaseClient firebaseClient)
        {
            _firebaseCollection = firebaseClient.Child(ColecoesFirebase.Versao);
        }

        public Task AddAsync(VersaoFirebaseDto versao, CancellationToken cancellationToken)
        {
            return _firebaseCollection
                .PatchAsync(versao)
                .WaitAsync(cancellationToken);
        }

        public Task UpdateAsync(EditarVersaoDto versao, CancellationToken cancellationToken)
        {
            return _firebaseCollection
                .PatchAsync(versao)
                .WaitAsync(cancellationToken);
        }
    }
}
