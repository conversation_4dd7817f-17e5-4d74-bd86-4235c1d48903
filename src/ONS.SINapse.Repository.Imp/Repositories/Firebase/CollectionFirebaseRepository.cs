using Firebase.Database;
using ONS.SINapse.Repository.IRepository.Firebase;

namespace ONS.SINapse.Repository.Imp.Repositories.Firebase
{
    public class CollectionFirebaseRepository : ICollectionFirebaseRepository
    {
        private readonly FirebaseClient _firebaseClient;

        public CollectionFirebaseRepository(FirebaseClient firebaseClient)
        {
            _firebaseClient = firebaseClient;
        }

        public Task PatchAsync(string collectionName, string jsonData, CancellationToken cancellationToken)
        {
            var firebaseCollection = _firebaseClient.Child(collectionName);

            return firebaseCollection
                .PatchAsync(jsonData)
                .WaitAsync(cancellationToken);
        }

        public Task PutAsync(string collectionName, string jsonData, CancellationToken cancellationToken)
        {
            var firebaseCollection = _firebaseClient.Child(collectionName);

            return firebaseCollection
                .PutAsync(jsonData)
                .WaitAsync(cancellationToken);
        }

        public Task DeleteAsync(string collectionName, CancellationToken cancellationToken)
        {
            var firebaseCollection = _firebaseClient.Child(collectionName);

            return firebaseCollection
                .DeleteAsync()
                .WaitAsync(cancellationToken);
        }

        public Task<IReadOnlyCollection<FirebaseObject<object>>> GetAsync(string collectionName, CancellationToken cancellationToken)
        {
            var firebaseCollection = _firebaseClient.Child(collectionName);

            return firebaseCollection
                .OnceAsync<object>()
                .WaitAsync(cancellationToken);
        }
    }
}
