using MongoDB.Bson;

namespace ONS.SINapse.Repository.Imp.Repositories.Pipelines
{
    public static class SolicitacaoPipeline
    {
        public static BsonDocument[] AgentesDeSolicitacoesPorCentro(string[] centros)
        {
            var pipeline = new[]
            {
                new BsonDocument("$match", new BsonDocument
                {
                    { "$and", new BsonArray
                        {
                            new BsonDocument("cod_status", new BsonDocument("$in", new BsonArray { 4, 5 })),
                            new BsonDocument("obj_origem", new BsonDocument("$exists", true)),
                            new BsonDocument("obj_origem.cod_local", new BsonDocument("$exists", true)),
                            new BsonDocument("obj_destino", new BsonDocument("$exists", true)),
                            new BsonDocument("obj_destino.cod_local", new BsonDocument("$exists", true)),
                            new BsonDocument("obj_origem", new BsonDocument("$ne", BsonNull.Value)),
                            new BsonDocument("obj_origem.cod_local", new BsonDocument("$ne", BsonNull.Value)),
                            new BsonDocument("obj_destino", new BsonDocument("$ne", BsonNull.Value)),
                            new BsonDocument("obj_destino.cod_local", new BsonDocument("$ne", BsonNull.Value))
                        }
                    },
                    { "$or", new BsonArray
                        {
                            new BsonDocument("obj_origem.cod_local", new BsonDocument("$in", new BsonArray(centros))),
                            new BsonDocument("obj_destino.cod_local", new BsonDocument("$in", new BsonArray(centros)))
                        }
                    }
                }),
                new BsonDocument("$group", new BsonDocument
                {
                    { "_id", new BsonDocument
                        {
                            { "obj_destino᎐cod_local", "$obj_destino.cod_local" },
                            { "obj_destino᎐nom_local", "$obj_destino.nom_local" }
                        }
                    }
                }),
                new BsonDocument("$project", new BsonDocument
                {
                    { "codigo", "$_id.obj_destino᎐cod_local" },
                    { "descricao", "$_id.obj_destino᎐nom_local" },
                    { "_id", 0 }
                })
            };
            
            
            //new BsonDocument("obj_origem.cod_local", new BsonDocument("$in", new BsonArray(centros))),
            
            return pipeline;
        }

        public static BsonDocument[] SolicitacoesComErroListChatPipeline()
        {
            return
            [
                new BsonDocument("$match", new BsonDocument
                {
                    { "obj_chat.list_mensagens", new BsonDocument { { "$exists", true }, { "$type", "array" } } },
                    { "list_historicostatus", new BsonDocument { { "$exists", true }, { "$type", "array" } } }
                }),
                new BsonDocument("$addFields", new BsonDocument
                {
                    { "mensagens_length", new BsonDocument("$size", "$obj_chat.list_mensagens") },
                    { "historicostatus_length", new BsonDocument("$size", "$list_historicostatus") }
                }),
                new BsonDocument("$match", new BsonDocument
                {
                    { "$expr", new BsonDocument("$ne", new BsonArray { "$mensagens_length", "$historicostatus_length" }) }
                })
            ];
        }
    }
}