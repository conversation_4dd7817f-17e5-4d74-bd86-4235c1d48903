using Firebase.Database;
using Firebase.Database.Query;
using ONS.SINapse.Entities.Entities;
using ONS.SINapse.Repository.IRepository;
using ONS.SINapse.Shared.Constants;
using ONS.SINapse.Shared.DTO.Firebase;

namespace ONS.SINapse.Repository.Imp.Repositories;

public class RascunhoSolicitacaoRepository : IRascunhoSolicitacaoRepository
{
    private readonly ChildQuery _firebaseCollection;

    public RascunhoSolicitacaoRepository(FirebaseClient firebaseClient)
    {
        _firebaseCollection = firebaseClient.Child(ColecoesFirebase.Rascunho);
    }

    public Task AddUpdateAsync(RascunhoSolicitacao rascunho, CancellationToken cancellationToken)
    {
        return _firebaseCollection
            .Child(rascunho.Id.ToString)
            .PutAsync(rascunho)
            .WaitAsync(cancellationToken);
    }
    
    public Task DeleteAsync(Guid rascunho, CancellationToken cancellationToken)
    {
        return _firebaseCollection
            .Child(rascunho.ToString)
            .DeleteAsync()
            .WaitAsync(cancellationToken);
    }

    public Task InvalidarAsync(Guid rascunho, CancellationToken cancellationToken)
    {
        return _firebaseCollection
            .Child(rascunho.ToString)
            .PatchAsync(new { Valido = false })
            .WaitAsync(cancellationToken);
    }

    public async Task<IEnumerable<RascunhoSolicitacaoDto>> ObterRascunhoPorNomeAsync(string nome, CancellationToken cancellationToken)
    {
        var dados = await _firebaseCollection
            .OrderBy("nome")
            .EqualTo(nome)
            .OnceAsync<RascunhoSolicitacaoDto>()
            .WaitAsync(cancellationToken);
        
        return dados.Select(x => x.Object);
    }
}