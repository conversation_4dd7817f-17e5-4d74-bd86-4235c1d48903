using MongoDB.Driver;
using ONS.SINapse.Entities.Entities;
using ONS.SINapse.Repository.IContext;
using ONS.SINapse.Repository.Imp.Repositories.Base;
using ONS.SINapse.Repository.IRepository;
using System.Linq.Expressions;

namespace ONS.SINapse.Repository.Imp.Repositories;

public class RespostaDaChamadaRepository : MongoRepository<RespostaDaChamada>, IRespostaDaChamadaRepository
{
    public RespostaDaChamadaRepository(IMongoConnection mongoConnection) : base(mongoConnection)
    {
    }

    public Task<RespostaDaChamada?> BuscarPorCentro(string codigo, CancellationToken cancellationToken)
        => GetOneAsync(s => s.CentroAgente.Codigo == codigo, cancellationToken);

    public async Task<ICollection<RespostaDaChamada>> FindOrCreate(ObjetoDeManobra[] centroAgentes, CancellationToken cancellationToken)
    {
        var centros = await GetAsync(s => 
            centroAgentes.Any(n => n.Codigo == s.CentroAgente.Codigo), cancellationToken);

        if (centros.Count == 0)
        {
            return await CreateAsync(centroAgentes, 0, cancellationToken);
        }

        var centrosNaoEncontrados = centroAgentes.ExceptBy(centros.Select(c => c.CentroAgente.Codigo), manobra => manobra.Codigo).ToArray();
        var centrosCriados = await CreateAsync(centrosNaoEncontrados, 0, cancellationToken);
        return centros.Concat(centrosCriados).ToList();
    }

    public async Task<IEnumerable<RespostaDaChamada>> BuscarPendentesDeNotificacaoAsync(CancellationToken cancellationToken)
    {
        return await GetAsync(r => !r.Notificado, cancellationToken);
    }

    private async Task<ICollection<RespostaDaChamada>> CreateAsync(ObjetoDeManobra[] centroAgentes, long chamada, CancellationToken cancellationToken)
    {
        var statusCentros = centroAgentes.Select(c => new RespostaDaChamada(c, chamada)).ToList();
        await BulkCreateAsync(statusCentros, cancellationToken);
        return statusCentros;
    }

    public async Task<string[]> ObterCodigoAgentesOnlineAsync(Expression<Func<RespostaDaChamada, bool>> predicate, CancellationToken cancellationToken)
    {
        return (await _collection
            .Find(predicate)
            .Project(resposta => resposta.CentroAgente.Codigo)
            .ToListAsync(cancellationToken))
            .ToArray();
    }
}