using System.Linq.Expressions;
using MongoDB.Driver;
using ONS.SINapse.Entities.Entities;
using ONS.SINapse.Repository.IContext;
using ONS.SINapse.Repository.IRepository;

namespace ONS.SINapse.Repository.Imp.Repositories;

public class SolicitacaoRepository : ISolicitacaoRepository
{
    private readonly IMongoCollection<Solicitacao> _collection;
    private const string CollectionName = "c_solicitacao";

    public SolicitacaoRepository(IMongoConnection mongoConnection)
    {
        _collection = mongoConnection.GetDatabase().GetCollection<Solicitacao>(CollectionName);
    }

    public Task DeleteManyAsync(IEnumerable<string> ids, CancellationToken cancellationToken)
    {
        var filter = Builders<Solicitacao>.Filter.In(doc => doc.Id, ids);
        return _collection.DeleteManyAsync(filter, cancellationToken);
    }

    public Task BulkUpdateAsync(IEnumerable<Solicitacao> documents, bool isUpsert,
        CancellationToken cancellationToken)
    {
        var filterBuilder = Builders<Solicitacao>.Filter;

        var updates = documents.Select(doc =>
            {
                var filter = filterBuilder.Where(x => x.Id == doc.Id);
                return new ReplaceOneModel<Solicitacao>(filter, doc){ IsUpsert = isUpsert };
            })
            .ToList();

        return updates.Count != 0 ? _collection.BulkWriteAsync(updates, null, cancellationToken) : Task.CompletedTask;
    }

    public async Task EnviadoAoAnaliticoAsync(List<string> ids, CancellationToken cancellationToken)
    {
        var filter = Builders<Solicitacao>.Filter.In(doc => doc.Id, ids);
        var update = Builders<Solicitacao>.Update
            .Set(x => x.EnviadoAoAnalitico, true)
            .Set(x => x.DataEnvioAoAnalitico, DateTime.UtcNow);
        
        await _collection.UpdateManyAsync(filter, update, new UpdateOptions { IsUpsert = false }, cancellationToken);
    }
    
    public Task<IAsyncCursor<Solicitacao>> FindAsync(Expression<Func<Solicitacao, bool>> predicate,
        int? limit = null,
        FindOptions? options = null, CancellationToken cancellationToken = default)
    {
        return _collection.Find(predicate, options).Limit(limit).ToCursorAsync(cancellationToken);
    }
}

