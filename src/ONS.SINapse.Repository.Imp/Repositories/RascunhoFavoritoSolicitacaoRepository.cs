using Firebase.Database;
using Firebase.Database.Query;
using ONS.SINapse.Repository.IRepository;
using ONS.SINapse.Shared.Constants;

namespace ONS.SINapse.Repository.Imp.Repositories;

public class RascunhoFavoritoSolicitacaoRepository : IRascunhoFavoritoSolicitacaoRepository
{
    private readonly ChildQuery _firebaseCollection;

    public RascunhoFavoritoSolicitacaoRepository(FirebaseClient firebaseClient)
    {
        _firebaseCollection = firebaseClient.Child(ColecoesFirebase.RascunhoFavorito);
    }

    public Task AddUpdateAsync(string path, CancellationToken cancellationToken)
    {
        return _firebaseCollection
            .Child(path)
            .PutAsync(true)
            .WaitAsync(cancellationToken);
    }
    
    public Task DeleteAsync(string path, CancellationToken cancellationToken)
    {
        return _firebaseCollection
            .Child(path)
            .DeleteAsync()
            .WaitAsync(cancellationToken);
    }

    public async Task<Dictionary<string, Dictionary<string, object>>> GetRascunhosPorCentroAsync(string centro, CancellationToken cancellationToken)
    {
        var snapshot = await _firebaseCollection
            .Child(centro)
            .OnceAsync<Dictionary<string, object>>()
            .WaitAsync(cancellationToken);

        var resultado = new Dictionary<string, Dictionary<string, object>>();

        foreach (var usuario in snapshot)
        {
            resultado[usuario.Key] = usuario.Object ?? new Dictionary<string, object>();
        }

        return resultado;
    }

}