using ONS.SINapse.Entities.Entities.Extracoes;
using ONS.SINapse.Repository.IContext;
using ONS.SINapse.Repository.Imp.Repositories.Base;
using ONS.SINapse.Repository.IRepository;

namespace ONS.SINapse.Repository.Imp.Repositories;

public class ExtracaoDeDadoRepository : MongoRepository<ExtracaoDeDado>, IExtracaoDeDadoRepository
{
    public ExtracaoDeDadoRepository(IMongoConnection mongoConnection) : base(mongoConnection)
    {
    }
}