using ONS.SINapse.Entities.Entities;
using ONS.SINapse.Repository.IContext;
using ONS.SINapse.Repository.Imp.Repositories.Base;
using ONS.SINapse.Repository.IRepository;

namespace ONS.SINapse.Repository.Imp.Repositories;

public class HistoricoDeAcessoRepository : MongoRepository<HistoricoAcesso>, IHistoricoDeAcessoRepository
{
    public HistoricoDeAcessoRepository(IMongoConnection mongoConnection) : base(mongoConnection)
    {
    }
}