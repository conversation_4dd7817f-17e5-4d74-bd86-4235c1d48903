using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using ONS.SINapse.CacheSync.Services;
using ONS.SINapse.Entities.Entities.DadosCadastrais;
using ONS.SINapse.Repository.IRepository.DadosCadastrais;
using ONS.SINapse.Shared.Services.Caching;
using ONS.SINapse.Shared.Settings;

namespace ONS.SINapse.Repository.Imp.Repositories.DadosCadastrais;

public class AgenteRepository : SinapaseDadosQueryService<Agente>, IAgenteRepository
{
    public AgenteRepository(ISinapseDadosService sinapseDadosService, ICacheService cacheService, ILogger<AgenteRepository> logger, IOptions<DataSyncSettings> dataSyncSettings) 
        : base(sinapseDadosService, cacheService, logger, dataSyncSettings)
    {
    }
    public Task<IEnumerable<Agente>> GetAsync(IEnumerable<string> codigos, CancellationToken cancellationToken)
    {
        return GetAsync(d => codigos.Contains(d.Codigo), cancellationToken);
    }
}