using MongoDB.Driver;
using ONS.SINapse.Entities.Entities.Base;
using ONS.SINapse.Repository.IContext;
using ONS.SINapse.Shared.CustomAttributes;
using System.Linq.Expressions;
using ONS.SINapse.Repository.IRepository.Base;

namespace ONS.SINapse.Repository.Imp.Repositories.Base;


public abstract class MongoRepository<TDocument> : IMongoRepository<TDocument> where TDocument : Entidade
{
    protected readonly IMongoCollection<TDocument> _collection;
    protected readonly IMongoDatabase _database;

    protected MongoRepository(IMongoConnection mongoConnection)
    {
        _database = mongoConnection.GetDatabase();
        _collection = _database.GetCollection<TDocument>(GetCollectionName());
        var collections = _database.ListCollectionNames().ToList();
        if (!collections.Contains(GetCollectionName()))
        {
            CreateCollectionAsync(CancellationToken.None).Wait();
        }
    }

    protected string? GetCollectionName()
    {
        var documentType = typeof(TDocument);
        var attribute = documentType
            .GetCustomAttributes(typeof(BsonCollectionAttribute), true)
            .FirstOrDefault() as BsonCollectionAttribute;

        return attribute?.CollectionName;
    }

    public async Task<ICollection<TDocument>> GetAsync(CancellationToken cancellationToken) 
        => (await _collection.FindAsync(doc => true, null, cancellationToken)).ToList(cancellationToken);

    public async Task<ICollection<TDocument>> GetAsync(IEnumerable<string> ids, CancellationToken cancellationToken)
        => await GetAsync(doc => ids.Contains(doc.Id), new FindOptions<TDocument, TDocument>() { }, cancellationToken);

    public async Task<ICollection<TDocument>> GetAsync(Expression<Func<TDocument, bool>> predicate, CancellationToken cancellationToken)
        => await GetAsync(predicate, new FindOptions<TDocument, TDocument>() { }, cancellationToken);

    private async Task<ICollection<TDocument>> GetAsync(Expression<Func<TDocument, bool>> predicate, FindOptions<TDocument, TDocument>? options, CancellationToken cancellationToken)
        => (await _collection.FindAsync(predicate, options, cancellationToken)).ToList(cancellationToken);

    private Task<ICollection<TDocument>> GetAsync(Expression<Func<TDocument, bool>> predicate,
        IDictionary<string, bool> sortBy, int? skip, int? limit, CancellationToken cancellationToken)
    {
        SortDefinitionBuilder<TDocument> sortDefinitionBuilder = Builders<TDocument>.Sort;
        SortDefinition<TDocument>? sortDefinition = null;

        foreach (var item in sortBy)
        {
            sortDefinition = GetSortDefinition(sortDefinitionBuilder, item.Key, item.Value);
        }

        var options = new FindOptions<TDocument, TDocument>()
        {
            Sort = sortDefinition,
            Skip = skip,
            Limit = limit
        };

        return GetAsync(predicate, options, cancellationToken);
    }

    public Task<ICollection<TDocument>> GetAsync(Expression<Func<TDocument, bool>> predicate,
        IDictionary<string, bool> sortBy, CancellationToken cancellationToken) =>
        GetAsync(predicate, sortBy, null, null, cancellationToken);

    public async Task<TDocument?> GetOneAsync(string id, CancellationToken cancellationToken)
        => (await _collection.FindAsync<TDocument>(doc => doc.Id == id, null, cancellationToken)).FirstOrDefault(cancellationToken);

    public async Task<TDocument?> GetOneAsync(Expression<Func<TDocument, bool>> predicate, CancellationToken cancellationToken)
        => (await _collection.FindAsync<TDocument>(predicate, null, cancellationToken)).FirstOrDefault(cancellationToken);

    public async Task<bool> AnyAsync(string id, CancellationToken cancellationToken)
        => (await _collection.CountDocumentsAsync(doc => doc.Id == id, null, cancellationToken)) > 0;

    public async Task<bool> AnyAsync(Expression<Func<TDocument, bool>> predicate, CancellationToken cancellationToken)
        => (await _collection.CountDocumentsAsync(predicate, null, cancellationToken)) > 0;

    public Task<long> CountAsync(IEnumerable<string> ids, CancellationToken cancellationToken)
        => CountAsync(doc => ids.Contains(doc.Id), cancellationToken);

    public Task<long> CountAsync(Expression<Func<TDocument, bool>> predicate, CancellationToken cancellationToken)
        => _collection.CountDocumentsAsync(predicate, null, cancellationToken);

    public Task<long> CountAsync(CancellationToken cancellationToken)
        => CountAsync(doc => true, cancellationToken);

    private static SortDefinition<TDocument> GetSortDefinition(
        SortDefinitionBuilder<TDocument> sortDefinitionBuilder, string propertyName, bool ascending) 
    { 
        var parameter = Expression.Parameter(typeof(TDocument), "x"); 
        var lambda = Expression.Lambda<Func<TDocument, object>>(
            Expression.Convert(
                Expression.Property(parameter, propertyName),
                typeof(object)),
            parameter
            );

        return ascending 
            ? sortDefinitionBuilder.Ascending(lambda) 
            : sortDefinitionBuilder.Descending(lambda);
    }

    public async Task<(ICollection<TDocument>, long)> GetPagedAsync(Expression<Func<TDocument, bool>> predicate,
        IDictionary<string, bool> sortBy, int page, int limit, CancellationToken cancellationToken)
    {
        SortDefinitionBuilder<TDocument> sortDefinitionBuilder = Builders<TDocument>.Sort;
        SortDefinition<TDocument>? sortDefinition = null;

        foreach (var item in sortBy) 
        {
            sortDefinition = GetSortDefinition(sortDefinitionBuilder, item.Key, item.Value);
        }

        var options = new FindOptions<TDocument, TDocument>()
        {
            Sort = sortDefinition,
            Skip = (page - 1) * limit,
            Limit = limit
        };
        
        long count = await CountAsync(predicate, cancellationToken);
        var itemsPaged = await GetAsync(predicate, options, cancellationToken);

        return (itemsPaged, count);
    }

    public async Task<TDocument> AddAsync(TDocument document, CancellationToken cancellationToken)
    {
        await _collection.InsertOneAsync(document, new InsertOneOptions(), cancellationToken);
        return document;
    }

    public Task UpdateAsync(string id, TDocument document, CancellationToken cancellationToken)
        => _collection.ReplaceOneAsync(doc => doc.Id == id, document, new ReplaceOptions(), cancellationToken);

    public Task DeleteAsync(TDocument document, CancellationToken cancellationToken) 
        => _collection.DeleteOneAsync(doc => doc.Id == document.Id, cancellationToken);

    public Task DeleteAsync(Expression<Func<TDocument, bool>> predicate, CancellationToken cancellationToken)
        => _collection.DeleteOneAsync(predicate, cancellationToken);

    public async Task<bool> DeleteAsync(string id, CancellationToken cancellationToken)
    {
        var result = await _collection.DeleteOneAsync(doc => doc.Id == id, cancellationToken);
        return result.IsAcknowledged;
    }

    public Task BulkAddAsync(IEnumerable<TDocument> documents, CancellationToken cancellationToken) =>
        documents.Any() ? _collection.InsertManyAsync(documents, null, cancellationToken) : Task.CompletedTask;


    public Task BulkUpdateAsync(IEnumerable<TDocument> documents, CancellationToken cancellationToken)
    {
        var filterBuilder = Builders<TDocument>.Filter;
        var updates = documents.Select(doc => 
        {
            var filter = filterBuilder.Where(x => x.Id == doc.Id);
            return new ReplaceOneModel<TDocument>(filter, doc);
        });
        return updates.Any() ? _collection.BulkWriteAsync(updates, null, cancellationToken) : Task.CompletedTask;
    }

    public Task BulkCreateAsync(IEnumerable<TDocument> documents, CancellationToken cancellationToken)
    {
        var inserts = documents.Select(doc => new InsertOneModel<TDocument>(doc));
        return inserts.Any() ? _collection.BulkWriteAsync(inserts, null, cancellationToken) : Task.CompletedTask;
    }

    public Task DeleteManyAsync(IEnumerable<string> ids, CancellationToken cancellationToken)
    {
        var filter = Builders<TDocument>.Filter.In(doc => doc.Id, ids);
        return _collection.DeleteManyAsync(filter, cancellationToken);
    }

    public Task DeleteManyAsync(Expression<Func<TDocument, bool>> predicate, CancellationToken cancellationToken)
        => _collection.DeleteManyAsync(predicate, cancellationToken);

    public Task DeleteManyAsync(IEnumerable<TDocument> documents, CancellationToken cancellationToken)
        => DeleteManyAsync(documents.Select(doc => doc.Id), cancellationToken);

    public Task DeleteAllAsync(CancellationToken cancellationToken)
        => _collection.DeleteManyAsync(doc => true, cancellationToken);

    public async Task DropAsync(CancellationToken cancellationToken)
        => await _database.DropCollectionAsync(GetCollectionName(), cancellationToken);
    
    private async Task CreateCollectionAsync(CancellationToken cancellationToken)
        => await _database.CreateCollectionAsync(GetCollectionName(), cancellationToken: cancellationToken);
}
