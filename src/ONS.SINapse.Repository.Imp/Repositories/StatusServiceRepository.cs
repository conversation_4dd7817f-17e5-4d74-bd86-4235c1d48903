using MongoDB.Driver;
using ONS.SINapse.Entities.Entities;
using ONS.SINapse.Repository.IContext;
using ONS.SINapse.Repository.Imp.Repositories.Base;
using ONS.SINapse.Repository.IRepository;

namespace ONS.SINapse.Repository.Imp.Repositories;

public class StatusServiceRepository :  MongoRepository<StatusService>, IStatusServiceRepository
{
    public StatusServiceRepository(IMongoConnection mongoConnection) : base(mongoConnection)
    {
    }

    public async Task UpdateStatusAsync(string service, bool health, Exception? exception , string? error, CancellationToken cancellationToken)
    {
        var document = new StatusService(service, health, exception, error);
        var filter = Builders<StatusService>.Filter.Eq(doc => doc.Id, service);
        var options = new ReplaceOptions { IsUpsert = true };
    
        await _collection.ReplaceOneAsync(filter, document, options, cancellationToken);
    }
}