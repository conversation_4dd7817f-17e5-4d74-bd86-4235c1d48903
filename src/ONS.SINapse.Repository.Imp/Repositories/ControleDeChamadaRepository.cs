using ONS.SINapse.Entities.Entities;
using ONS.SINapse.Repository.IContext;
using ONS.SINapse.Repository.Imp.Repositories.Base;
using ONS.SINapse.Repository.IRepository;

namespace ONS.SINapse.Repository.Imp.Repositories;

public class ControleDeChamadaRepository : MongoRepository<ControleDeChamada>, IControleDeChamadaRepository
{
    public ControleDeChamadaRepository(IMongoConnection mongoConnection) : base(mongoConnection)
    {
    }

    public async Task<ControleDeChamada?> BuscarChamada(CancellationToken cancellationToken)
    {
        return await GetOneAsync(a => true, cancellationToken);
    }
}