using ONS.SINapse.Repository.Imp.Store;
using System.Linq.Expressions;
using ONS.SINapse.Entities.Entities;
using ONS.SINapse.Repository.IRepository.InMemory;

namespace ONS.SINapse.Repository.Imp.Repositories.InMemory;

public class SolicitacaoMemoryRepository : ISolicitacaoMemoryRepository
{
    public ICollection<Solicitacao> Get(Expression<Func<Solicitacao, bool>> predicate)
    {
       return SolicitacaoStore.Solicitacoes.AsQueryable().Where(predicate).ToList();
    }
}
