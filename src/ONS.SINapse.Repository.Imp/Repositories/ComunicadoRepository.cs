using ONS.SINapse.Entities.Entities;
using ONS.SINapse.Repository.IContext;
using ONS.SINapse.Repository.Imp.Repositories.Base;
using ONS.SINapse.Repository.IRepository;

namespace ONS.SINapse.Repository.Imp.Repositories;

public class ComunicadoRepository : MongoRepository<Comunicado>, IComunicadoRepository
{
    public ComunicadoRepository(IMongoConnection mongoConnection)
        : base(mongoConnection)
    {
    }
}