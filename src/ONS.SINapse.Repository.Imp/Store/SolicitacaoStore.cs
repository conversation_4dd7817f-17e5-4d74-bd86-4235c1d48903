using System.Collections.Concurrent;
using System.Collections.Immutable;
using System.ComponentModel.DataAnnotations;
using ONS.SINapse.Entities.Entities;

namespace ONS.SINapse.Repository.Imp.Store;

public static class SolicitacaoStore
{
    private static ConcurrentDictionary<string, Solicitacao> SolicitacoesDictionary { get; } = new();
    
    public static bool Remove(string key)
    {
        return SolicitacoesDictionary.TryRemove(key, out _);
    }
    
    public static void AddOrUpdate([Required] Solicitacao value)
    {
        SolicitacoesDictionary[value.Id] = value;
    }
    
    public static ImmutableList<Solicitacao> Solicitacoes => SolicitacoesDictionary.Values.ToImmutableList();

    public static void Clear()
    {
       SolicitacoesDictionary.Clear();
    }
}