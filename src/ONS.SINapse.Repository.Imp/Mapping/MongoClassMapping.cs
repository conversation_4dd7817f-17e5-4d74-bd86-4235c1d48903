using MongoDB.Bson.Serialization;
using MongoDB.Bson.Serialization.Conventions;
using ONS.SINapse.Entities.Entities;
using ONS.SINapse.Entities.Entities.Base;

namespace ONS.SINapse.Repository.Imp.Mapping;

public static class MongoClassMapping
{
    public static void RegisterMappings()
    {
        BsonClassMap.RegisterClassMap<Entidade>();
        
        BsonClassMap.RegisterClassMap<Solicitacao>(cm =>
        {
            cm.AutoMap();
            
            var propertyInfo = typeof(Solicitacao).GetProperties()
                .First(x => x.Name == "Id");
            
            var member = new BsonMemberMap(cm, propertyInfo);
            
            member.SetIdGenerator(new SolicitacaoIdGenerator());
            
            cm.SetIdMember(member);
            cm.SetIgnoreExtraElements(true);
        });
    }
}