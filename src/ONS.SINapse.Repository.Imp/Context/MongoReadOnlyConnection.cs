using MongoDB.Driver;
using MongoDB.Driver.Linq;
using ONS.SINapse.Repository.IContext;
using ONS.SINapse.Shared.CustomAttributes;
using System.Linq.Expressions;

namespace ONS.SINapse.Repository.Imp.Context;

public interface IMongoReadOnlyConnection
{
    IQueryable<TBsonDocument> ObterQueryable<TBsonDocument>();

    Task<IEnumerable<TBsonDocument>> AggregateAsync<TBsonDocument>(PipelineDefinition<TBsonDocument, TBsonDocument> pipeline, CancellationToken cancellationToken);

    Task<List<T>> ExecutarConsultaAsync<T>(IQueryable<T> query, CancellationToken cancellationToken);

    Task<T> BuscarFirstOrDefaultAsync<T>(IQueryable<T> query, Expression<Func<T, bool>> filtro, CancellationToken cancellationToken);
}

public class MongoReadOnlyConnection : IMongoReadOnlyConnection
{
    private readonly IMongoDatabase _mongoDatabase;

    public MongoReadOnlyConnection(IMongoConnection mongoConnection)
    {
        _mongoDatabase = mongoConnection.GetDatabase();
    }
    
    public IQueryable<TBsonDocument> ObterQueryable<TBsonDocument>()
    {
        var nome = ObterCollectionName<TBsonDocument>();

        return _mongoDatabase.GetCollection<TBsonDocument>(nome).AsQueryable();
    }

    public async Task<IEnumerable<TBsonDocument>> AggregateAsync<TBsonDocument>(PipelineDefinition<TBsonDocument, TBsonDocument> pipeline, CancellationToken cancellationToken)
    {
        var nome = ObterCollectionName<TBsonDocument>();
        var collection = _mongoDatabase.GetCollection<TBsonDocument>(nome);

        var cursor = await collection
            .AggregateAsync(pipeline, new AggregateOptions(), cancellationToken);

        return await cursor.ToListAsync(cancellationToken);
    }

    public async Task<List<T>> ExecutarConsultaAsync<T>(IQueryable<T> query, CancellationToken cancellationToken)
    {
        return await query.ToListAsync(cancellationToken);
    }

    public async Task<T> BuscarFirstOrDefaultAsync<T>(IQueryable<T> query, Expression<Func<T,bool>> filtro, CancellationToken cancellationToken)
    {
        return await query.FirstOrDefaultAsync(filtro, cancellationToken).ConfigureAwait(false);
    }

    private static string ObterCollectionName<TBsonDocument>()
    {
        return typeof(TBsonDocument)
                   .GetCollectionName() ??
               throw new Exception(
                   "[MongoReadOnlyConnection][ObterCollectionName] - Bson document não contém o attribute de collection name '[BsonCollectionAttribute]'");
    }
    
}