using Microsoft.Extensions.Logging;
using MongoDB.Bson;
using MongoDB.Driver;
using ONS.SINapse.Repository.IContext;

namespace ONS.SINapse.Repository.Imp.Context;


public class MongoConnection : IMongoConnection
{
    private readonly ILogger<MongoConnection> _logger;
    private readonly IMongoDatabase _mongoDatabase;

    public MongoConnection(ILogger<MongoConnection> logger, IMongoDatabase mongoDatabase)
    {
        _logger = logger;
        _mongoDatabase = mongoDatabase;
    }
    
    public IMongoDatabase GetDatabase() => _mongoDatabase;

    public bool IsConnected()
    {
        try
        {
            BsonDocumentCommand<BsonDocument> command = new(BsonDocument.Parse("{ping:1}"));
            
            GetDatabase().RunCommand(command);
            
            return true;
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Erro ao estabelecer uma conexão com o mongo.");
            return false;
        }
    }
}

