<Project Sdk="Microsoft.NET.Sdk">

	<!-- Propriedades específicas do projeto -->
	<PropertyGroup>
		<!-- Propriedades específicas serão herdadas do Directory.Build.props -->
	</PropertyGroup>

	<!-- Referências de projeto -->
	<ItemGroup>
		<ProjectReference Include="..\ONS.SINapse.Repository\ONS.SINapse.Repository.csproj" />
		<ProjectReference Include="..\ONS.SINapse.Shared\ONS.SINapse.Shared.csproj" />
	</ItemGroup>

	<!-- Pacotes NuGet -->
	<ItemGroup>
		<PackageReference Include="FirebaseDatabase.net" />
		<PackageReference Include="Google.Apis.FirebaseCloudMessaging.v1" />
	</ItemGroup>
	<ItemGroup>
	  <Compile Remove="Dados\**" />
	</ItemGroup>
	<ItemGroup>
	  <EmbeddedResource Remove="Dados\**" />
	</ItemGroup>
	<ItemGroup>
	  <None Remove="Dados\**" />
	</ItemGroup>

</Project>
