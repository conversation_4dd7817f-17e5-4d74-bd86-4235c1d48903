<Project Sdk="Microsoft.NET.Sdk">

	<!-- Propriedades específicas do projeto -->
	<PropertyGroup>
		<!-- Propriedades específicas serão herdadas do Directory.Build.props -->
	</PropertyGroup>

	<!-- Referências de projeto -->
	<ItemGroup>
		<ProjectReference Include="..\ONS.SINapse.Repository\ONS.SINapse.Repository.csproj" />
		<ProjectReference Include="..\ONS.SINapse.Shared\ONS.SINapse.Shared.csproj" />
	</ItemGroup>

	<!-- Pacotes NuGet específicos do Repository -->
	<ItemGroup>
		<PackageReference Include="MongoDB.Bson" />
		<PackageReference Include="MongoDB.Driver" />
		<PackageReference Include="MongoDB.Driver.Core.Extensions.DiagnosticSources" />
		<PackageReference Include="FirebaseAdmin" />
		<PackageReference Include="FirebaseDatabase.net" />
		<PackageReference Include="FirebaseAuthentication.net" />
		<PackageReference Include="Google.Apis.FirebaseCloudMessaging.v1" />
		<PackageReference Include="LinqKit.Core" />
	</ItemGroup>
	<ItemGroup>
	  <Compile Remove="Dados\**" />
	</ItemGroup>
	<ItemGroup>
	  <EmbeddedResource Remove="Dados\**" />
	</ItemGroup>
	<ItemGroup>
	  <None Remove="Dados\**" />
	</ItemGroup>

</Project>
