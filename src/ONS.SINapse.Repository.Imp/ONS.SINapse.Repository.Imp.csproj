<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
	</PropertyGroup>

	<ItemGroup>
		<ProjectReference Include="..\ONS.SINapse.Repository\ONS.SINapse.Repository.csproj" />
		<ProjectReference Include="..\ONS.SINapse.Shared\ONS.SINapse.Shared.csproj" />
	</ItemGroup>
	<ItemGroup>
		<PackageReference Include="FirebaseDatabase.net" Version="4.2.0" />
		<PackageReference Include="Google.Apis.FirebaseCloudMessaging.v1" Version="1.68.0.3603" />
	</ItemGroup>
	<ItemGroup>
	  <Compile Remove="Dados\**" />
	</ItemGroup>
	<ItemGroup>
	  <EmbeddedResource Remove="Dados\**" />
	</ItemGroup>
	<ItemGroup>
	  <None Remove="Dados\**" />
	</ItemGroup>

</Project>
