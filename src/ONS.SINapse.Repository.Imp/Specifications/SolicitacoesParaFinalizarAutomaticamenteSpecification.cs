using ONS.SINapse.Entities.Entities;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Repository.Imp.Specifications.Base;
using ONS.SINapse.Shared.DTO;
using System.Linq.Expressions;

namespace ONS.SINapse.Repository.Imp.Specifications;

public class SolicitacoesParaFinalizarAutomaticamenteSpecification : Specification<Solicitacao>
{
    private readonly int _minutosAntesDeFinalizar;

    public SolicitacoesParaFinalizarAutomaticamenteSpecification(int minutosAntesDeFinalizar)
	{
        _minutosAntesDeFinalizar = minutosAntesDeFinalizar;
    }

    public override Expression<Func<Solicitacao, bool>> Criteria()
    {
        return solicitacao =>
            (StatusDeSolicitacao.Confirmada == solicitacao.Status || StatusDeSolicitacao.CienciaInformada == solicitacao.Status)
            && solicitacao.UpdatedAt <= DateTime.Now.AddMinutes(-_minutosAntesDeFinalizar);
    }

    public static SolicitacoesParaFinalizarAutomaticamenteSpecification New(int minutosAntesDeFinalizar) 
        => new(minutosAntesDeFinalizar);
}
