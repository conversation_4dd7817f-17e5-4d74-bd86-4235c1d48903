using System.Linq.Expressions;
using LinqKit;
using ONS.SINapse.Entities.Entities;
using ONS.SINapse.Repository.Imp.Specifications.Base;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Enums;

namespace ONS.SINapse.Repository.Imp.Specifications;

public class ConsultaDeComunicadosSpecification : Specification<Comunicado>
{
    private readonly ConsultaDeComunicadosDto _input;
    public ConsultaDeComunicadosSpecification(ConsultaDeComunicadosDto input)
    {
        _input = input;
    }

    public override Expression<Func<Comunicado, bool>> Criteria()
    {
        var expresion = PredicateBuilder.New<Comunicado>(c => false);
        _input.CodigosDosDestinatarios.ToList().ForEach(a =>
            expresion = expresion.Or(c =>  c.UpdatedAt >= _input.DataDeAtualizacaoLimite && c.Destinatarios != null && c.Destinatarios.Any(d => d.Codigo.Equals(a))));

        return expresion
            .Or(c => c.UpdatedAt >= _input.DataDeAtualizacaoLimite && c.TipoDeComunicado == TipoDeComunicado.Todos)
            .Or(c => c.UpdatedAt >= _input.DataDeAtualizacaoLimite &&
                     _input.CodigoCentroEmitente.Contains(c.Origem.Codigo));
    }
}