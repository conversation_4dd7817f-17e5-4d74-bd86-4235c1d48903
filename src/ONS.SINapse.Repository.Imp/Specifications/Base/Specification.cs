using System.Linq.Expressions;
using LinqKit;
using ONS.SINapse.Entities.Entities.Base;

namespace ONS.SINapse.Repository.Imp.Specifications.Base;

public abstract class Specification<TEntity>
    where TEntity : Entidade
{
    protected readonly Expression<Func<TEntity, bool>> PredicateExpression;

    protected Specification()
    {
        PredicateExpression = PredicateBuilder.New<TEntity>(s => true);
    }

    protected Specification(Expression<Func<TEntity, bool>> predicateExpression)
    {
        PredicateExpression = predicateExpression;
    }

    public abstract Expression<Func<TEntity, bool>> Criteria();

    public bool IsSatisfiedBy(TEntity entity)
    {
        var predicate = Criteria().Compile();
        return predicate(entity);
    }
}