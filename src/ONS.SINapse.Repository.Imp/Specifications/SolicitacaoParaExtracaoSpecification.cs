using System.Linq.Expressions;
using ONS.SINapse.Entities.Entities;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Repository.Imp.Specifications.Base;
using ONS.SINapse.Shared.Settings;

namespace ONS.SINapse.Repository.Imp.Specifications;

public class SolicitacaoParaExtracaoSpecification : ExtracaoSpecification<Solicitacao>
{
    public SolicitacaoParaExtracaoSpecification(ExtracaoDeDadoSettings extracaoDeDadoSettings) 
        : base(extracaoDeDadoSettings)
    {
    }

    public override Expression<Func<Solicitacao, bool>> Criteria()
    {
        var dataLimite = DateTime.UtcNow.AddHours(-_extracaoDeDadoSettings.TempoDeVidaDosRegistrosEmHoras);
        var statusFinais = new[] { StatusDeSolicitacao.Cancelada, StatusDeSolicitacao.Finalizada };
        return solicitacao => !solicitacao.EnviadoAoAnalitico &&
                              solicitacao.CreatedAt <= dataLimite && 
                              statusFinais.Contains(solicitacao.Status);
    }
}