using System.Reflection;

namespace ONS.SINapse.Dados.Shared.CustomAttributes;

[AttributeUsage(AttributeTargets.Class, Inherited = false)]
public class CacheKeyAttribute : Attribute
{
    public string Key { get; }

    public CacheKeyAttribute(string key)
    {
        Key = key;
    }
}

public static class Cache<PERSON>eyHelper
{
    public static string? GetCacheKey(this Type documentType) => documentType.GetCustomAttribute<CacheKeyAttribute>()?.Key;
}