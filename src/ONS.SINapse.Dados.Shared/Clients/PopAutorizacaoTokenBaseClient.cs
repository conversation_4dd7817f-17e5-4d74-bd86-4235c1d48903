using System.Text.Json;
using Microsoft.Extensions.Options;
using ONS.SINapse.Dados.Shared.DTO;
using ONS.SINapse.Dados.Shared.Settings;

namespace ONS.SINapse.Dados.Shared.Clients;

public interface IPopAutorizacaoTokenClient
{
    Task<string> GetAccessToken(string appName, string? customTokenUrl = null,
        CancellationToken cancellationToken = default);
}

public abstract class PopAutorizacaoTokenBaseClient : IPopAutorizacaoTokenClient
{
    private readonly HttpClient _httpClient;
    private readonly ConfiguracoesPopAuthSettings _configuracoesPopAuth;

    private PopAutorizacaoTokenDto? _tokenDto;
    private DateTime _dataExpiracaoToken;

    protected PopAutorizacaoTokenBaseClient(HttpClient httpClient,
        IOptions<ConfiguracoesPopAuthSettings> configuracoesPopAuthSettings)
    {
        _httpClient = httpClient;
        _configuracoesPopAuth = configuracoesPopAuthSettings.Value;
    }

    public async Task<string> GetAccessToken(string appName, string? customTokenUrl = null,
        CancellationToken cancellationToken = default)
    {
        if (_tokenDto == null || !TokenValido)
        {
            var tokenPath = string.IsNullOrWhiteSpace(customTokenUrl)
                ? _configuracoesPopAuth.TokenUrl
                : customTokenUrl;

            using var content = new FormUrlEncodedContent(GetContentParaAccessToken(appName));

            using var requestMessage = BuildRequest(HttpMethod.Post, tokenPath, content);

            using var response = await _httpClient.SendAsync(requestMessage, cancellationToken);
            response.EnsureSuccessStatusCode();

            await using var responseContentStream = await response.Content.ReadAsStreamAsync(cancellationToken);

            _tokenDto = await JsonSerializer.DeserializeAsync<PopAutorizacaoTokenDto>(responseContentStream,
                cancellationToken: cancellationToken);

            if (_tokenDto is null || string.IsNullOrWhiteSpace(_tokenDto.AccessToken))
                throw new InvalidOperationException(
                    $"Erro ao obter token do M. \n Status Code: {response.StatusCode}; \n Content: {response.Content}");

            AtualizarDataExpiracaoToken();
        }

        return _tokenDto.AccessToken;
    }

    private bool TokenValido => DateTime.Now <= _dataExpiracaoToken;

    private void AtualizarDataExpiracaoToken()
        => _dataExpiracaoToken = DateTime.Now.AddSeconds(_tokenDto?.ExpiresIn ?? 0);

    private List<KeyValuePair<string, string>> GetContentParaAccessToken(string appName)
    {
        var formContent = new List<KeyValuePair<string, string>>
        {
            new("client_id", appName),
            new("grant_type", _configuracoesPopAuth.GrantTypeAccessToken),
            new("username", _configuracoesPopAuth.UserName),
            new("password", _configuracoesPopAuth.Password),
        };

        return formContent;
    }

    private FormUrlEncodedContent GetContentParaRefreshToken()
    {
        var formContent = new List<KeyValuePair<string, string>>
        {
            new("client_id", _configuracoesPopAuth.ClientId),
            new("refresh_token", _tokenDto?.RefreshToken ?? string.Empty),
            new("grant_type", _configuracoesPopAuth.GrantTypeRefreshToken)
        };

        return new FormUrlEncodedContent(formContent);
    }

    private HttpRequestMessage BuildRequest(HttpMethod method, string path, HttpContent content)
    {
        var request = new HttpRequestMessage(method, path);
        request.Content = content;

        request.Headers.Add("Origin", _configuracoesPopAuth.Origin);

        if (!string.IsNullOrWhiteSpace(_tokenDto?.AccessToken))
            request.Headers.Add("Authorization", _tokenDto.AccessToken);

        return request;
    }
}