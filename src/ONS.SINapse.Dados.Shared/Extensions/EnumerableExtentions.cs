
using System.Linq.Expressions;

namespace ONS.SINapse.Dados.Shared.Extensions;

public static class EnumerableExtentions
{
    public static bool Empty<TValue>(this IEnumerable<TValue>? list)
        where TValue : class
    {
        return list is null || !list.Any();
    }

    public static bool NotEmpty<TValue>(this IEnumerable<TValue>? list)
        where TValue : class
    {
        return !list.Empty<TValue>();
    }

    public static void ForAll<T>(this IEnumerable<T> enumerable, Action<T> action)
    {
        foreach (var feature in enumerable)
        {
            action(feature);
        }
    }
    
    public static IEnumerable<T> OrderBy<T>(this IEnumerable<T> enumerable, IDictionary<string, bool> sortBy)
    {
        var isFirst = true;
        foreach (var sort in sortBy)
        {
            
            var parameter = Expression.Parameter(typeof(T), "x"); 
            var lambda = Expression.Lambda<Func<T, object>>(
                Expression.Convert(
                    Expression.Property(parameter, sort.Key),
                    typeof(object)),
                parameter
            );
            if(isFirst)
            {
                enumerable = sort.Value 
                    ? enumerable.OrderBy(lambda.Compile())
                    : enumerable.OrderByDescending(lambda.Compile());
                isFirst = false;
            }
            else
            {
                enumerable = sort.Value 
                    ? ((IOrderedEnumerable<T>)enumerable).ThenBy(lambda.Compile())
                    : ((IOrderedEnumerable<T>)enumerable).ThenByDescending(lambda.Compile());
            }
        }

        return enumerable;
    }
}