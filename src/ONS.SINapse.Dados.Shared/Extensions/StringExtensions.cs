using System.Text;
using System.Text.RegularExpressions;

namespace ONS.SINapse.Dados.Shared.Extensions;

public static partial class StringExtensions
{
    /// <summary>
    /// Convert camelCase to snwke_case
    /// </summary>
    /// <param name="str"></param>
    /// <returns></returns>
    public static string ToSnakeCase(this string str)
    {
        return string.Concat(str.Select((x, i) => i > 0 && char.IsUpper(x) ? "_" + x.ToString() : x.ToString())).ToLower();
    }
    
    /// <summary>
    ///  Returns true if the string is a Guid, or false if it's null or not a string representing a Guid. 
    /// </summary>
    /// <param name="value">string | null</param>
    /// <returns>bool</returns>
    public static bool IsGuid(this string? value)
        => Guid.TryParse(value, out _);

    public static string RemoverCarcteresEspeciais(this string? value)
    {
        string? normalizedString =
            !string.IsNullOrEmpty(value) ? value.Normalize(NormalizationForm.FormD) : string.Empty;
        
        Regex? regex = AcentosRegex();

        return regex.Replace(normalizedString, string.Empty).Normalize(NormalizationForm.FormC);
    }

    [GeneratedRegex(@"\p{M}")]
    private static partial Regex AcentosRegex();
}
