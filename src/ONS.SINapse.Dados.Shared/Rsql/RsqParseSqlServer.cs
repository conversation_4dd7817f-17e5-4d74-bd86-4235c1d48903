using DotnetLeague.SimpleRsqlParse;

namespace ONS.SINapse.Dados.Shared.Rsql;

public class RsqParseSqlServer: IRsqParse
{
    public RsqlQuery Parse(string? rsqlQuery)
    {
        if (string.IsNullOrEmpty(rsqlQuery?.Trim()))
        {
            return new RsqlQuery();
        }

        var tokenizer = new Tokenizer(rsqlQuery);
        var parser = new SimpleRsqlParser(tokenizer);
        
        (string whereClause, Dictionary<string, object> parameters) = parser.Parse();
        
        return new RsqlQuery
        {
            SqlWhere = whereClause,
            Parameters = parameters
        };
    }
    public static IRsqParse Instance() => new RsqParseSqlServer();
}
