using System.Globalization;
using System.Text.RegularExpressions;

namespace ONS.SINapse.Dados.Shared.Helpers
{
    public partial class NaturalSortHelper : IComparer<string>
    {
        public static NaturalSortHelper Instance { get; } = new NaturalSortHelper();

        public int Compare(string? x, string? y)
        {
            if (x == null || y == null)
                return x == null ? -1 : 1;

            var valueX = DigitRegex().Replace(x, m => m.Value.PadLeft(10, '0'));
            var valueY = DigitRegex().Replace(y, m => m.Value.PadLeft(10, '0'));
            return string.Compare(valueX, valueY, CultureInfo.CurrentCulture, CompareOptions.IgnoreCase);
        }

        [GeneratedRegex(@"\d+")]
        public static partial Regex DigitRegex();

        [GeneratedRegex(@"\s+")]
        public static partial Regex WhitespaceRegex();

        [GeneratedRegex(@"^\[.*?\]\s*-\s*")]
        public static partial Regex PrefixWithBracketRegex();
    }
}
