using System.Net;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using ons.configit.entidade;
using Simple.OData.Client;

namespace ONS.SINapse.Dados.Shared.ConfigIt;

internal class ConfigItConfigurationProvider : ConfigurationProvider
{
    protected IDictionary<string, Dictionary<string, string>>? DeParaConfigIt = new Dictionary<string, Dictionary<string, string>>();

    private string _configItFileName = "configit.json";

    private Action<Dictionary<string, string>> _optionsAction;

    public ConfigItConfigurationProvider(Action<Dictionary<string, string>> optionsAction)
    {
        _optionsAction = optionsAction;
    }

    public override void Load()
    {
        LoadDeParaConfigIt();
        Dictionary<string, string> dictionary = new Dictionary<string, string>();
        _optionsAction(dictionary);
        base.Data = CreateAndSaveDefaultValues(dictionary);
    }

    private static IDictionary<string, string> CreateAndSaveDefaultValues(Dictionary<string, string> options)
    {
        string amb = options["-amb"];

        ODataClientSettings settings = new ODataClientSettings(new Uri(options["-r"]), new NetworkCredential(options["-user"], options["-password"]));
        ODataClient oDataClient = new ODataClient(settings);

        IBoundClient<AmbienteParametro> boundClient = oDataClient.For<AmbienteParametro>();
        Task<IEnumerable<AmbienteParametro>> task = boundClient.Expand(a => a.Ambiente).Expand(a => a.Parametro).Filter(x => x.Ambiente.CodigoAmbiente == amb)
            .FindEntriesAsync();
        task.Wait();

        var result = task.Result.ToDictionary(a => a.Parametro.CodigoParametro, a => a.ValAmbienteParametro);

        return result;
    }

    private void LoadDeParaConfigIt()
    {
        try
        {
            string value = File.ReadAllText(_configItFileName);
            DeParaConfigIt = JsonConvert.DeserializeObject<Dictionary<string, Dictionary<string, string>>>(value);
        }
        catch (Exception)
        {
            // ignored
        }
    }

    public override bool TryGet(string key, out string value)
    {
        if (DeParaConfigIt != null && DeParaConfigIt.ContainsKey(key))
        {
            return base.TryGet(DeParaConfigIt[key]["depara"], out value);
        }

        return base.TryGet(key, out value);
    }
}

public class ConfigItConfigurationSource : IConfigurationSource
{
    private readonly Action<Dictionary<string, string>> _optionsAction;

    public ConfigItConfigurationSource(Action<Dictionary<string, string>> optionsAction)
    {
        _optionsAction = optionsAction;
    }

    public IConfigurationProvider Build(IConfigurationBuilder builder)
    {
        return new ConfigItConfigurationProvider(_optionsAction);
    }
} 

public static class ConfigurationBuilderExtensions
{
    public static IConfigurationBuilder AddConfigItConfiguration(this IConfigurationBuilder builder, Action<Dictionary<string, string>> optionsAction)
    {
        return builder.Add(new ConfigItConfigurationSource(optionsAction));
    }
}   