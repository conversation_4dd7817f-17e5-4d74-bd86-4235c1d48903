using System.Net;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using ons.configit.entidade;
using Simple.OData.Client;

namespace ONS.SINapse.Dados.Shared.ConfigIt;

internal sealed class ConfigItConfigurationProvider : ConfigurationProvider
{
    private Dictionary<string, Dictionary<string, string>>? _deParaConfigIt = [];

    private const string ConfigItFileName = "configit.json";

    private readonly Action<Dictionary<string, string>> _optionsAction;

    public ConfigItConfigurationProvider(Action<Dictionary<string, string>> optionsAction)
    {
        _optionsAction = optionsAction;
    }

    public override void Load()
    {
        LoadDeParaConfigIt();
        var dictionary = new Dictionary<string, string>();
        _optionsAction(dictionary);
        Data = CreateAndSaveDefaultValues(dictionary)!;
    }

    private static Dictionary<string, string> CreateAndSaveDefaultValues(Dictionary<string, string> options)
    {
        string amb = options["-amb"];

        var settings = new ODataClientSettings(new Uri(options["-r"]), new NetworkCredential(options["-user"], options["-password"]));
        var oDataClient = new ODataClient(settings);

        IBoundClient<AmbienteParametro> boundClient = oDataClient.For<AmbienteParametro>();
        Task<IEnumerable<AmbienteParametro>> task = boundClient.Expand(a => a.Ambiente).Expand(a => a.Parametro).Filter(x => x.Ambiente.CodigoAmbiente == amb)
            .FindEntriesAsync();
        task.Wait();

        var result = task.Result.ToDictionary(a => a.Parametro.CodigoParametro, a => a.ValAmbienteParametro);

        return result;
    }

    private void LoadDeParaConfigIt()
    {
        try
        {
            string value = File.ReadAllText(ConfigItFileName);
            _deParaConfigIt = JsonConvert.DeserializeObject<Dictionary<string, Dictionary<string, string>>>(value);
        }
        catch (Exception)
        {
            // ignored
        }
    }

    public override bool TryGet(string key, out string? value)
    {
        if (_deParaConfigIt != null && _deParaConfigIt.TryGetValue(key, out Dictionary<string, string>? deParaConfig))
        {
            return base.TryGet(deParaConfig["depara"], out value);
        }

        return base.TryGet(key, out value);
    }
}

public class ConfigItConfigurationSource : IConfigurationSource
{
    private readonly Action<Dictionary<string, string>> _optionsAction;

    public ConfigItConfigurationSource(Action<Dictionary<string, string>> optionsAction)
    {
        _optionsAction = optionsAction;
    }

    public IConfigurationProvider Build(IConfigurationBuilder builder)
    {
        return new ConfigItConfigurationProvider(_optionsAction);
    }
} 

public static class ConfigurationBuilderExtensions
{
    public static IConfigurationBuilder AddConfigItConfiguration(this IConfigurationBuilder builder, Action<Dictionary<string, string>> optionsAction)
    {
        return builder.Add(new ConfigItConfigurationSource(optionsAction));
    }
}   
