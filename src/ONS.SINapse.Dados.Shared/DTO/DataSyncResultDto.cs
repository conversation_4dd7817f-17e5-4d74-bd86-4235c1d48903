namespace ONS.SINapse.Dados.Shared.DTO;

public class DataSyncResultDto
{
    public DateTime Inicio { get; set; }
    public double DuracaoEmSegundos { get; set; }
    public bool Sucesso { get; set; }
    public string Mensagem { get; set; }

    public IEnumerable<string> Tabelas { get; set; }

    public DataSyncResultDto(DateTime inicio, bool sucesso, string mensagem)
    {
        Inicio = inicio;
        DuracaoEmSegundos = (DateTime.Now - inicio).TotalSeconds;
        Sucesso = sucesso;
        Mensagem = mensagem;
        Tabelas = Array.Empty<string>();
    }

    public DataSyncResultDto(DateTime inicio, bool sucesso, string mensagem, IEnumerable<string> tabelas)
    {
        Inicio = inicio;
        DuracaoEmSegundos = (DateTime.Now - inicio).TotalSeconds;
        Sucesso = sucesso;
        Mensagem = mensagem;
        Tabelas = tabelas;
    }
}