using System.Text.Json.Serialization;

namespace ONS.SINapse.Dados.Shared.DTO;

public sealed record AuthResultDto
{
    [JsonPropertyName("access_token")]
    public string AccessToken { get; set; }

    [JsonPropertyName("token_type")]
    public string TokenType { get; set; }

    [JsonPropertyName("expires_in")]
    public long ExpiresIn { get; set; }

    [JsonPropertyName("operations")]
    public IEnumerable<string> Operations { get; set; }

    [JsonPropertyName("refresh_token")]
    public string RefreshToken { get; set; }

    [JsonPropertyName("firebase_token")]
    public string FirebaseCustomToken { get; set; }
}
