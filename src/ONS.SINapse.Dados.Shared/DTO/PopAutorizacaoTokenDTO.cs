using System.Text.Json.Serialization;

namespace ONS.SINapse.Dados.Shared.DTO;

public class PopAutorizacaoTokenDto
{
    public PopAutorizacaoTokenDto(
        string accessToken,
        string tokenType,
        double expiresIn,
        string refreshToken)
    {
        AccessToken = accessToken;
        TokenType = tokenType;
        ExpiresIn = expiresIn;
        RefreshToken = refreshToken;
    }

    [JsonPropertyName("access_token")]
    public string AccessToken { get; init; }
        
    [JsonPropertyName("token_type")]
    public string TokenType { get; init; }
        
    [JsonPropertyName("expires_in")]
    public double ExpiresIn { get; init; }
        
    [JsonPropertyName("refresh_token")]
    public string RefreshToken { get; init; }
}