using System.Text.Json;
using Microsoft.Extensions.Options;
using ONS.SINapse.Dados.Shared.DTO;
using ONS.SINapse.Dados.Shared.Settings;

namespace ONS.SINapse.Dados.Shared.Services;

public interface IAuthService
{
    Task<AuthResultDto?> AuthorizeAsync(AuthDto dto);
    Task<AuthResultDto?> RefreshTokenAsync(RefreshTokenDto dto);
}

public class PopAuthException : Exception
{
    public PopAuthException(Exception exception)
        : base("Login/senha errados ou usuário sem permissão! Após 6 tentativas, o usuário será bloqueado no POP.",
            exception)
    {
    }
}

public class AuthService : IAuthService
{
    private readonly HttpClient _httpClient;
    private readonly ConfiguracoesPopAuthSettings _configuracoesAuth;

    public AuthService(HttpClient httpClient,
        IOptions<ConfiguracoesPopAuthSettings> configuracoesAuthSettings)
    {
        _httpClient = httpClient;
        _configuracoesAuth = configuracoesAuthSettings.Value;
    }

    public async Task<AuthResultDto?> AuthorizeAsync(AuthDto dto)
    {
        try
        {
            var request = GetRequestMessage(dto);
            var response = await _httpClient.SendAsync(request);

            response.EnsureSuccessStatusCode();
            var body = await response.Content.ReadAsStringAsync();
            var result = JsonSerializer.Deserialize<AuthResultDto>(body);

            return result;
        }
        catch (Exception ex)
        {
            throw new PopAuthException(ex);
        }
    }

    public async Task<AuthResultDto?> RefreshTokenAsync(RefreshTokenDto dto)
    {
        var request = new HttpRequestMessage
        {
            Method = HttpMethod.Post,
            RequestUri = new Uri(_configuracoesAuth.TokenUrl),
            Headers =
            {
                { "Origin", _configuracoesAuth.Origin }
            },
            Content = new FormUrlEncodedContent(new Dictionary<string, string>
            {
                { "client_id", _configuracoesAuth.ClientId },
                { "grant_type", _configuracoesAuth.GrantTypeRefreshToken },
                { "refresh_token", dto.RefreshToken }
            }),
        };

        var response = await _httpClient.SendAsync(request);

        if (!response.IsSuccessStatusCode)
        {
            throw new Exception(
                $"[{response.StatusCode}] \nReasonPhrase: {response.ReasonPhrase}\nContent:{response.Content}.");
        }

        response.EnsureSuccessStatusCode();
        var body = await response.Content.ReadAsStringAsync();
        return JsonSerializer.Deserialize<AuthResultDto>(body);
    }

    private HttpRequestMessage GetRequestMessage(AuthDto dto)
    {
        HttpRequestMessage message = new()
        {
            Method = HttpMethod.Post,
            RequestUri = new Uri(_configuracoesAuth.TokenUrl),
            Headers =
            {
                { "Origin", _configuracoesAuth.Origin }
            }
        };

        Dictionary<string, string> content = new()
        {
            { "client_id", _configuracoesAuth.ClientId },
            { "grant_type", _configuracoesAuth.GrantTypeAccessToken },
            { "username", dto.Username! },
            { "password", dto.Password! }
        };

        message.Content = new FormUrlEncodedContent(content);

        return message;
    }
}