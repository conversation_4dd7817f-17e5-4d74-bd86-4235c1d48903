using ONS.SINapse.Entities.Entities;
using ONS.SINapse.Shared.DTO.Firebase;

namespace ONS.SINapse.Repository.IRepository;

public interface IRascunhoSolicitacaoRepository
{
    Task AddUpdateAsync(RascunhoSolicitacao rascunho, CancellationToken cancellationToken);
    Task DeleteAsync(Guid rascunho, CancellationToken cancellationToken);
    Task InvalidarAsync(Guid rascunho, CancellationToken cancellationToken);
    Task<IEnumerable<RascunhoSolicitacaoDto>> ObterRascunhoPorNomeAsync(string nome, CancellationToken cancellationToken);
}