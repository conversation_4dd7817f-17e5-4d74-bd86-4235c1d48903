using System.Linq.Expressions;
using ONS.SINapse.Entities.Entities.Base;

namespace ONS.SINapse.Repository.IRepository.Base;

public interface IMongoRepository<TDocument> where TDocument : Entidade
{
    Task<ICollection<TDocument>> GetAsync(CancellationToken cancellationToken);
    Task<ICollection<TDocument>> GetAsync(IEnumerable<string> ids, CancellationToken cancellationToken);
    Task<ICollection<TDocument>> GetAsync(Expression<Func<TDocument, bool>> predicate, CancellationToken cancellationToken);

    Task<ICollection<TDocument>> GetAsync(Expression<Func<TDocument, bool>> predicate, 
        IDictionary<string, bool> sortBy, CancellationToken cancellationToken);

    Task<TDocument?> GetOneAsync(string id, CancellationToken cancellationToken);
    Task<TDocument?> GetOneAsync(Expression<Func<TDocument, bool>> predicate, CancellationToken cancellationToken);
    Task<bool> AnyAsync(string id, CancellationToken cancellationToken);
    Task<bool> AnyAsync(Expression<Func<TDocument, bool>> predicate, CancellationToken cancellationToken);
    Task<long> CountAsync(IEnumerable<string> ids, CancellationToken cancellationToken);
    Task<long> CountAsync(Expression<Func<TDocument, bool>> predicate, CancellationToken cancellationToken);
    Task<long> CountAsync(CancellationToken cancellationToken);

    Task<(ICollection<TDocument>, long)> GetPagedAsync(Expression<Func<TDocument, bool>> predicate,
        IDictionary<string, bool> sortBy, int page, int limit, CancellationToken cancellationToken);

    Task<TDocument> AddAsync(TDocument document, CancellationToken cancellationToken);
    Task UpdateAsync(string id, TDocument document, CancellationToken cancellationToken);
    Task DeleteAsync(TDocument document, CancellationToken cancellationToken);
    Task DeleteAsync(Expression<Func<TDocument, bool>> predicate, CancellationToken cancellationToken);
    Task<bool> DeleteAsync(string id, CancellationToken cancellationToken);
    Task BulkAddAsync(IEnumerable<TDocument> documents, CancellationToken cancellationToken);
    Task BulkUpdateAsync(IEnumerable<TDocument> documents, CancellationToken cancellationToken);
    Task BulkCreateAsync(IEnumerable<TDocument> documents, CancellationToken cancellationToken);
    Task DeleteManyAsync(IEnumerable<string> ids, CancellationToken cancellationToken);
    Task DeleteManyAsync(Expression<Func<TDocument, bool>> predicate, CancellationToken cancellationToken);
    Task DeleteManyAsync(IEnumerable<TDocument> documents, CancellationToken cancellationToken);
    Task DeleteAllAsync(CancellationToken cancellationToken);
    Task DropAsync(CancellationToken cancellationToken);
}