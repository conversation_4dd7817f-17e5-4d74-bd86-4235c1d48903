using ONS.SINapse.Entities.Entities;
using ONS.SINapse.Repository.IRepository.Base;
using System.Linq.Expressions;

namespace ONS.SINapse.Repository.IRepository;

public interface IRespostaDaChamadaRepository: IMongoRepository<RespostaDaChamada>
{
    Task<RespostaDaChamada?> BuscarPorCentro(string codigo, CancellationToken cancellationToken);    
    Task<ICollection<RespostaDaChamada>> FindOrCreate(ObjetoDeManobra[] centroAgentes, CancellationToken cancellationToken);
    Task<IEnumerable<RespostaDaChamada>> BuscarPendentesDeNotificacaoAsync(CancellationToken cancellationToken);
    Task<string[]> ObterCodigoAgentesOnlineAsync(Expression<Func<RespostaDaChamada, bool>> predicate, CancellationToken cancellationToken);
}