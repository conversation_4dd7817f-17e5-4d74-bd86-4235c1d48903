using Firebase.Database;

namespace ONS.SINapse.Repository.IRepository.Firebase
{
    public interface ICollectionFirebaseRepository
    {
        Task PatchAsync(string collectionName, string jsonData, CancellationToken cancellationToken);
        Task PutAsync(string collectionName, string jsonData, CancellationToken cancellationToken);
        Task DeleteAsync(string collectionName, CancellationToken cancellationToken);
        Task<IReadOnlyCollection<FirebaseObject<object>>> GetAsync(string collectionName, CancellationToken cancellationToken);
    }
}
