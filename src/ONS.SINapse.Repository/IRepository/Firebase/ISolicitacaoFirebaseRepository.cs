using ONS.SINapse.Shared.Converters;

namespace ONS.SINapse.Repository.IRepository.Firebase;

public interface ISolicitacaoFirebaseRepository
{
    Task AddUpdateAsync(List<Entities.Entities.Solicitacao> solicitacoes, CancellationToken cancellationToken);
    Task PatchFlattenAsync<T>(IEnumerable<T> solicitacao, CancellationToken cancellationToken) where T : FlattenableJson;
    Task PatchFlattenAsync<T>(T solicitacao, CancellationToken cancellationToken) where T : FlattenableJson;
    Task RemoverSolicitacoesAsync(IReadOnlyCollection<string> ids, CancellationToken cancellationToken);
}
