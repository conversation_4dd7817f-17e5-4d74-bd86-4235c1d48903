using ONS.SINapse.Entities.Entities;
using ONS.SINapse.Shared.Converters;

namespace ONS.SINapse.Repository.IRepository.Firebase;

public interface ISolicitacaoFirebaseRepository
{
    Task AddUpdateAsync(List<Entities.Entities.Solicitacao> solicitacoes, CancellationToken cancellationToken);
    Task AddUpdateAsync(Dictionary<string, object?> solicitacoes, CancellationToken cancellationToken);
    Task PatchFlattenAsync<T>(IEnumerable<T> solicitacao, CancellationToken cancellationToken) where T : FlattenableJson;
    Task PatchFlattenAsync<T>(T solicitacao, CancellationToken cancellationToken) where T : FlattenableJson;
    Task RemoveAsync(IReadOnlyCollection<string> ids, CancellationToken cancellationToken);
    Task<Solicitacao?> GetByIdAsync(string id, CancellationToken cancellationToken);
    Task<List<Solicitacao>> GetByIdAsync(string[] ids, CancellationToken cancellationToken);

    Task<IReadOnlyCollection<Solicitacao>> GetSolicitacoesParaFinalizarAsync(DateTime updatedBefore, CancellationToken cancellationToken);
}
