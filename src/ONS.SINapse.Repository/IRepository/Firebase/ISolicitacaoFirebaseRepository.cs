using ONS.SINapse.Entities.Entities;
using ONS.SINapse.Integracao.Shared.Enums;

namespace ONS.SINapse.Repository.IRepository.Firebase;

public interface ISolicitacaoFirebaseRepository
{
    Task AddUpdateAsync(List<Entities.Entities.Solicitacao> solicitacoes, CancellationToken cancellationToken);
    Task AddUpdateAsync(Dictionary<string, object?> solicitacoes, CancellationToken cancellationToken);
    Task RemoveAsync(IReadOnlyCollection<string> ids, CancellationToken cancellationToken);
    Task<StatusDeSolicitacao> StatusAsync(string id, CancellationToken cancellationToken);
    Task<Solicitacao?> GetByIdAsync(string id, CancellationToken cancellationToken);
    Task<List<Solicitacao>> GetByIdAsync(string[] ids, CancellationToken cancellationToken);
    Task<List<Solicitacao>> GetAlldAsync(CancellationToken cancellationToken);

    Task<IReadOnlyCollection<Solicitacao>> GetSolicitacoesParaFinalizarAsync(DateTime updatedBefore, CancellationToken cancellationToken);
}