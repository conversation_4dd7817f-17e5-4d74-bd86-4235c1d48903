using ONS.SINapse.Entities.Entities;
using ONS.SINapse.Repository.IRepository.Base;
using System.Linq.Expressions;
using MongoDB.Driver;

namespace ONS.SINapse.Repository.IRepository;

public interface ISolicitacaoRepository : IExtraivelRepository<Solicitacao>
{
    Task EnviadoAoAnaliticoAsync(List<string> ids, CancellationToken cancellationToken);
    Task<IAsyncCursor<Solicitacao>> FindAsync(Expression<Func<Solicitacao, bool>> predicate,
        int? limit = null,
        FindOptions? options = null, CancellationToken cancellationToken = default);
}