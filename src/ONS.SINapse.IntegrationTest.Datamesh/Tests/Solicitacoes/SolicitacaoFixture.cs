using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Factories;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using ONS.SINapse.Solicitacao.Dtos.Integracao;


namespace ONS.SINapse.IntegrationTest.Datamesh.Tests.Solicitacoes;

public class SolicitacaoFixture
{
    private readonly UsuarioDto _usuarioSolicitante;
    private readonly UsuarioDto _usuario;

    private readonly ObjetoDeManobraDto _origem;
    private readonly ObjetoDeManobraDto _destino;
    private readonly ObjetoDeManobraDto _local;
    private readonly ObjetoDeManobraDto _encaminharPara;

    public SolicitacaoFixture()
    {
        _usuarioSolicitante = new UsuarioDto(
            sid: Guid.NewGuid().ToString(),
            nome: "Usuário Solicitante Teste API",
            login: "usuario.solicitante.teste"
        );

        _usuario = new UsuarioDto(
            sid: Guid.NewGuid().ToString(),
            nome: "Usuário Teste API",
            login: "usuario.teste"
        );

        _origem = new ObjetoDeManobraDto("NE", "COSR-NE");
        _destino = new ObjetoDeManobraDto("GSU", "ENGIE");
        _local = new ObjetoDeManobraDto("PNGJPEG", "Imagem");
        _encaminharPara = new ObjetoDeManobraDto("GSU", "ENGIE");
    }

    public IEnumerable<StatusDeSolicitacaoIntegracaoRecebimentoDto> GerarStatusSolicitacoesCanceladas(IEnumerable<Configurations.Resources.Solicitacao.Dtos.Integracao.StatusDeSolicitacaoIntegracaoEnvioDto> solicitacoes)
        => solicitacoes.Select(x => GerarStatusSolicitacao(x, StatusDeSolicitacao.Cancelada, _origem, _usuarioSolicitante));

    public IEnumerable<StatusDeSolicitacaoIntegracaoRecebimentoDto> GerarStatusSolicitacoesConfirmadas(IEnumerable<Configurations.Resources.Solicitacao.Dtos.Integracao.StatusDeSolicitacaoIntegracaoEnvioDto> solicitacoes)
        => solicitacoes.Select(x => GerarStatusSolicitacao(x, StatusDeSolicitacao.Confirmada, _destino, _usuario));

    public IEnumerable<StatusDeSolicitacaoIntegracaoRecebimentoDto> GerarStatusSolicitacoesFinalizadas(IEnumerable<Configurations.Resources.Solicitacao.Dtos.Integracao.StatusDeSolicitacaoIntegracaoEnvioDto> solicitacoes)
        => solicitacoes.Select(x => GerarStatusSolicitacao(x, StatusDeSolicitacao.Finalizada, _destino, _usuario));

    private StatusDeSolicitacaoIntegracaoRecebimentoDto GerarStatusSolicitacao(Configurations.Resources.Solicitacao.Dtos.Integracao.StatusDeSolicitacaoIntegracaoEnvioDto solicitacao, StatusDeSolicitacao status, ObjetoDeManobraDto centro, UsuarioDto usuario)
    => new (solicitacao.Id, status, string.Empty, centro, usuario, solicitacao.SistemaDeOrigem);

    public IEnumerable<CadastroSolicitacaoDto> GerarCadastroSolicitacoes(int quantidade = 5)
    {
        int index = 0;
        var list = new List<CadastroSolicitacaoDto>();

        while (index != quantidade)
        {
            index++;
            list.Add(GerarCadastroSolicitacao());
        }

        return list;
    }

    private CadastroSolicitacaoDto GerarCadastroSolicitacao()
    {


        var id = IdSolicitacaoFactory.GerarNovoId(_origem.Codigo, _destino.Codigo);

        var solicitacao = new CadastroSolicitacaoDto
        {
            Origem = _origem,
            Destino = _destino,
            Local = _local,
            EncaminharPara = _encaminharPara,
            InformacaoAdicional = "Teste solicitações via testes de integração",
            Mensagem = "Teste de solicitações via testes de integração",
            SistemaDeOrigem = "SINAPSE",
            Id = id,
            Motivo = "",
            Usuario = _usuarioSolicitante
        };

        solicitacao.AdicionarTags(new[] { "Geração" });        
        
        return solicitacao;
    }
}