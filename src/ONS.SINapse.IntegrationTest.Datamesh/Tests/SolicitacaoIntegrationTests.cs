using MassTransit;
using ONS.SINapse.Business.Imp.Business.EventHandlers.Events;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.IntegrationTest.Datamesh.Configurations.Resources.Shared.Settings;
using ONS.SINapse.IntegrationTest.Datamesh.Configurations.Tests.Collections;
using ONS.SINapse.IntegrationTest.Datamesh.Configurations.Tests.Fixtures;
using ONS.SINapse.IntegrationTest.Datamesh.Configurations.Tests.Helpers;
using ONS.SINapse.IntegrationTest.Datamesh.Configurations.Tests.Interfaces;
using ONS.SINapse.IntegrationTest.Datamesh.Tests.Solicitacoes;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using ONS.SINapse.Solicitacao.Dtos;

namespace ONS.SINapse.IntegrationTest.Datamesh.Tests;

[Collection(nameof(IntegrationTestsCollection))]
public class SolicitacaoIntegracaoTests(IntegrationTestsFixture fixture) : IClassFixture<IntegrationTestsFixture>
{
    private readonly TestSettings? _settings = fixture.TestSettings;
    private readonly AthenaHelper _athena = fixture.Athena;
    private readonly IBus _bus = fixture.Bus;
    private readonly IApiSolicitacaoIntegracao _client = fixture.GetClient<IApiSolicitacaoIntegracao>();
    private readonly SolicitacaoFixture _solicitacaoFixture = new();

    private async Task AplicaDelayAntesDeFinalizarSolicitacoes()
    {
        if (_settings != null) await Task.Delay(_settings.DelayAntesDeFinalizarSolicitacoes ?? 0);
    }

    private async Task AplicaDelayAposFinalizarSolicitacoes()
    {
        if (_settings != null) await Task.Delay(_settings.DelayAposFinalizarSolicitacoes ?? 0);
    }

    private async Task AplicaDelayAntesDePublicarEventoDeExtracaoDeDados()
    {
        if (_settings != null) await Task.Delay(_settings.DelayAntesDePublicarEventoDeExtracaoDeDados ?? 0);
    }

    private async Task AplicaDelayDeIntervaloDeTempoEntreConsultasNoAthena()
    {
        if (_settings != null) await Task.Delay(_settings.DelayDeIntervaloDeTempoEntreConsultasNoAthena ?? 0);
    }

    private async Task<string[]> PrepararDadosDeSolicitacoes()
    {
        var cadastrarSolicitacaocommand = new CadastrarSolicitacaoEmLoteCommand(_solicitacaoFixture.GerarCadastroSolicitacoes().ToList());

        var respostaCadastroSolicitacoes = await _client.CriarEmLote(cadastrarSolicitacaocommand, CancellationToken.None);

        var alterarStatusCancelarECriarCommand = new StatusSolicitacaoDto(_solicitacaoFixture.GerarStatusSolicitacoesCanceladas(respostaCadastroSolicitacoes.Solicitacoes.Take(3))
            .Concat(_solicitacaoFixture.GerarStatusSolicitacoesConfirmadas(respostaCadastroSolicitacoes.Solicitacoes.Skip(3).Take(2))).ToList());

        var respostaCancelarEConfirmarSolicitacoes = await _client.Patch(alterarStatusCancelarECriarCommand, CancellationToken.None);

        var alterarStatusFinalizarSolicitacoesCommand = new StatusSolicitacaoDto(_solicitacaoFixture
            .GerarStatusSolicitacoesFinalizadas(respostaCancelarEConfirmarSolicitacoes.Solicitacoes
                .Where(x => x.Status == StatusDeSolicitacao.Confirmada)).ToList());

        await AplicaDelayAntesDeFinalizarSolicitacoes();

        var respostaFinalizarSolicitacoes = await _client.Patch(alterarStatusFinalizarSolicitacoesCommand, CancellationToken.None);

        await AplicaDelayAposFinalizarSolicitacoes();

        return alterarStatusCancelarECriarCommand.Solicitacoes
            .Where(x => x.Status == StatusDeSolicitacao.Cancelada)
            .Select(x => x.Id)
            .Concat(respostaFinalizarSolicitacoes.Solicitacoes.Select(x => x.Id)).ToArray()!;
    }

    private async Task RealizarExtracaoDeDados()
    {
        await AplicaDelayAntesDePublicarEventoDeExtracaoDeDados();

        await _bus.Publish(new ExtracaoDeSolicitacaoEvent
        {
            Message = "Extração de dados utilizada pelo projeto de testes de integração"
        });
    }

    private async Task<string[]> RealizarConsultaAthena(string[] ids)
    {
        var totalRetentativas = _settings!.RetentativasParaConsultaNoAthena ?? 5;
        var retentativas = 0;
        List<string> resultado;

        var formattedIds = string.Join(", ", ids.Select(id => $"'{id}'"));
        var query = $" SELECT id_solicitacao FROM tb_solicitacao WHERE id_solicitacao IN ({formattedIds}) ";

        do
        {
            resultado = await _athena.ExecutarQueryAsync(query);

            if (resultado.Count == 0)
            {
                retentativas++;
                await AplicaDelayDeIntervaloDeTempoEntreConsultasNoAthena();
            }

        } while (resultado.Count < ids.Length || (resultado.Count == 0 && retentativas < totalRetentativas));
        
        return resultado.Select(x => x.Split('|')[0].Trim()).ToArray();
    }

    [Fact(DisplayName = "[Realizar Extração de Dados] - Deve realizar a extração de dados com scuesso")]
    [Trait("Integration Tests", "Realizar Extração de Dados")]
    public async Task DeveRealizarExtracaoDadosComSucesso()
    {
        // Arrange

        var idsSolicitacoes = await PrepararDadosDeSolicitacoes();

        await RealizarExtracaoDeDados();

        // Act

        var resultado = await RealizarConsultaAthena(idsSolicitacoes);

        // Assert
        Assert.NotNull(resultado);
        Assert.NotEmpty(resultado);
        Assert.Equal(idsSolicitacoes.OrderBy(x => x), resultado.OrderBy(x => x));
    }
}