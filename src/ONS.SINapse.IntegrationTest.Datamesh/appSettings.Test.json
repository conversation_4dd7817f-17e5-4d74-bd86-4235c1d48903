{"ONSEnv": "ons.sinapse.dev", "AWS": {"Region": "us-east-1", "AccessKey": "", "SecretKey": ""}, "AWSAthena": {"Region": "us-east-1", "AccessKey": "", "SecretKey": "", "OutputS3": "s3://ons-dl-01-dev-athena/", "Database": "sinapse"}, "TestApiOptions": {"CustomHeaders": {"Accept": "application/json", "Content-Type": "application/json", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/108.0.0.0 Safari/537.36"}, "ServiceUri": "https://api.devsinapseintegracao.ons.org.br/", "Authentication": {"ServiceUri": "https://poptst.ons.org.br/ons.pop.federation/oauth2/token", "ApplicationCacheExpiration": 300, "ApplicationName": "SINAPSE", "ApplicationOrigin": "http://local.ons.org.br", "Username": "<EMAIL>", "Password": ""}}, "TestOptions": {"DelayAntesDeFinalizarSolicitacoes": 5000, "DelayAposFinalizarSolicitacoes": 30000, "DelayAntesDePublicarEventoDeExtracaoDeDados": 5000, "DelayDeIntervaloDeTempoEntreConsultasNoAthena": 60000, "RetentativasParaConsultaNoAthena": 5}, "CacheSettings": {"ExpiracaoDeFonteEmMinutos": 60, "ExpiracaoDeMotivoEmMinutos": 60, "ExpiracaoDeOperacaoEmMinutos": 60}, "ONS": {"Authorization": {"Issuer": "https://poptst.ons.org.br/ons.pop.federation/", "Audience": "SINAPSE", "UseRsa": "true", "RsaModulus": "wSxNKSkhfB1XR+fD/KZxK5nLEEHHBNrbSpiNw9FtcJHkvOiBXWI+G43Y1rvp6zp2/sjEqiXbQlFuMf2d/hM9ScIrdtrykf3m0OpDvhACFgwvvdiIaWOqIZ9oJCS9uzgEq7OGwH4gQklIOUbVrjZftXc0qFRR3XwkwGPGaNLsVpzSMeJHDJJReJe4MtztgsBS//AzkSdbhBpcAwQYOdmeQZTxL76miZqIHqAWAGQZgh/y3kHdfayhMb/hSgay933ITWyV2V7TUMBByYm6MOLuSRWTuloVIiwA/Nap5tgrQFdCuc34GCNgQIocn8qbcICI21AebnbyEyo96sONodToEQ==", "RsaPublicExponent": "AQAB"}, "PopAuth": {"Origin": "http://local.ons.org.br", "TokenURL": "https://poptst.ons.org.br/ons.pop.federation/oauth2/token", "clientId": "SINAPSE", "username": "ons\\_servicedsagerapurac", "password": "", "grantTypeRefreshToken": "refresh_token", "grantTypeAccessToken": "password"}}}