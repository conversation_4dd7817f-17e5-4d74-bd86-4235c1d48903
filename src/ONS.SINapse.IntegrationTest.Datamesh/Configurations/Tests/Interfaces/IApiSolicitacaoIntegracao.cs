using ONS.SINapse.IntegrationTest.Datamesh.Configurations.Resources.Solicitacao.Dtos.Integracao;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using ONS.SINapse.Solicitacao.Dtos;
using Refit;

namespace ONS.SINapse.IntegrationTest.Datamesh.Configurations.Tests.Interfaces;

public interface IApiSolicitacaoIntegracao
{
    [Post("/api/integracao/client/v2/solicitacao")]
    Task< StatusDeSolicitacaoIntegracaoEnvioEmLoteDto> CriarEmLote([Body] CadastrarSolicitacaoEmLoteCommand command, CancellationToken cancellationToken);
    [Patch("/api/integracao/client/v2/solicitacao")]
    Task<StatusDeSolicitacaoIntegracaoEnvioEmLoteDto> Patch([Body] StatusSolicitacaoDto request, CancellationToken cancellationToken);
}
