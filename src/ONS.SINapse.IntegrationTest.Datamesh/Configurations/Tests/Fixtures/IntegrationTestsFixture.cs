using MassTransit;
using Microsoft.Extensions.DependencyInjection;
using ONS.SINapse.IntegrationTest.Datamesh.Configurations.Resources.Shared.Settings;
using ONS.SINapse.IntegrationTest.Datamesh.Configurations.Tests.Factories;
using ONS.SINapse.IntegrationTest.Datamesh.Configurations.Tests.Helpers;

namespace ONS.SINapse.IntegrationTest.Datamesh.Configurations.Tests.Fixtures;

public class IntegrationTestsFixture
{
    public readonly TestSettings? TestSettings;
    public readonly AthenaHelper Athena;
    private readonly SinapseAppFactory _factory;
    public readonly IBus Bus;

    public IntegrationTestsFixture()
    {
        _factory = new SinapseAppFactory().CreateInstance();
        Bus = (_factory.ServiceProvider ?? throw new InvalidOperationException()).GetRequiredService<IBus>();
        Athena = _factory.ServiceProvider.GetRequiredService<AthenaHelper>();
        TestSettings = _factory.GetTestSettings();
    }

    public T GetClient<T>() where T : notnull
    {
        return _factory.GetRefitClient<T>();
    }
}