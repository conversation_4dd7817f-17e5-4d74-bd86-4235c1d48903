using Amazon.Athena.Model;
using Amazon.Athena;

namespace ONS.SINapse.IntegrationTest.Datamesh.Configurations.Tests.Helpers;

public class AthenaHelper(IAmazonAthena athenaClient, string database, string outputLocation)
{
    public async Task<List<string>> ExecutarQueryAsync(string query)
    {
        var request = new StartQueryExecutionRequest
        {
            QueryString = query,
            QueryExecutionContext = new QueryExecutionContext { Database = database },
            ResultConfiguration = new ResultConfiguration { OutputLocation = outputLocation }
        };

        var startResponse = await athenaClient.StartQueryExecutionAsync(request);
        var executionId = startResponse.QueryExecutionId;

        string state;
        do
        {
            await Task.Delay(3000);
            var statusResponse = await athenaClient.GetQueryExecutionAsync(new GetQueryExecutionRequest
            {
                QueryExecutionId = executionId
            });

            state = statusResponse.QueryExecution.Status.State;
        }
        while (state == QueryExecutionState.RUNNING || state == QueryExecutionState.QUEUED);

        if (state != QueryExecutionState.SUCCEEDED)
        {
            throw new Exception($"Query Athena falhou com estado: {state}");
        }

        var resultResponse = await athenaClient.GetQueryResultsAsync(new GetQueryResultsRequest
        {
            QueryExecutionId = executionId
        });

        var results = new List<string>();

        foreach (var row in resultResponse.ResultSet.Rows.Skip(1)) 
        {
            var line = string.Join(" | ", row.Data.Select(d => d.VarCharValue));
            results.Add(line);
        }

        return results;
    }
}