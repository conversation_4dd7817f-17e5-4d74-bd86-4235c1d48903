using System.Net;
using Amazon;
using Amazon.Athena;
using MassTransit;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using ONS.SINapse.IntegrationTest.Datamesh.Configurations.Resources.Extensions;
using ONS.SINapse.IntegrationTest.Datamesh.Configurations.Resources.Shared.Settings;
using ONS.SINapse.IntegrationTest.Datamesh.Configurations.Tests.Helpers;
using ONS.SINapse.IntegrationTest.Datamesh.Configurations.Tests.Interfaces;
using ONS.SINapse.Shared.Extensions;
using Refit;
using AwsSettings = ONS.SINapse.IntegrationTest.Datamesh.Configurations.Resources.Shared.Settings.AwsSettings;
using Uri = System.Uri;

namespace ONS.SINapse.IntegrationTest.Datamesh.Configurations.Tests.Factories;

public class SinapseAppFactory
{
    private ServiceCollection? _services;

    public IServiceProvider? ServiceProvider;

    private TestSettings? _testSettings;

    private void ConfigurarRefit(IConfiguration configuration)
    {
        var apiOptions = configuration.GetSection("TestApiOptions").Get<TestApiOptions>();
        ArgumentNullException.ThrowIfNull(apiOptions);

        _services?.AddRefitClient<IApiSolicitacaoIntegracao>(settings: new RefitSettings(
                new NewtonsoftJsonContentSerializer(new JsonSerializerSettings()
                {
                    ContractResolver = new CamelCasePropertyNamesContractResolver()
                }))
            {
                CollectionFormat = CollectionFormat.Multi
            }).ConfigureHttpClient(options => { options.BaseAddress = new Uri(apiOptions.ServiceUri); })
            .ConfigurePrimaryHttpMessageHandler(() => new HttpClientHandler
            {
                ServerCertificateCustomValidationCallback = (_, cert, _, _) => cert != null,
                UseDefaultCredentials = true,
                AllowAutoRedirect = true,
                PreAuthenticate = true,
                Credentials = CredentialCache.DefaultNetworkCredentials,
                DefaultProxyCredentials = CredentialCache.DefaultNetworkCredentials
            })
            .AddAuthenticationRequestHandler<TestApiOptions>(configuration);
    }

    private void ConfigurarAws(IConfiguration configuration)
    {
        var awsSettings = configuration.GetSection("AWS").Get<AwsSettings>();
        ArgumentNullException.ThrowIfNull(awsSettings);

        _services.AddMassTransit<IBus>(x =>
        {
            x.UsingAmazonSqs((_, cfg) =>
            {
                cfg.Host(awsSettings.Region, h =>
                {
                    h.AccessKey(awsSettings.AccessKey);
                    h.SecretKey(awsSettings.SecretKey);
                });
            });
        });

        var awsAthenaSettings = configuration.GetSection("AWSAthena").Get<AwsAthenaSettings>();
        ArgumentNullException.ThrowIfNull(awsAthenaSettings);

        _services?.AddSingleton<IAmazonAthena>(_ => new AmazonAthenaClient(awsAthenaSettings.AccessKey, awsAthenaSettings.SecretKey, new AmazonAthenaConfig
        {
            RegionEndpoint = RegionEndpoint.GetBySystemName(awsAthenaSettings.Region)
        }));

        _services?.AddSingleton<AthenaHelper>(provider =>
        {
            var athena = provider.GetRequiredService<IAmazonAthena>();
            return new AthenaHelper(athena, awsAthenaSettings.Database, awsAthenaSettings.OutputS3);
        });
    }

    private void ConfigurarTestSettings(IConfiguration configuration)
    {
        var testSettings = configuration.GetSection("TestOptions").Get<TestSettings>();
        ArgumentNullException.ThrowIfNull(testSettings);

        _testSettings = testSettings;
    }

    public SinapseAppFactory CreateInstance()
    {
        var configuration = new ConfigurationBuilder()
            .AddJsonFile("appsettings.Test.json")
            .Build();

        _services = new ServiceCollection();

        DependencyInjector.RegisterCaching(_services, configuration);

        ConfigurarRefit(configuration);
        ConfigurarAws(configuration);
        ConfigurarTestSettings(configuration);

        ServiceProvider = _services.BuildServiceProvider();

        return this;
    }

    public TestSettings? GetTestSettings()
        => _testSettings;

    public T GetRefitClient<T>() where T : notnull
    {
        return (ServiceProvider ?? throw new InvalidOperationException()).GetRequiredService<T>();
    }
}