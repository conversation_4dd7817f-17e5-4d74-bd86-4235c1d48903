using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace ONS.SINapse.IntegrationTest.Datamesh.Configurations.Resources.Solicitacao.Converters;

public class EnumToObjectJsonConverter<TEnum> : JsonConverter where TEnum : struct, Enum
{
    public override bool CanConvert(Type objectType)
    {
        // Suporta tipo direto ou Nullable<Enum>
        var type = Nullable.GetUnderlyingType(objectType) ?? objectType;
        return type.IsEnum && type == typeof(TEnum);
    }

    public override object? ReadJson(JsonReader reader, Type objectType, object? existingValue, JsonSerializer serializer)
    {
        if (reader.TokenType == JsonToken.Null)
        {
            if (Nullable.GetUnderlyingType(objectType) != null)
                return null;

            throw new JsonSerializationException($"Não é possível converter null para {objectType.Name}");
        }

        var obj = JObject.Load(reader);

        if (!obj.TryGetValue("codigo", out var codigoToken))
            throw new JsonSerializationException($"Propriedade 'codigo' não encontrada no JSON do enum {typeof(TEnum).Name}");

        var underlyingType = Enum.GetUnderlyingType(typeof(TEnum));
        var rawValue = Convert.ChangeType(codigoToken.Value<object>(), underlyingType);

        if (!Enum.IsDefined(typeof(TEnum), rawValue))
            throw new JsonSerializationException($"Valor inválido '{rawValue}' para enum {typeof(TEnum).Name}");

        return (TEnum)Enum.ToObject(typeof(TEnum), rawValue);
    }


    public override void WriteJson(JsonWriter writer, object? value, JsonSerializer serializer)
    {
        if (value == null)
        {
            writer.WriteNull();
            return;
        }

        var enumValue = (TEnum)value;

        writer.WriteStartObject();
        writer.WritePropertyName("codigo");
        writer.WriteValue(Convert.ToInt32(enumValue));
        writer.WritePropertyName("descricao");
        writer.WriteValue(GetDescription(enumValue));
        writer.WriteEndObject();
    }

    private static string GetDescription(TEnum value)
    {
        var field = typeof(TEnum).GetField(value.ToString());
        var attribute = field?.GetCustomAttributes(typeof(System.ComponentModel.DescriptionAttribute), false)
                              .FirstOrDefault() as System.ComponentModel.DescriptionAttribute;

        return attribute?.Description ?? value.ToString();
    }
}