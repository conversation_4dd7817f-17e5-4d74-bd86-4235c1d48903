using Newtonsoft.Json;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.IntegrationTest.Datamesh.Configurations.Resources.Solicitacao.Converters;
using ONS.SINapse.Shared.DTO;

namespace ONS.SINapse.IntegrationTest.Datamesh.Configurations.Resources.Solicitacao.Dtos.Integracao;

public class StatusDeSolicitacaoIntegracaoEnvioEmLoteDto
{
    public StatusDeSolicitacaoIntegracaoEnvioEmLoteDto()
    {
        _solicitacoes = [];
    }

    public StatusDeSolicitacaoIntegracaoEnvioEmLoteDto(List<StatusDeSolicitacaoIntegracaoEnvioDto> solicitacoes)
    {
        _solicitacoes = solicitacoes;
    }

    private readonly List<StatusDeSolicitacaoIntegracaoEnvioDto> _solicitacoes;
    public IReadOnlyCollection<StatusDeSolicitacaoIntegracaoEnvioDto> Solicitacoes => _solicitacoes;
}

public record StatusDeSolicitacaoIntegracaoEnvioDto
{
    public string Id { get; init; }

    [JsonConverter(typeof(EnumToObjectJsonConverter<StatusDeSolicitacao>))]
    public StatusDeSolicitacao Status { get; set; }

    public UsuarioDto Usuario { get; init; }

    public DateTime Data { get; init; }

    public string? Justificativa { get; init; }

    public string SistemaDeOrigem { get; init; }

    public string[] Erros { get; private set; } = [];

    public void AdicionarErros(string[] erros)
    {
        Status = StatusDeSolicitacao.Erro;
        Erros = Erros.Concat(erros).ToArray();
    }

    public bool EstaValido() => Erros.Length == 0;

    public string ErrosFormatados() => string.Join(", ", Erros);
}