using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using ONS.SINapse.IntegrationTest.Datamesh.Configurations.Resources.Shared.Settings;
using ONS.SINapse.Shared.Services.Caching;
using StackExchange.Redis;

namespace ONS.SINapse.IntegrationTest.Datamesh.Configurations.Resources.Extensions;

public static class DependencyInjector
{
    public static void RegisterCaching(IServiceCollection? services, IConfiguration configuration)
    {
        services.AddDistributedMemoryCache();
        services.AddSingleton<ICacheService, DistributedCacheService>();

        var cacheSettings = configuration.GetSection(nameof(CacheSettings)).Get<CacheSettings>();
        ArgumentNullException.ThrowIfNull(cacheSettings);

        if (cacheSettings.UseRedis)
        {
            services.AddStackExchangeRedisCache(options =>

            {
                var configurationOptions = ConfigurationOptions.Parse($"{cacheSettings.Host}:{cacheSettings.Port},password={cacheSettings.Password},abortConnect=false");

                configurationOptions.ReconnectRetryPolicy =
                    new
                        ExponentialRetry(5000);

                configurationOptions.ConnectRetry = 3;
                configurationOptions.AbortOnConnectFail = false;
                options.InstanceName = cacheSettings.InstanceName;
                options.ConfigurationOptions = configurationOptions;
            });
        }
    }
}