<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>

    <IsPackable>false</IsPackable>
    <IsTestProject>true</IsTestProject>
  </PropertyGroup>

  <ItemGroup>
    <Content Include="appSettings.Test.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </Content>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="AWSSDK.Athena" Version="3.7.403.75" />
    <PackageReference Include="AWSSDK.SQS" Version="3.7.400.133" />
    <PackageReference Include="coverlet.collector" Version="6.0.0" />
    <PackageReference Include="MassTransit" Version="8.3.2" />
    <PackageReference Include="MassTransit.AmazonSQS" Version="8.3.2" />
    <PackageReference Include="MassTransit.Newtonsoft" Version="8.3.2" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Testing" Version="8.0.15" />
    <PackageReference Include="Microsoft.Extensions.Caching.StackExchangeRedis" Version="8.0.15" />
    <PackageReference Include="Microsoft.IdentityModel.Tokens" Version="8.2.1" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.8.0" />
    <PackageReference Include="Refit" Version="8.0.0" />
    <PackageReference Include="Refit.HttpClientFactory" Version="8.0.0" />
    <PackageReference Include="Refit.Newtonsoft.Json" Version="8.0.0" />
    <PackageReference Include="StackExchange.Redis" Version="2.7.27" />
    <PackageReference Include="xunit" Version="2.9.3" />
    <PackageReference Include="xunit.runner.visualstudio" Version="3.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\ONS.SINapse.Solicitacao\ONS.SINapse.Solicitacao.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Using Include="Xunit" />
  </ItemGroup>

</Project>
