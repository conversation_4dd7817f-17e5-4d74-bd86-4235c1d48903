<Project Sdk="Microsoft.NET.Sdk">

  <!-- Propriedades específicas do projeto de teste de integração -->
  <PropertyGroup>
    <IsPackable>false</IsPackable>
    <IsTestProject>true</IsTestProject>
  </PropertyGroup>

  <ItemGroup>
    <Content Include="appSettings.Test.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </Content>
  </ItemGroup>

  <!-- Pacotes específicos para testes de integração -->
  <ItemGroup>
    <PackageReference Include="AWSSDK.Athena" />
    <PackageReference Include="AWSSDK.SQS" />
    <PackageReference Include="coverlet.collector" />
    <PackageReference Include="MassTransit" />
    <PackageReference Include="MassTransit.AmazonSQS" />
    <PackageReference Include="MassTransit.Newtonsoft" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Testing" />
    <PackageReference Include="Microsoft.Extensions.Caching.StackExchangeRedis" />
    <PackageReference Include="Microsoft.IdentityModel.Tokens" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" />
    <PackageReference Include="Refit" />
    <PackageReference Include="Refit.HttpClientFactory" />
    <PackageReference Include="Refit.Newtonsoft.Json" />
    <PackageReference Include="StackExchange.Redis" />
    <PackageReference Include="xunit" />
    <PackageReference Include="xunit.runner.visualstudio" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\ONS.SINapse.Solicitacao\ONS.SINapse.Solicitacao.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Using Include="Xunit" />
  </ItemGroup>

</Project>
