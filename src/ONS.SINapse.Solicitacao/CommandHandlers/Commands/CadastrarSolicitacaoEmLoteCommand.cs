using FluentValidation.Results;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Shared.Messages;
using ONS.SINapse.Solicitacao.Interfaces;

namespace ONS.SINapse.Solicitacao.CommandHandlers.Commands;

public class CadastrarSolicitacaoEmLoteCommand : Command<ResultadoCadastroSolicitacaoEmLoteDto>, IWithUserDefinitionCommand
{
    public CadastrarSolicitacaoEmLoteCommand(List<CadastroSolicitacaoDto> solicitacoes)
    {
        CadastrosDeSolicitacao = solicitacoes;
    }
    
    public List<CadastroSolicitacaoDto> CadastrosDeSolicitacao { get; set; }
    
    public UsuarioDto? Usuario { get; private set; }

    public void DefinirLote(Guid lote)
    {
        CadastrosDeSolicitacao.ForEach(x => x.DefinirLote(lote));
    }
    
    public void DefinirUsuario(UsuarioDto usuarioDto)
    {
        Usuario = usuarioDto;
        CadastrosDeSolicitacao.ForEach(x =>
        {
            if(!x.PossuiUsuario())
                x.DefinirUsuario(usuarioDto);
        });
    }

    public bool PossuiUsuario() => Usuario is not null;

}

public class ResultadoCadastroSolicitacaoEmLoteDto : CommandResult
{
    public ResultadoCadastroSolicitacaoEmLoteDto()
    {
        Resultado = new List<ResultadoCadastroSolicitacaoDto>();
    }
    
    public List<ResultadoCadastroSolicitacaoDto> Resultado { get; set; }


    public void AdicionarResultado(ResultadoCadastroSolicitacaoDto resultado) => Resultado.Add(resultado);
    
    public bool EstaValido() => Resultado.All(x => x.ValidationResult.IsValid);

    public void AdicionarErro(ValidationResult validationResult, string? codigoExterno)
    {
        var novoResultado = new ResultadoCadastroSolicitacaoDto(codigoExterno);
        novoResultado.AdicionarErro(validationResult);
        ValidationResult.AdicionarErro(validationResult);
        Resultado.Add(novoResultado);
    }
}

public class CadastroSolicitacaoDto
{
    public CadastroSolicitacaoDto()
    {
        Lote = Guid.NewGuid();
        Tags = [];
        IntegracaoViaApi = true;
    }

    public CadastroSolicitacaoDto(
        ObjetoDeManobraDto origem, 
        ObjetoDeManobraDto destino, 
        string? informacaoAdicional, 
        string? mensagem,
        UsuarioDto usuario,
        string solicitacaoDeOrigem,
        List<string>? tags = null
        )
    {
        Origem = origem;
        Destino = destino;
        InformacaoAdicional = informacaoAdicional;
        Mensagem = mensagem;
        Tags = tags ?? [];
        Usuario = usuario;
        SolicitacaoDeOrigem = solicitacaoDeOrigem;
        IntegracaoViaApi = true;
    }


    #region Informacoes de Local da Operacao

    
    public ObjetoDeManobraDto? Origem { get; init; }
    public ObjetoDeManobraDto? Destino { get; init; }
    public ObjetoDeManobraDto? Local { get; init; }
    public ObjetoDeManobraDto? EncaminharPara { get; init; }
    

    #endregion
    
    public string? InformacaoAdicional { get; init; }
    public string? Mensagem { get; init; }
    
    public bool Externo => 
        !string.IsNullOrEmpty(SistemaDeOrigem) && 
        SistemaDeOrigem != Entities.Entities.Solicitacao.SistemaDeOrigemInterno && 
        !string.IsNullOrEmpty(CodigoExterno);
    
    public string? SistemaDeOrigem { get; set; }
    public string? CodigoExterno { get; set; }
    public bool IntegracaoViaApi { get; private set; }
    public string? Motivo { get; init; }
    public Guid Lote { get; private set; }
    public List<string> Tags { get; init; }

    /// <summary>
    /// Usado quando encaminhar uma solicitação
    /// </summary>
    public string? SolicitacaoDeOrigem { get; init; }

    private bool _estaValido = true;

    public bool EstaValido() => _estaValido;
    public void InvalidarSolicitacao() => _estaValido = false;
    
    public void DefinirComoSolicitacaoExterna(string sistema, string codigo, bool viaApi = false)
    {
        SistemaDeOrigem = sistema;
        CodigoExterno = codigo;
        IntegracaoViaApi = viaApi;
    }
    
    public void DefinirLote(Guid id) => Lote = id;

    #region Informacoes de usuário de cadastro da solicitacao
    
    /// <summary>
    /// Campo preenchido pelo middleware de cadastro de solicitação 'UserDefinitionMiddleware'
    /// </summary>
    public UsuarioDto? Usuario { get; private set; }
    
    public void DefinirUsuario(UsuarioDto usuario)
    {
        Usuario = usuario;
    }

    public bool PossuiUsuario() => Usuario is not null;

    #endregion
}

public class ResultadoCadastroSolicitacaoDto : CommandResult
{
    public ResultadoCadastroSolicitacaoDto(string? codigoExterno)
    {
        Id = codigoExterno;
        CodigoDaSolicitacaoOrigem = codigoExterno;
        Status = StatusDeSolicitacao.Pendente;
        DataEHoraDeCriacao = DateTime.Now;
        Mensagem = string.Empty;
    }
    
    public string? Id { get; set; }
    public string? Codigo => Id;
    public string? CodigoDaSolicitacaoOrigem { get; set; }
    public DateTime? DataEHoraDeCriacao { get; set; }
    public string? Mensagem { get; set; }
    public StatusDeSolicitacao Status { get; set; }
    public IReadOnlyCollection<string> Erros => ValidationResult.Errors.Select(e => e.ErrorMessage).ToList().AsReadOnly();

    public void AdicionarErro(string erro)
    {
        Status = StatusDeSolicitacao.Erro;
        ValidationResult.AdicionarErro(erro);
    }

    public void AdicionarErro(ValidationResult validationResult)
    {
        if(validationResult.IsValid) return;

        Status = StatusDeSolicitacao.Erro;
        ValidationResult.AdicionarErro(validationResult);
    }

    private void Map(Entities.Entities.Solicitacao solicitacao)
    {
        Id = solicitacao.Id;
        CodigoDaSolicitacaoOrigem = solicitacao.CodigoExterno ?? solicitacao.SolicitacaoDeOrigemId;
        DataEHoraDeCriacao = solicitacao.CreatedAt;
        Mensagem = solicitacao.Mensagem;
    }

    public static ResultadoCadastroSolicitacaoDto Mapear(Entities.Entities.Solicitacao solicitacao)
    {
        var result = new ResultadoCadastroSolicitacaoDto(solicitacao.CodigoExterno);
        result.Map(solicitacao);
        return result;
    }
}
