using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Messages;
using ONS.SINapse.Solicitacao.Dtos.Integracao;

namespace ONS.SINapse.Solicitacao.CommandHandlers.Commands;

public class CadastrarSolicitacaoEmLoteCommand : Command<StatusDeSolicitacaoIntegracaoEnvioEmLoteDto>
{
    public CadastrarSolicitacaoEmLoteCommand(List<CadastroSolicitacaoDto> solicitacoes)
    {
        Solicitacoes = solicitacoes;
    }
    
    public List<CadastroSolicitacaoDto> Solicitacoes { get; set; }

    public void DefinirLote(Guid lote)
    {
        Solicitacoes.ForEach(x => x.DefinirLote(lote));
    }
}

public class CadastroSolicitacaoDto
{
    public CadastroSolicitacaoDto()
    {
        Lote = Guid.NewGuid();
        Tags = [];
        IntegracaoViaApi = true;
    }

    public CadastroSolicitacaoDto(
        string id,
        ObjetoDeManobraDto origem, 
        ObjetoDeManobraDto destino, 
        string? informacaoAdicional, 
        string? mensagem,
        UsuarioDto usuario,
        string solicitacaoDeOrigem
        )
    {
        Id = id;
        Origem = origem;
        Destino = destino;
        InformacaoAdicional = informacaoAdicional;
        Mensagem = mensagem;
        Tags = [];
        Usuario = usuario;
        SolicitacaoDeOrigem = solicitacaoDeOrigem;
        IntegracaoViaApi = true;
    }


    #region Informacoes de Local da Operacao

    
    public ObjetoDeManobraDto? Origem { get; init; }
    public ObjetoDeManobraDto? Destino { get; init; }
    public ObjetoDeManobraDto? Local { get; init; }
    public ObjetoDeManobraDto? EncaminharPara { get; init; }
    

    #endregion
    public string? Id { get; set; }
    public string? InformacaoAdicional { get; init; }
    public string? Mensagem { get; init; }
    
    public bool Externo => 
        !string.IsNullOrEmpty(SistemaDeOrigem) && 
        SistemaDeOrigem != Entities.Entities.Solicitacao.SistemaDeOrigemInterno && 
        !string.IsNullOrEmpty(Id);
    
    public string? SistemaDeOrigem { get; set; }
    public UsuarioDto? Usuario { get; set; }
    public bool IntegracaoViaApi { get; private set; }
    public string? Motivo { get; init; }
    public Guid Lote { get; private set; }
    public List<string> Tags { get; init; }
    
    /// <summary>
    /// Usado quando encaminhar uma solicitação
    /// </summary>
    public string? SolicitacaoDeOrigem { get; init; }

    private bool _estaValido = true;

    public bool EstaValido() => _estaValido;
    public void InvalidarSolicitacao() => _estaValido = false;
    
    public void DefinirComoSolicitacaoExterna(string sistema, string codigo, bool viaApi = false)
    {
        SistemaDeOrigem = sistema;
        Id = codigo;
        IntegracaoViaApi = viaApi;
    }

    public void AdicionarTags(string[] tags)
    {
        Tags.AddRange(tags);
    }
    
    public void DefinirLote(Guid id) => Lote = id;
}
