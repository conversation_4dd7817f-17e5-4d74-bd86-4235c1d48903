using FluentValidation.Results;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Shared.Messages;
using ONS.SINapse.Solicitacao.Interfaces;

namespace ONS.SINapse.Solicitacao.CommandHandlers.Commands;

public class CancelarEmLoteCommand : Command<ResultadoCancelamentoEmLoteDto>, IWithCentroDeOperacaoDefinitionCommand, IViaApiIntegracaoDefinitionCommand
{
    public CancelarEmLoteCommand(string[] solicitacoes)
    {
        _solicitacoes = solicitacoes.ToList();
        ViaKafka = false;
    }

    private readonly List<string> _solicitacoes;
    public IReadOnlyCollection<string> Solicitacoes => _solicitacoes.AsReadOnly();

    public IReadOnlyCollection<string>? Centros { get; private set; }
    public void DefinirCentros(IReadOnlyCollection<string> centros) => Centros = centros;

    public bool PossuiCentro() => Centros is not null && Centros.Count != 0;

    public bool ViaKafka { get; private set; }

    public void IntegrarViaKafka() => ViaKafka = true;
    
    public UsuarioDto? Usuario { get; private set; }
    public void DefinirUsuario(UsuarioDto usuarioDto) => Usuario = usuarioDto;
    public bool PossuiUsuario() => Usuario is not null;
    
    public bool ViaApiIntegracao { get; private set; }
    public void DefinirViaApiIntegracao() => ViaApiIntegracao = true;
}


public class ResultadoCancelamentoEmLoteDto : CommandResult
{
    public ResultadoCancelamentoEmLoteDto()
    {
        _resultado = new List<ResultadoCancelamentoDto>();
    }

    private readonly List<ResultadoCancelamentoDto> _resultado;
    public IReadOnlyCollection<ResultadoCancelamentoDto> Resultado => _resultado.AsReadOnly();

    public override void AdicionarValidation(ValidationResult validationResult)
    {
        base.AdicionarValidation(validationResult);
        _resultado.ForEach(x => x.AdicionarValidation(validationResult));
    }

    public void AdicionarResultado(ResultadoCancelamentoDto resultadoCancelamento)
        => _resultado.Add(resultadoCancelamento);

    public void AdicionarResultadoComErro(string codigoSolicitacao, string mensagem)
    {
        var resultado = new ResultadoCancelamentoDto(codigoSolicitacao, StatusDeSolicitacao.Erro);
        resultado.AdicionarErro(mensagem);
        AdicionarResultado(resultado);
    }

}

public class ResultadoCancelamentoDto
{
    public ResultadoCancelamentoDto(string codigoDeSolicitacao, StatusDeSolicitacao status)
    {
        CodigoDeSolicitacao = codigoDeSolicitacao;
        (StatusId, Status) = ((short)status, status.GetDescription());
        DataDeAlteracao = DateTime.Now;
        _erros = new List<string>();
    }
    
    public string CodigoDeSolicitacao { get; private set; }
    public short StatusId { get; private set; }
    public string Status { get; private set; }
    public DateTime DataDeAlteracao { get; private set; }
    
    private readonly List<string> _erros;
    
    public IReadOnlyCollection<string> Erros => _erros.AsReadOnly();

    private void DefinirStatus(StatusDeSolicitacao status)
    {
        StatusId = (short)status;
        Status = status.GetDescription();
        DataDeAlteracao = DateTime.Now;
    }

    public void AdicionarValidation(ValidationResult validationResult)
    {
        if(validationResult.IsValid) return;
        _erros.AddRange(validationResult.Errors.Select(x => x.ToString()));
    }

    public void AdicionarErro(string mensagem)
    {
        _erros.Add(mensagem);
        DefinirStatus(StatusDeSolicitacao.Erro);
    }
}
