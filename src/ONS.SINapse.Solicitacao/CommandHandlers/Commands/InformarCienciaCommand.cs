using FluentValidation.Results;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Shared.Messages;
using ONS.SINapse.Solicitacao.Interfaces;

namespace ONS.SINapse.Solicitacao.CommandHandlers.Commands;

public class InformarCienciaCommand : Command<CienciaInformadaResultDto>, IWithCentroDeOperacaoDefinitionCommand, IViaApiIntegracaoDefinitionCommand
{
    public InformarCienciaCommand(string codigo)
    {
        Codigo = codigo;
    }
    
    public string Codigo { get; private set; }
    
    
    public IReadOnlyCollection<string>? Centros { get; private set; }
    public void DefinirCentros(IReadOnlyCollection<string> centros) => Centros = centros;

    public bool PossuiCentro() => Centros is not null && Centros.Count != 0;
    
    public UsuarioDto? Usuario { get; private set; }
    public void DefinirUsuario(UsuarioDto usuarioDto) => Usuario = usuarioDto;
    public bool PossuiUsuario() => Usuario is not null;

    public bool ViaApiIntegracao { get; private set; }
    public void DefinirViaApiIntegracao() => ViaApiIntegracao = true;
}


public class CienciaInformadaResultDto : CommandResult
{
    public CienciaInformadaResultDto()
    {
        SolicitacaoId = string.Empty;
        const StatusDeSolicitacao status = StatusDeSolicitacao.Erro;
        StatusId = (short)status;
        Status = status.GetDescription();
        DataDeAlteracao = new DateTime();
        _erros = [];
    }

    public CienciaInformadaResultDto(string id)
    {
        SolicitacaoId = id;
        const StatusDeSolicitacao status = StatusDeSolicitacao.CienciaInformada;
        StatusId = (short)status;
        Status = status.GetDescription();
        DataDeAlteracao = DateTime.Now;
        _erros = [];
    }

    public CienciaInformadaResultDto(List<ValidationFailure> erros, string? id)
    {
        SolicitacaoId = id ?? string.Empty;
        const StatusDeSolicitacao status = StatusDeSolicitacao.Erro;
        StatusId = (short)status;
        Status = status.GetDescription();
        DataDeAlteracao = DateTime.Now;
        _erros = [..erros.Select(x => x.ErrorMessage)];
    }

    public string CodigoDeSolicitacao => SolicitacaoId;
    public string SolicitacaoId { get; private set; }
    public short StatusId { get; private set; }
    public string Status { get; private set; }
    public DateTime DataDeAlteracao { get; private set; }
    private readonly List<string> _erros;
    public IReadOnlyCollection<string> Erros => _erros.AsReadOnly();
    public bool EstaValida() => Erros.Count == 0;
}