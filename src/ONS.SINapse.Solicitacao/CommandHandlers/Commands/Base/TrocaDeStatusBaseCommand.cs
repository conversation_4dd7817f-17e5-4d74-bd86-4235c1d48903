using ONS.SINapse.Shared.Messages;
using ONS.SINapse.Solicitacao.Dtos.Integracao;

namespace ONS.SINapse.Solicitacao.CommandHandlers.Commands.Base;

public abstract class TrocaDeStatusBaseCommand : Command<StatusDeSolicitacaoIntegracaoEnvioEmLoteDto>
{
    protected TrocaDeStatusBaseCommand(List<StatusDeSolicitacaoIntegracaoRecebimentoDto> solicitacoes)
    {
        _solicitacoes = solicitacoes;
    }
    
    private readonly List<StatusDeSolicitacaoIntegracaoRecebimentoDto> _solicitacoes;
    public IReadOnlyCollection<StatusDeSolicitacaoIntegracaoRecebimentoDto> Solicitacoes => _solicitacoes.AsReadOnly();
}