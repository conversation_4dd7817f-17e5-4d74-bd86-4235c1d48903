using System.Collections.ObjectModel;
using ONS.SINapse.Shared.Messages;

namespace ONS.SINapse.Solicitacao.CommandHandlers.Commands.Base;

public abstract class EnviarSolicitacaoFirebaseBaseCommand : Command, ISolicitacaoFirebaseCommand
{
    private readonly List<Dictionary<string, object?>> _solicitacoes;
    
    protected EnviarSolicitacaoFirebaseBaseCommand(List<Dictionary<string, object?>> solicitacoes)
    {
        _solicitacoes = solicitacoes;
    }
    public ReadOnlyCollection<Dictionary<string, object?>> Solicitacoes => _solicitacoes.AsReadOnly();
}