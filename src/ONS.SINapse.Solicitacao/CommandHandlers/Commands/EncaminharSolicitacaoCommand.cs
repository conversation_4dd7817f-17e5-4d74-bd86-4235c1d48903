using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Messages;
using ONS.SINapse.Solicitacao.Interfaces;

namespace ONS.SINapse.Solicitacao.CommandHandlers.Commands;

public class EncaminharSolicitacaoCommand : Command, IWithUserDefinitionCommand
{
    public string[]? SolicitacoesId { get; set; }
    public string? InformacaoAdicional { get; set; }
    public UsuarioDto? Usuario { get; private set; }

    public void DefinirUsuario(UsuarioDto usuarioDto)
        => Usuario = usuarioDto;

    public bool PossuiUsuario()
        => Usuario is not null;
}