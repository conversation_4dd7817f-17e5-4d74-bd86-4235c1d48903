using FluentValidation.Results;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Shared.Messages;
using ONS.SINapse.Solicitacao.Interfaces;

namespace ONS.SINapse.Solicitacao.CommandHandlers.Commands;

public class ConfirmarCommand : Command<SolicitacaoConfirmadaResultDto>, IWithUserDefinitionCommand
{
    public ConfirmarCommand(string codigo)
    {
        Codigo = codigo;
    }
    
    public string Codigo { get; private set; }
    
    public UsuarioDto? Usuario { get; private set; }
    public void DefinirUsuario(UsuarioDto usuarioDto) => Usuario = usuarioDto;
    public bool PossuiUsuario() => Usuario is not null;
}

public class SolicitacaoConfirmadaResultDto : CommandResult
{
    public SolicitacaoConfirmadaResultDto()
    {
        SolicitacaoId = string.Empty;
        StatusId = (short)StatusDeSolicitacao.Erro;
        Status = StatusDeSolicitacao.Erro.GetDescription();
        DataDeAlteracao = DateTime.Now;
    }

    public SolicitacaoConfirmadaResultDto(string id)
    {
        SolicitacaoId = id;
        const StatusDeSolicitacao status = StatusDeSolicitacao.Confirmada;
        StatusId = (short)status;
        Status = status.GetDescription();
        DataDeAlteracao = DateTime.Now;
    }

    public string CodigoDeSolicitacao => SolicitacaoId;
    public string SolicitacaoId { get; private set; }
    public short StatusId { get; private set; }
    public string Status { get; private set; }
    public DateTime DataDeAlteracao { get; private set; }
    public IReadOnlyCollection<string> Erros => ValidationResult.Errors.Select(e => e.ErrorMessage).ToList().AsReadOnly();

    public void AdicionarErro(string mensagem)
    {
        ValidationResult.AdicionarErro(mensagem);
        DefinirStatus(StatusDeSolicitacao.Erro);
    }

    public override void AdicionarValidation(ValidationResult validationResult)
    {
        if(validationResult.IsValid) return;
        DefinirStatus(StatusDeSolicitacao.Erro);
        base.AdicionarValidation(validationResult);
    }

    private void DefinirStatus(StatusDeSolicitacao status)
    {
        Status = status.GetDescription();
        StatusId = (short)status;
    }
}
