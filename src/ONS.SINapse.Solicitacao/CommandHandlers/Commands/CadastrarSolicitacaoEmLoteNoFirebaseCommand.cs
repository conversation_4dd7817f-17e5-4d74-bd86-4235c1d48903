using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Messages;

namespace ONS.SINapse.Solicitacao.CommandHandlers.Commands;

public class CadastrarSolicitacaoEmLoteNoFirebaseCommand : Command
{
    public CadastrarSolicitacaoEmLoteNoFirebaseCommand(List<Entities.Entities.Solicitacao> solicitacao)
    {
        Solicitacao = solicitacao;
    }
    
    public List<Entities.Entities.Solicitacao> Solicitacao { get; private set; }
}


public class ConfirmarLeituraSolicitacaoNoFirebaseCommand : Command
{
    public ConfirmarLeituraSolicitacaoNoFirebaseCommand(ChatDeSolicitacaoFirebaseDto solicitacao)
    {
        Solicitacao = solicitacao;
    }

    public ChatDeSolicitacaoFirebaseDto Solicitacao { get; private set; }
}

public class ConfirmarEntregasSolicitacaoNoFirebaseCommand : Command
{
    public ConfirmarEntregasSolicitacaoNoFirebaseCommand(List<ChatDeSolicitacaoFirebaseDto> solicitacao)
    {
        Solicitacao = solicitacao;
    }

    public List<ChatDeSolicitacaoFirebaseDto> Solicitacao { get; private set; }
}

public class EnviarMensagemChatSolicitacaoNoFirebaseCommand : Command
{
    public EnviarMensagemChatSolicitacaoNoFirebaseCommand(ChatDeSolicitacaoFirebaseDto solicitacao)
    {
        Solicitacao = solicitacao;
    }

    public ChatDeSolicitacaoFirebaseDto Solicitacao { get; private set; }
}