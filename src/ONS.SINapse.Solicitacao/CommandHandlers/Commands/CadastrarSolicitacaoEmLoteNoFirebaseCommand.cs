using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Messages;

namespace ONS.SINapse.Solicitacao.CommandHandlers.Commands;

public class CadastrarSolicitacaoEmLoteNoFirebaseCommand : Command
{
    public CadastrarSolicitacaoEmLoteNoFirebaseCommand(List<SolicitacaoFirebaseDto> solicitacao)
    {
        Solicitacao = solicitacao;
    }
    
    public List<SolicitacaoFirebaseDto> Solicitacao { get; private set; }
}

public class ConfirmarSolicitacaoNoFirebaseCommand : Command
{
    public ConfirmarSolicitacaoNoFirebaseCommand(StatusDeSolicitacaoFirebaseDto solicitacao)
    {
        Solicitacao = solicitacao;
    }

    public StatusDeSolicitacaoFirebaseDto Solicitacao { get; private set; }
}

public class ImpedirSolicitacaoNoFirebaseCommand : Command
{
    public ImpedirSolicitacaoNoFirebaseCommand(ImpedirSolicitacaoFirebaseDto solicitacao)
    {
        Solicitacao = solicitacao;
    }

    public ImpedirSolicitacaoFirebaseDto Solicitacao { get; private set; }
}

public class InformarCienciaSolicitacaoNoFirebaseCommand : Command
{
    public InformarCienciaSolicitacaoNoFirebaseCommand(StatusDeSolicitacaoFirebaseDto solicitacao)
    {
        Solicitacao = solicitacao;
    }

    public StatusDeSolicitacaoFirebaseDto Solicitacao { get; private set; }
}

public class ConfirmarLeituraSolicitacaoNoFirebaseCommand : Command
{
    public ConfirmarLeituraSolicitacaoNoFirebaseCommand(ChatDeSolicitacaoFirebaseDto solicitacao)
    {
        Solicitacao = solicitacao;
    }

    public ChatDeSolicitacaoFirebaseDto Solicitacao { get; private set; }
}

public class ConfirmarEntregasSolicitacaoNoFirebaseCommand : Command
{
    public ConfirmarEntregasSolicitacaoNoFirebaseCommand(List<ChatDeSolicitacaoFirebaseDto> solicitacao)
    {
        Solicitacao = solicitacao;
    }

    public List<ChatDeSolicitacaoFirebaseDto> Solicitacao { get; private set; }
}

public class EnviarMensagemChatSolicitacaoNoFirebaseCommand : Command
{
    public EnviarMensagemChatSolicitacaoNoFirebaseCommand(ChatDeSolicitacaoFirebaseDto solicitacao)
    {
        Solicitacao = solicitacao;
    }

    public ChatDeSolicitacaoFirebaseDto Solicitacao { get; private set; }
}