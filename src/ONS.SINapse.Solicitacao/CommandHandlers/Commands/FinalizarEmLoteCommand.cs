using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Shared.Messages;
using ONS.SINapse.Solicitacao.Interfaces;

namespace ONS.SINapse.Solicitacao.CommandHandlers.Commands;

public class FinalizarEmLoteCommand : Command<SolicitacaoFinalizadaEmLoteResultDto>, IWithUserDefinitionCommand, IViaApiIntegracaoDefinitionCommand
{
    public FinalizarEmLoteCommand(string[] solicitacoes)
    {
        Solicitacoes = solicitacoes;
        FinalizadoAutomaticamente = false;
    }
    
    public string[] Solicitacoes { get; }
    public bool FinalizadoAutomaticamente { get; private set; }
    public UsuarioDto? Usuario { get; private set; }
    public void DefinirUsuario(UsuarioDto usuarioDto) => Usuario = usuarioDto;
    public bool PossuiUsuario() => Usuario is not null;
   
    public bool ViaApiIntegracao { get; private set; }
    public void DefinirViaApiIntegracao() => ViaApiIntegracao = true;

    public void FinalizadoProcessoAutomatico() => FinalizadoAutomaticamente = true;
}

public class SolicitacaoFinalizadaEmLoteResultDto : CommandResult
{
    public SolicitacaoFinalizadaEmLoteResultDto()
    {
        _solicitacoes = [];
    }

    public SolicitacaoFinalizadaEmLoteResultDto(List<SolicitacaoFinalizadaResultDto> solicitacoes)
    {
        _solicitacoes = solicitacoes;
    }
    
    private readonly List<SolicitacaoFinalizadaResultDto> _solicitacoes;
    public IReadOnlyCollection<SolicitacaoFinalizadaResultDto> Solicitacoes => _solicitacoes.AsReadOnly();


    public void AdicionarResultado(SolicitacaoFinalizadaResultDto solicitacao)
    {
        _solicitacoes.Add(solicitacao);
    }
    
    public void AdicionarResultadoComErro(string id, string mensagem)
    {
        var resultado = new SolicitacaoFinalizadaResultDto(id);
        resultado.AdicionarErro(mensagem);
        AdicionarResultado(resultado);
    }
}

public class SolicitacaoFinalizadaResultDto 
{
    public SolicitacaoFinalizadaResultDto()
    {
        SolicitacaoId = string.Empty;
        StatusId = (short)StatusDeSolicitacao.Erro;
        Status = StatusDeSolicitacao.Erro.GetDescription();
        DataDeAlteracao = new DateTime();
        _erros = [];
    }

    public SolicitacaoFinalizadaResultDto(string id)
    {
        SolicitacaoId = id;
        const StatusDeSolicitacao status = StatusDeSolicitacao.Finalizada;
        StatusId = (short)status;
        Status = status.GetDescription();
        DataDeAlteracao = DateTime.Now;
        _erros = [];
    }

    public string CodigoDeSolicitacao => SolicitacaoId;
    public string SolicitacaoId { get; private set; }
    public short StatusId { get; private set; }
    public string Status { get; private set; }
    public DateTime DataDeAlteracao { get; private set; }
    private readonly List<string> _erros;
    public IReadOnlyCollection<string> Erros => _erros.AsReadOnly();

    public void AdicionarErro(string mensagem)
    {
        _erros.Add(mensagem);
        DefinirStatus(StatusDeSolicitacao.Erro);
        DataDeAlteracao = DateTime.Now;
    }

    private void DefinirStatus(StatusDeSolicitacao status)
    {
        Status = status.GetDescription();
        StatusId = (short)status;
    }
}