using ONS.SINapse.Solicitacao.CommandHandlers.Commands.TrocaDeStatusCommand;
using ONS.SINapse.Solicitacao.Dtos.Integracao;

namespace ONS.SINapse.Solicitacao.CommandHandlers.Commands;

public class FinalizarEmLoteCommand(List<StatusDeSolicitacaoIntegracaoRecebimentoDto> solicitacoes)
    : TrocaDeStatusBaseCommand(solicitacoes)
{
    public bool ProcessoAutomatico { get; private set; }
    
    public void FinalizadoProcessoAutomatico() => ProcessoAutomatico = true;
}
