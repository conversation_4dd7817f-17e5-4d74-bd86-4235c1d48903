using ONS.SINapse.Shared.Messages;
namespace ONS.SINapse.Solicitacao.CommandHandlers.Commands;

public class ConfirmarEntregaCommand : Command<ResultadoConfirmacaoDeEntregaDto>
{
    public ConfirmarEntregaCommand(List<string> solicitacoesId)
    {
        SolicitacoesId = solicitacoesId;
    }
    public List<string> SolicitacoesId { get; set; }    
}

public class ResultadoConfirmacaoDeEntregaDto : CommandResult
{
    public ResultadoConfirmacaoDeEntregaDto()
    {
        Entregues = 0;
        SolicitacoesEntregues = [];
    }

    public int Entregues { get; private set; }
    public List<string> SolicitacoesEntregues { get; private set; }

    public void AdicionarResultado(int entregues, List<string> solicitacoesEntregues)
    {
        Entregues = entregues;
        SolicitacoesEntregues = new List<string>(solicitacoesEntregues);
    }
}