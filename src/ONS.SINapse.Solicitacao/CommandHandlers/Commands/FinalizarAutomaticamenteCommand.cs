using ONS.SINapse.Shared.Messages;

namespace ONS.SINapse.Solicitacao.CommandHandlers.Commands;

public class FinalizarAutomaticamenteCommand : Command
{
    public FinalizarAutomaticamenteCommand()
    {
        IgnorarValidacaoTempo = false;
    }
    public FinalizarAutomaticamenteCommand(bool ignorarValidacaoTempo = false)
    {
        IgnorarValidacaoTempo = ignorarValidacaoTempo;
    }
    public bool IgnorarValidacaoTempo { get; private set; }
}