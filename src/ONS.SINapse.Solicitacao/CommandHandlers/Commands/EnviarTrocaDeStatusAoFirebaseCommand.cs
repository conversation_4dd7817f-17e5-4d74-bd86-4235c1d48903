using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Messages;

namespace ONS.SINapse.Solicitacao.CommandHandlers.Commands;


public class EnviarTrocaDeStatusAoFirebaseCommand : Command
{
    public EnviarTrocaDeStatusAoFirebaseCommand(List<StatusDeSolicitacaoFirebaseDto> solicitacoes)
    {
        _solicitacoes = solicitacoes;
    }

    private readonly List<StatusDeSolicitacaoFirebaseDto> _solicitacoes;
    public IReadOnlyCollection<StatusDeSolicitacaoFirebaseDto> Solicitacoes => _solicitacoes.AsReadOnly();
}