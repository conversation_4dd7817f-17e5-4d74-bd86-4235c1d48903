using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Shared.CustomAttributes;
using ONS.SINapse.Shared.Messages;
using ONS.SINapse.Solicitacao.Dtos;

namespace ONS.SINapse.Solicitacao.CommandHandlers.Commands;

public class ExportarConsultaHistoricoAnaliticaCommand : Command<ArquivoResultDto>
{
    public string? Origem { get; set; }
    public string? Destino { get; set; }
    public string? Mensagem { get; set; }
    public StatusDeSolicitacao? Status { get; set; }
    public DateTime? PeriodoInicial { get; set; }
    public DateTime? PeriodoFinal { get; set; }
}

[BsonCollection("c_solicitacao")]
public class ArquivoConsultaHistoricoAnaliticaDto : IBsonCollection
{
    public ArquivoConsultaHistoricoAnaliticaDto(string codigo, string origem, string destino, string mensagem, string usuarioDeCriacao)
    {
        Codigo = codigo;
        Origem = origem;
        Destino = destino;
        Mensagem = mensagem;
        UsuarioDeCriacao = usuarioDeCriacao;
    }

    public string Codigo { get; set; }
    public string Origem { get; set; }
    public string Destino { get; set; }
    public string? Motivo { get; set; }
    public string Mensagem { get; set; }
    public string? InformacaoAdicional { get; set; }
    public  DateTime DataDeCriacao { get; set; }
    public string UsuarioDeCriacao { get; set; }
    public short CodigoStatus { get; set; }
    public  DateTime? DataDeAlteracao { get; set; }
    public string? UsuarioDeAlteracao { get; set; }
    public string? MotivoImpedimento { get; set; }
    public string? UsuarioEntrega { get; set; }
    public  DateTime? DataDeEntrega { get; set; }
    public string? UsuarioLeitura { get; set; }
    public  DateTime? DataDeLeitura { get; set; }
}