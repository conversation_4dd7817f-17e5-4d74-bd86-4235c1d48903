using MongoDB.Bson.Serialization.Attributes;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Shared.CustomAttributes;
using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Shared.Messages;
using ONS.SINapse.Solicitacao.Dtos;

namespace ONS.SINapse.Solicitacao.CommandHandlers.Commands;

public class ExportarConsultaHistoricoCommand : Command<ArquivoResultDto>
{
    public string? Origem { get; set; }
    public string? Destino { get; set; }
    public string? Mensagem { get; set; }
    public StatusDeSolicitacao? Status { get; set; }
    public DateTime? PeriodoInicial { get; set; }
    public DateTime? PeriodoFinal { get; set; }
}

[BsonCollection("c_solicitacao")]
public class ArquivoConsultaHistoricoDto : IBsonCollection
{
    public ArquivoConsultaHistoricoDto(string codigo, string origem, string destino, string mensagem, string usuarioDeCadastro)
    {
        Codigo = codigo;
        Origem = origem;
        Destino = destino;
        Mensagem = mensagem;
        UsuarioDeCadastro = usuarioDeCadastro;
    }

    public string Codigo { get; set; }
    public string Origem { get; set; }
    public string Destino { get; set; }
    public string Mensagem { get; set; }
    
    [BsonElement("DataDeCadastro")]
    private  DateTime Cadastro { get; set; }

    [BsonIgnore] 
    public string DataDeCadastro => Cadastro.ToFormattedSouthAmericaStandardTime("dd/MM/yyyy");
    [BsonIgnore] 
    public string HoraDeCadastro => Cadastro.ToFormattedSouthAmericaStandardTime("HH:mm:ss");
    public string UsuarioDeCadastro { get; set; }
    public short CodigoStatus { get; set; }
    public string? InformacaoAdicional { get; set; }
    
    [BsonElement("DataDeAlteracao")]
    public  DateTime? Alteracao { get; set; }
    
    [BsonIgnore] 
    public string DataDeAlteracao => Alteracao?.ToFormattedSouthAmericaStandardTime("dd/MM/yyyy") ?? string.Empty;
    
    [BsonIgnore] 
    public string HoraDeAlteracao => Alteracao?.ToFormattedSouthAmericaStandardTime("HH:mm:ss") ?? string.Empty;
    
    public string? UsuarioDeAlteracao { get; set; }
}