using FluentValidation.Results;
using ONS.SINapse.Repository.IRepository.InMemory;
using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Shared.Identity;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Shared.Messages;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using ONS.SINapse.Solicitacao.Factories;

namespace ONS.SINapse.Solicitacao.CommandHandlers.Handlers;

public class ChatDeSolicitacaoCommandHandler : CommandHandler<ChatDeSolicitacaoCommand>
{
    private readonly ISolicitacaoMemoryRepository _solicitacaoMemoryRepository;
    private readonly IMediatorHandler _mediatorHandler;
    private readonly IUserContext _userContext;
    private const string SolicitacaoNaoEncontrada = "Solicitação não encontrada.";
    private const string MensagemNaoEncontrada = "Mensagem não informada.";

    public ChatDeSolicitacaoCommandHandler(
        ISolicitacaoMemoryRepository solicitacaoRepository,
        IMediatorHandler mediatorHandler,
        IUserContext userContext)
    {
        _solicitacaoMemoryRepository = solicitacaoRepository;
        _mediatorHandler = mediatorHandler;
        _userContext = userContext;
    }

    protected override async Task<ValidationResult> Handle(ChatDeSolicitacaoCommand request, CancellationToken cancellationToken)
    {
        var solicitacao = (_solicitacaoMemoryRepository.GetList(x => x.Id == request.SolicitacaoId)).FirstOrDefault();
        if (solicitacao is null)
        {
            ValidationResult.AdicionarErro(SolicitacaoNaoEncontrada);
            return ValidationResult;
        }

        if (string.IsNullOrWhiteSpace(request.Mensagem))
        {
            ValidationResult.AdicionarErro(MensagemNaoEncontrada);
            return ValidationResult;
        }
        
        var usuario = _userContext.Usuario();
        solicitacao.AdicionarMensagemNoChat(request.Mensagem, usuario, _userContext.Perfil);

        var firebaseCommand = SolicitacaoFirebaseCommandFactory.ObterCommand<CriarMensagemNoChatDeSolicitacaoFirebaseCommand>([solicitacao]);

        var validationResult = await _mediatorHandler.EnviarComandoAsync(firebaseCommand, cancellationToken);

        if (!validationResult.IsValid)
        {
            ValidationResult.AdicionarErro(validationResult);
        }

        return ValidationResult;
    }
}