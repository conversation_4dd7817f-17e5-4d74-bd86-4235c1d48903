using AutoMapper;
using FluentValidation.Results;
using ONS.SINapse.Repository.IRepository.InMemory;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Shared.Identity;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Shared.Messages;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using ONS.SINapse.Solicitacao.Mapper.Manual;

namespace ONS.SINapse.Solicitacao.CommandHandlers.Handlers;

public class ChatDeSolicitacaoCommandHandler : CommandHandler<ChatDeSolicitacaoCommand>
{
    private readonly ISolicitacaoMemoryRepository _solicitacaoMemoryRepository;
    private readonly IMapper _mapper;
    private readonly IMediatorHandler _mediatorHandler;
    private readonly IUserContext _userContext;
    private const string SolicitacaoNaoEncontrada = "Solicitação não encontrada.";
    private const string MensagemNaoEncontrada = "Mensagem não informada.";

    public ChatDeSolicitacaoCommandHandler(
        ISolicitacaoMemoryRepository solicitacaoRepository,
        IMapper mapper,
        IMediatorHandler mediatorHandler,
        IUserContext userContext)
    {
        _solicitacaoMemoryRepository = solicitacaoRepository;
        _mapper = mapper;
        _mediatorHandler = mediatorHandler;
        _userContext = userContext;
    }

    protected override async Task<ValidationResult> Handle(ChatDeSolicitacaoCommand request, CancellationToken cancellationToken)
    {
        var solicitacao = (_solicitacaoMemoryRepository.Get(x => x.Id == request.SolicitacaoId)).FirstOrDefault();
        if (solicitacao is null)
        {
            ValidationResult.AdicionarErro(SolicitacaoNaoEncontrada);
            return ValidationResult;
        }

        if (string.IsNullOrWhiteSpace(request.Mensagem))
        {
            ValidationResult.AdicionarErro(MensagemNaoEncontrada);
            return ValidationResult;
        }
        
        var usuario = _userContext.Usuario();
        solicitacao.AdicionarMensagemNoChat(request.Mensagem, usuario, _userContext.Perfil);

        var firebaseSolicitacao = _mapper.Map<ChatDeSolicitacaoFirebaseDto>(solicitacao);

        var firebaseCommand = new EnviarMensagemChatSolicitacaoNoFirebaseCommand(firebaseSolicitacao);

        await _mediatorHandler.EnviarComandoAsync(firebaseCommand, cancellationToken);

        return ValidationResult;
    }
}