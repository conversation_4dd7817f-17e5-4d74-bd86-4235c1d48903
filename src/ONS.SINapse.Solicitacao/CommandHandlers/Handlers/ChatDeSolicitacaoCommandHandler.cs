using AutoMapper;
using FluentValidation.Results;
using ONS.SINapse.Repository.IRepository;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Shared.Identity;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Shared.Messages;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;

namespace ONS.SINapse.Solicitacao.CommandHandlers.Handlers;

public class ChatDeSolicitacaoCommandHandler : CommandHandler<ChatDeSolicitacaoCommand>
{
    private readonly ISolicitacaoRepository _solicitacaoRepository;
    private readonly IMapper _mapper;
    private readonly IMediatorHandler _mediatorHandler;
    private readonly IUserContext _userContext;
    private const string SolicitacaoNaoEncontrada = "Solicitação não encontrada.";
    private const string MensagemNaoEncontrada = "Mensagem não informada.";

    public ChatDeSolicitacaoCommandHandler(
        ISolicitacaoRepository solicitacaoRepository,
        IMapper mapper,
        IMediatorHandler mediatorHandler,
        IUserContext userContext)
    {
        _solicitacaoRepository = solicitacaoRepository;
        _mapper = mapper;
        _mediatorHandler = mediatorHandler;
        _userContext = userContext;
    }

    protected override async Task<ValidationResult> Handle(ChatDeSolicitacaoCommand request, CancellationToken cancellationToken)
    {
        var solicitacao = (await _solicitacaoRepository.GetAsync(x => x.Id == request.SolicitacaoId, cancellationToken)).FirstOrDefault();
        if (solicitacao is null)
        {
            ValidationResult.AdicionarErro(SolicitacaoNaoEncontrada);
            return ValidationResult;
        }

        if (string.IsNullOrWhiteSpace(request.Mensagem))
        {
            ValidationResult.AdicionarErro(MensagemNaoEncontrada);
            return ValidationResult;
        }

        var usuario = _userContext.Usuario();
        solicitacao.AdicionarMensagemNoChat(request.Mensagem, usuario, _userContext.Perfil);

        await _solicitacaoRepository.AtualizarAsync(solicitacao, cancellationToken);

        var firebaseSolicitacao = _mapper.Map<ChatDeSolicitacaoFirebaseDto>(solicitacao);

        var firebaseCommand = new EnviarMensagemChatSolicitacaoNoFirebaseCommand(firebaseSolicitacao);

        await _mediatorHandler.EnviarComandoAsync(firebaseCommand, cancellationToken);

        return ValidationResult;
    }
}