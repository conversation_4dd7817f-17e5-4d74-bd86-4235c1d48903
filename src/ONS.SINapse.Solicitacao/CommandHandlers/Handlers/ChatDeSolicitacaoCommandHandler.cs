using FluentValidation.Results;
using ONS.SINapse.Repository.IRepository.Firebase;
using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Shared.Identity;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Shared.Messages;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands.Firebase;
using ONS.SINapse.Solicitacao.Factories;

namespace ONS.SINapse.Solicitacao.CommandHandlers.Handlers;

public class ChatDeSolicitacaoCommandHandler : CommandHandler<ChatDeSolicitacaoCommand>
{
    private readonly ISolicitacaoFirebaseRepository _solicitacaoFirebaseRepository;
    private readonly IMediatorHandler _mediatorHandler;
    private readonly IUserContext _userContext;
    private readonly ISolicitacaoFirebaseCommandFactory _solicitacaoFirebaseCommandFactory;
    private const string SolicitacaoNaoEncontrada = "Solicitação não encontrada.";
    private const string MensagemNaoEncontrada = "Mensagem não informada.";

    public ChatDeSolicitacaoCommandHandler(
        ISolicitacaoFirebaseRepository solicitacaoFirebaseRepository,
        IMediatorHandler mediatorHandler,
        IUserContext userContext,
        ISolicitacaoFirebaseCommandFactory solicitacaoFirebaseCommandFactory)
    {
        _solicitacaoFirebaseRepository = solicitacaoFirebaseRepository;
        _mediatorHandler = mediatorHandler;
        _userContext = userContext;
        _solicitacaoFirebaseCommandFactory = solicitacaoFirebaseCommandFactory;
    }

    protected override async Task<ValidationResult> Handle(ChatDeSolicitacaoCommand request, CancellationToken cancellationToken)
    {
        var solicitacao = await _solicitacaoFirebaseRepository.GetByIdAsync(request.SolicitacaoId, cancellationToken);
        if (solicitacao is null)
        {
            ValidationResult.AdicionarErro(SolicitacaoNaoEncontrada);
            return ValidationResult;
        }

        if (string.IsNullOrWhiteSpace(request.Mensagem))
        {
            ValidationResult.AdicionarErro(MensagemNaoEncontrada);
            return ValidationResult;
        }
        
        var usuario = _userContext.Usuario();
        solicitacao.AdicionarMensagemNoChat(request.Mensagem, usuario, _userContext.Perfil);

        var firebaseCommand = _solicitacaoFirebaseCommandFactory.ObterCommand<CriarMensagemNoChatDeSolicitacaoFirebaseCommand>([solicitacao]);

        var validationResult = await _mediatorHandler.EnviarComandoAsync(firebaseCommand, cancellationToken);

        if (!validationResult.IsValid)
        {
            ValidationResult.AdicionarErro(validationResult);
        }

        return ValidationResult;
    }
}