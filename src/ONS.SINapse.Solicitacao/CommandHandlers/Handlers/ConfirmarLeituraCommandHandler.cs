using FluentValidation.Results;
using ONS.SINapse.Repository.IRepository.Firebase;

using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Shared.Identity;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Shared.Messages;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands.Firebase;
using ONS.SINapse.Solicitacao.Factories;

namespace ONS.SINapse.Solicitacao.CommandHandlers.Handlers;

public class ConfirmarLeituraCommandHandler : CommandHandler<ConfirmarLeituraCommand>
{
    private readonly ISolicitacaoFirebaseRepository _solicitacaoFirebaseRepository;
    private readonly IMediatorHandler _mediatorHandler;
    private readonly IUserContext _userContext;
    private readonly ISolicitacaoFirebaseCommandFactory _solicitacaoFirebaseCommandFactory;
    private const string SolicitacaoNaoEncontrada = "Solicitação não encontrada.";

    public ConfirmarLeituraCommandHandler(
        ISolicitacaoFirebaseRepository solicitacaoFirebaseRepository,
        IMediatorHandler mediatorHandler,
        IUserContext userContext,
        ISolicitacaoFirebaseCommandFactory solicitacaoFirebaseCommandFactory)
    {
        _solicitacaoFirebaseRepository = solicitacaoFirebaseRepository;
        _mediatorHandler = mediatorHandler;
        _userContext = userContext;
        _solicitacaoFirebaseCommandFactory = solicitacaoFirebaseCommandFactory;
    }

    protected override async Task<ValidationResult> Handle(ConfirmarLeituraCommand request, CancellationToken cancellationToken)
    {
        var solicitacao = await _solicitacaoFirebaseRepository.GetByIdAsync(request.SolicitacaoId, cancellationToken);

        if (solicitacao is null)
        {
            ValidationResult.AdicionarErro(SolicitacaoNaoEncontrada);
            return ValidationResult;
        }

        var usuario = _userContext.Usuario();
        var existeMensagemNaoLida = solicitacao.ConfirmarLeitura(usuario, _userContext.Perfil);

        if (!existeMensagemNaoLida) return ValidationResult;

        var firebaseCommand = _solicitacaoFirebaseCommandFactory.ObterCommand<LeituraDeSolicitacaoNoFirebaseCommand>([solicitacao]);

        var validationResult = await _mediatorHandler.EnviarComandoAsync(firebaseCommand, cancellationToken);
        
        if (!validationResult.IsValid)
        { 
            ValidationResult.AdicionarErro(validationResult);
        } 
        
        return ValidationResult;
    }
}