using AutoMapper;
using FluentValidation.Results;
using ONS.SINapse.Repository.IRepository.InMemory;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Shared.Identity;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Shared.Messages;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;

namespace ONS.SINapse.Solicitacao.CommandHandlers.Handlers;

public class ConfirmarLeituraCommandHandler : CommandHandler<ConfirmarLeituraCommand>
{
    private readonly ISolicitacaoMemoryRepository _solicitacaoMemoryRepository;
    private readonly IMapper _mapper;
    private readonly IMediatorHandler _mediatorHandler;
    private readonly IUserContext _userContext;
    private const string SolicitacaoNaoEncontrada = "Solicitação não encontrada.";

    public ConfirmarLeituraCommandHandler(
        ISolicitacaoMemoryRepository solicitacaoMemoryRepository,
        I<PERSON>apper mapper,
        IMediatorHandler mediatorHandler,
        IUserContext userContext)
    {
        _solicitacaoMemoryRepository = solicitacaoMemoryRepository;
        _mapper = mapper;
        _mediatorHandler = mediatorHandler;
        _userContext = userContext;
    }

    protected override async Task<ValidationResult> Handle(ConfirmarLeituraCommand request, CancellationToken cancellationToken)
    {
        var solicitacao = (_solicitacaoMemoryRepository.Get(x => x.Id == request.SolicitacaoId)).FirstOrDefault();
        if (solicitacao is null)
        {
            ValidationResult.AdicionarErro(SolicitacaoNaoEncontrada);
            return ValidationResult;
        }

        var usuario = _userContext.Usuario();
        var existeMensagemNaoLida = solicitacao.ConfirmarLeitura(usuario, _userContext.Perfil);

        if (!existeMensagemNaoLida) return ValidationResult;

        var firebaseSolicitacao = _mapper.Map<ChatDeSolicitacaoFirebaseDto>(solicitacao);

        var firebaseCommand = new ConfirmarLeituraSolicitacaoNoFirebaseCommand(firebaseSolicitacao);

        await _mediatorHandler.EnviarComandoAsync(firebaseCommand, cancellationToken);
       
        return ValidationResult;
    }
}