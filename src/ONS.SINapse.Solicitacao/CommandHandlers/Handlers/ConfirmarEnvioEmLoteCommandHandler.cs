using FluentValidation;
using FluentValidation.Results;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Repository.IRepository.Firebase;
using ONS.SINapse.Shared.Bus;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Shared.Factories;
using ONS.SINapse.Shared.Identity;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Shared.Messages;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using ONS.SINapse.Solicitacao.Dtos.Integracao;
using ONS.SINapse.Solicitacao.EventHandlers.Events;
using ONS.SINapse.Solicitacao.Factories;
using ONS.SINapse.Solicitacao.Helpers;

namespace ONS.SINapse.Solicitacao.CommandHandlers.Handlers;

public class ConfirmarEnvioEmLoteCommandHandler : CommandHandler<ConfirmarEnvioEmLoteCommand, StatusDeSolicitacaoIntegracaoEnvioEmLoteDto>
{
    private readonly IValidator<StatusDeSolicitacaoIntegracaoRecebimentoDto> _validator;
    private readonly ISolicitacaoFirebaseRepository _solicitacaoFirebaseRepository;
    private readonly IMediatorHandler _mediatorHandler;
    private readonly IUserContext _userContext;
    private readonly ISqsBus _bus;

    public ConfirmarEnvioEmLoteCommandHandler(IValidator<StatusDeSolicitacaoIntegracaoRecebimentoDto> validator,
        ISolicitacaoFirebaseRepository solicitacaoFirebaseRepository,
        ISolicitacaoFirebaseCommandFactory solicitacaoFirebaseCommandFactory,
        IMediatorHandler mediatorHandler,
        IUserContext userContext,
        ISqsBus bus)
    {
        _validator = validator;
        _solicitacaoFirebaseRepository = solicitacaoFirebaseRepository;
        _mediatorHandler = mediatorHandler;
        _userContext = userContext;
        _bus = bus;
    }

    protected override async Task<StatusDeSolicitacaoIntegracaoEnvioEmLoteDto> Handle(ConfirmarEnvioEmLoteCommand request, CancellationToken cancellationToken)
    {
        if(request.Solicitacoes.Count == 0)
            return new StatusDeSolicitacaoIntegracaoEnvioEmLoteDto([]);

        var ids = request.Solicitacoes.Select(s => s.Id!).ToArray();
        var solicitacoes = await _solicitacaoFirebaseRepository.GetByIdAsync(ids, cancellationToken);

        var solicitacoesInvalidas = Validar(request, solicitacoes);

        var solicitacoesValidas = solicitacoes
            .ExceptBy(solicitacoesInvalidas.Select(x => x.Id), solicitacao => solicitacao.Id)
            .ToList();

        if (solicitacoesValidas.Count == 0)
        {
            var statusDeSolicitacaoInvalidaEvent = new StatusDeSolicitacaoIntegracaoRecebidaEvent(solicitacoesInvalidas);
            await _mediatorHandler.PublicarEventoAsync(statusDeSolicitacaoInvalidaEvent, cancellationToken);
            return new StatusDeSolicitacaoIntegracaoEnvioEmLoteDto(solicitacoesInvalidas);
        }
        var dtos = solicitacoesValidas.Select( s => GerarCadastrarSolicitacaoDto(s, _userContext.Usuario()));
        var command = new ConfirmarEnvioSolicitacaoFirebaseCommand(dtos.ToList());
        var result = await _mediatorHandler.EnviarComandoAsync<ConfirmarEnvioSolicitacaoFirebaseCommand, StatusDeSolicitacaoIntegracaoEnvioEmLoteDto>(command, cancellationToken);
        var solicitacoesEnviadas = result.Solicitacoes.ToList();
        return result.ValidationResult.IsValid
            ? await PublicarEventosDeSolicitacoesProcessadas(solicitacoesEnviadas, solicitacoesInvalidas, cancellationToken)
            : await PublicarEventosDeErros(solicitacoesEnviadas, solicitacoesInvalidas, result.ValidationResult, cancellationToken);
    }

    private static CadastroSolicitacaoDto GerarCadastrarSolicitacaoDto(Entities.Entities.Solicitacao solicitacao, UsuarioDto usuarioDto)
    {
        var origem = new ObjetoDeManobraDto(solicitacao.Origem.Codigo, solicitacao.Origem.Nome);

        var destino = new ObjetoDeManobraDto(solicitacao.Destino.Codigo, solicitacao.Destino.Nome);

        var local = solicitacao.Local is null
            ? null
            : new ObjetoDeManobraDto(solicitacao.Local.Codigo, solicitacao.Local.Nome);
            
        return new CadastroSolicitacaoDto(
            IdSolicitacaoFactory.GerarNovoId(origem.Codigo, destino.Codigo),
            origem,
            destino,
            solicitacao.InformacaoAdicional,
            solicitacao.Mensagem,
            usuarioDto,
            solicitacao.Id
        )
        { 
            Local = local,
            EncaminharPara = null,
            Motivo = solicitacao.Motivo,
            SistemaDeOrigem = Entities.Entities.Solicitacao.SistemaDeOrigemInterno,
            Tags = solicitacao.Tags.ToList(),
            RequerAprovacaoEnvio = false
        };
    }

    private async Task<StatusDeSolicitacaoIntegracaoEnvioEmLoteDto> PublicarEventosDeErros(
        List<StatusDeSolicitacaoIntegracaoEnvioDto> solicitacoesCanceladas,
        List<StatusDeSolicitacaoIntegracaoEnvioDto> solicitacoesInvalidas,
        ValidationResult validationResult,
        CancellationToken cancellationToken)
    {
        solicitacoesCanceladas.ForEach(x => x.AdicionarErros(validationResult.Errors.Select(v => v.ErrorMessage).ToArray()));
        var solicitacoesProcessadas = solicitacoesCanceladas.Concat(solicitacoesInvalidas).ToList();
        var statusDeSolicitacaoIntegracaoRecebidaEvent = new StatusDeSolicitacaoIntegracaoRecebidaEvent(solicitacoesProcessadas);
        await _mediatorHandler.PublicarEventoAsync(statusDeSolicitacaoIntegracaoRecebidaEvent, cancellationToken);
        return new StatusDeSolicitacaoIntegracaoEnvioEmLoteDto(solicitacoesProcessadas);
    }

    private async Task<StatusDeSolicitacaoIntegracaoEnvioEmLoteDto> PublicarEventosDeSolicitacoesProcessadas(
        List<StatusDeSolicitacaoIntegracaoEnvioDto> solicitacoesCanceladas,
        List<StatusDeSolicitacaoIntegracaoEnvioDto> solicitacoesInvalidas,
        CancellationToken cancellationToken)
    {
        var solicitacoesProcessadas = solicitacoesCanceladas.Concat(solicitacoesInvalidas).ToList();
        var statusDeSolicitacaoIntegracaoRecebidaEvent = new StatusDeSolicitacaoIntegracaoRecebidaEvent(solicitacoesProcessadas);
        var enviarKafka = _mediatorHandler.PublicarEventoAsync(statusDeSolicitacaoIntegracaoRecebidaEvent, cancellationToken);
        var solicitacoesId = solicitacoesProcessadas
            .Where(x => x.Status == StatusDeSolicitacao.Enviada)
            .Select(x => x.Id)
            .ToList();
        
        var eventoSolicitacoesConcluidas = new SolicitacaoConcluidaEvent { Solicitacoes = solicitacoesId };
        var notificar = _bus.Publish(eventoSolicitacoesConcluidas, cancellationToken);

        await Task.WhenAll(enviarKafka, notificar);

        return new StatusDeSolicitacaoIntegracaoEnvioEmLoteDto(solicitacoesProcessadas);
    }

    #region Validacoes

    private List<StatusDeSolicitacaoIntegracaoEnvioDto> Validar(ConfirmarEnvioEmLoteCommand request, List<Entities.Entities.Solicitacao> solicitacoes)
    {
        var requestInvalidos = ValidarRequest(request);
        var solicitacoesNaoExistem = ValidarSolicitacaoNaoExistente(request, solicitacoes);
        var solicitacoesSolicitanteInvalido = ValidarSolicitacaoSolicitanteInvalido(request, solicitacoes);
        var solicitacoesStatusInvalido = ValidarSolicitacaoStatusInvalido(request, solicitacoes);
        
        return Enumerable.Empty<StatusDeSolicitacaoIntegracaoEnvioDto>()
                .Concat(requestInvalidos)
                .Concat(solicitacoesNaoExistem)
                .Concat(solicitacoesSolicitanteInvalido)
                .Concat(solicitacoesStatusInvalido)
                .GroupBy(group => new
                    {
                        group.Id,
                        group.Usuario,
                        group.Justificativa,
                        group.SistemaDeOrigem
                    },
                    by => by.Erros,
                    (group, by) => SolicitacaoHelper.CreateErrorDto(
                        group.Id,
                        group.Usuario,
                        group.Justificativa,
                        group.SistemaDeOrigem,
                        by.SelectMany(x => x).ToArray()
                    ))
                .ToList();
    }

    private static List<StatusDeSolicitacaoIntegracaoEnvioDto> ValidarSolicitacaoNaoExistente(ConfirmarEnvioEmLoteCommand request, List<Entities.Entities.Solicitacao> solicitacoes)
    {
        return request.Solicitacoes
            .ExceptBy(solicitacoes.Select(x => x.Id), dto => dto.Id)
            .Select(erro => SolicitacaoHelper.CreateErrorDto(
                erro.Id ?? string.Empty,
                erro.Usuario ?? new UsuarioDto(),
                erro.Motivo,
                erro.SistemaDeOrigem ?? Entities.Entities.Solicitacao.SistemaDeOrigemInterno,
                [$"Não foi possível alterar o status da solicitação {erro.Id} para {erro.Status.GetDescription()}. Solicitação não existe."]
            ))
            .ToList();
    }

    private List<StatusDeSolicitacaoIntegracaoEnvioDto> ValidarRequest(ConfirmarEnvioEmLoteCommand request)
    {
        return request.Solicitacoes
            .Select(solicitacao => (Validacao: _validator.Validate(solicitacao), Solicitacao: solicitacao))
            .Where(x => !x.Validacao.IsValid)
            .Select(erro => SolicitacaoHelper.CreateErrorDto(
                erro.Solicitacao.Id ?? string.Empty,
                erro.Solicitacao.Usuario ?? new UsuarioDto(),
                erro.Solicitacao.Motivo,
                erro.Solicitacao.SistemaDeOrigem ?? Entities.Entities.Solicitacao.SistemaDeOrigemInterno,
                erro.Validacao.Errors.Select(x => x.ErrorMessage).ToArray()
            ))
            .ToList();
    }

    private static List<StatusDeSolicitacaoIntegracaoEnvioDto> ValidarSolicitacaoStatusInvalido(ConfirmarEnvioEmLoteCommand request, List<Entities.Entities.Solicitacao> solicitacoes)
    {
        if (solicitacoes.Count == 0) return [];
        
        return solicitacoes
            .Join(request.Solicitacoes, 
                a => a.Id, 
                b => b.Id, 
                (a, b) => new { a, b })
            .Where(t => t.a.Status != StatusDeSolicitacao.AguardandoEnvio)
            .Select(t => SolicitacaoHelper.CreateErrorDto(
                t.b.Id ?? string.Empty,
                t.b.Usuario ?? new UsuarioDto(),
                t.b.Motivo,
                t.b.SistemaDeOrigem ?? Entities.Entities.Solicitacao.SistemaDeOrigemInterno,
                [$"Não foi possível alterar o status da solicitação {t.b.Id} para {t.b.Status.GetDescription()}. Solicitação se encontra com status {t.a.Status.GetDescription()}."]))
            .ToList();
    }

    private static List<StatusDeSolicitacaoIntegracaoEnvioDto> ValidarSolicitacaoSolicitanteInvalido(ConfirmarEnvioEmLoteCommand request, List<Entities.Entities.Solicitacao> solicitacoes)
    {
        if (solicitacoes.Count == 0) return [];
        
        return solicitacoes
            .Join(request.Solicitacoes, 
                a => a.Id, 
                b => b.Id, 
                (a, b) => new { a, b })
            .Where(t => !t.a.IsSolicitante([t.b.Centro?.Codigo ?? string.Empty]))
            .Select(t => SolicitacaoHelper.CreateErrorDto(
                t.b.Id ?? string.Empty,
                t.b.Usuario ?? new UsuarioDto(),
                t.b.Motivo,
                t.b.SistemaDeOrigem ?? Entities.Entities.Solicitacao.SistemaDeOrigemInterno,
                [$"Não foi possível alterar o status da solicitação {t.b.Id} para {t.b.Status.GetDescription()}. Operação não permitida, usuário não é o solicitante."]))
            .ToList();
    }

    #endregion
    
}