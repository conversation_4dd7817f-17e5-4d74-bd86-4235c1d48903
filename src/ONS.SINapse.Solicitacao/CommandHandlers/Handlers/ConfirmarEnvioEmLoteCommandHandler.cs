using System.Text;
using AutoMapper;
using FluentValidation;
using FluentValidation.Results;
using Microsoft.Extensions.Logging;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Repository.IRepository.InMemory;
using ONS.SINapse.Shared.Bus;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Shared.Factories;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Shared.Messages;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using ONS.SINapse.Solicitacao.Dtos.Integracao;
using ONS.SINapse.Solicitacao.EventHandlers.Events;
using ONS.SINapse.Solicitacao.Helpers;

namespace ONS.SINapse.Solicitacao.CommandHandlers.Handlers;

public class ConfirmarEnvioEmLoteCommandHandler : CommandHandler<ConfirmarEnvioEmLoteCommand, StatusDeSolicitacaoIntegracaoEnvioEmLoteDto>
{
    private readonly IValidator<StatusDeSolicitacaoIntegracaoRecebimentoDto> _validator;
    private readonly ISolicitacaoMemoryRepository _solicitacaoMemoryRepository;
    private readonly IMediatorHandler _mediatorHandler;
    private readonly IMapper _mapper;
    private readonly ILogger<ConfirmarEnvioEmLoteCommandHandler> _logger;
    private readonly ISqsBus _bus;

    public ConfirmarEnvioEmLoteCommandHandler(IValidator<StatusDeSolicitacaoIntegracaoRecebimentoDto> validator,
        ISolicitacaoMemoryRepository solicitacaoMemoryRepository,
        IMediatorHandler mediatorHandler,
        IMapper mapper,
        ILogger<ConfirmarEnvioEmLoteCommandHandler> logger,
        ISqsBus bus)
    {
        _validator = validator;
        _solicitacaoMemoryRepository = solicitacaoMemoryRepository;
        _mediatorHandler = mediatorHandler;
        _mapper = mapper;
        _logger = logger;
        _bus = bus;
    }

    protected override async Task<StatusDeSolicitacaoIntegracaoEnvioEmLoteDto> Handle(ConfirmarEnvioEmLoteCommand request, CancellationToken cancellationToken)
    {
        if(request.Solicitacoes.Count == 0)
            return new StatusDeSolicitacaoIntegracaoEnvioEmLoteDto([]);

        var ids = request.Solicitacoes.Select(s => s.Id).ToArray();
        var solicitacoes = _solicitacaoMemoryRepository.GetList(x => ids.Contains(x.Id)).ToList();

        var solicitacoesInvalidas = Validar(request, solicitacoes);

        var solicitacoesValidas = solicitacoes
            .ExceptBy(solicitacoesInvalidas.Select(x => x.Id), solicitacao => solicitacao.Id)
            .ToList();

        if (solicitacoesValidas.Count == 0)
        {
            var statusDeSolicitacaoInvalidaEvent = new StatusDeSolicitacaoIntegracaoRecebidaEvent(solicitacoesInvalidas);
            await _mediatorHandler.PublicarEventoAsync(statusDeSolicitacaoInvalidaEvent, cancellationToken);
            return new StatusDeSolicitacaoIntegracaoEnvioEmLoteDto(solicitacoesInvalidas);
        }
        
        var solicitacoesProcessadas = await CadastrarSolicitacoesAsync(solicitacoesValidas, request.Solicitacoes, cancellationToken);
        var solicitacoesEnviadas = solicitacoesProcessadas
            .Join(request.Solicitacoes,
                inner => inner.Id,
                join => join.Id,
                (inner, join) =>
                {
                    inner.ConfirmarEnvio(join.Usuario!);
                    return new StatusDeSolicitacaoIntegracaoEnvioDto(
                        inner.Id,
                        inner.Status,
                        join.Usuario!,
                        DateTime.UtcNow,
                        null,
                        join.SistemaDeOrigem ?? Entities.Entities.Solicitacao.SistemaDeOrigemInterno,
                        []
                    );
                })
            .ToList();
        var validationResult = await EnviarComandoAoFirebase(solicitacoesProcessadas, cancellationToken);
        return validationResult.IsValid
            ? await PublicarEventosDeSolicitacoesProcessadas(solicitacoesEnviadas, solicitacoesInvalidas, cancellationToken)
            : await PublicarEventosDeErros(solicitacoesEnviadas, solicitacoesInvalidas, validationResult, cancellationToken);
    }
    
    private async Task<List<Entities.Entities.Solicitacao>> CadastrarSolicitacoesAsync(
        List<Entities.Entities.Solicitacao> solicitacoes,
        IReadOnlyCollection<StatusDeSolicitacaoIntegracaoRecebimentoDto> solicitacoesDtoValidos,
        CancellationToken cancellationToken)
    {
        var solicitacoesProcessadas = solicitacoesDtoValidos
            .Join(solicitacoes,
                inner => inner.Id,
                join => join.Id,
                (inner, join) => 
                    (solicitacao: join, solicitacaoCadastro: GerarCadastrarSolicitacaoDto(join, null, inner.Usuario!)))
            .ToList();
        
        var command = new CadastrarSolicitacaoEmLoteCommand(solicitacoesProcessadas
            .Select(x => x.solicitacaoCadastro)
            .ToList());
        
        var result = await _mediatorHandler
                .EnviarComandoAsync<CadastrarSolicitacaoEmLoteCommand, StatusDeSolicitacaoIntegracaoEnvioEmLoteDto>(command, cancellationToken);

        var message = new StringBuilder();
        
        foreach (var solicitacaoInvalida in result.Solicitacoes.Where(x => !x.EstaValido()))
        {
            message.AppendLine($"[ENCAMINHAR SOLICITACAO] - Erro ao encaminhar solicitação. Codigo: {solicitacaoInvalida.Id} Erros: {solicitacaoInvalida.ErrosFormatados()}");
        }

        if (message.Length > 0)
        {
            _logger.LogInformation("Message: {Message}", message.ToString());
        }

        return solicitacoesProcessadas
            .Join(result.Solicitacoes,
                inner => inner.solicitacaoCadastro.Id,
                join => join.Id,
                (inner, join) => new { valido = join.EstaValido(), inner.solicitacao })
            .Where(x => x.valido)
            .Select(x => x.solicitacao)
            .ToList();
    }

    private static CadastroSolicitacaoDto GerarCadastrarSolicitacaoDto(Entities.Entities.Solicitacao solicitacao, 
        string? informacaoAdicional,
        UsuarioDto usuarioDto)
    {
        var origem = new ObjetoDeManobraDto(solicitacao.Origem.Codigo, solicitacao.Origem.Nome);

        var destino = new ObjetoDeManobraDto(solicitacao.Destino.Codigo, solicitacao.Destino.Nome);

        var local = solicitacao.Local is null
            ? null
            : new ObjetoDeManobraDto(solicitacao.Local.Codigo, solicitacao.Local.Nome);
            
        return new CadastroSolicitacaoDto(
            IdSolicitacaoFactory.GerarNovoId(origem.Codigo, destino.Codigo),
            origem,
            destino,
            informacaoAdicional,
            solicitacao.Mensagem,
            usuarioDto,
            solicitacao.Id
        )
        { 
            Local = local,
            EncaminharPara = null,
            Motivo = solicitacao.Motivo,
            SistemaDeOrigem = Entities.Entities.Solicitacao.SistemaDeOrigemInterno,
            Tags = solicitacao.Tags.ToList()
        };
    }

    private async Task<ValidationResult> EnviarComandoAoFirebase(
        List<Entities.Entities.Solicitacao> solicitacoesValidas, CancellationToken cancellationToken)
    {
        var solicitacaoFirebase = _mapper.Map<List<StatusDeSolicitacaoFirebaseDto>>(solicitacoesValidas);
        var command = new EnviarTrocaDeStatusAoFirebaseCommand(solicitacaoFirebase);
        return await _mediatorHandler.EnviarComandoAsync(command, cancellationToken);
    }

    private async Task<StatusDeSolicitacaoIntegracaoEnvioEmLoteDto> PublicarEventosDeErros(
        List<StatusDeSolicitacaoIntegracaoEnvioDto> solicitacoesCanceladas,
        List<StatusDeSolicitacaoIntegracaoEnvioDto> solicitacoesInvalidas,
        ValidationResult validationResult,
        CancellationToken cancellationToken)
    {
        solicitacoesCanceladas.ForEach(x => x.AdicionarErros(validationResult.Errors.Select(v => v.ErrorMessage).ToArray()));
        var solicitacoesProcessadas = solicitacoesCanceladas.Concat(solicitacoesInvalidas).ToList();
        var statusDeSolicitacaoIntegracaoRecebidaEvent = new StatusDeSolicitacaoIntegracaoRecebidaEvent(solicitacoesProcessadas);
        await _mediatorHandler.PublicarEventoAsync(statusDeSolicitacaoIntegracaoRecebidaEvent, cancellationToken);
        return new StatusDeSolicitacaoIntegracaoEnvioEmLoteDto(solicitacoesProcessadas);
    }

    private async Task<StatusDeSolicitacaoIntegracaoEnvioEmLoteDto> PublicarEventosDeSolicitacoesProcessadas(
        List<StatusDeSolicitacaoIntegracaoEnvioDto> solicitacoesCanceladas,
        List<StatusDeSolicitacaoIntegracaoEnvioDto> solicitacoesInvalidas,
        CancellationToken cancellationToken)
    {
        var solicitacoesProcessadas = solicitacoesCanceladas.Concat(solicitacoesInvalidas).ToList();
        var statusDeSolicitacaoIntegracaoRecebidaEvent = new StatusDeSolicitacaoIntegracaoRecebidaEvent(solicitacoesProcessadas);
        var enviarKafka = _mediatorHandler.PublicarEventoAsync(statusDeSolicitacaoIntegracaoRecebidaEvent, cancellationToken);

        var eventoSolicitacoesConcluidas = new SolicitacaoConcluidaEvent { Solicitacoes = solicitacoesProcessadas.Select(x => x.Id).ToList() };
        var notificar = _bus.Publish(eventoSolicitacoesConcluidas, cancellationToken);

        await Task.WhenAll(enviarKafka, notificar);

        return new StatusDeSolicitacaoIntegracaoEnvioEmLoteDto(solicitacoesProcessadas);
    }

    #region Validacoes

    private List<StatusDeSolicitacaoIntegracaoEnvioDto> Validar(ConfirmarEnvioEmLoteCommand request, List<Entities.Entities.Solicitacao> solicitacoes)
    {
        var requestInvalidos = ValidarRequest(request);
        var solicitacoesNaoExistem = ValidarSolicitacaoNaoExistente(request, solicitacoes);
        var solicitacoesSolicitanteInvalido = ValidarSolicitacaoSolicitanteInvalido(request, solicitacoes);
        var solicitacoesStatusInvalido = ValidarSolicitacaoStatusInvalido(request, solicitacoes);
        
        return Enumerable.Empty<StatusDeSolicitacaoIntegracaoEnvioDto>()
                .Concat(requestInvalidos)
                .Concat(solicitacoesNaoExistem)
                .Concat(solicitacoesSolicitanteInvalido)
                .Concat(solicitacoesStatusInvalido)
                .GroupBy(group => new
                    {
                        group.Id,
                        group.Usuario,
                        group.Justificativa,
                        group.SistemaDeOrigem
                    },
                    by => by.Erros,
                    (group, by) => SolicitacaoHelper.CreateErrorDto(
                        group.Id,
                        group.Usuario,
                        group.Justificativa,
                        group.SistemaDeOrigem,
                        by.SelectMany(x => x).ToArray()
                    ))
                .ToList();
    }

    private static List<StatusDeSolicitacaoIntegracaoEnvioDto> ValidarSolicitacaoNaoExistente(ConfirmarEnvioEmLoteCommand request, List<Entities.Entities.Solicitacao> solicitacoes)
    {
        return request.Solicitacoes
            .ExceptBy(solicitacoes.Select(x => x.Id), dto => dto.Id)
            .Select(erro => SolicitacaoHelper.CreateErrorDto(
                erro.Id ?? string.Empty,
                erro.Usuario ?? new UsuarioDto(),
                erro.Motivo,
                erro.SistemaDeOrigem ?? Entities.Entities.Solicitacao.SistemaDeOrigemInterno,
                [$"Não foi possível alterar o status da solicitação {erro.Id} para {erro.Status.GetDescription()}. Solicitação não existe."]
            ))
            .ToList();
    }

    private List<StatusDeSolicitacaoIntegracaoEnvioDto> ValidarRequest(ConfirmarEnvioEmLoteCommand request)
    {
        return request.Solicitacoes
            .Select(solicitacao => (Validacao: _validator.Validate(solicitacao), Solicitacao: solicitacao))
            .Where(x => !x.Validacao.IsValid)
            .Select(erro => SolicitacaoHelper.CreateErrorDto(
                erro.Solicitacao.Id ?? string.Empty,
                erro.Solicitacao.Usuario ?? new UsuarioDto(),
                erro.Solicitacao.Motivo,
                erro.Solicitacao.SistemaDeOrigem ?? Entities.Entities.Solicitacao.SistemaDeOrigemInterno,
                erro.Validacao.Errors.Select(x => x.ErrorMessage).ToArray()
            ))
            .ToList();
    }

    private static List<StatusDeSolicitacaoIntegracaoEnvioDto> ValidarSolicitacaoStatusInvalido(ConfirmarEnvioEmLoteCommand request, List<Entities.Entities.Solicitacao> solicitacoes)
    {
        if (solicitacoes.Count == 0) return [];
        
        return solicitacoes
            .Join(request.Solicitacoes, 
                a => a.Id, 
                b => b.Id, 
                (a, b) => new { a, b })
            .Where(t => t.a.Status != StatusDeSolicitacao.AguardandoEnvio)
            .Select(t => SolicitacaoHelper.CreateErrorDto(
                t.b.Id ?? string.Empty,
                t.b.Usuario ?? new UsuarioDto(),
                t.b.Motivo,
                t.b.SistemaDeOrigem ?? Entities.Entities.Solicitacao.SistemaDeOrigemInterno,
                [$"Não foi possível alterar o status da solicitação {t.b.Id} para {t.b.Status.GetDescription()}. Solicitação se encontra com status {t.a.Status.GetDescription()}."]))
            .ToList();
    }

    private static List<StatusDeSolicitacaoIntegracaoEnvioDto> ValidarSolicitacaoSolicitanteInvalido(ConfirmarEnvioEmLoteCommand request, List<Entities.Entities.Solicitacao> solicitacoes)
    {
        if (solicitacoes.Count == 0) return [];
        
        return solicitacoes
            .Join(request.Solicitacoes, 
                a => a.Id, 
                b => b.Id, 
                (a, b) => new { a, b })
            .Where(t => !t.a.IsSolicitante([t.b.Centro?.Codigo ?? string.Empty]))
            .Select(t => SolicitacaoHelper.CreateErrorDto(
                t.b.Id ?? string.Empty,
                t.b.Usuario ?? new UsuarioDto(),
                t.b.Motivo,
                t.b.SistemaDeOrigem ?? Entities.Entities.Solicitacao.SistemaDeOrigemInterno,
                [$"Não foi possível alterar o status da solicitação {t.b.Id} para {t.b.Status.GetDescription()}. Operação não permitida, usuário não é o solicitante."]))
            .ToList();
    }

    #endregion
    
}