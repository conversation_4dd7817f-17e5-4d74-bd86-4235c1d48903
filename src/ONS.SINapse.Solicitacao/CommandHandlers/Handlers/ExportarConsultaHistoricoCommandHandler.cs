using CsvHelper;
using CsvHelper.Configuration;
using MongoDB.Bson;
using ONS.SINapse.Repository.Imp.Context;
using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Shared.Identity;
using ONS.SINapse.Shared.Messages;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using ONS.SINapse.Solicitacao.Dtos;
using ONS.SINapse.Solicitacao.Mapper;
using System.Globalization;
using System.Text;

namespace ONS.SINapse.Solicitacao.CommandHandlers.Handlers;

public class ExportarConsultaHistoricoCommandHandler : CommandHandler<ExportarConsultaHistoricoCommand, ArquivoResultDto>
{
    private readonly IUserContext _userContext;
    private readonly IMongoReadOnlyConnection _mongoReadOnlyConnection;

    public ExportarConsultaHistoricoCommandHandler(
        IUserContext userContext,
        IMongoReadOnlyConnection mongoReadOnlyConnection)
    {
        _userContext = userContext;
        _mongoReadOnlyConnection = mongoReadOnlyConnection;
    }
    
    protected override async Task<ArquivoResultDto> Handle(ExportarConsultaHistoricoCommand request, CancellationToken cancellationToken)
    {
        var centro = _userContext.Perfil.Centros.Aggregate("", (seed, current) => seed + "_" + current);
        var pipe = ExportarConsultaHistoricoPipeline.Pipeline(request, _userContext.Perfil);
        var dados = 
            await _mongoReadOnlyConnection.AggregateAsync<ArquivoConsultaHistoricoDto>(pipe, cancellationToken);
        
        var config = new CsvConfiguration(new CultureInfo("pt-BR"))
        {
            Delimiter = ";",
            BufferSize = 1024,
            Quote = '"',
            ShouldQuote = _ => false
        };
        
        var fileName = $"historico_simplificado_{DateTime.Now:ddMMyyyyHHmm}_{centro}";

        var stream = new MemoryStream();
        
        await using var writer = new StreamWriter(stream, encoding:Encoding.UTF8, leaveOpen: true);
        await using var csvWriter = new CsvWriter(writer, config);
        
        csvWriter.Context.RegisterClassMap<ArquivoConsultaHistoricoMapper>();
        
        await csvWriter.WriteRecordsAsync(dados, cancellationToken);
        await csvWriter.FlushAsync();
        
        stream.Position = 0;
        
        Result = new ArquivoResultDto(stream, fileName);
        
        return Result;
    }


    private static class ExportarConsultaHistoricoPipeline
    {
        public static BsonDocument[] Pipeline(ExportarConsultaHistoricoCommand command, Perfil perfilDeUsuario)
        {
            var filtro = Filtro(command, perfilDeUsuario);
            return new[]
            {
                new BsonDocument { { "$match", filtro } },
                new BsonDocument
                {
                    {
                        "$project", new BsonDocument
                        {
                            { "Codigo", "$_id" },
                            { "Origem", "$obj_origem.nom_local" },
                            { "Destino", "$obj_destino.nom_local" },
                            { "Mensagem", "$dsc_mensagem" },
                            { "DataDeCadastro", "$din_criacao" },
                            { "UsuarioDeCadastro", "$obj_usuario.nom_usuario" },
                            { "CodigoStatus", "$cod_status" },
                            { "InformacaoAdicional", "$dsc_informacaoadicional" },
                            {
                                "max_alteracao", new BsonDocument
                                {
                                    {
                                        "$reduce", new BsonDocument
                                        {
                                            { "input", "$list_historicostatus" },
                                            {
                                                "initialValue",
                                                new BsonDocument { { "din_alteracao", new DateTime(1970, 1, 1) } }
                                            },
                                            {
                                                "in", new BsonDocument
                                                {
                                                    {
                                                        "$cond", new BsonArray
                                                        {
                                                            new BsonDocument
                                                            {
                                                                {
                                                                    "$gt",
                                                                    new BsonArray
                                                                    {
                                                                        "$$this.din_alteracao", "$$value.din_alteracao"
                                                                    }
                                                                }
                                                            },
                                                            "$$this",
                                                            "$$value"
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                },
                new BsonDocument
                {
                    {
                        "$project", new BsonDocument
                        {
                            { "Codigo", 1 },
                            { "Origem", 1 },
                            { "Destino", 1 },
                            { "Mensagem", 1 },
                            { "DataDeCadastro", 1 },
                            { "UsuarioDeCadastro", 1 },
                            { "CodigoStatus", 1 },
                            { "InformacaoAdicional", 1 },
                            { "DataDeAlteracao", "$max_alteracao.din_alteracao" },
                            { "UsuarioDeAlteracao", "$max_alteracao.obj_usuario.nom_usuario" }
                        }
                    }
                }
            };
        }

        private static BsonDocument Filtro(ExportarConsultaHistoricoCommand command,
            Perfil perfilDeUsuario)
        {
            var filtro = new BsonDocument();

            var codigosCentro = perfilDeUsuario.Centros;
            
            var centroFilter = new BsonDocument("$in", new BsonArray(codigosCentro));
            
            filtro.Add("$or", new BsonArray
            {
                new BsonDocument("obj_origem.cod_local", centroFilter),
                new BsonDocument("obj_destino.cod_local",centroFilter)
            });
            
            if (!string.IsNullOrEmpty(command.Origem))
                filtro.Add("obj_origem.cod_local", command.Origem);
        
            if (!string.IsNullOrEmpty(command.Destino))
                filtro.Add("obj_destino.cod_local", command.Destino);
        
            if (command.PeriodoInicial.IsValid() || command.PeriodoFinal.IsValid())
            {
                var periodo = new BsonDocument();

                if (command.PeriodoInicial.IsValid())
                    periodo.Add("$gte", command.PeriodoInicial!.Value);
            
                if(command.PeriodoFinal.IsValid())
                    periodo.Add("$lte", command.PeriodoFinal!.Value);

                filtro.Add("din_criacao", periodo);
            }
        
            if (!string.IsNullOrEmpty(command.Mensagem))
            {
                var regex = new BsonDocument
                {
                    { "$regex", command.Mensagem.RemoverCaracteresEspeciais().ToLower() }
                };
            
                filtro.Add("dsc_mensagemnormalizada", regex);
            }
        
            if (command.Status is not null)
            {
                var status = (int)command.Status;
                filtro.Add("cod_status", status);
            }

            return filtro;
        }
    }
    
}