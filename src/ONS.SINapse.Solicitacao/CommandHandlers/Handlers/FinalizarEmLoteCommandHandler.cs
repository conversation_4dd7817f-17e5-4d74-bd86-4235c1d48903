using AutoMapper;
using FluentValidation;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Repository.IRepository;
using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Shared.Identity;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Shared.Messages;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using ONS.SINapse.Solicitacao.EventHandlers.Events;

namespace ONS.SINapse.Solicitacao.CommandHandlers.Handlers;

public class FinalizarEmLoteCommandHandler : CommandHandler<FinalizarEmLoteCommand, SolicitacaoFinalizadaEmLoteResultDto>
{
    private readonly ISolicitacaoRepository _solicitacaoRepository;
    private readonly IUserContext _userContext;
    private readonly IMediatorHandler _mediatorHandler;
    private readonly IMapper _mapper;
    private readonly IValidator<FinalizarEmLoteCommand> _validator;

    public FinalizarEmLoteCommandHandler(
        ISolicitacaoRepository solicitacaoRepository,
        IUserContext userContext,
        IMediatorHandler mediatorHandler,
        IMapper mapper,
        IValidator<FinalizarEmLoteCommand> validator)
    {
        _solicitacaoRepository = solicitacaoRepository;
        _userContext = userContext;
        _mediatorHandler = mediatorHandler;
        _mapper = mapper;
        _validator = validator;
    }
    
    protected override async Task<SolicitacaoFinalizadaEmLoteResultDto> Handle(FinalizarEmLoteCommand request, CancellationToken cancellationToken)
    {
        var validation = await _validator.ValidateAsync(request, cancellationToken);
        
        if (!validation.IsValid)
        {
            AdicionarValidation(validation);
            return Result;
        }
        
        var solicitacoes = await _solicitacaoRepository
            .GetAsync(x => request.Solicitacoes.Contains(x.Id), cancellationToken);

        solicitacoes = Validar(request, solicitacoes);
        
        if (solicitacoes.Count == 0) return Result;

        foreach (var solicitacao in solicitacoes)
        {
            solicitacao.Finalizar(request.Usuario!);
            Result.AdicionarResultado(new SolicitacaoFinalizadaResultDto(solicitacao.Id));
        }
        
        await _solicitacaoRepository.BulkUpdateAsync(solicitacoes, cancellationToken);

        var evento = new SolicitacaoFinalizadaEmLoteEvent(_mapper.Map<List<SolicitacaoFinalizadaDto>>(solicitacoes));
        
        await _mediatorHandler.PublicarEventoAsync(evento, cancellationToken);
        
        return Result;
    }
    
    
    private ICollection<Entities.Entities.Solicitacao> Validar(
        FinalizarEmLoteCommand command, 
        ICollection<Entities.Entities.Solicitacao> solicitacoes)
    {
        if (command.FinalizadoAutomaticamente)
            return solicitacoes;
        
        solicitacoes = ValidarSolicitacaoNaoExistente(command, solicitacoes);
        solicitacoes = ValidarSolicitacaoStatusInvalido(solicitacoes);
        solicitacoes = ValidarSolicitacaoSolicitanteInvalido(solicitacoes);

        return solicitacoes;
    }
    
    private ICollection<Entities.Entities.Solicitacao> ValidarSolicitacaoNaoExistente(FinalizarEmLoteCommand command, 
        ICollection<Entities.Entities.Solicitacao> solicitacoes)
    {
        var solicitacoesNaoExistentes = command.Solicitacoes.Except(solicitacoes.Select(x => x.Id)).ToList();

        if (solicitacoesNaoExistentes.Count == 0) return solicitacoes;

        foreach (var solicitacao in solicitacoesNaoExistentes)
        {
            var mensagem =
                $"Não foi possível alterar o status da solicitação {solicitacao} para Cancelada. Solicitação não existe.";
            Result.AdicionarResultadoComErro(solicitacao, mensagem);
        }
        
        return solicitacoes;
    }
    
    private ICollection<Entities.Entities.Solicitacao> ValidarSolicitacaoStatusInvalido(ICollection<Entities.Entities.Solicitacao> solicitacoes)
    {
        if (solicitacoes.Count == 0) return solicitacoes;
        
        var statusInvalidos = solicitacoes
            .Where(x => x.Status != StatusDeSolicitacao.CienciaInformada && x.Status != StatusDeSolicitacao.Confirmada)
            .ToList();

        if (statusInvalidos.Count == 0) return solicitacoes;

        foreach (var solicitacao in statusInvalidos)
        {
            var mensagem = $"Não foi possível alterar o status da solicitação {solicitacao.Id} para finalizada. Solicitação se encontra com status '{solicitacao.Status.GetDescription()}'.";
            Result.AdicionarResultadoComErro(solicitacao.Id, mensagem);
        }

        return solicitacoes.Except(statusInvalidos).ToList();
    }
    
    private ICollection<Entities.Entities.Solicitacao> ValidarSolicitacaoSolicitanteInvalido(
        ICollection<Entities.Entities.Solicitacao> solicitacoes)
    {
        if (solicitacoes.Count == 0) return solicitacoes;
        
        var solicitantesInvalidos =
            solicitacoes
                .Where(x => !x.IsDestinatario(_userContext.Perfil))
                .ToList();
        
        if (solicitantesInvalidos.Count == 0) return solicitacoes;

        foreach (var id in solicitantesInvalidos.Select(solicitacao => solicitacao.Id))
        {
            var mensagem =
                $"Não foi possível alterar o status da solicitação {id} para finalizada. Operação não permitida, usuário é o solicitante.";

            Result.AdicionarResultadoComErro(id, mensagem);
        }
        
        return solicitacoes.Except(solicitantesInvalidos).ToList();
    }
}