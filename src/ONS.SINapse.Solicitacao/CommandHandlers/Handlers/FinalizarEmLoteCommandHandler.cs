using AutoMapper;
using FluentValidation;
using FluentValidation.Results;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Repository.IRepository.InMemory;
using ONS.SINapse.Shared.Bus;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Shared.Messages;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using ONS.SINapse.Solicitacao.Dtos.Integracao;
using ONS.SINapse.Solicitacao.EventHandlers.Events;

namespace ONS.SINapse.Solicitacao.CommandHandlers.Handlers;

public class FinalizarEmLoteCommandHandler : CommandHandler<FinalizarEmLoteCommand, StatusDeSolicitacaoIntegracaoEnvioEmLoteDto>
{
    private readonly ISolicitacaoMemoryRepository _solicitacaoMemoryRepository;
    private readonly IMediatorHandler _mediatorHandler;
    private readonly IValidator<StatusDeSolicitacaoIntegracaoRecebimentoDto> _validator;
    private readonly IMapper _mapper;
    private readonly ISqsBus _bus;

    public FinalizarEmLoteCommandHandler(
        ISolicitacaoMemoryRepository solicitacaoMemoryRepository,
        IMediatorHandler mediatorHandler,
        IValidator<StatusDeSolicitacaoIntegracaoRecebimentoDto> validator,
        IMapper mapper,
        ISqsBus sqsBus)
    {
        _solicitacaoMemoryRepository = solicitacaoMemoryRepository;
        _mediatorHandler = mediatorHandler;
        _validator = validator;
        _mapper = mapper;
        _bus = sqsBus;
    }
    
    protected override async Task<StatusDeSolicitacaoIntegracaoEnvioEmLoteDto> Handle(FinalizarEmLoteCommand request, CancellationToken cancellationToken)
    {
        if (request.Solicitacoes.Count == 0)
            return new StatusDeSolicitacaoIntegracaoEnvioEmLoteDto([]);

        var ids = request.Solicitacoes.Select(status => status.Id).ToArray();
        var solicitacoes = _solicitacaoMemoryRepository.GetList(x => ids.Contains(x.Id)).ToList();

        var solicitacoesInvalidas = Validar(request, solicitacoes);
        
        var solicitacoesValidas = solicitacoes
            .ExceptBy(solicitacoesInvalidas.Select(x => x.Id), solicitacao => solicitacao.Id)
            .ToList();

        if (solicitacoesValidas.Count == 0)
        {
            var statusDeSolicitacaoInvalidaEvent = new StatusDeSolicitacaoIntegracaoRecebidaEvent(solicitacoesInvalidas);
            await _mediatorHandler.PublicarEventoAsync(statusDeSolicitacaoInvalidaEvent, cancellationToken);
            return new StatusDeSolicitacaoIntegracaoEnvioEmLoteDto(solicitacoesInvalidas);
        }
        
        var solicitacoesFinalizadas = ProcessarSolicitacoesValidas(request, solicitacoesValidas);
        
        var validationResult = await EnviarComandoAoFirebase(solicitacoesValidas, cancellationToken);

        return validationResult.IsValid
            ? await PublicarEventosDeSolicitacoesProcessadas(solicitacoesFinalizadas, solicitacoesInvalidas, cancellationToken)
            : await PublicarEventosDeErros(solicitacoesFinalizadas, solicitacoesInvalidas, validationResult, cancellationToken);
    }

    private async Task<ValidationResult> EnviarComandoAoFirebase(
        List<Entities.Entities.Solicitacao> solicitacoesValidas, CancellationToken cancellationToken)
    {
        var solicitacaoFirebase = _mapper.Map<List<StatusDeSolicitacaoFirebaseDto>>(solicitacoesValidas);
        var command = new EnviarTrocaDeStatusAoFirebaseCommand(solicitacaoFirebase);
        return await _mediatorHandler.EnviarComandoAsync(command, cancellationToken);
    }

    private async Task<StatusDeSolicitacaoIntegracaoEnvioEmLoteDto> PublicarEventosDeErros(
        List<StatusDeSolicitacaoIntegracaoEnvioDto> solicitacoesCanceladas,
        List<StatusDeSolicitacaoIntegracaoEnvioDto> solicitacoesInvalidas,
        ValidationResult validationResult,
        CancellationToken cancellationToken)
    {
        solicitacoesCanceladas.ForEach(x => x.AdicionarErros(validationResult.Errors.Select(v => v.ErrorMessage).ToArray()));
        var solicitacoesProcessadas = solicitacoesCanceladas.Concat(solicitacoesInvalidas).ToList();
        var statusDeSolicitacaoIntegracaoRecebidaEvent = new StatusDeSolicitacaoIntegracaoRecebidaEvent(solicitacoesProcessadas);
        await _mediatorHandler.PublicarEventoAsync(statusDeSolicitacaoIntegracaoRecebidaEvent, cancellationToken);
        return new StatusDeSolicitacaoIntegracaoEnvioEmLoteDto(solicitacoesProcessadas);
    }

    private async Task<StatusDeSolicitacaoIntegracaoEnvioEmLoteDto> PublicarEventosDeSolicitacoesProcessadas(
        List<StatusDeSolicitacaoIntegracaoEnvioDto> solicitacoesCanceladas,
        List<StatusDeSolicitacaoIntegracaoEnvioDto> solicitacoesInvalidas,
        CancellationToken cancellationToken)
    {
        var solicitacoesProcessadas = solicitacoesCanceladas.Concat(solicitacoesInvalidas).ToList();
        var statusDeSolicitacaoIntegracaoRecebidaEvent = new StatusDeSolicitacaoIntegracaoRecebidaEvent(solicitacoesProcessadas);
        var enviarKafka = _mediatorHandler.PublicarEventoAsync(statusDeSolicitacaoIntegracaoRecebidaEvent, cancellationToken);

        var eventoSolicitacoesConcluidas = new SolicitacaoConcluidaEvent { Solicitacoes = solicitacoesProcessadas.Select(x => x.Id).ToList() };
        var notificar = _bus.Publish(eventoSolicitacoesConcluidas, cancellationToken);

        await Task.WhenAll(enviarKafka, notificar);

        return new StatusDeSolicitacaoIntegracaoEnvioEmLoteDto(solicitacoesProcessadas);
    }

    private static List<StatusDeSolicitacaoIntegracaoEnvioDto> ProcessarSolicitacoesValidas(
        FinalizarEmLoteCommand request, List<Entities.Entities.Solicitacao> solicitacoesValidas)
    {
        return solicitacoesValidas.Join(request.Solicitacoes,
            inner => inner.Id,
            join => join.Id,
            (inner, join) =>
            {
                if(request.ProcessoAutomatico)
                    inner.FinalizarAutomaticamente(join.Usuario!);
                else
                    inner.Finalizar(join.Usuario!);
                
                return new StatusDeSolicitacaoIntegracaoEnvioDto(
                    inner.Id,
                    inner.Status,
                    join.Usuario!,
                    DateTime.UtcNow,
                    join.Motivo,
                    join.SistemaDeOrigem ?? Entities.Entities.Solicitacao.SistemaDeOrigemInterno,
                    []
                );
            }).ToList();
    }
    
    
    #region Validacoes

    private List<StatusDeSolicitacaoIntegracaoEnvioDto> Validar(FinalizarEmLoteCommand request, List<Entities.Entities.Solicitacao> solicitacoes)
    {
        if (request.ProcessoAutomatico) return [];
        
        var requestInvalidos = ValidarRequest(request);
        var solicitacoesNaoExistem = ValidarSolicitacaoNaoExistente(request, solicitacoes);
        var solicitacoesSolicitanteInvalido = ValidarSolicitacaoSolicitanteInvalido(request, solicitacoes);
        var solicitacoesStatusInvalido = ValidarSolicitacaoStatusInvalido(request, solicitacoes);
        return 
            Enumerable.Empty<StatusDeSolicitacaoIntegracaoEnvioDto>()
                .Concat(requestInvalidos)
                .Concat(solicitacoesNaoExistem)
                .Concat(solicitacoesSolicitanteInvalido)
                .Concat(solicitacoesStatusInvalido)
                .GroupBy(group => new
                    {
                        group.Id,
                        group.Usuario,
                        group.Justificativa,
                        group.SistemaDeOrigem
                    },
                    by => by.Erros,
                    (group, by) => new StatusDeSolicitacaoIntegracaoEnvioDto(
                        group.Id,
                        StatusDeSolicitacao.Erro,
                        group.Usuario,
                        DateTime.UtcNow, 
                        group.Justificativa,
                        group.SistemaDeOrigem,
                        by.SelectMany(x => x).ToArray()
                    ))
                .ToList();
    }

    private static List<StatusDeSolicitacaoIntegracaoEnvioDto> ValidarSolicitacaoNaoExistente(FinalizarEmLoteCommand request, List<Entities.Entities.Solicitacao> solicitacoes)
    {
        return request.Solicitacoes
            .ExceptBy(solicitacoes.Select(x => x.Id), dto => dto.Id)
            .Select(erro => new StatusDeSolicitacaoIntegracaoEnvioDto(
                erro.Id ?? string.Empty,
                StatusDeSolicitacao.Erro,
                erro.Usuario ?? new UsuarioDto(),
                DateTime.UtcNow,
                erro.Motivo,
                erro.SistemaDeOrigem ?? Entities.Entities.Solicitacao.SistemaDeOrigemInterno,
                [$"Não foi possível alterar o status da solicitação {erro.Id} para {StatusDeSolicitacao.Finalizada.GetDescription()}. Solicitação não existe."]
            ))
            .ToList();
    }

    private List<StatusDeSolicitacaoIntegracaoEnvioDto> ValidarRequest(FinalizarEmLoteCommand request)
    {
        return
            request.Solicitacoes
                .Select(solicitacao => (Validacao: _validator.Validate(solicitacao), Solicitacao: solicitacao))
                .Where(x => !x.Validacao.IsValid)
                .Select(erro => new StatusDeSolicitacaoIntegracaoEnvioDto(
                    erro.Solicitacao.Id ?? string.Empty,
                    StatusDeSolicitacao.Erro,
                    erro.Solicitacao.Usuario ?? new UsuarioDto(),
                    DateTime.UtcNow,
                    erro.Solicitacao.Motivo,
                    erro.Solicitacao.SistemaDeOrigem ?? Entities.Entities.Solicitacao.SistemaDeOrigemInterno,
                    erro.Validacao.Errors.Select(x => x.ErrorMessage).ToArray()
                ))
                .ToList();
    }

    private static List<StatusDeSolicitacaoIntegracaoEnvioDto> ValidarSolicitacaoStatusInvalido(FinalizarEmLoteCommand request, List<Entities.Entities.Solicitacao> solicitacoes)
    {
        if (solicitacoes.Count == 0) return [];
        
        return solicitacoes
            .Join(request.Solicitacoes, 
                a => a.Id, 
                b => b.Id, 
                (a, b) => new { a, b })
            .Where(t => t.a.Status != StatusDeSolicitacao.CienciaInformada && t.a.Status != StatusDeSolicitacao.Confirmada)
            .Select(t => new StatusDeSolicitacaoIntegracaoEnvioDto(
                t.b.Id ?? string.Empty,
                StatusDeSolicitacao.Erro,
                t.b.Usuario ?? new UsuarioDto(),
                DateTime.UtcNow,
                t.b.Motivo,
                t.b.SistemaDeOrigem ?? Entities.Entities.Solicitacao.SistemaDeOrigemInterno,
                [$"Não foi possível alterar o status da solicitação {t.b.Id} para {StatusDeSolicitacao.Finalizada.GetDescription()}. Solicitação se encontra com status {t.a.Status.GetDescription()}."]))
            .ToList();
    }

    private static List<StatusDeSolicitacaoIntegracaoEnvioDto> ValidarSolicitacaoSolicitanteInvalido(FinalizarEmLoteCommand request, List<Entities.Entities.Solicitacao> solicitacoes)
    {
        if (solicitacoes.Count == 0) return [];
        
        return solicitacoes
            .Join(request.Solicitacoes, 
                a => a.Id, 
                b => b.Id, 
                (a, b) => new { a, b })
            .Where(t => !t.a.IsDestinatario([t.b.Centro?.Codigo ?? string.Empty]))
            .Select(t => new StatusDeSolicitacaoIntegracaoEnvioDto(
                t.b.Id ?? string.Empty,
                StatusDeSolicitacao.Erro,
                t.b.Usuario ?? new UsuarioDto(),
                DateTime.UtcNow,
                t.b.Motivo,
                t.b.SistemaDeOrigem ?? Entities.Entities.Solicitacao.SistemaDeOrigemInterno,
                [$"Não foi possível alterar o status da solicitação {t.b.Id} para {StatusDeSolicitacao.Finalizada.GetDescription()}. Operação não permitida, usuário é o solicitante."]))
            .ToList();
    }

    #endregion
}