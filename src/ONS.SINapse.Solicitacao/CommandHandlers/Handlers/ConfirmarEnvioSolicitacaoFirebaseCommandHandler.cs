using AutoMapper;
using FluentValidation;
using ONS.SINapse.Entities.Entities;
using ONS.SINapse.Repository.IRepository.Firebase;
using ONS.SINapse.Shared.Identity;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands.Firebase;
using ONS.SINapse.Solicitacao.Dtos.Integracao;
using ONS.SINapse.Solicitacao.Factories;

namespace ONS.SINapse.Solicitacao.CommandHandlers.Handlers;

public class ConfirmarEnvioSolicitacaoFirebaseCommandHandler : CadastrarSolicitacaoEmLoteCommandHandlerBase<ConfirmarEnvioSolicitacaoFirebaseCommand>
{
    private readonly ISolicitacaoFirebaseCommandFactory _solicitacaoFirebaseCommandFactory;
    private readonly IUserContext _userContext;

    public ConfirmarEnvioSolicitacaoFirebaseCommandHandler(
        ISolicitacaoFirebaseRepository solicitacaoFirebaseRepository, 
        IMapper mapper, IMediator<PERSON><PERSON>ler mediatorHandler, 
        IValidator<CadastroSolicitacaoDto> cadastroSolicitacaoDtoValidator,
        ISolicitacaoFirebaseCommandFactory solicitacaoFirebaseCommandFactory,
        IUserContext userContext
    ) : base(solicitacaoFirebaseRepository, mapper, mediatorHandler, cadastroSolicitacaoDtoValidator)
    {
        _solicitacaoFirebaseCommandFactory = solicitacaoFirebaseCommandFactory;
        _userContext = userContext;
    }

    protected override async Task<StatusDeSolicitacaoIntegracaoEnvioEmLoteDto> Handle(ConfirmarEnvioSolicitacaoFirebaseCommand request, CancellationToken cancellationToken)
    {
        if (request.Solicitacoes.Count == 0)
            return new StatusDeSolicitacaoIntegracaoEnvioEmLoteDto([]);
        var usuario = new Usuario(_userContext.Sid, _userContext.Login, _userContext.Nome);
        var solicitacoesInvalidas = Validar(request, cancellationToken).ToList();
        
        var solicitacoesValidas = request.Solicitacoes
            .ExceptBy(solicitacoesInvalidas.Select(x => x.Id), solicitacao => solicitacao.Id)
            .ToList();
        var idOrigem = solicitacoesValidas
            .Where(x => !string.IsNullOrWhiteSpace(x.SolicitacaoDeOrigem))
            .Select(x => x.SolicitacaoDeOrigem!).ToArray();
        
        var solicitacoesParaConfirmarEnvio = await SolicitacaoFirebaseRepository.GetByIdAsync(idOrigem, cancellationToken);
        
        solicitacoesParaConfirmarEnvio.ForEach(x =>
        {
            x.ConfirmarEnvio(usuario);
        });

        if (solicitacoesValidas.Count == 0) 
            return await PublicarEventoDeSolicitacoesInvalidas(solicitacoesInvalidas, cancellationToken);
        
        var solicitacoes = Mapper.Map<List<Entities.Entities.Solicitacao>>(solicitacoesValidas);
        solicitacoes.AddRange(solicitacoesParaConfirmarEnvio);
        var firebaseCommand = _solicitacaoFirebaseCommandFactory.ObterCommand<ConfirmarEnvioSolicitacaoNoFirebaseCommand>(solicitacoes);
        var validation = await MediatorHandler.EnviarComandoAsync(firebaseCommand, cancellationToken);

        return validation.IsValid
            ? await PublicarEventosDeSolicitacoesProcessadasAsync(solicitacoesInvalidas, solicitacoes, cancellationToken)
            : await PublicarEventosDeErrosAsync(solicitacoesValidas, solicitacoesInvalidas, validation, cancellationToken);
    }
}