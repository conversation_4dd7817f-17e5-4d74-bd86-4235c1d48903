using FluentValidation;
using FluentValidation.Results;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Repository.IRepository.Firebase;
using ONS.SINapse.Shared.Bus;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Shared.Messages;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands.Firebase;
using ONS.SINapse.Solicitacao.Dtos.Integracao;
using ONS.SINapse.Solicitacao.EventHandlers.Events;
using ONS.SINapse.Solicitacao.Factories;
using ONS.SINapse.Solicitacao.Helpers;

namespace ONS.SINapse.Solicitacao.CommandHandlers.Handlers;

public sealed class CancelarEnvioEmLoteCommandHandler : CommandHandler<CancelarEnvioEmLoteCommand, StatusDeSolicitacaoIntegracaoEnvioEmLoteDto>
{
    private readonly IValidator<StatusDeSolicitacaoIntegracaoRecebimentoDto> _validator;
    private readonly ISolicitacaoFirebaseRepository _solicitacaoFirebaseRepository;
    private readonly ISolicitacaoFirebaseCommandFactory _solicitacaoFirebaseCommandFactory;
    private readonly IMediatorHandler _mediatorHandler;
    private readonly ISqsBus _bus;

    public CancelarEnvioEmLoteCommandHandler(IValidator<StatusDeSolicitacaoIntegracaoRecebimentoDto> validator,
        ISolicitacaoFirebaseRepository solicitacaoFirebaseRepository,
        ISolicitacaoFirebaseCommandFactory solicitacaoFirebaseCommandFactory,
        IMediatorHandler mediatorHandler,
        ISqsBus bus)
    {
        _validator = validator;
        _solicitacaoFirebaseRepository = solicitacaoFirebaseRepository;
        _solicitacaoFirebaseCommandFactory = solicitacaoFirebaseCommandFactory;
        _mediatorHandler = mediatorHandler;
        _bus = bus;
    }

    protected override async Task<StatusDeSolicitacaoIntegracaoEnvioEmLoteDto> Handle(CancelarEnvioEmLoteCommand request, CancellationToken cancellationToken)
    {
        if(request.Solicitacoes.Count == 0)
            return new StatusDeSolicitacaoIntegracaoEnvioEmLoteDto([]);

        var ids = request.Solicitacoes.Select(s => s.Id!).ToArray();
        var solicitacoes = await _solicitacaoFirebaseRepository.GetByIdAsync(ids, cancellationToken);

        var solicitacoesInvalidas = Validar(request, solicitacoes);

        var solicitacoesValidas = solicitacoes
            .ExceptBy(solicitacoesInvalidas.Select(x => x.Id), solicitacao => solicitacao.Id)
            .ToList();

        if (solicitacoesValidas.Count == 0)
        {
            var statusDeSolicitacaoInvalidaEvent = new StatusDeSolicitacaoIntegracaoRecebidaEvent(solicitacoesInvalidas);
            await _mediatorHandler.PublicarEventoAsync(statusDeSolicitacaoInvalidaEvent, cancellationToken);
            return new StatusDeSolicitacaoIntegracaoEnvioEmLoteDto(solicitacoesInvalidas);
        }

        var solicitacoesEnvioCancelado = solicitacoesValidas
            .Join(request.Solicitacoes,
                inner => inner.Id,
                join => join.Id,
                (inner, join) =>
                {
                    inner.CancelarEnvio(join.Usuario!);
                    return new StatusDeSolicitacaoIntegracaoEnvioDto(
                        inner.Id,
                        inner.Status,
                        join.Usuario!,
                        DateTime.UtcNow,
                        join.Motivo,
                        join.SistemaDeOrigem ?? Entities.Entities.Solicitacao.SistemaDeOrigemInterno,
                        []
                    );
                })
            .ToList();

        var validationResult = await EnviarComandoAoFirebase(solicitacoesValidas, cancellationToken);

        return validationResult.IsValid
            ? await PublicarEventosDeSolicitacoesProcessadas(solicitacoesEnvioCancelado, solicitacoesInvalidas, cancellationToken)
            : await PublicarEventosDeErros(solicitacoesEnvioCancelado, solicitacoesInvalidas, validationResult, cancellationToken);
    }

    private async Task<ValidationResult> EnviarComandoAoFirebase(
        List<Entities.Entities.Solicitacao> solicitacoesValidas, CancellationToken cancellationToken)
    {
        var command = _solicitacaoFirebaseCommandFactory.ObterCommand<TrocarDeStatusNoFirebaseCommand>(solicitacoesValidas);
        return await _mediatorHandler.EnviarComandoAsync(command, cancellationToken);
    }

    private async Task<StatusDeSolicitacaoIntegracaoEnvioEmLoteDto> PublicarEventosDeErros(
        List<StatusDeSolicitacaoIntegracaoEnvioDto> solicitacoesCanceladas,
        List<StatusDeSolicitacaoIntegracaoEnvioDto> solicitacoesInvalidas,
        ValidationResult validationResult,
        CancellationToken cancellationToken)
    {
        solicitacoesCanceladas.ForEach(x => x.AdicionarErros(validationResult.Errors.Select(v => v.ErrorMessage).ToArray()));
        var solicitacoesProcessadas = solicitacoesCanceladas.Concat(solicitacoesInvalidas).ToList();
        var statusDeSolicitacaoIntegracaoRecebidaEvent = new StatusDeSolicitacaoIntegracaoRecebidaEvent(solicitacoesProcessadas);
        await _mediatorHandler.PublicarEventoAsync(statusDeSolicitacaoIntegracaoRecebidaEvent, cancellationToken);
        return new StatusDeSolicitacaoIntegracaoEnvioEmLoteDto(solicitacoesProcessadas);
    }

    private async Task<StatusDeSolicitacaoIntegracaoEnvioEmLoteDto> PublicarEventosDeSolicitacoesProcessadas(
        List<StatusDeSolicitacaoIntegracaoEnvioDto> solicitacoesCanceladas,
        List<StatusDeSolicitacaoIntegracaoEnvioDto> solicitacoesInvalidas,
        CancellationToken cancellationToken)
    {
        var solicitacoesProcessadas = solicitacoesCanceladas.Concat(solicitacoesInvalidas).ToList();
        var statusDeSolicitacaoIntegracaoRecebidaEvent = new StatusDeSolicitacaoIntegracaoRecebidaEvent(solicitacoesProcessadas);
        var enviarKafka = _mediatorHandler.PublicarEventoAsync(statusDeSolicitacaoIntegracaoRecebidaEvent, cancellationToken);

        var eventoSolicitacoesConcluidas = new SolicitacaoConcluidaEvent { Solicitacoes = solicitacoesProcessadas.Select(x => x.Id).ToList() };
        var notificar = _bus.Publish(eventoSolicitacoesConcluidas, cancellationToken);

        await Task.WhenAll(enviarKafka, notificar);

        return new StatusDeSolicitacaoIntegracaoEnvioEmLoteDto(solicitacoesProcessadas);
    }

    #region Validacoes

    private List<StatusDeSolicitacaoIntegracaoEnvioDto> Validar(CancelarEnvioEmLoteCommand request, List<Entities.Entities.Solicitacao> solicitacoes)
    {
        var requestInvalidos = ValidarRequest(request);
        var solicitacoesNaoExistem = ValidarSolicitacaoNaoExistente(request, solicitacoes);
        var solicitacoesSolicitanteInvalido = ValidarSolicitacaoSolicitanteInvalido(request, solicitacoes);
        var solicitacoesStatusInvalido = ValidarSolicitacaoStatusInvalido(request, solicitacoes);
    
        return Enumerable.Empty<StatusDeSolicitacaoIntegracaoEnvioDto>()
            .Concat(requestInvalidos)
            .Concat(solicitacoesNaoExistem)
            .Concat(solicitacoesSolicitanteInvalido)
            .Concat(solicitacoesStatusInvalido)
            .GroupBy(group => new
                {
                    group.Id,
                    group.Usuario,
                    group.Justificativa,
                    group.SistemaDeOrigem
                },
                by => by.Erros,
                (group, by) => SolicitacaoHelper.CreateErrorDto(
                    group.Id,
                    group.Usuario,
                    group.Justificativa,
                    group.SistemaDeOrigem,
                    by.SelectMany(x => x).ToArray()
                ))
            .ToList();
    }

    private static List<StatusDeSolicitacaoIntegracaoEnvioDto> ValidarSolicitacaoNaoExistente(CancelarEnvioEmLoteCommand request, List<Entities.Entities.Solicitacao> solicitacoes)
    {
        return request.Solicitacoes
            .ExceptBy(solicitacoes.Select(x => x.Id), dto => dto.Id)
            .Select(erro => SolicitacaoHelper.CreateErrorDto(
                erro,
                $"Não foi possível alterar o status da solicitação {erro.Id} para Envio Cancelado. Solicitação não existe."
            ))
            .ToList();
    }

    private List<StatusDeSolicitacaoIntegracaoEnvioDto> ValidarRequest(CancelarEnvioEmLoteCommand request)
    {
        return request.Solicitacoes
            .Select(solicitacao => (Validacao: _validator.Validate(solicitacao), Solicitacao: solicitacao))
            .Where(x => !x.Validacao.IsValid)
            .Select(erro => SolicitacaoHelper.CreateErrorDto(
                erro.Solicitacao,
                erro.Validacao.Errors.Select(x => x.ErrorMessage).ToArray()
            )).ToList();
    }

    private static List<StatusDeSolicitacaoIntegracaoEnvioDto> ValidarSolicitacaoStatusInvalido(CancelarEnvioEmLoteCommand request, List<Entities.Entities.Solicitacao> solicitacoes)
    {
        if (solicitacoes.Count == 0) return [];
    
        return solicitacoes
            .Join(request.Solicitacoes, 
                a => a.Id, 
                b => b.Id, 
                (a, b) => new { a, b })
            .Where(t => t.a.Status != StatusDeSolicitacao.AguardandoEnvio)
            .Select(t => SolicitacaoHelper.CreateErrorDto( // <-- Refactored
                t.b.Id ?? string.Empty,
                t.b.Usuario ?? new UsuarioDto(),
                t.b.Motivo,
                t.b.SistemaDeOrigem ?? Entities.Entities.Solicitacao.SistemaDeOrigemInterno,
                [$"Não foi possível alterar o status da solicitação {t.b.Id} para {StatusDeSolicitacao.EnvioCancelado.GetDescription()}. Solicitação se encontra com status {t.a.Status.GetDescription()}."]))
            .ToList();
    }

    private static List<StatusDeSolicitacaoIntegracaoEnvioDto> ValidarSolicitacaoSolicitanteInvalido(CancelarEnvioEmLoteCommand request, List<Entities.Entities.Solicitacao> solicitacoes)
    {
        if (solicitacoes.Count == 0) return [];
    
        return solicitacoes
            .Join(request.Solicitacoes, 
                a => a.Id, 
                b => b.Id, 
                (a, b) => new { a, b })
            .Where(t => !t.a.IsSolicitante([t.b.Centro?.Codigo ?? string.Empty]))
            .Select(t => SolicitacaoHelper.CreateErrorDto(
                t.b.Id ?? string.Empty,
                t.b.Usuario ?? new UsuarioDto(),
                t.b.Motivo,
                t.b.SistemaDeOrigem ?? Entities.Entities.Solicitacao.SistemaDeOrigemInterno,
                [$"Não foi possível alterar o status da solicitação {t.b.Id} para Envio Cancelado. Operação não permitida, usuário não é o solicitante."]))
            .ToList();
    }

    #endregion
    
}