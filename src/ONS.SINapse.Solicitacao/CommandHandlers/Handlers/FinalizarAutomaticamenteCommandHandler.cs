using FluentValidation.Results;
using Microsoft.Extensions.Options;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Repository.IRepository.Firebase;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Shared.Messages;
using ONS.SINapse.Shared.Settings;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using ONS.SINapse.Solicitacao.Dtos.Integracao;
using ONS.SINapse.Solicitacao.EventHandlers.Events;

namespace ONS.SINapse.Solicitacao.CommandHandlers.Handlers;

public class FinalizarAutomaticamenteCommandHandler : CommandHandler<FinalizarAutomaticamenteCommand>
{
    private readonly ISolicitacaoFirebaseRepository _solicitacaoFirebaseRepository;
    private readonly IMediatorHandler _mediatorHandler;
    private readonly FinalizacaoAutomaticaDeSolicitacaoSettings _finalizacaoSettings;
    private readonly UsuarioDoSistemaSettings _usuarioDoSistemaSettings;

    public FinalizarAutomaticamenteCommandHandler(
        ISolicitacaoFirebaseRepository solicitacaoFirebaseRepository,
        IOptions<FinalizacaoAutomaticaDeSolicitacaoSettings> finalizacaoAutomaticaDeSolicitacaoSettings,
        IOptions<UsuarioDoSistemaSettings> usuarioDoSistemaSettings,
        IMediatorHandler mediatorHandler)
    {
        _usuarioDoSistemaSettings = usuarioDoSistemaSettings.Value;
        _solicitacaoFirebaseRepository = solicitacaoFirebaseRepository;
        _mediatorHandler = mediatorHandler;
        _finalizacaoSettings = finalizacaoAutomaticaDeSolicitacaoSettings.Value;
    }
    
    protected override async Task<ValidationResult> Handle(FinalizarAutomaticamenteCommand request, CancellationToken cancellationToken)
    {
        try
        {
            var minutos = request.IgnorarValidacaoTempo ? 0 : _finalizacaoSettings.MinutosAntesDeFinalizar;
            var dados = await SolicitacoesParaFinalizar(minutos, cancellationToken);

            if (dados.Length == 0)
            {
                await _mediatorHandler.PublicarEventoAsync(new SolicitacaoFinalizadaAutomaticamenteEvent(ValidationResult), cancellationToken);
                return ValidationResult;
            }
            
            var command = new FinalizarEmLoteCommand(dados.Select(x => new StatusDeSolicitacaoIntegracaoRecebimentoDto(
                x,
                StatusDeSolicitacao.Finalizada,
                null,
                null,
                UsuarioDoSistema(),
                Entities.Entities.Solicitacao.SistemaDeOrigemInterno
            )).ToList());
            
            command.FinalizadoProcessoAutomatico();
        
            var result = await _mediatorHandler.EnviarComandoAsync<FinalizarEmLoteCommand, StatusDeSolicitacaoIntegracaoEnvioEmLoteDto>(command, cancellationToken);

            var failures = result.Solicitacoes.SelectMany(x => x.Erros)
                .Select(errorMessage => new ValidationFailure("Finalizar", errorMessage))
                .ToList();
            
            if(failures.Count != 0)
                ValidationResult.AdicionarErro(new ValidationResult(failures));
        }
        catch (Exception ex)
        {
            ValidationResult.AdicionarErro(ex, "Erro ao finalizar automaticamente.");
        }
        
        await _mediatorHandler.PublicarEventoAsync(new SolicitacaoFinalizadaAutomaticamenteEvent(ValidationResult), cancellationToken);
        
        return ValidationResult;
    }

    private async Task<string[]> SolicitacoesParaFinalizar(int minutosAntesDeFinalizar, CancellationToken cancellationToken)
    {
        var atualizadoAntesDe = DateTime.UtcNow.AddMinutes(-minutosAntesDeFinalizar);
        return (await _solicitacaoFirebaseRepository.GetSolicitacoesParaFinalizarAsync(atualizadoAntesDe, cancellationToken))
            .Select(s => s.Id).ToArray();
    }
    
    private UsuarioDto UsuarioDoSistema()
        => new()
        {
            Login = _usuarioDoSistemaSettings.Login,
            Nome = _usuarioDoSistemaSettings.Nome,
            Sid = _usuarioDoSistemaSettings.Sid
        };
    
}