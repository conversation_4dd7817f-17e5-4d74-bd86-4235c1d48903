using FluentValidation.Results;
using Microsoft.Extensions.Options;
using MongoDB.Driver.Linq;
using ONS.SINapse.Repository.Imp.Specifications;
using ONS.SINapse.Repository.IRepository;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Shared.Messages;
using ONS.SINapse.Shared.Settings;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using ONS.SINapse.Solicitacao.EventHandlers.Events;

namespace ONS.SINapse.Solicitacao.CommandHandlers.Handlers;

public class FinalizarAutomaticamenteCommandHandler : CommandHandler<FinalizarAutomaticamenteCommand>
{
    private readonly ISolicitacaoRepository _solicitacaoRepository;
    private readonly IMediatorHandler _mediatorHandler;
    private readonly FinalizacaoAutomaticaDeSolicitacaoSettings _finalizacaoAutomaticaDeSolicitacaoSettings;
    private readonly UsuarioDoSistemaSettings _usuarioDoSistemaSettings;

    public FinalizarAutomaticamenteCommandHandler(
        ISolicitacaoRepository solicitacaoRepository,
        IOptions<FinalizacaoAutomaticaDeSolicitacaoSettings> finalizacaoAutomaticaDeSolicitacaoSettings,
        IOptions<UsuarioDoSistemaSettings> usuarioDoSistemaSettings,
        IMediatorHandler mediatorHandler)
    {
        _solicitacaoRepository = solicitacaoRepository;
        _usuarioDoSistemaSettings = usuarioDoSistemaSettings.Value;
        _mediatorHandler = mediatorHandler;
        _finalizacaoAutomaticaDeSolicitacaoSettings = finalizacaoAutomaticaDeSolicitacaoSettings.Value;
    }
    
    protected override async Task<ValidationResult> Handle(FinalizarAutomaticamenteCommand request, CancellationToken cancellationToken)
    {
        try
        {
            var dados = (await SolicitacoesParaFinalizarAsync(cancellationToken)).ToArray();

            if (dados.Length == 0)
            {
                await _mediatorHandler.PublicarEventoAsync(new SolicitacaoFinalizadaAutomaticamenteEvent(ValidationResult), cancellationToken);
                return ValidationResult;
            }
        
            var command = new FinalizarEmLoteCommand(dados);
            command.FinalizadoProcessoAutomatico();
            command.DefinirUsuario(UsuarioDoSistema());
        
            var result = await _mediatorHandler.EnviarComandoAsync<FinalizarEmLoteCommand, SolicitacaoFinalizadaEmLoteResultDto>(command, cancellationToken);

            ValidationResult.AdicionarErro(result.ValidationResult);
        }
        catch (Exception ex)
        {
            ValidationResult.AdicionarErro(ex, "Erro ao finalizar automaticamente.");
        }
        
        await _mediatorHandler.PublicarEventoAsync(new SolicitacaoFinalizadaAutomaticamenteEvent(ValidationResult), cancellationToken);
        
        return ValidationResult;
    }

    private async Task<IEnumerable<string>> SolicitacoesParaFinalizarAsync(CancellationToken cancellationToken)
    {
        var minutosAntesDeFinalizar = _finalizacaoAutomaticaDeSolicitacaoSettings.MinutosAntesDeFinalizar;
        var expression = SolicitacoesParaFinalizarAutomaticamenteSpecification.New(minutosAntesDeFinalizar).Criteria();
        
        var dados = await _solicitacaoRepository
            .Queryable()
            .Where(expression)
            .Select(x => x.Id)
            .ToListAsync(cancellationToken);
        
        return dados;
    }
    
    private UsuarioDto UsuarioDoSistema()
        => new()
        {
            Login = _usuarioDoSistemaSettings.Login,
            Nome = _usuarioDoSistemaSettings.Nome,
            Sid = _usuarioDoSistemaSettings.Sid
        };
    
}