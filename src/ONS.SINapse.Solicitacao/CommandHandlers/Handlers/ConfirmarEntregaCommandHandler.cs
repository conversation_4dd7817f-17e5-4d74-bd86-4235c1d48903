using ONS.SINapse.Repository.IRepository.Firebase;
using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Shared.Identity;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Shared.Messages;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands.Firebase;
using ONS.SINapse.Solicitacao.Factories;

namespace ONS.SINapse.Solicitacao.CommandHandlers.Handlers;

public class ConfirmarEntregaCommandHandler : CommandHandler<ConfirmarEntregaCommand, ResultadoConfirmacaoDeEntregaDto>
{
    private readonly ISolicitacaoFirebaseRepository _solicitacaoFirebaseRepository;
    private readonly IMediatorHandler _mediatorHandler;
    private readonly IUserContext _userContext;
    private readonly ISolicitacaoFirebaseCommandFactory _solicitacaoFirebaseCommandFactory;
    private const string SolicitacoesNaoInformadas = "Solicitações não informadas.";

    public ConfirmarEntregaCommandHandler(
        ISolicitacaoFirebaseRepository solicitacaoFirebaseRepository,
        IMediatorHandler mediatorHandler,
        IUserContext userContext,
        ISolicitacaoFirebaseCommandFactory solicitacaoFirebaseCommandFactory)
    {
        _solicitacaoFirebaseRepository = solicitacaoFirebaseRepository;
        _mediatorHandler = mediatorHandler;
        _userContext = userContext;
        _solicitacaoFirebaseCommandFactory = solicitacaoFirebaseCommandFactory;
    }

    protected override async Task<ResultadoConfirmacaoDeEntregaDto> Handle(ConfirmarEntregaCommand request, CancellationToken cancellationToken)
    {
        if (request?.SolicitacoesId is null || request.SolicitacoesId.Count == 0)
        {
            AdicionarErro(SolicitacoesNaoInformadas);
            return Result;
        }

        var usuario = _userContext.Usuario();
        var solicitacoes = await _solicitacaoFirebaseRepository.GetByIdAsync(request.SolicitacoesId.ToArray(), cancellationToken);

        var solicitacoesParaAtualizar = solicitacoes.Select(x =>
        {
            var existeMensagemNaoEntregue = x.ConfirmarEntrega(usuario, _userContext.Perfil);

            if (!existeMensagemNaoEntregue) return null;
            return x;
        }).Where(x => x is not null).ToList();
      
        if (solicitacoesParaAtualizar.Count != 0)
        {
            var firebaseCommand = _solicitacaoFirebaseCommandFactory
                .ObterCommand<EntregaDeSolicitacaoNoFirebaseCommand>(solicitacoesParaAtualizar!);

            var validationResult = await _mediatorHandler.EnviarComandoAsync(firebaseCommand, cancellationToken);
            
            if (!validationResult.IsValid)
            {
                AdicionarValidation(validationResult);
            }
        }

        var solicitacoesEntregues = solicitacoes.Select(s => s.Id).ToList();
        Result.AdicionarResultado(solicitacoesParaAtualizar.Count, solicitacoesEntregues);

        return Result;     
    }
}