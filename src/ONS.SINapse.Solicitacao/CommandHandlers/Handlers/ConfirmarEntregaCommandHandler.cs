using AutoMapper;
using ONS.SINapse.Repository.IRepository;
using ONS.SINapse.Repository.IRepository.InMemory;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Shared.Identity;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Shared.Messages;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;

namespace ONS.SINapse.Solicitacao.CommandHandlers.Handlers;

public class ConfirmarEntregaCommandHandler : CommandHandler<ConfirmarEntregaCommand, ResultadoConfirmacaoDeEntregaDto>
{
    private readonly ISolicitacaoMemoryRepository _solicitacaoMemoryRepository;
    private readonly IMapper _mapper;
    private readonly IMediatorHandler _mediatorHandler;
    private readonly IUserContext _userContext;
    private const string SolicitacoesNaoInformadas = "Solicitações não informadas.";

    public ConfirmarEntregaCommandHandler(
        ISolicitacaoMemoryRepository solicitacaoMemoryRepository,
        IMapper mapper,
        IMediatorHandler mediatorHandler,
        IUserContext userContext)
    {
        _solicitacaoMemoryRepository = solicitacaoMemoryRepository;
        _mapper = mapper;
        _mediatorHandler = mediatorHandler;
        _userContext = userContext;
    }

    protected override async Task<ResultadoConfirmacaoDeEntregaDto> Handle(ConfirmarEntregaCommand request, CancellationToken cancellationToken)
    {
        if (request?.SolicitacoesId is null || request.SolicitacoesId.Count == 0)
        {
            AdicionarErro(SolicitacoesNaoInformadas);
            return Result;
        }

        var usuario = _userContext.Usuario();
        var solicitacoes = _solicitacaoMemoryRepository.Get(x => request.SolicitacoesId.Contains(x.Id)).ToList();

        var solicitacoesParaAtualizar = solicitacoes.Select(x =>
        {
            var existeMensagemNaoEntregue = x.ConfirmarEntrega(usuario, _userContext.Perfil);

            if (!existeMensagemNaoEntregue) return null;
            return x;
        }).Where(x => x is not null).ToList();
      
        if (solicitacoesParaAtualizar.Count != 0)
        {
            var firebaseSolicitacao = _mapper.Map<List<ChatDeSolicitacaoFirebaseDto>>(solicitacoesParaAtualizar);

            var firebaseCommand = new ConfirmarEntregasSolicitacaoNoFirebaseCommand(firebaseSolicitacao);
            await _mediatorHandler.EnviarComandoAsync(firebaseCommand, cancellationToken);
        }

        var solicitacoesEntregues = solicitacoes.Select(s => s.Id).ToList();
        Result.AdicionarResultado(solicitacoesParaAtualizar.Count, solicitacoesEntregues);

        return Result;     
    }
}