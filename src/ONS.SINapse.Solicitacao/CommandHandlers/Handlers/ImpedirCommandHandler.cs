using AutoMapper;
using FluentValidation;
using FluentValidation.Results;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Repository.IRepository.Firebase;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Shared.Messages;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands.Firebase;
using ONS.SINapse.Solicitacao.Dtos.Integracao;
using ONS.SINapse.Solicitacao.EventHandlers.Events;
using ONS.SINapse.Solicitacao.Factories;

namespace ONS.SINapse.Solicitacao.CommandHandlers.Handlers;

public sealed class ImpedirCommandHandler : CommandHandler<ImpedirCommand, StatusDeSolicitacaoIntegracaoEnvioEmLoteDto>
{
    private readonly IValidator<StatusDeSolicitacaoIntegracaoRecebimentoDto> _validator;
    private readonly ISolicitacaoFirebaseRepository _solicitacaoFirebaseRepository;
    private readonly IMapper _mapper;
    private readonly IMediatorHandler _mediatorHandler;
    private readonly ISolicitacaoFirebaseCommandFactory _solicitacaoFirebaseCommandFactory;

    public ImpedirCommandHandler(IValidator<StatusDeSolicitacaoIntegracaoRecebimentoDto> validator,
        ISolicitacaoFirebaseRepository solicitacaoFirebaseRepository,
        IMapper mapper,
        IMediatorHandler mediatorHandler,
        ISolicitacaoFirebaseCommandFactory solicitacaoFirebaseCommandFactory)
    {
        _validator = validator;
        _solicitacaoFirebaseRepository = solicitacaoFirebaseRepository;
        _mapper = mapper;
        _mediatorHandler = mediatorHandler;
        _solicitacaoFirebaseCommandFactory = solicitacaoFirebaseCommandFactory;
    }
    
    protected override async Task<StatusDeSolicitacaoIntegracaoEnvioEmLoteDto> Handle(ImpedirCommand request, CancellationToken cancellationToken)
    {
        if (request.Solicitacoes.Count == 0)
            return new StatusDeSolicitacaoIntegracaoEnvioEmLoteDto([]);

        var ids = request.Solicitacoes.Select(status => status.Id!).ToArray();
        var solicitacoes = await _solicitacaoFirebaseRepository.GetByIdAsync(ids, cancellationToken);

        var solicitacoesInvalidas = Validar(request, solicitacoes);
        
        var solicitacoesValidas = solicitacoes
            .ExceptBy(solicitacoesInvalidas.Select(x => x.Id), solicitacao => solicitacao.Id)
            .ToList();

        if (solicitacoesValidas.Count == 0)
            return await PublicarEventoDeSolicitacoesInvalidas(solicitacoesInvalidas, cancellationToken);

        var solicitacoesImpedidas = ProcessarSolicitacoesValidas(request, solicitacoesValidas);
        var command = _solicitacaoFirebaseCommandFactory.ObterCommand<TrocarDeStatusNoFirebaseCommand>(solicitacoesValidas);
        var validationResult = await _mediatorHandler.EnviarComandoAsync(command, cancellationToken);

        return validationResult.IsValid
            ? await PublicarEventosDeSolicitacoesProcessadas(solicitacoesImpedidas, solicitacoesInvalidas, solicitacoesValidas, cancellationToken)
            : await PublicarEventosDeErros(solicitacoesImpedidas, solicitacoesInvalidas, validationResult, cancellationToken);
    }

    private async Task<StatusDeSolicitacaoIntegracaoEnvioEmLoteDto> PublicarEventoDeSolicitacoesInvalidas(
        List<StatusDeSolicitacaoIntegracaoEnvioDto> solicitacoesInvalidas, CancellationToken cancellationToken)
    {
        var statusDeSolicitacaoInvalidaEvent = new StatusDeSolicitacaoIntegracaoRecebidaEvent(solicitacoesInvalidas);
        await _mediatorHandler.PublicarEventoAsync(statusDeSolicitacaoInvalidaEvent, cancellationToken);
        return new StatusDeSolicitacaoIntegracaoEnvioEmLoteDto(solicitacoesInvalidas);
    }

    private static List<StatusDeSolicitacaoIntegracaoEnvioDto> ProcessarSolicitacoesValidas(
        ImpedirCommand request, List<Entities.Entities.Solicitacao> solicitacoesValidas)
    {
        return solicitacoesValidas.Join(request.Solicitacoes,
            inner => inner.Id,
            join => join.Id,
            (inner, join) =>
            {
                inner.Impedir(join.Motivo!, join.Usuario!);
                return new StatusDeSolicitacaoIntegracaoEnvioDto(
                    inner.Id,
                    inner.Status,
                    join.Usuario!,
                    DateTime.UtcNow,
                    join.Motivo,
                    join.SistemaDeOrigem ?? Entities.Entities.Solicitacao.SistemaDeOrigemInterno,
                    []
                );
            }).ToList();
    }

    private async Task<StatusDeSolicitacaoIntegracaoEnvioEmLoteDto> PublicarEventosDeErros(
        List<StatusDeSolicitacaoIntegracaoEnvioDto> solicitacoesImpedidas,
        List<StatusDeSolicitacaoIntegracaoEnvioDto> solicitacoesInvalidas,
        ValidationResult validationResult,
        CancellationToken cancellationToken)
    {
        solicitacoesImpedidas.ForEach(x => x.AdicionarErros(validationResult.Errors.Select(v => v.ErrorMessage).ToArray()));
        var solicitacoesProcessadas = solicitacoesImpedidas.Concat(solicitacoesInvalidas).ToList();
        var statusDeSolicitacaoIntegracaoRecebidaEvent = new StatusDeSolicitacaoIntegracaoRecebidaEvent(solicitacoesProcessadas);
        await _mediatorHandler.PublicarEventoAsync(statusDeSolicitacaoIntegracaoRecebidaEvent, cancellationToken);
        return new StatusDeSolicitacaoIntegracaoEnvioEmLoteDto(solicitacoesProcessadas);
    }

    private async Task<StatusDeSolicitacaoIntegracaoEnvioEmLoteDto> PublicarEventosDeSolicitacoesProcessadas(
        List<StatusDeSolicitacaoIntegracaoEnvioDto> solicitacoesImpedidas,
        List<StatusDeSolicitacaoIntegracaoEnvioDto> solicitacoesInvalidas,
        List<Entities.Entities.Solicitacao> solicitacoesValidas,
        CancellationToken cancellationToken)
    {
        var solicitacoesProcessadas = solicitacoesImpedidas.Concat(solicitacoesInvalidas).ToList();
        var statusDeSolicitacaoIntegracaoRecebidaEvent = new StatusDeSolicitacaoIntegracaoRecebidaEvent(solicitacoesProcessadas);
        var solicitacoesImpedidaDto = _mapper.Map<List<SolicitacaoImpedidaDto>>(solicitacoesValidas);
        var solicitacaoImpedidaEvent = _mediatorHandler.PublicarEventoAsync(new SolicitacaoImpedidaEvent(solicitacoesImpedidaDto), cancellationToken);
        var trocaDeStatusEvent = _mediatorHandler.PublicarEventoAsync(statusDeSolicitacaoIntegracaoRecebidaEvent, cancellationToken);
        await Task.WhenAll(solicitacaoImpedidaEvent, trocaDeStatusEvent);
        return new StatusDeSolicitacaoIntegracaoEnvioEmLoteDto(solicitacoesProcessadas);
    }
    
    #region Validacoes

    private List<StatusDeSolicitacaoIntegracaoEnvioDto> Validar(ImpedirCommand request, List<Entities.Entities.Solicitacao> solicitacoes)
    {
        var requestInvalidos = ValidarRequest(request);
        var solicitacoesNaoExistem = ValidarSolicitacaoNaoExistente(request, solicitacoes);
        var solicitacoesSolicitanteInvalido = ValidarSolicitacaoSolicitanteInvalido(request, solicitacoes);
        var solicitacoesStatusInvalido = ValidarSolicitacaoStatusInvalido(request, solicitacoes);
        return 
            Enumerable.Empty<StatusDeSolicitacaoIntegracaoEnvioDto>()
                .Concat(requestInvalidos)
                .Concat(solicitacoesNaoExistem)
                .Concat(solicitacoesSolicitanteInvalido)
                .Concat(solicitacoesStatusInvalido)
                .GroupBy(group => new
                    {
                        group.Id,
                        group.Usuario,
                        group.Justificativa,
                        group.SistemaDeOrigem
                    },
                    by => by.Erros,
                    (group, by) => new StatusDeSolicitacaoIntegracaoEnvioDto(
                        group.Id,
                        StatusDeSolicitacao.Erro,
                        group.Usuario,
                        DateTime.UtcNow, 
                        group.Justificativa,
                        group.SistemaDeOrigem,
                        by.SelectMany(x => x).ToArray()
                    ))
                .ToList();
    }

    private static List<StatusDeSolicitacaoIntegracaoEnvioDto> ValidarSolicitacaoNaoExistente(ImpedirCommand request, List<Entities.Entities.Solicitacao> solicitacoes)
    {
        return request.Solicitacoes
            .ExceptBy(solicitacoes.Select(x => x.Id), dto => dto.Id)
            .Select(erro => new StatusDeSolicitacaoIntegracaoEnvioDto(
                erro.Id ?? string.Empty,
                StatusDeSolicitacao.Erro,
                erro.Usuario ?? new UsuarioDto(),
                DateTime.UtcNow,
                erro.Motivo,
                erro.SistemaDeOrigem ?? Entities.Entities.Solicitacao.SistemaDeOrigemInterno,
                [$"Não foi possível alterar o status da solicitação {erro.Id} para {StatusDeSolicitacao.Impedida.GetDescription()}. Solicitação não existe."]
            ))
            .ToList();
    }

    private List<StatusDeSolicitacaoIntegracaoEnvioDto> ValidarRequest(ImpedirCommand request)
    {
        return
            request.Solicitacoes
                .Select(solicitacao => (Validacao: _validator.Validate(solicitacao), Solicitacao: solicitacao))
                .Where(x => !x.Validacao.IsValid)
                .Select(erro => new StatusDeSolicitacaoIntegracaoEnvioDto(
                    erro.Solicitacao.Id ?? string.Empty,
                    StatusDeSolicitacao.Erro,
                    erro.Solicitacao.Usuario ?? new UsuarioDto(),
                    DateTime.UtcNow,
                    erro.Solicitacao.Motivo,
                    erro.Solicitacao.SistemaDeOrigem ?? Entities.Entities.Solicitacao.SistemaDeOrigemInterno,
                    erro.Validacao.Errors.Select(x => x.ErrorMessage).ToArray()
                ))
                .ToList();
    }

    private static List<StatusDeSolicitacaoIntegracaoEnvioDto> ValidarSolicitacaoStatusInvalido(ImpedirCommand request, List<Entities.Entities.Solicitacao> solicitacoes)
    {
        if (solicitacoes.Count == 0) return [];
        
        return solicitacoes
            .Join(request.Solicitacoes, 
                a => a.Id, 
                b => b.Id, 
                (a, b) => new { a, b })
            .Where(t => t.a.Status != StatusDeSolicitacao.Pendente && t.a.Status != StatusDeSolicitacao.Confirmada)
            .Select(t => new StatusDeSolicitacaoIntegracaoEnvioDto(
                t.b.Id ?? string.Empty,
                StatusDeSolicitacao.Erro,
                t.b.Usuario ?? new UsuarioDto(),
                DateTime.UtcNow,
                t.b.Motivo,
                t.b.SistemaDeOrigem ?? Entities.Entities.Solicitacao.SistemaDeOrigemInterno,
                [$"Não foi possível alterar o status da solicitação {t.b.Id} para {StatusDeSolicitacao.Impedida.GetDescription()}. Solicitação se encontra com status {t.a.Status.GetDescription()}."]))
            .ToList();
    }

    private static List<StatusDeSolicitacaoIntegracaoEnvioDto> ValidarSolicitacaoSolicitanteInvalido(ImpedirCommand request, List<Entities.Entities.Solicitacao> solicitacoes)
    {
        if (solicitacoes.Count == 0) return [];
        
        return solicitacoes
            .Join(request.Solicitacoes, 
                a => a.Id, 
                b => b.Id, 
                (a, b) => new { a, b })
            .Where(t => !t.a.IsDestinatario([t.b.Centro?.Codigo ?? string.Empty]))
            .Select(t => new StatusDeSolicitacaoIntegracaoEnvioDto(
                t.b.Id ?? string.Empty,
                StatusDeSolicitacao.Erro,
                t.b.Usuario ?? new UsuarioDto(),
                DateTime.UtcNow,
                t.b.Motivo,
                t.b.SistemaDeOrigem ?? Entities.Entities.Solicitacao.SistemaDeOrigemInterno,
                [$"Não foi possível alterar o status da solicitação {t.b.Id} para {StatusDeSolicitacao.Impedida.GetDescription()}. Operação não permitida, usuário é o solicitante."]))
            .ToList();
    }

    #endregion
    
}