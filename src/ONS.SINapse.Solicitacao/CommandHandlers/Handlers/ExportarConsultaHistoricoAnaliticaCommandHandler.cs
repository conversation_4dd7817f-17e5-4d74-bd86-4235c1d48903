using CsvHelper;
using CsvHelper.Configuration;
using MongoDB.Bson;
using ONS.SINapse.Repository.Imp.Context;
using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Shared.Messages;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using ONS.SINapse.Solicitacao.Dtos;
using ONS.SINapse.Solicitacao.Mapper;
using System.Globalization;
using System.Text;
using ONS.SINapse.Shared.Identity;

namespace ONS.SINapse.Solicitacao.CommandHandlers.Handlers;

public class ExportarConsultaHistoricoAnaliticaCommandHandler : CommandHandler<ExportarConsultaHistoricoAnaliticaCommand, ArquivoResultDto>
{
    private readonly IMongoReadOnlyConnection _mongoReadOnlyConnection;
    private readonly IUserContext _userContext;

    public ExportarConsultaHistoricoAnaliticaCommandHandler(
        IMongoReadOnlyConnection mongoReadOnlyConnection,
        IUserContext userContext)
    {
        _mongoReadOnlyConnection = mongoReadOnlyConnection;
        _userContext = userContext;
    }
    
    protected override async Task<ArquivoResultDto> Handle(ExportarConsultaHistoricoAnaliticaCommand request, CancellationToken cancellationToken)
    {
        var centro = _userContext.Perfil.Centros.Aggregate("", (seed, current) => seed + "_" + current);
        
        var pipe = ExportarConsultaHistoricoPipeline.Pipeline(request, _userContext.Perfil);
        var dados = 
            await _mongoReadOnlyConnection.AggregateAsync<ArquivoConsultaHistoricoAnaliticaDto>(pipe, cancellationToken);
        
        var config = new CsvConfiguration(new CultureInfo("pt-BR"))
        {
            Delimiter = ";",
            BufferSize = 1024,
            Quote = '"',
            ShouldQuote = _ => false
        };
        
        var fileName = $"historico_completo_{DateTime.UtcNow:ddMMyyyyHHmm}_{centro}";

        var stream = new MemoryStream();
        
        await using var writer = new StreamWriter(stream, encoding:Encoding.UTF8, leaveOpen: true);
        await using var csvWriter = new CsvWriter(writer, config);
        
        csvWriter.Context.RegisterClassMap<ArquivoConsultaHistoricoAnaliticoMapper>();
        
        await csvWriter.WriteRecordsAsync(dados, cancellationToken);
        await csvWriter.FlushAsync();
        
        stream.Position = 0;

        var conteudo = stream.ToArray();
        Result = new ArquivoResultDto(conteudo, fileName);
        
        return Result;
    }
    
    private static class ExportarConsultaHistoricoPipeline
    {
        public static BsonDocument[] Pipeline(ExportarConsultaHistoricoAnaliticaCommand command, Perfil perfilDeUsuario)
        {
            var filtro = Filtro(command, perfilDeUsuario);
            return new[]
            {
                new BsonDocument { { "$match", filtro } },

                new BsonDocument("$unwind", "$list_historicostatus"),

                new BsonDocument("$project", new BsonDocument
                {
                    { "Codigo", "$_id" },
                    { "Origem", "$obj_origem.nom_local" },
                    { "Destino", "$obj_destino.nom_local" },
                    { "Motivo", "$dsc_motivo" },
                    { "Mensagem", "$dsc_mensagem" },
                    { "InformacaoAdicional", "$dsc_informacaoadicional" },
                    { "DataDeCriacao", "$din_criacao" },
                    { "UsuarioDeCriacao", "$obj_usuario.nom_usuario" },
                    { "CodigoStatus", "$list_historicostatus.cod_status" },
                    { "DataDeAlteracao", "$list_historicostatus.din_alteracao" },
                    { "UsuarioDeAlteracao", "$list_historicostatus.obj_usuario.nom_usuario" },
                    { "impedimento", "$dsc_detalheimpedimento" },
                    {
                        "mensagem_correspondente", new BsonDocument
                        {
                            {
                                "$arrayElemAt", new BsonArray
                                {
                                    new BsonDocument
                                    {
                                        {
                                            "$filter", new BsonDocument
                                            {
                                                { "input", "$list_chat" },
                                                { "as", "mensagem" },
                                                {
                                                    "cond", new BsonDocument
                                                    {
                                                        {
                                                            "$eq",
                                                            new BsonArray
                                                            {
                                                                "$$mensagem.cod_status",
                                                                "$list_historicostatus.cod_status"
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    },
                                    0
                                }
                            }
                        }
                    }
                }),

                new BsonDocument("$addFields", new BsonDocument
                {
                    {
                        "MotivoImpedimento", new BsonDocument
                        {
                            {
                                "$cond", new BsonDocument
                                {
                                    { "if", new BsonDocument { { "$eq", new BsonArray { "$CodigoStatus", 3 } } } },
                                    { "then", "$impedimento" },
                                    { "else", BsonNull.Value }
                                }
                            }
                        }
                    }
                }),

                new BsonDocument("$project", new BsonDocument
                {
                    { "Codigo", 1 },
                    { "Origem", 1 },
                    { "Destino", 1 },
                    { "Motivo", 1 },
                    { "Mensagem", 1 },
                    { "InformacaoAdicional", 1 },
                    { "DataDeCriacao", 1 },
                    { "UsuarioDeCriacao", 1 },
                    { "CodigoStatus", 1 },
                    { "DataDeAlteracao", 1 },
                    { "UsuarioDeAlteracao", 1 },
                    { "MotivoImpedimento", 1 },
                    { "UsuarioEntrega", "$mensagem_correspondente.obj_usuarioentrega.obj_usuario.nom_usuario" },
                    { "DataDeEntrega", "$mensagem_correspondente.obj_usuarioentrega.din_registro" },
                    { "UsuarioLeitura", "$mensagem_correspondente.obj_usuarioleitura.obj_usuario.nom_usuario" },
                    { "DataDeLeitura", "$mensagem_correspondente.obj_usuarioleitura.din_registro" }
                })
            };
        }

        private static BsonDocument Filtro(ExportarConsultaHistoricoAnaliticaCommand command,
            Perfil perfilDeUsuario)
        {
            var filtro = new BsonDocument();

            var codigosCentro = perfilDeUsuario.Centros;
            
            var centroFilter = new BsonDocument("$in", new BsonArray(codigosCentro));
            
            filtro.Add("$or", new BsonArray
            {
                new BsonDocument("obj_origem.cod_local", centroFilter),
                new BsonDocument("obj_destino.cod_local",centroFilter)
            });
            
            if (!string.IsNullOrEmpty(command.Origem))
                filtro.Add("obj_origem.cod_local", command.Origem);
        
            if (!string.IsNullOrEmpty(command.Destino))
                filtro.Add("obj_destino.cod_local", command.Destino);
        
            if (command.PeriodoInicial.IsValid() || command.PeriodoFinal.IsValid())
            {
                var periodo = new BsonDocument();

                if (command.PeriodoInicial.IsValid())
                    periodo.Add("$gte", command.PeriodoInicial!.Value.GetFirstHour());
            
                if(command.PeriodoFinal.IsValid())
                    periodo.Add("$lt", command.PeriodoFinal!.Value.GetDateTimeLastHour());

                filtro.Add("din_criacao", periodo);
            }
        
            if (!string.IsNullOrEmpty(command.Mensagem))
            {
                var regex = new BsonDocument
                {
                    { "$regex", command.Mensagem.RemoverCaracteresEspeciais().ToLower() }
                };
            
                filtro.Add("dsc_mensagemnormalizada", regex);
            }
        
            if (command.Status is not null)
            {
                var status = (int)command.Status;
                filtro.Add("cod_status", status);
            }

            return filtro;
        }
    }
    
}