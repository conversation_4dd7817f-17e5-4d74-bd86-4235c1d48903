using AutoMapper;
using FluentValidation;
using FluentValidation.Results;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Repository.IRepository.Firebase;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Shared.Messages;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using ONS.SINapse.Solicitacao.Dtos.Integracao;
using ONS.SINapse.Solicitacao.EventHandlers.Events;

namespace ONS.SINapse.Solicitacao.CommandHandlers.Handlers;

public abstract class CadastrarSolicitacaoEmLoteCommandHandlerBase<T> : CommandHandler<T, StatusDeSolicitacaoIntegracaoEnvioEmLoteDto> where T : CadastrarSolicitacaoEmLoteCommandBase
{
    protected readonly ISolicitacaoFirebaseRepository SolicitacaoFirebaseRepository;
    protected IMapper Mapper;
    protected IMediatorHandler MediatorHandler;
    protected IValidator<CadastroSolicitacaoDto> CadastroSolicitacaoDtoValidator;

    protected CadastrarSolicitacaoEmLoteCommandHandlerBase(
        ISolicitacaoFirebaseRepository solicitacaoFirebaseRepository,
        IMapper mapper,
        IMediatorHandler mediatorHandler,
        IValidator<CadastroSolicitacaoDto> cadastroSolicitacaoDtoValidator)
    {
        SolicitacaoFirebaseRepository = solicitacaoFirebaseRepository;
        Mapper = mapper;
        MediatorHandler = mediatorHandler;
        CadastroSolicitacaoDtoValidator = cadastroSolicitacaoDtoValidator;
    }

    protected async Task<StatusDeSolicitacaoIntegracaoEnvioEmLoteDto> PublicarEventosDeErrosAsync(
        List<CadastroSolicitacaoDto> solicitacoesCadastradas,
        List<StatusDeSolicitacaoIntegracaoEnvioDto> solicitacoesInvalidas,
        ValidationResult validationResult,
        CancellationToken cancellationToken)
    {
        var solicitacoesProcessadas = solicitacoesCadastradas
            .Select(x => new StatusDeSolicitacaoIntegracaoEnvioDto(
                x.Id ?? string.Empty,
                StatusDeSolicitacao.Erro,
                x.Usuario ?? new UsuarioDto(),
                DateTime.UtcNow,
                null,
                x.SistemaDeOrigem ?? string.Empty,
                validationResult.Errors.Select(e => e.ErrorMessage).ToArray()
            ))
            .Concat(solicitacoesInvalidas)
            .ToList();
        
        var statusDeSolicitacaoIntegracaoRecebidaEvent = new StatusDeSolicitacaoIntegracaoRecebidaEvent(solicitacoesProcessadas);
        await MediatorHandler.PublicarEventoAsync(statusDeSolicitacaoIntegracaoRecebidaEvent, cancellationToken);

        return new StatusDeSolicitacaoIntegracaoEnvioEmLoteDto(solicitacoesProcessadas);
    }

    protected async Task<StatusDeSolicitacaoIntegracaoEnvioEmLoteDto> PublicarEventosDeSolicitacoesProcessadasAsync(
        List<StatusDeSolicitacaoIntegracaoEnvioDto> solicitacoesInvalidas,
        List<Entities.Entities.Solicitacao> solicitacoesValidas,
        CancellationToken cancellationToken)
    {
        var solicitacoesProcessadas =
            solicitacoesValidas.Select(x => new StatusDeSolicitacaoIntegracaoEnvioDto(
                    x.Id,
                    x.Status,
                    new UsuarioDto(x.UsuarioDeCriacao.Sid, x.UsuarioDeCriacao.Nome, x.UsuarioDeCriacao.Login),
                    x.CreatedAt,
                    null,
                    x.SistemaDeOrigem,
                    []
                ))
                .Concat(solicitacoesInvalidas)
                .ToList();
            
        var statusDeSolicitacaoIntegracaoRecebidaEvent = new StatusDeSolicitacaoIntegracaoRecebidaEvent(solicitacoesProcessadas);
        var trocaDeStatusEvent = MediatorHandler.PublicarEventoAsync(statusDeSolicitacaoIntegracaoRecebidaEvent, cancellationToken);
        
        var solicitacoesCadastradasDto = Mapper.Map<List<SolicitacaoCadastradasEmLoteDto>>(solicitacoesValidas);
        var solicitacoesCadastradasEvent = MediatorHandler.PublicarEventoAsync(new SolicitacoesCadastradasEmLoteEvent(solicitacoesCadastradasDto), cancellationToken);
        
        await Task.WhenAll(solicitacoesCadastradasEvent, trocaDeStatusEvent);
        
        return new StatusDeSolicitacaoIntegracaoEnvioEmLoteDto(solicitacoesProcessadas);
    }

    protected async Task<StatusDeSolicitacaoIntegracaoEnvioEmLoteDto> PublicarEventoDeSolicitacoesInvalidas(
        List<StatusDeSolicitacaoIntegracaoEnvioDto> solicitacoesInvalidas, CancellationToken cancellationToken)
    {
        var statusDeSolicitacaoInvalidaEvent = new StatusDeSolicitacaoIntegracaoRecebidaEvent(solicitacoesInvalidas);
        await MediatorHandler.PublicarEventoAsync(statusDeSolicitacaoInvalidaEvent, cancellationToken);
        return new StatusDeSolicitacaoIntegracaoEnvioEmLoteDto(solicitacoesInvalidas);
    }

    protected IEnumerable<StatusDeSolicitacaoIntegracaoEnvioDto> Validar(CadastrarSolicitacaoEmLoteCommandBase request, CancellationToken cancellationToken)
    {
        var solicitacoesExistentes = ValidarSolicitacaoExistenteAsync(request, cancellationToken).Result;

        var solicitacoesInvalidas = ValidarRequest(request);

        return Enumerable.Empty<StatusDeSolicitacaoIntegracaoEnvioDto>()
            .Concat(solicitacoesExistentes)
            .Concat(solicitacoesInvalidas)
            .GroupBy(group => new
                {
                    group.Id,
                    group.SistemaDeOrigem,
                    group.Usuario
                },
                by => by.Erros,
                (group, by) =>
                    new StatusDeSolicitacaoIntegracaoEnvioDto(
                        group.Id,
                        StatusDeSolicitacao.Erro,
                        group.Usuario,
                        DateTime.UtcNow,
                        null,
                        group.SistemaDeOrigem,
                        by.SelectMany(x => x).ToArray()
                    ));
    }

    private IEnumerable<StatusDeSolicitacaoIntegracaoEnvioDto> ValidarRequest(CadastrarSolicitacaoEmLoteCommandBase request)
    {
        return from cadastroSolicitacaoDto in request.Solicitacoes
            let validation = CadastroSolicitacaoDtoValidator.Validate(cadastroSolicitacaoDto, options =>
            {
                options.IncludeRuleSets(Validations.CadastroSolicitacaoDtoValidator.RuleSets.ObjetoDeManobra);
                options.IncludeRulesNotInRuleSet();
            })
            where !validation.IsValid
            select new StatusDeSolicitacaoIntegracaoEnvioDto(
                cadastroSolicitacaoDto.Id ?? string.Empty,
                StatusDeSolicitacao.Erro,
                cadastroSolicitacaoDto.Usuario ?? new UsuarioDto(),
                DateTime.UtcNow,
                null,
                cadastroSolicitacaoDto.SistemaDeOrigem ?? Entities.Entities.Solicitacao.SistemaDeOrigemInterno,
                validation.Errors.Select(x => x.ErrorMessage).ToArray()
            );
    }

    private async Task<IEnumerable<StatusDeSolicitacaoIntegracaoEnvioDto>> ValidarSolicitacaoExistenteAsync(CadastrarSolicitacaoEmLoteCommandBase request, CancellationToken cancellationToken)
    {
        var idsSolicitacoes = request.Solicitacoes
            .Where(x =>
                !string.IsNullOrWhiteSpace(x.SistemaDeOrigem) &&
                x.SistemaDeOrigem != Entities.Entities.Solicitacao.SistemaDeOrigemInterno)
            .Select(y => y.Id)
            .ToList();

        if (idsSolicitacoes.Count == 0) return [];

        var ids = request.Solicitacoes.Select(status => status.Id!).ToArray();
        var solicitacoes = await SolicitacaoFirebaseRepository.GetByIdAsync(ids, cancellationToken);
        var solicitacoesExistente = solicitacoes.Select(s => s.Id).ToList();

        if (solicitacoesExistente.Count == 0) return [];
        
        return solicitacoesExistente
            .Join(request.Solicitacoes,
                inner => inner,
                join => join.Id,
                (inner, join) =>
                    new StatusDeSolicitacaoIntegracaoEnvioDto(
                        inner,
                        StatusDeSolicitacao.Erro,
                        join.Usuario ?? new UsuarioDto(),
                        DateTime.UtcNow, 
                        null,
                        join.SistemaDeOrigem ?? Entities.Entities.Solicitacao.SistemaDeOrigemInterno,
                        ["Erro ao cadastrar solicitação, solicitação já esta cadastrada na base de dados."]
                    ));
    }
}