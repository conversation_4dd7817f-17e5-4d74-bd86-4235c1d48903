using FluentValidation;
using FluentValidation.Results;
using Microsoft.Extensions.Logging;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Repository.IRepository.Firebase;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Shared.Factories;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Shared.Messages;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using ONS.SINapse.Solicitacao.Dtos.Integracao;
using ONS.SINapse.Solicitacao.Validations;

namespace ONS.SINapse.Solicitacao.CommandHandlers.Handlers;

public class EncaminharSolicitacaoCommandHandler : CommandHandler<EncaminharSolicitacaoCommand>
{
    private readonly ISolicitacaoFirebaseRepository _solicitacaoFirebaseRepository;
    private readonly IMediatorHandler _mediatorHandler;
    private readonly IValidator<EncaminharSolicitacaoCommand> _encaminharSolicitacaoCommandValidator;
    public EncaminharSolicitacaoCommandHandler(
        ISolicitacaoFirebaseRepository solicitacaoFirebaseRepository,
        IMediatorHandler mediatorHandler,
        IValidator<EncaminharSolicitacaoCommand> encaminharSolicitacaoCommandValidator,
        ILogger<EncaminharSolicitacaoCommandHandler> logger)
    {
        _solicitacaoFirebaseRepository = solicitacaoFirebaseRepository;
        _mediatorHandler = mediatorHandler;
        _encaminharSolicitacaoCommandValidator = encaminharSolicitacaoCommandValidator;
    }
    
    protected override async Task<ValidationResult> Handle(EncaminharSolicitacaoCommand request, CancellationToken cancellationToken)
    {
        var validate = await _encaminharSolicitacaoCommandValidator.ValidateAsync(request,
            options =>
            {
                options.IncludeRulesNotInRuleSet();
                options.IncludeRuleSets(EncaminharSolicitacaoCommandValidator.RuleSets
                    .ValidarPreenchimentoIdsSolicitacoes);
            }
            , cancellationToken);

        if (!validate.IsValid) return validate;

        var solicitacoes = await _solicitacaoFirebaseRepository.GetByIdAsync(request.SolicitacoesId!, cancellationToken);

        var solicitacoesJaEncaminhadas = ValidarJaEncaminhadasAsync(solicitacoes);
        
        var solicitacoesValidas =
            solicitacoes
                .ExceptBy(ValidarStatusSolicitacao(solicitacoes).Select(vs => vs.Id), s => s.Id)
                .ExceptBy(ValidarOrigemSolicitacao(solicitacoes).Select(vs => vs.Id), s => s.Id)
                .ExceptBy(solicitacoesJaEncaminhadas.Select(vs => vs.SolicitacaoDeOrigemId), s => s.Id)
                .Where(s => 
                    !s.Origem.Codigo.Equals(s.Destino.Codigo) && 
                    !string.IsNullOrEmpty(s.Local?.Codigo) && !s.Local.Codigo.Equals(s.Destino.Codigo)
                    )
                .ToList();

        if (solicitacoesValidas.Count == 0)
        {
            ValidationResult.AdicionarErro("Não há solicitações válidas para encaminhar.");
            return ValidationResult;
        }
        
        var dtos = solicitacoesValidas
            .Select(s => GerarCadastrarSolicitacaoDto(s, request.InformacaoAdicional, request.Usuario!)).ToList();
        
        var command = new EncaminharSolicitacaoFirebaseCommand(dtos);
        var result = await _mediatorHandler
            .EnviarComandoAsync<EncaminharSolicitacaoFirebaseCommand, StatusDeSolicitacaoIntegracaoEnvioEmLoteDto>(command, cancellationToken);
        
        if (!result.ValidationResult.IsValid)
        {
            ValidationResult.AdicionarErro(result.ValidationResult);
        }
        
        return ValidationResult;
    }

    #region Metodos privados

    private static CadastroSolicitacaoDto GerarCadastrarSolicitacaoDto(Entities.Entities.Solicitacao solicitacao, 
        string? informacaoAdicional,
        UsuarioDto usuarioDto)
    {
        var origem = 
            new ObjetoDeManobraDto(solicitacao.Destino.Codigo, solicitacao.Destino.Nome);

        var destino =
            new ObjetoDeManobraDto(solicitacao.EncaminharPara!.Codigo, solicitacao.EncaminharPara.Nome);

        var local = solicitacao.Local is null
            ? null
            : new ObjetoDeManobraDto(solicitacao.Local.Codigo, solicitacao.Local.Nome);
            
        return new CadastroSolicitacaoDto(
            IdSolicitacaoFactory.GerarNovoId(origem.Codigo, destino.Codigo),
            origem,
            destino,
            informacaoAdicional,
            solicitacao.Mensagem,
            usuarioDto,
            solicitacao.Id
        )
        { 
            Local = local,
            EncaminharPara = null,
            Motivo = solicitacao.Motivo,
            SistemaDeOrigem = Entities.Entities.Solicitacao.SistemaDeOrigemInterno,
            Tags = solicitacao.Tags.ToList()
        };
    }
    
    
    private IEnumerable<Entities.Entities.Solicitacao> ValidarStatusSolicitacao(ICollection<Entities.Entities.Solicitacao> solicitacoes)
    {
        return solicitacoes
            .Where(a => !a.Status.Equals(StatusDeSolicitacao.Pendente))
            .Select(solicitacao =>
            {
                ValidationResult.AdicionarErro($"Solicitação {solicitacao.Id} não pode ser encaminhada pois está {solicitacao.Status.GetDescription()}.");
                return solicitacao;
            });
    }

    private IEnumerable<Entities.Entities.Solicitacao> ValidarOrigemSolicitacao(ICollection<Entities.Entities.Solicitacao> solicitacoes)
    {
        return solicitacoes
            .Where(a => !a.Origem.Codigo.Equals(CentroDeOperacaoDto.CodigoCNOS))
            .Select(solicitacao =>
            {
                ValidationResult.AdicionarErro($"Solicitação {solicitacao.Id} não pode ser encaminhada pois não é do CNOS.");
                return solicitacao;
            });
    }

    private List<Entities.Entities.Solicitacao> ValidarJaEncaminhadasAsync(ICollection<Entities.Entities.Solicitacao> solicitacoes)
    {
        var solicitacoesJaEncaminhadas = solicitacoes.Where(s => s.Encaminhada).ToList();

        foreach (var solicitacoesJaEncaminhada in solicitacoesJaEncaminhadas)
        {
            ValidationResult.AdicionarErro($"Solicitacao {solicitacoesJaEncaminhada.Id} já foi encaminhada.");
        }

        return solicitacoesJaEncaminhadas;
    }
    #endregion
}