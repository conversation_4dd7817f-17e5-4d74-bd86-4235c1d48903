using AutoMapper;
using FluentValidation;
using FluentValidation.Results;
using Microsoft.Extensions.Logging;
using ONS.SINapse.Entities.Entities;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Repository.IRepository.Firebase;
using ONS.SINapse.Repository.IRepository.InMemory;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Shared.Factories;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Shared.Messages;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using ONS.SINapse.Solicitacao.Dtos.Integracao;
using ONS.SINapse.Solicitacao.Validations;
using System.Text;

namespace ONS.SINapse.Solicitacao.CommandHandlers.Handlers;

public class EncaminharSolicitacaoCommandHandler : CommandHandler<EncaminharSolicitacaoCommand>
{
    private readonly ISolicitacaoMemoryRepository _solicitacaoMemoryRepository;
    private readonly ISolicitacaoFirebaseRepository _solicitacaoFirebaseRepository;
    private readonly IMediatorHandler _mediatorHandler;
    private readonly IMapper _mapper;
    private readonly IValidator<EncaminharSolicitacaoCommand> _encaminharSolicitacaoCommandValidator;
    private readonly ILogger<EncaminharSolicitacaoCommandHandler> _logger;

    public EncaminharSolicitacaoCommandHandler(
        ISolicitacaoMemoryRepository solicitacaoMemoryRepository,
        ISolicitacaoFirebaseRepository solicitacaoFirebaseRepository,
        IMediatorHandler mediatorHandler,
        IMapper mapper,
        IValidator<EncaminharSolicitacaoCommand> encaminharSolicitacaoCommandValidator,
        ILogger<EncaminharSolicitacaoCommandHandler> logger)
    {
        _solicitacaoMemoryRepository = solicitacaoMemoryRepository;
        _solicitacaoFirebaseRepository = solicitacaoFirebaseRepository;
        _mediatorHandler = mediatorHandler;
        _mapper = mapper;
        _encaminharSolicitacaoCommandValidator = encaminharSolicitacaoCommandValidator;
        _logger = logger;
    }
    
    protected override async Task<ValidationResult> Handle(EncaminharSolicitacaoCommand request, CancellationToken cancellationToken)
    {
        var validate = await _encaminharSolicitacaoCommandValidator.ValidateAsync(request,
            options =>
            {
                options.IncludeRulesNotInRuleSet();
                options.IncludeRuleSets(EncaminharSolicitacaoCommandValidator.RuleSets
                    .ValidarPreenchimentoIdsSolicitacoes);
            }
            , cancellationToken);

        if (!validate.IsValid) return validate;

        var solicitacoes = _solicitacaoMemoryRepository.Get(x => request.SolicitacoesId!.Contains(x.Id)).ToList();

        var solicitacoesJaEncaminhadas = ValidarJaEncaminhadasAsync(solicitacoes);
        
        var solicitacoesValidas =
            solicitacoes
                .ExceptBy(ValidarStatusSolicitacao(solicitacoes).Select(vs => vs.Id), s => s.Id)
                .ExceptBy(ValidarOrigemSolicitacao(solicitacoes).Select(vs => vs.Id), s => s.Id)
                .ExceptBy(solicitacoesJaEncaminhadas.Select(vs => vs.SolicitacaoDeOrigemId), s => s.Id)
                .Where(s => 
                    !s.Origem.Codigo.Equals(s.Destino.Codigo) && 
                    !string.IsNullOrEmpty(s.Local?.Codigo) && !s.Local.Codigo.Equals(s.Destino.Codigo)
                    )
                .ToList();

        if (solicitacoesValidas.Count == 0)
        {
            ValidationResult.AdicionarErro("Não há solicitações válidas para encaminhar.");
            return ValidationResult;
        }

        var solicitacoesCadastradas = await CadastrarSolicitacoesAsync(
            solicitacoesValidas, 
            request.InformacaoAdicional, 
            request.Usuario!,
            cancellationToken);

        if(solicitacoesCadastradas.Count == 0)
        {
            ValidationResult.AdicionarErro("Não há solicitações válidas para encaminhar.");
            return ValidationResult;
        }
        
        var message = new StringBuilder();

        message.Append($"[ENCAMINHAR SOLICITACAO] - Total de Solicitações encaminhadas: {solicitacoesCadastradas.Count}");
        
        solicitacoesCadastradas.ForEach(x =>
        {
            x.Encaminhar(new Usuario(request.Usuario!.Sid, request.Usuario!.Login, request.Usuario!.Nome));
            message.AppendLine(
                $"[ENCAMINHAR SOLICITACAO] - Solicitação encaminhada: {x.Id}. ID Solicitação de Origem: {x.Origem.Codigo}");
        });

        await EncaminharParaFirebaseAsync(solicitacoesCadastradas, cancellationToken);

        _logger.LogInformation("Message: {Message}", message);
        return ValidationResult;
    }


    #region Private Methods


    private Task EncaminharParaFirebaseAsync(List<Entities.Entities.Solicitacao> solicitacoes,
        CancellationToken cancellationToken)
    {
        var solicitacoesFirebase = _mapper.Map<List<EncaminharSolicitacaoFirebaseDto>>(solicitacoes);
        return _solicitacaoFirebaseRepository.PatchFlattenAsync(solicitacoesFirebase, cancellationToken);
    }
    
    private async Task<List<Entities.Entities.Solicitacao>> CadastrarSolicitacoesAsync(
        List<Entities.Entities.Solicitacao> solicitacoes,
        string? informacaoAdicional,
        UsuarioDto usuarioDto,
        CancellationToken cancellationToken)
    {
        var solicitacoesParaCadastrar = solicitacoes
            .Select(x => (solicitacao: x, solicitacaoCadastro: GerarCadastrarSolicitacaoDto(x, informacaoAdicional, usuarioDto)))
            .ToList();
        
        var command = new CadastrarSolicitacaoEmLoteCommand(solicitacoesParaCadastrar
            .Select(x => x.solicitacaoCadastro)
            .ToList());
        
        var result = await _mediatorHandler
            .EnviarComandoAsync<CadastrarSolicitacaoEmLoteCommand, StatusDeSolicitacaoIntegracaoEnvioEmLoteDto>(command, cancellationToken);

        var message = new StringBuilder();
        
        foreach (var solicitacaoInvalida in result.Solicitacoes.Where(x => !x.EstaValido()))
        {
            message.AppendLine(
                $"[ENCAMINHAR SOLICITACAO] - Erro ao encaminhar solicitação. Codigo: {solicitacaoInvalida.Id} Erros: {solicitacaoInvalida.ErrosFormatados()}");
        }

        _logger.LogInformation("Message: {Message}", message);

        return solicitacoesParaCadastrar
            .Join(result.Solicitacoes,
                inner => inner.solicitacaoCadastro.Id,
                join => join.Id,
                (inner, join) => new { valido = join.EstaValido(), inner.solicitacao })
            .Where(x => x.valido)
            .Select(x => x.solicitacao)
            .ToList();
    }

    private static CadastroSolicitacaoDto GerarCadastrarSolicitacaoDto(Entities.Entities.Solicitacao solicitacao, 
        string? informacaoAdicional,
        UsuarioDto usuarioDto)
    {
        var origem = 
            new ObjetoDeManobraDto(solicitacao.Destino.Codigo, solicitacao.Destino.Nome);

        var destino =
            new ObjetoDeManobraDto(solicitacao.EncaminharPara!.Codigo, solicitacao.EncaminharPara.Nome);

        var local = solicitacao.Local is null
            ? null
            : new ObjetoDeManobraDto(solicitacao.Local.Codigo, solicitacao.Local.Nome);
            
        return new CadastroSolicitacaoDto(
            IdSolicitacaoFactory.GerarNovoId(origem.Codigo, destino.Codigo),
            origem,
            destino,
            informacaoAdicional,
            solicitacao.Mensagem,
            usuarioDto,
            solicitacao.Id
        )
        { 
            Local = local,
            EncaminharPara = null,
            Motivo = solicitacao.Motivo,
            SistemaDeOrigem = Entities.Entities.Solicitacao.SistemaDeOrigemInterno,
            Tags = solicitacao.Tags.ToList()
        };
    }
    
    
    private IEnumerable<Entities.Entities.Solicitacao> ValidarStatusSolicitacao(ICollection<Entities.Entities.Solicitacao> solicitacoes)
    {
        return solicitacoes
            .Where(a => !a.Status.Equals(StatusDeSolicitacao.Pendente))
            .Select(solicitacao =>
            {
                ValidationResult.AdicionarErro($"Solicitação {solicitacao.Id} não pode ser encaminhada pois está {solicitacao.Status.GetDescription()}.");
                return solicitacao;
            });
    }

    private IEnumerable<Entities.Entities.Solicitacao> ValidarOrigemSolicitacao(ICollection<Entities.Entities.Solicitacao> solicitacoes)
    {
        return solicitacoes
            .Where(a => !a.Origem.Codigo.Equals(CentroDeOperacaoDto.CodigoCNOS))
            .Select(solicitacao =>
            {
                ValidationResult.AdicionarErro($"Solicitação {solicitacao.Id} não pode ser encaminhada pois não é do CNOS.");
                return solicitacao;
            });
    }

    private List<Entities.Entities.Solicitacao> ValidarJaEncaminhadasAsync(ICollection<Entities.Entities.Solicitacao> solicitacoes)
    {
        var solicitacoesJaEncaminhadas = solicitacoes.Where(s => s.Encaminhada).ToList();

        foreach (var solicitacoesJaEncaminhada in solicitacoesJaEncaminhadas)
        {
            ValidationResult.AdicionarErro($"Solicitacao {solicitacoesJaEncaminhada.Id} já foi encaminhada.");
        }

        return solicitacoesJaEncaminhadas;
    }
    

    #endregion
    
}