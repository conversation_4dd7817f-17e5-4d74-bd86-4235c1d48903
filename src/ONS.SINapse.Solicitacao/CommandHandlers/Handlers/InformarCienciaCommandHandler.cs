using AutoMapper;
using FluentValidation;
using FluentValidation.Results;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Repository.IRepository;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Shared.Messages;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using ONS.SINapse.Solicitacao.EventHandlers.Events;

namespace ONS.SINapse.Solicitacao.CommandHandlers.Handlers;

public class InformarCienciaCommandHandler : CommandHandler<InformarCienciaCommand, CienciaInformadaResultDto>
{
    private readonly ISolicitacaoRepository _solicitacaoRepository;
    private readonly IValidator<InformarCienciaCommand> _validator;
    private readonly IMapper _mapper;
    private readonly IMediatorHandler _mediatorHandler;

    public InformarCienciaCommandHandler(
        ISolicitacaoRepository solicitacaoRepository,
        IValidator<InformarCienciaCommand> validator,
        IMapper mapper,
        IMediatorHandler mediatorHandler)
    {
        _solicitacaoRepository = solicitacaoRepository;
        _validator = validator;
        _mapper = mapper;
        _mediatorHandler = mediatorHandler;
    }
    
    protected override async Task<CienciaInformadaResultDto> Handle(InformarCienciaCommand request, CancellationToken cancellationToken)
    {
        var validationResult = await _validator.ValidateAsync(request, cancellationToken);

        if (!validationResult.IsValid)
        {
            if (!request.ViaApiIntegracao) return new CienciaInformadaResultDto(validationResult.Errors, request.Codigo);
            
            var eventoErro = CienciaInformadaComErroEvent.CienciaInformadaComErro(request.Codigo, validationResult.ToString(), null, request.Usuario);
            await _mediatorHandler.PublicarEventoAsync(eventoErro, cancellationToken);            
            return new CienciaInformadaResultDto(validationResult.Errors, request.Codigo);
        }
        
        var solicitacao = await _solicitacaoRepository.GetOneAsync(request.Codigo, cancellationToken);

        if (solicitacao is null)
        {
            validationResult.AdicionarErro("Solicitação não existe.");
            
            if (!request.ViaApiIntegracao) return new CienciaInformadaResultDto(validationResult.Errors, request.Codigo);
            
            var eventoErro = CienciaInformadaComErroEvent.CienciaInformadaComErro(request.Codigo, validationResult.ToString(), null, request.Usuario);
            await _mediatorHandler.PublicarEventoAsync(eventoErro, cancellationToken);
            
            return new CienciaInformadaResultDto(validationResult.Errors, request.Codigo);
        }

        validationResult = Validar(request.Centros!.ToArray(), solicitacao);
        
        if (!validationResult.IsValid)
        {
            if (!request.ViaApiIntegracao) return new CienciaInformadaResultDto(validationResult.Errors, solicitacao.Id);
            
            var eventoErro = CienciaInformadaComErroEvent.CienciaInformadaComErro(request.Codigo, validationResult.ToString(), solicitacao.CodigoExterno, request.Usuario);
            await _mediatorHandler.PublicarEventoAsync(eventoErro, cancellationToken);
            
            return new CienciaInformadaResultDto(validationResult.Errors, solicitacao.Id);
        }
        
        solicitacao.InformarCiencia(request.Usuario!);
        await _solicitacaoRepository.AtualizarAsync(solicitacao, cancellationToken);

        var solicitacaoFirebase = _mapper.Map<StatusDeSolicitacaoFirebaseDto>(solicitacao);
        var command = new InformarCienciaSolicitacaoNoFirebaseCommand(solicitacaoFirebase);
        
        validationResult =
            await _mediatorHandler.EnviarComandoAsync(command, cancellationToken);
        
        if (!validationResult.IsValid)
            return new CienciaInformadaResultDto(validationResult.Errors, solicitacao.Id);

        var status = _mapper.Map<CienciaInformadaDto>(solicitacao);
        
        var evento = new CienciaInformadaEvent(status);
        
        await _mediatorHandler.PublicarEventoAsync(evento, cancellationToken);
        
        return new CienciaInformadaResultDto(solicitacao.Id);
    }

    private static ValidationResult Validar(string[] centro, Entities.Entities.Solicitacao solicitacao)
    {
        var validation = new ValidationResult();
        
        if (solicitacao.Status != StatusDeSolicitacao.Impedida)
            validation.AdicionarErro("Solicitação não está impedida.");

        if (!solicitacao.IsSolicitante(centro))
            validation.AdicionarErro("Perfil não é solicitante, operação não permitida.");

        return validation;
    }
}