using AutoMapper;
using FluentValidation;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Repository.IRepository;
using ONS.SINapse.Repository.IRepository.InMemory;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Shared.Messages;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using ONS.SINapse.Solicitacao.Dtos.Integracao;
using ONS.SINapse.Solicitacao.EventHandlers.Events;

namespace ONS.SINapse.Solicitacao.CommandHandlers.Handlers;

public class InformarCienciaCommandHandler : CommandHandler<InformarCienciaCommand, StatusDeSolicitacaoIntegracaoEnvioEmLoteDto>
{
    private readonly ISolicitacaoMemoryRepository _solicitacaoMemoryRepository;
    private readonly IValidator<StatusDeSolicitacaoIntegracaoRecebimentoDto> _validator;
    private readonly IMapper _mapper;
    private readonly IMediatorHandler _mediatorHandler;

    public InformarCienciaCommandHandler(
        ISolicitacaoMemoryRepository solicitacaoMemoryRepository,
        IValidator<StatusDeSolicitacaoIntegracaoRecebimentoDto> validator,
        IMapper mapper,
        IMediatorHandler mediatorHandler)
    {
        _solicitacaoMemoryRepository = solicitacaoMemoryRepository;
        _validator = validator;
        _mapper = mapper;
        _mediatorHandler = mediatorHandler;
    }
    
    protected override async Task<StatusDeSolicitacaoIntegracaoEnvioEmLoteDto> Handle(InformarCienciaCommand request, CancellationToken cancellationToken)
    {
        if(request.Solicitacoes.Count == 0)
            return new StatusDeSolicitacaoIntegracaoEnvioEmLoteDto([]);

        var ids = request.Solicitacoes.Select(status => status.Id).ToArray();
        var solicitacoes = _solicitacaoMemoryRepository.Get(x => ids.Contains(x.Id)).ToList();

        var solicitacoesInvalidas = Validar(request, solicitacoes);

        var solicitacoesValidas = solicitacoes
            .ExceptBy(solicitacoesInvalidas.Select(x => x.Id), solicitacao => solicitacao.Id)
            .ToList();

        if (solicitacoesValidas.Count == 0)
        {
            var statusDeSolicitacaoInvalidaEvent = new StatusDeSolicitacaoIntegracaoRecebidaEvent(solicitacoesInvalidas);
            await _mediatorHandler.PublicarEventoAsync(statusDeSolicitacaoInvalidaEvent, cancellationToken);
            return new StatusDeSolicitacaoIntegracaoEnvioEmLoteDto(solicitacoesInvalidas);
        }

        var solicitacoesCienciaInformada = solicitacoesValidas
            .Join(request.Solicitacoes,
                inner => inner.Id,
                join => join.Id,
                (inner, join) =>
                {
                    inner.InformarCiencia(join.Usuario!);
                    return new StatusDeSolicitacaoIntegracaoEnvioDto(
                        inner.Id,
                        inner.Status,
                        join.Usuario!,
                        DateTime.Now,
                        join.Motivo,
                        join.SistemaDeOrigem ?? Entities.Entities.Solicitacao.SistemaDeOrigemInterno,
                        []
                    );
                })
            .ToList();
        
        var solicitacaoFirebase = _mapper.Map<List<StatusDeSolicitacaoFirebaseDto>>(solicitacoesValidas);
        var command = new EnviarTrocaDeStatusAoFirebaseCommand(solicitacaoFirebase);
        
        var validationResult =
            await _mediatorHandler.EnviarComandoAsync(command, cancellationToken);

        if (!validationResult.IsValid)
        {
            solicitacoesCienciaInformada.ForEach(x =>
            {
               x.AdicionarErros(validationResult.Errors.Select(v => v.ErrorMessage).ToArray());
            });
        }
        
        var solicitacoesProcessadas = solicitacoesCienciaInformada
            .Concat(solicitacoesInvalidas)
            .ToList();
        
        var statusDeSolicitacaoIntegracaoRecebidaEvent = new StatusDeSolicitacaoIntegracaoRecebidaEvent(solicitacoesProcessadas);
        
        await _mediatorHandler.PublicarEventoAsync(statusDeSolicitacaoIntegracaoRecebidaEvent, cancellationToken);
        
        return new StatusDeSolicitacaoIntegracaoEnvioEmLoteDto(solicitacoesProcessadas);
    }
    
    
    #region Validacoes

    private List<StatusDeSolicitacaoIntegracaoEnvioDto> Validar(InformarCienciaCommand request, List<Entities.Entities.Solicitacao> solicitacoes)
    {
        var requestInvalidos = ValidarRequest(request);
        var solicitacoesNaoExistem = ValidarSolicitacaoNaoExistente(request, solicitacoes);
        var solicitacoesSolicitanteInvalido = ValidarSolicitacaoSolicitanteInvalido(request, solicitacoes);
        var solicitacoesStatusInvalido = ValidarSolicitacaoStatusInvalido(request, solicitacoes);
        return 
            Enumerable.Empty<StatusDeSolicitacaoIntegracaoEnvioDto>()
                .Concat(requestInvalidos)
                .Concat(solicitacoesNaoExistem)
                .Concat(solicitacoesSolicitanteInvalido)
                .Concat(solicitacoesStatusInvalido)
                .GroupBy(group => new
                    {
                        group.Id,
                        group.Usuario,
                        group.Justificativa,
                        group.SistemaDeOrigem
                    },
                    by => by.Erros,
                    (group, by) => new StatusDeSolicitacaoIntegracaoEnvioDto(
                        group.Id,
                        StatusDeSolicitacao.Erro,
                        group.Usuario,
                        DateTime.Now, 
                        group.Justificativa,
                        group.SistemaDeOrigem,
                        by.SelectMany(x => x).ToArray()
                    ))
                .ToList();
    }

    private static List<StatusDeSolicitacaoIntegracaoEnvioDto> ValidarSolicitacaoNaoExistente(InformarCienciaCommand request, List<Entities.Entities.Solicitacao> solicitacoes)
    {
        return request.Solicitacoes
            .ExceptBy(solicitacoes.Select(x => x.Id), dto => dto.Id)
            .Select(erro => new StatusDeSolicitacaoIntegracaoEnvioDto(
                erro.Id ?? string.Empty,
                StatusDeSolicitacao.Erro,
                erro.Usuario ?? new UsuarioDto(),
                DateTime.Now,
                erro.Motivo,
                erro.SistemaDeOrigem ?? Entities.Entities.Solicitacao.SistemaDeOrigemInterno,
                [$"Não foi possível alterar o status da solicitação {erro.Id} para {StatusDeSolicitacao.CienciaInformada.GetDescription()}. Solicitação não existe."]
            ))
            .ToList();
    }

    private List<StatusDeSolicitacaoIntegracaoEnvioDto> ValidarRequest(InformarCienciaCommand request)
    {
        return
            request.Solicitacoes
                .Select(solicitacao => (Validacao: _validator.Validate(solicitacao), Solicitacao: solicitacao))
                .Where(x => !x.Validacao.IsValid)
                .Select(erro => new StatusDeSolicitacaoIntegracaoEnvioDto(
                    erro.Solicitacao.Id ?? string.Empty,
                    StatusDeSolicitacao.Erro,
                    erro.Solicitacao.Usuario ?? new UsuarioDto(),
                    DateTime.Now,
                    erro.Solicitacao.Motivo,
                    erro.Solicitacao.SistemaDeOrigem ?? Entities.Entities.Solicitacao.SistemaDeOrigemInterno,
                    erro.Validacao.Errors.Select(x => x.ErrorMessage).ToArray()
                ))
                .ToList();
    }

    private static List<StatusDeSolicitacaoIntegracaoEnvioDto> ValidarSolicitacaoStatusInvalido(InformarCienciaCommand request, List<Entities.Entities.Solicitacao> solicitacoes)
    {
        if (solicitacoes.Count == 0) return [];
        
        return solicitacoes
            .Join(request.Solicitacoes, 
                a => a.Id, 
                b => b.Id, 
                (a, b) => new { a, b })
            .Where(t => t.a.Status != StatusDeSolicitacao.Impedida)
            .Select(t => new StatusDeSolicitacaoIntegracaoEnvioDto(
                t.b.Id ?? string.Empty,
                StatusDeSolicitacao.Erro,
                t.b.Usuario ?? new UsuarioDto(),
                DateTime.Now,
                t.b.Motivo,
                t.b.SistemaDeOrigem ?? Entities.Entities.Solicitacao.SistemaDeOrigemInterno,
                [$"Não foi possível alterar o status da solicitação {t.b.Id} para {StatusDeSolicitacao.CienciaInformada.GetDescription()}. Solicitação se encontra com status {t.a.Status.GetDescription()}."]))
            .ToList();
    }

    private static List<StatusDeSolicitacaoIntegracaoEnvioDto> ValidarSolicitacaoSolicitanteInvalido(InformarCienciaCommand request, List<Entities.Entities.Solicitacao> solicitacoes)
    {
        if (solicitacoes.Count == 0) return [];
        
        return solicitacoes
            .Join(request.Solicitacoes, 
                a => a.Id, 
                b => b.Id, 
                (a, b) => new { a, b })
            .Where(t => !t.a.IsSolicitante([t.b.Centro?.Codigo ?? string.Empty]))
            .Select(t => new StatusDeSolicitacaoIntegracaoEnvioDto(
                t.b.Id ?? string.Empty,
                StatusDeSolicitacao.Erro,
                t.b.Usuario ?? new UsuarioDto(),
                DateTime.Now,
                t.b.Motivo,
                t.b.SistemaDeOrigem ?? Entities.Entities.Solicitacao.SistemaDeOrigemInterno,
                [$"Não foi possível alterar o status da solicitação {t.b.Id} para {StatusDeSolicitacao.CienciaInformada.GetDescription()}. Operação não permitida, usuário é o solicitante."]))
            .ToList();
    }

    #endregion
    
}