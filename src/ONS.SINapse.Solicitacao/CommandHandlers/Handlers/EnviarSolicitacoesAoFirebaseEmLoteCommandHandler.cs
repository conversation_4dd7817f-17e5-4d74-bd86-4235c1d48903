using MassTransit;
using ONS.SINapse.Repository.IRepository.Firebase;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using ValidationResult = FluentValidation.Results.ValidationResult;

namespace ONS.SINapse.Solicitacao.CommandHandlers.Handlers;

public class EnviarSolicitacoesAoFirebaseEmLoteCommandHandler 
    : IConsumer<CadastrarSolicitacaoEmLoteNoFirebaseCommand>,
        IConsumer<EnviarTrocaDeStatusAoFirebaseCommand>,
        IConsumer<ConfirmarLeituraSolicitacaoNoFirebaseCommand>,
        IConsumer<ConfirmarEntregasSolicitacaoNoFirebaseCommand>,
        IConsumer<EnviarMensagemChatSolicitacaoNoFirebaseCommand>
        
{
    private readonly ISolicitacaoFirebaseRepository _solicitacaoFirebaseRepository;

    public EnviarSolicitacoesAoFirebaseEmLoteCommandHandler(
        ISolicitacaoFirebaseRepository solicitacaoFirebaseRepository)
    {
        _solicitacaoFirebaseRepository = solicitacaoFirebaseRepository;
    }

    public async Task Consume(ConsumeContext<CadastrarSolicitacaoEmLoteNoFirebaseCommand> context)
    {
        await _solicitacaoFirebaseRepository.AddUpdateAsync(context.Message.Solicitacao, context.CancellationToken).ConfigureAwait(false);
        await context.RespondAsync(new ValidationResult()).ConfigureAwait(false);
    }

    public async Task Consume(ConsumeContext<EnviarTrocaDeStatusAoFirebaseCommand> context)
    {
        await _solicitacaoFirebaseRepository.PatchFlattenAsync(context.Message.Solicitacoes, context.CancellationToken).ConfigureAwait(false);
        await context.RespondAsync(new ValidationResult()).ConfigureAwait(false);
    }

    public async Task Consume(ConsumeContext<ConfirmarLeituraSolicitacaoNoFirebaseCommand> context)
    {
        await _solicitacaoFirebaseRepository.PatchFlattenAsync(context.Message.Solicitacao, context.CancellationToken).ConfigureAwait(false);
        await context.RespondAsync(new ValidationResult()).ConfigureAwait(false);
    }

    public async Task Consume(ConsumeContext<ConfirmarEntregasSolicitacaoNoFirebaseCommand> context)
    {
        await _solicitacaoFirebaseRepository.PatchFlattenAsync(context.Message.Solicitacao, context.CancellationToken).ConfigureAwait(false);
        await context.RespondAsync(new ValidationResult()).ConfigureAwait(false);
    }

    public async Task Consume(ConsumeContext<EnviarMensagemChatSolicitacaoNoFirebaseCommand> context)
    {
        await _solicitacaoFirebaseRepository.PatchFlattenAsync(context.Message.Solicitacao, context.CancellationToken).ConfigureAwait(false);
        await context.RespondAsync(new ValidationResult()).ConfigureAwait(false);
    }
}