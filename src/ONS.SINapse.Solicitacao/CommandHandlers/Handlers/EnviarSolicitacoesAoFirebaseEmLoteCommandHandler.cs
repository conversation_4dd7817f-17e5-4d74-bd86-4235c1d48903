using FluentValidation.Results;
using MassTransit;
using ONS.SINapse.Repository.IRepository.Firebase;
using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands.Base;
using ValidationResult = FluentValidation.Results.ValidationResult;

namespace ONS.SINapse.Solicitacao.CommandHandlers.Handlers;

public class EnviarSolicitacoesAoFirebaseEmLoteCommandHandler : IConsumer<ISolicitacaoFirebaseCommand>
{
    private readonly ISolicitacaoFirebaseRepository _solicitacaoFirebaseRepository;

    public EnviarSolicitacoesAoFirebaseEmLoteCommandHandler(
        ISolicitacaoFirebaseRepository solicitacaoFirebaseRepository)
    {
        _solicitacaoFirebaseRepository = solicitacaoFirebaseRepository;
    }
    
    public async Task Consume(ConsumeContext<ISolicitacaoFirebaseCommand> context)
    {
        var solicitacoes = context.Message.Solicitacoes.ToList().AggregateUniqueDictionaries();
        var invalideKeys = solicitacoes.Where(x => string.IsNullOrWhiteSpace(x.Key)).ToList();
        
        if (invalideKeys.Count != 0)
        {
            await context.RespondAsync(new ValidationResult(
                    invalideKeys.Select(x => new ValidationFailure("Id", "Id inválido"))
                )).ConfigureAwait(false);
            return;
        }
        
        await _solicitacaoFirebaseRepository.AddUpdateAsync(solicitacoes, context.CancellationToken).ConfigureAwait(false);
        await context.RespondAsync(new ValidationResult()).ConfigureAwait(false);
    }
}