using AutoMapper;
using FluentValidation;
using FluentValidation.Results;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Repository.IRepository.InMemory;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Shared.Messages;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using ONS.SINapse.Solicitacao.Dtos.Integracao;
using ONS.SINapse.Solicitacao.EventHandlers.Events;
using ONS.SINapse.Solicitacao.Validations;

namespace ONS.SINapse.Solicitacao.CommandHandlers.Handlers;

public class CadastrarSolicitacaoEmLoteCommandHandler : CommandHandler<CadastrarSolicitacaoEmLoteCommand, StatusDeSolicitacaoIntegracaoEnvioEmLoteDto>
{
    private readonly ISolicitacaoMemoryRepository _solicitacaoMemoryRepository;
    private readonly IMapper _mapper;
    private readonly IMediatorHandler _mediatorHandler;
    private readonly IValidator<CadastroSolicitacaoDto> _cadastroSolicitacaoDtoValidator;
    
    public CadastrarSolicitacaoEmLoteCommandHandler(
        ISolicitacaoMemoryRepository solicitacaoMemoryRepository,
        IMapper mapper,
        IMediatorHandler mediatorHandler,
        IValidator<CadastroSolicitacaoDto> cadastroSolicitacaoDtoValidator)
    {
        _solicitacaoMemoryRepository = solicitacaoMemoryRepository;
        _mapper = mapper;
        _mediatorHandler = mediatorHandler;
        _cadastroSolicitacaoDtoValidator = cadastroSolicitacaoDtoValidator;
    }

    protected override async Task<StatusDeSolicitacaoIntegracaoEnvioEmLoteDto> Handle(
        CadastrarSolicitacaoEmLoteCommand request, CancellationToken cancellationToken)
    {
        if (request.Solicitacoes.Count == 0)
            return new StatusDeSolicitacaoIntegracaoEnvioEmLoteDto([]);
        
        var solicitacoesInvalidas = Validar(request).ToList();
        
        var solicitacoesValidas = request.Solicitacoes
            .ExceptBy(solicitacoesInvalidas.Select(x => x.Id), solicitacao => solicitacao.Id)
            .ToList();
        
        if (solicitacoesValidas.Count == 0) 
            return await PublicarEventoDeSolicitacoesInvalidas(solicitacoesInvalidas, cancellationToken);
        
        var solicitacoes = _mapper.Map<List<Entities.Entities.Solicitacao>>(solicitacoesValidas);

        var validation = await EnviarSolicitacoesFirebaseAsync(solicitacoes, cancellationToken);
        
        return validation.IsValid
            ? await PublicarEventosDeSolicitacoesProcessadasAsync(solicitacoesInvalidas, solicitacoes, cancellationToken)
            : await PublicarEventosDeErrosAsync(solicitacoesValidas, solicitacoesInvalidas, validation, cancellationToken);
        
    }
    
    private async Task<StatusDeSolicitacaoIntegracaoEnvioEmLoteDto> PublicarEventosDeErrosAsync(
        List<CadastroSolicitacaoDto> solicitacoesCadastradas,
        List<StatusDeSolicitacaoIntegracaoEnvioDto> solicitacoesInvalidas,
        ValidationResult validationResult,
        CancellationToken cancellationToken)
    {
        var solicitacoesProcessadas = solicitacoesCadastradas
            .Select(x => new StatusDeSolicitacaoIntegracaoEnvioDto(
                x.Id ?? string.Empty,
                StatusDeSolicitacao.Erro,
                x.Usuario ?? new UsuarioDto(),
                DateTime.UtcNow,
                null,
                x.SistemaDeOrigem ?? string.Empty,
                validationResult.Errors.Select(e => e.ErrorMessage).ToArray()
            ))
            .Concat(solicitacoesInvalidas)
            .ToList();
        
        var statusDeSolicitacaoIntegracaoRecebidaEvent = new StatusDeSolicitacaoIntegracaoRecebidaEvent(solicitacoesProcessadas);
        await _mediatorHandler.PublicarEventoAsync(statusDeSolicitacaoIntegracaoRecebidaEvent, cancellationToken);

        return new StatusDeSolicitacaoIntegracaoEnvioEmLoteDto(solicitacoesProcessadas);
    }
    
    private Task<ValidationResult> EnviarSolicitacoesFirebaseAsync(
        List<Entities.Entities.Solicitacao> solicitacoes, CancellationToken cancellationToken)
    {
        var firebaseCommand = new CadastrarSolicitacaoEmLoteNoFirebaseCommand(solicitacoes);
        
        return _mediatorHandler.EnviarComandoAsync(firebaseCommand, cancellationToken);
    }
    
    private async Task<StatusDeSolicitacaoIntegracaoEnvioEmLoteDto> PublicarEventosDeSolicitacoesProcessadasAsync(
        List<StatusDeSolicitacaoIntegracaoEnvioDto> solicitacoesInvalidas,
        List<Entities.Entities.Solicitacao> solicitacoesValidas,
        CancellationToken cancellationToken)
    {
        var solicitacoesProcessadas =
            solicitacoesValidas.Select(x => new StatusDeSolicitacaoIntegracaoEnvioDto(
                    x.Id,
                    x.Status,
                    new UsuarioDto(x.UsuarioDeCriacao.Sid, x.UsuarioDeCriacao.Nome, x.UsuarioDeCriacao.Login),
                    x.CreatedAt,
                    null,
                    x.SistemaDeOrigem,
                    []
                ))
                .Concat(solicitacoesInvalidas)
                .ToList();
            
        var statusDeSolicitacaoIntegracaoRecebidaEvent = new StatusDeSolicitacaoIntegracaoRecebidaEvent(solicitacoesProcessadas);
        var trocaDeStatusEvent = _mediatorHandler.PublicarEventoAsync(statusDeSolicitacaoIntegracaoRecebidaEvent, cancellationToken);
        
        var solicitacoesCadastradasDto = _mapper.Map<List<SolicitacaoCadastradasEmLoteDto>>(solicitacoesValidas);
        var solicitacoesCadastradasEvent = _mediatorHandler.PublicarEventoAsync(new SolicitacoesCadastradasEmLoteEvent(solicitacoesCadastradasDto), cancellationToken);
        
        await Task.WhenAll(solicitacoesCadastradasEvent, trocaDeStatusEvent);
        
        return new StatusDeSolicitacaoIntegracaoEnvioEmLoteDto(solicitacoesProcessadas);
    }
    
    private async Task<StatusDeSolicitacaoIntegracaoEnvioEmLoteDto> PublicarEventoDeSolicitacoesInvalidas(
        List<StatusDeSolicitacaoIntegracaoEnvioDto> solicitacoesInvalidas, CancellationToken cancellationToken)
    {
        var statusDeSolicitacaoInvalidaEvent = new StatusDeSolicitacaoIntegracaoRecebidaEvent(solicitacoesInvalidas);
        await _mediatorHandler.PublicarEventoAsync(statusDeSolicitacaoInvalidaEvent, cancellationToken);
        return new StatusDeSolicitacaoIntegracaoEnvioEmLoteDto(solicitacoesInvalidas);
    }
    
    
    #region Validacoes

    
    private IEnumerable<StatusDeSolicitacaoIntegracaoEnvioDto> Validar(CadastrarSolicitacaoEmLoteCommand request)
    {
        var solicitacoesExistentes = ValidarSolicitacaoExistenteAsync(request);

        var solicitacoesInvalidas = ValidarRequest(request);

        return Enumerable.Empty<StatusDeSolicitacaoIntegracaoEnvioDto>()
            .Concat(solicitacoesExistentes)
            .Concat(solicitacoesInvalidas)
            .GroupBy(group => new
                {
                    group.Id,
                    group.SistemaDeOrigem,
                    group.Usuario
                },
                by => by.Erros,
                (group, by) =>
                    new StatusDeSolicitacaoIntegracaoEnvioDto(
                        group.Id,
                        StatusDeSolicitacao.Erro,
                        group.Usuario,
                        DateTime.UtcNow,
                        null,
                        group.SistemaDeOrigem,
                        by.SelectMany(x => x).ToArray()
                    ));
    }

    private IEnumerable<StatusDeSolicitacaoIntegracaoEnvioDto> ValidarRequest(CadastrarSolicitacaoEmLoteCommand request)
    {
        return from cadastroSolicitacaoDto in request.Solicitacoes
            let validation = _cadastroSolicitacaoDtoValidator.Validate(cadastroSolicitacaoDto, options =>
            {
                options.IncludeRuleSets(CadastroSolicitacaoDtoValidator.RuleSets.ObjetoDeManobra);
                options.IncludeRulesNotInRuleSet();
            })
            where !validation.IsValid
            select new StatusDeSolicitacaoIntegracaoEnvioDto(
                cadastroSolicitacaoDto.Id ?? string.Empty,
                StatusDeSolicitacao.Erro,
                cadastroSolicitacaoDto.Usuario ?? new UsuarioDto(),
                DateTime.UtcNow,
                null,
                cadastroSolicitacaoDto.SistemaDeOrigem ?? Entities.Entities.Solicitacao.SistemaDeOrigemInterno,
                validation.Errors.Select(x => x.ErrorMessage).ToArray()
            );
    }
    
    private IEnumerable<StatusDeSolicitacaoIntegracaoEnvioDto> ValidarSolicitacaoExistenteAsync(CadastrarSolicitacaoEmLoteCommand request)
    {
        var idsSolicitacoes = request.Solicitacoes
            .Where(x =>
                !string.IsNullOrWhiteSpace(x.SistemaDeOrigem) &&
                x.SistemaDeOrigem != Entities.Entities.Solicitacao.SistemaDeOrigemInterno)
            .Select(y => y.Id)
            .ToList();

        if (idsSolicitacoes.Count == 0) return [];

        var ids = request.Solicitacoes.Select(status => status.Id).ToArray();
        var solicitacoes = _solicitacaoMemoryRepository.GetList(x => ids.Contains(x.Id));
        var solicitacoesExistente = solicitacoes.Select(s => s.Id).ToList();

        if (solicitacoesExistente.Count == 0) return [];
        
        return solicitacoesExistente
            .Join(request.Solicitacoes,
                inner => inner,
                join => join.Id,
                (inner, join) =>
                    new StatusDeSolicitacaoIntegracaoEnvioDto(
                        inner,
                        StatusDeSolicitacao.Erro,
                        join.Usuario ?? new UsuarioDto(),
                        DateTime.UtcNow, 
                        null,
                        join.SistemaDeOrigem ?? Entities.Entities.Solicitacao.SistemaDeOrigemInterno,
                        ["Erro ao cadastrar solicitação, solicitação já esta cadastrada na base de dados."]
                    ));
    }
    

    #endregion
}