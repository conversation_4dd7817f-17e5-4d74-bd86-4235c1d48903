using AutoMapper;
using FluentValidation;
using ONS.SINapse.Repository.IRepository.Firebase;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands.Firebase;
using ONS.SINapse.Solicitacao.Dtos.Integracao;
using ONS.SINapse.Solicitacao.Factories;

namespace ONS.SINapse.Solicitacao.CommandHandlers.Handlers;

public class CadastrarSolicitacaoEmLoteCommandHandler : CadastrarSolicitacaoEmLoteCommandHandlerBase<CadastrarSolicitacaoEmLoteCommand>
{
    private readonly ISolicitacaoFirebaseCommandFactory _solicitacaoFirebaseCommandFactory;

    public CadastrarSolicitacaoEmLoteCommandHandler(
        ISolicitacaoFirebaseRepository solicitacaoFirebaseRepository,
        IMapper mapper,
        IMediatorHandler mediatorHandler,
        IValidator<CadastroSolicitacaoDto> cadastroSolicitacaoDtoValidator,
        ISolicitacaoFirebaseCommandFactory solicitacaoFirebaseCommandFactory) : base(solicitacaoFirebaseRepository, mapper, mediatorHandler, cadastroSolicitacaoDtoValidator)
    {
        _solicitacaoFirebaseCommandFactory = solicitacaoFirebaseCommandFactory;
    }

    protected override async Task<StatusDeSolicitacaoIntegracaoEnvioEmLoteDto> Handle(
        CadastrarSolicitacaoEmLoteCommand request, CancellationToken cancellationToken)
    {
        if (request.Solicitacoes.Count == 0)
            return new StatusDeSolicitacaoIntegracaoEnvioEmLoteDto([]);
        
        var solicitacoesInvalidas = Validar(request, cancellationToken).ToList();
        
        var solicitacoesValidas = request.Solicitacoes
            .ExceptBy(solicitacoesInvalidas.Select(x => x.Id), solicitacao => solicitacao.Id)
            .ToList();
        
        if (solicitacoesValidas.Count == 0) 
            return await PublicarEventoDeSolicitacoesInvalidas(solicitacoesInvalidas, cancellationToken);
        
        var solicitacoes = Mapper.Map<List<Entities.Entities.Solicitacao>>(solicitacoesValidas);
        
        var firebaseCommand = _solicitacaoFirebaseCommandFactory.ObterCommand<CriarSolicitacaoNoFirebaseCommand>(solicitacoes);
        var validation = await MediatorHandler.EnviarComandoAsync(firebaseCommand, cancellationToken);

        return validation.IsValid
            ? await PublicarEventosDeSolicitacoesProcessadasAsync(solicitacoesInvalidas, solicitacoes, cancellationToken)
            : await PublicarEventosDeErrosAsync(solicitacoesValidas, solicitacoesInvalidas, validation, cancellationToken);
    }
}