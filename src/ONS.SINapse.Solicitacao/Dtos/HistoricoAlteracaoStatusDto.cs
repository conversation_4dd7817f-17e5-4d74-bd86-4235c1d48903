using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Shared.DTO;

namespace ONS.SINapse.Solicitacao.Dtos;

public class HistoricoAlteracaoStatusDto
{
    public HistoricoAlteracaoStatusDto(DateTime dataEHora, StatusDeSolicitacao status, UsuarioDto usuario)
    {
        DataEHora = dataEHora;
        Status = status;
        Usuario = usuario;
    }

    public DateTime DataEHora { get; set; }
    public StatusDeSolicitacao Status { get; set; }
    public UsuarioDto Usuario { get; set; }
}