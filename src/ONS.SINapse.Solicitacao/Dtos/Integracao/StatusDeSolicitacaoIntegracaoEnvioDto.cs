using System.Text.Json.Serialization;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Shared.Converters;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Messages;

namespace ONS.SINapse.Solicitacao.Dtos.Integracao;

public class StatusDeSolicitacaoIntegracaoEnvioEmLoteDto : CommandResult
{
    public StatusDeSolicitacaoIntegracaoEnvioEmLoteDto()
    {
        _solicitacoes = [];
    }
    
    public StatusDeSolicitacaoIntegracaoEnvioEmLoteDto(List<StatusDeSolicitacaoIntegracaoEnvioDto> solicitacoes)
    {
        _solicitacoes = solicitacoes;
    }

    private readonly List<StatusDeSolicitacaoIntegracaoEnvioDto> _solicitacoes;
    public IReadOnlyCollection<StatusDeSolicitacaoIntegracaoEnvioDto> Solicitacoes => _solicitacoes;
}

public record StatusDeSolicitacaoIntegracaoEnvioDto(
    string Id,
    StatusDeSolicitacao Status,
    UsuarioDto Usuario,
    DateTime Data,
    string? Justificativa,
    string SistemaDeOrigem,
    string[] Erros
)
{
    public string[] Erros { get; private set; } = Erros;
    [JsonConverter(typeof(EnumToObjectJsonConverter<StatusDeSolicitacao>))]
    public StatusDeSolicitacao Status { get; private set; } = Status;
    
    public void AdicionarErros(string[] erros)
    {
        Status = StatusDeSolicitacao.Erro;
        Erros = Erros.Concat(erros).ToArray();
    }

    public bool EstaValido() => Erros.Length == 0;

    public string ErrosFormatados() => string.Join(", ", Erros);
}