using System.Text.Json.Serialization;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Shared.Converters;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Messages;

namespace ONS.SINapse.Solicitacao.Dtos.Integracao;

public class StatusDeSolicitacaoIntegracaoEnvioEmLoteDto : CommandResult
{
    public StatusDeSolicitacaoIntegracaoEnvioEmLoteDto()
    {
        _solicitacoes = [];
    }
    
    public StatusDeSolicitacaoIntegracaoEnvioEmLoteDto(List<StatusDeSolicitacaoIntegracaoEnvioDto> solicitacoes)
    {
        _solicitacoes = solicitacoes;
    }

    private readonly List<StatusDeSolicitacaoIntegracaoEnvioDto> _solicitacoes;
    public IReadOnlyCollection<StatusDeSolicitacaoIntegracaoEnvioDto> Solicitacoes => _solicitacoes;
}

public class StatusDeSolicitacaoIntegracaoEnvioDto
{
    public string Id { get; private set; }

    [JsonConverter(typeof(EnumToObjectJsonConverter<StatusDeSolicitacao>))]
    public StatusDeSolicitacao Status { get; private set; }

    public UsuarioDto Usuario { get; private set; }

    public DateTime Data { get; private set; }

    public string? Justificativa { get; private set; }

    public string SistemaDeOrigem { get; private set; }
    
    public string? SolicitacaoDeOrigemId { get; init; }

    public string[] Erros { get; private set; }

    public StatusDeSolicitacaoIntegracaoEnvioDto(
        string id,
        StatusDeSolicitacao status,
        UsuarioDto usuario,
        DateTime data,
        string? justificativa,
        string sistemaDeOrigem,
        string[] erros)
    {
        Id = id ?? throw new ArgumentNullException(nameof(id));
        Status = status;
        Usuario = usuario ?? throw new ArgumentNullException(nameof(usuario));
        Data = data;
        Justificativa = justificativa;
        SistemaDeOrigem = sistemaDeOrigem ?? throw new ArgumentNullException(nameof(sistemaDeOrigem));
        Erros = erros ?? Array.Empty<string>();
    }

    /// <summary>
    /// Adiciona novos erros à lista, e marca o status como 'Erro'.
    /// </summary>
    public void AdicionarErros(string[] novosErros)
    {
        if (novosErros == null || novosErros.Length == 0) return;

        Status = StatusDeSolicitacao.Erro;
        Erros = Erros.Concat(novosErros).ToArray();
    }

    /// <summary>
    /// Indica se não há erros registrados.
    /// </summary>
    public bool EstaValido() =>
        Erros.Length == 0;

    /// <summary>
    /// Retorna todos os erros formatados como uma única string separada por vírgulas.
    /// </summary>
    public string ErrosFormatados() =>
        string.Join(", ", Erros);
}
