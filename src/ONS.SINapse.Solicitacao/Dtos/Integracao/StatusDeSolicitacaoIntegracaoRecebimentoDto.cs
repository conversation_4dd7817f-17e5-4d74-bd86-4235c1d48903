using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Shared.DTO;

namespace ONS.SINapse.Solicitacao.Dtos.Integracao;


public record StatusDeSolicitacaoIntegracaoRecebimentoDto(
    string? Id,
    StatusDeSolicitacao? Status,
    string? Motivo,
    ObjetoDeManobraDto? Centro,
    UsuarioDto? Usuario,
    string? SistemaDeOrigem
)
{
    public UsuarioDto? Usuario { get; private set; } = Usuario;

    public StatusDeSolicitacaoIntegracaoRecebimentoDto DefinirUsuario(UsuarioDto usuario) =>
        this with { Usuario = usuario };
}
