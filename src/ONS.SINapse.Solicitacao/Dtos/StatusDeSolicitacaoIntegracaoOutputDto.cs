using ONS.SINapse.Shared.DTO;

namespace ONS.SINapse.Solicitacao.Dtos;

public class StatusDeSolicitacaoIntegracaoOutputDto 
{
    public StatusDeSolicitacaoIntegracaoOutputDto(List<StatusDeSolicitacaoIntegracaoDto> solicitacoes)
    {
        _solicitacoes = solicitacoes;
    }

    private readonly List<StatusDeSolicitacaoIntegracaoDto> _solicitacoes;
    public IReadOnlyCollection<StatusDeSolicitacaoIntegracaoDto> Solicitacoes => _solicitacoes;
}

public class StatusDeSolicitacaoIntegracaoDto
{
    public StatusDeSolicitacaoIntegracaoDto(string id,
        IEnumerable<HistoricoDoStatusDto> historicos)
    {
        Id = id;
        _historicos = historicos.ToList();
        _erros = [];
    }

    public string Id { get; }
    private readonly List<HistoricoDoStatusDto> _historicos;
    public IReadOnlyCollection<HistoricoDoStatusDto> Historicos => _historicos;
    
    private readonly List<string> _erros;
    public IReadOnlyCollection<string> Erros => _erros;
    
    public void AdicionarErro(string erro)
    {
        _erros.Add(erro);
    }
}


public record HistoricoDoStatusDto(StatusDoHistoricoDto Status, UsuarioDto Usuario, DateTime Data, string? Justificativa);

public record StatusDoHistoricoDto(short Codigo, string Descricao);