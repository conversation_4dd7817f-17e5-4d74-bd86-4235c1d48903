using Newtonsoft.Json;
using ONS.SINapse.Shared.DTO.Firebase;

namespace ONS.SINapse.Solicitacao.Converters;

public class AlertaSonoroChaveJsonConverter : JsonConverter<Dictionary<string, AlertaSonoroRealtimeDto>>
{
    public override void WriteJson(JsonWriter writer, Dictionary<string, AlertaSonoroRealtimeDto>? value, JsonSerializer serializer)
    {
        if(value is null) return;
        
        writer.WriteStartObject();
        foreach (var dict in value)
        {
            writer.WritePropertyName(dict.Key.ToLowerInvariant()); 
            serializer.Serialize(writer, dict.Value);
        }
        writer.WriteEndObject();
    }

    public override Dictionary<string, AlertaSonoroRealtimeDto>? Read<PERSON>son(JsonReader reader, Type objectType, Dictionary<string, AlertaSonoroRealtimeDto>? existingValue, bool hasExistingValue,
        JsonSerializer serializer)
    {
        throw new NotImplementedException();
    }
    
    public override bool CanRead => false;
}