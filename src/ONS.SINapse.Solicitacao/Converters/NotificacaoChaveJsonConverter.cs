using Newtonsoft.Json;
using ONS.SINapse.Shared.DTO.Firebase;

namespace ONS.SINapse.Solicitacao.Converters;

public class NotificacaoChaveJsonConverter : JsonConverter<Dictionary<string, Dictionary<string, NotificacaoRealtimeDto>>>
{
    public override void Write<PERSON><PERSON>(JsonWriter writer, Dictionary<string, Dictionary<string, NotificacaoRealtimeDto>>? value, JsonSerializer serializer)
    {
        if(value is null) return;
        
        writer.WriteStartObject();
        foreach (var dict in value)
        {
            writer.WritePropertyName(dict.Key.ToLowerInvariant()); 
            serializer.Serialize(writer, dict.Value);
        }
        writer.WriteEndObject();
    }
    
    public override Dictionary<string, Dictionary<string, NotificacaoRealtimeDto>>? Read<PERSON>son(JsonReader reader, Type objectType, Dictionary<string, Dictionary<string, NotificacaoRealtimeDto>>? existingValue, bool hasExistingValue,
        JsonSerializer serializer)
    {
        throw new NotImplementedException();
    }

    public override bool CanRead => false;
}