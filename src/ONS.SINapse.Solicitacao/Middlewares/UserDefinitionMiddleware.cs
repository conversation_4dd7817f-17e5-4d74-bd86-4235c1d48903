using MassTransit;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Identity;
using ONS.SINapse.Solicitacao.Interfaces;

namespace ONS.SINapse.Solicitacao.Middlewares;

public class UserDefinitionMiddleware<TWithUserCommand> : IFilter<ConsumeContext<TWithUserCommand>>
    where TWithUserCommand : class, IWithUserDefinitionCommand
{
    private readonly IUserContext _userContext;

    public UserDefinitionMiddleware(IUserContext userContext)
    {
        _userContext = userContext;
    }

    public Task Send(ConsumeContext<TWithUserCommand> context, IPipe<ConsumeContext<TWithUserCommand>> next)
    {
        if (context.Message.PossuiUsuario()) return next.Send(context);
        
        if(_userContext.UsuarioAutenticado())
            context.Message.DefinirUsuario(new UsuarioDto(_userContext.Sid, _userContext.Nome, _userContext.Login));
        
        return next.Send(context);
    }

    public void Probe(ProbeContext context)
    {
        // O método 'Probe' não requer implementação específica neste middleware, 
        // pois não há informações adicionais de diagnóstico a serem fornecidas.
    }
}