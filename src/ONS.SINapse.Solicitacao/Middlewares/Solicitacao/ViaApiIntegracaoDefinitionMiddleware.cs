using MassTransit;
using Microsoft.AspNetCore.Http;
using ONS.SINapse.Solicitacao.Interfaces;

namespace ONS.SINapse.Solicitacao.Middlewares.Solicitacao;

public sealed class ViaApiIntegracaoDefinitionMiddleware<TViaApiIntegracao> : IFilter<ConsumeContext<TViaApiIntegracao>>
    where TViaApiIntegracao : class, IViaApiIntegracaoDefinitionCommand
{
    private readonly IHttpContextAccessor _httpContextAccessor;

    public ViaApiIntegracaoDefinitionMiddleware(IHttpContextAccessor httpContextAccessor)
    {
        _httpContextAccessor = httpContextAccessor;
    }
    
    public Task Send(ConsumeContext<TViaApiIntegracao> context, IPipe<ConsumeContext<TViaApiIntegracao>> next)
    {
        if (_httpContextAccessor.HttpContext is not null && _httpContextAccessor.HttpContext.Request.Headers.ContainsKey("AuthorizationUser"))
            context.Message.DefinirViaApiIntegracao();
        
        return next.Send(context);
    }

    public void Probe(ProbeContext context)
    {
        // O m�todo 'Probe' n�o requer implementa��o espec�fica neste middleware, 
        // pois n�o h� informa��es adicionais de diagn�stico a serem fornecidas.
    }
}