using MassTransit;
using ONS.SINapse.Shared.Factories;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;

namespace ONS.SINapse.Solicitacao.Middlewares.Solicitacao;

public class IdDefinitionMiddleware : IFilter<ConsumeContext<CadastrarSolicitacaoEmLoteCommand>>
{
    public Task Send(ConsumeContext<CadastrarSolicitacaoEmLoteCommand> context, IPipe<ConsumeContext<CadastrarSolicitacaoEmLoteCommand>> next)
    {
        context.Message.Solicitacoes.ForEach(x =>
        {
            if (x.Origem is null && x.<PERSON><PERSON> is null) return;
            
            if(string.IsNullOrWhiteSpace(x.Id))
                x.Id = IdSolicitacaoFactory.GerarNovoId(x.Origem!.Codigo, x.Destino!.Codigo);
        });
        
        return next.Send(context);
    }

    public void Probe(ProbeContext context)
    {
        // O método 'Probe' não requer implementação específica neste middleware, 
        // pois não há informações adicionais de diagnóstico a serem fornecidas.
    }
}