using MassTransit;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;

namespace ONS.SINapse.Solicitacao.Middlewares.Solicitacao;

public class LoteIdDefinitionMiddleware : IFilter<ConsumeContext<CadastrarSolicitacaoEmLoteCommand>>
{
    public Task Send(ConsumeContext<CadastrarSolicitacaoEmLoteCommand> context, IPipe<ConsumeContext<CadastrarSolicitacaoEmLoteCommand>> next)
    {
        var loteId = Guid.NewGuid();
        context.Message.DefinirLote(loteId);
        return next.Send(context);
    }

    public void Probe(ProbeContext context)
    {
        // O método 'Probe' não requer implementação específica neste middleware, 
        // pois não há informações adicionais de diagnóstico a serem fornecidas.
    }
}