using MassTransit;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;

namespace ONS.SINapse.Solicitacao.Middlewares.Solicitacao;

public class LoteIdDefinitionMiddleware : IFilter<ConsumeContext<CadastrarSolicitacaoEmLoteCommand>>,
    IFilter<ConsumeContext<ConfirmarEnvioSolicitacaoFirebaseCommand>>,
    IFilter<ConsumeContext<EncaminharSolicitacaoFirebaseCommand>>
{
    public Task Send(ConsumeContext<CadastrarSolicitacaoEmLoteCommand> context, IPipe<ConsumeContext<CadastrarSolicitacaoEmLoteCommand>> next)
    {
        DefinirLote(context);
        return next.Send(context);
    }
    
    public Task Send(ConsumeContext<ConfirmarEnvioSolicitacaoFirebaseCommand> context, IPipe<ConsumeContext<ConfirmarEnvioSolicitacaoFirebaseCommand>> next)
    {
        DefinirLote(context);
        return next.Send(context);
    }

    public Task Send(ConsumeContext<EncaminharSolicitacaoFirebaseCommand> context, IPipe<ConsumeContext<EncaminharSolicitacaoFirebaseCommand>> next)
    {
        DefinirLote(context);
        return next.Send(context);
    }

    public void Probe(ProbeContext context)
    {
        // O método 'Probe' não requer implementação específica neste middleware, 
        // pois não há informações adicionais de diagnóstico a serem fornecidas.
    }
    
    private static void DefinirLote(ConsumeContext<CadastrarSolicitacaoEmLoteCommandBase> context)
    {
        var loteId = Guid.NewGuid();
        context.Message.DefinirLote(loteId);
    }
}