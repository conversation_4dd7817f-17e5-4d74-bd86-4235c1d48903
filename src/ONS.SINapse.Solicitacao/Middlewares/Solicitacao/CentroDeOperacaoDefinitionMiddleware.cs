using MassTransit;
using ONS.SINapse.Repository.IRepository;
using ONS.SINapse.Shared.Identity;
using ONS.SINapse.Solicitacao.Interfaces;

namespace ONS.SINapse.Solicitacao.Middlewares.Solicitacao;

public sealed class CentroDeOperacaoDefinitionMiddleware<TWithCentro> : IFilter<ConsumeContext<TWithCentro>>
    where TWithCentro : class, IWithCentroDeOperacaoDefinitionCommand
{
    private readonly IUserContext _userContext;

    public CentroDeOperacaoDefinitionMiddleware(IUserContext userContext)
    {
        _userContext = userContext;
    }
    
    public async Task Send(ConsumeContext<TWithCentro> context, IPipe<ConsumeContext<TWithCentro>> next)
    {
        if (context.Message.PossuiCentro())
        {
            await next.Send(context).ConfigureAwait(false);
            return;
        }
        
        if(!context.Message.PossuiUsuario())
        {
            await next.Send(context).ConfigureAwait(false);
            return;
        }
        
        context.Message.DefinirCentros(_userContext.Perfil.Centros);
        
        await next.Send(context).ConfigureAwait(false);
    }

    public void Probe(ProbeContext context)
    {
        // O m�todo 'Probe' n�o requer implementa��o espec�fica neste middleware, 
        // pois n�o h� informa��es adicionais de diagn�stico a serem fornecidas.
    }
}