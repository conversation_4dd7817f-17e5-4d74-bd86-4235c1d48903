using ONS.SINapse.Shared.DTO;

namespace ONS.SINapse.Solicitacao.Interfaces;

/// <summary>
/// A aplicação deverá tentar carregar um usuário com base em um token de autenticação.
/// Em caso de integração kafka, no momento do consumo da mensagem, o usuário deverá ser fornecido pelo header do evento de integração.
/// </summary>
public interface IWithUserDefinitionCommand
{
    public UsuarioDto? Usuario { get; }
    void DefinirUsuario(UsuarioDto usuarioDto);
    bool PossuiUsuario();
}