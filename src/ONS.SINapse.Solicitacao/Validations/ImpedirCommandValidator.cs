using FluentValidation;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;

namespace ONS.SINapse.Solicitacao.Validations;

public class ImpedirCommandValidator : AbstractValidator<ImpedirCommand>
{
    private const string CampoPreenchido = "Campo {PropertyName} precisa ser preenchido.";
    
    public ImpedirCommandValidator()
    {
        RuleFor(x => x.Codigo)
            .NotNull()
            .NotEmpty()
            .WithMessage(CampoPreenchido);
        
        RuleFor(x => x.Motivo)
            .NotNull()
            .NotEmpty()
            .WithMessage(CampoPreenchido);
        
        RuleFor(x => x.<PERSON>)
            .NotNull()
            .NotEmpty()
            .WithMessage(CampoPreenchido);
    }
}