using FluentValidation;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;

namespace ONS.SINapse.Solicitacao.Validations;

public class ConfirmarSolicitacaoValidator : AbstractValidator<ConfirmarCommand>
{
    private const string MensagemCampoPreenchido = "Campo {PropertyName} precisa ser preenchido";
    
    public ConfirmarSolicitacaoValidator()
    {
        RuleFor(x => x.Codigo)
            .NotNull()
            .NotEmpty()
            .WithMessage(MensagemCampoPreenchido);

        RuleFor(x => x.Usuario)
            .NotEmpty()
            .NotNull()
            .WithMessage(MensagemCampoPreenchido);
    }
}