using FluentValidation;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;

namespace ONS.SINapse.Solicitacao.Validations;

public class CancelarEmLoteCommandValidator : AbstractValidator<CancelarEmLoteCommand>
{
    public CancelarEmLoteCommandValidator()
    {
        RuleFor(x => x.Solicitacoes)
            .NotNull()
            .NotEmpty()
            .WithMessage("Nenhuma solicitação foi encontrada.");

        RuleFor(x => x.Usuario)
            .NotEmpty()
            .NotNull()
            .WithMessage("Não foi possivel definir o usuário que está efetuando o cancelamento.");

        RuleFor(x => x.Centros)
            .NotEmpty()
            .NotNull()
            .WithMessage("Nenhum centro de operação encontrado para o usuário da operação.");
    }
}