using FluentValidation;
using ONS.SINapse.Solicitacao.EventHandlers.IntegrationEvents;

namespace ONS.SINapse.Solicitacao.Validations;

public class TrocaDeStatusExternaIntegrationEventValidator : AbstractValidator<TrocaDeStatusExternaIntegrationEvent>
{
    private const string MensagemCampoPreenchido = "Campo {PropertyName} precisa ser preenchido.";
    
    public TrocaDeStatusExternaIntegrationEventValidator()
    {
        RuleFor(x => x.CentroDeOperacao)
            .NotNull()
            .NotEmpty()
            .WithMessage(MensagemCampoPreenchido);
        
        RuleFor(x => x.Codigo)
            .NotNull()
            .NotEmpty()
            .WithMessage(MensagemCampoPreenchido);
        
        RuleFor(x => x.Status)
            .NotNull()
            .NotEmpty()
            .WithMessage(MensagemCampoPreenchido);
    }
}