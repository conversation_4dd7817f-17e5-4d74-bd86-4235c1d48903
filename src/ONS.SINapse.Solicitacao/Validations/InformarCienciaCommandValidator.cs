using FluentValidation;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;

namespace ONS.SINapse.Solicitacao.Validations;

public class InformarCienciaCommandValidator : AbstractValidator<InformarCienciaCommand>
{
    private const string MensagemPreenchido = "Campo {PropertyName} precisa ser preenchido.";
    
    public InformarCienciaCommandValidator()
    {
        RuleFor(x => x.Centros)
            .NotNull()
            .NotEmpty()
            .WithMessage(MensagemPreenchido);
        
        RuleFor(x => x.<PERSON>ua<PERSON>)
            .NotNull()
            .WithMessage(MensagemPreenchido);
    }
}