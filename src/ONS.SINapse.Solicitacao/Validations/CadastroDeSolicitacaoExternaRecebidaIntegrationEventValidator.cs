using FluentValidation;
using ONS.SINapse.Solicitacao.EventHandlers.IntegrationEvents;

namespace ONS.SINapse.Solicitacao.Validations;

public class CadastroDeSolicitacaoExternaRecebidaIntegrationEventValidator : AbstractValidator<CadastroDeSolicitacaoExternaRecebidaIntegrationEvent>
{
    private const string ValorNaoPodeSerVazio = "Campo '{PropertyName}' não pode estar vazio.";
    private const string ValorNaoPodeSerNulo = "Campo '{PropertyName}' não pode ser nulo.";

    public CadastroDeSolicitacaoExternaRecebidaIntegrationEventValidator()
    {
        RuleFor(x => x.Solicitacoes)
            .NotNull()
            .WithMessage(ValorNaoPodeSerNulo)
            .NotEmpty()
            .WithMessage(ValorNaoPodeSerVazio);

        RuleForEach(x => x.Solicitacoes)
            .SetValidator(new CadastroDeSolicitacaoExternaDtoValidator());
    }
}

public class CadastroDeSolicitacaoExternaDtoValidator : AbstractValidator<CadastroDeSolicitacaoExternaDto>
{
    private const string ValorNaoPodeSerVazio = "Campo '{PropertyName}' não pode estar vazio.";
    private const string ValorNaoPodeSerNulo = "Campo '{PropertyName}' não pode ser nulo.";

    public CadastroDeSolicitacaoExternaDtoValidator()
    {
        RuleFor(x => x.Destino)
            .NotNull()
            .WithMessage(ValorNaoPodeSerNulo)
            .NotEmpty()
            .WithMessage(ValorNaoPodeSerVazio);

        RuleFor(x => x.Origem)
            .NotNull()
            .WithMessage(ValorNaoPodeSerNulo)
            .NotEmpty()
            .WithMessage(ValorNaoPodeSerVazio);

        RuleFor(x => x.Mensagem)
            .NotNull()
            .WithMessage(ValorNaoPodeSerNulo)
            .NotEmpty()
            .WithMessage(ValorNaoPodeSerVazio);

        RuleFor(x => x.Id)
            .NotNull()
            .WithMessage(ValorNaoPodeSerNulo)
            .NotEmpty()
            .WithMessage(ValorNaoPodeSerVazio);

        RuleFor(x => x.SistemaDeOrigem)
            .NotNull()
            .WithMessage(ValorNaoPodeSerNulo)
            .NotEmpty()
            .WithMessage(ValorNaoPodeSerVazio);

        RuleFor(x => x.Usuario)
            .NotNull()
            .WithMessage(ValorNaoPodeSerNulo)
            .NotEmpty()
            .WithMessage(ValorNaoPodeSerVazio);
    }
}