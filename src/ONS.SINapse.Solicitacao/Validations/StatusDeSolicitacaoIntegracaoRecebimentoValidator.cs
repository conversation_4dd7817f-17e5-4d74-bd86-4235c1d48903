using FluentValidation;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Identity;
using ONS.SINapse.Solicitacao.Dtos.Integracao;

namespace ONS.SINapse.Solicitacao.Validations;

public sealed class StatusDeSolicitacaoIntegracaoRecebimentoValidator : AbstractValidator<StatusDeSolicitacaoIntegracaoRecebimentoDto>
{
    private readonly IUserContext _userContext;
    private const string MensagemCampoPreenchido = "Campo {PropertyName} precisa ser preenchido.";
    
    public StatusDeSolicitacaoIntegracaoRecebimentoValidator(IUserContext userContext)
    {
        _userContext = userContext;
        
        RuleFor(x => x.Id)
            .NotNull()
            .NotEmpty()
            .WithMessage(MensagemCampoPreenchido);
            
        RuleFor(x => x.Status)
            .NotNull()
            .NotEmpty()
            .WithMessage(MensagemCampoPreenchido)
            .IsInEnum()
            .WithMessage("Valor inválido ou desconhecido");

        RuleFor(x => x.Usuario)
            .NotNull()
            .NotEmpty()
            .WithMessage(MensagemCampoPreenchido)
            .SetValidator(new UsuarioValidator()!)
            .WithMessage(MensagemCampoPreenchido)
            .Must(UsuarioEstaValido)
            .WithMessage("Campo {PropertyName} inválido com o usuário logado no sistema.");

        RuleFor(x => x.Centro)
            .NotNull()
            .NotEmpty()
            .WithMessage(MensagemCampoPreenchido)
            .SetValidator(new ObjetoManobraValidator()!)
            .Must(CentroEstaValido)
            .WithMessage("Centro não corresponde ao perfil selecionado.");
        
        RuleFor(x => x.Motivo)
            .Must(MotivoEstaValido)
            .WithMessage(MensagemCampoPreenchido);
    }
    
    
    /// <summary>
    /// Valida se o campo motivo for preenchido quando um status de impedimento for recebido
    /// </summary>
    /// <param name="statusDto">Dto da alteração de status</param>
    /// <param name="motivo">Campo a ser validado</param>
    /// <returns></returns>
    private static bool MotivoEstaValido(StatusDeSolicitacaoIntegracaoRecebimentoDto statusDto, string? motivo)
        => !(statusDto.Status == StatusDeSolicitacao.Impedida && string.IsNullOrWhiteSpace(motivo));

    /// <summary>
    /// Quando troca de status via sinapse validar o centro enviado com base no perfil selecionado
    /// </summary>
    /// <param name="statusDto">Dto da alteração de status</param>
    /// <param name="centro">Campo a ser validado</param>
    /// <returns></returns>
    private bool CentroEstaValido(StatusDeSolicitacaoIntegracaoRecebimentoDto statusDto, ObjetoDeManobraDto? centro)
    {
        return statusDto.SistemaDeOrigem != Entities.Entities.Solicitacao.SistemaDeOrigemInterno || 
               _userContext.Perfil.Centros.Contains(centro?.Codigo ?? string.Empty);
    }
    
    private bool UsuarioEstaValido(StatusDeSolicitacaoIntegracaoRecebimentoDto statusDto, UsuarioDto? usuario)
    {
        return statusDto.SistemaDeOrigem != Entities.Entities.Solicitacao.SistemaDeOrigemInterno || 
               _userContext.Sid == usuario?.Sid;
    }
    
    private sealed class ObjetoManobraValidator : AbstractValidator<ObjetoDeManobraDto>
    {
        public ObjetoManobraValidator()
        {
            RuleFor(x => x.Codigo)
                .NotNull()
                .NotEmpty()
                .WithMessage(MensagemCampoPreenchido);
            
            RuleFor(x => x.Nome)
                .NotNull()
                .NotEmpty()
                .WithMessage(MensagemCampoPreenchido);
        }
    }

    private sealed class UsuarioValidator : AbstractValidator<UsuarioDto>
    {
        public UsuarioValidator()
        {
            RuleFor(x => x.Nome)
                .NotNull()
                .NotEmpty()
                .WithMessage(MensagemCampoPreenchido);
            
            RuleFor(x => x.Login)
                .NotNull()
                .NotEmpty()
                .WithMessage(MensagemCampoPreenchido);
            
            RuleFor(x => x.Sid)
                .NotNull()
                .NotEmpty()
                .WithMessage(MensagemCampoPreenchido);
        }
    }
}