using FluentValidation;
using ONS.SINapse.Repository.IRepository;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;

namespace ONS.SINapse.Solicitacao.Validations;

public class CadastroSolicitacaoDtoValidator : AbstractValidator<CadastroSolicitacaoDto>
{
    private const string ValorNaoPodeSerVazio = "Campo '{PropertyName}' não pode estar vazio.";
    private const string ValorNaoPodeSerNulo = "Campo '{PropertyName}' não pode ser nulo.";
    private const string SolicitacaoJaExiste = "Solicitacao já existe.";
    
    private readonly ISolicitacaoRepository _solicitacaoRepository;

    public CadastroSolicitacaoDtoValidator(ISolicitacaoRepository solicitacaoRepository)
    {
        _solicitacaoRepository = solicitacaoRepository;

        RuleForObjetoDeManobra();
        
        RuleFor(s => s.Mensagem)
            .NotNull()
            .WithMessage(ValorNaoPodeSerNulo)
            .NotEmpty()
            .WithMessage(ValorNaoPodeSerVazio);

        RuleFor(s => s.SistemaDeOrigem)
            .NotNull()
            .WithMessage(ValorNaoPodeSerNulo)
            .NotEmpty()
            .WithMessage(ValorNaoPodeSerVazio);
        
        RuleFor(x => x.Usuario)
            .NotNull()
            .WithMessage(ValorNaoPodeSerNulo)
            .NotEmpty()
            .WithMessage(ValorNaoPodeSerVazio);
        
        RuleFor(x => x.CodigoExterno)
            .MustAsync(ValidarSolicitacaoExistenteAsync)
            .WithMessage(SolicitacaoJaExiste);
    }

    private void RuleForObjetoDeManobra()
    {
        RuleFor(s => s.Origem)
            .NotNull()
            .WithMessage(ValorNaoPodeSerNulo)
            .NotEmpty()
            .WithMessage(ValorNaoPodeSerVazio);

        RuleFor(s => s.Destino)
            .NotNull()
            .WithMessage(ValorNaoPodeSerNulo)
            .NotEmpty()
            .WithMessage(ValorNaoPodeSerVazio);
    }
    
    
    private async Task<bool> ValidarSolicitacaoExistenteAsync(CadastroSolicitacaoDto command, string? codigoExterno, CancellationToken cancellationToken)
    {
        // Não valida caso não haja código externo
        if (string.IsNullOrEmpty(codigoExterno)) return true;

        var result =
            await _solicitacaoRepository
                .AnyAsync(x => x.CodigoExterno != null && x.CodigoExterno == codigoExterno, cancellationToken);

        return !result;
    }

    public static class RuleSets
    {
        public const string ObjetoDeManobra = nameof(ObjetoDeManobra);
    }
}