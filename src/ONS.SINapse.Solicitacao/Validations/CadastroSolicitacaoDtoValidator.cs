using FluentValidation;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Factories;
using ONS.SINapse.Shared.Identity;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;

namespace ONS.SINapse.Solicitacao.Validations;

public class CadastroSolicitacaoDtoValidator : AbstractValidator<CadastroSolicitacaoDto>
{
    private const string ValorNaoPodeSerVazio = "Campo '{PropertyName}' não pode estar vazio.";
    private const string ValorNaoPodeSerNulo = "Campo '{PropertyName}' não pode ser nulo.";
    
    private readonly IUserContext _userContext;

    public CadastroSolicitacaoDtoValidator(IUserContext userContext)
    {
        _userContext = userContext;

        RuleForObjetoDeManobra();
        
        RuleFor(s => s.Mensagem)
            .NotNull()
            .WithMessage(ValorNaoPodeSerNulo)
            .NotEmpty()
            .WithMessage(ValorNaoPodeSerVazio);

        RuleFor(s => s.SistemaDeOrigem)
            .NotNull()
            .WithMessage(ValorNaoPodeSerNulo)
            .NotEmpty()
            .WithMessage(ValorNaoPodeSerVazio);
        
        RuleFor(x => x.Usuario)
            .NotNull()
            .WithMessage(ValorNaoPodeSerNulo)
            .NotEmpty()
            .WithMessage(ValorNaoPodeSerVazio)
            .Must(UsuarioEstaValido)
            .WithMessage("Campo {PropertyName} inválido com o usuário logado no sistema.");

        RuleFor(x => x.Id)
            .NotNull()
            .WithMessage(ValorNaoPodeSerNulo)
            .NotEmpty()
            .WithMessage(ValorNaoPodeSerVazio)
            .Must(IdSolicitacaoFactory.EstaValido)
            .WithMessage(x => @$"Id em um formato inválido Id: {x.Id} Formato: ^\d{14}-[A-Z0-9]+-[A-Za-z0-9]{6}-[A-Z0-9]+$");
    }

    private void RuleForObjetoDeManobra()
    {
        RuleFor(s => s.Origem)
            .NotNull()
            .WithMessage(ValorNaoPodeSerNulo)
            .NotEmpty()
            .WithMessage(ValorNaoPodeSerVazio)
            .Must(CentroEstaValido)
            .WithMessage("Centro não corresponde ao perfil selecionado.");

        RuleFor(s => s.Destino)
            .NotNull()
            .WithMessage(ValorNaoPodeSerNulo)
            .NotEmpty()
            .WithMessage(ValorNaoPodeSerVazio);
    }

    public static class RuleSets
    {
        public const string ObjetoDeManobra = nameof(ObjetoDeManobra);
    }

    /// <summary>
    /// Quando troca de status via sinapse validar o centro enviado com base no perfil selecionado
    /// </summary>
    /// <param name="command">Dto da alteração de status</param>
    /// <param name="centro">Campo a ser validado</param>
    /// <returns></returns>
    private bool CentroEstaValido(CadastroSolicitacaoDto command, ObjetoDeManobraDto? centro)
    {
        return command.SistemaDeOrigem != Entities.Entities.Solicitacao.SistemaDeOrigemInterno ||
               _userContext.Perfil.Centros.Contains(centro?.Codigo ?? string.Empty);
    }

    private bool UsuarioEstaValido(CadastroSolicitacaoDto command, UsuarioDto? usuario)
    {
        return command.SistemaDeOrigem != Entities.Entities.Solicitacao.SistemaDeOrigemInterno ||
               _userContext.Sid == usuario?.Sid;
    }
}