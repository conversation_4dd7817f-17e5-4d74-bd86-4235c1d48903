using FluentValidation;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;

namespace ONS.SINapse.Solicitacao.Validations;

public class FinalizarSolicitacaoEmLoteCommandValidator : AbstractValidator<FinalizarEmLoteCommand>
{
    private const string MessagePreenchido = "Campo {PropertyName} precisa ser preenchido.";
    
    public FinalizarSolicitacaoEmLoteCommandValidator()
    {
        RuleFor(x => x.<PERSON><PERSON>)
            .NotNull()
            .WithMessage(MessagePreenchido);
    }
}