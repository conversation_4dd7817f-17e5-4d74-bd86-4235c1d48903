using FluentValidation;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;

namespace ONS.SINapse.Solicitacao.Validations;

public class EncaminharSolicitacaoCommandValidator : AbstractValidator<EncaminharSolicitacaoCommand>
{
    private const string ValorNaoPodeSerVazio = "Campo '{PropertyName}' não pode estar vazio.";
    private const string ValorNaoPodeSerNulo = "Campo '{PropertyName}' não pode ser nulo.";

    public EncaminharSolicitacaoCommandValidator()
    {
        RuleSet(RuleSets.ValidarPreenchimentoIdsSolicitacoes, () =>
        {
            RuleFor(x => x.SolicitacoesId)
                .NotNull()
                .WithMessage(ValorNaoPodeSerNulo)
                .NotEmpty()
                .WithMessage(ValorNaoPodeSerVazio);
        
            RuleForEach(x => x.SolicitacoesId)
                .NotNull()
                .WithMessage(ValorNaoPodeSerNulo)
                .NotEmpty()
                .WithMessage(ValorNaoPodeSerVazio);
        });
        
        RuleFor(x => x.Usuario)
            .NotNull()
            .WithMessage(ValorNaoPodeSerNulo)
            .NotEmpty()
            .WithMessage(ValorNaoPodeSerVazio);
        
    }
    
    public static class RuleSets
    {
        public const string ValidarPreenchimentoIdsSolicitacoes = nameof(ValidarPreenchimentoIdsSolicitacoes);
    }
    
}