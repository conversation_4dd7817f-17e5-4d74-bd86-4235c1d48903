<Project Sdk="Microsoft.NET.Sdk">

    <!-- Propriedades específicas do projeto -->
    <PropertyGroup>
        <!-- Propriedades específicas serão herdadas do Directory.Build.props -->
    </PropertyGroup>

    <!-- Pacotes específicos do Solicitacao -->
    <ItemGroup>
        <PackageReference Include="CsvHelper" />
        <PackageReference Include="LinqKit.Core" />
    </ItemGroup>

    <!-- Referências de projeto -->
    <ItemGroup>
      <ProjectReference Include="..\ONS.SINapse.Business.Imp\ONS.SINapse.Business.Imp.csproj" />
      <ProjectReference Include="..\ONS.SINapse.Business\ONS.SINapse.Business.csproj" />
      <ProjectReference Include="..\ONS.SINapse.Shared\ONS.SINapse.Shared.csproj" />
    </ItemGroup>

</Project>
