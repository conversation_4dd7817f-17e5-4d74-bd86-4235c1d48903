using MassTransit.Mediator;
using MongoDB.Driver.Linq;
using ONS.SINapse.Repository.Imp.Context;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Shared.Identity;
using ONS.SINapse.Shared.Messages;
using ONS.SINapse.Solicitacao.QueryHandlers.Dtos;
using ONS.SINapse.Solicitacao.QueryHandlers.Queries;

namespace ONS.SINapse.Solicitacao.QueryHandlers.Handlers;

public class ObterCadastroSolicitacaoPorIdQueryHandler
    : MediatorRequestHandler<BuscarCadastroSolicitacaoPorIdQuery, QueryResult<CadastroDeSolicitacaoDto>>
{
    private readonly IMongoReadOnlyConnection _mongoReadOnlyConnection;
    private readonly IUserContext _userContext;

    public ObterCadastroSolicitacaoPorIdQueryHandler(
        IMongoReadOnlyConnection mongoReadOnlyConnection,
        IUserContext userContext)
    {
        _mongoReadOnlyConnection = mongoReadOnlyConnection;
        _userContext = userContext;
    }
    
    protected override async Task<QueryResult<CadastroDeSolicitacaoDto>> Handle(BuscarCadastroSolicitacaoPorIdQuery request, CancellationToken cancellationToken)
    {
        var result = new QueryResult<CadastroDeSolicitacaoDto>();
        
        var solicitacao =
            await _mongoReadOnlyConnection.ObterQueryable<Entities.Entities.Solicitacao>()
                .FirstOrDefaultAsync(request.ObterFiltro(), cancellationToken);

        if (solicitacao is null)
        {
            result.AdicionarErro("A solicitação informada não existe.");
            return result;
        }
        
        var isSolicitante = solicitacao.IsSolicitante(_userContext.Perfil);
        var isDestinatario = solicitacao.IsDestinatario(_userContext.Perfil); 
        
        if(!isSolicitante && !isDestinatario)
        {
            result.AdicionarErro("Você não tem permissão para acessar os detalhes dessa solicitação.");
            return result;
        }

        result.Result = new CadastroDeSolicitacaoDto(solicitacao);
        result.Result.IsSolicitante = isSolicitante;
        result.Result.IsDestinatario = isDestinatario;

        return result;
    }
}