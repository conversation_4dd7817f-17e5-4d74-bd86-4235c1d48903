using MassTransit.Mediator;
using ONS.SINapse.Repository.Imp.Context;
using ONS.SINapse.Repository.Imp.Repositories.Pipelines;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Identity;
using ONS.SINapse.Shared.Messages;
using ONS.SINapse.Shared.Services.Caching;
using ONS.SINapse.Solicitacao.QueryHandlers.Queries;

namespace ONS.SINapse.Solicitacao.QueryHandlers.Handlers;

public class BuscarDestinatariosDeSolicitacaoQueryHandler : MediatorRequestHandler<BuscarDestinatariosDeSolicitacaoQuery, QueryResult<IEnumerable<DestinatarioDeSolicitacaoDto>>>
{
    private readonly IUserContext _userContext;
    private readonly IMongoReadOnlyConnection _mongoReadOnlyConnection;

    public BuscarDestinatariosDeSolicitacaoQueryHandler(
        IUserContext userContext, 
        IMongoReadOnlyConnection mongoReadOnlyConnection,
        ICacheService cacheService)
    {
        _userContext = userContext;
        _mongoReadOnlyConnection = mongoReadOnlyConnection;
    }
    
    protected override async Task<QueryResult<IEnumerable<DestinatarioDeSolicitacaoDto>>> Handle(BuscarDestinatariosDeSolicitacaoQuery request, CancellationToken cancellationToken)
    {
        var result = new QueryResult<IEnumerable<DestinatarioDeSolicitacaoDto>>();
    
        var centros = _userContext.Perfil.Centros.Distinct().ToArray();

        var pipeline = SolicitacaoPipeline.AgentesDeSolicitacoesPorCentro(centros);

        var dados =
            new List<DestinatarioDeSolicitacaoDto>(
            await _mongoReadOnlyConnection
                .AggregateAsync<DestinatarioDeSolicitacaoDto>(pipeline, cancellationToken)
                .ConfigureAwait(false));

        result.Result = dados;
        
        return result;
    }
}