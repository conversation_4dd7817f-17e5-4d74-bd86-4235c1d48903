using MassTransit.Mediator;
using MongoDB.Driver.Linq;
using ONS.SINapse.Repository.Imp.Context;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Identity;
using ONS.SINapse.Shared.Messages;
using ONS.SINapse.Solicitacao.QueryHandlers.Queries;

namespace ONS.SINapse.Solicitacao.QueryHandlers.Handlers;

public class BuscarCentrosDeOperacaoEmitentesDeSolicitacaoQueryHandler : MediatorRequestHandler<BuscarCentrosDeOperacaoEmitentesDeSolicitacaoQuery, QueryResult<List<CentroDeOperacaoDto>>>
{
    private readonly IUserContext _userContext;
    private readonly IMongoReadOnlyConnection _mongoReadOnlyConnection;

    public BuscarCentrosDeOperacaoEmitentesDeSolicitacaoQueryHandler(IUserContext userContext,
        IMongoReadOnlyConnection mongoReadOnlyConnection)
    {
        _userContext = userContext;
        _mongoReadOnlyConnection = mongoReadOnlyConnection;
    }
    
    protected override async Task<QueryResult<List<CentroDeOperacaoDto>>> Handle(BuscarCentrosDeOperacaoEmitentesDeSolicitacaoQuery request, CancellationToken cancellationToken)
    {
        var result = new QueryResult<List<CentroDeOperacaoDto>>();

        var filtro = BuscarCentrosDeOperacaoEmitentesDeSolicitacaoQuery.FiltroPerfilUsuario(_userContext.Perfil);
        
        var countQueryableSolicitacoes = _mongoReadOnlyConnection
            .ObterQueryable<Entities.Entities.Solicitacao>()
            .Where(filtro)
            .GroupBy(p => p.Origem)
            .Select(g => new { Centro = g.Key, Count = g.Count() });

        var dados = await countQueryableSolicitacoes.ToListAsync(cancellationToken).ConfigureAwait(false);

        result.Result = dados.Select(g => new CentroDeOperacaoDto
            {
                Codigo = g.Centro.Codigo,
                Nome = g.Centro.Nome,
            })
            .DistinctBy(c => c.Codigo)
            .ToList();

        return result;
    }
}