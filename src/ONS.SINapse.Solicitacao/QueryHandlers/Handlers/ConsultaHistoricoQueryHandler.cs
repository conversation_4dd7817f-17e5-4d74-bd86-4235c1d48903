using System.Linq.Expressions;
using MassTransit;
using Microsoft.Extensions.Options;
using MongoDB.Driver.Linq;
using ONS.SINapse.Entities.ValueObjects;
using ONS.SINapse.Repository.Imp.Context;
using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Shared.Identity;
using ONS.SINapse.Shared.Messages;
using ONS.SINapse.Shared.Services.Caching;
using ONS.SINapse.Shared.Settings;
using ONS.SINapse.Solicitacao.QueryHandlers.Dtos;
using ONS.SINapse.Solicitacao.QueryHandlers.Queries;

namespace ONS.SINapse.Solicitacao.QueryHandlers.Handlers;

public class ConsultaHistoricoQueryHandler : IConsumer<ConsultaHistoricoSolicitacaoQuery>
{
    private readonly IMongoReadOnlyConnection _mongoReadOnlyConnection;
    private readonly IUserContext _userContext;
    private readonly ConfiguracaoDeSistemaSettings _configuracaoDeSistemaSettings;
    private readonly QueryResult<Paging<ConsultaHistoricoSolicitacaoDto>> _result;
    
    public ConsultaHistoricoQueryHandler(
        IMongoReadOnlyConnection mongoReadOnlyConnection, 
        IOptions<ConfiguracaoDeSistemaSettings> configuracaoDeSistemaSettings,
        IUserContext userContext,
        ICacheService cacheService)
    {
        _mongoReadOnlyConnection = mongoReadOnlyConnection;
        _userContext = userContext;
        _configuracaoDeSistemaSettings = configuracaoDeSistemaSettings.Value;
        _result = new QueryResult<Paging<ConsultaHistoricoSolicitacaoDto>>(Paging<ConsultaHistoricoSolicitacaoDto>.Empty());
    }
    
    public async Task Consume(ConsumeContext<ConsultaHistoricoSolicitacaoQuery> context)
    {
        var request = context.Message;
        var limiteDias = _configuracaoDeSistemaSettings.QuantidadeDeDiasLimiteParaConsultaDoHistoricoDeSolicitacoes;
        
        var filter = request.Filtro();
        var filterDatas = request.FiltroDatas(limiteDias);
        
        ValidarLimiteDias(request.PeriodoInicial!.Value, request.PeriodoFinal!.Value, limiteDias);

        if (!_result.ValidationResult.IsValid)
        {
            await context
                .RespondAsync(_result)
                .ConfigureAwait(false);
            
            return;
        }
        
        filter.And(ConsultaHistoricoSolicitacaoQuery.FiltroPerfilUsuario(_userContext.Perfil));
        filter.And(filterDatas);

        var dados = await ObterDoMongoAsync(filter, request.Pagina, request.Limite, context.CancellationToken);

        _result.Result = new Paging<ConsultaHistoricoSolicitacaoDto>(dados, request.Pagina); 
        
        await context
            .RespondAsync(_result)
            .ConfigureAwait(false);
    }
   
    private async Task<List<ConsultaHistoricoSolicitacaoDto>> ObterDoMongoAsync(
        Expression<Func<ConsultaHistoricoSolicitacaoDto, bool>> filtro,
        int pagina,
        int limite,
        CancellationToken cancellationToken)
    {
        var query =
            _mongoReadOnlyConnection
                .ObterQueryable<ConsultaHistoricoSolicitacaoDto>()
                .Where(filtro)
                .OrderByDescending(x => x.DataDeCriacao)
                .Paginate(pagina, limite);

        return await query.ToListAsync(cancellationToken).ConfigureAwait(false) 
                    ?? new List<ConsultaHistoricoSolicitacaoDto>();
    }
    
    private void ValidarLimiteDias(DateTime inicio, DateTime fim, int limiteDias)
    {
        var mensagem = $"O intervalo máximo entre o Periodo Inicial e Final é de {limiteDias} dias";
        if (!inicio.IsValid() || !fim.IsValid())
        {
            _result.AdicionarErro(mensagem);
            return;
        }
        
        var intervalo = (fim - inicio).Days;

        if(intervalo > limiteDias) _result.AdicionarErro(mensagem);
    }
    
}