using MassTransit;
using MongoDB.Bson;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Repository.Imp.Context;
using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Shared.Identity;
using ONS.SINapse.Shared.Messages;
using ONS.SINapse.Solicitacao.Helpers;
using ONS.SINapse.Solicitacao.QueryHandlers.Queries;

namespace ONS.SINapse.Solicitacao.QueryHandlers.Handlers;

public class ConsultaHistoricoSolicitacaoAnaliticaQueryHandler : IConsumer<ConsultaHistoricoSolicitacaoAnaliticaQuery>
{
    private readonly IMongoReadOnlyConnection _mongoReadOnlyConnection;
    private readonly IUserContext _userContext;
    private readonly QueryResult<ConsultaAnaliticaDeSolicitacaoDto> _result;
    
    public ConsultaHistoricoSolicitacaoAnaliticaQueryHandler(IMongoReadOnlyConnection mongoReadOnlyConnection, IUserContext userContext)
    {
        _mongoReadOnlyConnection = mongoReadOnlyConnection;
        _userContext = userContext;
        _result = new QueryResult<ConsultaAnaliticaDeSolicitacaoDto>(new ConsultaAnaliticaDeSolicitacaoDto());
    }
    
    public async Task Consume(ConsumeContext<ConsultaHistoricoSolicitacaoAnaliticaQuery> context)
    {
        var request = context.Message;
        var codigosDeStatus = SolicitacaoHelper.ObterStatusFinaisDeSolitacaoPorPerfil(_userContext.Perfil).Select(x => (int)x.GetCode()).ToArray();
        var pipe = ConsultaHistoricoSolicitacaoAnaliticaPipeLine.Pipeline(request, codigosDeStatus);

        var dados = await _mongoReadOnlyConnection
            .AggregateAsync<HistoricoConsultaAnaliticaDto>(pipe, context.CancellationToken);

        _result.Result = new ConsultaAnaliticaDeSolicitacaoDto(request, dados.ToList());
        
        await context.RespondAsync(_result).ConfigureAwait(false);
    }

    private static class ConsultaHistoricoSolicitacaoAnaliticaPipeLine
    {
        public static BsonDocument[] Pipeline(ConsultaHistoricoSolicitacaoAnaliticaQuery query, int[] codigosDeStatus)
        {
            var filtro = Filtro(query, codigosDeStatus);
            
            return new[]
            {
                new BsonDocument { { "$match", filtro } },
                new BsonDocument
                {
                    { "$unwind", "$list_historicostatus" }
                },
                new BsonDocument
                {
                    {
                        "$project", new BsonDocument
                        {
                            { "Codigo", "$_id" },
                            { "Origem", "$obj_origem.nom_local" },
                            { "Destino", "$obj_destino.nom_local" },
                            { "Mensagem", "$dsc_mensagem" },
                            { "DataDeCadastro", "$din_criacao" },
                            { "UsuarioDeCadastro", "$obj_usuario.nom_usuario" },
                            { "CodigoStatus", "$list_historicostatus.cod_status" },
                            { "DataDaAlteracaoDeStatus", "$list_historicostatus.din_alteracao" },
                            { "UsuarioDaAlteracaoDeStatus", "$list_historicostatus.obj_usuario.nom_usuario" },
                            { "InformacaoAdicional", "$dsc_informacaoadicional" },
                            { "impedimento", "$dsc_detalheimpedimento" },
                            {
                                "mensagem_correspondente", new BsonDocument
                                {
                                    {
                                        "$arrayElemAt", new BsonArray
                                        {
                                            new BsonDocument
                                            {
                                                {
                                                    "$filter", new BsonDocument
                                                    {
                                                        { "input", "$list_chat" },
                                                        { "as", "mensagem" },
                                                        {
                                                            "cond", new BsonDocument
                                                            {
                                                                {
                                                                    "$eq",
                                                                    new BsonArray
                                                                    {
                                                                        "$$mensagem.cod_status",
                                                                        "$list_historicostatus.cod_status"
                                                                    }
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            },
                                            0
                                        }
                                    }
                                }
                            }
                        }
                    }
                },
                new BsonDocument
                {
                    {
                        "$addFields", new BsonDocument
                        {
                            {
                                "MotivoImpedimento", new BsonDocument
                                {
                                    {
                                        "$cond", new BsonDocument
                                        {
                                            {
                                                "if",
                                                new BsonDocument { { "$eq", new BsonArray { "$CodigoStatus", 3 } } }
                                            },
                                            { "then", "$impedimento" },
                                            { "else", BsonNull.Value }
                                        }
                                    }
                                }
                            }
                        }
                    }
                },
                new BsonDocument
                {
                    {
                        "$project", new BsonDocument
                        {
                            { "Codigo", 1 },
                            { "Origem", 1 },
                            { "Destino", 1 },
                            { "Mensagem", 1 },
                            { "DataDeCadastro", 1 },
                            { "UsuarioDeCadastro", 1 },
                            { "CodigoStatus", 1 },
                            { "DataDaAlteracaoDeStatus", 1 },
                            { "UsuarioDaAlteracaoDeStatus", 1 },
                            {
                                "EntregueParaOUsuario", new BsonDocument
                                {
                                    {
                                        "$ifNull",
                                        new BsonArray
                                        {
                                            "$mensagem_correspondente.obj_usuarioentrega.obj_usuario.nom_usuario",
                                            BsonNull.Value
                                        }
                                    }
                                }
                            },
                            {
                                "DataDeEntrega", new BsonDocument
                                {
                                    {
                                        "$ifNull",
                                        new BsonArray
                                        {
                                            "$mensagem_correspondente.obj_usuarioentrega.din_registro", BsonNull.Value
                                        }
                                    }
                                }
                            },
                            {
                                "LidaPeloUsuario", new BsonDocument
                                {
                                    {
                                        "$ifNull",
                                        new BsonArray
                                        {
                                            "$mensagem_correspondente.obj_usuarioleitura.obj_usuario.nom_usuario",
                                            BsonNull.Value
                                        }
                                    }
                                }
                            },
                            {
                                "DataDeLeitura", new BsonDocument
                                {
                                    {
                                        "$ifNull",
                                        new BsonArray
                                        {
                                            "$mensagem_correspondente.obj_usuarioleitura.din_registro", BsonNull.Value
                                        }
                                    }
                                }
                            },
                            { "MotivoImpedimento", 1 }
                        }
                    }
                }
            };
        }
        
        private static BsonDocument Filtro(ConsultaHistoricoSolicitacaoAnaliticaQuery query, int[] codigosDeStatus)
        {
            var filtro = new BsonDocument();

            if (!string.IsNullOrEmpty(query.Origem))
                filtro.Add("obj_origem.cod_local", query.Origem);
        
            if (!string.IsNullOrEmpty(query.Destino))
                filtro.Add("obj_destino.cod_local", query.Destino);
        
            if (query.PeriodoInicial.IsValid() || query.PeriodoFinal.IsValid())
            {
                var periodo = new BsonDocument();

                if (query.PeriodoInicial.IsValid())
                    periodo.Add("$gte", query.PeriodoInicial!.Value);
            
                if(query.PeriodoFinal.IsValid())
                    periodo.Add("$lte", query.PeriodoFinal!.Value);

                filtro.Add("din_criacao", periodo);
            }
        
            if (!string.IsNullOrEmpty(query.Mensagem))
            {
                var regex = new BsonDocument
                {
                    { "$regex", query.Mensagem.RemoverCaracteresEspeciais().ToLower() }
                };
            
                filtro.Add("dsc_mensagemnormalizada", regex);
            }
        
            if (query.Status is not null)
            {
                var status = (int)query.Status;
                filtro.Add("cod_status", status);
            } else {
                filtro.Add("cod_status", new BsonDocument("$in", new BsonArray(codigosDeStatus)));
            }
        
            if (query.PossuiImpedimento is not null)
            {
                if (query.PossuiImpedimento.Value)
                {
                    filtro.Add("list_historicostatus.cod_status", StatusDeSolicitacao.Impedida.GetCode());
                }
                else
                {
                    var semImpedimento = new BsonDocument
                    {
                        { "$ne",  StatusDeSolicitacao.Impedida.GetCode()}
                    };
                    filtro.Add("list_historicostatus.cod_status", semImpedimento);
                }
            }

            return filtro;
        }
    }
}
