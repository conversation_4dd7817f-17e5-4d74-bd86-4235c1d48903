using AutoMapper;
using MassTransit.Mediator;
using MongoDB.Driver.Linq;
using ONS.SINapse.Repository.Imp.Context;
using ONS.SINapse.Shared.Messages;
using ONS.SINapse.Solicitacao.Dtos;
using ONS.SINapse.Solicitacao.QueryHandlers.Dtos;
using ONS.SINapse.Solicitacao.QueryHandlers.Queries;

namespace ONS.SINapse.Solicitacao.QueryHandlers.Handlers;

public class ObterCadastroSolicitacaoStatusPorIdQueryHandler
    : MediatorRequestHandler<BuscarCadastroSolicitacaoStatusPorIdQuery, QueryResult<StatusDeSolicitacaoIntegracaoDto>>
{
    private readonly IMongoReadOnlyConnection _mongoReadOnlyConnection;
    private readonly IMapper _mapper;

    public ObterCadastroSolicitacaoStatusPorIdQueryHandler(
        IMongoReadOnlyConnection mongoReadOnlyConnection,
        IMapper mapper)
    {
        _mongoReadOnlyConnection = mongoReadOnlyConnection;
        _mapper = mapper;
    }

    protected override async Task<QueryResult<StatusDeSolicitacaoIntegracaoDto>> Handle(BuscarCadastroSolicitacaoStatusPorIdQuery request, CancellationToken cancellationToken)
    {
        var result = new QueryResult<StatusDeSolicitacaoIntegracaoDto>();

        var solicitacao =
            await _mongoReadOnlyConnection.ObterQueryable<CadastroSolicitacaoStatusDto>()
                .FirstOrDefaultAsync(request.ObterFiltro(), cancellationToken);

        if (solicitacao is null)
        {
            result.AdicionarErro("A solicitação informada não existe.");
            return result;
        }

        result.Result = _mapper.Map<StatusDeSolicitacaoIntegracaoDto>(solicitacao);

        return result;
    }
}