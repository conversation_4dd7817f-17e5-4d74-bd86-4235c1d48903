using AutoMapper;
using MassTransit.Mediator;
using MongoDB.Driver.Linq;
using ONS.SINapse.Repository.Imp.Context;
using ONS.SINapse.Repository.IRepository.InMemory;
using ONS.SINapse.Shared.Messages;
using ONS.SINapse.Solicitacao.Dtos;
using ONS.SINapse.Solicitacao.QueryHandlers.Dtos;
using ONS.SINapse.Solicitacao.QueryHandlers.Queries;

namespace ONS.SINapse.Solicitacao.QueryHandlers.Handlers;

public class ObterCadastroSolicitacaoStatusQueryHandler
    : MediatorRequestHandler<BuscarCadastroSolicitacaoStatusQuery, QueryResult<List<StatusDeSolicitacaoIntegracaoDto>>>
{
    private readonly IMongoReadOnlyConnection _mongoReadOnlyConnection;
    private readonly IMapper _mapper;
    private readonly ISolicitacaoMemoryRepository _solicitacaoMemoryRepository;

    public ObterCadastroSolicitacaoStatusQueryHandler(
        IMongoReadOnlyConnection mongoReadOnlyConnection,
        ISolicitacaoMemoryRepository solicitacaoMemoryRepository,
        IMapper mapper)
    {
        _mongoReadOnlyConnection = mongoReadOnlyConnection;
        _mapper = mapper;
        _solicitacaoMemoryRepository = solicitacaoMemoryRepository;
    }

    protected override async Task<QueryResult<List<StatusDeSolicitacaoIntegracaoDto>>> Handle(BuscarCadastroSolicitacaoStatusQuery request, CancellationToken cancellationToken)
    {
        var result = new QueryResult<List<StatusDeSolicitacaoIntegracaoDto>>();

        var solicitacoesMongo = await 
             _mongoReadOnlyConnection.ObterQueryable<CadastroSolicitacaoStatusDto>()
                .Where(request.ObterFiltro())
                .ToListAsync(cancellationToken) ?? [];
        var solicitacoes = _solicitacaoMemoryRepository.GetList(x => request.Solicitacoes.Contains(x.Id)).ToList();

        if (solicitacoesMongo.Count == 0 && solicitacoes.Count == 0)
        {
            result.AdicionarErro("A solicitação informada não existe.");
            return result;
        }
        
        solicitacoesMongo.AddRange(solicitacoes.Select(s => new CadastroSolicitacaoStatusDto
        {
            Id = s.Id,
            HistoricosDeStatus = s.HistoricosDeStatus
        }));
        
        result.Result = _mapper.Map<List<StatusDeSolicitacaoIntegracaoDto>>(solicitacoesMongo);

        SolicitacoesNaoEncontradas(request.Solicitacoes, result.Result);

        return result;
    }

    private static void SolicitacoesNaoEncontradas(List<string> codigoSolicitacao,
        List<StatusDeSolicitacaoIntegracaoDto> solicitacoesOutputDtos)
    {
        var codigos = solicitacoesOutputDtos.Select(s => s.Id);
        var codigosExterno = solicitacoesOutputDtos.Select(s => s.Id);

        var codigosNaoEncontrados = codigoSolicitacao
            .Distinct()
            .Where(codigo => !codigos.Contains(codigo) && !codigosExterno.Contains(codigo));

        solicitacoesOutputDtos
            .AddRange(codigosNaoEncontrados.Select(codigo =>
            {
                var status = new StatusDeSolicitacaoIntegracaoDto(codigo, []);
                status.AdicionarErro($"Solicitação de código {codigo} não encontrada.");
                return status;
            }).ToList());
    }
}