using ONS.SINapse.Integracao.Shared.Enums;

namespace ONS.SINapse.Solicitacao.QueryHandlers.Dtos;

public class ConsultaHistoricoDto
{
    public string? Origem { get; set; }

    public string? <PERSON><PERSON> { get; set; }

    public string? Mensagem { get; set; }

    public StatusDeSolicitacao? Status { get; set; }

    public DateTimeOffset? PeriodoInicial { get; set; }

    public DateTimeOffset? PeriodoFinal { get; set; }

    public int Pagina { get; set; } = 1;

    public int Limite { get; set; } = 50;
}