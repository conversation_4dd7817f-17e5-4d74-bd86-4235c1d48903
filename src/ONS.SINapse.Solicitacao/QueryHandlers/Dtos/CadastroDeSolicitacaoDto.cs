using ONS.SINapse.Entities.Entities;

namespace ONS.SINapse.Solicitacao.QueryHandlers.Dtos;

public class CadastroDeSolicitacaoDto
{
    public CadastroDeSolicitacaoDto(Entities.Entities.Solicitacao solicitacao) 
    {
        Solicitacao = solicitacao;
    }

    public Entities.Entities.Solicitacao Solicitacao { get; set; }
    public bool IsSolicitante { get; set; }
    public bool IsDestinatario { get; set; }
}