using System.Text.Json.Serialization;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Shared.CustomAttributes;
using ONS.SINapse.Shared.Extensions;

namespace ONS.SINapse.Solicitacao.QueryHandlers.Dtos;

[BsonCollection("c_solicitacao")]
public class ConsultaHistoricoSolicitacaoDto : IBsonCollection
{
    public ConsultaHistoricoSolicitacaoDto(string id, ConsultaHistoricoSolicitacaoObjetoManobraDto origem, ConsultaHistoricoSolicitacaoObjetoManobraDto destino, string mensagem, string mensagemNormalizada)
    {
        Id = id;
        Origem = origem;
        Destino = destino;
        Mensagem = mensagem;
        MensagemNormalizada = mensagemNormalizada;
    }

    public string Id { get; set; }
    public string Codigo => Id;
    
    [BsonElement("obj_origem")]
    public ConsultaHistoricoSolicitacaoObjetoManobraDto Origem { get; set; }
    
    [BsonElement("obj_destino")]
    public ConsultaHistoricoSolicitacaoObjetoManobraDto Destino { get; set; }
    
    [BsonElement("dsc_mensagem")]
    public string Mensagem { get; set; }
    
    [BsonElement("dsc_informacaoadicional")]
    public string? InformacaoAdicional { get; set; }
    
    public string Responsavel => UltimaAtualizacao?.Usuario.Nome ?? string.Empty;

    public ConsultaHistoricoSolicitacaoStatusDto Status => InternalStatus();

    public DateTime DataDeAtualizacao => UltimaAtualizacao?.DataDeAlteracao ?? DataDeCriacao;
    
    [BsonElement("din_criacao")]
    public DateTime DataDeCriacao { get; set; }
    
    [BsonElement("flg_finalizadaautomaticamente")]
    public bool FinalizadaAutomaticamente { get; set; }

    public string Duracao => InternalDuracao();

    [BsonElement("dsc_mensagemnormalizada")]
    [JsonIgnore]
    public string MensagemNormalizada { get; set; }
    
    [BsonElement("obj_encaminharagente")]
    [JsonIgnore]
    public ConsultaHistoricoSolicitacaoObjetoManobraDto? Agente { get; set; }
    
    [BsonElement("cod_status")]
    [JsonIgnore]
    public int CodigoStatus { get; set; }
    
    [BsonElement("list_historicostatus")]
    [JsonIgnore]
    public List<ConsultaHistoricoSolicitacaoHistoricoDto>? Historicos { get; set; }
    
    [JsonIgnore]
    public ConsultaHistoricoSolicitacaoHistoricoDto? UltimaAtualizacao => Historicos?.MaxBy(h => h.DataDeAlteracao);
    
    private string InternalDuracao()
    {
        var dataAtualizacao = UltimaAtualizacao?.DataDeAlteracao ?? DataDeCriacao;
        var duracao = dataAtualizacao - DataDeCriacao;
        return duracao.ToString(@"hh\:mm\:ss");
    }

    private ConsultaHistoricoSolicitacaoStatusDto InternalStatus()
    {
        var status = (StatusDeSolicitacao?)CodigoStatus;
        return new ConsultaHistoricoSolicitacaoStatusDto(status.GetCode(), status.GetDescription());
    }
}


[BsonNoId]
public record ConsultaHistoricoSolicitacaoStatusDto(short Codigo, string Descricao);

[BsonNoId]
public record ConsultaHistoricoSolicitacaoHistoricoDto
{
    public ConsultaHistoricoSolicitacaoHistoricoDto(ConsultaHistoricoSolicitacaoUsuarioDto usuario)
    { 
        Usuario = usuario;
    }

    [BsonElement("din_alteracao")]
    public DateTime DataDeAlteracao { get; set; }
    
    [BsonElement("obj_usuario")] 
    public ConsultaHistoricoSolicitacaoUsuarioDto Usuario { get; set; }
}


[BsonNoId]
public record ConsultaHistoricoSolicitacaoUsuarioDto
{
    public ConsultaHistoricoSolicitacaoUsuarioDto(string nome)
    { 
        Nome = nome;
    }

    [BsonElement("nom_usuario")]
    public string Nome { get; set; }
}
