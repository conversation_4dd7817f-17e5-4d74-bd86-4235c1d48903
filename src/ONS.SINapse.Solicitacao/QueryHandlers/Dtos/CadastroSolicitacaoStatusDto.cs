using MongoDB.Bson.Serialization.Attributes;
using ONS.SINapse.Entities.Entities;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Shared.CustomAttributes;

namespace ONS.SINapse.Integracao.Shared.Dtos
{
    [BsonCollection("c_solicitacao")]
    public class CadastroSolicitacaoStatusDto 
    {
        public CadastroSolicitacaoStatusDto()
        {
            CodigoDaSolicitacao = string.Empty;
            CodigoExterno = string.Empty;
            Justificativa = string.Empty;
            HistoricosDeStatus = [];
            Chat = [];
        }

        [BsonElement("_id")]
        public string CodigoDaSolicitacao { get; set; }

        [BsonElement("cod_externo")]
        public string CodigoExterno { get; set; }

        [BsonElement("cod_status")]
        public StatusDeSolicitacao StatusAtualId { get; set; }    

        [BsonElement("dsc_detalheimpedimento")]
        public string Justificativa { get; set; }

        [BsonElement("list_historicostatus")]
        public ICollection<HistoricoDeStatusDeSolicitacao> HistoricosDeStatus { get; set; }

        [BsonElement("list_chat")]
        public List<ChatDeSolicitacao> Chat { get; set; }

        public Usuario? GetUsuarioDoImpedimento()
        {
            return GetUsuarioDaOperacao(StatusDeSolicitacao.Impedida);
        }

        public Usuario? GetUsuarioDaConfirmacao()
        {
            return GetUsuarioDaOperacao(StatusDeSolicitacao.Confirmada);
        }

        public Usuario? GetUsuarioDaOperacao(StatusDeSolicitacao status)
        {
            return HistoricosDeStatus.Where(h => h.Status == status)?.FirstOrDefault()?.Usuario;
        }        
    }
}