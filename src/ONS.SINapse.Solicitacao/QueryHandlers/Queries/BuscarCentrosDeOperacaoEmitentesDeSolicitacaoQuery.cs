using LinqKit;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Identity;
using ONS.SINapse.Shared.Messages;

namespace ONS.SINapse.Solicitacao.QueryHandlers.Queries;

public class BuscarCentrosDeOperacaoEmitentesDeSolicitacaoQuery : Query<List<CentroDeOperacaoDto>>
{
    public static ExpressionStarter<Entities.Entities.Solicitacao> FiltroPerfilUsuario(Perfil perfilDeUsuario)
    {
        var filtro = PredicateBuilder.New<Entities.Entities.Solicitacao>();

        var centros = perfilDeUsuario.Centros;
        
        filtro.And(x =>
            centros.Contains(x.Origem.Codigo) ||
            centros.Contains(x.Destino.Codigo));

        return filtro;
    }
}