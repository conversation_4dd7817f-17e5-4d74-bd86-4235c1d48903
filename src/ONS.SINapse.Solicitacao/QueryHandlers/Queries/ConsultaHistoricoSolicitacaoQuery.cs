using LinqKit;
using ONS.SINapse.Entities.ValueObjects;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Shared.Identity;
using ONS.SINapse.Shared.Messages;
using ONS.SINapse.Solicitacao.Helpers;
using ONS.SINapse.Solicitacao.QueryHandlers.Dtos;

namespace ONS.SINapse.Solicitacao.QueryHandlers.Queries;

public abstract class ConsultaHistoricoSolicitacaoBaseQuery<TResponse> : Query<Paging<TResponse>> where TResponse : class
{
    public string? Origem { get; set; }
    public string? Destino { get; set; }
    public string? Mensagem { get; set; }
    public StatusDeSolicitacao? Status { get; set; }
    public DateTime? PeriodoInicial { get; set; }
    public DateTime? PeriodoFinal { get; set; }
    public int Pagina { get; set; } = 1;
    public int Limite { get; set; } = 50;

    public abstract ExpressionStarter<TResponse> Filtro();

    public override string ToString()
    {
        var toString = nameof(ConsultaHistoricoSolicitacaoBaseQuery<TResponse>);

        if (!string.IsNullOrEmpty(Origem))
            toString += $"|{nameof(Origem)}:{Origem}";

        if (!string.IsNullOrEmpty(Destino))
            toString += $"|{nameof(Destino)}:{Destino}";

        if (!string.IsNullOrEmpty(Mensagem))
            toString += $"|{nameof(Mensagem)}:{Mensagem}";

        if (Status is not null)
            toString += $"|{nameof(Status)}:{Status.GetCode()}";

        if (PeriodoInicial.IsValid())
            toString += $"|{nameof(PeriodoInicial)}:{PeriodoInicial!.Value:ddMMyyyy}";

        if (PeriodoFinal.IsValid())
            toString += $"|{nameof(PeriodoFinal)}:{PeriodoFinal!.Value:ddMMyyyy}";
        
        toString += $"|{nameof(Pagina)}:{Pagina}";
        toString += $"|{nameof(Limite)}:{Limite}";
        
        return toString;
    }
}

public class ConsultaHistoricoSolicitacaoQuery : ConsultaHistoricoSolicitacaoBaseQuery<ConsultaHistoricoSolicitacaoDto>
{
    public override ExpressionStarter<ConsultaHistoricoSolicitacaoDto> Filtro()
    {
        var filtro = PredicateBuilder.New<ConsultaHistoricoSolicitacaoDto>();

        if (!string.IsNullOrEmpty(Origem))
            filtro.And(x => x.Origem.Codigo == Origem);

        if (!string.IsNullOrEmpty(Destino))
            filtro.And(x => x.Destino.Codigo == Destino);

        if (!string.IsNullOrEmpty(Mensagem))
            filtro.And(x => x.MensagemNormalizada.Contains(Mensagem.RemoverCaracteresEspeciais(), StringComparison.CurrentCultureIgnoreCase));

        if (Status is not null)
        {
            var status = (int)Status;
            filtro.And(x => x.CodigoStatus == status);
        }
        else
        {
            var status = new[] { (int)StatusDeSolicitacao.Cancelada, (int)StatusDeSolicitacao.Finalizada, (int)StatusDeSolicitacao.EnvioCancelado, (int)StatusDeSolicitacao.Enviada };
            filtro.And(x => status.Contains(x.CodigoStatus));
        }
        
        return filtro;
    }

    public static ExpressionStarter<ConsultaHistoricoSolicitacaoDto> FiltroPerfilUsuario(Perfil perfil)
    {
        var codigosDeStatus = SolicitacaoHelper
            .ObterStatusFinaisDeSolitacaoPorPerfil(perfil)
            .Select(s => (int)s.GetCode())
            .ToArray();

        var filtro = PredicateBuilder.New<ConsultaHistoricoSolicitacaoDto>();
        filtro.And(x =>
            perfil.Centros.Contains(x.Origem.Codigo) ||
            perfil.Centros.Contains(x.Destino.Codigo));
        
        filtro.And(x => codigosDeStatus.Contains(x.CodigoStatus));
        
        return filtro;
    }

    public ExpressionStarter<ConsultaHistoricoSolicitacaoDto> FiltroDatas(int qtdDiasLimite)
    {
        var filtro = PredicateBuilder.New<ConsultaHistoricoSolicitacaoDto>();

        filtro.And(x => x.DataDeCriacao >= PeriodoInicial!.Value.GetFirstHour());
        filtro.And(x => x.DataDeCriacao < PeriodoFinal!.Value.GetDateTimeLastHour());

        return filtro;
    }
}