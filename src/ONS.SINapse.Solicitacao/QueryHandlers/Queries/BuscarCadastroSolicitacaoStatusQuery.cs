using LinqKit;
using ONS.SINapse.Shared.Messages;
using ONS.SINapse.Solicitacao.QueryHandlers.Dtos;

namespace ONS.SINapse.Solicitacao.QueryHandlers.Queries;

public class BuscarCadastroSolicitacaoStatusQuery : Query<List<Solicitacao.Dtos.StatusDeSolicitacaoIntegracaoDto>>
{
    public BuscarCadastroSolicitacaoStatusQuery(List<string> solicitacoes)
    {
        Solicitacoes = solicitacoes;
    }
    public List<string> Solicitacoes { get; }

    public ExpressionStarter<CadastroSolicitacaoStatusDto> ObterFiltro()
    {
        var filter = PredicateBuilder.New<CadastroSolicitacaoStatusDto>();
        filter.And(x => Solicitacoes.Contains(x.Id) || Solicitacoes.Contains(x.CodigoExterno));
        
        return filter;
    }
}

public class BuscarCadastroSolicitacaoStatusPorIdQuery : Query<Solicitacao.Dtos.StatusDeSolicitacaoIntegracaoDto>
{
    public BuscarCadastroSolicitacaoStatusPorIdQuery(string id)
    {
        Id = id;
    }
    public string Id { get; }

    public ExpressionStarter<CadastroSolicitacaoStatusDto> ObterFiltro()
    {
        var filter = PredicateBuilder.New<CadastroSolicitacaoStatusDto>();
        filter.And(x => x.Id == Id);

        return filter;
    }
}