using System.Text.Json.Serialization;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Shared.CustomAttributes;
using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Shared.Messages;

namespace ONS.SINapse.Solicitacao.QueryHandlers.Queries;

public class ConsultaHistoricoSolicitacaoAnaliticaQuery : Query<ConsultaAnaliticaDeSolicitacaoDto>
{
    public string? Origem { get; set; }
    public string? Destino { get; set; }
    public string? Mensagem { get; set; }
    public StatusDeSolicitacao? Status { get; set; }
    public DateTime? PeriodoInicial { get; set; }
    public DateTime? PeriodoFinal { get; set; }
    public bool? PossuiImpedimento { get; set; }
}

public class ConsultaAnaliticaDeSolicitacaoDto
{
    public ConsultaAnaliticaDeSolicitacaoDto(
        ConsultaHistoricoSolicitacaoAnaliticaQuery filtro, 
        List<HistoricoConsultaAnaliticaDto> resultado)
    {
        Filtro = filtro;
        TotalDeSolicitacoes = resultado.GroupBy(x => x.Codigo).Count();
        _resultado = resultado;
    }

    public ConsultaAnaliticaDeSolicitacaoDto()
    {
        Filtro = new ConsultaHistoricoSolicitacaoAnaliticaQuery();
        TotalDeSolicitacoes = 0;
        _resultado = new List<HistoricoConsultaAnaliticaDto>();
    }
    
    public ConsultaHistoricoSolicitacaoAnaliticaQuery Filtro { get; set; }
    public int TotalDeSolicitacoes { get; set; }
    public int TotalDeItens => Resultado.Count;

    private readonly List<HistoricoConsultaAnaliticaDto> _resultado;
    public IReadOnlyCollection<HistoricoConsultaAnaliticaDto> Resultado => _resultado.AsReadOnly();
}

[BsonCollection("c_solicitacao")]
public class HistoricoConsultaAnaliticaDto
{
    public required string Codigo { get; init; }
    public required string Origem { get; init; }
    public required string Destino { get; init; }
    public required string Mensagem { get; init; }
    public DateTime DataDeCadastro { get; set; }
    public required string UsuarioDeCadastro { get; init; }
    
    [JsonIgnore]
    public int CodigoStatus { get; set; }

    public string StatusDaSolicitacao => ((StatusDeSolicitacao)CodigoStatus).GetDescription();
    
    public DateTime DataDaAlteracaoDeStatus { get; set; }
    public required string UsuarioDaAlteracaoDeStatus { get; init; }
    public required string EntregueParaOUsuario { get; init; }
    public DateTime? DataDeEntrega { get; set; }
    public required string LidaPeloUsuario { get; init; }
    public DateTime? DataDeLeitura { get; set; }
    public required string MotivoImpedimento { get; init; }
    public required string InformacaoAdicional { get; init; }
}