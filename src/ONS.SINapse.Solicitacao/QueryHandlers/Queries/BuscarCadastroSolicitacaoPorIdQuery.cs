using LinqKit;
using ONS.SINapse.Shared.Messages;
using ONS.SINapse.Solicitacao.QueryHandlers.Dtos;

namespace ONS.SINapse.Solicitacao.QueryHandlers.Queries;

public class BuscarCadastroSolicitacaoPorIdQuery : Query<CadastroDeSolicitacaoDto>
{
    public BuscarCadastroSolicitacaoPorIdQuery(string id)
    {
        Id = id;
    }
    public string Id { get; }

    public ExpressionStarter<Entities.Entities.Solicitacao> ObterFiltro()
    {
        var filter = PredicateBuilder.New<Entities.Entities.Solicitacao>();

        filter.And(x => x.Id == Id);
        
        return filter;
    }
}