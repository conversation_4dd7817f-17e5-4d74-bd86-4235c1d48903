using System.Reactive.Disposables;
using System.Reactive.Linq;
using Firebase.Database;
using Firebase.Database.Streaming;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using ONS.SINapse.Repository.Imp.Store;
using ONS.SINapse.Shared.Constants;
using ONS.SINapse.Shared.DTO.Solicitacao;

namespace ONS.SINapse.Solicitacao.Workers.Listener;

public class SolicitacaoFirebaseListener : BackgroundService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<SolicitacaoFirebaseListener> _logger;
    private IDisposable? _subscription;
    private const int ReconnectInterval = 50;

    public SolicitacaoFirebaseListener(IServiceProvider serviceProvider, ILogger<SolicitacaoFirebaseListener> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
    }

    protected override Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("[SolicitacaoFirebaseListener] Serviço iniciado. Conectando ao Firebase a cada {ReconnectInterval} minutos.", ReconnectInterval);

        _subscription = Observable
            .Timer(TimeSpan.Zero, TimeSpan.FromMinutes(ReconnectInterval))
            .Select(_ => Observable.FromAsync(() => ConnectToFirebaseAsync(stoppingToken)))
            .Switch()
            .Subscribe();

        return Task.CompletedTask;
    }

    private async Task<IDisposable> ConnectToFirebaseAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("[SolicitacaoFirebaseListener] Conectando ao Firebase...");

        try
        {
            using var scope = _serviceProvider.CreateScope();
            var firebaseClient = scope.ServiceProvider.GetRequiredService<FirebaseClient>();
            SolicitacaoStore.Clear();
            firebaseClient
                .Child(ColecoesFirebase.Solicitacao)
                .AsObservable<SolicitacaoDto>()
                .Subscribe(OnFirebaseEvent, OnFirebaseError);

            _logger.LogInformation("[SolicitacaoFirebaseListener] Conectado com sucesso ao Firebase.");
            await Task.Delay(Timeout.InfiniteTimeSpan, stoppingToken);
            return Disposable.Empty;    
        }
        catch (TaskCanceledException)
        {
            _logger.LogInformation("[SolicitacaoFirebaseListener] Serviço cancelado.");
            return Disposable.Empty;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[SolicitacaoFirebaseListener] Erro ao conectar ao Firebase.");
            return Disposable.Empty;
        }
    }

    private void OnFirebaseEvent(FirebaseEvent<SolicitacaoDto> d)
    {
        switch (d.EventType)
        {
            case FirebaseEventType.InsertOrUpdate:
                Sync(d.Key, d.Object);
                break;

            case FirebaseEventType.Delete:
                Sync(d.Key);
                break;

            default:
                _logger.LogInformation("[SolicitacaoFirebaseListener] Evento não suportado, chave: {Key}", d.Key);
                break;
        }
    }

    private void Sync(string key, SolicitacaoDto? solicitacao = null)
    {
        if (solicitacao != null)
        {
            var solicitacaoMongo = Entities.Entities.Solicitacao.FromDto(solicitacao);
            SolicitacaoStore.AddOrUpdate(solicitacaoMongo);
            return;
        }

        if (SolicitacaoStore.Remove(key))
        {
            return;
        }

        _logger.LogWarning("[SolicitacaoFirebaseListener] Tentativa de remover chave inexistente: {Key}", key);
    }

    private void OnFirebaseError(Exception ex)
    {
        _logger.LogError(ex, "[SolicitacaoFirebaseListener] Erro na conexão com o Firebase. A conexão será reiniciada.");
    }

    public override void Dispose()
    {
        _logger.LogInformation("[SolicitacaoFirebaseListener] Encerrando escuta de eventos do Firebase");
        _subscription?.Dispose();
        base.Dispose();
        GC.SuppressFinalize(this);
    }
}
