using Microsoft.Extensions.Logging;
using ONS.SINapse.Shared.Kafka.Providers;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Solicitacao.EventHandlers.IntegrationEvents;
using ONS.SINapse.Solicitacao.Workers.Providers.Consumers.Interfaces;

namespace ONS.SINapse.Solicitacao.Workers.Providers.Consumers;

public interface ITrocaDeStatusConsumerProvider : IConsumerProviderBase { }

public class TrocaDeStatusConsumerProvider : ITrocaDeStatusConsumerProvider
{
    private readonly IConsumerProvider<TrocaDeStatusExternaIntegrationEvent> _consumerProvider;
    private readonly IMediatorHandler _mediatorHandler;
    private readonly ILogger<CadastroDeSolicitacaoConsumerProvider> _logger;

    public TrocaDeStatusConsumerProvider(
        IConsumerProvider<TrocaDeStatusExternaIntegrationEvent> consumerProvider,
        IMediatorHand<PERSON> mediatorHandler,
        ILogger<CadastroDeSolicitacaoConsumerProvider> logger)
    {
        _consumerProvider = consumerProvider;
        _mediatorHandler = mediatorHandler;
        _logger = logger;
    }
    
    public Task SubscribeAsync(CancellationToken cancellationToken)
    {
        return _consumerProvider.SubscribeAsync(
            async request =>
                await OnMessage(request, cancellationToken)
            , cancellationToken);
    }

    private async Task OnMessage(TrocaDeStatusExternaIntegrationEvent evento, CancellationToken cancellationToken)
    {
        var task = _mediatorHandler.PublicarEventoAsync(evento, cancellationToken); 
        
        try
        {
            await task;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[CadastroDeSolicitacaoConsumerProvider][OnMessage] - Não foi possivel cadastrar a solicitação no sinapse.");
        }
    }
    

    private bool _disposed;
    protected virtual void Dispose(bool disposing)
    {
        if (_disposed) return;

        if (disposing)
        {
            _consumerProvider.Dispose();
        }

        _disposed = true;
    }
    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }
}