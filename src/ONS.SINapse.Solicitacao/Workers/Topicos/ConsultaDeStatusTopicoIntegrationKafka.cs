using ONS.SINapse.Shared.Kafka;
using ONS.SINapse.Solicitacao.Workers.Messages;

namespace ONS.SINapse.Solicitacao.Workers.Topicos;

public class ConsultaDeStatusTopicoIntegrationKafka : TopicoIntegrationKafka<AtualizacaoStatusMessage>
{
    public ConsultaDeStatusTopicoIntegrationKafka(string topico) : base(topico)
    {
        TopicoAlteracao = topico;
    }
    
    private string TopicoAlteracao { get; set; }
    
    public override string ToString() => TopicoAlteracao;

    public override void AlterarValor(string novoTopico) => TopicoAlteracao = novoTopico;

}