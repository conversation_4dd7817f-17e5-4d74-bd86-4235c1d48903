using ONS.SINapse.Shared.Kafka;
using ONS.SINapse.Solicitacao.EventHandlers.IntegrationEvents;
using ONS.SINapse.Solicitacao.Workers.Messages;

namespace ONS.SINapse.Solicitacao.Workers.Topicos;

public class CadastroDeSolicitacaoTopicoIntegrationKafka : TopicoIntegrationKafka<CadastroDeSolicitacaoExternaRecebidaIntegrationEvent>
{
    public CadastroDeSolicitacaoTopicoIntegrationKafka(string topico) : base(topico)
    {
    }
}


public class CadastrarSolicitacaoTopicoKafka(string topico) : TopicoIntegrationKafka<CadastroSolicitacaoMessage>(topico);
