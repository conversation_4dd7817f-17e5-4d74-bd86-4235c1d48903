using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Kafka;

namespace ONS.SINapse.Solicitacao.Workers.Messages;

public record CadastroSolicitacaoMessage : IIntegrationKafka
{
    public ObjetoDeManobraDto? Origem { get; set; }
    public ObjetoDeManobraDto? Destino { get; set; }
    public ObjetoDeManobraDto? Local { get; set; }
    public ObjetoDeManobraDto? EncaminharPara { get; set; }
    public string? InformacaoAdicional { get; set; }
    public string? Mensagem { get; set; }
    public string? SistemaDeOrigem { get; set; }
    public string? CodigoExterno { get; set; }
    public string? Motivo { get; set; }
    public List<string>? Tags { get; set; } = [];
    public UsuarioDto? Usuario { get; set; }

    public static CadastroSolicitacaoMessage Copy(CadastroSolicitacaoMessage cadastroSolicitacaoMessage) =>
        new()
        {
            Origem = cadastroSolicitacaoMessage.Origem,
            Destino = cadastroSolicitacaoMessage.Destino,
            Local = cadastroSolicitacaoMessage.Local,
            CodigoExterno = cadastroSolicitacaoMessage.CodigoExterno,
            Motivo = cadastroSolicitacaoMessage.Motivo,
            EncaminharPara = cadastroSolicitacaoMessage.EncaminharPara,
            Mensagem = cadastroSolicitacaoMessage.Mensagem,
            Tags = cadastroSolicitacaoMessage.Tags,
            InformacaoAdicional = cadastroSolicitacaoMessage.InformacaoAdicional,
            SistemaDeOrigem = cadastroSolicitacaoMessage.SistemaDeOrigem,
            Usuario = cadastroSolicitacaoMessage.Usuario,
        };

    public CadastroSolicitacaoMessage Copy() => Copy(this);
}