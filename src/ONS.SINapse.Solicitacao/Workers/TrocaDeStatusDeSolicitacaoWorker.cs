using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using ONS.SINapse.Shared.Settings;
using ONS.SINapse.Solicitacao.Workers.Providers.Consumers;

namespace ONS.SINapse.Solicitacao.Workers;

public class TrocaDeStatusDeSolicitacaoWorker : BackgroundService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<TrocaDeStatusDeSolicitacaoWorker> _logger;
    private readonly KafkaSettings _options;

    public TrocaDeStatusDeSolicitacaoWorker(
        IServiceProvider serviceProvider, 
        ILogger<TrocaDeStatusDeSolicitacaoWorker> logger,
        IOptions<KafkaSettings> options)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
        _options = options.Value;
    }
    
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("Worker TrocaDeStatusDeSolicitacaoWorker iniciado.");

        if (!_options.Ativo)
        {
            _logger.LogWarning(
                "Kafka está com flag inativo worker de troca de status via kafka está parando. Para ativa-lo novamente, coloque a flag ativo para true e faça deploy da aplicação novamente.");
            
            await StopAsync(stoppingToken);
            return;
        }
        
        try
        {
            using var scope = _serviceProvider.CreateScope();
            using var provider = scope.ServiceProvider.GetRequiredService<ITrocaDeStatusConsumerProvider>();
            
            while (!stoppingToken.IsCancellationRequested)
            {
                await provider.SubscribeAsync(stoppingToken);
            }
        }
        catch (OperationCanceledException e)
        {
            _logger.LogError(e, "A operação foi cancelada serviço worker parando.");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex,
                "Erro durante a execução do processo, serviço worker de troca de status será parado.");
        }
    }

    public override Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogWarning("Worker de troca de status via kafka está parando.");
        return base.StopAsync(cancellationToken);
    }
}