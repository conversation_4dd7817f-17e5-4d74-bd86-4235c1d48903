using Firebase.Database;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using ONS.SINapse.Shared.Constants;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Shared.Settings;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;

namespace ONS.SINapse.Solicitacao.Workers;

public class FinalizarSolicitacaoAutomaticamenteWorker(
    IServiceProvider serviceProvider,
    IOptions<ApplicationSettings> optionsSettings,
    ILogger<FinalizarSolicitacaoAutomaticamenteWorker> logger
) : BackgroundService
{
    protected override Task ExecuteAsync(CancellationToken stoppingToken)
    {
        logger.LogInformation("[FinalizarSolicitacaoAutomaticamenteWorker] Worker iniciado.");
        
        try
        {
            var applicationVersion = optionsSettings.Value.Version;
            using var scope = serviceProvider.CreateScope();
            using var firebaseClient = scope.ServiceProvider.GetRequiredService<FirebaseClient>();
            var mediatorHandler = scope.ServiceProvider.GetRequiredService<IMediatorHandler>();
            var firebaseVersion = firebaseClient
                .Child(ColecoesFirebase.Version)
                .OnceSingleAsync<string>().Result;
            logger.LogInformation("Versão do Firebase: {FirebaseVersion}", firebaseVersion);
            
            if (applicationVersion == firebaseVersion)
            {
                return Task.CompletedTask;
            }

            var command = new FinalizarAutomaticamenteCommand(true);
            
            logger.LogInformation("Versão do Firebase diferente da versão da aplicação, iniciando rotina de finalização automática.");
            
            return mediatorHandler.EnviarComandoAsync(command, stoppingToken);
        }
        catch (Exception e)
        {
            logger.LogError(e, "[FinalizarSolicitacaoAutomaticamenteWorker] Erro ao finalizar solicitações automaticamente.");    
        }
        return Task.CompletedTask;
    }
}