using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Messages;
using ONS.SINapse.Solicitacao.Dtos;

namespace ONS.SINapse.Solicitacao.EventHandlers.Events;

public class CienciaInformadaEvent : Event
{
    public CienciaInformadaEvent(CienciaInformadaDto solicitacao)
    {
        Solicitacao = solicitacao;
    }
    
    public CienciaInformadaDto Solicitacao { get; private set; }
}


public class CienciaInformadaComErroEvent : Event
{
    public CienciaInformadaComErroEvent(CienciaInformadaDto solicitacao)
    {
        Solicitacao = solicitacao;
    }
    
    public CienciaInformadaDto Solicitacao { get; private set; }

    public static CienciaInformadaComErroEvent CienciaInformadaComErro(string codigo, string mensagem,
        string? codigoExterno = null, UsuarioDto? usuarioDto = null)
    {
        var status = new CienciaInformadaDto(codigo, codigoExterno, string.Empty,
            new List<HistoricoAlteracaoStatusDto>(), new List<string>());
            
        status.AdicionarErro(mensagem, usuarioDto ?? new UsuarioDto());
        return new CienciaInformadaComErroEvent(status);
    }
}


public class CienciaInformadaDto : StatusSolicitacaoDto
{
    public CienciaInformadaDto(string codigo, string? codigoExterno, string sistemaDeOrigem, List<HistoricoAlteracaoStatusDto> historico, List<string> erros) 
        : base(codigo, codigoExterno, sistemaDeOrigem, StatusDeSolicitacao.CienciaInformada, historico, erros)
    {
    }
}
