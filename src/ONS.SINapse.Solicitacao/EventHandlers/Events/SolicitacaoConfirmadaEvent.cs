using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Messages;
using ONS.SINapse.Solicitacao.Dtos;

namespace ONS.SINapse.Solicitacao.EventHandlers.Events;

public class SolicitacaoConfirmadaEvent : Event
{
    public SolicitacaoConfirmadaEvent(SolicitacaoConfirmadaDto solicitacao)
    {
        SolicitacaoConfirmada = solicitacao;
    }

    public SolicitacaoConfirmadaDto SolicitacaoConfirmada { get; private set; }
}


public class SolicitacaoConfirmadaDto : StatusSolicitacaoDto
{
    public SolicitacaoConfirmadaDto(
        string codigo, 
        string? codigoExterno,
        string sistemaDeOrigem,
        UsuarioDto usuario,
        List<HistoricoAlteracaoStatusDto> historico,
        List<string> erros)
        :base(codigo, codigoExterno, sistemaDeOrigem, StatusDeSolicitacao.Confirmada, historico, erros)
    {
        Usuario = usuario;
    }
    
    public UsuarioDto Usuario { get; }
}