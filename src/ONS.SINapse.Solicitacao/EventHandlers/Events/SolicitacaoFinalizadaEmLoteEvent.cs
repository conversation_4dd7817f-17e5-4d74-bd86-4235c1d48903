using ONS.SINapse.Shared.Messages;

namespace ONS.SINapse.Solicitacao.EventHandlers.Events;

public class SolicitacaoFinalizadaEmLoteEvent : Event
{
    public SolicitacaoFinalizadaEmLoteEvent(List<string> solicitacoes)
    {
        _solicitacoes = solicitacoes;
    }

    private readonly List<string> _solicitacoes;
    public IReadOnlyCollection<string> Solicitacoes => _solicitacoes.AsReadOnly();
}