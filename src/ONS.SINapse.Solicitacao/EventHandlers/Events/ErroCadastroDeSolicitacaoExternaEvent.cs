using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Messages;

namespace ONS.SINapse.Solicitacao.EventHandlers.Events;

public class ErroCadastroDeSolicitacaoExternaEvent : Event
{
    public ErroCadastroDeSolicitacaoExternaEvent(string sistemaDeOrigem, string codigoExterno, UsuarioDto usuario, string[] erros)
    {
        SistemaDeOrigem = sistemaDeOrigem;
        CodigoExterno = codigoExterno;
        Erros = erros;
        Usuario = usuario;
    }

    public string SistemaDeOrigem { get; set; }
    public string CodigoExterno { get; set; }
    public string[] Erros { get; set; }
    public UsuarioDto Usuario { get; set; }
}