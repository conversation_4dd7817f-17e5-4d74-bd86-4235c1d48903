using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Messages;

namespace ONS.SINapse.Solicitacao.EventHandlers.Events;

public class SolicitacoesCadastradasEmLoteEvent : Event
{
    public SolicitacoesCadastradasEmLoteEvent(List<SolicitacaoCadastradasEmLoteDto> solicitacoesCadastradasEmLote)
    {
        SolicitacoesCadastradasEmLote = solicitacoesCadastradasEmLote;
    }
    
    public List<SolicitacaoCadastradasEmLoteDto> SolicitacoesCadastradasEmLote { get; set; }
}


public class SolicitacaoCadastradasEmLoteDto
{
    public SolicitacaoCadastradasEmLoteDto(
        string id, 
        string origem, 
        string mensagem, 
        UsuarioDto usuario, 
        string destino, 
        StatusDeSolicitacaoDto status)
    {
        Id = id;
        Origem = origem;
        Mensagem = mensagem;
        Usuario = usuario;
        Destino = destino;
        Status = status;
    }

    public string Id { get; }
    public string Origem { get; }
    public string? Agente { get; init; }
    public string Mensagem { get; }
    public UsuarioDto Usuario { get; }
    public string Destino { get; }
    public string? Local { get; init; }
    public required DateTime CreatedAt { get; init;  }
    public required DateTime UpdatedAt { get; init; }
    public StatusDeSolicitacaoDto Status { get; }
    public bool Externa => !string.IsNullOrEmpty(SistemaDeOrigem) && SistemaDeOrigem != Entities.Entities.Solicitacao.SistemaDeOrigemInterno;
    
    public bool IntegracaoFeitaViaApi { get; set; }
    public string? SistemaDeOrigem { get; init; }
}