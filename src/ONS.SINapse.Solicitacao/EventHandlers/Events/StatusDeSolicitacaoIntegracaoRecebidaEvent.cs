using ONS.SINapse.Shared.Messages;
using ONS.SINapse.Solicitacao.Dtos.Integracao;

namespace ONS.SINapse.Solicitacao.EventHandlers.Events;

/// <summary>
/// Evento tanto para informar erro quanto sucesso na troca de status
/// </summary>
public class StatusDeSolicitacaoIntegracaoRecebidaEvent : Event
{
    public StatusDeSolicitacaoIntegracaoRecebidaEvent(List<StatusDeSolicitacaoIntegracaoEnvioDto> solicitacoes)
    {
        Solicitacoes = solicitacoes;
    }
    
    public IReadOnlyCollection<StatusDeSolicitacaoIntegracaoEnvioDto> Solicitacoes { get; }
}

