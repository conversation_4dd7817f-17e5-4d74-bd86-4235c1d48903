using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Messages;

namespace ONS.SINapse.Solicitacao.EventHandlers.Events;

public class SolicitacaoImpedidaEvent : Event
{
    public SolicitacaoImpedidaEvent(List<SolicitacaoImpedidaDto> solicitacoes)
    {
        _solicitacoes = solicitacoes;
    }
    
    private readonly List<SolicitacaoImpedidaDto> _solicitacoes;
    public IReadOnlyCollection<SolicitacaoImpedidaDto> Solicitacoes => _solicitacoes;
}

public record SolicitacaoImpedidaDto(
    string Origem,
    string Id,
    string? Local,
    DateTime Criacao,
    DateTime Atualizacao,
    string Mensagem,
    UsuarioDto Usuario,
    StatusDeSolicitacao Status
);