using ONS.SINapse.Integracao.Shared.Enums;

namespace ONS.SINapse.Solicitacao.EventHandlers.Events;

/// <summary>
/// Evento para finalização automática de solicitações.
/// </summary>
public class SolicitacaoFinalizacaoAutoEvent
{
    public required List<SolicitacaoFinalizacaoAutoDto> Solicitacoes { get; init; }
}

/// <summary>
/// Dto para finalização automática de solicitação.
/// Necessário informar o status atual da solicitação no momento da publicação do evento, pois o status pode ser alterado entre a publicação do evento e o consumo.
/// </summary>
/// <param name="Id"></param>
/// <param name="Status"></param>
public record SolicitacaoFinalizacaoAutoDto(string Id, StatusDeSolicitacao Status);