using AutoMapper;
using FluentValidation;
using MassTransit.Mediator;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using ONS.SINapse.Solicitacao.Dtos.Integracao;
using ONS.SINapse.Solicitacao.EventHandlers.Events;

namespace ONS.SINapse.Solicitacao.EventHandlers.IntegrationEvents;

public class CadastroDeSolicitacaoExternaRecebidaIntegrationEventHandler : 
    MediatorRequestHandler<CadastroDeSolicitacaoExternaRecebidaIntegrationEvent>
{
    private readonly IMediatorHandler _mediatorHandler;
    private readonly IValidator<CadastroDeSolicitacaoExternaRecebidaIntegrationEvent> _validator;
    private readonly IMapper _mapper;

    public CadastroDeSolicitacaoExternaRecebidaIntegrationEventHandler(
        IMediatorHandler mediatorHandler,
        IValidator<CadastroDeSolicitacaoExternaRecebidaIntegrationEvent> validator,
        IMapper mapper)
    {
        _mediatorHandler = mediatorHandler;
        _validator = validator;
        _mapper = mapper;
    }
    
    protected override async Task Handle(CadastroDeSolicitacaoExternaRecebidaIntegrationEvent request, CancellationToken cancellationToken)
    {
        var validate = await _validator.ValidateAsync(request, cancellationToken);
        
        if (!validate.IsValid)
        {
            var solicitacoesComErro =
                request.Solicitacoes
                    .Select(solicitacao => new StatusDeSolicitacaoIntegracaoEnvioDto(
                        solicitacao.Id ?? string.Empty,
                        StatusDeSolicitacao.Erro,
                        solicitacao.Usuario ?? new UsuarioDto("Integracao", "Integracao", "Integracao"),
                        DateTime.UtcNow, 
                        null,
                        solicitacao.SistemaDeOrigem ?? string.Empty,
                        validate.Errors.Select(x => x.ErrorMessage).ToArray()
                    )).ToList();

            var evento = new StatusDeSolicitacaoIntegracaoRecebidaEvent(solicitacoesComErro);

            await _mediatorHandler.PublicarEventoAsync(evento, cancellationToken);
            return;
        }
        
        var cadastroSolicitacao = _mapper.Map<List<CadastroSolicitacaoDto>>(request);

        var command = new CadastrarSolicitacaoEmLoteCommand(cadastroSolicitacao);
        
        await _mediatorHandler
            .EnviarComandoAsync<CadastrarSolicitacaoEmLoteCommand, StatusDeSolicitacaoIntegracaoEnvioEmLoteDto>(command, cancellationToken);
    }
}