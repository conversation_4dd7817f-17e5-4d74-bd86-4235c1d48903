using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Kafka;

namespace ONS.SINapse.Solicitacao.EventHandlers.IntegrationEvents;

public class CadastroDeSolicitacaoExternaRecebidaIntegrationEvent : IntegrationKafkaEvent
{
    public CadastroDeSolicitacaoExternaRecebidaIntegrationEvent()
    {
        Solicitacoes = [];
    }

    public List<CadastroDeSolicitacaoExternaDto> Solicitacoes { get; set; }
}

public class CadastroDeSolicitacaoExternaDto
{
    public CadastroDeSolicitacaoExternaDto()
    {
        Tags = [];
    }

    public string? Id { get; set; }
    public ObjetoDeManobraDto? Origem { get; set; }
    public ObjetoDeManobraDto? Destino { get; set; }
    public ObjetoDeManobraDto? Local { get; set; }
    public ObjetoDeManobraDto? EncaminharPara { get; set; }
    public string? SistemaDeOrigem { get; set; }
    public string? InformacaoAdicional { get; set; }
    public string? Mensagem { get; set; }
    public string? Motivo { get; set; }
    public List<string>? Tags { get; set; }
    public UsuarioDto? Usuario { get; set; }
    
    public bool RequerAprovacaoEnvio { get; set; }
}