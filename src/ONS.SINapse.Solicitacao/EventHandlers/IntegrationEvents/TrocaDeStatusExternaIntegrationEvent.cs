using ONS.SINapse.Shared.Kafka;
using ONS.SINapse.Solicitacao.Dtos.Integracao;

namespace ONS.SINapse.Solicitacao.EventHandlers.IntegrationEvents;

public class TrocaDeStatusExternaIntegrationEvent : IntegrationKafkaEvent
{
    public TrocaDeStatusExternaIntegrationEvent()
    {
        Solicitacoes = [];
    }
    public TrocaDeStatusExternaIntegrationEvent(List<StatusDeSolicitacaoIntegracaoRecebimentoDto> solicitacoes)
    {
        Solicitacoes = solicitacoes;
    }
 
    public List<StatusDeSolicitacaoIntegracaoRecebimentoDto> Solicitacoes { get; set; }
}