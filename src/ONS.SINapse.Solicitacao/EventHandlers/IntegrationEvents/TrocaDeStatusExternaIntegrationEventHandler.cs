using MassTransit.Mediator;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Shared.Messages;
using ONS.SINapse.Solicitacao.Dtos.Integracao;
using ONS.SINapse.Solicitacao.Factories.Integracao;

namespace ONS.SINapse.Solicitacao.EventHandlers.IntegrationEvents;

public class TrocaDeStatusExternaIntegrationEventHandler
    : MediatorRequestHandler<TrocaDeStatusExternaIntegrationEvent>
{
    private readonly IMediatorHandler _mediatorHandler;
    private readonly ICriarCommandStatusDeSolicitacaoRecebidoFactory _commandStatusDeSolicitacaoRecebidoFactory;

    public TrocaDeStatusExternaIntegrationEventHandler(
        IMediatorHandler mediatorHandler,
        ICriarCommandStatusDeSolicitacaoRecebidoFactory commandStatusDeSolicitacaoRecebidoFactory)
    {
        _mediatorHandler = mediatorHandler;
        _commandStatusDeSolicitacaoRecebidoFactory = commandStatusDeSolicitacaoRecebidoFactory;
    }
    
    protected override Task Handle(TrocaDeStatusExternaIntegrationEvent request, CancellationToken cancellationToken)
    {
        var commands = _commandStatusDeSolicitacaoRecebidoFactory
            .ObterCommand(request.Solicitacoes);
        
        var tasks = commands
            .Select(async command => await SendCommandsAsync(command, cancellationToken));
        
        return Task.WhenAll(tasks);
    }

    private Task<StatusDeSolicitacaoIntegracaoEnvioEmLoteDto> SendCommandsAsync<T>(T command, CancellationToken cancellationToken) where T : Command<StatusDeSolicitacaoIntegracaoEnvioEmLoteDto>
    {
        return _mediatorHandler.EnviarComandoAsync<T, StatusDeSolicitacaoIntegracaoEnvioEmLoteDto>(command, cancellationToken);
    }
}