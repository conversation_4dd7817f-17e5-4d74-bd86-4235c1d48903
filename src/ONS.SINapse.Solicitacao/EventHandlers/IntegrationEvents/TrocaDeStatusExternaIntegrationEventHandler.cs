using System.Text;
using Confluent.Kafka;
using FluentValidation;
using MassTransit.Mediator;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Integracao.Shared.Helpers;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Shared.Messages;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using ONS.SINapse.Solicitacao.EventHandlers.Events;

namespace ONS.SINapse.Solicitacao.EventHandlers.IntegrationEvents;

public class TrocaDeStatusExternaIntegrationEventHandler
    : MediatorRequestHandler<TrocaDeStatusExternaIntegrationEvent>
{
    private readonly IMediatorHandler _mediatorHandler;
    private readonly IValidator<TrocaDeStatusExternaIntegrationEvent> _validator;
    private readonly IUsuarioJwtHelper _usuarioJwtHelper;

    public TrocaDeStatusExternaIntegrationEventHandler(
        IMediatorHandler mediatorHandler,
        IValidator<TrocaDeStatusExternaIntegrationEvent> validator,
        IUsuarioJwtHelper usuarioJwtHelper)
    {
        _mediatorHandler = mediatorHandler;
        _validator = validator;
        _usuarioJwtHelper = usuarioJwtHelper;
    }
    
    protected override async Task Handle(TrocaDeStatusExternaIntegrationEvent request, CancellationToken cancellationToken)
    {
        var validate = await _validator.ValidateAsync(request, cancellationToken);
        
        var usuario = ObterUsuario(request.Headers);
        
        if (usuario is null)
            validate.AdicionarErro("Não foi possível definir o usuário. Nenhum header encontrado.");
        
        if (!validate.IsValid)
        {
            await PublicarEventoDeErroAsync(request.Codigo ?? string.Empty, request.Status ?? 0,
                usuario ?? new UsuarioDto(), validate.ToString(), cancellationToken);
            return;
        }
        
        switch ((StatusDeSolicitacao?)request.Status)
        {
            case StatusDeSolicitacao.Cancelada:
                await CancelarAsync(request, usuario!, cancellationToken);
                break;
            case StatusDeSolicitacao.CienciaInformada:
                await InformarCienciaAsync(request, usuario!, cancellationToken);
                break;
            default:
                await PublicarEventoDeErroAsync(request.Codigo ?? string.Empty, request.Status ?? 0, usuario!,
                    "Status não permitido, Operação não realizada", cancellationToken);
                break;
        }
    }

    private async Task InformarCienciaAsync(TrocaDeStatusExternaIntegrationEvent request, UsuarioDto usuario, CancellationToken cancellationToken)
    {
        var command = new InformarCienciaCommand(request.Codigo!);
        command.DefinirCentros(new [] {request.CentroDeOperacao!});
        command.DefinirUsuario(usuario);

        await _mediatorHandler.EnviarComandoAsync<InformarCienciaCommand, CienciaInformadaResultDto>(command, cancellationToken);
    }
    
    private async Task CancelarAsync(TrocaDeStatusExternaIntegrationEvent request, UsuarioDto usuario, CancellationToken cancellationToken)
    {
        var command = new CancelarEmLoteCommand(new[] { request.Codigo! });
        command.IntegrarViaKafka();
        command.DefinirCentros(new [] {request.CentroDeOperacao!});
        command.DefinirUsuario(usuario);
        
        await _mediatorHandler.EnviarComandoAsync<CancelarEmLoteCommand, ResultadoCancelamentoEmLoteDto>(command, cancellationToken);
    }
    
    private UsuarioDto? ObterUsuario(Headers? headers)
    {
        var header = headers?.FirstOrDefault(x => x.Key == "AuthorizationUser")?.GetValueBytes();

        if (header is null) return null;
        
        var token = Encoding.UTF8.GetString(header);

        var usuarioJwt = _usuarioJwtHelper.GetUsuario(token);

        return new UsuarioDto(usuarioJwt.Sid, usuarioJwt.Nome, usuarioJwt.Login);
    }

    private Task PublicarEventoDeErroAsync(string codigo, int status, UsuarioDto usuarioDto, string mensagem, CancellationToken cancellationToken)
    {
        switch ((StatusDeSolicitacao)status)
        {
            case StatusDeSolicitacao.Finalizada:
                break;
            case StatusDeSolicitacao.Cancelada:
                var eventoCancelar = SolicitacoesCanceladasComErroEmLoteEvent.SolicitacoesCanceladasComErro(codigo, mensagem, usuarioDto);
                return _mediatorHandler.PublicarEventoAsync(eventoCancelar, cancellationToken);
            case StatusDeSolicitacao.CienciaInformada:
                var eventoInformarCiencia = CienciaInformadaComErroEvent.CienciaInformadaComErro(codigo, mensagem, null, usuarioDto);
                return _mediatorHandler.PublicarEventoAsync(eventoInformarCiencia, cancellationToken);
            case StatusDeSolicitacao.Impedida:
            case StatusDeSolicitacao.Erro:
            case StatusDeSolicitacao.Pendente:
            case StatusDeSolicitacao.Confirmada:
            default:
                throw new ArgumentOutOfRangeException(nameof(status), status, mensagem);
        }

        throw new NotImplementedException();
    }
}