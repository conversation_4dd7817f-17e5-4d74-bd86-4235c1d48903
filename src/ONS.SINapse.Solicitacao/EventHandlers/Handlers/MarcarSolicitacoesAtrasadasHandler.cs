using MassTransit;
using Microsoft.Extensions.Logging;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Repository.IRepository.Firebase;
using ONS.SINapse.Shared.Constants;
using ONS.SINapse.Solicitacao.EventHandlers.Events;

namespace ONS.SINapse.Solicitacao.EventHandlers.Handlers;

public class MarcarSolicitacoesAtrasadasHandler : IConsumer<SolicitacoesCadastradasEvent>
{
    private readonly ISolicitacaoFirebaseRepository _solicitacaoFirebaseRepository;
    private readonly ILogger _logger;

    public MarcarSolicitacoesAtrasadasHandler(ISolicitacaoFirebaseRepository solicitacaoFirebaseRepository, ILogger<MarcarSolicitacoesAtrasadasHandler> logger)
    {
        _solicitacaoFirebaseRepository = solicitacaoFirebaseRepository;
        _logger = logger;
    }
    public async Task Consume(ConsumeContext<SolicitacoesCadastradasEvent> context)
    {

        var statusSolicitacoesTask = context.Message.Solicitacoes.Select(dto => _solicitacaoFirebaseRepository.StatusAsync(dto.Id, context.CancellationToken)).ToArray();
        var statusSolicitacoes = await Task.WhenAll(statusSolicitacoesTask);
        
        if (statusSolicitacoes.All(x => x != StatusDeSolicitacao.Pendente))
        {
            return;
        }
        
        var solicitacoes = context.Message.Solicitacoes
            .Where(x => statusSolicitacoes[context.Message.Solicitacoes.IndexOf(x)] == StatusDeSolicitacao.Pendente)
            .ToList();
        
        if (solicitacoes.Count == 0)
        {
            return;
        }
       
        _logger.LogInformation("[SolicitacaoCadastradaEventHandler] Solicitações cadastradas pendentes de confirmação: {Solicitacoes}", string.Join(", ", solicitacoes.Select(x => x.Id)));

        Dictionary<string, object?> update = new();

        solicitacoes.ForEach(x =>
        {
            update.Add($"{ColecoesFirebase.Solicitacao}/{x.Id}/atrasada", true);
        });
        
        await _solicitacaoFirebaseRepository.AddUpdateAsync(update, context.CancellationToken);
    }
}