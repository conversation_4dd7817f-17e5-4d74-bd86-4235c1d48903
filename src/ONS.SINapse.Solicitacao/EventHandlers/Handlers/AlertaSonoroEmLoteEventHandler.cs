using MassTransit;
using ONS.SINapse.Shared.DTO.Firebase;
using ONS.SINapse.Shared.Services.Firebase;
using ONS.SINapse.Solicitacao.EventHandlers.Events;

namespace ONS.SINapse.Solicitacao.EventHandlers.Handlers;

public class AlertaSonoroEmLoteEventHandler : 
    IConsumer<SolicitacoesCadastradasEmLoteEvent>,
    IConsumer<SolicitacaoImpedidaEvent>
{
    private readonly IAlertaSonoroFirebaseDatabaseService _alertaSonoroFirebaseDatabaseService;

    public AlertaSonoroEmLoteEventHandler(IAlertaSonoroFirebaseDatabaseService alertaSonoroFirebaseDatabaseService)
    {
        _alertaSonoroFirebaseDatabaseService = alertaSonoroFirebaseDatabaseService;
    }

    public Task Consume(ConsumeContext<SolicitacoesCadastradasEmLoteEvent> context)
    {
        var alertas =
            context.Message.SolicitacoesCadastradasEmLote
                .GroupBy(
                    group => group.Destino,
                    by => new AlertaSonoroRealtimeDto
                    (
                        by.Usuario.Sid,
                        by.Usuario.Nome,
                        DateTime.UtcNow,
                        true
                    ))
                .ToDictionary(x => x.Key.ToLower(), y => y.First());

        return _alertaSonoroFirebaseDatabaseService.SetAsync(alertas, context.CancellationToken);
    }

    public Task Consume(ConsumeContext<SolicitacaoImpedidaEvent> context)
    {
        var alertas =
            context.Message.Solicitacoes
                .GroupBy(
                    group => group.Origem.ToLower(),
                    by => new AlertaSonoroRealtimeDto
                    (
                        by.Usuario.Sid,
                        by.Usuario.Nome,
                        DateTime.UtcNow,
                        true
                    ))
                .ToDictionary(x => x.Key.ToLower(), y => y.First());

        return _alertaSonoroFirebaseDatabaseService.SetAsync(alertas, context.CancellationToken);
    }
}