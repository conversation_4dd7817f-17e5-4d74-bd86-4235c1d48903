using MassTransit;
using Microsoft.Extensions.Logging;
using ONS.SINapse.Shared.Bus;
using ONS.SINapse.Solicitacao.EventHandlers.Events;

namespace ONS.SINapse.Solicitacao.EventHandlers.Handlers;

/// <summary>
/// Manipulador responsável por receber eventos de cadastros de solicitações
/// e publicar eventos de solicitação cadastrada para notificação de solicitações pendentes.
/// </summary>
public class PublicarSolicitacoesCadastradasHandler(ISqsBus bus, ILogger<PublicarSolicitacoesCadastradasHandler> logger)
    : IConsumer<SolicitacoesCadastradasEmLoteEvent>
{

    public Task Consume(ConsumeContext<SolicitacoesCadastradasEmLoteEvent> context)
    {
        var solicitacoes = context.Message.SolicitacoesCadastradasEmLote
            .Select(x => new SolicitacaoCadastradaDto(x.Id, x.Origem, x.<PERSON>))
            .ToList();

        var evento = new SolicitacoesCadastradasEvent(solicitacoes);

        logger.LogInformation(
            "Publicando evento de solicitação cadastrada para {Count} solicitações: {@Solicitacoes}", evento.Solicitacoes.Count, evento.Solicitacoes);

        return bus.Publish(evento, context.CancellationToken);
    }
}
