using FluentValidation.Results;
using MassTransit;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Repository.IRepository.Firebase;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Shared.Settings;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using ONS.SINapse.Solicitacao.Dtos.Integracao;
using ONS.SINapse.Solicitacao.EventHandlers.Events;

namespace ONS.SINapse.Solicitacao.EventHandlers.Handlers;

/// <summary>
/// Manipulador para o evento de finalização automática de solicitações.
/// </summary>
public class SolicitacaoFinalizacaoAutoEventHandler : IConsumer<SolicitacaoFinalizacaoAutoEvent>
{
    private readonly UsuarioDoSistemaSettings _usuarioDoSistemaSettings;
    private readonly IMediatorHandler _mediatorHandler;
    private readonly ISolicitacaoFirebaseRepository _solicitacaoFirebaseRepository;
    private readonly ILogger<SolicitacaoFinalizacaoAutoEventHandler> _logger;

    public SolicitacaoFinalizacaoAutoEventHandler(
        IOptions<UsuarioDoSistemaSettings> usuarioDoSistemaSettings,
        IMediatorHandler mediatorHandler,
        ISolicitacaoFirebaseRepository solicitacaoFirebaseRepository,
        ILogger<SolicitacaoFinalizacaoAutoEventHandler> logger)
    {
        _usuarioDoSistemaSettings = usuarioDoSistemaSettings.Value;
        _mediatorHandler = mediatorHandler;
        _solicitacaoFirebaseRepository = solicitacaoFirebaseRepository;
        _logger = logger;
    }

    public async Task Consume(ConsumeContext<SolicitacaoFinalizacaoAutoEvent> context)
    {
        var solicitacoes = context.Message.Solicitacoes;

        if (solicitacoes.Count == 0)
        {
            _logger.LogWarning("[SolicitacaoFinalizacaoAutoEventHandler] Payload vazio. Nenhuma solicitação para finalizar automaticamente.");
            return;
        }
        
        var solicitacoesTask = solicitacoes.Select(dto => _solicitacaoFirebaseRepository.StatusAsync(dto.Id, context.CancellationToken)).ToArray();
        
        var statusDasSolicitacoes = await Task.WhenAll(solicitacoesTask);
        if (statusDasSolicitacoes.All(x => x != StatusDeSolicitacao.Confirmada && x != StatusDeSolicitacao.CienciaInformada))
        {
            _logger.LogWarning("[SolicitacaoFinalizacaoAutoEventHandler] Nenhuma solicitação com status adequado para finalização automática.");
            return;
        }

        var solicitacoesIds = solicitacoes
            .Where(x => statusDasSolicitacoes[solicitacoes.IndexOf(x)] == x.Status);

        var usuario = UsuarioDoSistema();
        
        var command = new FinalizarEmLoteCommand(solicitacoesIds.Select(id => new StatusDeSolicitacaoIntegracaoRecebimentoDto(
            id.Id,
            StatusDeSolicitacao.Finalizada,
            null,
            null,
            usuario,
            Entities.Entities.Solicitacao.SistemaDeOrigemInterno
        )).ToList());
        
        command.FinalizadoProcessoAutomatico();

        var result = await _mediatorHandler
            .EnviarComandoAsync<FinalizarEmLoteCommand, StatusDeSolicitacaoIntegracaoEnvioEmLoteDto>(command, context.CancellationToken);

        var failures = result.Solicitacoes.SelectMany(x => x.Erros)
            .Select(errorMessage => new ValidationFailure("Finalizar", errorMessage))
            .ToList();

        if (failures.Count != 0)
        {
            _logger.LogError("[SolicitacaoFinalizacaoAutoEventHandler] Erro ao finalizar automaticamente: {Erros}", failures.Select(x => x.ErrorMessage));
            return;
        }
        
        await Task.CompletedTask;
    }
    
    private UsuarioDto UsuarioDoSistema()
        => new()
        {
            Login = _usuarioDoSistemaSettings.Login,
            Nome = _usuarioDoSistemaSettings.Nome,
            Sid = _usuarioDoSistemaSettings.Sid
        };
}