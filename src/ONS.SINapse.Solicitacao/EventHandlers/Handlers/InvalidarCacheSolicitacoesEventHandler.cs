using MassTransit;
using ONS.SINapse.Shared.Constants;
using ONS.SINapse.Shared.Services.Caching;
using ONS.SINapse.Solicitacao.EventHandlers.Events;

namespace ONS.SINapse.Solicitacao.EventHandlers.Handlers;

public class InvalidarCacheSolicitacoesEventHandler : 
    IConsumer<SolicitacoesCanceladasEmLoteEvent>,
    IConsumer<SolicitacaoFinalizadaEmLoteEvent>
{
    private readonly ICacheService _cacheService;

    public InvalidarCacheSolicitacoesEventHandler(ICacheService cacheService)
    {
        _cacheService = cacheService;
    }

    public Task Consume(ConsumeContext<SolicitacoesCanceladasEmLoteEvent> context)
    {
        return LimparCacheAsync(context.CancellationToken);
    }

    public Task Consume(ConsumeContext<SolicitacaoFinalizadaEmLoteEvent> context)
    {
        return LimparCacheAsync(context.CancellationToken);
    }

    private Task LimparCacheAsync(CancellationToken cancellationToken)
    {
        return Task.WhenAll(
            _cacheService.RemoveByPrefixAsync(CacheRedisKey.KeyISolicitacaoPaged, cancellationToken),
            _cacheService.RemoveByPrefixAsync(CacheRedisKey.KeyISolicitacaoPagedCount, cancellationToken),
            _cacheService.RemoveByPrefixAsync(CacheRedisKey.KeyISolicitacaoRepository, cancellationToken)
        );
    }
    
}