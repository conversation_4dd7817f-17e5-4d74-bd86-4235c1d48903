using MassTransit.Mediator;
using ONS.SINapse.Shared.Kafka.Providers;
using ONS.SINapse.Solicitacao.EventHandlers.Events;
using ONS.SINapse.Solicitacao.Workers.Messages;

namespace ONS.SINapse.Solicitacao.EventHandlers.Handlers;

public class PublicarSolicitacaoCadastradaEmLoteNoKafkaEventHandler : MediatorRequestHandler<SolicitacoesCadastradasEmLoteEvent>
{
    private readonly IProducerProvider<AtualizacaoStatusMessage> _atualizacaoStatusProvider;

    public PublicarSolicitacaoCadastradaEmLoteNoKafkaEventHandler(IProducerProvider<AtualizacaoStatusMessage> atualizacaoStatusProvider)
    {
        _atualizacaoStatusProvider = atualizacaoStatusProvider;
    }
    
    protected override Task Handle(SolicitacoesCadastradasEmLoteEvent request, CancellationToken cancellationToken)
    {
        var tasks = request.SolicitacoesCadastradasEmLote
            .Select(solicitacaoCadastradasEmLoteDto =>
            {
                // Publica mensagem no kafka somente se for uma integração que não foi feita via api.
                if (solicitacaoCadastradasEmLoteDto is not { Externa: true, IntegracaoFeitaViaApi: false })
                    return Task.CompletedTask;

                var message = new AtualizacaoStatusMessage
                {
                    CodigoDaSolicitacao = solicitacaoCadastradasEmLoteDto.Id,
                    CodigoExterno = solicitacaoCadastradasEmLoteDto.CodigoExterno!,
                    StatusAtualId = solicitacaoCadastradasEmLoteDto.Status.Id,
                    DescricaoStatusAtual = solicitacaoCadastradasEmLoteDto.Status.Descricao,
                    Enviada = false,
                    DataEHoraDeEnvio = null,
                    Justificativa = string.Empty,
                    Historicos = new List<HistoricoDoStatusDto>
                    {
                        new(
                            solicitacaoCadastradasEmLoteDto.CreatedAt,
                            solicitacaoCadastradasEmLoteDto.Status.Id,
                            solicitacaoCadastradasEmLoteDto.Status.Descricao,
                            solicitacaoCadastradasEmLoteDto.Usuario.Nome,
                            solicitacaoCadastradasEmLoteDto.Usuario.Login,
                            solicitacaoCadastradasEmLoteDto.Usuario.Sid
                        )
                    }
                };

                _atualizacaoStatusProvider.AtualizarTopico(topico =>
                    topico.AlterarValor($"{topico.Topico}.{solicitacaoCadastradasEmLoteDto.SistemaDeOrigem}"));
                return _atualizacaoStatusProvider.PublishAsync(message, new Dictionary<string, string>(),
                    cancellationToken);
            });
        
        return Task.WhenAll(tasks);
    }
}