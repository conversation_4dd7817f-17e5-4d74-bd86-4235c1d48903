using MassTransit;
using ONS.SINapse.Repository.IRepository.Firebase;
using ONS.SINapse.Solicitacao.EventHandlers.Events;

namespace ONS.SINapse.Solicitacao.EventHandlers.Handlers;

public class RemoverSolicitacoesFirebaseEventHandler : 
    IConsumer<SolicitacaoEnviadoParaMongoEvent>
{
    private readonly ISolicitacaoFirebaseRepository _solicitacaoFirebaseRepository;

    public RemoverSolicitacoesFirebaseEventHandler(ISolicitacaoFirebaseRepository solicitacaoFirebaseRepository)
    {
        _solicitacaoFirebaseRepository = solicitacaoFirebaseRepository;
    }

    public Task Consume(ConsumeContext<SolicitacaoEnviadoParaMongoEvent> context)
    {
        return _solicitacaoFirebaseRepository.RemoverSolicitacoesAsync(context.Message.Solicitacoes, context.CancellationToken);
    }
}