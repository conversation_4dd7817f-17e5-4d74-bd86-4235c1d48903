using System.Collections.ObjectModel;
using MassTransit;
using ONS.SINapse.Repository.IRepository.Firebase;
using ONS.SINapse.Solicitacao.EventHandlers.Events;

namespace ONS.SINapse.Solicitacao.EventHandlers.Handlers;

public class RemoverSolicitacoesFirebaseEventHandler : 
    IConsumer<SolicitacaoFinalizadaEmLoteEvent>,
    IConsumer<SolicitacoesCanceladasEmLoteEvent>
{
    private readonly ISolicitacaoFirebaseRepository _solicitacaoFirebaseRepository;

    public RemoverSolicitacoesFirebaseEventHandler(ISolicitacaoFirebaseRepository solicitacaoFirebaseRepository)
    {
        _solicitacaoFirebaseRepository = solicitacaoFirebaseRepository;
    }
    
    public Task Consume(ConsumeContext<SolicitacaoFinalizadaEmLoteEvent> context)
    {
        var ids = new ReadOnlyCollection<string>(context.Message.SolicitacoesFinalizada.Select(x => x.Id).ToList());
        
        return _solicitacaoFirebaseRepository.RemoverSolicitacoesAsync(ids, context.CancellationToken);
    }

    public Task Consume(ConsumeContext<SolicitacoesCanceladasEmLoteEvent> context)
    {
        var ids = new ReadOnlyCollection<string>(context.Message.Solicitacoes.Select(x => x.Id).ToList());
        
        return _solicitacaoFirebaseRepository.RemoverSolicitacoesAsync(ids, context.CancellationToken);
    }
}