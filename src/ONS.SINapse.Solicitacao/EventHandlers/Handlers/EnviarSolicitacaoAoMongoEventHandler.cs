using MassTransit;
using Microsoft.Extensions.Logging;
using ONS.SINapse.Repository.IRepository;
using ONS.SINapse.Repository.IRepository.InMemory;
using ONS.SINapse.Shared.Bus;
using ONS.SINapse.Solicitacao.EventHandlers.Events;

namespace ONS.SINapse.Solicitacao.EventHandlers.Handlers;

public class EnviarSolicitacaoAoMongoEventHandler(
    ILogger<EnviarSolicitacaoAoMongoEventHandler> logger,
    ISolicitacaoMemoryRepository solicitacaoMemoryRepository,
    ISolicitacaoRepository solicitacaoMongoRepository,
    ISqsBus bus)
    : IConsumer<SolicitacaoConcluidaEvent>
{

    public async Task Consume(ConsumeContext<SolicitacaoConcluidaEvent> context)
    {
        var ids = context.Message.Solicitacoes.ToArray();
        var solicitacoes = solicitacaoMemoryRepository.Get(x => ids.Contains(x.Id));

        if (solicitacoes.Count == 0)
        {
            logger.LogWarning("[EnviarSolicitacaoAoMongoEventHandler] Nenhuma Solicitação encontrada {Solicitacoes}", string.Join(", ", ids));
            return;
        }
        
        var solicitacoesNaoEncontradas = ids.Except(solicitacoes.Select(x => x.Id)).ToArray();
        
        if (solicitacoesNaoEncontradas.Length > 0)
        {
            logger.LogWarning("[EnviarSolicitacaoAoMongoEventHandler] Solicitações não encontradas {Solicitacoes}", string.Join(", ", solicitacoesNaoEncontradas));
        }
        
        await solicitacaoMongoRepository.BulkUpdateAsync(solicitacoes, true, context.CancellationToken);
        var enviadoAoMongoEvent = new SolicitacaoEnviadoParaMongoEvent { Solicitacoes = solicitacoes.Select(x => x.Id).ToList() };
        await bus.Publish(enviadoAoMongoEvent, context.CancellationToken);
        logger.LogInformation("[EnviarSolicitacaoAoMongoEventHandler] Total de solicitações enviadas para o Mongo: {Total}", solicitacoes.Count);
    }
}