using MassTransit.Mediator;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Shared.Kafka.Providers;
using ONS.SINapse.Solicitacao.EventHandlers.Events;
using ONS.SINapse.Solicitacao.Workers.Messages;

namespace ONS.SINapse.Solicitacao.EventHandlers.Handlers;

public class ErroCadastroDeSolicitacaoExternaEventHandler
    : MediatorRequestHandler<ErroCadastroDeSolicitacaoExternaEvent>
{
    private readonly IProducerProvider<AtualizacaoStatusMessage> _atualizacaoStatusProvider;

    public ErroCadastroDeSolicitacaoExternaEventHandler(IProducerProvider<AtualizacaoStatusMessage> atualizacaoStatusProvider)
    {
        _atualizacaoStatusProvider = atualizacaoStatusProvider;
    }
    
    protected override async Task Handle(ErroCadastroDeSolicitacaoExternaEvent request, CancellationToken cancellationToken)
    {
        var message = new AtualizacaoStatusMessage
        {
            CodigoDaSolicitacao = request.CodigoExterno,
            CodigoExterno = request.CodigoExterno,
            StatusAtualId = (short)StatusDeSolicitacao.Erro,
            DescricaoStatusAtual = StatusDeSolicitacao.Erro.GetDescription(),
            Justificativa = string.Empty,
            Historicos =
            [
                new HistoricoDoStatusDto
                (
                    DateTime.Now,
                    (short)StatusDeSolicitacao.Erro,
                    StatusDeSolicitacao.Erro.GetDescription(),
                    request.Usuario.Nome,
                    request.Usuario.Login,
                    request.Usuario.Sid
                )
            ],
            Erros = request.Erros.ToList()
        };
        
        _atualizacaoStatusProvider.AtualizarTopico(topico => topico.AlterarValor($"{topico.Topico}.{request.SistemaDeOrigem}"));
        await _atualizacaoStatusProvider.PublishAsync(message, new Dictionary<string, string>(), cancellationToken);
    }
}