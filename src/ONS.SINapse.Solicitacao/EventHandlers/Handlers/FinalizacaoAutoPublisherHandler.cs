using MassTransit;
using Microsoft.Extensions.Logging;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Shared.Bus;
using ONS.SINapse.Solicitacao.Dtos.Integracao;
using ONS.SINapse.Solicitacao.EventHandlers.Events;

namespace ONS.SINapse.Solicitacao.EventHandlers.Handlers;

/// <summary>
/// Manipulador responsável por receber eventos de status de solicitações
/// e publicar eventos de finalização automática para aquelas que atendem aos critérios.
/// Critérios de elegibilidade para finalização automática:
/// - Status deve ser Confirmada ou Ciência Informada.
/// - Lista de erros deve estar vazia.
/// </summary>
public class FinalizacaoAutoPublisherHandler(ISqsBus bus, ILogger<FinalizacaoAutoPublisherHandler> logger)
    : IConsumer<StatusDeSolicitacaoIntegracaoRecebidaEvent>
{
    /// <summary>
    /// Processa o evento <see cref="StatusDeSolicitacaoIntegracaoRecebidaEvent"/>,
    /// filtrando as solicitações elegíveis para finalização automática e
    /// publicando um <see cref="SolicitacaoFinalizacaoAutoEvent"/>.
    /// </summary>
    /// <param name="context">Contexto do evento recebido.</param>
    public Task Consume(ConsumeContext<StatusDeSolicitacaoIntegracaoRecebidaEvent> context)
    {
        var solicitacoesParaFinalizar = FiltrarSolicitacoesParaFinalizar(context.Message.Solicitacoes);

        if (solicitacoesParaFinalizar.Count == 0)
            return Task.CompletedTask;

        var finalizacaoAutoEvent = new SolicitacaoFinalizacaoAutoEvent
        {
            Solicitacoes = solicitacoesParaFinalizar.Select(x => new SolicitacaoFinalizacaoAutoDto(x.Id, x.Status)).ToList()
        };

        logger.LogInformation(
            "Publicando evento de finalização automática para {Count} solicitações: {@Solicitacoes}",
            finalizacaoAutoEvent.Solicitacoes.Count,
            finalizacaoAutoEvent.Solicitacoes);

        bus.Publish(finalizacaoAutoEvent, context.CancellationToken);

        return Task.CompletedTask;
    }

    /// <summary>
    /// Filtra as solicitações que podem ser finalizadas automaticamente.
    /// Critérios:
    /// - Status deve ser Confirmada ou Ciência Informada.
    /// - Lista de erros deve estar vazia.
    /// </summary>
    /// <param name="solicitacoes">Coleção de solicitações recebidas no evento.</param>
    /// <returns>Lista de solicitações elegíveis para finalização.</returns>
    private List<StatusDeSolicitacaoIntegracaoEnvioDto> FiltrarSolicitacoesParaFinalizar(
        IReadOnlyCollection<StatusDeSolicitacaoIntegracaoEnvioDto> solicitacoes)
    {
        return solicitacoes
            .Where(x => x.Status == StatusDeSolicitacao.Confirmada || x.Status == StatusDeSolicitacao.CienciaInformada)
            .Where(x => x.Erros.Length == 0)
            .ToList();
    }
    /// <summary>
    /// Filtra as solicitações que podem ter o agendamento de finalização automática removido.
    /// Isso é necessário para que a finalização automática não seja executada novamente quando o status for alterado para finalizada pelo processo manual.
    /// Também remove o agendamento para solicitações que foram confirmadas e posteriormente impedidas visto que ao informar ciência um novo agendamento é criado.
    /// </summary>
    /// <param name="solicitacoes"></param>
    /// <returns></returns>
    private List<StatusDeSolicitacaoIntegracaoEnvioDto> FiltrarSolicitacoesParaRemoverAgendamento(
        IReadOnlyCollection<StatusDeSolicitacaoIntegracaoEnvioDto> solicitacoes)
    {
        return solicitacoes
            .Where(x => x.Status == StatusDeSolicitacao.Impedida || x.Status == StatusDeSolicitacao.Confirmada)
            .Where(x => x.Erros.Length == 0)
            .ToList();
    }
    
}
