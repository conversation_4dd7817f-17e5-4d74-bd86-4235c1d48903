using MassTransit;
using ONS.SINapse.Shared.Kafka.Providers;
using ONS.SINapse.Solicitacao.Dtos.Integracao;
using ONS.SINapse.Solicitacao.EventHandlers.Events;
using ONS.SINapse.Solicitacao.Workers.Messages;

namespace ONS.SINapse.Solicitacao.EventHandlers.Handlers;

public class EnviarSolicitacaoAoKafkaEventHandler : 
    IConsumer<StatusDeSolicitacaoIntegracaoRecebidaEvent>
{
    private readonly IProducerProvider<AtualizacaoStatusMessage> _atualizacaoStatusProvider;

    public EnviarSolicitacaoAoKafkaEventHandler(IProducerProvider<AtualizacaoStatusMessage> atualizacaoStatusProvider)
    {
        _atualizacaoStatusProvider = atualizacaoStatusProvider;
    }
    
    
    public Task Consume(ConsumeContext<StatusDeSolicitacaoIntegracaoRecebidaEvent> context)
    {
        return PublicarAsync(context.Message.Solicitacoes, context.CancellationToken);
    }

    private Task PublicarAsync(IEnumerable<StatusDeSolicitacaoIntegracaoEnvioDto> solicitacoes, CancellationToken cancellationToken)
    {
        if (!_atualizacaoStatusProvider.EstaConectado()) return Task.CompletedTask;
        
        var tasks = solicitacoes
            .Where(x => x.SistemaDeOrigem != Entities.Entities.Solicitacao.SistemaDeOrigemInterno)
            .GroupBy(group => group.SistemaDeOrigem,
                by => by,
                async (group, by) =>
                {
                    _atualizacaoStatusProvider.AtualizarTopico(topico =>
                        topico.AlterarValor($"{topico.Topico}.{group}"));

                    var message = new AtualizacaoStatusMessage(by.ToList());

                    await _atualizacaoStatusProvider.PublishAsync(message, new Dictionary<string, string>(),
                        cancellationToken);
                });
        
        return Task.WhenAll(tasks);
    }
}