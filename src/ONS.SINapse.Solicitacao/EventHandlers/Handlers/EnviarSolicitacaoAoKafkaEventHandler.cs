using AutoMapper;
using MassTransit;
using ONS.SINapse.Shared.Kafka.Providers;
using ONS.SINapse.Solicitacao.Dtos;
using ONS.SINapse.Solicitacao.EventHandlers.Events;
using ONS.SINapse.Solicitacao.Workers.Messages;

namespace ONS.SINapse.Solicitacao.EventHandlers.Handlers;

public class EnviarSolicitacaoAoKafkaEventHandler : 
    IConsumer<SolicitacoesCanceladasEmLoteEvent>,
    IConsumer<SolicitacoesCanceladasComErroEmLoteEvent>,
    IConsumer<SolicitacaoConfirmadaEvent>,
    IConsumer<CienciaInformadaEvent>,
    IConsumer<CienciaInformadaComErroEvent>,
    IConsumer<SolicitacaoImpedidaEvent>,
    IConsumer<SolicitacaoFinalizadaEmLoteEvent>
{
    private readonly IProducerProvider<AtualizacaoStatusMessage> _atualizacaoStatusProvider;
    private readonly IMapper _mapper;

    public EnviarSolicitacaoAoKafkaEventHandler(IProducerProvider<AtualizacaoStatusMessage> atualizacaoStatusProvider,
        IMapper mapper)
    {
        _atualizacaoStatusProvider = atualizacaoStatusProvider;
        _mapper = mapper;
    }

    public Task Consume(ConsumeContext<SolicitacoesCanceladasEmLoteEvent> context)
    {
        var solicitacoes = context.Message.Solicitacoes
            .Where(x => x.CodigoExterno is not null);

        return PublicarAsync(solicitacoes, context.CancellationToken);
    }

    public Task Consume(ConsumeContext<SolicitacoesCanceladasComErroEmLoteEvent> context)
    {
        var solicitacoes = context.Message.Solicitacoes
            .Where(x => x.CodigoExterno is not null);

        return PublicarAsync(solicitacoes, context.CancellationToken);
    }
    
    public Task Consume(ConsumeContext<SolicitacaoConfirmadaEvent> context)
    {
        if(context.Message.SolicitacaoConfirmada.CodigoExterno is null) return Task.CompletedTask;
        return PublicarAsync(new List<SolicitacaoConfirmadaDto> { context.Message.SolicitacaoConfirmada }, context.CancellationToken);
    }
    
    public Task Consume(ConsumeContext<CienciaInformadaEvent> context)
    {
        if(context.Message.Solicitacao.CodigoExterno is null) return Task.CompletedTask;
        return PublicarAsync(new List<CienciaInformadaDto> { context.Message.Solicitacao }, context.CancellationToken);
    }

    public Task Consume(ConsumeContext<CienciaInformadaComErroEvent> context)
    {
        if(context.Message.Solicitacao.CodigoExterno is null) return Task.CompletedTask;
        return PublicarAsync(new List<CienciaInformadaDto> { context.Message.Solicitacao }, context.CancellationToken);
    }
    
    public Task Consume(ConsumeContext<SolicitacaoImpedidaEvent> context)
    {
        if(context.Message.Solicitacao.CodigoExterno is null) return Task.CompletedTask;
        return PublicarAsync(new List<SolicitacaoImpedidaDto> { context.Message.Solicitacao }, context.CancellationToken);
    }
    
    public Task Consume(ConsumeContext<SolicitacaoFinalizadaEmLoteEvent> context)
    {
        var solicitacoes = context.Message.SolicitacoesFinalizada
            .Where(x => x.CodigoExterno is not null);

        return PublicarAsync(solicitacoes, context.CancellationToken);
    }
    
    private Task PublicarAsync<T>(IEnumerable<T> solicitacoes, CancellationToken cancellationToken)
        where T : StatusSolicitacaoDto
    {
        var tasks = solicitacoes
            .Select(async x =>
            {
                var message = _mapper.Map<AtualizacaoStatusMessage>(x);
                
                _atualizacaoStatusProvider.AtualizarTopico(topico =>
                    topico.AlterarValor($"{topico.Topico}.{x.SistemaDeOrigem}"));
               
                await _atualizacaoStatusProvider.PublishAsync(message, new Dictionary<string, string>(),
                    cancellationToken);
            });
        
        return Task.WhenAll(tasks);
    }
}