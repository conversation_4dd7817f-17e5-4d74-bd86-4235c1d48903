using MassTransit;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using ONS.SINapse.Integracao.Shared.Settings;
using ONS.SINapse.Shared.Kafka.Providers;
using ONS.SINapse.Solicitacao.Dtos.Integracao;
using ONS.SINapse.Solicitacao.EventHandlers.Events;
using ONS.SINapse.Solicitacao.Workers.Messages;

namespace ONS.SINapse.Solicitacao.EventHandlers.Handlers;

public class EnviarSolicitacaoAoKafkaEventHandler : 
    IConsumer<StatusDeSolicitacaoIntegracaoRecebidaEvent>
{
    private readonly IProducerProvider<AtualizacaoStatusMessage> _atualizacaoStatusProvider;
    private readonly KafkaTopicsSettings _kafkaTopicsSettings;
    private readonly ILogger<EnviarSolicitacaoAoKafkaEventHandler> _logger;

    public EnviarSolicitacaoAoKafkaEventHandler(
        IProducerProvider<AtualizacaoStatusMessage> atualizacaoStatusProvider,
        IOptions<KafkaTopicsSettings> kafkaTopicsSettings,
        ILogger<EnviarSolicitacaoAoKafkaEventHandler> logger)
    {
        _atualizacaoStatusProvider = atualizacaoStatusProvider;
        _kafkaTopicsSettings = kafkaTopicsSettings.Value;
        _logger = logger;
    }
    
    
    public Task Consume(ConsumeContext<StatusDeSolicitacaoIntegracaoRecebidaEvent> context)
    {
        return PublicarAsync(context.Message.Solicitacoes, context.CancellationToken);
    }

    private async Task PublicarAsync(IEnumerable<StatusDeSolicitacaoIntegracaoEnvioDto> solicitacoes, CancellationToken cancellationToken)
    {
        if (!_atualizacaoStatusProvider.EstaConectado()) return;
        
        var solicitacoesExternas = solicitacoes
            .Where(x => x.SistemaDeOrigem != Entities.Entities.Solicitacao.SistemaDeOrigemInterno)
            .GroupBy(x => x.SistemaDeOrigem);

        foreach (var grupo in solicitacoesExternas)
        {
            var sistemaConfig = _kafkaTopicsSettings.Sistemas
                .FirstOrDefault(s => s.Nome.Equals(grupo.Key, StringComparison.OrdinalIgnoreCase));

            if (sistemaConfig == null || (sistemaConfig.Notificar && string.IsNullOrEmpty(sistemaConfig.Topic)))
            {
                _logger.LogWarning("Solicitação com sistema de origem '{Sistema}' não publicada: tópico não configurado ou notificação desabilitada.", grupo.Key);
                continue;
            }

            if (!sistemaConfig.Notificar)
            {
                continue;
            } 

            _atualizacaoStatusProvider.AtualizarTopico(topico =>
                topico.AlterarValor(sistemaConfig.Topic));

            var message = new AtualizacaoStatusMessage(grupo.ToList());

            await _atualizacaoStatusProvider.PublishAsync(message, new Dictionary<string, string>(),
                cancellationToken);
        }
    }
}