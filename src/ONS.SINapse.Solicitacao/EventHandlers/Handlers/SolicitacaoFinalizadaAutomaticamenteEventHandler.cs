using MassTransit;
using ONS.SINapse.Repository.IRepository;
using ONS.SINapse.Shared.Constants;
using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Solicitacao.EventHandlers.Events;

namespace ONS.SINapse.Solicitacao.EventHandlers.Handlers;

public class SolicitacaoFinalizadaAutomaticamenteEventHandler : IConsumer<SolicitacaoFinalizadaAutomaticamenteEvent>
{
    private readonly IStatusServiceRepository _statusServiceRepository;
    private const string Service = JobService.FinalizarSolicitacaoAutomaticamente;

    public SolicitacaoFinalizadaAutomaticamenteEventHandler(IStatusServiceRepository statusServiceRepository)
    {
        _statusServiceRepository = statusServiceRepository;
    }

    public Task Consume(ConsumeContext<SolicitacaoFinalizadaAutomaticamenteEvent> context)
    {
        var validation = context.Message.ValidationResult;

        if (validation.IsValid)
        {
            return _statusServiceRepository.UpdateStatusAsync(Service, true, null, null, context.CancellationToken);
        }
        
        var erros = validation.Errors.Aggregate(string.Empty,
            (current, error) => current + (error.ErrorMessage + Environment.NewLine));

        var exception = ((ValidationExptionFailure?)validation.Errors.FirstOrDefault(x => x is ValidationExptionFailure))?.Exception;        
        return _statusServiceRepository.UpdateStatusAsync(Service, false, exception, erros, context.CancellationToken);
    }
}