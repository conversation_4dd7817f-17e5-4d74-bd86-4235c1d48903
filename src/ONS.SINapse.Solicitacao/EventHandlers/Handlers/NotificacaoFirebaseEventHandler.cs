using MassTransit;
using ONS.SINapse.Business.IBusiness.Firebase;
using ONS.SINapse.Shared.Constants;
using ONS.SINapse.Shared.DTO.Firebase;
using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Solicitacao.EventHandlers.Events;

namespace ONS.SINapse.Solicitacao.EventHandlers.Handlers;

public class NotificacaoFirebaseEventHandler :
    IConsumer<SolicitacoesCadastradasEmLoteEvent>,
    IConsumer<SolicitacaoImpedidaEvent>
{
    private readonly INotificacaoFirebaseBusiness _notificacaoFirebaseBusiness;
    
    private const string TituloDaNovaSolicitacao = "Nova solicitação";
    
    public NotificacaoFirebaseEventHandler(INotificacaoFirebaseBusiness notificacaoFirebaseBusiness)
    {
        _notificacaoFirebaseBusiness = notificacaoFirebaseBusiness;
    }
    
    public Task Consume(ConsumeContext<SolicitacoesCadastradasEmLoteEvent> context)
    {
        var notificacoes =
            context.Message.SolicitacoesCadastradasEmLote
                .Select(x =>
                    new NotificacaoRealtimeDto(x.<PERSON><PERSON>, TituloDaNovaSolicitacao, x.Mensa<PERSON>m,
                        new NotificationFirebaseDataDto
                        {
                            Tipo = "CadastroSolicitacao",
                            Id = x.Id,
                            Mensagem = x.Mensagem,
                            Sid = x.Usuario.Sid,
                            DataCriacao = x.CreatedAt.ToString("O"),
                            DataAtualizacao = x.UpdatedAt.ToString("O"),
                            SomNotificacao = TipoDeSom.Sucesso,
                            Status = x.Status.Descricao,
                            StatusId = x.Status.Id,
                            Local = x.Local
                        }))
                .ToList();

        return _notificacaoFirebaseBusiness.SendAsync(notificacoes, context.CancellationToken);
    }

    public Task Consume(ConsumeContext<SolicitacaoImpedidaEvent> context)
    {
        var titulo = $"Solicitação com status {context.Message.Solicitacao.Status.GetDescription()}";
        
        var notificacao = new NotificacaoRealtimeDto(context.Message.Origem, titulo, context.Message.Mensagem,
            new NotificationFirebaseDataDto
            {
                Tipo = "CadastroSolicitacao",
                Id = context.Message.Id,
                Mensagem = context.Message.Mensagem,
                Sid = context.Message.Solicitacao.Usuario.Sid,
                DataCriacao = context.Message.Criacao.ToString("O"),
                DataAtualizacao = context.Message.Atualizacao.ToString("O"),
                SomNotificacao = TipoDeSom.Sucesso,
                Status = context.Message.Solicitacao.Status.GetDescription(),
                StatusId = (short)context.Message.Solicitacao.Status,
                Local = context.Message.Local
            });
        
        return _notificacaoFirebaseBusiness.SendAsync(new List<NotificacaoRealtimeDto>{ notificacao }, context.CancellationToken);
    }
}