using MassTransit;
using ONS.SINapse.Business.IBusiness.Firebase;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Shared.DTO.Firebase;
using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Solicitacao.EventHandlers.Events;

namespace ONS.SINapse.Solicitacao.EventHandlers.Handlers;

public class NotificacaoFirebaseEventHandler :
    IConsumer<SolicitacoesCadastradasEmLoteEvent>,
    IConsumer<SolicitacaoImpedidaEvent>
{
    private readonly INotificacaoFirebaseBusiness _notificacaoFirebaseBusiness;
    
    private const string TituloDaNovaSolicitacao = "Nova solicitação";
    
    public NotificacaoFirebaseEventHandler(INotificacaoFirebaseBusiness notificacaoFirebaseBusiness)
    {
        _notificacaoFirebaseBusiness = notificacaoFirebaseBusiness;
    }
    
    public Task Consume(ConsumeContext<SolicitacoesCadastradasEmLoteEvent> context)
    {
        var notificacoes =
            context.Message.SolicitacoesCadastradasEmLote
                .Where(x => x.Status.Id != (short)StatusDeSolicitacao.AguardandoEnvio)
                .Select(x =>
                    new NotificacaoRealtimeDto(x.Destino, TituloDaNovaSolicitacao, x.Mensagem, "CadastroSolicitacao", x.Local))
                .ToList();

        return _notificacaoFirebaseBusiness.SendAsync(notificacoes, context.CancellationToken);
    }

    public Task Consume(ConsumeContext<SolicitacaoImpedidaEvent> context)
    {
        var titulo = $"Solicitação com status {StatusDeSolicitacao.Impedida.GetDescription()}";
        
        var notificacoes =
            context.Message.Solicitacoes
                .Select(x =>
                    new NotificacaoRealtimeDto(x.Origem, titulo, x.Mensagem, "CadastroSolicitacao", x.Local))
                .ToList();
        
        return _notificacaoFirebaseBusiness.SendAsync(notificacoes, context.CancellationToken);
    }
}