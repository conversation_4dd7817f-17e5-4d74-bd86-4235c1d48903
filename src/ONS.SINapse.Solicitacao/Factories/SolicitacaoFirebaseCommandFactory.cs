using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Shared.Constants;
using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Shared.Messages;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands.Base;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands.Firebase;
using ONS.SINapse.Solicitacao.Mapper.Manual;

namespace ONS.SINapse.Solicitacao.Factories;

public interface ISolicitacaoFirebaseCommandFactory
{
    Command ObterCommand<T>(List<Entities.Entities.Solicitacao> solicitacoes) where T : Command, ISolicitacaoFirebaseCommand;
}

public class SolicitacaoFirebaseCommandFactory : ISolicitacaoFirebaseCommandFactory
{
    private static readonly Dictionary<Type, Func<List<Entities.Entities.Solicitacao>, Command>> CommandFactories =
        new()
        {
            { 
                typeof(CriarSolicitacaoNoFirebaseCommand), 
                solicitacoes => new CriarSolicitacaoNoFirebaseCommand(solicitacoes.SelectMany(CriarSolicitacao).ToList()) 
            },
            { 
                typeof(TrocarDeStatusNoFirebaseCommand), 
                solicitacoes => new TrocarDeStatusNoFirebaseCommand(solicitacoes.SelectMany(TrocarStatusSolicitacao).ToList()) 
            },
            { 
                typeof(LeituraDeSolicitacaoNoFirebaseCommand), 
                solicitacoes => new LeituraDeSolicitacaoNoFirebaseCommand(solicitacoes.SelectMany(ConfirmarLeituraChatSolicitacao).ToList()) 
            },
            { 
                typeof(EntregaDeSolicitacaoNoFirebaseCommand), 
                solicitacoes => new EntregaDeSolicitacaoNoFirebaseCommand(solicitacoes.SelectMany(ConfirmarEntregaChatSolicitacao).ToList()) 
            },
            { 
                typeof(EncaminharSolicitacaoNoFirebaseCommand), 
                solicitacoes => new EncaminharSolicitacaoNoFirebaseCommand(solicitacoes.SelectMany(EncaminharSolicitacao).ToList()) 
            },
            { 
                typeof(CriarMensagemNoChatDeSolicitacaoFirebaseCommand), 
                solicitacoes => new CriarMensagemNoChatDeSolicitacaoFirebaseCommand(solicitacoes.SelectMany(CriarMensagemChatSolicitacao).ToList()) 
            },
            { 
                typeof(ConfirmarEnvioSolicitacaoNoFirebaseCommand), 
                solicitacoes => new ConfirmarEnvioSolicitacaoNoFirebaseCommand(solicitacoes.SelectMany(ConfirmarEnvioSolicitacao).ToList()) 
            }
        };

    /// <summary>
    /// Obtém um comando específico para Firebase baseado no tipo genérico <typeparamref name="T"/>.
    /// </summary>
    /// <typeparam name="T">O tipo do comando a ser obtido, deve ser um Command e ISolicitacaoFirebaseCommand.</typeparam>
    /// <param name="solicitacoes">A lista de solicitações de entidades para processar.</param>
    /// <returns>Uma instância do comando solicitado.</returns>
    /// <exception cref="ArgumentException">Lançada se o tipo do comando não estiver mapeado.</exception>
    public Command ObterCommand<T>(List<Entities.Entities.Solicitacao> solicitacoes) 
        where T : Command, ISolicitacaoFirebaseCommand
    {
        Type commandType = typeof(T);

        if (CommandFactories.TryGetValue(commandType, out var factory))
        {
            return factory.Invoke(solicitacoes);
        }

        throw new ArgumentException($"Command do tipo '{commandType.Name}' não mapeado.");
    }

    private static List<Dictionary<string, object?>> CriarSolicitacao(Entities.Entities.Solicitacao solicitacao)
    {
        if (!solicitacao.IsConcluida())
        {
            return
            [
                solicitacao.CriarSolicitacaoPainelDto().Flatten(root: $"{ColecoesFirebase.Solicitacao}/{solicitacao.Id}"),
                solicitacao.CriarSolicitacaoDto().Flatten(root: $"{ColecoesFirebase.CadastroSolicitacao}/{solicitacao.Id}")
            ];
        }
        
        object? remove = null;
        
        return  
        [
            remove!.Flatten(root: $"{ColecoesFirebase.Solicitacao}/{solicitacao.Id}"),
            remove!.Flatten(root: $"{ColecoesFirebase.CadastroSolicitacao}/{solicitacao.Id}")
        ];
    }
    
    private static List<Dictionary<string, object?>> TrocarStatusSolicitacao(Entities.Entities.Solicitacao solicitacao)
    {
        object? remove = null;
        
        if (!solicitacao.IsConcluida())
        {
            return
            [
                solicitacao.ToTrocaDeStatusPainelDto().Flatten(root: $"{ColecoesFirebase.Solicitacao}/{solicitacao.Id}"),
                remove!.Flatten(root: $"{ColecoesFirebase.Solicitacao}/{solicitacao.Id}/atrasada"),
                solicitacao.ToStatusDeSolicitacaoFirebaseDto().Flatten(root: $"{ColecoesFirebase.CadastroSolicitacao}/{solicitacao.Id}")
            ];
        }
        
        return  
        [
            remove!.Flatten(root: $"{ColecoesFirebase.Solicitacao}/{solicitacao.Id}"),
            solicitacao.ToStatusDeSolicitacaoFirebaseDto().Flatten(root: $"{ColecoesFirebase.CadastroSolicitacao}/{solicitacao.Id}")
        ];
    }
    
    private static List<Dictionary<string, object?>> ConfirmarLeituraChatSolicitacao(Entities.Entities.Solicitacao solicitacao)
    {
        if (solicitacao.IsConcluida()) return [];
        
        return
        [
            solicitacao.ToLeituraChatDto()
                .Flatten(root: $"{ColecoesFirebase.CadastroSolicitacao}/{solicitacao.Id}"),
            solicitacao.ToLeituraChatPainelDto()
                .Flatten(root: $"{ColecoesFirebase.Solicitacao}/{solicitacao.Id}")
        ];
    }
    private static List<Dictionary<string, object?>> ConfirmarEntregaChatSolicitacao(Entities.Entities.Solicitacao solicitacao)
    {
        if (solicitacao.IsConcluida()) return [];
        return
        [
            solicitacao.ToEntregaChatDto()
                .Flatten(root: $"{ColecoesFirebase.CadastroSolicitacao}/{solicitacao.Id}"),
            solicitacao.ToEntregaChatPainelDto()
                .Flatten(root: $"{ColecoesFirebase.Solicitacao}/{solicitacao.Id}")
        ];
    }

    private static List<Dictionary<string, object?>> EncaminharSolicitacao(Entities.Entities.Solicitacao solicitacao)
    {
        if (solicitacao.Status != StatusDeSolicitacao.Confirmada && solicitacao.Status != StatusDeSolicitacao.Pendente)
        {
            return [];
        }
        
        return solicitacao.Status == StatusDeSolicitacao.Pendente ? CriarSolicitacao(solicitacao) : Encaminhar(solicitacao);
    } 
    private static List<Dictionary<string, object?>> ConfirmarEnvioSolicitacao(Entities.Entities.Solicitacao solicitacao)
    {
        if (solicitacao.Status != StatusDeSolicitacao.Enviada && solicitacao.Status != StatusDeSolicitacao.Pendente)
        {
            return [];
        }
        
        var solicitacaoEnviada = solicitacao.Status == StatusDeSolicitacao.Pendente ? CriarSolicitacao(solicitacao) : ConfirmarEnvio(solicitacao);
        return solicitacaoEnviada;
    }

    private static List<Dictionary<string, object?>> Encaminhar(Entities.Entities.Solicitacao solicitacao)
    {
        return
        [
            solicitacao.ToEncaminharSolicitacaoDto()
                .Flatten(root: $"{ColecoesFirebase.CadastroSolicitacao}/{solicitacao.Id}"),
            solicitacao.ToEncaminharSolicitacaoPainelDto()
                .Flatten(root: $"{ColecoesFirebase.Solicitacao}/{solicitacao.Id}")
        ];
    }
    private static List<Dictionary<string, object?>> ConfirmarEnvio(Entities.Entities.Solicitacao solicitacao)
    {
        if (solicitacao.Status != StatusDeSolicitacao.Enviada)
        {
            return [];
        }
        
        object? remove = null;
        
        return  
        [
            remove!.Flatten(root: $"{ColecoesFirebase.Solicitacao}/{solicitacao.Id}"),
            solicitacao.ToStatusDeSolicitacaoFirebaseDto().Flatten(root: $"{ColecoesFirebase.CadastroSolicitacao}/{solicitacao.Id}")
        ];
    }

    private static List<Dictionary<string, object?>> CriarMensagemChatSolicitacao(Entities.Entities.Solicitacao solicitacao)
    {
        if (solicitacao.IsConcluida()) return [];

        var chat = solicitacao.Chat.Last();
        var dto = chat.ToChatDeSolicitacaoDto();
        return 
        [
            dto.Flatten(root: $"{ColecoesFirebase.CadastroSolicitacao}/{solicitacao.Id}"),
            solicitacao.ToChatDeSolicitacaoPainelDto()
                .Flatten(root: $"{ColecoesFirebase.Solicitacao}/{solicitacao.Id}")
        ];
    }
}