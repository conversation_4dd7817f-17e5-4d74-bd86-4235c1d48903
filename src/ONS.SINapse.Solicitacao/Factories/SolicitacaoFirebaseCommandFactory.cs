using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Shared.Constants;
using ONS.SINapse.Shared.DTO.Solicitacao;
using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Shared.Messages;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands.Base;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands.Firebase;
using ONS.SINapse.Solicitacao.Mapper.Manual;

namespace ONS.SINapse.Solicitacao.Factories;

public interface ISolicitacaoFirebaseCommandFactory
{
    Command ObterCommand<T>(List<Entities.Entities.Solicitacao> solicitacoes) where T : Command, ISolicitacaoFirebaseCommand;
}

public class SolicitacaoFirebaseCommandFactory : ISolicitacaoFirebaseCommandFactory
{
    private static readonly IReadOnlyDictionary<Type, Func<List<Entities.Entities.Solicitacao>, Command>> CommandFactories =
        new Dictionary<Type, Func<List<Entities.Entities.Solicitacao>, Command>>
        {
            { 
                typeof(CriarSolicitacaoNoFirebaseCommand), 
                solicitacoes => new CriarSolicitacaoNoFirebaseCommand(solicitacoes.SelectMany(CriarSolicitacao).ToList()) 
            },
            { 
                typeof(TrocarDeStatusNoFirebaseCommand), 
                solicitacoes => new TrocarDeStatusNoFirebaseCommand(solicitacoes.SelectMany(TrocarStatusSolicitacao).ToList()) 
            },
            { 
                typeof(LeituraDeSolicitacaoNoFirebaseCommand), 
                solicitacoes => new LeituraDeSolicitacaoNoFirebaseCommand(solicitacoes.SelectMany(ConfirmarLeituraChatSolicitacao).ToList()) 
            },
            { 
                typeof(EntregaDeSolicitacaoNoFirebaseCommand), 
                solicitacoes => new EntregaDeSolicitacaoNoFirebaseCommand(solicitacoes.SelectMany(ConfirmarEntregaChatSolicitacao).ToList()) 
            },
            { 
                typeof(EncaminharSolicitacaoNoFirebaseCommand), 
                solicitacoes => new EncaminharSolicitacaoNoFirebaseCommand(solicitacoes.SelectMany(EncaminharSolicitacao).ToList()) 
            },
            { 
                typeof(CriarMensagemNoChatDeSolicitacaoFirebaseCommand), 
                solicitacoes => new CriarMensagemNoChatDeSolicitacaoFirebaseCommand(solicitacoes.SelectMany(CriarMensagemChatSolicitacao).ToList()) 
            }
        };

    /// <summary>
    /// Obtém um comando específico para Firebase baseado no tipo genérico <typeparamref name="T"/>.
    /// </summary>
    /// <typeparam name="T">O tipo do comando a ser obtido, deve ser um Command e ISolicitacaoFirebaseCommand.</typeparam>
    /// <param name="solicitacoes">A lista de solicitações de entidades para processar.</param>
    /// <returns>Uma instância do comando solicitado.</returns>
    /// <exception cref="ArgumentException">Lançada se o tipo do comando não estiver mapeado.</exception>
    public Command ObterCommand<T>(List<Entities.Entities.Solicitacao> solicitacoes) 
        where T : Command, ISolicitacaoFirebaseCommand
    {
        Type commandType = typeof(T);

        if (CommandFactories.TryGetValue(commandType, out var factory))
        {
            return factory.Invoke(solicitacoes);
        }

        throw new ArgumentException($"Command do tipo '{commandType.Name}' não mapeado.");
    }

    private static List<Dictionary<string, object?>> CriarSolicitacao(Entities.Entities.Solicitacao solicitacao)
    {
        return
        [
            solicitacao.CriarSolicitacaoPainelDto().Flatten(root: $"{ColecoesFirebase.Solicitacao}/{solicitacao.Id}"),
            solicitacao.CriarSolicitacaoDto().Flatten(root: $"{ColecoesFirebase.CadastroSolicitacao}/{solicitacao.Id}")
        ];
    }
    
    private static List<Dictionary<string, object?>> TrocarStatusSolicitacao(Entities.Entities.Solicitacao solicitacao)
    {
        if (!solicitacao.IsConcluida())
        {
            return
            [
                solicitacao.ToTrocaDeStatusPainelDto().Flatten(root: $"{ColecoesFirebase.Solicitacao}/{solicitacao.Id}"),
                solicitacao.ToStatusDeSolicitacaoFirebaseDto().Flatten(root: $"{ColecoesFirebase.CadastroSolicitacao}/{solicitacao.Id}")
            ];
        }
        
        object? remove = null;
        return  
        [
            remove!.Flatten(root: $"{ColecoesFirebase.Solicitacao}/{solicitacao.Id}"),
            solicitacao.ToStatusDeSolicitacaoFirebaseDto().Flatten(root: $"{ColecoesFirebase.CadastroSolicitacao}/{solicitacao.Id}")
        ];
    }
    
    private static List<Dictionary<string, object?>> ConfirmarLeituraChatSolicitacao(Entities.Entities.Solicitacao solicitacao)
    {
        var result = new List<Dictionary<string, object?>>();

        if (solicitacao.IsConcluida()) return result;
        
        result.Add(solicitacao.ToLeituraChatDto().Flatten(root: $"{ColecoesFirebase.CadastroSolicitacao}/{solicitacao.Id}"));
        
        result.Add(solicitacao.ToLeituraChatPainelDto()
            .Flatten(root: $"{ColecoesFirebase.Solicitacao}/{solicitacao.Id}"));
        
        return result;
    }
    private static List<Dictionary<string, object?>> ConfirmarEntregaChatSolicitacao(Entities.Entities.Solicitacao solicitacao)
    {
        var result = new List<Dictionary<string, object?>>();

        if (solicitacao.IsConcluida()) return result;
        
        result.Add(solicitacao.ToEntregaChatDto()
            .Flatten(root: $"{ColecoesFirebase.CadastroSolicitacao}/{solicitacao.Id}"));
        
        result.Add(solicitacao.ToEntregaChatPainelDto()
            .Flatten(root: $"{ColecoesFirebase.Solicitacao}/{solicitacao.Id}"));
        
        return result;
    }

    private static IEnumerable<object> HistoricoPainel(Entities.Entities.Solicitacao solicitacao)
    {
        return solicitacao.HistoricosDeStatus.Select(c =>
            new
            {
                c.Status,
                c.StatusAnterior,
                c.Usuario,
                c.DataDeAlteracao
            });
    }

    private static object TrocaDeStatusPainel(Entities.Entities.Solicitacao solicitacao)
    {
        return new
        {
            solicitacao.Status,
            solicitacao.UpdatedAt,
            Chat = ChatPainel(solicitacao),
            HistoricosDeStatus = HistoricoPainel(solicitacao),
            solicitacao.DetalheDoImpedimento
        };
    }

    private static List<Dictionary<string, object?>> EncaminharSolicitacao(Entities.Entities.Solicitacao solicitacao)
    {
        if (solicitacao.Status != StatusDeSolicitacao.Confirmada && solicitacao.Status != StatusDeSolicitacao.Pendente)
        {
            return [];
        }
        
        if(solicitacao.Status == StatusDeSolicitacao.Pendente)
        {
            return CriarSolicitacao(solicitacao);
        }

        return Encaminhar(solicitacao);
    }

    private static List<Dictionary<string, object?>> Encaminhar(Entities.Entities.Solicitacao solicitacao)
    {
        return
        [
            solicitacao.ToEncaminharSolicitacaoDto().Flatten(root: $"{ColecoesFirebase.CadastroSolicitacao}/{solicitacao.Id}"),
            solicitacao.ToEncaminharSolicitacaoPainelDto().Flatten(root: $"{ColecoesFirebase.Solicitacao}/{solicitacao.Id}")
        ];
    }

    private static List<Dictionary<string, object?>> CriarMensagemChatSolicitacao(Entities.Entities.Solicitacao solicitacao)
    {
        List<Dictionary<string, object?>> result = new();
        
        if (solicitacao.IsConcluida()) return [];
        var chatCadastro = new
        {
            Chat = solicitacao.Chat.Select(c => c.ToChatDeSolicitacaoDto()).ToList(),
        };
        
        result.Add(chatCadastro.Flatten(root: $"{ColecoesFirebase.CadastroSolicitacao}/{solicitacao.Id}"));
        
        var chat = new
        {
            Chat = ChatPainel(solicitacao)
        };
        result.Add(chat.Flatten(root: $"{ColecoesFirebase.Solicitacao}/{solicitacao.Id}"));
        return result;
    }

    private static IEnumerable<object> ChatPainel(Entities.Entities.Solicitacao solicitacao)
    {
        return solicitacao.Chat
            .Select(c => new
            {
                c.DataEHoraDeEnvio,
                Lida = c.PrimeiraLeitura is not null,
                Entregue = c.PrimeiraEntrega is not null,
                c.Mensagem,
                Origem = c.Origem.Nome,
                c.Status,
                UsuarioRemetente = c.UsuarioRemetente.Nome
            });
    }
    
    private static IEnumerable<object> LeituraChatPainel(Entities.Entities.Solicitacao solicitacao)
    {
        return solicitacao.Chat
            .Select(c => new
            {
                Lida = c.PrimeiraLeitura is not null
            });
    }
    private static IEnumerable<object> EntregaChatPainel(Entities.Entities.Solicitacao solicitacao)
    {
        return solicitacao.Chat
            .Select(c => new
            {
                Entregue = c.PrimeiraEntrega is not null
            });
    }
}