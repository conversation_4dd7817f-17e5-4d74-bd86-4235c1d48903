using AutoMapper;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Shared.Messages;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using ONS.SINapse.Solicitacao.Dtos.Integracao;

namespace ONS.SINapse.Solicitacao.Factories.Integracao;

public interface ICriarCommandStatusDeSolicitacaoRecebidoFactory
{
    public List<Command<StatusDeSolicitacaoIntegracaoEnvioEmLoteDto>> ObterCommand(IEnumerable<StatusDeSolicitacaoIntegracaoRecebimentoDto> solicitacaoIntegracaoRecebimento);
}


public class CriarCommandStatusDeSolicitacaoRecebidoFactory : ICriarCommandStatusDeSolicitacaoRecebidoFactory
{
    private readonly IMapper _mapper;

    public CriarCommandStatusDeSolicitacaoRecebidoFactory(IMapper mapper)
    {
        _mapper = mapper;
    }
    
    public List<Command<StatusDeSolicitacaoIntegracaoEnvioEmLoteDto>> ObterCommand(IEnumerable<StatusDeSolicitacaoIntegracaoRecebimentoDto> solicitacaoIntegracaoRecebimento)
    {
        var factory =
            new Dictionary<StatusDeSolicitacao,
                Func<IEnumerable<StatusDeSolicitacaoIntegracaoRecebimentoDto>, Command<StatusDeSolicitacaoIntegracaoEnvioEmLoteDto>>>
            {
                {
                    StatusDeSolicitacao.Cancelada,
                    ObterCommand<CancelarEmLoteCommand>
                },
                {
                    StatusDeSolicitacao.CienciaInformada,
                    ObterCommand<InformarCienciaCommand>
                },
                {
                    StatusDeSolicitacao.Impedida,
                    ObterCommand<ImpedirCommand>
                },
                {
                    StatusDeSolicitacao.Confirmada,
                    ObterCommand<ConfirmarCommand>
                },
                {
                    StatusDeSolicitacao.Finalizada,
                    ObterCommand<FinalizarEmLoteCommand>
                }
            };

        return solicitacaoIntegracaoRecebimento
            .GroupBy(group => group.Status,
                by => by,
                (group, by)
                    => !factory.TryGetValue(group ?? StatusDeSolicitacao.Erro, out var factoryMethod)
                        ? null
                        : factoryMethod(by))
            .Where(x => x is not null)
            .ToList()!;
    }

    private T ObterCommand<T>(IEnumerable<StatusDeSolicitacaoIntegracaoRecebimentoDto> solicitacaoIntegracaoRecebimento) 
        => _mapper.Map<IEnumerable<StatusDeSolicitacaoIntegracaoRecebimentoDto>, T>(solicitacaoIntegracaoRecebimento);
}