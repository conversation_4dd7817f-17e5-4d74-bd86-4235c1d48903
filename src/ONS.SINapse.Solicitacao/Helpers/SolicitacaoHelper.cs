using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Shared.Constants;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Identity;
using ONS.SINapse.Solicitacao.Dtos.Integracao;

namespace ONS.SINapse.Solicitacao.Helpers;

public static class SolicitacaoHelper
{
    public static StatusDeSolicitacaoIntegracaoEnvioDto CreateErrorDto(
        string id,
        UsuarioDto usuario,
        string? motivo,
        string sistemaDeOrigem,
        params string[] errors)
    {
        return new StatusDeSolicitacaoIntegracaoEnvioDto(
            id,
            StatusDeSolicitacao.Erro,
            usuario,
            DateTime.UtcNow,
            motivo,
            sistemaDeOrigem,
            errors
        );
    }

    public static StatusDeSolicitacaoIntegracaoEnvioDto CreateErrorDto(
        StatusDeSolicitacaoIntegracaoRecebimentoDto solicitacaoDto,
        params string[] errors)
    {
        return CreateErrorDto(
            solicitacaoDto.Id ?? string.Empty,
            solicitacaoDto.Usuario ?? new UsuarioDto(),
            solicitacaoDto.Motivo,
            solicitacaoDto.SistemaDeOrigem ?? Entities.Entities.Solicitacao.SistemaDeOrigemInterno,
            errors
        );
    }

    public static StatusDeSolicitacao[] ObterStatusFinaisDeSolitacaoPorPerfil(Perfil perfil)
    {
        return perfil.Operacoes.Contains(Operacoes.AprovarEnvioSolicitacao) switch
        {
            true =>
            [
                StatusDeSolicitacao.Cancelada, 
                StatusDeSolicitacao.Enviada,
                StatusDeSolicitacao.EnvioCancelado,
                StatusDeSolicitacao.Finalizada
            ],
            false => [StatusDeSolicitacao.Cancelada, StatusDeSolicitacao.Finalizada]
        };
    }
}