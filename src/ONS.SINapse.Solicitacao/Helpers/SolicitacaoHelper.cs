using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Solicitacao.Dtos.Integracao;

namespace ONS.SINapse.Solicitacao.Helpers
{
    public static class SolicitacaoHelper
    {
        public static StatusDeSolicitacaoIntegracaoEnvioDto CreateErrorDto(
            string id,
            UsuarioDto usuario,
            string? motivo,
            string sistemaDeOrigem,
            params string[] errors)
        {
            return new StatusDeSolicitacaoIntegracaoEnvioDto(
                id,
                StatusDeSolicitacao.Erro,
                usuario,
                DateTime.UtcNow,
                motivo,
                sistemaDeOrigem,
                errors
            );
        }

        public static StatusDeSolicitacaoIntegracaoEnvioDto CreateErrorDto(
            StatusDeSolicitacaoIntegracaoRecebimentoDto solicitacaoDto,
            params string[] errors)
        {
            return CreateErrorDto(
                solicitacaoDto.Id ?? string.Empty,
                solicitacaoDto.Usuario ?? new UsuarioDto(),
                solicitacaoDto.Motivo,
                solicitacaoDto.SistemaDeOrigem ?? Entities.Entities.Solicitacao.SistemaDeOrigemInterno,
                errors
            );
        }
    }
}