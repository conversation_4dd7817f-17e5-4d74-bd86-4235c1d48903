using AutoMapper;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Solicitacao.Mapper.Manual;

namespace ONS.SINapse.Solicitacao.Mapper;

public class StatusSolicitacaoFirebaseMapper : Profile
{
    public StatusSolicitacaoFirebaseMapper()
    {
        // Usado quando confirmar leitura, confirmar entrega, ou quando uma mensagem de chat e enviada
        CreateMap<Entities.Entities.Solicitacao, ChatDeSolicitacaoFirebaseDto>()
            .IgnoreAllPropertiesWithAnInaccessibleSetter()
            .ConstructUsing((solicitacao, context)
                => new ChatDeSolicitacaoFirebaseDto(solicitacao.Id, solicitacao.Chat.Select(c => c.FromEntity()).ToList()));
        
        // Usado para alterações de Confirmar e Informar Ciência
        CreateMap<Entities.Entities.Solicitacao, StatusDeSolicitacaoFirebaseDto>()
            .IgnoreAllPropertiesWithAnInaccessibleSetter()
            .ConstructUsing((solicitacao, context)
                => new StatusDeSolicitacaoFirebaseDto
                (
                    solicitacao.Id,
                    solicitacao.Status,
                    solicitacao.UpdatedAt,
                    solicitacao.Chat.Select(c => c.FromEntity()).ToList(),
                    solicitacao.HistoricosDeStatus.Select(c => c.FromEntity()).ToList()
                ))
            .ForMember(x => x.DetalheDoImpedimento,
                m => m.MapFrom(
                    solicitacao => solicitacao.Status == StatusDeSolicitacao.Impedida
                        ? solicitacao.DetalheDoImpedimento!
                        : null));
    }
}