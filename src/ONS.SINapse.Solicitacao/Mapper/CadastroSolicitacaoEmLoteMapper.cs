using AutoMapper;
using ONS.SINapse.Business.Imp.Builders;
using ONS.SINapse.Entities.Entities;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using ONS.SINapse.Solicitacao.EventHandlers.Events;
using ONS.SINapse.Solicitacao.EventHandlers.IntegrationEvents;

namespace ONS.SINapse.Solicitacao.Mapper;

public class CadastroSolicitacaoEmLoteMapper : Profile
{
    public CadastroSolicitacaoEmLoteMapper()
    {
        CreateMap<CadastroSolicitacaoDto, Entities.Entities.Solicitacao>()
            .ConvertUsing<CadastroSolicitacaoDtoToSolicitacaoConverter>();

        CreateMap<Entities.Entities.Solicitacao, SolicitacaoCadastradasEmLoteDto>()
            .IgnoreAllPropertiesWithAnInaccessibleSetter()
            .ConstructUsing((solicitacao, _) =>
                new SolicitacaoCadastradasEmLoteDto(
                    solicitacao.Id,
                    solicitacao.Origem.Codigo,
                    solicitacao.Mensagem,
                    new UsuarioDto
                    {
                        Nome = solicitacao.UsuarioDeCriacao.Nome,
                        Login = solicitacao.UsuarioDeCriacao.Login,
                        Sid = solicitacao.UsuarioDeCriacao.Sid
                    },
                    solicitacao.Destino.Codigo,
                    new StatusDeSolicitacaoDto
                    (
                        (short)solicitacao.Status,
                        solicitacao.Status.GetDescription()
                    )
                )
                {
                    Agente = solicitacao.EncaminharPara?.Codigo,
                    Local = solicitacao.Local?.Codigo,
                    CreatedAt = solicitacao.CreatedAt,
                    UpdatedAt = solicitacao.UpdatedAt,
                    SistemaDeOrigem = solicitacao.SistemaDeOrigem
                });

        CreateMap<CadastroDeSolicitacaoExternaRecebidaIntegrationEvent, List<CadastroSolicitacaoDto>>()
            .ConstructUsing((src, ctx) => src.Solicitacoes
                .Select(solicitacao => {
                    var dto = new CadastroSolicitacaoDto();
                    dto.DefinirComoSolicitacaoExterna(solicitacao.SistemaDeOrigem!, solicitacao.Id!);
                    return ctx.Mapper.Map<CadastroSolicitacaoDto>(solicitacao);
                }).ToList());

        CreateMap<CadastroDeSolicitacaoExternaDto, CadastroSolicitacaoDto>()
            .ForMember(x => x.Origem, map => map.MapFrom(x => x.Origem))
            .ForMember(x => x.Destino, map => map.MapFrom(x => x.Destino))
            .ForMember(x => x.Local, map => map.MapFrom(x => x.Local))
            .ForMember(x => x.EncaminharPara, map => map.MapFrom(x => x.EncaminharPara))
            .ForMember(x => x.InformacaoAdicional, map => map.MapFrom(x => x.InformacaoAdicional))
            .ForMember(x => x.Mensagem, map => map.MapFrom(x => x.Mensagem))
            .ForMember(x => x.Motivo, map => map.MapFrom(x => x.Motivo))
            .ForMember(x => x.Tags, map => map.MapFrom(x => x.Tags))
            .ForMember(x => x.RequerAprovacaoEnvio, map => map.MapFrom(x => x.RequerAprovacaoEnvio));
    }
}

public class CadastroSolicitacaoDtoToSolicitacaoConverter : ITypeConverter<CadastroSolicitacaoDto, Entities.Entities.Solicitacao>
{
    public Entities.Entities.Solicitacao Convert(CadastroSolicitacaoDto source, Entities.Entities.Solicitacao destination, ResolutionContext context)
    {
        var solicitacaoBuilder = new SolicitacaoBuilder();

        solicitacaoBuilder
            .WithOrigem(Convert(source.Origem)!)
            .WithDestino(Convert(source.Destino)!)
            .WithLocal(Convert(source.Local))
            .WithAgente(Convert(source.EncaminharPara))
            .WithInformacaoAdicional(source.InformacaoAdicional)
            .WithMensagem(source.Mensagem!)
            .WithUsuario(new Usuario(source.Usuario!.Sid, source.Usuario!.Login, source.Usuario!.Nome))
            .WithMotivo(source.Motivo)
            .WithLoteId(source.Lote.ToString())
            .WithTag(source.Tags.ToArray())
            .WithEncaminhada(source.SolicitacaoDeOrigem is not null)
            .WithSolicitacaoDeOrigemId(source.SolicitacaoDeOrigem)
            .WithRequerAprovacaoEnvio(source.RequerAprovacaoEnvio);

        if (source.Externo)
            solicitacaoBuilder.WithSistemaExterno(source.SistemaDeOrigem!, source.Id!);

        return solicitacaoBuilder.Build(source.Id);
    }

    private static ObjetoDeManobra? Convert(ObjetoDeManobraDto? obj)
        => obj is null ? null : new ObjetoDeManobra(obj.Codigo, obj.Nome);
}