using System.Text;
using CsvHelper.Configuration;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;

namespace ONS.SINapse.Solicitacao.Mapper;

public sealed class ArquivoConsultaHistoricoAnaliticoMapper : ClassMap<ArquivoConsultaHistoricoAnaliticaDto>
{
    public ArquivoConsultaHistoricoAnaliticoMapper()
    {
        Map(x => x.Codigo)
            .Name("Código")
            .Index(0);
        
        Map(x => x.Origem)
            .Name("Origem")
            .Index(1);
        
        Map(x => x.Destino)
            .Name("Destino")
            .Index(2);

        Map(x => x.Motivo)
            .Name("Motivo")
            .Index(3);

        Map(x => x.Mensagem)
            .Name("Mensagem")
            .Index(4)
            .Convert(x => $"\"{x.Value.Mensagem}\"");

        Map(x => x.InformacaoAdicional)
            .Name("Informação Adicional")
            .Index(5)
            .Convert(x => $"\"{x.Value.InformacaoAdicional}\"");

        Map(x => x.DataDeCriacao)
            .Name("Hora de Cadastro")
            .Index(6)
            .Convert(x =>
                x.Value.DataDeCriacao.ToFormattedSouthAmericaStandardTime());
        
        Map(x => x.UsuarioDeCriacao)
            .Name("Usuário de Cadastro")
            .Index(7);
        
        Map(x => x.CodigoStatus)
            .Name("Status solicitação")
            .Index(8)
            .Convert(x =>
            {
                var status = (StatusDeSolicitacao)x.Value.CodigoStatus;
                return status.GetDescription();
            });

        Map(x => x.DataDeAlteracao)
            .Name("Horário status")
            .Index(9)
            .Convert(x =>
                x.Value.DataDeAlteracao is null
                    ? string.Empty
                    : x.Value.DataDeAlteracao?.ToFormattedSouthAmericaStandardTime());
        
        Map(x => x.UsuarioDeAlteracao)
            .Name("Usuário status")
            .Index(10);

        Map(x => x.UsuarioEntrega)
            .Name("Usuário entregue")
            .Index(11);

        Map(x => x.DataDeEntrega)
            .Name("Horário entregue")
            .Index(12)
            .Convert(x =>
                x.Value.DataDeEntrega is null
                    ? string.Empty
                    : x.Value.DataDeEntrega?.ToFormattedSouthAmericaStandardTime("dd/MM/yyyy HH:mm:ss"));
        
        Map(x => x.UsuarioLeitura)
            .Name("Usuário lido")
            .Index(13);

        Map(x => x.DataDeLeitura)
            .Name("Horário leitura")
            .Index(14)
            .Convert(x =>
                x.Value.DataDeLeitura is null
                    ? string.Empty
                    : x.Value.DataDeLeitura?.ToFormattedSouthAmericaStandardTime("dd/MM/yyyy HH:mm:ss"));

        Map(x => x.MotivoImpedimento)
            .Name("Motivo impedimento")
            .Convert(x => SanitizarMotivoImpedimentoString(x.Value.MotivoImpedimento))
            .Index(15);
    }

    public static string? SanitizarMotivoImpedimentoString(string? input)
    {
        if (string.IsNullOrWhiteSpace(input))
        {
            return input;
        }

        var separator = input.Contains('\r') ? Environment.NewLine : "\n";

        var splits = input.Split(separator, StringSplitOptions.RemoveEmptyEntries | StringSplitOptions.TrimEntries);

        var sb = new StringBuilder();

        foreach (var str in splits)
        {
            sb.Append(str);
            if (!str.EndsWith('.'))
            {
                sb.Append('.');
            }

            sb.Append(' ');
        }
        
        sb.Remove(sb.Length - 2, 2);
        
        return splits.Length switch
        {
            0 => input,
            1 => splits[0],
            _ => sb.ToString()
        };
    }
}