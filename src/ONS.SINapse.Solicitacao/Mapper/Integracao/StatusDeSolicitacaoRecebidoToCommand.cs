using AutoMapper;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using ONS.SINapse.Solicitacao.Dtos.Integracao;

namespace ONS.SINapse.Solicitacao.Mapper.Integracao;

public class StatusDeSolicitacaoRecebidoToCommand : Profile
{
    public StatusDeSolicitacaoRecebidoToCommand()
    {
        CreateMap<IEnumerable<StatusDeSolicitacaoIntegracaoRecebimentoDto>, CancelarEmLoteCommand>()
            .ConvertUsing(source => new CancelarEmLoteCommand(source.ToList()));
        
        CreateMap<IEnumerable<StatusDeSolicitacaoIntegracaoRecebimentoDto>, InformarCienciaCommand>()
            .ConvertUsing(source => new InformarCienciaCommand(source.ToList()));
        
        CreateMap<IEnumerable<StatusDeSolicitacaoIntegracaoRecebimentoDto>, ImpedirCommand>()
            .ConvertUsing(source => new ImpedirCommand(source.ToList()));
        
        CreateMap<IEnumerable<StatusDeSolicitacaoIntegracaoRecebimentoDto>, ConfirmarCommand>()
            .ConvertUsing(source => new ConfirmarCommand(source.ToList()));
        
        CreateMap<IEnumerable<StatusDeSolicitacaoIntegracaoRecebimentoDto>, FinalizarEmLoteCommand>()
            .ConvertUsing(source => new FinalizarEmLoteCommand(source.ToList()));
        CreateMap<IEnumerable<StatusDeSolicitacaoIntegracaoRecebimentoDto>, CancelarEnvioEmLoteCommand>()
            .ConvertUsing(source => new CancelarEnvioEmLoteCommand(source.ToList()));
        CreateMap<IEnumerable<StatusDeSolicitacaoIntegracaoRecebimentoDto>, ConfirmarEnvioEmLoteCommand>()
            .ConvertUsing(source => new ConfirmarEnvioEmLoteCommand(source.ToList()));
    }
}