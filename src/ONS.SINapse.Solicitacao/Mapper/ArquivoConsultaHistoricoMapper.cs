using CsvHelper.Configuration;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using ONS.SINapse.Solicitacao.QueryHandlers.Queries;

namespace ONS.SINapse.Solicitacao.Mapper;

public sealed class ArquivoConsultaHistoricoMapper : ClassMap<ArquivoConsultaHistoricoDto>
{
    public ArquivoConsultaHistoricoMapper()
    {
        Map(x => x.Codigo)
            .Name("Código")
            .Index(0);

        Map(x => x.DataDeCadastro)
            .Name("Data de Cadastro")
            .Index(1);

        Map(x => x.HoraDeCadastro)
            .Name("Hora de Cadastro")
            .Index(2);
        
        Map(x => x.UsuarioDeCadastro)
            .Name("Usuário de Cadastro")
            .Index(3);
        
        Map(x => x.Origem)
            .Name("Origem")
            .Index(4);
        
        Map(x => x.Destino)
            .Name("Destino")
            .Index(5);
        
        Map(x => x.Mensagem)
            .Name("Mensagem")
            .Index(6)
            .Convert(x => $"\"{x.Value.Mensagem}\"");
        
        Map(x => x.CodigoStatus)
            .Name("Status")
            .Index(7)
            .Convert(x =>
            {
                var status = (StatusDeSolicitacao)x.Value.CodigoStatus;
                return status.GetDescription();
            });
        
        Map(x => x.InformacaoAdicional)
            .Name("Informação Adicional")
            .Index(8)
            .Convert(x => $"\"{x.Value.InformacaoAdicional}\"");

        Map(x => x.DataDeAlteracao)
            .Name("Data de Alteração")
            .Index(9);

        Map(x => x.HoraDeAlteracao)
            .Name("Hora de Alteração")
            .Index(10);
        
        Map(x => x.UsuarioDeAlteracao)
            .Name("Usuário de Alteração")
            .Index(11);
    }   
}