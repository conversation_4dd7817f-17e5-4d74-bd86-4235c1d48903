using AutoMapper;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Solicitacao.Dtos;
using ONS.SINapse.Solicitacao.EventHandlers.Events;
using ONS.SINapse.Solicitacao.Workers.Messages;

namespace ONS.SINapse.Solicitacao.Mapper;

public class ConfirmarSolicitacaoMapper : Profile
{
    public ConfirmarSolicitacaoMapper()
    {
        CreateMap<Entities.Entities.Solicitacao, SolicitacaoConfirmadaDto>()
            .ConstructUsing((solicitacao, context) =>
            {
                var historicos = context.Mapper.Map<List<HistoricoAlteracaoStatusDto>>(solicitacao.HistoricosDeStatus);
                var usuario = solicitacao.GetUsuarioDaConfirmacao();
                var usuarioDto = new UsuarioDto(usuario!.Sid, usuario.Nome, usuario.Login);
                
                return new SolicitacaoConfirmadaDto(solicitacao.Id, solicitacao.CodigoExterno,
                    solicitacao.SistemaDeOrigem, usuarioDto, historicos, new List<string>());
            })
            .ForAllMembers(x => x.Ignore());
        
        
        CreateMap<SolicitacaoConfirmadaDto, AtualizacaoStatusMessage>()
            .ConstructUsing((dto, context) => new AtualizacaoStatusMessage
            {
                CodigoDaSolicitacao = dto.Id,
                CodigoExterno = dto.CodigoExterno,
                StatusAtualId = (short)dto.Status,
                DescricaoStatusAtual = dto.Status.GetDescription(),
                UsuarioDaConfirmacao = dto.Usuario.Nome,
                LoginDoUsuarioDaConfirmacao = dto.Usuario.Login,
                SidDoUsuarioDaConfirmacao = dto.Usuario.Sid,
                Historicos = context.Mapper.Map<List<HistoricoDoStatusDto>>(dto.HistoricoAlteracao),
                Erros = dto.Erros.ToList()
            })
            .ForAllMembers(x => x.Ignore());
    }
}