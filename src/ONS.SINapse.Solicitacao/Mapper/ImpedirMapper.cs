using AutoMapper;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Solicitacao.Dtos;
using ONS.SINapse.Solicitacao.EventHandlers.Events;
using ONS.SINapse.Solicitacao.Workers.Messages;

namespace ONS.SINapse.Solicitacao.Mapper;

public class ImpedirMapper : Profile
{
    public ImpedirMapper()
    {
        CreateMap<Entities.Entities.Solicitacao, SolicitacaoImpedidaDto>()
            .ConstructUsing((solicitacao, _) =>
            {
                var usuario = solicitacao.GetUsuarioDoImpedimento();
                var usuarioDto = new UsuarioDto(usuario!.Sid, usuario.Nome, usuario.Login);
                
                return new SolicitacaoImpedidaDto(
                    solicitacao.Origem.Codigo,
                    solicitacao.Id,
                    solicitacao.Local?.Codigo,
                    solicitacao.CreatedAt,
                    solicitacao.UpdatedAt,
                    solicitacao.Mensagem,
                    usuarioDto,
                    solicitacao.Status
                    );
            })
            .ForAllMembers(x => x.Ignore());
    }
}