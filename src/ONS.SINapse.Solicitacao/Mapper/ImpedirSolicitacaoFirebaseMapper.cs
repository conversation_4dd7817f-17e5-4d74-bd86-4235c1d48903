using AutoMapper;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Extensions;

namespace ONS.SINapse.Solicitacao.Mapper;

public class ImpedirSolicitacaoFirebaseMapper : Profile
{
    public ImpedirSolicitacaoFirebaseMapper()
    {
        CreateMap<Entities.Entities.Solicitacao, ImpedirSolicitacaoFirebaseDto>()
            .IgnoreAllPropertiesWithAnInaccessibleSetter()
            .ConstructUsing((solicitacao, context)
                => new ImpedirSolicitacaoFirebaseDto
                (
                    solicitacao.Id,
                    solicitacao.Status.GetDescription(),
                    (short)solicitacao.Status,
                    solicitacao.UpdatedAt,
                    context.Mapper.Map<List<ChatDeSolicitacaoDto>>(solicitacao.Chat),
                    context.Mapper.Map<List<HistoricoDeSolicitacaoDto>>(solicitacao.HistoricosDeStatus),
                    new ImpedimentoDto(solicitacao.DetalheDoImpedimento!)
                ));
    }
}