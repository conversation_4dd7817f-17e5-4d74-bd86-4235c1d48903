using AutoMapper;
using ONS.SINapse.Integracao.Shared.Dtos;
using ONS.SINapse.Shared.Extensions;

namespace ONS.SINapse.Solicitacao.Mapper;

public class ObterStatusCadastroSolicitacaoQueryMapper : Profile
{
    public ObterStatusCadastroSolicitacaoQueryMapper()
    {
        CreateMap<CadastroSolicitacaoStatusDto, StatusDeSolicitacaoIntegracaoOutputDto>()
            .IgnoreAllPropertiesWithAnInaccessibleSetter()
            .ConstructUsing(cadastroSolicitacaoStatusDto => Convert(cadastroSolicitacaoStatusDto));
    }


    private static StatusDeSolicitacaoIntegracaoOutputDto Convert(CadastroSolicitacaoStatusDto cadastroSolicitacaoStatusDto)
    {
        var mensagemAtual = cadastroSolicitacaoStatusDto.Chat.FirstOrDefault(m => m.Status == cadastroSolicitacaoStatusDto.StatusAtualId);
        return new StatusDeSolicitacaoIntegracaoOutputDto(
            cadastroSolicitacaoStatusDto.CodigoDaSolicitacao,
            cadastroSolicitacaoStatusDto.CodigoExterno,
            (short)cadastroSolicitacaoStatusDto.StatusAtualId,
            cadastroSolicitacaoStatusDto.StatusAtualId.GetDescription(),
            cadastroSolicitacaoStatusDto.HistoricosDeStatus.Select(historico => new HistoricoDoStatusDto
                (
                    historico.DataDeAlteracao,
                    (short)historico.Status,
                    historico.Status.GetDescription(),
                    historico.Usuario.Nome,
                    historico.Usuario.Login,
                    historico.Usuario.Sid
                )
            ))
        {
            UsuarioDoImpedimento = cadastroSolicitacaoStatusDto.GetUsuarioDoImpedimento()?.Nome,
            LoginDoUsuarioDoImpedimento = cadastroSolicitacaoStatusDto.GetUsuarioDoImpedimento()?.Login,
            SidDoUsuarioDoImpedimento = cadastroSolicitacaoStatusDto.GetUsuarioDoImpedimento()?.Sid,
            UsuarioDaConfirmacao = cadastroSolicitacaoStatusDto.GetUsuarioDaConfirmacao()?.Nome,
            LoginDoUsuarioDaConfirmacao = cadastroSolicitacaoStatusDto.GetUsuarioDaConfirmacao()?.Login,
            SidDoUsuarioDaConfirmacao = cadastroSolicitacaoStatusDto.GetUsuarioDaConfirmacao()?.Sid,
            Justificativa = cadastroSolicitacaoStatusDto.Justificativa,
            Enviada = mensagemAtual?.DataEHoraDeEnvio != null,
            DataEHoraDeEnvio = mensagemAtual?.DataEHoraDeEnvio
        };
    }
}