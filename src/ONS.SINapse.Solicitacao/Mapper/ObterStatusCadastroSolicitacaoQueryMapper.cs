using AutoMapper;
using ONS.SINapse.Integracao.Shared.Dtos;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Solicitacao.Dtos;
using ONS.SINapse.Solicitacao.QueryHandlers.Dtos;

namespace ONS.SINapse.Solicitacao.Mapper;

public class ObterStatusCadastroSolicitacaoQueryMapper : Profile
{
    public ObterStatusCadastroSolicitacaoQueryMapper()
    {
        CreateMap<CadastroSolicitacaoStatusDto, Dtos.StatusDeSolicitacaoIntegracaoDto>()
            .IgnoreAllPropertiesWithAnInaccessibleSetter()
            .ConstructUsing(cadastroSolicitacaoStatusDto => Convert(cadastroSolicitacaoStatusDto));
    }


    private static Dtos.StatusDeSolicitacaoIntegracaoDto Convert(CadastroSolicitacaoStatusDto cadastroSolicitacaoStatusDto)
    {
        return new Dtos.StatusDeSolicitacaoIntegracaoDto(
            cadastroSolicitacaoStatusDto.Id,
            cadastroSolicitacaoStatusDto.HistoricosDeStatus.Select(historico => new HistoricoDoStatusDto
                (
                    new StatusDoHistoricoDto((short)historico.Status, historico.Status.GetDescription()),
                    new UsuarioDto(historico.Usuario.Sid, historico.Usuario.Nome, historico.Usuario.Login),
                    historico.DataDeAlteracao,
                    historico.Status == StatusDeSolicitacao.Impedida
                        ? cadastroSolicitacaoStatusDto.Justificativa
                        : null)
            ));
    }
}