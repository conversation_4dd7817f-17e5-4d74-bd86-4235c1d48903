using AutoMapper;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Extensions;

namespace ONS.SINapse.Solicitacao.Mapper;

public class EncaminharSolicitacaoFirebaseMapper : Profile
{
    public EncaminharSolicitacaoFirebaseMapper()
    {
        CreateMap<Entities.Entities.Solicitacao, EncaminharSolicitacaoFirebaseDto>()
            .IgnoreAllPropertiesWithAnInaccessibleSetter()
            .ConstructUsing((solicitacao, context)
                => new EncaminharSolicitacaoFirebaseDto
                (
                    solicitacao.Id,
                    solicitacao.Status.GetDescription(),
                    (short)solicitacao.Status,
                    solicitacao.UpdatedAt,
                    context.Mapper.Map<List<ChatDeSolicitacaoDto>>(solicitacao.Chat),
                    context.Mapper.Map<List<HistoricoDeSolicitacaoDto>>(solicitacao.HistoricosDeStatus)
                ));
    }
}