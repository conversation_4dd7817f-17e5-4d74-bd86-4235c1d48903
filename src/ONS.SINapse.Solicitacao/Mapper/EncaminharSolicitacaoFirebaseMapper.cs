using AutoMapper;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Solicitacao.Mapper.Manual;

namespace ONS.SINapse.Solicitacao.Mapper;

public class EncaminharSolicitacaoFirebaseMapper : Profile
{
    public EncaminharSolicitacaoFirebaseMapper()
    {
        CreateMap<Entities.Entities.Solicitacao, EncaminharSolicitacaoFirebaseDto>()
            .IgnoreAllPropertiesWithAnInaccessibleSetter()
            .ConstructUsing((solicitacao, context)
                => new EncaminharSolicitacaoFirebaseDto
                (
                    solicitacao.Id,
                    solicitacao.Status,
                    solicitacao.UpdatedAt,
                    solicitacao.Chat.Select(c => c.FromEntity()).ToList(),
                    solicitacao.HistoricosDeStatus.Select(c => c.FromEntity()).ToList()
                ));
    }
}