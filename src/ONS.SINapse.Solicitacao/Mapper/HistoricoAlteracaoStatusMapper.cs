using AutoMapper;
using ONS.SINapse.Entities.Entities;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Solicitacao.Dtos;

namespace ONS.SINapse.Solicitacao.Mapper;

public class HistoricoAlteracaoStatusMapper : Profile
{
    public HistoricoAlteracaoStatusMapper()
    {
        CreateMap<HistoricoDeStatusDeSolicitacao, HistoricoAlteracaoStatusDto>()
            .ConstructUsing((historico, _) =>
            {
                var usuario = new UsuarioDto(historico.Usuario.Sid, historico.Usuario.Nome, historico.Usuario.Login);
                return new HistoricoAlteracaoStatusDto(historico.DataDeAlteracao, historico.Status, usuario);
            })
            .ForAllMembers(x => x.Ignore());
    }
}