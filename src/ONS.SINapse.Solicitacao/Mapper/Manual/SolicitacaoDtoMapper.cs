using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.DTO.Solicitacao;

namespace ONS.SINapse.Solicitacao.Mapper.Manual;

public static class SolicitacaoDtoMapper
{
    public static SolicitacaoDto CriarSolicitacaoDto(this Entities.Entities.Solicitacao solicitacao)
    {
        return new SolicitacaoDto(
            solicitacao.Id,
            new UsuarioDto(solicitacao.UsuarioDeCriacao.Sid, solicitacao.UsuarioDeCriacao.Nome, solicitacao.UsuarioDeCriacao.Login),
            new ObjetoDeManobraDto(solicitacao.Origem.Codigo, solicitacao.Origem.Nome),
            new ObjetoDeManobraDto(solicitacao.Destino.Codigo, solicitacao.Destino.Nome),
            solicitacao.Mensagem,
            solicitacao.Tags,
            solicitacao.SistemaDeOrigem
        )
        {
            Local = solicitacao.Local is null ? null : new ObjetoDeManobraDto(solicitacao.Local.Codigo, solicitacao.Local.Nome),
            EncaminharPara = solicitacao.EncaminharPara is null ? null : new ObjetoDeManobraDto(solicitacao.EncaminharPara.Codigo, solicitacao.EncaminharPara.Nome),
            InformacaoAdicional = solicitacao.InformacaoAdicional,
            Status = solicitacao.Status,
            FinalizadaAutomaticamente = solicitacao.FinalizadaAutomaticamente,
            DetalheDoImpedimento = solicitacao.DetalheDoImpedimento,
            HistoricosDeStatus = solicitacao.HistoricosDeStatus.Select(h => h.ToHistoricoDeSatatusDto()).ToList(),
            Chat = solicitacao.Chat.Select(c => c.ToChatDeSolicitacaoDto()).ToList(),
            UpdatedAt = solicitacao.UpdatedAt,
            CreatedAt = solicitacao.CreatedAt,
            IsExterna = solicitacao.IsExterna,
            CodigoExterno = solicitacao.CodigoExterno,
            LoteId = solicitacao.LoteId,
            Motivo = solicitacao.Motivo,
            Encaminhada = solicitacao.Encaminhada,
            SolicitacaoDeOrigemId = solicitacao.SolicitacaoDeOrigemId
        };
    }
    
    public static StatusDeSolicitacaoFirebaseDto ToStatusDeSolicitacaoFirebaseDto(this Entities.Entities.Solicitacao solicitacao)
    {
        return new StatusDeSolicitacaoFirebaseDto(
            solicitacao.Id,
            solicitacao.Status,
            solicitacao.UpdatedAt,
            solicitacao.Chat.Select(c => c.ToChatDeSolicitacaoDto()).ToList(),
            solicitacao.HistoricosDeStatus.Select(c => c.ToHistoricoDeSatatusDto()).ToList(),
            solicitacao.DetalheDoImpedimento
        );
    }
    
    public static StatusDeSolicitacaoSimplificadoDto ToStatusDeSolicitacaoSimplificadoDto(this Entities.Entities.Solicitacao solicitacao)
    {
        return new StatusDeSolicitacaoSimplificadoDto(
            solicitacao.Id,
            solicitacao.Status,
            solicitacao.UpdatedAt,
            chat: null,
            solicitacao.ToListStatusPainelDtoDto(),
            solicitacao.DetalheDoImpedimento
        );
    }
}

