using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.DTO.Solicitacao;

namespace ONS.SINapse.Solicitacao.Mapper.Manual;

public static class SolicitacaoDtoMapper
{
    public static EntregaChatDto ToEntregaChatDto(this Entities.Entities.Solicitacao solicitacao)
    {
        return new EntregaChatDto(solicitacao.Chat
            .Select(c => new EntregaChatItemDto(
                c.DataEHoraDeEntregaAoDestinatario ?? DateTime.MinValue, 
                c.PrimeiraEntrega?.ToRegistroUsuarioHoraDto())).ToList());
    }
    public static LeituraChatDto ToLeituraChatDto(this Entities.Entities.Solicitacao solicitacao)
    {
        return new LeituraChatDto(solicitacao.Chat
            .Select(c => new LeituraChatItemDto(
                c.DataEHoraDeLeituraDoDestinatario ?? DateTime.MinValue, 
                c.PrimeiraLeitura?.ToRegistroUsuarioHoraDto())).ToList());
    }
    public static ChatDeSolicitacaoDto ToChatDeSolicitacaoDto(this Entities.Entities.Solicitacao solicitacao)
    {
        return new ChatDeSolicitacaoDto(
            solicitacao.Chat.Select(c => c.ToChatDeSolicitacaoDto()).ToList()
        );
    }
    
    public static ChatDeSolicitacaoItemDto ToChatDeSolicitacaoDto(this Entities.Entities.ChatDeSolicitacao chatDeSolicitacao)
    {
        return new ChatDeSolicitacaoItemDto(
            chatDeSolicitacao.Mensagem,
            new UsuarioDto(chatDeSolicitacao.UsuarioRemetente.Sid, chatDeSolicitacao.UsuarioRemetente.Nome, chatDeSolicitacao.UsuarioRemetente.Login),
            new ObjetoDeManobraDto(chatDeSolicitacao.Origem.Codigo, chatDeSolicitacao.Origem.Nome),
            chatDeSolicitacao.Status,
            chatDeSolicitacao.DataEHoraDeEnvio
        )
        {
            PrimeiraLeitura = chatDeSolicitacao.PrimeiraLeitura?.ToRegistroUsuarioHoraDto(),
            PrimeiraEntrega = chatDeSolicitacao.PrimeiraEntrega?.ToRegistroUsuarioHoraDto(),
            DataEHoraDeEntregaAoDestinatario = chatDeSolicitacao.DataEHoraDeEntregaAoDestinatario,
            DataEHoraDeLeituraDoDestinatario = chatDeSolicitacao.DataEHoraDeLeituraDoDestinatario,
        };
    }
    
    public static ChatDeSolicitacaoItemDto ToChatTrocaDeStatusDeSolicitacaoDto(this Entities.Entities.ChatDeSolicitacao chatDeSolicitacao)
    {
        return new ChatDeSolicitacaoItemDto(
            chatDeSolicitacao.Mensagem,
            new UsuarioDto(chatDeSolicitacao.UsuarioRemetente.Sid, chatDeSolicitacao.UsuarioRemetente.Nome, chatDeSolicitacao.UsuarioRemetente.Login),
            new ObjetoDeManobraDto(chatDeSolicitacao.Origem.Codigo, chatDeSolicitacao.Origem.Nome),
            chatDeSolicitacao.Status,
            chatDeSolicitacao.DataEHoraDeEnvio
        )
        {
            PrimeiraLeitura = chatDeSolicitacao.PrimeiraLeitura?.ToRegistroUsuarioHoraDto(),
            PrimeiraEntrega = chatDeSolicitacao.PrimeiraEntrega?.ToRegistroUsuarioHoraDto(),
            DataEHoraDeEntregaAoDestinatario = chatDeSolicitacao.DataEHoraDeEntregaAoDestinatario,
            DataEHoraDeLeituraDoDestinatario = chatDeSolicitacao.DataEHoraDeLeituraDoDestinatario,
        };
    }

    public static Dictionary<int, ChatDeSolicitacaoItemDto> ToDictionaryChatDeSolicitacaoDto(this Entities.Entities.Solicitacao solicitacao)
    {
        var dictionary = new Dictionary<int, ChatDeSolicitacaoItemDto>();
        var i = 0;
        while (dictionary.Count < solicitacao.Chat.Count)
        {
            dictionary.Add(i, solicitacao.Chat.ElementAt(i).ToChatDeSolicitacaoDto());
            i++;
        }
        return dictionary;
    }
    
    public static Dictionary<int, ChatDeSolicitacaoItemDto> ToDictionaryChatDeSolicitacaoDto(this Entities.Entities.ChatDeSolicitacao chat, int position)
    {
        var dictionary = new Dictionary<int, ChatDeSolicitacaoItemDto> { { position, chat.ToChatDeSolicitacaoDto() } };
        return dictionary;
    }
    
    public static SolicitacaoDto CriarSolicitacaoDto(this Entities.Entities.Solicitacao solicitacao)
    {
        return new SolicitacaoDto(
            solicitacao.Id,
            new UsuarioDto(solicitacao.UsuarioDeCriacao.Sid, solicitacao.UsuarioDeCriacao.Nome, solicitacao.UsuarioDeCriacao.Login),
            new ObjetoDeManobraDto(solicitacao.Origem.Codigo, solicitacao.Origem.Nome),
            new ObjetoDeManobraDto(solicitacao.Destino.Codigo, solicitacao.Destino.Nome),
            solicitacao.Mensagem,
            solicitacao.Tags,
            solicitacao.SistemaDeOrigem
        )
        {
            Local = solicitacao.Local is null ? null : new ObjetoDeManobraDto(solicitacao.Local.Codigo, solicitacao.Local.Nome),
            EncaminharPara = solicitacao.EncaminharPara is null ? null : new ObjetoDeManobraDto(solicitacao.EncaminharPara.Codigo, solicitacao.EncaminharPara.Nome),
            InformacaoAdicional = solicitacao.InformacaoAdicional,
            Status = solicitacao.Status,
            FinalizadaAutomaticamente = solicitacao.FinalizadaAutomaticamente,
            DetalheDoImpedimento = solicitacao.DetalheDoImpedimento,
            HistoricosDeStatus = solicitacao.HistoricosDeStatus.Select(h => h.ToHistoricoDeSatatusDto()).ToList(),
            Chat = solicitacao.Chat.Select(c => c.ToChatDeSolicitacaoDto()).ToList(),
            UpdatedAt = solicitacao.UpdatedAt,
            CreatedAt = solicitacao.CreatedAt,
            IsExterna = solicitacao.IsExterna,
            CodigoExterno = solicitacao.CodigoExterno,
            LoteId = solicitacao.LoteId,
            Motivo = solicitacao.Motivo,
            Encaminhada = solicitacao.Encaminhada,
            SolicitacaoDeOrigemId = solicitacao.SolicitacaoDeOrigemId,
            DataInicioCadastro = solicitacao.DataInicioCadastro
        };
    }
    
    public static StatusDeSolicitacaoFirebaseDto ToStatusDeSolicitacaoFirebaseDto(this Entities.Entities.Solicitacao solicitacao)
    {
        var chat = solicitacao.Chat.Last();
        var position = solicitacao.Chat.Count - 1;
        return new StatusDeSolicitacaoFirebaseDto(
            solicitacao.Status,
            solicitacao.UpdatedAt,
            chat.ToDictionaryChatDeSolicitacaoDto(position),
            solicitacao.HistoricosDeStatus.Select(c => c.ToHistoricoDeSatatusDto()).ToList(),
            solicitacao.DetalheDoImpedimento
        );
    }
    
    public static LeituraChatPainelDto ToLeituraChatPainelDto(this Entities.Entities.Solicitacao solicitacao)
    {
        var dictionary = new Dictionary<int, LeituraChatPainelItemDto>();
        var position = 0;
        while (position < solicitacao.Chat.Count)
        {
            var chat = solicitacao.Chat.ElementAt(position);
            if (chat.LidaPeloDestinatario() && chat.Status == solicitacao.Status)
            {
                dictionary.Add(position, new LeituraChatPainelItemDto(true));
            }
            
            position++;
        }
        return new LeituraChatPainelDto(dictionary);
    }
    
    public static EntregaChatPainelDto ToEntregaChatPainelDto(this Entities.Entities.Solicitacao solicitacao)
    {
        var position = 0;
        var dictionary = new Dictionary<int, EntregaChatPainelItemDto>();
        while (position < solicitacao.Chat.Count)
        {
            var chat = solicitacao.Chat.ElementAt(position);
            if (chat.EntregueAoDestinatario() && chat.Status == solicitacao.Status)
            {
                dictionary.Add(position, new EntregaChatPainelItemDto(true));
            }
            
            position++;
        }
        
        return new EntregaChatPainelDto(dictionary);
    }
    
    public static EncaminharSolicitacaoDto ToEncaminharSolicitacaoDto(this Entities.Entities.Solicitacao solicitacao)
    {
        return new EncaminharSolicitacaoDto(
            solicitacao.Status,
            solicitacao.UpdatedAt,
            solicitacao.Chat.Select(c => c.ToChatDeSolicitacaoDto()).ToList(),
            solicitacao.HistoricosDeStatus.Select(c => c.ToHistoricoDeSatatusDto()).ToList(),
            solicitacao.Encaminhada
        );
    }
}

