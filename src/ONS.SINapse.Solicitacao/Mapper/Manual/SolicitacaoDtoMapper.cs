using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.DTO.Solicitacao;

namespace ONS.SINapse.Solicitacao.Mapper.Manual;

public static class SolicitacaoDtoMapper
{
    public static SolicitacaoDto FromEntity(this Entities.Entities.Solicitacao solicitacao)
    {
        return new SolicitacaoDto(
            solicitacao.Id,
            new UsuarioDto(solicitacao.UsuarioDeCriacao.Sid, solicitacao.UsuarioDeCriacao.Nome, solicitacao.UsuarioDeCriacao.Login),
            new ObjetoDeManobraDto(solicitacao.Origem.Codigo, solicitacao.Origem.Nome),
            new ObjetoDeManobraDto(solicitacao.Destino.Codigo, solicitacao.Destino.Nome),
            solicitacao.Mensagem,
            solicitacao.Tags,
            solicitacao.SistemaDeOrigem
        )
        {
            Local = solicitacao.Local is null ? null : new ObjetoDeManobraDto(solicitacao.Local.Codigo, solicitacao.Local.Nome),
            EncaminharPara = solicitacao.EncaminharPara is null ? null : new ObjetoDeManobraDto(solicitacao.EncaminharPara.Codigo, solicitacao.EncaminharPara.Nome),
            InformacaoAdicional = solicitacao.InformacaoAdicional,
            Status = solicitacao.Status,
            FinalizadaAutomaticamente = solicitacao.FinalizadaAutomaticamente,
            DetalheDoImpedimento = solicitacao.DetalheDoImpedimento,
            HistoricosDeStatus = solicitacao.HistoricosDeStatus.Select(h => h.FromEntity()).ToList(),
            Chat = solicitacao.Chat.Select(c => c.FromEntity()).ToList(),
            UpdatedAt = solicitacao.UpdatedAt,
            IsExterna = solicitacao.IsExterna,
            CodigoExterno = solicitacao.CodigoExterno,
            LoteId = solicitacao.LoteId,
            Motivo = solicitacao.Motivo,
            Encaminhada = solicitacao.Encaminhada,
            SolicitacaoDeOrigemId = solicitacao.SolicitacaoDeOrigemId
        };
    }
}

