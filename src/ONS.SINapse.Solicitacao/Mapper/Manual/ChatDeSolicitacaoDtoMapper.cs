using ONS.SINapse.Entities.Entities;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.DTO.Solicitacao;

namespace ONS.SINapse.Solicitacao.Mapper.Manual;

public static class ChatDeSolicitacaoDtoMapper
{
    public static ChatDeSolicitacaoDto FromEntity(this ChatDeSolicitacao chatDeSolicitacao)
    {
        return new ChatDeSolicitacaoDto(
            chatDeSolicitacao.Mensagem,
            new UsuarioDto(chatDeSolicitacao.UsuarioRemetente.Sid, chatDeSolicitacao.UsuarioRemetente.Nome, chatDeSolicitacao.UsuarioRemetente.Login),
            new ObjetoDeManobraDto(chatDeSolicitacao.Origem.Codigo, chatDeSolicitacao.Origem.Nome),
            chatDeSolicitacao.Status,
            chatDeSolicitacao.DataEHoraDeEnvio
        )
        {
            PrimeiraLeitura = chatDeSolicitacao.PrimeiraLeitura is not null
                ? chatDeSolicitacao.PrimeiraLeitura.FromEntity()
                : null,
            PrimeiraEntrega = chatDeSolicitacao.PrimeiraEntrega is not null
                ? chatDeSolicitacao.PrimeiraEntrega.FromEntity()
                : null,
            DataEHoraDeEntregaAoDestinatario = chatDeSolicitacao.DataEHoraDeEntregaAoDestinatario,
            DataEHoraDeLeituraDoDestinatario = chatDeSolicitacao.DataEHoraDeLeituraDoDestinatario,
        };
    }
}
