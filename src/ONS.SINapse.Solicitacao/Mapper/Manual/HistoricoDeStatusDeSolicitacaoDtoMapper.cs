using ONS.SINapse.Entities.Entities;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.DTO.Solicitacao;

namespace ONS.SINapse.Solicitacao.Mapper.Manual;

public static class HistoricoDeStatusDeSolicitacaoDtoMapper
{
    public static HistoricoDeStatusDeSolicitacaoDto ToHistoricoDeSatatusDto(this HistoricoDeStatusDeSolicitacao historicoDeStatusDeSolicitacao)
    {
        return new HistoricoDeStatusDeSolicitacaoDto
        {
            Status = historicoDeStatusDeSolicitacao.Status,
            StatusAnterior = historicoDeStatusDeSolicitacao.StatusAnterior,
            Usuario = new UsuarioDto(historicoDeStatusDeSolicitacao.Usuario.Sid, historicoDeStatusDeSolicitacao.Usuario.Nome, historicoDeStatusDeSolicitacao.Usuario.Login),
            DataDeAlteracao = historicoDeStatusDeSolicitacao.DataDeAlteracao
        };
    }
}
