using ONS.SINapse.Entities.Entities;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.DTO.Solicitacao;

namespace ONS.SINapse.Solicitacao.Mapper.Manual;

public static class RegistroUsuarioHoraDtoMapper
{
    public static RegistroUsuarioHoraDto ToRegistroUsuarioHoraDto(this RegistroUsuarioHora registroUsuarioHora)
    {
        return new RegistroUsuarioHoraDto(
            new UsuarioDto(registroUsuarioHora.Usuario.Sid, registroUsuarioHora.Usuario.Nome, registroUsuarioHora.Usuario.Login),
            registroUsuarioHora.Data);
    }
}