using ONS.SINapse.Entities.Entities;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.DTO.Solicitacao;

namespace ONS.SINapse.Solicitacao.Mapper.Manual;

public static class SolicitacaoPainelDtoMapper
{
    public static SolicitacaoPainelDto CriarSolicitacaoPainelDto(this Entities.Entities.Solicitacao solicitacao)
    {
        return new SolicitacaoPainelDto(
            solicitacao.Id,
            new ObjetoDeManobraDto(solicitacao.Origem.Codigo, solicitacao.Origem.Nome),
            new ObjetoDeManobraDto(solicitacao.Destino.Codigo, solicitacao.Destino.Nome),
            solicitacao.Mensagem,
            solicitacao.ToDictionaryStatusPainelDtoDto(),
            solicitacao.ToLastMensagenChatDeSolicitacaoPainelDto(),
            solicitacao.SistemaDeOrigem
        )
        {
            CreatedAt = solicitacao.CreatedAt,
            UpdatedAt = solicitacao.UpdatedAt,
            Motivo = solicitacao.Motivo,
            Status = solicitacao.Status,
            Encaminhada = false,
            Tags = solicitacao.Tags,
            EncaminharPara = solicitacao.EncaminharPara is null ? null : new ObjetoDeManobraDto(solicitacao.EncaminharPara.Codigo, solicitacao.EncaminharPara.Nome),
            Local = solicitacao.Local is null ? null : new ObjetoDeManobraDto(solicitacao.Local.Codigo, solicitacao.Local.Nome),
            InformacaoAdicional = solicitacao.InformacaoAdicional,
        };
    }
    
    public static HistoricoDeStatusPainelDto ToHistoricoDeStatusPainelDto(this Entities.Entities.Solicitacao solicitacao)
    {
        return new HistoricoDeStatusPainelDto(solicitacao.ToListStatusPainelDtoDto());
    }
    
    public static List<StatusPainelDto> ToListStatusPainelDtoDto(this Entities.Entities.Solicitacao solicitacao)
    {
        return solicitacao.HistoricosDeStatus
            .Select(c => new StatusPainelDto(c.Status))
            .ToList();
    }
    
    public static  Dictionary<int, StatusPainelDto> ToDictionaryStatusPainelDtoDto(this Entities.Entities.Solicitacao solicitacao)
    {
        var dictionary = new Dictionary<int, StatusPainelDto>();
        var i = 0;
        
        while (dictionary.Count < solicitacao.HistoricosDeStatus.Count)
        {
            dictionary.Add(i, new StatusPainelDto(solicitacao.HistoricosDeStatus.ElementAt(i).Status));
            i++;
        }

        return dictionary;
    }
    
    public static ChatDeSolicitacaoPainelDto ToChatDeSolicitacaoPainelDto(this Entities.Entities.Solicitacao solicitacao)
    {
        return new ChatDeSolicitacaoPainelDto(solicitacao.ToLastMensagenChatDeSolicitacaoPainelDto());
    }

    public static MensagenChatDeSolicitacaoPainelDto ToMensagenChatDeSolicitacaoPainelDto(this ChatDeSolicitacao chatDeSolicitacao)
    {
        var chat = new MensagenChatDeSolicitacaoPainelDto(
            chatDeSolicitacao.Mensagem,
            chatDeSolicitacao.UsuarioRemetente.Nome,
            chatDeSolicitacao.Origem.Nome,
            chatDeSolicitacao.Status,
            chatDeSolicitacao.DataEHoraDeEnvio
        );
        
        return chat;
    }
    
    public static Dictionary<int, MensagenChatDeSolicitacaoPainelDto> ToAllMensagenChatDeSolicitacaoPainelDto(this Entities.Entities.Solicitacao solicitacao)
    {
        var dictionary = new Dictionary<int, MensagenChatDeSolicitacaoPainelDto>();
        var i = 0;
        
        while (dictionary.Count < solicitacao.Chat.Count)
        {
            dictionary.Add(i, solicitacao.Chat.ElementAt(i).ToMensagenChatDeSolicitacaoPainelDto());
            i++;
        }

        return dictionary;
    }
    
    public static Dictionary<int, MensagenChatDeSolicitacaoPainelDto> ToLastMensagenChatDeSolicitacaoPainelDto(this Entities.Entities.Solicitacao solicitacao)
    {
        var dictionary = new Dictionary<int, MensagenChatDeSolicitacaoPainelDto>();
        var position = solicitacao.Chat.Count - 1;
        var last = solicitacao.Chat.ElementAt(position);
        dictionary.Add(position, last.ToMensagenChatDeSolicitacaoPainelDto());
        return dictionary;
    }
    
    public static TrocaDeStatusPainelDto ToTrocaDeStatusPainelDto(this Entities.Entities.Solicitacao solicitacao)
    {
        return new TrocaDeStatusPainelDto(
            solicitacao.Status,
            solicitacao.UpdatedAt,
            solicitacao.ToLastMensagenChatDeSolicitacaoPainelDto(),
            solicitacao.ToDictionaryStatusPainelDtoDto(),
            solicitacao.DetalheDoImpedimento
        );
    }

    public static EncaminharSolicitacaoPainelDto ToEncaminharSolicitacaoPainelDto(
        this Entities.Entities.Solicitacao solicitacao)
    {
        return new EncaminharSolicitacaoPainelDto(
            solicitacao.Status,
            solicitacao.UpdatedAt,
            solicitacao.ToLastMensagenChatDeSolicitacaoPainelDto(),
            solicitacao.ToDictionaryStatusPainelDtoDto(), 
            solicitacao.Encaminhada
        );
    }
}