using AutoMapper;
using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Solicitacao.Dtos;
using ONS.SINapse.Solicitacao.EventHandlers.Events;
using ONS.SINapse.Solicitacao.Workers.Messages;

namespace ONS.SINapse.Solicitacao.Mapper;

public class InformarCienciaMapper : Profile
{
    public InformarCienciaMapper()
    {
        CreateMap<Entities.Entities.Solicitacao, CienciaInformadaDto>()
            .ConstructUsing((solicitacao, context) =>
            {
                var historicos = context.Mapper.Map<List<HistoricoAlteracaoStatusDto>>(solicitacao.HistoricosDeStatus);
                return new CienciaInformadaDto(solicitacao.Id, solicitacao.CodigoExterno,
                    solicitacao.SistemaDeOrigem, historicos, new List<string>());
            })
            .ForAllMembers(x => x.Ignore());
        
        
        CreateMap<CienciaInformadaDto, AtualizacaoStatusMessage>()
            .ConstructUsing((dto, context) => new AtualizacaoStatusMessage
            {
                CodigoDaSolicitacao = dto.Id,
                CodigoExterno = dto.CodigoExterno,
                StatusAtualId = (short)dto.Status,
                DescricaoStatusAtual = dto.Status.GetDescription(),
                Historicos = context.Mapper.Map<List<HistoricoDoStatusDto>>(dto.HistoricoAlteracao),
                Erros = dto.Erros.ToList()
            })
            .ForAllMembers(x => x.Ignore());
    }
}