using ONS.SINapse.Entities.Entities.Mapper;
using ONS.SINapse.Shared.DTO.Solicitacao;
using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Integracao.Shared.Enums;
namespace ONS.SINapse.Entities.Entities;

public partial class Solicitacao : ICloneable
{
    public static Solicitacao FromDto(SolicitacaoDto dto)
    {
        return new Solicitacao(
            dto.Chat.Select(c => c.ToChatDeSolicitacaoItemDto()).ToList()
        )
        {
            Id = dto.Id,
            UsuarioDeCriacao = new Usuario(dto.UsuarioDeCriacao.Sid, dto.UsuarioDeCriacao.Nome, dto.UsuarioDeCriacao.Login),
            Origem = new ObjetoDeManobra(dto.Origem.Codigo, dto.Origem.Nome),
            Destino = new ObjetoDeManobra(dto.Destino.Codigo, dto.Destino.Nome),
            Mensagem = dto.Mensagem,
            MensagemNormalizada = dto.Mensagem.RemoverCaracteresEspeciais().ToLower(),
            Tags = dto.Tags ?? [],
            SistemaDeOrigem = dto.SistemaDeOrigem,
            Local = dto.Local is null ? null : new ObjetoDeManobra(dto.Local.Codigo, dto.Local.Nome),
            EncaminharPara = dto.EncaminharPara is null ? null : new ObjetoDeManobra(dto.EncaminharPara.Codigo, dto.EncaminharPara.Nome),
            InformacaoAdicional = dto.InformacaoAdicional,
            LoteId = dto.LoteId,
            Motivo = dto.Motivo,
            SolicitacaoDeOrigemId = dto.SolicitacaoDeOrigemId,

            HistoricosDeStatus = dto.HistoricosDeStatus
                .Select(h => h.ToHistoricoDeStatusDeSolicitacao())
                .ToList(),

            FinalizadaAutomaticamente = dto.FinalizadaAutomaticamente,
            DetalheDoImpedimento = dto.DetalheDoImpedimento,
            UpdatedAt = dto.UpdatedAt,
            IsExterna = dto.IsExterna,
            CodigoExterno = dto.CodigoExterno,
            Encaminhada = dto.Encaminhada,
            Status = dto.Status,
            CreatedAt = dto.CreatedAt,
            DataInicioCadastro = dto.DataInicioCadastro
        };
    }

    public object Clone()
    {
        var chatClonado = _chat.Select(c => c.Clone()).ToList();
        var clone = new Solicitacao(chatClonado)
        {
            Id = Id,
            Origem = Origem,
            Destino = Destino,
            Local = Local,
            EncaminharPara = EncaminharPara,
            InformacaoAdicional = InformacaoAdicional,
            Mensagem = Mensagem,
            MensagemNormalizada = MensagemNormalizada,
            Status = Status,
            FinalizadaAutomaticamente = FinalizadaAutomaticamente,
            DetalheDoImpedimento = DetalheDoImpedimento,
            UsuarioDeCriacao = UsuarioDeCriacao,
            UpdatedAt = UpdatedAt,
            IsExterna = IsExterna,
            CodigoExterno = CodigoExterno,
            LoteId = LoteId,
            SistemaDeOrigem = SistemaDeOrigem,
            Motivo = Motivo,
            Encaminhada = Encaminhada,
            SolicitacaoDeOrigemId = SolicitacaoDeOrigemId,
            Tags = Tags,
            CreatedAt = CreatedAt,
            DataInicioCadastro = DataInicioCadastro,
        };

        foreach (var h in HistoricosDeStatus)
        {
            clone.HistoricosDeStatus.Add(h.Clone());
        }

        return clone;
    }

    public bool IsConcluida()
    {
        StatusDeSolicitacao[] status = [
            StatusDeSolicitacao.Cancelada, 
            StatusDeSolicitacao.Finalizada, 
            StatusDeSolicitacao.EnvioCancelado, 
            StatusDeSolicitacao.Enviada
        ];
        
        return status.Contains(Status);
    }
}
