using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using ONS.SINapse.Entities.Entities.Base;
using ONS.SINapse.Shared.CustomAttributes;

namespace ONS.SINapse.Entities.Entities;

[BsonCollection("c_historicoacesso")]
public sealed class HistoricoAcesso : Entidade
{
    private HistoricoAcesso(ObjetoDeManobra centroAgente)
    {
        CentroAgente = centroAgente;
        Inicio = DateTime.Now;
    }
    
    [BsonConstructor]
    public HistoricoAcesso(string id, ObjetoDeManobra centroAgente, DateTime inicio, DateTime? fim)
        : base(id)
    {
        CentroAgente = centroAgente;
        Inicio = inicio;
        Fim = fim;
    }
    
    [BsonElement("obj_centroagente")]
    [BsonRequired]
    public ObjetoDeManobra CentroAgente { get; private set; }
    
    [BsonElement("din_inicio")]
    [BsonRequired]
    public DateTime Inicio { get; private set; }
    
    [BsonElement("din_fim")]
    public DateTime? Fim { get; private set; }

    public void FinalizarAcesso(DateTime? data = null)
    {
        Fim = data ?? DateTime.Now;
    }
    
    public static HistoricoAcesso IniciarAcesso(ObjetoDeManobra centroAgente) => new(centroAgente);
}