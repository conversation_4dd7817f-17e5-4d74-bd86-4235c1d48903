using MongoDB.Bson.Serialization.Attributes;
using Newtonsoft.Json;

namespace ONS.SINapse.Entities.Entities.Base;

public abstract class IntegrarAnalitico : Entidade
{
    protected IntegrarAnalitico(DateTime? createdAt = null)
        : base(createdAt ?? DateTime.Now)
    {
    }

    protected IntegrarAnalitico(string id)
        : base(id)
    {
    }
    
    [JsonIgnore]
    [BsonElement("flg_enviadoanalitico")]
    public bool EnviadoAoAnalitico { get; private set; }
    
    [JsonIgnore]
    [BsonElement("din_enviadoanalitico")]
    public DateTime? DataEnvioAoAnalitico { get; private set; }

    public void RegistrarExtracao()
    {
        EnviadoAoAnalitico = true;
        DataEnvioAoAnalitico = DateTime.Now;
    }
}