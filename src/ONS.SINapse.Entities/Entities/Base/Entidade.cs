using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using Newtonsoft.Json;

namespace ONS.SINapse.Entities.Entities.Base;

public abstract class Entidade
{
    private Entidade(string id, DateTime dataCriacao)
    {
        Id = id;
        CreatedAt = dataCriacao;
    }
        
    protected Entidade(DateTime createdAt) 
        : this(ObjectId.GenerateNewId().ToString(), createdAt)
    {
    }

    protected Entidade(string id) : this(id, DateTime.Now)
    {
    }
        
    protected Entidade() : this(ObjectId.GenerateNewId().ToString(), DateTime.Now)
    {
    }
        
    [BsonId]
    [BsonRepresentation(BsonType.String)]
    public string Id { get; protected set; }

    [BsonRequired]
    [BsonElement("din_criacao")]
    public DateTime CreatedAt { get; private set; }

    public override string ToString()
        => JsonConvert.SerializeObject(this);
}