using MongoDB.Bson;
using MongoDB.Bson.Serialization;
using MongoDB.Bson.Serialization.Attributes;
using ONS.SINapse.Entities.Entities.Base;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Shared.Constants;
using ONS.SINapse.Shared.CustomAttributes;
using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Shared.Factories;
using ONS.SINapse.Shared.Identity;

namespace ONS.SINapse.Entities.Entities;

[BsonCollection("c_solicitacao")]
public partial class Solicitacao : IntegrarAnalitico
{
    public const string SistemaDeOrigemInterno = "SINapse";
    public const string CodigoDeOrigemCNOS = "CN";

    public Solicitacao(
        string id,
        Usuario usuario,
        ObjetoDeManobra origem,
        ObjetoDeManobra destino,
        string mensagem,
        string[] tags,
        string sistemaDeOrigem)
          : base(id)
    {
        HistoricosDeStatus = new List<HistoricoDeStatusDeSolicitacao>();
        UsuarioDeCriacao = usuario;
        Origem = origem;
        Destino = destino;
        Mensagem = mensagem;
        MensagemNormalizada = mensagem.RemoverCaracteresEspeciais().ToLower();
        Tags = tags;
        SistemaDeOrigem = sistemaDeOrigem;
        SetStatusPendente(usuario);

        _chat = [
            new ChatDeSolicitacao(MensagensPredefinidasDoChat.SolicitacaoCriada, usuario, origem,
                StatusDeSolicitacao.Pendente)
        ];
    }


    [BsonConstructor("_chat")]
    private Solicitacao(List<ChatDeSolicitacao> chatDeSolicitacao) 
    {
        HistoricosDeStatus = new List<HistoricoDeStatusDeSolicitacao>();
        UsuarioDeCriacao = new Usuario(string.Empty, string.Empty, string.Empty);
        Origem = new ObjetoDeManobra(string.Empty, string.Empty);
        Destino = new ObjetoDeManobra(string.Empty, string.Empty);
        Mensagem = string.Empty;
        MensagemNormalizada = string.Empty;
        Tags = [];
        SistemaDeOrigem = string.Empty;

        _chat = chatDeSolicitacao;
    }

    [BsonRequired]
    [BsonElement("obj_origem")]
    public ObjetoDeManobra Origem { get; private set; }

    [BsonRequired]
    [BsonElement("obj_destino")]
    public ObjetoDeManobra Destino { get; private set; }

    [BsonElement("obj_local")]
    public ObjetoDeManobra? Local { get; init; }
    
    [BsonElement("obj_encaminharpara")]
    public ObjetoDeManobra? EncaminharPara { get; init; }

    [BsonElement("dsc_informacaoadicional")]
    public string? InformacaoAdicional { get; init; }

    [BsonRequired]
    [BsonElement("dsc_mensagem")]
    public string Mensagem { get; private set; }

    [BsonElement("dsc_mensagemnormalizada")]
    public string MensagemNormalizada { get; private set; }

    [BsonRequired]
    [BsonElement("cod_status")]
    public StatusDeSolicitacao Status { get; private set; }

    [BsonRequired]
    [BsonElement("flg_finalizadaautomaticamente")]
    public bool FinalizadaAutomaticamente { get; private set; }

    [BsonElement("dsc_detalheimpedimento")]
    public string? DetalheDoImpedimento { get; private set; }

    [BsonRequired]
    [BsonElement("obj_usuario")]
    public Usuario UsuarioDeCriacao { get; private set; }

    [BsonRequired]
    [BsonElement("list_historicostatus")]
    public ICollection<HistoricoDeStatusDeSolicitacao> HistoricosDeStatus { get; private set; }

    [BsonRequired]
    [BsonElement("list_chat")]
    private readonly List<ChatDeSolicitacao> _chat; 
    
    [BsonIgnore]
    public IReadOnlyCollection<ChatDeSolicitacao> Chat => _chat.AsReadOnly();
    
    
    [BsonRequired]
    [BsonElement("data_atualizacao")]
    public DateTime UpdatedAt { get; private set; }

    [BsonElement("flg_externa")]
    public bool IsExterna { get; private set; }

    [BsonElement("cod_externo")]
    public string? CodigoExterno { get; private set; }

    [BsonElement("id_lote")]
    public string? LoteId { get; init; }

    [BsonElement("cod_sistemaorigem")]
    public string SistemaDeOrigem { get; private set; }

    [BsonElement("dsc_motivo")]
    public string? Motivo { get; init; }

    [BsonElement("flg_encaminhada")]
    public bool Encaminhada { get; private set; }

    [BsonElement("id_solicitacaoorigem")]
    public string? SolicitacaoDeOrigemId { get; init; }

    [BsonElement("list_tags")]
    public string[] Tags { get; private set; }

    public void DefinirComoSolicitacaoExterna(string sistema, string codigo)
    {
        SistemaDeOrigem = sistema;
        CodigoExterno = codigo.Trim();
        IsExterna = !string.IsNullOrEmpty(codigo);
    }
    
    public void Encaminhar(Usuario usuario)
    {
        Encaminhada = true;
        Confirmar(usuario);
    }
    
    public bool ConfirmarLeitura(Usuario usuario, Perfil perfil)
    {
        var dataEHoraDeLeitura = DateTime.UtcNow;
        var mensagensNaoLidas = _chat.Where(mensagem => mensagem.PrimeiraLeitura is null && !perfil.Centros.Contains(mensagem.Origem.Codigo)).ToList();
    
        if (mensagensNaoLidas.Count == 0) return false;
    
        foreach (var mensagem in mensagensNaoLidas)
        {
            if (mensagem.PrimeiraEntrega is null)
                mensagem.ConfirmarEntrega(usuario, dataEHoraDeLeitura);
    
            mensagem.ConfirmarLeitura(usuario, dataEHoraDeLeitura);
        }
    
        return true;
    }
    
    public bool ConfirmarEntrega(Usuario usuario, Perfil perfil)
    {
        var dataEHoraDeEntrega = DateTime.UtcNow;
        var mensagensNaoEntregues = _chat.Where(mensagem => mensagem.PrimeiraEntrega is null && !perfil.Centros.Contains(mensagem.Origem.Codigo)).ToList();
    
        if (mensagensNaoEntregues.Count == 0) return false;
    
        foreach (var mensagem in mensagensNaoEntregues)
        {
            mensagem.ConfirmarEntrega(usuario, dataEHoraDeEntrega);
        }
    
        return true;
    }
    

    private void SetStatusPendente(Usuario usuario) => SetStatus(StatusDeSolicitacao.Pendente, usuario);

    public void AdicionarMensagemNoChat(string mensagem, Usuario usuario, Perfil perfil)
    {
        var origem = perfil.Centros.Contains(Origem.Codigo)? Origem : Destino;
        AdicionarMensagem(mensagem, usuario, origem,null);
    }

    public void Cancelar(Usuario usuario)
    {
        if (!Status.Equals(StatusDeSolicitacao.Pendente)) return;
        
        const StatusDeSolicitacao status = StatusDeSolicitacao.Cancelada;
        SetStatus(status, usuario);

        AdicionarMensagem(MensagensPredefinidasDoChat.SolicitacaoCancelada, usuario, Origem, status);
    }

    public Solicitacao Confirmar(Usuario usuario)
    {
        if (!Status.Equals(StatusDeSolicitacao.Pendente)) return this;
        const StatusDeSolicitacao status = StatusDeSolicitacao.Confirmada;
        SetStatus(status, usuario);
        AdicionarMensagem(MensagensPredefinidasDoChat.SolicitacaoConfirmada, usuario, Destino, status);

        return this;
    }

    public void Impedir(string motivo, Usuario usuario)
    {
        if (!Status.Equals(StatusDeSolicitacao.Pendente)
            && !Status.Equals(StatusDeSolicitacao.Confirmada)) return;

        DetalheDoImpedimento = motivo;
        const StatusDeSolicitacao status = StatusDeSolicitacao.Impedida;
        SetStatus(status, usuario);

        var mensagem = MensagensPredefinidasDoChat.SolicitacaoImpedida(motivo);
        AdicionarMensagem(mensagem, usuario, Destino, status);
    }

    private void AdicionarMensagem(string mensagem, Usuario usuario, ObjetoDeManobra origem, StatusDeSolicitacao? status)
    {
        _chat.Add(new ChatDeSolicitacao(mensagem, usuario, origem, status));
    }

    private bool PodeFinalizar => Status.Equals(StatusDeSolicitacao.Confirmada) || Status.Equals(StatusDeSolicitacao.CienciaInformada);

    public void Finalizar(Usuario usuario)
    {
        if (!PodeFinalizar) return;

        const StatusDeSolicitacao status = StatusDeSolicitacao.Finalizada;
        SetStatus(status, usuario);
        
        AdicionarMensagem(MensagensPredefinidasDoChat.SolicitacaoFinalizada, usuario, Destino, status);
    }

    public void FinalizarAutomaticamente(Usuario usuario)
    {
        Finalizar(usuario);

        if (Status.Equals(StatusDeSolicitacao.Finalizada))
            FinalizadaAutomaticamente = true;
    }

    public void InformarCiencia(Usuario usuario)
    {
        if (!Status.Equals(StatusDeSolicitacao.Impedida)) return;
        var status = StatusDeSolicitacao.CienciaInformada;
        SetStatus(status, usuario);
        
        AdicionarMensagem(MensagensPredefinidasDoChat.SolicitacaoComCienciaInformada, usuario, Origem, status);
    }

    public bool IsSolicitante(string[] codigoObjetoDeManobra)
        => codigoObjetoDeManobra.Contains(Origem.Codigo);

    public bool IsSolicitante(Perfil perfilDeUsuario)
    {
        var centroAtual = perfilDeUsuario.Scopes.Select(s => s.Codigo);
        
        return IsSolicitante(centroAtual.ToArray());
    }

    public bool IsDestinatario(string[] codigoObjetoDeManobra)
        => codigoObjetoDeManobra.Contains(Destino.Codigo);
    
    public bool IsDestinatario(Perfil perfilDeUsuario)
    {
        var centroAtual = perfilDeUsuario.Scopes.Select(s => s.Codigo);
        
        return IsDestinatario(centroAtual.ToArray());
    }

    private void SetStatus(StatusDeSolicitacao status, Usuario usuario)
    {
        var statusAnterior = Status != default ? Status : (StatusDeSolicitacao?)null;
        Status = status;
        UpdatedAt = DateTime.UtcNow;
        HistoricosDeStatus.Add(new HistoricoDeStatusDeSolicitacao(usuario)
        {
            Status = status,
            StatusAnterior = statusAnterior,
            DataDeAlteracao = UpdatedAt
        });
    }

    public Usuario? GetUsuarioDoImpedimento()
    {
        return GetUsuarioDaOperacao(StatusDeSolicitacao.Impedida);
    }

    public Usuario? GetUsuarioDaConfirmacao()
    {
        return GetUsuarioDaOperacao(StatusDeSolicitacao.Confirmada);
    }

    public Usuario? GetUsuarioDaOperacao(StatusDeSolicitacao status)
    {
        return HistoricosDeStatus.Where(h => h.Status == status)?.FirstOrDefault()?.Usuario;
    }
}


public class SolicitacaoIdGenerator : IIdGenerator
{
    public object GenerateId(object container, object document)
    {
        return document is not Solicitacao solicitacao 
            ? ObjectId.GenerateNewId().ToString() 
            : GerarNovoId(solicitacao);
    }

    public bool IsEmpty(object id)
    {
        if (id is not string idString) return true;
        
        return !IdSolicitacaoFactory.EstaValido(idString);
    }

    private static string GerarNovoId(Solicitacao solicitacao)
        => IdSolicitacaoFactory.GerarNovoId(solicitacao.Origem.Codigo, solicitacao.Destino.Codigo);

}