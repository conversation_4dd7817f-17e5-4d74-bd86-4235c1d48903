using MongoDB.Bson.Serialization.Attributes;
using ONS.SINapse.Shared.DTO;

namespace ONS.SINapse.Entities.Entities;

[BsonNoId]
public class ObjetoDeManobra
{
    public ObjetoDeManobra(string codigo, string nome)
    {
        Codigo = codigo;
        Nome = nome;
    }
    
    [BsonRequired]
    [BsonElement("cod_local")]
    public string Codigo { get; set; }
    
    [BsonRequired]
    [BsonElement("nom_local")]
    public string Nome { get; set; }
    
    public static implicit operator ObjetoDeManobra(ObjetoDeManobraDto objetoDeManobraDto) 
        => new(objetoDeManobraDto.Codigo, objetoDeManobraDto.Nome);
    public override string ToString() => Codigo;
    
    
}