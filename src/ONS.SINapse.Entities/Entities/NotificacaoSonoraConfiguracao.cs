using ONS.SINapse.Shared.DTO.Firebase;

namespace ONS.SINapse.Entities.Entities;

public class NotificacaoSonoraConfiguracao
{
    public NotificacaoSonoraConfiguracao(string tipo, string audio, bool ativo)
    {
        Tipo = tipo;
        Audio = audio;
        Ativo = ativo;
    }

    public string Tipo { get; init; }
    public string Audio { get; init; }
    public bool Ativo { get; init; }

    public static NotificacaoSonoraConfiguracao FromDto(NotificacaoSonoraConfiguracaoDto dto)
    {
        return new NotificacaoSonoraConfiguracao(dto.Tipo, dto.Audio, dto.Ativo);
    }
}
