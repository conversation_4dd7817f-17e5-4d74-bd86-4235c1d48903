using MongoDB.Bson.Serialization.Attributes;
using ONS.SINapse.Shared.DTO;

namespace ONS.SINapse.Entities.Entities;

public class Usuario
{
    public Usuario(string sid, string login, string nome)
    {
        Sid = sid;
        Login = login;
        Nome = nome;
    }
    
    [BsonRequired]
    [BsonElement("cod_sid")]
    public string Sid { get; set; }

    [BsonRequired]
    [BsonElement("lgn_usuario")]
    public string Login { get; set; }

    [BsonRequired]
    [BsonElement("nom_usuario")]
    public string Nome { get; set; }

    public static implicit operator Usuario(UsuarioDto usuarioDto) 
        => new(usuarioDto.Sid, usuarioDto.Login, usuarioDto.Nome);
}