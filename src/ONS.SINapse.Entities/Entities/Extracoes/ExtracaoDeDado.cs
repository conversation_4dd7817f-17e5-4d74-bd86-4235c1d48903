using MongoDB.Bson.Serialization.Attributes;
using ONS.SINapse.Entities.Entities.Base;
using ONS.SINapse.Shared.CustomAttributes;

namespace ONS.SINapse.Entities.Entities.Extracoes;

[BsonCollection("c_extracaodado")]
public class ExtracaoDeDado : Entidade
{
    public ExtracaoDeDado(string entidade, string conteudo, int quantidadeDeRegistros) 
        : base(DateTime.Now)
    {
        Entidade = entidade;
        Conteudo = conteudo;
        QuantidadeDeRegistros = quantidadeDeRegistros;
    }
    
    [BsonElement("nom_entidade")]
    public string Entidade { get; private set; }
    
    [BsonElement("json_conteudo")]
    public string Conteudo { get; private set; }
    
    [BsonElement("num_registros")]
    public int QuantidadeDeRegistros { get; private set; }
}