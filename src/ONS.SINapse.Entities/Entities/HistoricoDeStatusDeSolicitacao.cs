using MongoDB.Bson.Serialization.Attributes;
using ONS.SINapse.Integracao.Shared.Enums;

namespace ONS.SINapse.Entities.Entities;

public class HistoricoDeStatusDeSolicitacao
{
    public HistoricoDeStatusDeSolicitacao(Usuario usuario) 
    {
        Usuario = usuario;
    }

    [BsonRequired]
    [BsonElement("cod_status")]
    public StatusDeSolicitacao Status { get; set; }

    [BsonElement("cod_statusanterior")]
    public StatusDeSolicitacao? StatusAnterior { get; set; }

    [BsonRequired]
    [BsonElement("obj_usuario")]
    public Usuario Usuario { get; set; }

    [BsonRequired]
    [BsonElement("din_alteracao")]
    public DateTime DataDeAlteracao { get; set; }
}