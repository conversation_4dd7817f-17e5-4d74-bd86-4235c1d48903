using MongoDB.Bson.Serialization.Attributes;
using ONS.SINapse.Entities.Entities.Base;
using ONS.SINapse.Shared.CustomAttributes;

namespace ONS.SINapse.Entities.Entities;

[BsonCollection("c_statusservice")]
public class StatusService : Entidade
{
    public StatusService(string service, bool health, Exception? exception = null, string? error = null) : base(service)
    {
        Health = health;
        Exception = exception;
        Error = error ?? exception?.Message;
    }
    [BsonElement("health")]
    public bool Health { get; set; }
    [BsonElement("exception")]
    public Exception? Exception { get; set; }
    [BsonElement("error")]
    public string? Error { get; set; }
}