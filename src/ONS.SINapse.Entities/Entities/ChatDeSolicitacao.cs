using MongoDB.Bson.Serialization.Attributes;
using ONS.SINapse.Integracao.Shared.Enums;

namespace ONS.SINapse.Entities.Entities;

[BsonNoId]
public sealed class ChatDeSolicitacao
{ 
    public ChatDeSolicitacao(string mensagem, Usuario usuario, ObjetoDeManobra origem, StatusDeSolicitacao? status,  DateTime? dataEHoraDeEnvio = null)
    {
        Mensagem = mensagem;
        UsuarioRemetente = usuario;
        Status = status;
        DataEHoraDeEnvio = dataEHoraDeEnvio ?? DateTime.Now;
        Origem = origem;
    }

    // Usado pelo mongo para que deserialize essa classe
    // Adicionar todas as propriedades da classe nesse construtor
    [BsonConstructor]
    private ChatDeSolicitacao(
        string mensagem, 
        StatusDeSolicitacao? status, 
        Usuario usuarioRemetente, 
        ObjetoDeManobra origem, 
        RegistroUsuarioHora? primeiraLeitura, 
        RegistroUsuarioHora? primeiraEntrega, 
        DateTime dataEHoraDeEnvio, 
        DateTime? dataEHoraDeEntregaAoDestinatario, 
        DateTime? dataEHoraDeLeituraDoDestinatario)
        : this(mensagem, usuarioRemetente, origem, status)
    {
        DataEHoraDeEnvio = dataEHoraDeEnvio;
        PrimeiraLeitura = primeiraLeitura;
        PrimeiraEntrega = primeiraEntrega;
        DataEHoraDeEntregaAoDestinatario = dataEHoraDeEntregaAoDestinatario;
        DataEHoraDeLeituraDoDestinatario = dataEHoraDeLeituraDoDestinatario;
    }

    [BsonRequired]
    [BsonElement("dsc_mensagem")]
    public string Mensagem { get; private set; }

    [BsonElement("cod_status")]
    public StatusDeSolicitacao? Status { get; private set; }

    [BsonRequired]
    [BsonElement("obj_usuario")]
    public Usuario UsuarioRemetente { get; private set; }
    
    [BsonRequired]
    [BsonElement("obj_origem")]
    public ObjetoDeManobra Origem { get; private set; }
    
    [BsonElement("obj_usuarioleitura")]
    public RegistroUsuarioHora? PrimeiraLeitura { get; set; }
    
    [BsonElement("obj_usuarioentrega")]
    public RegistroUsuarioHora? PrimeiraEntrega { get; set; }
    
    [BsonRequired]
    [BsonElement("din_envio")]
    public DateTime DataEHoraDeEnvio { get; private set; }

    [BsonElement("din_entregadestino")]
    public DateTime? DataEHoraDeEntregaAoDestinatario { get; set; }

    [BsonElement("din_leituradestino")]
    public DateTime? DataEHoraDeLeituraDoDestinatario { get; set; }

    
    public void ConfirmarEntrega(Usuario usuario, DateTime dataEHoraDeEntrega)
    {
        if (!EntregueAoDestinatario())
        {
            DataEHoraDeEntregaAoDestinatario = dataEHoraDeEntrega;
        }

        PrimeiraEntrega = new RegistroUsuarioHora(usuario, dataEHoraDeEntrega);
    }

    public void ConfirmarLeitura(Usuario usuario, DateTime dataEHoraDeLeitura)
    {
        if (!LidaPeloDestinatario())
        {
            DataEHoraDeLeituraDoDestinatario = dataEHoraDeLeitura;
        }

        PrimeiraLeitura = new RegistroUsuarioHora(usuario, dataEHoraDeLeitura);
    }
    
    public bool LidaPeloDestinatario() => PrimeiraLeitura is not null;

    public bool EntregueAoDestinatario() => PrimeiraEntrega is not null;
}

public record RegistroUsuarioHora
{
    [BsonRequired]
    [BsonElement("obj_usuario")]
    public Usuario Usuario { get; init; }

    [BsonRequired]
    [BsonElement("din_registro")]
    public DateTime Data { get; init; }

    public RegistroUsuarioHora(Usuario usuario, DateTime data)
    {
        Usuario = usuario;
        Data = data;
    }
}
