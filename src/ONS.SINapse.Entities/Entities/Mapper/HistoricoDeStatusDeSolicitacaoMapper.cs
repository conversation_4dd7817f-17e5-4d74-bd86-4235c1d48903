using ONS.SINapse.Shared.DTO.Solicitacao;

namespace ONS.SINapse.Entities.Entities.Mapper;

public static class HistoricoDeStatusDeSolicitacaoMapper
{
    public static HistoricoDeStatusDeSolicitacao ToHistoricoDeStatusDeSolicitacao(this HistoricoDeStatusDeSolicitacaoDto historicoDeStatusDeSolicitacaoDto)
    {
        return new HistoricoDeStatusDeSolicitacao(
             new Usuario(historicoDeStatusDeSolicitacaoDto.Usuario.Sid, historicoDeStatusDeSolicitacaoDto.Usuario.Nome, historicoDeStatusDeSolicitacaoDto.Usuario.Login))
        {
            Status = historicoDeStatusDeSolicitacaoDto.Status,
            StatusAnterior = historicoDeStatusDeSolicitacaoDto.StatusAnterior,
            DataDeAlteracao = historicoDeStatusDeSolicitacaoDto.DataDeAlteracao
        };
    }
}
