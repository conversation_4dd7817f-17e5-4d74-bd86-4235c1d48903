using ONS.SINapse.Shared.DTO.Solicitacao;

namespace ONS.SINapse.Entities.Entities.Mapper;

public static class ChatDeSolicitacaoMapper
{
    public static ChatDeSolicitacao FromDto(this ChatDeSolicitacaoDto chatDeSolicitacaoDto)
    {
        return new ChatDeSolicitacao(
            chatDeSolicitacaoDto.Mensagem,
            new Usuario(chatDeSolicitacaoDto.UsuarioRemetente.Sid, chatDeSolicitacaoDto.UsuarioRemetente.Nome, chatDeSolicitacaoDto.UsuarioRemetente.Login),
            new ObjetoDeManobra(chatDeSolicitacaoDto.Origem.Codigo, chatDeSolicitacaoDto.Origem.Nome),
            chatDeSolicitacaoDto.Status,
            chatDeSolicitacaoDto.DataEHoraDeEnvio
        )
        {
            PrimeiraLeitura = chatDeSolicitacaoDto.PrimeiraLeitura is not null
                ? chatDeSolicitacaoDto.PrimeiraLeitura.FromDto()
                : null,
            PrimeiraEntrega = chatDeSolicitacaoDto.PrimeiraEntrega is not null
                ? chatDeSolicitacaoDto.PrimeiraEntrega.FromDto()
                : null,
            DataEHoraDeEntregaAoDestinatario = chatDeSolicitacaoDto.DataEHoraDeEntregaAoDestinatario,
            DataEHoraDeLeituraDoDestinatario = chatDeSolicitacaoDto.DataEHoraDeLeituraDoDestinatario,
        };
    }
}
