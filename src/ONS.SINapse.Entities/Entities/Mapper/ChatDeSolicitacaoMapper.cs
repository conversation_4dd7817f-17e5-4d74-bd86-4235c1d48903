using ONS.SINapse.Shared.DTO.Solicitacao;

namespace ONS.SINapse.Entities.Entities.Mapper;

public static class ChatDeSolicitacaoMapper
{
    public static ChatDeSolicitacao ToChatDeSolicitacaoItemDto(this ChatDeSolicitacaoItemDto chatDeSolicitacaoItemDto)
    {
        return new ChatDeSolicitacao(
            chatDeSolicitacaoItemDto.Mensagem,
            new Usuario(chatDeSolicitacaoItemDto.UsuarioRemetente.Sid, chatDeSolicitacaoItemDto.UsuarioRemetente.Login, chatDeSolicitacaoItemDto.UsuarioRemetente.Nome),
            new ObjetoDeManobra(chatDeSolicitacaoItemDto.Origem.Codigo, chatDeSolicitacaoItemDto.Origem.Nome),
            chatDeSolicitacaoItemDto.Status,
            chatDeSolicitacaoItemDto.DataEHoraDeEnvio
        )
        {
            PrimeiraLeitura = chatDeSolicitacaoItemDto.PrimeiraLeitura?.ToRegistroUsuarioHora(),
            PrimeiraEntrega = chatDeSolicitacaoItemDto.PrimeiraEntrega?.ToRegistroUsuarioHora(),
            DataEHoraDeEntregaAoDestinatario = chatDeSolicitacaoItemDto.DataEHoraDeEntregaAoDestinatario,
            DataEHoraDeLeituraDoDestinatario = chatDeSolicitacaoItemDto.DataEHoraDeLeituraDoDestinatario,
        };
    }
}
