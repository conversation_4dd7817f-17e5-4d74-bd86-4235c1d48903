using ONS.SINapse.Shared.DTO.Solicitacao;

namespace ONS.SINapse.Entities.Entities.Mapper;

public static class RegistroUsuarioHoraDtoMapper
{
    public static RegistroUsuarioHora FromDto(this RegistroUsuarioHoraDto registroUsuarioHora)
    {
        return new RegistroUsuarioHora(
            new Usuario(registroUsuarioHora.Usuario.Sid, registroUsuarioHora.Usuario.Nome, registroUsuarioHora.Usuario.Login),
            registroUsuarioHora.Data);
    }
}
