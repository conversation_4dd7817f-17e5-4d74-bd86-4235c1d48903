using MongoDB.Bson.Serialization.Attributes;
using ONS.SINapse.Entities.Entities.Base;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Shared.CustomAttributes;

namespace ONS.SINapse.Entities.Entities;

[BsonCollection("c_controledechamada")]
public class ControleDeChamada : Entidade
{
    public ControleDeChamada(int duracaoEmSegundos = 120)
    {
        Status = StatusControleDeChamada.LiberadoParaChamada;
        DataDeAtualizacao = DateTime.UtcNow;
        NumeroDaChamada = 0;
        DuracaoEmSegundos = duracaoEmSegundos;
    }

    [BsonRequired]
    [BsonElement("num_chamada")]
    public long NumeroDaChamada { get; set; }

    [BsonRequired]
    [BsonElement("duracao")]
    public int DuracaoEmSegundos { get; set; }

    [BsonRequired]
    [BsonElement("cod_status")]
    public StatusControleDeChamada Status { get; set; }

    [BsonElement("din_atualizacao")]
    public DateTime DataDeAtualizacao { get; set; }

    public void LiberarParaChamada()
    {
        if (Status != StatusControleDeChamada.ProcessamentoFinalizado)
            return;
        AlterarChamada(StatusControleDeChamada.LiberadoParaChamada);
    }

    public void IniciarChamada()
    {
        if (Status != StatusControleDeChamada.LiberadoParaChamada)
            return;
        NumeroDaChamada += 1;
        AlterarChamada(StatusControleDeChamada.ChamadaEmAndamento);
    }

    public void FinalizarChamada()
    {
        if (!PermiteFinalizarChamada())
            return;
        AlterarChamada(StatusControleDeChamada.ProcessamentoPendente);
    }

    public void IniciarProcessamento()
    {
        if (Status != StatusControleDeChamada.ProcessamentoPendente)
            return;
        AlterarChamada(StatusControleDeChamada.Processando);
    }

    public void FinalizarProcessamento()
    {
        if (Status != StatusControleDeChamada.Processando)
            return;
        AlterarChamada(StatusControleDeChamada.ProcessamentoFinalizado);
    }

    public bool  EstaLiberadoParaChamada()
    {
        return Status == StatusControleDeChamada.LiberadoParaChamada;
    }

    public bool EstaEmChamada()
    {
        return Status == StatusControleDeChamada.ChamadaEmAndamento;
    }

    public bool EstaPendenteDeProcessamento()
    {
        return Status == StatusControleDeChamada.ProcessamentoPendente;
    }

    public bool EstaProcessando()
    {
        return Status == StatusControleDeChamada.Processando;
    }

    public bool EstaProcessado()
    {
        return Status == StatusControleDeChamada.ProcessamentoFinalizado;
    }

    public bool PermiteFinalizarChamada()
    {
        return Status == StatusControleDeChamada.ChamadaEmAndamento &&
               DateTime.UtcNow >= DataDeAtualizacao.AddSeconds(DuracaoEmSegundos);
    }

    public TimeSpan TempoRestante()
    {
        if (!EstaEmChamada())
            return TimeSpan.Zero;
        if (DataDeAtualizacao.AddSeconds(DuracaoEmSegundos) < DateTime.UtcNow)
            return TimeSpan.Zero;
        return DataDeAtualizacao.AddSeconds(DuracaoEmSegundos) - DateTime.UtcNow;
    }

    private void AlterarChamada(StatusControleDeChamada status)
    {
        Status = status;
        DataDeAtualizacao = DateTime.UtcNow;
    }
}