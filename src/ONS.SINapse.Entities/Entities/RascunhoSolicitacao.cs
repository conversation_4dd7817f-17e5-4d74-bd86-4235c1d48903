using System.Text.Json;
using Newtonsoft.Json;
using ONS.SINapse.Shared.Extensions;

namespace ONS.SINapse.Entities.Entities;

public class RascunhoSolicitacao
{
    public RascunhoSolicitacao(Guid id, string nome, string proprietario, string centro)
    {
        Id = id;
        Nome = nome;
        Proprietario = proprietario;
        Centro = centro;
        _dadosDoRascunho = new List<DadosDoRascunhoSolicitacao>();
        Valido = true;
    }
    
    public RascunhoSolicitacao(string nome, string proprietario, string centro) : this(Guid.NewGuid(), nome, proprietario, centro)
    {
    }
    
    public Guid Id { get; private set; }
    public string Nome { get; private set; }
    public string Proprietario { get; private set; }
    public bool Valido { get; private set; }
    
    public string Centro { get; private set; }
    
    [JsonIgnore]
    private List<DadosDoRascunhoSolicitacao> _dadosDoRascunho;
    public IReadOnlyCollection<DadosDoRascunhoSolicitacao> DadosDoRascunho => _dadosDoRascunho;

    public void EditarNome(string nome)
    {
        Nome = nome;
    }

    public void AdicionarDadosDeRascunho(List<DadosDoRascunhoSolicitacao> dadosDoRascunho) 
        => _dadosDoRascunho = dadosDoRascunho;
    
    public void AdicionarDadosDeRascunho(DadosDoRascunhoSolicitacao rascunho)
        => _dadosDoRascunho.Add(rascunho);

    public void Invalidar() => Valido = false;
}

public class DadosDoRascunhoSolicitacao
{
    public DadosDoRascunhoSolicitacao(int posicao, List<dynamic> dados)
    {
        Posicao = posicao;
        Dados = ConverterParaDynamic(dados);
    }
    public int Posicao { get; private set; }

    public dynamic Dados { get; private set; }

    private static dynamic ConverterParaDynamic(List<dynamic> dados)
        => (from JsonElement dado in dados select dado.ConvertJsonElementToDynamic()).ToList();
}

