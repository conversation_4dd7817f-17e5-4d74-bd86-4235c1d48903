using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using ONS.SINapse.Entities.Entities.Base;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Shared.CustomAttributes;

namespace ONS.SINapse.Entities.Entities;

[BsonCollection("c_respostadachamada")]
public sealed class RespostaDaChamada: Entidade
{
    public RespostaDaChamada(ObjetoDeManobra centro, long chamada)
    {
        Status = StatusDeCentroAgente.Indefinido;
        CentroAgente = centro;
        NumeroDaChamada = chamada;
        DataDeAtualizacao = DateTime.UtcNow;
        Notificado = false;
        TmpEquipamentos = new HashSet<string>();
        Equipamentos = [];
    }

    [BsonRequired]
    [BsonElement("obj_centroagente")]
    public ObjetoDeManobra CentroAgente { get; set; }

    #region Dados Atualizado apenas via processamento
    [BsonElement("list_equipamentos")]
    public string[] Equipamentos { get; set; }
    
    [BsonElement("flg_visualizatodosequipamentos")]
    public bool VisualizaTodosOsEquipamentos { get; set; }
    #endregion

    #region Dados Excluídos após processamento

    [BsonRequired]
    [BsonElement("tmp_list_equipamentos")]
    public HashSet<string> TmpEquipamentos { get; set; }
    
    [BsonRequired]
    [BsonElement("tmp_flg_visualizatodosequipamentos")]
    public bool TmpVisualizaTodosOsEquipamentos { get; set; }

    #endregion
    
    [BsonRequired]
    [BsonElement("num_chamada")]
    public long NumeroDaChamada { get; set; }
    
    [BsonRequired]
    [BsonElement("cod_status")]
    public StatusDeCentroAgente Status { get; set; }
    
    [BsonElement("din_atualizacao")]
    public DateTime DataDeAtualizacao { get; set; }
    
    [BsonElement("flg_notificado")]
    public bool Notificado { get; set; }
    
    public void ResponderChamada(long chamada, string[] locais)
    {
        DataDeAtualizacao = DateTime.UtcNow;
        NumeroDaChamada = chamada;
        TmpEquipamentos.UnionWith(locais);
        TmpVisualizaTodosOsEquipamentos = TmpVisualizaTodosOsEquipamentos || TmpEquipamentos.Count == 0;
    }


    public bool RespondeuChamada(long chamadaNumeroDaChamada)
    {
        return NumeroDaChamada == chamadaNumeroDaChamada;
    }

    public void RegistrarAusencia()
    {
        Notificado = Status == StatusDeCentroAgente.Offline && Notificado;
        Status = StatusDeCentroAgente.Offline;
        AtualizarStatusLocais();
    }
    
    public void RegistrarPresenca()
    {
        Notificado = Status == StatusDeCentroAgente.Online && Notificado;
        Status = StatusDeCentroAgente.Online;
        AtualizarStatusLocais();
    }

    public void Notificar()
    {
        Notificado = true;
    }
    
    private void ResetarDadosTemporarios()
    {
        TmpEquipamentos.Clear();
        TmpVisualizaTodosOsEquipamentos = false;
    }
    private void AtualizarStatusLocais()
    {
        VisualizaTodosOsEquipamentos = TmpVisualizaTodosOsEquipamentos;
        Equipamentos = TmpEquipamentos.ToArray();
        ResetarDadosTemporarios();
    }
}