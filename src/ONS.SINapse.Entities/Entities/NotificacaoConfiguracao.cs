using ONS.SINapse.Shared.DTO.Firebase;

namespace ONS.SINapse.Entities.Entities;

public class NotificacaoConfiguracao
{
    public NotificacaoConfiguracao(List<NotificacaoSonoraConfiguracao> sonora)
    { 
        Sonora = sonora;
    }

    public List<NotificacaoSonoraConfiguracao> Sonora { get; init; }

    public static NotificacaoConfiguracao FromDto(NotificacaoConfiguracaoDto dto)
    {
        return new NotificacaoConfiguracao(
            dto.Sonora.Select(x => NotificacaoSonoraConfiguracao.FromDto(x))
            .ToList());
    }
}
