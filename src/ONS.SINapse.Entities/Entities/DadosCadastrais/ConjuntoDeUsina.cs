using ONS.SINapse.Entities.Entities.Base;
using ONS.SINapse.Shared.CustomAttributes;
using System.Text.Json.Serialization;

namespace ONS.SINapse.Entities.Entities.DadosCadastrais
{
    [IntegracaoSync("c_conjuntousina")]
    public class ConjuntoDeUsina : EntidadeDadosCadastrais
    {
        public required string Id { get; init; }

        public required string Codigo { get; init; }

        public required string CodigoOns { get; init; }

        public required string Nome { get; init; }

        public required string CodigoDoTipoDeFonte { get; init; }

        public string? Descricao { get; init; }

        public DateTime? DataDeDesativacao { get; init; }

        public required string AgeId { get; init; }

        public required string NomeDoAgente { get; init; }

        public required string CodigoDoCentroDeOperacao { get; init; }

        public string? CodigoOnsDoPontoDeConexao { get; init; }

        public string? NomeDoPontoDeConexao { get; init; }

        [JsonIgnore]
        public string? PontoDeConexao => CodigoOnsDoPontoDeConexao?.Split("_")?.First()?.Substring(2);
    }
}
