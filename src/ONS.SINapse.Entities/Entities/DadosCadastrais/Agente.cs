using ONS.SINapse.Entities.Entities.Base;
using ONS.SINapse.Shared.CustomAttributes;

namespace ONS.SINapse.Entities.Entities.DadosCadastrais;

[BsonCollection("c_agente")]
[IntegracaoSync("c_agente")]
public class Agente : EntidadeDadosCadastrais
{
    public Agente(
        string codigo, 
        string nome, 
        ICollection<string> centros, 
        ICollection<string> areasEletricas, 
        List<CentroDoAgente> centrosDoAgente, 
        bool validarGeolocalizacao)
    {
        Codigo = codigo;
        Nome = nome;
        Centros = centros;
        AreasEletricas = areasEletricas;
        CentrosDoAgente = centrosDoAgente;
        ValidarGeolocalizacao = validarGeolocalizacao;
    }

    public string Codigo { get; set; }

    public string Nome { get; set; }

    public ICollection<string> Centros { get; set; }
    
    public ICollection<string> AreasEletricas { get; set; }

    public List<CentroDoAgente> CentrosDoAgente { get; private init; }

    public bool ValidarGeolocalizacao { get; private set; }

    public void SetValidarGeolocalizacao(bool validarGeolocalizacao) 
    {
        ValidarGeolocalizacao = validarGeolocalizacao;
    }

    public void AdicionarCentrosDoAgente(IEnumerable<CentroDoAgente> centrosDoAgente)
    {
        if (centrosDoAgente.Any()) return;
        CentrosDoAgente.AddRange(centrosDoAgente);
    }
}

public class CentroDoAgente
{
    private CentroDoAgente(string nome, double latitude, double longitude)
    {
        Nome = nome;
        Latitude = latitude;
        Longitude = longitude;
    }

    public string Nome { get; private set; }

    public double Latitude { get; private set; }

    public double Longitude { get; private set; }

    public void SetLatitudeLongitude(double latitude, double longitude) 
    {
        Latitude = latitude;
        Longitude = longitude;
    }

    public static CentroDoAgente Criar(string nome, double latitude, double longitude)
        => new CentroDoAgente(nome, latitude, longitude);
}
