using ONS.SINapse.Entities.Entities.Base;
using ONS.SINapse.Shared.CustomAttributes;
using System.Text.Json.Serialization;

namespace ONS.SINapse.Entities.Entities.DadosCadastrais
{
    [IntegracaoSync("c_usina")]
    public class Usina : EntidadeDadosCadastrais
    {
        public Usina()
        {
            Id = string.Empty;
            UsiId = string.Empty;
            CodigoOns = string.Empty;
            CodigoDoTipoDeFonte = string.Empty;
            NomeCurto = string.Empty;
            AgeId = string.Empty;
            NomeDoAgente = string.Empty;
            CodigoDoCentroDeOperacao = string.Empty;
            NomeDaBacia = string.Empty;
            Uges = [];
        }

        public string Id { get; set; }

        public string UsiId { get; set; }

        public string CodigoOns { get; set; }

        public string CodigoDoTipoDeFonte { get; set; }

        public string NomeCurto { get; set; }

        public string? NomeLongo { get; set; }

        public string? Descricao { get; set; }

        public DateTime? DataDeDesativacao { get; set; }

        public string AgeId { get; set; }

        public string NomeDoAgente { get; set; }

        public string CodigoDoCentroDeOperacao { get; set; }

        public string? CodigoOnsDoPontoDeConexao { get; set; }

        public string? NomeDoPontoDeConexao { get; set; }

        public string NomeDaBacia { get; set; }

        public bool OperaPeloCAG { get; set; }

        public IEnumerable<UgeDaUsina> Uges { get; set; }

        [JsonIgnore]
        public string? PontoDeConexao => CodigoOnsDoPontoDeConexao?.Split("_")?.First()?.Substring(2);
    }

    public class UgeDaUsina
    {
        public UgeDaUsina(string ugeId, string nome, string numeroOperacional)
        {
            UgeId = ugeId;
            Nome = nome;
            NumeroOperacional = numeroOperacional;
        }

        public string UgeId { get; set; }

        public string Nome { get; set; }

        public string NumeroOperacional { get; set; }
    }
}
