using MongoDB.Bson.Serialization.Attributes;
using ONS.SINapse.Entities.Entities.Base;
using ONS.SINapse.Shared.CustomAttributes;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Enums;
using ONS.SINapse.Shared.Extensions;

namespace ONS.SINapse.Entities.Entities;

[BsonCollection("c_comunicado")]
public class Comunicado : Entidade
{
    public Comunicado(Usuario usuario)
    {
        UsuarioDeCriacao = usuario;
        ListaDeLeitores = new List<Leitor>();
        Complementos = new List<Complemento>();
        Destinatarios = new List<Destinatario>();
        UpdatedAt = DateTime.UtcNow;
        Titulo = string.Empty;
        Origem = new ObjetoDeManobra(string.Empty, string.Empty);
        Mensagem = string.Empty;
    }

    [BsonRequired]
    [BsonElement("tit_comunicado")]
    public string Titulo { get; set; }
    
    [BsonRequired]
    [BsonElement("obj_origem")]
    public ObjetoDeManobra Origem { get; set; }

    [BsonRequired]
    [BsonElement("text_comunicado")]
    public string Mensagem { get; set; }

    [BsonRequired]
    [BsonElement("tip_comunicado")]
    public TipoDeComunicado TipoDeComunicado { get; set; }

    [BsonRequired]
    [BsonElement("obj_usuario")]
    public Usuario UsuarioDeCriacao { get; set; }

    [BsonRequired]
    [BsonElement("data_atualizacao")]
    public DateTime UpdatedAt { get; private set; }

    [BsonRequired]
    [BsonElement("list_complementos")]
    public ICollection<Complemento>? Complementos { get; private set; }

    [BsonRequired]
    [BsonElement("list_leitores")]
    public List<Leitor> ListaDeLeitores { get; private set; }

    [BsonElement("list_destinatarios")]
    public ICollection<Destinatario>? Destinatarios { get; set; }

    [BsonIgnore] 
    public IReadOnlyCollection<Leitor> Leitores => ListaDeLeitores
        .Where(l => l.DataDoComunicado == UpdatedAt).ToList().AsReadOnly();

    public void AdicionarComplemento(ObjetoDeManobraDto centro, string mensagem)
    {
        if (Origem.Codigo != centro.Codigo) return;
        if (string.IsNullOrWhiteSpace(mensagem)) return;

        UpdatedAt = DateTime.UtcNow;
        Complementos ??= new List<Complemento>();
        Complementos.Add(new Complemento(mensagem, UpdatedAt));
    }

    public bool AdicionarDestinatario(string? codigo, string? nomeCurto, string? codigoCentro, ICollection<string>? areasEletrica)
    {
        if (string.IsNullOrEmpty(codigo) || string.IsNullOrEmpty(nomeCurto) || string.IsNullOrEmpty(codigoCentro))
        {
            return false;
        }
        if (TipoDeComunicado is TipoDeComunicado.AreasEletricas && areasEletrica.IsNullOrEmpty())
        {
            return false;
        }
        if (TipoDeComunicado != TipoDeComunicado.AreasEletricas && !areasEletrica.IsNullOrEmpty())
        {
            return false;
        }
        
        Destinatarios ??= new List<Destinatario>();
        
        Destinatarios.Add(new Destinatario
            (
                codigo!,
                nomeCurto!,
                codigoCentro!
            )
            { NomeAreasEletricas = areasEletrica }
        );
        return true;
    }
    public void AdicionarLeitor(
        Usuario usuario, 
        DateTime? createdAt = null,
        ICollection<ObjetoDeManobraDto>? centros = null
    )
    {
        if (IsComunicadoLido(usuario)) return;

        ListaDeLeitores.Add(new Leitor(usuario, UpdatedAt, createdAt, centros?.Select(x => new ObjetoDeManobra(x.Codigo, x.Nome)).ToList()));
    }

    public bool IsComunicadoLido(Usuario usuario)
    {
        return (usuario.Login.Equals(UsuarioDeCriacao.Login) && Leitores.Count != 0) ||
            Leitores.Any(l => l.Usuario.Login == usuario.Login && l.DataDoComunicado == UpdatedAt);
    }
    
    public bool HasComplemento => Complementos?.Count != 0;
    public Complemento? UltimoComplemento => Complementos?.FirstOrDefault(c => c.DataDoComplemento == UpdatedAt);
    public string UltimaMensagem() => HasComplemento ? UltimoComplemento!.Mensagem : Mensagem;
    public bool HasDestinatarios() => Destinatarios?.Count != 0;
}

[BsonNoId]
public sealed class Complemento
{
    [BsonElement("dsc_mensagem")]
    public string Mensagem { get; init; }
        
    [BsonElement("din_criacao")]
    public DateTime DataDoComplemento { get; init; }

    public Complemento(string mensagem, DateTime? createdAt = null)
    {
        Mensagem = mensagem;
        DataDoComplemento = createdAt ?? DateTime.UtcNow;
    }
}

[BsonNoId]
public sealed class Destinatario
{
    public Destinatario(
        string codigo,
        string nomeCurto,
        string centro) 
    {
        Codigo = codigo;
        NomeCurto = nomeCurto;
        Centro = centro;
    }

    [BsonElement("cod_destinatario")]
    public string Codigo { get; set; }
        
    [BsonElement("nom_detinatario")]
    public string NomeCurto { get; set; }
        
    [BsonElement("ido_centrooperacao")]
    public string Centro { get; set; }

    [BsonElement("list_nomeareaeletrica")]
    public ICollection<string>? NomeAreasEletricas { get; set; }
}
    
[BsonNoId]
public sealed class Leitor
{
    [BsonElement("din_criacao")] 
    public DateTime DataCriacao { get; set; }
        
    [BsonElement("obj_usuario")]
    public Usuario Usuario { get; init; }
        
    [BsonElement("list_centros")]
    public ICollection<ObjetoDeManobra> Centros { get; set; }
        
    [BsonElement("din_comunicado")] 
    public DateTime DataDoComunicado { get; set; }
    
    public Leitor(Usuario usuario, DateTime dataDoComunicado, DateTime? createdAt = null, ICollection<ObjetoDeManobra>? centros = null) 
    {
        Usuario = usuario;
        DataDoComunicado = dataDoComunicado;
        Centros = centros ?? new List<ObjetoDeManobra>();
        DataCriacao = createdAt ?? DateTime.UtcNow;
    }
}