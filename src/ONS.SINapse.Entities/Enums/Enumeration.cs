using System.Reflection;

namespace ONS.SINapse.Entities.Enums;

public class Enumeration
{
    public int Value { get; }
    public string Description { get; }

    public Enumeration(int value, string description)
    {
        Value = value;
        Description = description;
    }

    public static IReadOnlyCollection<T> GetAll<T>()
        where T : Enumeration
    {
        Type abstractType = typeof(T);
        return abstractType
            .GetFields(BindingFlags.Public | BindingFlags.Static)
            .Select(p => p.GetValue(abstractType))
            .OfType<T>()
            .ToList()
            .AsReadOnly();
    }

    public static T? FromValue<T>(int value)
        where T : Enumeration =>
        GetAll<T>().FirstOrDefault(item => item.Value.Equals(value));
    
    public static bool Exists<T>(int value) 
        where T : Enumeration => 
        GetAll<T>().Any(item => item.Value == value);
    
    public static implicit operator int(Enumeration enumeration) => enumeration.Value;
}