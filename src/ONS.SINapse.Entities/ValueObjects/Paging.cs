namespace ONS.SINapse.Entities.ValueObjects;

public class Paging<T> : Paging
    where T : class
{
    public Paging(List<T> items, int pageNumber)
        : base(pageNumber, items.Count)
    {
        _items = items;
    }

    private readonly List<T> _items;
    public IReadOnlyCollection<T> Items => _items.AsReadOnly();
    public static Paging<T> Empty() => new(new List<T>(), DefaultInitialPage);
}

public abstract class Paging
{
    protected const int DefaultPageSize = 50;
    protected const int DefaultInitialPage = 1;

    protected Paging(int pageNumber, int pageSize)
    {
        Page = pageNumber < DefaultInitialPage ? DefaultInitialPage : pageNumber;
        Size = pageSize;
    }

    public int Page { get; }
    public int Size { get; }
}