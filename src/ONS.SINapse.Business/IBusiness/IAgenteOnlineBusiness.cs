using ONS.SINapse.Shared.DTO;

namespace ONS.SINapse.Business.IBusiness;

public interface IAgenteOnlineBusiness : IDisposable
{
    Task<IEnumerable<AgenteOnlineDto>> ObterAgentesOnlineAsync(CancellationToken cancellationToken);
    Task<string[]> ObterCodigoAgentesOnlineAsync(CancellationToken cancellationToken);
    Task<IEnumerable<string>> BuscarAgentesInativosAsync(string[] codigoCentroAgente, CancellationToken cancellationToken);
}