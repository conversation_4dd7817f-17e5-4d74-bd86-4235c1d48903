using ONS.SINapse.Entities.Entities;
using ONS.SINapse.Shared.DTO;

namespace ONS.SINapse.Business.IBusiness;

public interface IRespostaDaChamadaBusiness
{
    Task<ICollection<RespostaDaChamada>> BuscarPorCentro(string[] codigo, CancellationToken cancellationToken);
    Task ResponderChamada(ResponderChamadaDto dto, CancellationToken cancellationToken);
    Task<ICollection<RespostaDaChamada>> ProcessarChamada(ControleDeChamada chamada, CancellationToken cancellationToken);
    Task PublicarListaDeChamada(ControleDeChamada chamada, CancellationToken cancellationToken);
    Task<ICollection<RespostaDaChamada>> ObterAgentesOnline(CancellationToken cancellationToken);
    Task<string[]> ObterCodigoAgentesOnlineAsync(CancellationToken cancellationToken);
    Task<string[]> ObterCentroAgentesInativos(string[] codigoCentroAgente, CancellationToken cancellationToken);
}