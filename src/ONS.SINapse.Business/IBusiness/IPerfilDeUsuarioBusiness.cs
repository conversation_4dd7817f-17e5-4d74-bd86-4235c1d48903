using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.DTO.PerfilDoUsuario;

namespace ONS.SINapse.Business.IBusiness;

public interface IPerfilDeUsuarioBusiness
{
    Task<UsuarioPerfilDto> ObterPerfilAsync(CancellationToken cancellationToken);

    Task<List<VisaoDeUsuarioSelecionadoDto>> AlterarVisoesAsync(ICollection<SelecaoDeVisaoDeUsuarioDto> visoes,
        CancellationToken cancellationToken);
}
