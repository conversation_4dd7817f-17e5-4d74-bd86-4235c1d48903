using ONS.SINapse.Shared.DTO;

namespace ONS.SINapse.Business.IBusiness;

public interface IComunicadoBusiness
{
    Task<IEnumerable<ComunicadoItemDto>> GetAsync(CancellationToken cancellationToken);
    
    Task<ComunicadoDetalheDto> AddAsync(ComunicadoDto dto, CancellationToken cancellationToken);
    Task<ComunicadoDetalheDto> GetByIdAsync(string id, CancellationToken cancellationToken);
    Task<ComunicadoDetalheDto> RegistrarLeituraAsync(string id, CancellationToken cancellationToken);
    Task<ComunicadoDetalheDto> AdicionarComplentoAsync(
        string id, ComplementoDeComunicadoDto complemento, CancellationToken cancellationToken);
    Task<ICollection<string>> ExcluirComunicadosAntigos(CancellationToken cancellationToken);
    Task<IEnumerable<AreaEletricaComunicadoDto>> GetAreasEletricas(CancellationToken cancellationToken);
    Task<long> ObterTotalDeComunicadosNaoLidosPeloUsuario(CancellationToken cancellationToken);
}