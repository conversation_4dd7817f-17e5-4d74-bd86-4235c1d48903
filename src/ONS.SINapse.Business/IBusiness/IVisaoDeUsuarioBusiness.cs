using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.DTO.Firebase;

namespace ONS.SINapse.Business.IBusiness;

public interface IVisaoDeUsuarioBusiness
{
    Task<IDictionary<string, VisaoUsuarioRealtimeDto>> VisoesEmUsoAsync(string nome, string codigoCentroAgente, CancellationToken cancellationToken);
    Task<VisaoDeUsuarioDto> AddAsync(VisaoDeUsuarioDto dto, CancellationToken cancellationToken);
    Task<VisaoDeUsuarioDto> UpdateAsync(string id, string codigoCentroAgenteAtual, VisaoDeUsuarioDto dto, CancellationToken cancellationToken);
    Task DeleteAsync(string id, string codigoCentroAgente, CancellationToken cancellationToken);
}