using Microsoft.AspNetCore.Http;
using ONS.SINapse.Shared.DTO;

namespace ONS.SINapse.Business.IBusiness.Firebase
{
    public interface ICollectionFirebaseBusiness
    {
        Task PatchAsync(string collectionName, IFormFile jsonFile, CancellationToken cancellationToken);
        Task PutAsync(string collectionName, IFormFile jsonFile, CancellationToken cancellationToken);
        Task DeleteAsync(string collectionName, CancellationToken cancellationToken);
        Task<ArquivoCollectionDto> GetAsync(string collectionName, CancellationToken cancellationToken);
    }
}