using ONS.SINapse.Shared.DTO.Firebase;

namespace ONS.SINapse.Business.IBusiness.Firebase;

public interface IRascunhoSolicitacaoBusiness
{
    Task AdicionarRascunhoAsync(RascunhoSolicitacaoDto rascunhoDto, CancellationToken cancellationToken);
    Task EditarRascunhoAsync(RascunhoSolicitacaoDto rascunhoDto, CancellationToken cancellationToken);
    Task InvalidarRascunhoAsync(Guid id, CancellationToken cancellationToken);
    Task RemoverRascunhoAsync(Guid id, CancellationToken cancellationToken);
    Task FavoritarRascunhoAsync(FavoritarRascunhoSolicitacaoDto favoritarRascunhoSolicitacao, CancellationToken cancellationToken);
    Task RemoverRascunhoFavoritoAsync(DesfavoritarRascunhoSolicitacaoDto desfavoritarRascunhoSolicitacao, CancellationToken cancellationToken);
}