using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.DTO.Firebase;

namespace ONS.SINapse.Business.IBusiness;

public interface ITagBusiness
{
    Task<TagRealtimeDto?> AddAsync(TagDto dto, CancellationToken cancellationToken);
    Task<TagRealtimeDto?> GetAsync(string id, CancellationToken cancellationToken);
    Task<IEnumerable<TagRealtimeDto?>> GetAllAsync(CancellationToken cancellationToken);
    Task<TagRealtimeDto?> UpdateAsync(string id, TagDto dto, CancellationToken cancellationToken);
    Task DeleteAsync(string id, CancellationToken cancellationToken);
}