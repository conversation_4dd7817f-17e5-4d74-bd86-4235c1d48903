<Project Sdk="Microsoft.NET.Sdk">

  <!-- Propriedades específicas do projeto -->
  <PropertyGroup>
    <!-- Propriedades específicas serão herdadas do Directory.Build.props -->
  </PropertyGroup>

  <!-- Referências de projeto -->
  <ItemGroup>
    <ProjectReference Include="..\ONS.SINapse.Entities\ONS.SINapse.Entities.csproj" />
    <ProjectReference Include="..\ONS.SINapse.Shared\ONS.SINapse.Shared.csproj" />
  </ItemGroup>

  <!-- Pacotes NuGet -->
  <ItemGroup>
    <PackageReference Include="librdkafka.redist" />
  </ItemGroup>

  <ItemGroup>
    <Compile Remove="IBusiness\IAgenteAcessivelBusiness.cs" />
  </ItemGroup>

</Project>
