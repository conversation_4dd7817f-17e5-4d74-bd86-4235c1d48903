using System.ComponentModel;

namespace ONS.SINapse.Integracao.Shared.Enums
{
    public enum StatusControleDeChamada : short
    {
        [Description("Liberado para chamada")]
        LiberadoParaChamada = 1,

        [Description("Chamada em andamento")]
        ChamadaEmAndamento = 2,

        [Description("Pendente de Processamento")]
        ProcessamentoPendente = 3,
        
        [Description("Processando")]
        Processando = 4,
        
        [Description("Processando Finalizado")]
        ProcessamentoFinalizado = 5
    }
}
