using ONS.SINapse.Integracao.Shared.Dtos.Base;

namespace ONS.SINapse.Integracao.Shared.Dtos
{
    public class AgentesOnlineIntegracaoOutputDto : IntegracaoOutputDto
    {
        public AgentesOnlineIntegracaoOutputDto(IEnumerable<AgentesOnlineIntegracaoItemOutputDto> agente)
        { 
            Agentes = agente;
        }

        public IEnumerable<AgentesOnlineIntegracaoItemOutputDto> Agentes { get; set; }
    }

    public class AgentesOnlineIntegracaoItemOutputDto 
    {
        public AgentesOnlineIntegracaoItemOutputDto(
            string ageId,
            DateTime dataDaConsulta,
            short status,
            string descricaoDoStatus)
        { 
            AgeId = ageId;
            DataDaConsulta = dataDaConsulta;
            Status = status;
            DescricaoDoStatus = descricaoDoStatus;
        }

        public string AgeId { get; set; }
        public DateTime DataDaConsulta { get; set; }
        public short Status { get; set; }
        public string DescricaoDoStatus { get; set; }
    }
}
