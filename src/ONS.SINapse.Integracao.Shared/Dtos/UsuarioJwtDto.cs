using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Cryptography;

namespace ONS.SINapse.Integracao.Shared.Dtos
{
    public class UsuarioJwtDto
    {
        private const string SidClaim = "claims/sid";
        private const string LoginClaim = "unique_name";
        private const string UserNameClaim = "given_name";

        public UsuarioJwtDto(string sid, string login, string nome)
        {
            Sid = sid;
            Login = login;
            Nome = nome;
        }

        public string Sid { get; set; }
        public string Login { get; set; }
        public string Nome { get; set; }

        public static UsuarioJwtDto GenerateByAccessToken(string accessToken, string rsaModulus)
        {
            JwtSecurityToken validatedJwt;

            try
            {
                var rsa = new RSACryptoServiceProvider(2048);
                rsa.ImportParameters(
                  new RSAParameters()
                  {
                      Modulus = Convert.FromBase64String(rsaModulus),
                      Exponent = Convert.FromBase64String("AQAB")
                  });

                var validationParameters = new TokenValidationParameters
                {
                    RequireExpirationTime = true,
                    RequireSignedTokens = false,
                    ValidateAudience = false,
                    ValidateIssuer = false,
                    ValidateLifetime = false,
                    IssuerSigningKey = new RsaSecurityKey(rsa)
                };

                SecurityToken validatedSecurityToken;
                var handler = new JwtSecurityTokenHandler();
                handler.ValidateToken(accessToken, validationParameters, out validatedSecurityToken);
                validatedJwt = (validatedSecurityToken as JwtSecurityToken)!;
            }
            catch (SecurityTokenExpiredException ex)
            {
                throw new InvalidDataException("Access token is expired.", ex);
            }
            catch (SecurityTokenValidationException ex)
            {
                throw new InvalidDataException("Access token is invalid.", ex);
            }
            catch (Exception ex)
            {
                throw new InvalidDataException(ex.Message, ex);
            }

            string sid = GetClaimValue(validatedJwt, SidClaim);
            string login = GetClaimValue(validatedJwt, LoginClaim);
            string nome = GetClaimValue(validatedJwt, UserNameClaim);

            return new UsuarioJwtDto(sid, login, nome);

            string GetClaimValue(JwtSecurityToken token, string claim) 
                => token.Claims.Where(c => c.Type.EndsWith(claim))?.FirstOrDefault()?.Value ?? "";
        }
    }
}
