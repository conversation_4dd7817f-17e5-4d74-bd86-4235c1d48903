using ONS.SINapse.Integracao.Shared.Dtos.Base;

namespace ONS.SINapse.Integracao.Shared.Dtos;

public class StatusDeSolicitacaoIntegracaoOutputDto : IntegracaoOutputDto
{
    public StatusDeSolicitacaoIntegracaoOutputDto(
        string codigoDaSolicitacao, 
        string codigoExterno, 
        short statusAtualId, 
        string descricaoStatusAtual, 
        IEnumerable<HistoricoDoStatusDto> historicos)
    {
        CodigoDaSolicitacao = codigoDaSolicitacao;
        CodigoExterno = codigoExterno;
        StatusAtualId = statusAtualId;
        DescricaoStatusAtual = descricaoStatusAtual;
        Historicos = historicos;
    }

    public string CodigoDaSolicitacao { get; }
    public string CodigoExterno { get; }
    public short StatusAtualId { get; }
    public string DescricaoStatusAtual { get; }
    public string? Justificativa { get; init; }
    public string? UsuarioDoImpedimento { get; init; }
    public string? LoginDoUsuarioDoImpedimento { get; init; }
    public string? SidDoUsuarioDoImpedimento { get; init; }
    public string? UsuarioDaConfirmacao { get; init; }
    public string? LoginDoUsuarioDaConfirmacao { get; init; }
    public string? SidDoUsuarioDaConfirmacao { get; init; }
    public bool? Enviada { get; init; }
    public DateTime? DataEHoraDeEnvio { get; init; }
    public IEnumerable<HistoricoDoStatusDto> Historicos { get; }
}

public record HistoricoDoStatusDto(
    DateTime DataEHora,
    short StatusId,
    string DescricaoStatus,
    string Usuario,
    string LoginDoUsuario,
    string SidDoUsuario);
