using System.Collections.Generic;
using ONS.SINapse.Integracao.Shared.Dtos.Base;

namespace ONS.SINapse.Integracao.Shared.Dtos
{
    public class StatusDeSolicitacaoEmLoteIntegracaoOutputDto : IntegracaoOutputDto
    {
        public StatusDeSolicitacaoEmLoteIntegracaoOutputDto(ICollection<StatusDeSolicitacaoIntegracaoOutputDto> solicitacoes)
        {
            Solicitacoes = solicitacoes;
        }

        public ICollection<StatusDeSolicitacaoIntegracaoOutputDto> Solicitacoes { get; set; }
    }
}