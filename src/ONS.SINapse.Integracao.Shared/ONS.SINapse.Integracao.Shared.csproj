<Project Sdk="Microsoft.NET.Sdk">

  <!-- Propriedades específicas do projeto -->
  <PropertyGroup>
    <!-- Propriedades específicas serão herdadas do Directory.Build.props -->
  </PropertyGroup>

  <!-- Pacotes NuGet específicos da Integração -->
  <ItemGroup>
    <PackageReference Include="Confluent.Kafka" />
    <PackageReference Include="librdkafka.redist" />
    <PackageReference Include="Microsoft.Extensions.Options" />
    <PackageReference Include="Microsoft.IdentityModel.Tokens" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" />
    <PackageReference Include="Refit" />
    <PackageReference Include="Refit.HttpClientFactory" />
    <PackageReference Include="Refit.Newtonsoft.Json" />
  </ItemGroup>

</Project>
