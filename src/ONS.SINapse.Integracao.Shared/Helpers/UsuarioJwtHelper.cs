using Microsoft.Extensions.Options;
using ONS.SINapse.Integracao.Shared.Dtos;
using ONS.SINapse.Integracao.Shared.Settings;

namespace ONS.SINapse.Integracao.Shared.Helpers
{
    public interface IUsuarioJwtHelper
    {
        UsuarioJwtDto GetUsuario(string accessToken);
    }

    public class UsuarioJwtHelper : IUsuarioJwtHelper
    {
        private readonly AuthorizationSettings _authorizationSettings;

        public UsuarioJwtHelper(
            IOptions<AuthorizationSettings> authorizationSettings)
        {
            _authorizationSettings = authorizationSettings.Value;
        }

        public UsuarioJwtDto GetUsuario(string accessToken) 
            => UsuarioJwtDto.GenerateByAccessToken(accessToken, _authorizationSettings.RsaModulus);
    }
}
