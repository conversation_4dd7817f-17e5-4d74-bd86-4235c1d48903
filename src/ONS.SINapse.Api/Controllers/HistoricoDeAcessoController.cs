using System.Security.Claims;
using Microsoft.AspNetCore.Mvc;
using ONS.SINapse.Business.IBusiness;
using ONS.SINapse.Business.Imp.Claims;
using ONS.SINapse.Shared.Constants;
using ONS.SINapse.Shared.DTO;

namespace ONS.SINapse.Api.Controllers;

[ApiController]
[Route("api/historico-acesso")]
[ClaimRequirement(ClaimTypes.Role, Roles.Administrador)]
public class HistoricoDeAcessoController : ControllerBase
{
    private readonly IHistoricoDeAcessoBusiness _historicoDeAcessoBusiness;

    public HistoricoDeAcessoController(IHistoricoDeAcessoBusiness historicoDeAcessoBusiness)
    {
        _historicoDeAcessoBusiness = historicoDeAcessoBusiness;
    }

    [HttpGet]
    public async Task<IActionResult> ObterHistoricoDeAcesso([FromQuery] FiltroHistoricoDeAcesso? filtroHistoricoDeAcesso, CancellationToken cancellationToken)
    {
        var dados = 
            await _historicoDeAcessoBusiness.ObterHistoricoDeAcessoAsync(filtroHistoricoDeAcesso, cancellationToken).ConfigureAwait(false);
        return Ok(dados);
    }
}