using Microsoft.AspNetCore.Mvc;
using ONS.SINapse.Business.IBusiness;
using ONS.SINapse.Business.Imp.Claims;
using ONS.SINapse.Shared.Constants;
using ONS.SINapse.Shared.DTO;


namespace ONS.SINapse.Api.Controllers;


[ApiController]
[Route("api/status-centro")]
public class StatusCentroController: ControllerBase
{
    private readonly IRespostaDaChamadaBusiness _respostaDaChamadaBusiness;

    public StatusCentroController(IRespostaDaChamadaBusiness respostaDaChamadaBusiness)
    {
        _respostaDaChamadaBusiness = respostaDaChamadaBusiness;
    }
    
    [HttpPost("responder")]
    [UserClaimRequirement(Operacoes.RegistrarAudiencia)]
    public async Task<IActionResult> ResponderChamada(ResponderChamadaDto dto, CancellationToken cancellationToken)
    {
        await _respostaDaChamadaBusiness.ResponderChamada(dto, cancellationToken);
        return Ok();
    }
}