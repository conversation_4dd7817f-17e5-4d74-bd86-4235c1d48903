using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using ONS.SINapse.CacheSync.Services;
using ONS.SINapse.Entities.Entities.DadosCadastrais;
using ONS.SINapse.Shared.Constants;
using ONS.SINapse.Shared.Identity;
using ONS.SINapse.Shared.Services.Caching;
using ONS.SINapse.Shared.Settings;
using System.Net.Mime;
using ONS.SINapse.Business.Imp.Claims;
using ONS.SINapse.Shared.Kafka.Providers;
using ONS.SINapse.Solicitacao.EventHandlers.IntegrationEvents;

namespace ONS.SINapse.Api.Controllers;

[ApiController]
[Route("api/teste")]
[ClaimRequirement(PopClaimTypes.Operation, Operacoes.Administrar)]
public class TesteController : ControllerBase
{
    private readonly IProducerProvider<TrocaDeStatusExternaIntegrationEvent> _trocaDeStatusProvider;
    private readonly IConfiguration _configuration;
    private readonly ICacheService _cacheService;
    private readonly ISinapaseDadosQueryService<Agente> _sinapseDadosQueryService;
    private readonly FirebaseSettings _firebaseSettings;
    private readonly MongoDbSettings _mongoDbSettings;
    private readonly DataSyncSettings _dataSyncSettings;

    public TesteController(IOptions<FirebaseSettings> firebaseSettings,
        IOptions<MongoDbSettings> mongoDbSettings,
        IOptions<DataSyncSettings> dataSyncSettings,
        IConfiguration configuration,
        ICacheService cacheService,
        ISinapaseDadosQueryService<Agente> sinapseDadosQueryService,
        IProducerProvider<TrocaDeStatusExternaIntegrationEvent> trocaDeStatusProvider)
    {
        _firebaseSettings = firebaseSettings.Value;
        _mongoDbSettings = mongoDbSettings.Value;
        _dataSyncSettings = dataSyncSettings.Value;
        _configuration = configuration;
        _cacheService = cacheService;
        _sinapseDadosQueryService = sinapseDadosQueryService;
        _trocaDeStatusProvider = trocaDeStatusProvider;
    }

    [HttpGet("teste-dados")]
    public async Task<IActionResult> TestarDados(CancellationToken cancellationToken)
    {
        return Ok(await _sinapseDadosQueryService.GetOneAsync(x => x.Codigo == "GSU", cancellationToken));
    }
    
    [HttpGet("parametros-api-dados")]
    [Consumes(MediaTypeNames.Application.Json)]
    public IActionResult GetApiDados()
        => Ok(_dataSyncSettings);
    
    [HttpGet("parametros")]
    [Consumes(MediaTypeNames.Application.Json)]
    public IActionResult Get()
        => Ok(new
        {
            _firebaseSettings.AuthSecret,
            _firebaseSettings.Base64ConfigJson,
            _firebaseSettings.BasePath,
            _mongoDbSettings.ConnectionString,
            _mongoDbSettings.DatabaseName
        });

    [HttpGet("headers")]
    [Consumes(MediaTypeNames.Application.Json)]
    public  IActionResult TesteHeader() => Ok(Request.Headers);

    [HttpPost("alterar-status")]
    [Consumes(MediaTypeNames.Application.Json)]
    public async Task<IActionResult> AlterarStatus([FromBody] TrocaDeStatusExternaIntegrationEvent trocaDeStatus,
        CancellationToken cancellationToken)
    {
        var userAccessToken = Request.Headers["AuthorizationUser"]
                                  .FirstOrDefault()?.Split(' ')
                                  .LastOrDefault() ??
                              string.Empty;
        
        var headers = new Dictionary<string, string>
        {
            { "AuthorizationUser", userAccessToken },
            { "Authorization", Request.Headers.Authorization.FirstOrDefault() ?? string.Empty }
        };

        await _trocaDeStatusProvider.PublishAsync(trocaDeStatus, headers, cancellationToken);
        
        return Ok();
    }

    [HttpGet("cache/incluir")]
    [Consumes(MediaTypeNames.Application.Json)]
    public async Task<IActionResult> CacheIncluir()
    {
        await _cacheService.SetAsync("ons_value", DateTime.Now.ToString("U"));
        return Ok();
    }

    [HttpGet("cache/consultar")]
    [Consumes(MediaTypeNames.Application.Json)]
    public async Task<IActionResult> CacheConsultar()
    {
        var message = await _cacheService.GetAsync<string>("ons_value");
        return Ok(message);
    }
    
    [HttpGet("cache/invalidar")]
    [Consumes(MediaTypeNames.Application.Json)]
    public async Task<IActionResult> CacheInvalidar(CancellationToken cancellationToken)
    {
        var tasks = CacheRedisKey.GetKeys().Select( key => _cacheService.RemoveByPrefixAsync(key, cancellationToken));
        await Task.WhenAll(tasks).WaitAsync(cancellationToken);
        return Ok(CacheRedisKey.GetKeys());
    }
    
    [HttpGet("configuration")]
    public IActionResult GetConfiguration()
    {
        return Ok(_configuration.AsEnumerable().ToDictionary(k => k.Key, v => v.Value));
    }
}