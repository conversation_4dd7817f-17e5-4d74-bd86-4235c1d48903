using System.Net.Mime;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ONS.SINapse.Business.Imp.Claims;
using ONS.SINapse.CacheSync.Services;
using ONS.SINapse.Shared.Constants;
using ONS.SINapse.Shared.DTO;

namespace ONS.SINapse.Api.Controllers;

[ApiController]
[Route("api/local-operacao")]
[Authorize]
public class LocalDeOperacaoController: ControllerBase
{
    private readonly ISinapseDadosDatasetQueryService _dadosDatasetQueryService;

    public LocalDeOperacaoController(ISinapseDadosDatasetQueryService dadosDatasetQueryService)
    {
        _dadosDatasetQueryService = dadosDatasetQueryService;
    }

    [HttpGet]
    [Consumes(MediaTypeNames.Application.Json)]
    [UserClaimRequirement(Operacoes.GerenciarVisaoUsuario)]
    public async Task<IActionResult> GetDadosDeFiltro([FromQuery] string codigoCentroAgente, CancellationToken cancellationToken)
    {
        var dados =
            await _dadosDatasetQueryService
                .GetDatasetAsync(x => x.GetLocalDeOperacaoDataset(codigoCentroAgente,
                    cancellationToken), cancellationToken);
        return Ok(dados ?? Enumerable.Empty<LocalDeOperacaoDataset>());
    }
}