using Firebase.Database;
using Firebase.Database.Query;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using ONS.SINapse.Business.Imp.Claims;
using ONS.SINapse.Shared.Constants;
using ONS.SINapse.Shared.Identity;

namespace ONS.SINapse.Api.Controllers.ControleDeVersao._48_49;

[ApiController]
[Route("api/controle-versao/rascunho-solicitacao")]
[ClaimRequirement(PopClaimTypes.Operation, Operacoes.Administrar)]
public class RascunhoSolicitacaoController : ControllerBase
{
    private readonly ChildQuery _collection;

    public RascunhoSolicitacaoController(
        FirebaseClient firebaseClient)
    {
        _collection = firebaseClient.Child(ColecoesFirebase.Rascunho);
    }

    [HttpPost("48-49")]
    public async Task<IActionResult> ConsistirBase4849Async(CancellationToken cancellationToken)
    {
        var dadosRascunhosFirebase = await _collection.OnceSingleAsync<object>()
            .WaitAsync(cancellationToken)
            .ConfigureAwait(false);

        var jsonContent = JToken.FromObject(dadosRascunhosFirebase);

        if (!jsonContent.HasValues) return BadRequest("Não foi possivel deserializar dados do rascunho.");
        jsonContent = JToken.Parse(jsonContent.ToString().Replace("\"agent\"", "\"encaminharPara\""));
        
        await _collection.PutAsync(jsonContent)
            .WaitAsync(cancellationToken)
            .ConfigureAwait(false);
        
        return Ok($"Operação de migração de rascunhos realizada com sucesso. Total de rascunhos atualizados: {jsonContent.Count()}");
    }

    [HttpPost("49-48")]
    public async Task<IActionResult> ConsistirBase4948Async(CancellationToken cancellationToken)
    {
        var dadosRascunhosFirebase = await _collection.OnceSingleAsync<object>()
            .WaitAsync(cancellationToken)
            .ConfigureAwait(false);

        var jsonContent = JToken.FromObject(dadosRascunhosFirebase);

        if (!jsonContent.HasValues) return BadRequest("Não foi possivel deserializar dados do rascunho.");

        jsonContent = JToken.Parse(jsonContent.ToString().Replace("\"encaminharPara\"", "\"agent\""));
        
        await _collection.PutAsync(jsonContent)
            .WaitAsync(cancellationToken)
            .ConfigureAwait(false);

        return Ok($"Operação de rollbak de rascunhos realizada com sucesso. Total de rascunhos atualizados: {jsonContent.Count()}");
    }
}
