using Firebase.Database;
using Microsoft.AspNetCore.Mvc;
using MongoDB.Driver;
using ONS.SINapse.Business.Imp.Claims;
using ONS.SINapse.Repository.IRepository;
using ONS.SINapse.Repository.IRepository.Firebase;
using ONS.SINapse.Shared.Constants;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Shared.Identity;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands.Firebase;
using ONS.SINapse.Solicitacao.Factories;

namespace ONS.SINapse.Api.Controllers.ControleDeVersao._52_56;

[ApiController]
[Route("api/controle-versao/solicitacao")]
[ClaimRequirement(PopClaimTypes.Operation, Operacoes.Administrar)]
public class SolicitacaoController
{
    private readonly FirebaseClient _firebaseClient;
    private readonly ISolicitacaoRepository _solicitacaoRepository;
    private readonly ISolicitacaoFirebaseRepository _solicitacaoFirebaseRepository;
    private readonly ISolicitacaoFirebaseCommandFactory _solicitacaoFirebaseCommandFactory;
    private readonly IMediatorHandler _mediatorHandler;

    public SolicitacaoController(
        FirebaseClient firebaseClient, 
        ISolicitacaoRepository solicitacaoRepository, 
        ISolicitacaoFirebaseRepository solicitacaoFirebaseRepository,
        ISolicitacaoFirebaseCommandFactory solicitacaoFirebaseCommandFactory,
        IMediatorHandler mediatorHandler)
    {
        _firebaseClient = firebaseClient;
        _solicitacaoRepository = solicitacaoRepository;
        _solicitacaoFirebaseRepository = solicitacaoFirebaseRepository;
        _solicitacaoFirebaseCommandFactory = solicitacaoFirebaseCommandFactory;
        _mediatorHandler = mediatorHandler;
    }
    
    [HttpPost("52-56")]
    public async Task<List<string>> ConsistirBase5256Async(CancellationToken cancellationToken)
    {
        var collection = _firebaseClient.Child(ColecoesFirebase.Solicitacao);

        var items = await collection
            .OnceAsync<object>()
            .WaitAsync(cancellationToken)
            .ConfigureAwait(false);

        // Extraindo os IDs (que são as chaves dos nós no Firebase)
        var ids = items.Select(x => x.Key).ToList();

        var solicitacoesCursor = await _solicitacaoRepository.FindAsync( x => ids.Contains(x.Id), cancellationToken: cancellationToken);
        
        var solicitacoes = await solicitacoesCursor.ToListAsync(cancellationToken);
        
        var command = _solicitacaoFirebaseCommandFactory.ObterCommand<CriarSolicitacaoNoFirebaseCommand>(solicitacoes);
        
        await _mediatorHandler.EnviarComandoAsync(command, cancellationToken);

        return ids;
    }
    
    [HttpPost("56-52")]
    public async Task<List<Entities.Entities.Solicitacao>> ConsistirBase5652Async(CancellationToken cancellationToken)
    {
        var solicitacoes = await _solicitacaoFirebaseRepository.GetAlldAsync( cancellationToken);
        
        // Sincronizando os dados no mongo a partir dos dados do firebase
        await _solicitacaoRepository.BulkUpdateAsync(solicitacoes, true, cancellationToken);
        var solicitacoesFirebaseDto = solicitacoes
            .Where(s => !s.IsConcluida())
            .Select(s => CriarSolicitacaoFirebaseDto(s))
            .ToList();
        
        // Adicionando os dados no firebase em uma nova coleção (c_solicitacao_reverted) como cópia
        var solicitacoesFirebase = solicitacoesFirebaseDto
            .Select(s => s.Flatten(root: $"{ColecoesFirebase.Solicitacao}_reverted/{s.Id}"))
            .ToList();
        // Revertendo os dados no firebase para a versão anterior
        solicitacoesFirebase.AddRange(solicitacoesFirebaseDto
            .Select(s => s.Flatten(root: $"{ColecoesFirebase.Solicitacao}/{s.Id}")));

        var aggregatedDictionary = solicitacoesFirebase.AggregateUniqueDictionaries();
        // Atualizando os dados no firebase nas duas coleções
        await _solicitacaoFirebaseRepository.AddUpdateAsync(aggregatedDictionary, cancellationToken).ConfigureAwait(false);
        
        return solicitacoes;
    }
    
    
    private static SolicitacaoFirebaseDto CriarSolicitacaoFirebaseDto(Entities.Entities.Solicitacao solicitacao)
    {
        return new SolicitacaoFirebaseDto() {
            Id = solicitacao.Id,
            Origem = new ObjetoDeManobraDto(solicitacao.Origem.Codigo, solicitacao.Origem.Nome),
            Destino = new ObjetoDeManobraDto(solicitacao.Destino.Codigo, solicitacao.Destino.Nome),
            Local = solicitacao.Local is null ? null : new ObjetoDeManobraDto(solicitacao.Local.Codigo, solicitacao.Local.Nome),
            EncaminharPara = solicitacao.EncaminharPara is null ? null : new ObjetoDeManobraDto(solicitacao.EncaminharPara.Codigo, solicitacao.EncaminharPara.Nome),
            Motivo = solicitacao.Motivo,
            Mensagem = solicitacao.Mensagem,
            InformacaoAdicional = solicitacao.InformacaoAdicional,
            Status = solicitacao.Status.GetDescription(),
            StatusId = (short) solicitacao.Status,
            Historicos = solicitacao.HistoricosDeStatus.Select(h => new HistoricoDeSolicitacaoDto
            {
                Status = h.Status.GetDescription(),
                StatusId = (short) h.Status, 
            }).ToList(),
            Chat = solicitacao.Chat.Select(c => new ChatDeSolicitacaoDto()
            {
                Mensagem = c.Mensagem,
                Usuario = c.UsuarioRemetente.Nome,
                OrigemRemetente = c.Origem.Nome,
                DataEHoraDeEnvio = c.DataEHoraDeEnvio,
                Lida = c.DataEHoraDeLeituraDoDestinatario.HasValue,
                Entregue = c.DataEHoraDeEntregaAoDestinatario.HasValue
                
            }).ToList(),
            DataDeAtualizacao = solicitacao.UpdatedAt,
            DataDeCriacao = solicitacao.CreatedAt,
            Encaminhada = solicitacao.Encaminhada,
            Tags = solicitacao.Tags,
            Impedimento =string.IsNullOrWhiteSpace(solicitacao.DetalheDoImpedimento) ? null :new ImpedimentoDto(solicitacao.DetalheDoImpedimento)
        };
    }
}

public class SolicitacaoFirebaseDto
{
    public SolicitacaoFirebaseDto()
    {
        Id = string.Empty;
        Origem = new ObjetoDeManobraDto(string.Empty, string.Empty);
        Destino = new ObjetoDeManobraDto(string.Empty, string.Empty);
        Historicos = new List<HistoricoDeSolicitacaoDto>();
        Chat = new List<ChatDeSolicitacaoDto>();
        Mensagem = string.Empty;
        Status = string.Empty;
        Tags = [];
    }

    public string Id { get; set; }
    public ObjetoDeManobraDto Origem { get; set; }
    public ObjetoDeManobraDto Destino { get; set; }
    public ObjetoDeManobraDto? Local { get; set; }
    public ObjetoDeManobraDto? EncaminharPara { get; set; }
    public string? Motivo { get; set; }
    public string Mensagem { get; set; }
    public string? InformacaoAdicional { get; set; }
    public string Status { get; set; }
    public short StatusId { get; set; }
    public DateTime DataDeCriacao { get; set; }
    public DateTime DataDeAtualizacao { get; set; }
    public ImpedimentoDto? Impedimento { get; set; }
    public IEnumerable<HistoricoDeSolicitacaoDto> Historicos { get; set; }
    public List<ChatDeSolicitacaoDto> Chat { get; set; }
    public bool Encaminhada { get; set; }
    public string[] Tags { get; set; }
}

public class StatusDeSolicitacaoFirebaseDto
{
    public StatusDeSolicitacaoFirebaseDto(
        string idSolicitacao,
        string status,
        short statusId,
        DateTime dataDeAtualizacao,
        List<ChatDeSolicitacaoDto>? chat,
        List<HistoricoDeSolicitacaoDto> historicos)
    {
        Status = status;
        StatusId = statusId;
        DataDeAtualizacao = dataDeAtualizacao;
        Chat = chat ?? [];
        Historicos = historicos;
    }

    public string Status { get; private set; }
    public short StatusId { get; private set; }
    public DateTime DataDeAtualizacao { get; private set; }
    public List<ChatDeSolicitacaoDto> Chat { get; private set; }
    public List<HistoricoDeSolicitacaoDto> Historicos { get; private set; }
}

public class ChatDeSolicitacaoDto
{
    public ChatDeSolicitacaoDto()
    {
        Mensagem = string.Empty;
        Usuario = string.Empty;
        OrigemRemetente = string.Empty;
    }

    public string Mensagem { get; set; }
    public string Usuario { get; set; }
    public string OrigemRemetente { get; set; }
    public DateTime DataEHoraDeEnvio { get; set; }
    public bool Lida { get; set; }
    public bool Entregue { get; set; }
}

public class HistoricoDeSolicitacaoDto
{
    public HistoricoDeSolicitacaoDto()
    {
        Status = string.Empty;
    }

    public short StatusId { get; set; }
    public string Status { get; set; }
}

public class ImpedimentoDto
{
    public ImpedimentoDto(string motivo)
    {
        Motivo = motivo;
    }
    public string Motivo { get; }
}