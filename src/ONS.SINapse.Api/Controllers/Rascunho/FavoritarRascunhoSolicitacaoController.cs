using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ONS.SINapse.Business.IBusiness.Firebase;
using ONS.SINapse.Business.Imp.Claims;
using ONS.SINapse.Shared.Constants;
using ONS.SINapse.Shared.DTO.Firebase;

namespace ONS.SINapse.Api.Controllers;

[ApiController]
[Route("api/rascunho-solicitacao/{id}/favoritos")]
[Authorize]
public class FavoritarRascunhoSolicitacaoController : ControllerBase
{
    private readonly IRascunhoSolicitacaoBusiness _rascunhoSolicitacaoBusiness;

    public FavoritarRascunhoSolicitacaoController(IRascunhoSolicitacaoBusiness rascunhoSolicitacaoBusiness)
    {
        _rascunhoSolicitacaoBusiness = rascunhoSolicitacaoBusiness;
    }
    
    [HttpPost]
    [UserClaimRequirement(Operacoes.CriarRascunhoSolicitacao)]
    public async Task<IActionResult> Adicionar(FavoritarRascunhoSolicitacaoDto favoritarRascunhoSolicitacao, CancellationToken cancellationToken)
    {
        await _rascunhoSolicitacaoBusiness.FavoritarRascunhoAsync(favoritarRascunhoSolicitacao, cancellationToken);
        return Created();
    }
    
    [HttpDelete]
    [UserClaimRequirement(Operacoes.RemoverRascunhoSolicitacao)]
    public async Task<IActionResult> Remover(DesfavoritarRascunhoSolicitacaoDto desfavoritarRascunhoSolicitacao, CancellationToken cancellationToken)
    {
        await _rascunhoSolicitacaoBusiness.RemoverRascunhoFavoritoAsync(desfavoritarRascunhoSolicitacao, cancellationToken);
        return Ok();
    }
}