using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ONS.SINapse.Business.IBusiness;
using ONS.SINapse.Shared.DTO;

namespace ONS.SINapse.Api.Controllers;

[ApiController]
[Route("api/[controller]")]
public class AuthController : ControllerBase
{
    private readonly IAuthBusiness _authBusiness;

    public AuthController(
        IAuthBusiness authBusiness)
    {
        _authBusiness = authBusiness;
    }

    [HttpPost("login/token")]
    [Authorize]
    public async Task<IActionResult> TokenAuth(CancellationToken cancellationToken)
    {
        var authorization = await _authBusiness.AuthorizeAsync(cancellationToken);
        if(authorization is null) return Forbid();
        return Ok(authorization);
    }
}
