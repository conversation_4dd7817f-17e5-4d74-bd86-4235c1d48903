using Microsoft.AspNetCore.Mvc;
using ONS.SINapse.Business.IBusiness.Firebase;
using ONS.SINapse.Business.Imp.Claims;
using ONS.SINapse.Shared.Constants;
using ONS.SINapse.Shared.Identity;

namespace ONS.SINapse.Api.Controllers.Admin;

[ApiController]
[Route("api/firebase")]
[ClaimRequirement(PopClaimTypes.Operation, Operacoes.Administrar)]
public class FirebaseController : ControllerBase
{
    private readonly ICollectionFirebaseBusiness _firebaseCollectionBusiness;
    public FirebaseController(ICollectionFirebaseBusiness firebaseCollectionBusiness)
    {
        _firebaseCollectionBusiness = firebaseCollectionBusiness;
    }

    [HttpPatch]
    public async Task<IActionResult> PatchAsync([FromQuery] string collectionName, IFormFile jsonFile, CancellationToken cancellationToken)
    {
        await _firebaseCollectionBusiness.PatchAsync(collectionName, jsonFile, cancellationToken);
        return NoContent();
    }

    [HttpPut]
    public async Task<IActionResult> PutAsync([FromQuery] string collectionName, IFormFile jsonFile, CancellationToken cancellationToken)
    {
        await _firebaseCollectionBusiness.PutAsync(collectionName, jsonFile, cancellationToken);
        return NoContent();
    }

    [HttpDelete]
    public async Task<IActionResult> DeleteAsync([FromQuery] string collectionName, CancellationToken cancellationToken)
    {
        await _firebaseCollectionBusiness.DeleteAsync(collectionName, cancellationToken);
        return NoContent();
    }

    [HttpGet]
    public async Task<IActionResult> GetAsync([FromQuery] string collectionName, CancellationToken cancellationToken)
    {
        var data = await _firebaseCollectionBusiness.GetAsync(collectionName, cancellationToken);
        return File(data.Arquivo ?? Stream.Null, "text/json", data.FileName);
    }
}
