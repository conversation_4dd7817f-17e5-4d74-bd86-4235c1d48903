using Microsoft.AspNetCore.Mvc;
using ONS.SINapse.Business.Imp.Claims;
using ONS.SINapse.CacheSync.Services;
using ONS.SINapse.Shared.Constants;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Identity;

namespace ONS.SINapse.Api.Controllers.Admin;

[ApiController]
[Route("api/dataset/solicitacao/{view}")]
[ClaimRequirement(PopClaimTypes.Role, Roles.Administrador)]
public class SolicitacaoDatasetController(ISinapseDadosDatasetService client) : ControllerBase
{
    /// <summary>
    /// Retorna os dados de uma query SQL informada pelo usuário.
    /// </summary>
    /// <param type="ValidacaoSqlDatasetDto" name="dto">Objeto de validação.</param>
    /// <param type="CancellationToken" name="cancellationToken"></param>
    /// <returns>Lista de dataset items.</returns>
    [HttpPost]
    public async Task<IActionResult> ValidarSql(ValidacaoSqlDatasetDto dto, CancellationToken cancellationToken)
    {
        var validarSql = await client.ValidarSqlDataset(viewName: dto.ViewName, sqlQuery: dto.SqlQuery, query: dto.RsqlQuery, cancellationToken);
        return Ok(validarSql);
    }
}
