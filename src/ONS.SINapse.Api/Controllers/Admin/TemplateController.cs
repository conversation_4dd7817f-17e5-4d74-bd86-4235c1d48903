using Microsoft.AspNetCore.Mvc;
using ONS.SINapse.Business.IBusiness.TemplatesDeSolicitacao;
using ONS.SINapse.Business.Imp.Claims;
using ONS.SINapse.Shared.Constants;
using ONS.SINapse.Shared.Identity;

namespace ONS.SINapse.Api.Controllers.Admin;

[ApiController]
[Route("api/painel/templates")]
[ClaimRequirement(PopClaimTypes.Role, Roles.Administrador)]
public class TemplateController : ControllerBase
{
    [HttpGet("exportar")]
    public async Task<IActionResult> Exportar([FromServices] IExportacaoTemplatesDeSolicitacaoBusiness exportacaoTemplatesDeSolicitacaoBusiness, CancellationToken cancellationToken)
    {
        var templates = await exportacaoTemplatesDeSolicitacaoBusiness.ExportarAsync(cancellationToken);
        return File(templates, "application/zip", "templates.zip");
    }
}