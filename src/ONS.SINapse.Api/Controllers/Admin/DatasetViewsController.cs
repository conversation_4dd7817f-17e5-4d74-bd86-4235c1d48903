using Microsoft.AspNetCore.Mvc;
using ONS.SINapse.Business.IBusiness.TemplatesDeSolicitacao;
using ONS.SINapse.Business.Imp.Claims;
using ONS.SINapse.Shared.Constants;
using ONS.SINapse.Shared.Identity;

namespace ONS.SINapse.Api.Controllers.Admin;

[ApiController]
[Route("api/views")]
[ClaimRequirement(PopClaimTypes.Role, Roles.Administrador)]
public class DatasetViewsController : ControllerBase
{
    [HttpGet]
    public async Task<IActionResult> Exportar([FromServices] IExportacaoDatasetViewsBusiness exportacaoDatasetViewsBusiness,
        CancellationToken cancellationToken)
    {
        await using var stream = await exportacaoDatasetViewsBusiness.ExportarAsync(cancellationToken);
        stream.Position = 0;
        var buffer = new byte[stream.Length];
        _ = await stream.ReadAsync(buffer, cancellationToken);
        var timestamp = DateTime.UtcNow.ToString("yyyyMMdd_HHmmss");
        var fileName = $"sql_views_{timestamp}.zip";
        return File(buffer, "application/zip", fileName);
    }
}