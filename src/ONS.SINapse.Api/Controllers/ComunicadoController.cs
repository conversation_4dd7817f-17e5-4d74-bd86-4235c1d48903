using System.Net.Mime;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ONS.SINapse.Shared.Identity;
using ONS.SINapse.Business.IBusiness;
using ONS.SINapse.Business.Imp.Claims;
using ONS.SINapse.Shared.Constants;
using ONS.SINapse.Shared.DTO;

namespace ONS.SINapse.Api.Controllers;

[ApiController]
[Route("api/comunicados")]
[Authorize]
public class ComunicadoController : ControllerBase
{
    private readonly IComunicadoBusiness _business;

    public ComunicadoController(IComunicadoBusiness business)
    {
        _business = business;
    }

    [HttpGet]
    [UserClaimRequirement(Operacoes.ConsultarComunicado)]
    public async Task<IActionResult> Get(CancellationToken cancellationToken)
    {
        _ = Task.Run(() => _business.ExcluirComunicadosAntigos(CancellationToken.None), cancellationToken);
        return Ok(await _business.GetAsync(cancellationToken));
    }
    
    [HttpPost("{id}/leitores")]
    [UserClaimRequirement(Operacoes.RegistrarAudiencia)]
    public async Task<IActionResult> RegistrarLeitura([FromRoute] string id, CancellationToken cancellationToken)
    {
        return Ok(await _business.RegistrarLeituraAsync(id, cancellationToken));
    }

    [HttpPost]
    [Consumes(MediaTypeNames.Application.Json)]
    [UserClaimRequirement(false,Operacoes.CriarBroadcast, Operacoes.CriarMulticast)]
    public async Task<IActionResult> Add([FromBody] ComunicadoDto dto, CancellationToken cancellationToken)
    {
        return Ok(await _business.AddAsync(dto, cancellationToken));
    }

    [HttpPost("{id}/complementos")]
    [UserClaimRequirement(false,Operacoes.CriarBroadcast, Operacoes.CriarMulticast)]
    public async Task<IActionResult> AdicionarComplemento([FromRoute] string id,
        [FromBody] ComplementoDeComunicadoDto complemento, CancellationToken cancellationToken)
    {
        return Ok(await _business.AdicionarComplentoAsync(id, complemento, cancellationToken));
    }

    [HttpDelete("excluir-antigos")]
    [XTokenRequirement(XTokenRequirementOrigin.LambdaAws)]
    public async Task<IActionResult> ExcluirComunicadosAntigos(CancellationToken cancellationToken)
        => Ok(await _business.ExcluirComunicadosAntigos(cancellationToken));
    
    [HttpGet("areas-eletricas")]
    public async Task<IActionResult> GetAreasEletricas(CancellationToken cancellationToken) 
        => Ok(await _business.GetAreasEletricas(cancellationToken));

    [HttpGet("centros-operacao")]
    [Consumes(MediaTypeNames.Application.Json)]
    [ClaimRequirement(PopClaimTypes.Role, Roles.OperadorCentro)]
    public IActionResult CentrosDeOperacao()
    {
        return Ok(CentrosDeOperacoes.GetCentrosDeOperacao().Where(x => !x.IsCnos));
    }
    
    [HttpGet("{id}")]
    [UserClaimRequirement(Operacoes.ConsultarComunicado)]
    public async Task<IActionResult> GetById([FromRoute] string id, CancellationToken cancellationToken)
    {
        return Ok(await _business.GetByIdAsync(id, cancellationToken));
    }
    
    [HttpGet("quantidade-nao-lidos")]
    [UserClaimRequirement(Operacoes.ConsultarComunicado)]
    public async Task<IActionResult> GetQuantidadeNaoLidos(CancellationToken cancellationToken)
       => Ok(await _business.ObterTotalDeComunicadosNaoLidosPeloUsuario(cancellationToken));
}