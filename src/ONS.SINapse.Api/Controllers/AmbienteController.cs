using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace ONS.SINapse.Api.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class AmbienteController : ControllerBase
    {
        public AmbienteController()
        {
        }

        [HttpGet("teste")]
        [AllowAnonymous]
        public ActionResult Get()
        {
            return Ok("ok");
        }
    }
}
