using Microsoft.AspNetCore.Mvc;
using ONS.SINapse.Business.Imp.Claims;
using ONS.SINapse.Shared.Constants;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;

namespace ONS.SINapse.Api.Controllers.Solicitacao;

[ApiController]
[Route("api/solicitacao/encaminhar")]
[UserClaimRequirement(Operacoes.CriarSolicitacao)]
public class EncaminhamentoDeSolicitacaoController : ControllerBase
{
    private readonly IMediatorHandler _mediatorHandler;

    public EncaminhamentoDeSolicitacaoController(IMediatorHandler mediatorHandler)
    {
        _mediatorHandler = mediatorHandler;
    }
    
    [HttpPost]
    public async Task<IActionResult> Encaminhar([FromBody] EncaminharSolicitacaoCommand command, CancellationToken cancellationToken)
    {
        await _mediatorHandler.EnviarComandoAsync(command, cancellationToken);
        return Ok(new { });
    }
}