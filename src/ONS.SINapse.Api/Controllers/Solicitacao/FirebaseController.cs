using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ONS.SINapse.Repository.Imp.Store;
using ONS.SINapse.Shared.Bus;
using ONS.SINapse.Solicitacao.EventHandlers.Events;

namespace ONS.SINapse.Api.Controllers.Solicitacao;
[ApiController]
[Authorize]
[Route("api/solicitacao/firebase", Name = "Solicitacao::Firebase")]
public class FirebaseController(ISqsBus bus) : ControllerBase
{
    [HttpGet()]
    public IActionResult Get()
    {
        return Ok(SolicitacaoStore.Solicitacoes);
    }
    
    [HttpGet("{id}")]
    public IActionResult Get(string id)
    {
        var solicitacao = SolicitacaoStore.Solicitacoes.FirstOrDefault(x => x.Id == id);
        return solicitacao == null ? NotFound() : Ok(solicitacao);
    }
    
    [HttpPost("enviar-mongo")]
    public IActionResult EnviarMongo([FromBody] SolicitacaoConcluidaEvent eventMessage)
    {
        bus.Publish(eventMessage);
        return Ok();
    }
}