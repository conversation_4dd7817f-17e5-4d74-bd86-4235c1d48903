using System.Net.Mime;
using Microsoft.AspNetCore.Mvc;
using ONS.SINapse.Business.Imp.Claims;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Shared.Constants;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using ONS.SINapse.Solicitacao.Dtos;
using ONS.SINapse.Solicitacao.QueryHandlers.Queries;

namespace ONS.SINapse.Api.Controllers.Solicitacao;

[ApiController]
[Route("api/solicitacao/pesquisa")]
[UserClaimRequirement(Operacoes.ConsultarHistorico)]
public class ConsultaHistoricoDeSolicitacaoController : ControllerBase
{
    private readonly IMediatorHandler _mediatorHandler;

    public ConsultaHistoricoDeSolicitacaoController(IMediatorHandler mediatorHandler)
    {
        _mediatorHandler = mediatorHandler;
    }
    
    [HttpGet("status")]
    [Consumes(MediaTypeNames.Application.Json)]
    public IActionResult Get()
    {
        List<StatusDeSolicitacao> filtroHistorico = new(){ StatusDeSolicitacao.Cancelada, StatusDeSolicitacao.Finalizada };
        var statusHistorico = filtroHistorico
            .Select(t => new StatusDeSolicitacaoDto
            (
                (short) t,
                t.GetDescription()
            ));

        return Ok(statusHistorico);
    }
    
    [HttpGet]
    [Consumes(MediaTypeNames.Application.Json)]
    public async Task<IActionResult> Pesquisa([FromQuery]ConsultaHistoricoSolicitacaoQuery query, CancellationToken cancellationToken)
    {
        var dados = await _mediatorHandler.BuscarDadosAsync(query, cancellationToken);
        return Ok(dados);
    }
    
    [HttpGet("analitica")]
    [Consumes(MediaTypeNames.Application.Json)]
    [UserClaimRequirement(Operacoes.ConsultaGeralDoHistorico)]
    public async Task<IActionResult> ObterPesquisaAnalitica([FromQuery] ConsultaHistoricoSolicitacaoAnaliticaQuery query,
        CancellationToken cancellationToken)
    {
        var dados = await _mediatorHandler.BuscarDadosAsync(query, cancellationToken);
        return Ok(dados);
    }

    [HttpPost("exportar")]
    [Consumes(MediaTypeNames.Application.Json)]
    public async Task<IActionResult> Exportar([FromBody] ExportarConsultaHistoricoCommand command, CancellationToken cancellationToken)
    {
        var arquivo = await _mediatorHandler.EnviarComandoAsync<ExportarConsultaHistoricoCommand, ArquivoResultDto>(command, cancellationToken);
        Response.Headers.Append("Nome-Arquivo", arquivo.FileName);
        return File(arquivo.Arquivo ?? [], "text/csv", arquivo.FileName);
    }

    [HttpPost("exportar-analitico")]
    public async Task<IActionResult> ExportarAnalitico([FromBody]ExportarConsultaHistoricoAnaliticaCommand command,
        CancellationToken cancellationToken)
    {
        var arquivo = await _mediatorHandler.EnviarComandoAsync<ExportarConsultaHistoricoAnaliticaCommand, ArquivoResultDto>(command, cancellationToken);
        Response.Headers.Append("Nome-Arquivo", arquivo.FileName);
        return File(arquivo.Arquivo ?? [], "text/csv", arquivo.FileName);
    }

    [HttpGet("centros")]
    public async Task<IActionResult> GetCentrosEmitentes(CancellationToken cancellationToken)
    {
        var dados = 
            await _mediatorHandler
                .BuscarDadosAsync(new BuscarCentrosDeOperacaoEmitentesDeSolicitacaoQuery(), cancellationToken);

        return Ok(dados);
    }

    [HttpGet("destinatarios")]
    public async Task<IActionResult> GetDestinatariosDeSolicitacao(CancellationToken cancellationToken)
    {
        var dados = await _mediatorHandler
            .BuscarDadosAsync(new BuscarDestinatariosDeSolicitacaoQuery(), cancellationToken);

        return Ok(dados);
    }
}