using System.Net.Mime;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ONS.SINapse.Business.Imp.Claims;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Shared.Constants;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Helpers;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Shared.Messages;
using ONS.SINapse.Solicitacao.Dtos;
using ONS.SINapse.Solicitacao.Dtos.Integracao;
using ONS.SINapse.Solicitacao.Factories.Integracao;

namespace ONS.SINapse.Api.Controllers.Solicitacao;

[ApiController]
[Route("api/solicitacao")]
public class StatusDeSolicitacaoController : ControllerBase
{
    private readonly IMediatorHandler _mediatorHandler;
    private readonly ICriarCommandStatusDeSolicitacaoRecebidoFactory _commandStatusDeSolicitacaoRecebidoFactory;

    public StatusDeSolicitacaoController(IMediatorHandler mediatorHandler, ICriarCommandStatusDeSolicitacaoRecebidoFactory commandStatusDeSolicitacaoRecebidoFactory)
    {
        _mediatorHandler = mediatorHandler;
        _commandStatusDeSolicitacaoRecebidoFactory = commandStatusDeSolicitacaoRecebidoFactory;
    }

    [HttpGet("status")]
    [Consumes(MediaTypeNames.Application.Json)]
    [Authorize]
    public IActionResult Get()
    {
        var status = EnumHelper
            .ToDictionary<StatusDeSolicitacao>()
            .Where(s => s.Key > 0)
            .Select(t => new StatusDeSolicitacaoDto
            (
                t.Key,
                t.Value
            ));

        return Ok(status);
    }

    [HttpPatch]
    [Consumes(MediaTypeNames.Application.Json)]
    [UserClaimRequirement(validarTodos: false,
        Operacoes.ConfirmarSolicitacao, 
        Operacoes.CancelarSolicitacao, 
        Operacoes.FinalizarSolicitacao, 
        Operacoes.InformarImpedimentoSolicitacao,
        Operacoes.InformarCienciaImpedimento)]
    public async Task<IActionResult> Patch([FromBody] StatusSolicitacaoDto request,
        CancellationToken cancellationToken)
    {
        var commands = _commandStatusDeSolicitacaoRecebidoFactory
            .ObterCommand(request.Solicitacoes);
        
        var tasks = commands
            .Select(async command => await SendCommandsAsync(command, cancellationToken))
            .ToList();
        
        await Task.WhenAll(tasks);

        var result = new StatusDeSolicitacaoIntegracaoEnvioEmLoteDto(tasks
            .Select(x => x.Result)
            .SelectMany(x => x.Solicitacoes)
            .ToList());
        
        return Ok(result);
    }
    
    private Task<StatusDeSolicitacaoIntegracaoEnvioEmLoteDto> SendCommandsAsync<T>(T command, CancellationToken cancellationToken) where T : Command<StatusDeSolicitacaoIntegracaoEnvioEmLoteDto>
    {
        return _mediatorHandler.EnviarComandoAsync<T, StatusDeSolicitacaoIntegracaoEnvioEmLoteDto>(command, cancellationToken);
    }
}