using System.Net.Mime;
using Microsoft.AspNetCore.Mvc;
using ONS.SINapse.Business.Imp.Claims;
using ONS.SINapse.Shared.Constants;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using ONS.SINapse.Solicitacao.QueryHandlers.Queries;

namespace ONS.SINapse.Api.Controllers.Solicitacao;

[ApiController]
[Route("api/solicitacao")]
public class SolicitacaoController : ControllerBase
{
    private readonly IMediatorHandler _mediatorHandler;

    public SolicitacaoController(IMediatorHandler mediatorHandler)
    {
        _mediatorHandler = mediatorHandler;
    }

    [HttpGet("{id}")]
    [Consumes(MediaTypeNames.Application.Json)]
    [UserClaimRequirement(false,Operacoes.ConsultarPainelSolicitacoesTempoReal, Operacoes.ConsultarHistorico)]
    public async Task<IActionResult> GetById(string id, CancellationToken cancellationToken)
    {
        var query = new BuscarCadastroSolicitacaoPorIdQuery(id);
        return Ok(await _mediatorHandler.BuscarDadosAsync(query, cancellationToken));
    }


    [HttpPost]
    [Consumes(MediaTypeNames.Application.Json)]
    [UserClaimRequirement(Operacoes.CriarSolicitacao)]
    public async Task<IActionResult> Add([FromBody] CadastroSolicitacaoDto solicitacao, CancellationToken cancellationToken)
    {
        var command = new CadastrarSolicitacaoEmLoteCommand(new List<CadastroSolicitacaoDto> { solicitacao });
        var result = await _mediatorHandler
            .EnviarComandoAsync<CadastrarSolicitacaoEmLoteCommand, ResultadoCadastroSolicitacaoEmLoteDto>(command, cancellationToken);
        
        return Ok(result.Resultado.FirstOrDefault(x => x.ValidationResult.IsValid));
    }

    [HttpPost("adicionar-lote")]
    [Consumes(MediaTypeNames.Application.Json)]
    [UserClaimRequirement(Operacoes.CriarSolicitacao)]
    public async Task<IActionResult> AddLote([FromBody] List<CadastroSolicitacaoDto> solicitacoes, CancellationToken cancellationToken)
    {
        var command = new CadastrarSolicitacaoEmLoteCommand(solicitacoes);

        var result = await _mediatorHandler
            .EnviarComandoAsync<CadastrarSolicitacaoEmLoteCommand, ResultadoCadastroSolicitacaoEmLoteDto>(command,
                cancellationToken);
        
        return Ok(result.Resultado.Where(x => x.ValidationResult.IsValid));
    }  
}