using Microsoft.AspNetCore.Mvc;
using ONS.SINapse.Business.Imp.Claims;
using ONS.SINapse.Shared.Constants;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using System.Net.Mime;

namespace ONS.SINapse.Api.Controllers.Solicitacao;

[ApiController]
[Route("api/solicitacao")]
public class ChatDeSolicitacaoController : ControllerBase
{
    private readonly IMediatorHandler _mediatorHandler;

    public ChatDeSolicitacaoController(IMediatorHandler mediatorHandler)
    {
        _mediatorHandler = mediatorHandler;
    }

    [HttpPost("{solicitacaoId}/chat/mensagem")]
    [UserClaimRequirement(Operacoes.EnviarMensagemViaChat)]
    [Consumes(MediaTypeNames.Application.Json)]
    public async Task EnviarMensagem(string solicitacaoId, [FromBody] EnvioDeMensagemDto dto, CancellationToken cancellationToken)
    {
        var command = new ChatDeSolicitacaoCommand(solicitacaoId, dto.Mensagem);
        await _mediatorHandler.EnviarComandoAsync(command, cancellationToken);
    }


    [HttpPut("chat/confirmar-entregas")]
    [UserClaimRequirement(Operacoes.RegistrarAudiencia)]
    [Consumes(MediaTypeNames.Application.Json)]
    public async Task<ActionResult> ConfirmarEntrega([FromBody] ConfirmacaoDeEntregaDto dto, CancellationToken cancellationToken)
    {
        var command = new ConfirmarEntregaCommand(dto.SolicitacoesId.ToList());
        var result =
            await _mediatorHandler.EnviarComandoAsync<ConfirmarEntregaCommand, ResultadoConfirmacaoDeEntregaDto>(command, cancellationToken);

        if (!result.ValidationResult.IsValid)
            return BadRequest(result);

        return Ok(result);
    }

    [HttpPut("{solicitacaoId}/chat/confirmar-leitura")]
    [UserClaimRequirement(Operacoes.RegistrarAudiencia)]
    public async Task ConfirmarLeitura(string solicitacaoId, CancellationToken cancellationToken)
    {
        var command = new ConfirmarLeituraCommand(solicitacaoId);
        await _mediatorHandler.EnviarComandoAsync(command, cancellationToken);
    }
}