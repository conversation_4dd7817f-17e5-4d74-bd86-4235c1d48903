using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ONS.SINapse.Business.IBusiness;
using System.Net.Mime;

namespace ONS.SINapse.Api.Controllers;

[ApiController]
[Route("api/alerta-sonoro")]
[Authorize]
public class AlertaSonoroController : ControllerBase
{
    private readonly IAlertaSonoroBusiness _alertaSonoroBusiness;

    public AlertaSonoroController(IAlertaSonoroBusiness alertaSonoroBusiness)
    {
        _alertaSonoroBusiness = alertaSonoroBusiness;
    }

    [HttpPut("ativar")]
    [Consumes(MediaTypeNames.Application.Json)]
    public async Task<IActionResult> Ativar(CancellationToken cancellationToken)
    {
        await _alertaSonoroBusiness.AtivarAsync(cancellationToken);
        return NoContent();
    }

    [HttpPut("desativar")]
    [Consumes(MediaTypeNames.Application.Json)]
    public async Task<IActionResult> Desativar(CancellationToken cancellationToken)
    {
        await _alertaSonoroBusiness.DesativarAsync(cancellationToken);
        return NoContent();
    }
}