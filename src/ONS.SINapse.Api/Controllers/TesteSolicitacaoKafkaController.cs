using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ONS.SINapse.Shared.Constants;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Identity;
using ONS.SINapse.Shared.Kafka.Providers;
using ONS.SINapse.Solicitacao.Workers.Messages;

namespace ONS.SINapse.Api.Controllers;

[ApiController]
[Authorize]
[Route("api/solicitacao-kafka")]
[ClaimAuthorize(PopClaimTypes.Operation, Operacoes.Administrar)]
public class TesteSolicitacaoKafkaController : ControllerBase
{
    private readonly IProducerProvider<CadastroSolicitacaoMessage> _producerProvider;

    public TesteSolicitacaoKafkaController(IProducerProvider<CadastroSolicitacaoMessage> producerProvider)
    {
        _producerProvider = producerProvider;
    }

    [HttpPost("cadastrar")]
    public async Task<IActionResult> CadastrarAsync([FromBody] CadastrarSolicitacaoKafka? cadastro, CancellationToken cancellationToken)
    {
        var mensagens = GerarSolicitacoes(cadastro);
        
        var token = Request.Headers.Authorization.ToString().Replace("Bearer ", "");
        
        var header = new Dictionary<string, string>
        {
            {"AuthorizationUser", token}
        };

        var tasks = mensagens.Select(mensagem =>
            _producerProvider.PublishAsync(mensagem, header, cancellationToken));
            
        await Task.WhenAll(tasks);
        
        if (cadastro?.VerMensagens ?? false)
            return Ok(mensagens);
        
        return NoContent();
    }
    
    private static IEnumerable<CadastroSolicitacaoMessage> GerarSolicitacoes(CadastrarSolicitacaoKafka? cadastro)
    {
        cadastro ??= new CadastrarSolicitacaoKafka(new CadastroSolicitacaoMessage
        {
            Origem = new ObjetoDeManobraDto("NE", "COSR-NE"),
            Destino = new ObjetoDeManobraDto("CHF", "CHESF"),
            Local = new ObjetoDeManobraDto("TESTE", "Apenas Teste"),
            EncaminharPara = new ObjetoDeManobraDto("CHF", "CHESF"),
            InformacaoAdicional = "Mensagem teste kafka",
            Mensagem = "Estou enviando essa solicitação apenas para testar o fincionamento do envio de mensagens kafka",
            SistemaDeOrigem = "SINAPSE",
            CodigoExterno = Guid.NewGuid().ToString(),
            Motivo = "Teste kafka",
            Tags = ["Geração"]
        }, 200);

        return Enumerable
            .Range(1, cadastro.Quantidade)
            .Select(index =>
            {
                var newCadastro = cadastro.Solicitacao.Copy();
                newCadastro.CodigoExterno = $"{cadastro.Prefixo}-{Guid.NewGuid()}-{index}";
                return newCadastro;
            });
    }
    
    public record CadastrarSolicitacaoKafka(
        CadastroSolicitacaoMessage Solicitacao,
        int Quantidade,
        string Prefixo = "kafka",
        bool VerMensagens = false);
}