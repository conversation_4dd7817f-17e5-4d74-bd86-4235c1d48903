using System.Net.Mime;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ONS.SINapse.Business.IBusiness;
using ONS.SINapse.Business.Imp.Claims;
using ONS.SINapse.Shared.Constants;
using ONS.SINapse.Shared.DTO;

namespace ONS.SINapse.Api.Controllers;

[ApiController]
[Route("api/tags")]
[Authorize]
public class TagController : ControllerBase
{
    private readonly ITagBusiness _tagBusiness;

    public TagController(ITagBusiness tagBusiness)
    {
        _tagBusiness = tagBusiness;
    }

    [HttpPost]
    [Consumes(MediaTypeNames.Application.Json)]
    [UserClaimRequirement(Operacoes.GerenciarTags)]
    public async Task<IActionResult> PostAsync([FromBody] TagDto dto, CancellationToken cancellationToken)
        => Ok(await _tagBusiness.AddAsync(dto, cancellationToken));
    
    [HttpPut("{id}")]
    [Consumes(MediaTypeNames.Application.Json)]
    [UserClaimRequirement(Operacoes.GerenciarTags)]
    public async Task<IActionResult> PutAsync([FromRoute] string id,[FromBody] TagDto dto, CancellationToken cancellationToken)
        => Ok(await _tagBusiness.UpdateAsync(id, dto, cancellationToken));
    
    [HttpDelete("{id}")]
    [Consumes(MediaTypeNames.Application.Json)]
    [UserClaimRequirement(Operacoes.GerenciarTags)]
    public async Task<IActionResult> DeleteAsync([FromRoute] string id, CancellationToken cancellationToken)
    {
        await _tagBusiness.DeleteAsync(id, cancellationToken);
        return NoContent();
    }
    
    [HttpGet("{id}")]
    public async Task<IActionResult> Get([FromRoute] string id, CancellationToken cancellationToken)
        => Ok(await _tagBusiness.GetAsync(id, cancellationToken));
    
    [HttpGet]
    public async Task<IActionResult> Get(CancellationToken cancellationToken)
        => Ok(await _tagBusiness.GetAllAsync(cancellationToken));
}