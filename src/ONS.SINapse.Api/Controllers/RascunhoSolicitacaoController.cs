using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ONS.SINapse.Business.IBusiness.Firebase;
using ONS.SINapse.Business.Imp.Claims;
using ONS.SINapse.Shared.Constants;
using ONS.SINapse.Shared.DTO.Firebase;

namespace ONS.SINapse.Api.Controllers;

[ApiController]
[Route("api/rascunho-solicitacao")]
[Authorize]
public class RascunhoSolicitacaoController : ControllerBase
{
    private readonly IRascunhoSolicitacaoBusiness _rascunhoSolicitacaoBusiness;

    public RascunhoSolicitacaoController(IRascunhoSolicitacaoBusiness rascunhoSolicitacaoBusiness)
    {
        _rascunhoSolicitacaoBusiness = rascunhoSolicitacaoBusiness;
    }
    
    [HttpPost]
    [UserClaimRequirement(Operacoes.CriarRascunhoSolicitacao)]
    public async Task<IActionResult> Adicionar([FromBody] RascunhoSolicitacaoDto rascunho, CancellationToken cancellationToken)
    {
        await _rascunhoSolicitacaoBusiness.AdicionarRascunhoAsync(rascunho, cancellationToken);
        return Ok();
    }
    
    [HttpPut]
    [UserClaimRequirement(Operacoes.EditarRascunhoSolicitacao)]
    public async Task<IActionResult> Atualizar([FromBody] RascunhoSolicitacaoDto rascunho, CancellationToken cancellationToken)
    {
        await _rascunhoSolicitacaoBusiness.EditarRascunhoAsync(rascunho, cancellationToken);
        return Ok();
    }
    
    [HttpPatch("invalidar/{id:guid}")]
    [UserClaimRequirement(Operacoes.EditarRascunhoSolicitacao)]
    public async Task<IActionResult> Invalidar([FromRoute] Guid id, CancellationToken cancellationToken)
    {
        await _rascunhoSolicitacaoBusiness.InvalidarRascunhoAsync(id, cancellationToken);
        return Ok();
    }
    
    [HttpDelete("{id:guid}")]
    [UserClaimRequirement(Operacoes.RemoverRascunhoSolicitacao)]
    public async Task<IActionResult> Remover([FromRoute] Guid id, CancellationToken cancellationToken)
    {
        await _rascunhoSolicitacaoBusiness.RemoverRascunhoAsync(id, cancellationToken);
        return Ok();
    }
}