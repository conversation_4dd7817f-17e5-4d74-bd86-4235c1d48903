using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ONS.SINapse.Business.IBusiness.Firebase;
using ONS.SINapse.Shared.DTO.Firebase;

namespace ONS.SINapse.Api.Controllers;

[ApiController]
[Route("api/configuracao-usuario")]
[Authorize]
public class ConfiguracaoUsuarioController : ControllerBase
{
    private readonly IConfiguracaoUsuarioBusiness _configuracaoUsuarioBusiness;

    public ConfiguracaoUsuarioController(IConfiguracaoUsuarioBusiness configuracaoUsuarioBusiness)
    {
        _configuracaoUsuarioBusiness = configuracaoUsuarioBusiness;
    }

    [HttpPost("notificacao")]
    public async Task<IActionResult> Adicionar(NotificacaoConfiguracaoDto notificacaoConfiguracao, CancellationToken cancellationToken)
    {
        await _configuracaoUsuarioBusiness.AdicionarConfiguracaoNotificacaoAsync(notificacaoConfiguracao, cancellationToken);
        return Created();
    }
}
