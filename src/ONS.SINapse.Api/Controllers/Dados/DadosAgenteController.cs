using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ONS.SINapse.CacheSync.Services;

namespace ONS.SINapse.Api.Controllers.Dados;


[Route("api/dados")]
[Authorize]
public class DadosAgenteController : ControllerBase
{
    private readonly ISinapseDadosService _sinapseDadosService;
    
    public DadosAgenteController(ISinapseDadosService sinapseDadosService)
    {
        _sinapseDadosService = sinapseDadosService;
    }
    
    [HttpGet("agente")]
    public async Task<IActionResult> ObterAgentes([FromQuery] string query, CancellationToken cancellationToken)
    {
        return Ok(await _sinapseDadosService.GetDadosAgente(query, cancellationToken));
    }
}