using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ONS.SINapse.Business.IBusiness.TemplatesDeSolicitacao;

namespace ONS.SINapse.Api.Controllers;

[ApiController]
[Route("api/templates")]
[Authorize]
public class TemplateDeSolicitacaoController : ControllerBase
{
    private readonly ITemplateDeSolicitacaoStep1Business _templateDeSolicitacaoStep1Business;
    private readonly ITemplateDeSolicitacaoStep3Business _templateDeSolicitacaoStep3Business;
    private readonly ITemplateDeSolicitacaoStep4Business _templateDeSolicitacaoStep4Business;
    private readonly ITemplateDeSolicitacaoStep5Business _templateDeSolicitacaoStep5Business;

    public TemplateDeSolicitacaoController(
        ITemplateDeSolicitacaoStep1Business templateDeSolicitacaoStep1Business,
        ITemplateDeSolicitacaoStep3Business templateDeSolicitacaoStep3Business,
        ITemplateDeSolicitacaoStep4Business templateDeSolicitacaoStep4Business,
        ITemplateDeSolicitacaoStep5Business templateDeSolicitacaoStep5Business)
    {
        _templateDeSolicitacaoStep1Business = templateDeSolicitacaoStep1Business;
        _templateDeSolicitacaoStep3Business = templateDeSolicitacaoStep3Business;
        _templateDeSolicitacaoStep4Business = templateDeSolicitacaoStep4Business;
        _templateDeSolicitacaoStep5Business = templateDeSolicitacaoStep5Business;
    }

    [HttpGet("step-1/{centro:required}")]
    public async Task<ActionResult> GetStep1([FromRoute] string centro, CancellationToken cancellationToken) =>
        Ok(await _templateDeSolicitacaoStep1Business.ObterPorOrigemAsync(centro, cancellationToken));
    
    [HttpGet("step-3/{templateId}")]
    public async Task<ActionResult> GetStep3(string templateId, CancellationToken cancellationToken) =>
        Ok(await _templateDeSolicitacaoStep3Business.ObterPorIdAsync(templateId, cancellationToken));

    [HttpGet("step-4/{templateId}")]
    public async Task<ActionResult> GetStep4(string templateId, CancellationToken cancellationToken) =>
        Ok(await _templateDeSolicitacaoStep4Business.ObterPorIdAsync(templateId, cancellationToken));
    
    [HttpGet("step-5/{templateId}")]
    public async Task<ActionResult> GetStep5(string templateId, CancellationToken cancellationToken) =>
        Ok(await _templateDeSolicitacaoStep5Business.ObterPorIdAsync(templateId, cancellationToken));
}