using Microsoft.AspNetCore.Mvc;
using ONS.SINapse.Business.IBusiness;
using ONS.SINapse.Shared.Constants;
using System.Net.Mime;
using ONS.SINapse.Business.Imp.Claims;

namespace ONS.SINapse.Api.Controllers;
[Route("api/status-agente")]
public class StatusAgenteController : ControllerBase
{
    private readonly IStatusAgenteBusiness _agenteBusiness;

    public StatusAgenteController(IStatusAgenteBusiness agenteBusiness)
    {
        _agenteBusiness = agenteBusiness;
    }
    
    [HttpGet]
    [Consumes(MediaTypeNames.Application.Json)]
    [UserClaimRequirement(Operacoes.ConsultarAgentes)]
    public async Task<IActionResult> Get(CancellationToken cancellationToken)
        => Ok(await _agenteBusiness.GetStatusAgentes(cancellationToken));
}