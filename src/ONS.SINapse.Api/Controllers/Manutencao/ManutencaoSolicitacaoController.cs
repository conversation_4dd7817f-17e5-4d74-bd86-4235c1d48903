using Firebase.Database;
using Firebase.Database.Query;
using Microsoft.AspNetCore.Mvc;
using ONS.SINapse.Business.Imp.Claims;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Repository.Imp.Context;
using ONS.SINapse.Repository.Imp.Repositories.Pipelines;
using ONS.SINapse.Repository.IRepository;
using ONS.SINapse.Repository.IRepository.Firebase;
using ONS.SINapse.Shared.Constants;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Identity;

namespace ONS.SINapse.Api.Controllers.Manutencao;

[ApiController]
[Route("api/manutencao/solicitacao")]
[ClaimRequirement(PopClaimTypes.Operation, Operacoes.Administrar)]
public class ManutencaoSolicitacaoController : ControllerBase
{
    private readonly ISolicitacaoRepository _solicitacaoRepository;
    private readonly ISolicitacaoFirebaseRepository _solicitacaoFirebaseRepository;
    private readonly IMongoReadOnlyConnection _mongoReadOnlyConnection;
    private readonly ChildQuery _collection;
    private readonly List<StatusDeSolicitacao> _listStatusFinalizados = [StatusDeSolicitacao.Cancelada, StatusDeSolicitacao.Finalizada];

    public ManutencaoSolicitacaoController(
        FirebaseClient firebaseClient,
        ISolicitacaoRepository solicitacaoRepository,
        ISolicitacaoFirebaseRepository solicitacaoFirebaseRepository,
        IMongoReadOnlyConnection mongoReadOnlyConnection
        )
    {
        _solicitacaoRepository = solicitacaoRepository;
        _solicitacaoFirebaseRepository = solicitacaoFirebaseRepository;
        _mongoReadOnlyConnection = mongoReadOnlyConnection;
        _collection = firebaseClient.Child(ColecoesFirebase.Solicitacao);
    }

    [HttpGet("listar-solicitacao-firebase")]
    public async Task<IActionResult> ListarSolicitacaoFirebase(CancellationToken cancellationToken)
    {
        var solicitacaoesFirebase = await _collection.OnceSingleAsync<Dictionary<string,SolicitacaoFirebaseDto>>()
            .WaitAsync(cancellationToken)
            .ConfigureAwait(false);

        return Ok(new
        {
            total = solicitacaoesFirebase?.Count ?? 0,
            solicitacaoes = solicitacaoesFirebase?.Values
        });
    }
    
    [HttpGet("listar-solicitacao-presas-firebase")]
    public async Task<IActionResult> ListarSolicitacaoPresasFirebase(CancellationToken cancellationToken)
    {
        var solicitacaoesFirebase = await _collection
            .OnceSingleAsync<Dictionary<string,SolicitacaoFirebaseDto>>()
            .WaitAsync(cancellationToken)
            .ConfigureAwait(false);
        if (solicitacaoesFirebase == null)
        {
            return Ok(new
            {
                total = 0, 
                solicitacoes = new List<Entities.Entities.Solicitacao>()
            });
        }
        var solicitacoesId = solicitacaoesFirebase.Keys;
        var solicitacoes = await _solicitacaoRepository.GetAsync( 
                s => _listStatusFinalizados.Contains(s.Status) && solicitacoesId.Contains(s.Id), cancellationToken
            );
        
        return Ok(new
        {
            total = solicitacoes.Count, solicitacoes
        });
    }
    
    [HttpGet("listar-solicitacao-erro-historico")]
    public async Task<IActionResult> ListarSolicitacaoComErroNoHistorico(CancellationToken cancellationToken)
    {
        var solicitacoes = await _mongoReadOnlyConnection
            .AggregateAsync<Entities.Entities.Solicitacao>(SolicitacaoPipeline.SolicitacoesComErroListChatPipeline(), cancellationToken)
            .ConfigureAwait(false);
        
        return Ok(new
        {
            total = solicitacoes.Count(), solicitacoes
        });
    }
    
    [HttpPost("corrigir-solicitacao-erro-historico")]
    public async Task<IActionResult> CorrigirSolicitacaoComErroNoHistorico(CancellationToken cancellationToken)
    {
        var solicitacoes = await _mongoReadOnlyConnection
            .AggregateAsync<Entities.Entities.Solicitacao>(SolicitacaoPipeline.SolicitacoesComErroListChatPipeline(), cancellationToken)
            .ConfigureAwait(false);
        if (solicitacoes == null)
        {
            return Ok(new
            {
                total = 0,
                mensagem = "Nenhuma solicitacao com erro encontrada"
            });
            
        }
        
        foreach (var solicitacao in solicitacoes)
        {
            solicitacao.CorrigirListaDeMensagemDoChat();
        }
        
        await _solicitacaoRepository.BulkUpdateAsync(solicitacoes, cancellationToken);
        return Ok(new
        {
            total = solicitacoes.Count(), 
            mensagem = "Solicitacoes corrigidas com sucesso"
        });
    }
    
    [HttpDelete("remover-solicitacao-presas-firebase")]
    public async Task<IActionResult> RemoverSolicitacaoPresasFirebase(CancellationToken cancellationToken)
    {
        var listStatusFinalizados = new List<StatusDeSolicitacao>
        {
            StatusDeSolicitacao.Cancelada, 
            StatusDeSolicitacao.Finalizada
        };
        
        var solicitacaoesFirebase = await _collection
            .OnceSingleAsync<Dictionary<string,SolicitacaoFirebaseDto>>()
            .WaitAsync(cancellationToken)
            .ConfigureAwait(false);
        if (solicitacaoesFirebase == null)
        {
            return Ok(new
            {
                total = 0, 
                solicitacoes = new List<Entities.Entities.Solicitacao>()
            });
        }
        var solicitacoesId = solicitacaoesFirebase.Keys;
        var solicitacoes = await _solicitacaoRepository.GetAsync( 
            s => listStatusFinalizados.Contains(s.Status) && solicitacoesId.Contains(s.Id), cancellationToken
        );
        var solicitacoesParaRemover = solicitacoes.Select(s => s.Id).ToList();
        await _solicitacaoFirebaseRepository.RemoverSolicitacoesAsync(solicitacoesParaRemover, cancellationToken);
        return Ok(new
        {
            total = solicitacoes.Count, 
            solicitacoes
        });
    }
}