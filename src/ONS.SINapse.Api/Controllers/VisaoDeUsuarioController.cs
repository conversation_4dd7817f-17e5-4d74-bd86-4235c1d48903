using System.Net.Mime;
using Microsoft.AspNetCore.Mvc;
using ONS.SINapse.Business.IBusiness;
using ONS.SINapse.Business.Imp.Claims;
using ONS.SINapse.Shared.Constants;
using ONS.SINapse.Shared.DTO;

namespace ONS.SINapse.Api.Controllers;

[ApiController]
[Route("api/visao-usuario")]
[UserClaimRequirement(Operacoes.GerenciarVisaoUsuario)]
public class VisaoDeUsuarioController : ControllerBase
{
    private readonly IVisaoDeUsuarioBusiness _visaoDeUsuarioBusiness;

    public VisaoDeUsuarioController(IVisaoDeUsuarioBusiness visaoDeUsuarioBusiness)
    {
        _visaoDeUsuarioBusiness = visaoDeUsuarioBusiness;
    }

    [HttpPost]
    [Consumes(MediaTypeNames.Application.Json)]
    [UserClaimRequirement(Operacoes.GerenciarVisaoUsuario)]
    public async Task<IActionResult> PostAsync([FromBody] VisaoDeUsuarioDto dto, CancellationToken cancellationToken)
        => Ok(await _visaoDeUsuarioBusiness.AddAsync(dto, cancellationToken));
    
    [HttpPut("{centroAgenteAtual}/{id}")]
    [Consumes(MediaTypeNames.Application.Json)]
    [UserClaimRequirement(Operacoes.GerenciarVisaoUsuario)]
    public async Task<IActionResult> PutAsync([FromRoute] string centroAgenteAtual, [FromRoute] string id, [FromBody] VisaoDeUsuarioDto dto, CancellationToken cancellationToken)
        => Ok(await _visaoDeUsuarioBusiness.UpdateAsync(id, centroAgenteAtual, dto, cancellationToken));
    
    [HttpDelete("{codigoCentroAgente}/{id}")]
    [Consumes(MediaTypeNames.Application.Json)]
    [UserClaimRequirement(Operacoes.GerenciarVisaoUsuario)]
    public async Task<IActionResult> DeleteAsync([FromRoute] string codigoCentroAgente, [FromRoute] string id, CancellationToken cancellationToken)
    {
        await _visaoDeUsuarioBusiness.DeleteAsync(id, codigoCentroAgente, cancellationToken);
        return NoContent();
    }

    [HttpGet("{codigoCentroAgente}")]
    [Consumes(MediaTypeNames.Application.Json)]
    [UserClaimRequirement(Operacoes.GerenciarVisaoUsuario)]
    public async Task<IActionResult> ExistAsync([FromRoute] string codigoCentroAgente, [FromQuery] string nome, CancellationToken cancellationToken)
    {
        var visoes = await _visaoDeUsuarioBusiness.VisoesEmUsoAsync(nome, codigoCentroAgente, cancellationToken)
            .ConfigureAwait(false);
        
        return Ok(visoes.Any());
    }
}