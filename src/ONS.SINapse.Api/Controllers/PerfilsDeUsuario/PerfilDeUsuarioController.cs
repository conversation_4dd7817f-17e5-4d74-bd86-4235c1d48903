using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ONS.SINapse.Business.IBusiness;
using ONS.SINapse.Business.Imp.Claims;
using ONS.SINapse.Shared.Constants;
using ONS.SINapse.Shared.DTO;
using System.Net.Mime;

namespace ONS.SINapse.Api.Controllers.PerfilsDeUsuario;

[ApiController]
[Route("api/usuario/perfil")]
[Authorize]
public class PerfilDeUsuarioController : ControllerBase
{
    private readonly IPerfilDeUsuarioBusiness _perfil;

    public PerfilDeUsuarioController(
        IPerfilDeUsuarioBusiness perfil)
    {
        _perfil = perfil;
    }

    [HttpPut("visoes")]
    [Consumes(MediaTypeNames.Application.Json)]
    [UserClaimRequirement(Operacoes.GerenciarVisaoUsuario)]
    public async Task<IActionResult> AlterarVisoes([FromBody] ICollection<SelecaoDeVisaoDeUsuarioDto> visoes, CancellationToken cancellationToken)
    {
        var result = await _perfil.AlterarVisoesAsync(visoes, cancellationToken);
        return Ok(result);
    }
    
    [HttpGet]
    [Consumes(MediaTypeNames.Application.Json)]
    public async Task<IActionResult> Perfil(CancellationToken cancellationToken)
    {
        return Ok(await _perfil.ObterPerfilAsync(cancellationToken));
    }
}
