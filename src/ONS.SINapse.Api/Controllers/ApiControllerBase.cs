using Microsoft.AspNetCore.Mvc;
using ONS.SINapse.Shared.DTO;
using System.Net;


namespace ONS.SINapse.Api.Controllers;

public abstract class ApiControllerBase : ControllerBase
{
    protected IActionResult HandleResult<TData>(TData data) where TData : class
    {
        var statusCode = (int)HttpStatusCode.OK;
        var response = new BaseResponse<TData>(data, true, statusCode);

        return Ok(response);
    }
}