using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using ONS.SINapse.Shared.Settings;

namespace ONS.SINapse.Api.Controllers;

[ApiController]
[Route("api/dados-test")]
public class DadosTestController : ControllerBase
{
    private readonly DataSyncSettings _dataSyncSettings;
    private readonly ILogger<DadosTestController> _logger;

    public DadosTestController(IOptions<DataSyncSettings> dataSyncSettings, ILogger<DadosTestController> logger)
    {
        _dataSyncSettings = dataSyncSettings.Value;
        _logger = logger;
    }


    [HttpGet]
    public async Task<IActionResult> Get([FromQuery] int? timeout, CancellationToken cancellationToken)
    {
        var requestTimeout = timeout ?? 15;
        var apiUrl = _dataSyncSettings.ServiceUri;
        _logger.LogInformation("URL Api de Dados: {ApiUrl}", apiUrl);

        try
        {
            using var httpClientHandler = new HttpClientHandler();
            httpClientHandler.ServerCertificateCustomValidationCallback =
                (_, cert, _, _) => cert != null;
            httpClientHandler.AllowAutoRedirect = true;

            using var httpClient = new HttpClient(httpClientHandler);
            httpClient.BaseAddress = new Uri(apiUrl);
            httpClient.Timeout = TimeSpan.FromSeconds(requestTimeout);

            _logger.LogInformation("Sending request to api/Test");
            var response = await httpClient.GetAsync("/api/Test", cancellationToken);

            if (!response.IsSuccessStatusCode)
            {
                _logger.LogError("Request failed with status code {StatusCode}", response.StatusCode);
                return StatusCode((int)response.StatusCode,
                    await response.Content.ReadAsStringAsync(cancellationToken));
            }

            var content = await response.Content.ReadAsStringAsync(cancellationToken);
            _logger.LogInformation("Request succeeded.");
            return Ok(content);
        }
        catch (TaskCanceledException ex) when (!cancellationToken.IsCancellationRequested)
        {
            _logger.LogError(ex, "Request timed out.");
            return StatusCode(408, $"Request timed out. Timeout is set to {requestTimeout} seconds.");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "An error occurred while making the request.");
            return StatusCode(500, $"An error occurred while processing the request. {ex.Message}");
        }
    }
}