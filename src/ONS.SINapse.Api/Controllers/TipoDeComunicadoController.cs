using Microsoft.AspNetCore.Mvc;
using ONS.SINapse.Shared.Identity;
using ONS.SINapse.Business.Imp.Claims;
using ONS.SINapse.Shared.Constants;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Enums;
using ONS.SINapse.Shared.Helpers;

namespace ONS.SINapse.Api.Controllers;

[ApiController]
[Route("api/tipos-comunicado")]
[UserClaimRequirement(Operacoes.ConsultarComunicado)]
public class TipoDeComunicadoController: ControllerBase
{
    [HttpGet]
    public Task<IActionResult> Get()
    {
        var tipos = EnumHelper
            .ToDictionary<TipoDeComunicado>()
            .Select(t => new TipoDeComunicadoDto(
                t.Key,
                t.Value
            ));

        return Task.FromResult<IActionResult>(Ok(tipos));
    }
}