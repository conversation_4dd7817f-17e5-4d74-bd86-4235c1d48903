using System.Net.Mime;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ONS.SINapse.CacheSync.Services;
using ONS.SINapse.Shared.DTO;

namespace ONS.SINapse.Api.Controllers.Datasets;

[Route("api/equipamentos/dataset")]
[Authorize]
public class EquipamentoDatasetController : ControllerBase
{
    private readonly ISinapseDadosDatasetQueryService _sinapseDadosDatasetQueryService;
    
    public EquipamentoDatasetController(ISinapseDadosDatasetQueryService sinapseDadosDatasetQueryService)
    {
        _sinapseDadosDatasetQueryService = sinapseDadosDatasetQueryService;
    }
    
    [HttpGet("transformador")]
    [Consumes(MediaTypeNames.Application.Json)]
    public async Task<IActionResult> GetTransformadoresDataset([FromQuery] string codigoDoCentro, CancellationToken cancellationToken)
    {
        var dados = await _sinapseDadosDatasetQueryService
            .GetDatasetAsync(x => x.GetEquipamentoDatasetTransformador(codigoDoCentro, cancellationToken), 
                cancellationToken);
        
        return Ok(dados ?? Enumerable.Empty<DatasetItemDto>());
    }

    [HttpGet("transformacao")]
    [Consumes(MediaTypeNames.Application.Json)]
    public async Task<IActionResult> GetTransformacaoDataset([FromQuery] string codigoDoCentro, CancellationToken cancellationToken)
    {
        var dados = await _sinapseDadosDatasetQueryService
            .GetDatasetAsync(x => x.GetEquipamentoDatasetTransformacao(codigoDoCentro, cancellationToken), 
                cancellationToken);
        
        return Ok(dados ?? Enumerable.Empty<DatasetItemDto>());
    }

    [HttpGet("compensador")]
    [Consumes(MediaTypeNames.Application.Json)]
    public async Task<IActionResult> CompensadoresDataset([FromQuery] string codigoDoCentro, CancellationToken cancellationToken)
    {
        var dados =
            await _sinapseDadosDatasetQueryService
                .GetDatasetAsync(x => 
                        x.GetEquipamentoDatasetCompensador(codigoDoCentro, cancellationToken),
                    cancellationToken);
        
        return Ok(dados ?? Enumerable.Empty<DatasetItemDto>());
    }


    [HttpGet("instalacao-compensador")]
    [Consumes(MediaTypeNames.Application.Json)]
    public async Task<IActionResult> InstalacaoCompensadoresDataset([FromQuery] string codigoDoCentro, CancellationToken cancellationToken)
    {
        var dados =
            await _sinapseDadosDatasetQueryService
                .GetDatasetAsync(x => 
                        x.GetEquipamentoDatasetInstalacaoCompensador(codigoDoCentro, cancellationToken),
                    cancellationToken);
        
        return Ok(dados ?? Enumerable.Empty<DatasetItemDto>());
    }

    [HttpGet("banco-capacitor")]
    [Consumes(MediaTypeNames.Application.Json)]
    public async Task<IActionResult> BancoDeCapacitorDataset([FromQuery] string codigoDoCentro, CancellationToken cancellationToken)
    {
        var dados = 
            await _sinapseDadosDatasetQueryService
                .GetDatasetAsync(x => 
                        x.GetEquipamentoDatasetBancoDeCapacitor(codigoDoCentro, cancellationToken),
                    cancellationToken);
        
        return Ok(dados ?? Enumerable.Empty<DatasetItemDto>());
    }

    [HttpGet("reator")]
    [Consumes(MediaTypeNames.Application.Json)]
    public async Task<IActionResult> ReatorDataset([FromQuery] string codigoDoCentro, CancellationToken cancellationToken)
    {
        var dados = 
            await _sinapseDadosDatasetQueryService
                .GetDatasetAsync(x => 
                        x.GetEquipamentoDatasetReator(codigoDoCentro, cancellationToken),
                    cancellationToken);
        
        return Ok(dados ?? Enumerable.Empty<DatasetItemDto>());
    }

    [HttpGet("elo")]
    [Consumes(MediaTypeNames.Application.Json)]
    public async Task<IActionResult> EloDataset([FromQuery] string codigoDoCentro, CancellationToken cancellationToken)
    {
        var dados =
            await _sinapseDadosDatasetQueryService
                .GetDatasetAsync(x =>
                        x.GetEquipamentoDatasetElo(codigoDoCentro, cancellationToken),
                    cancellationToken);

        return Ok(dados ?? Enumerable.Empty<DatasetItemDto>());
    }
}
