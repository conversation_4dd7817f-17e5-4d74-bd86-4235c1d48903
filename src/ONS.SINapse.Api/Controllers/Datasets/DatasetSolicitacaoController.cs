using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ONS.SINapse.CacheSync.Services;
using ONS.SINapse.Shared.DTO;
using System.Net.Mime;

namespace ONS.SINapse.Api.Controllers.Datasets;

[Route("api/dataset/solicitacao")]
[Authorize]
public class DatasetSolicitacaoController : ControllerBase
{
    private readonly ISinapseDadosDatasetQueryService _sinapseDadosDatasetQueryService;
    
    public DatasetSolicitacaoController(ISinapseDadosDatasetQueryService sinapseDadosDatasetQueryService)
    {
        _sinapseDadosDatasetQueryService = sinapseDadosDatasetQueryService;
    }

    [HttpGet("{view}")]
    [Consumes(MediaTypeNames.Application.Json)]
    public async Task<IActionResult> Get(string view, [FromQuery] string? query, CancellationToken cancellationToken)
    {
        var dados = await _sinapseDadosDatasetQueryService
            .GetDatasetAsync(x => x.GetSolicitacaoDataset(view, query, cancellationToken), 
                cancellationToken);

        return Ok(dados ?? Enumerable.Empty<DatasetItemDto>());
    }
}
