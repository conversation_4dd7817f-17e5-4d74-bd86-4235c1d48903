using System.Net.Mime;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ONS.SINapse.CacheSync.Services;
using ONS.SINapse.Shared.DTO;

namespace ONS.SINapse.Api.Controllers.Datasets;

[ApiController]
[Route("api/agentes/dataset")]
[Authorize]
public class AgenteDatasetController : ControllerBase
{
    private readonly ISinapseDadosDatasetQueryService _dadosDatasetQueryService;
    
    public AgenteDatasetController(ISinapseDadosDatasetQueryService dadosDatasetQueryService)
    {
        _dadosDatasetQueryService = dadosDatasetQueryService;
    }
    
    [HttpGet]
    [Consumes(MediaTypeNames.Application.Json)]
    public async Task<IActionResult> GetByCodigoCentro([FromQuery]string codigoDoCentro, CancellationToken cancellationToken)
    {
        var dados = await _dadosDatasetQueryService
            .GetDatasetAsync(x => x.GetAgenteDataset(codigoDoCentro, cancellationToken),
                cancellationToken);
        
        return Ok(dados ?? Enumerable.Empty<DatasetItemDto>());
    }
}