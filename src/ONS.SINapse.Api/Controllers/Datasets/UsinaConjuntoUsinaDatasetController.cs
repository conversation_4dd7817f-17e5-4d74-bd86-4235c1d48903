using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ONS.SINapse.CacheSync.Services;
using ONS.SINapse.Shared.DTO;

namespace ONS.SINapse.Api.Controllers.Datasets;

[Route("api/usinas/dataset")]
[Authorize]
public class UsinaConjuntoUsinaDatasetController : ControllerBase
{
    private readonly ISinapseDadosDatasetQueryService _dadosDatasetQueryService;
    
    public UsinaConjuntoUsinaDatasetController(ISinapseDadosDatasetQueryService dadosDatasetQueryService)
    {
        _dadosDatasetQueryService = dadosDatasetQueryService;
    }
    
    [HttpGet("unidades-geradoras")]
    public async Task<IActionResult> UgeAsync([FromQuery] string codigoDoCentro, CancellationToken cancellationToken)
    {
        var dados = 
            await _dadosDatasetQueryService
                .GetDatasetAsync(x => 
                        x.GetUsinaConjuntoUsinaDatasetUnidadesGeradoras(codigoDoCentro, cancellationToken),
                    cancellationToken);
        
        return Ok(dados ?? Enumerable.Empty<DatasetItemDto>());
    }
    
    [HttpGet("cag")]
    public async Task<IActionResult> CagAsync([FromQuery] string codigoDoCentro, [FromQuery] string[]? codigoDoTipoDeFonte, CancellationToken cancellationToken)
    {
        var dados = 
            await _dadosDatasetQueryService
                .GetDatasetAsync(x => 
                        x.GetUsinaConjuntoUsinaDatasetCag(codigoDoCentro, codigoDoTipoDeFonte, cancellationToken),
                    cancellationToken);
        
        return Ok(dados ?? Enumerable.Empty<DatasetItemDto>());
    }
    
    [HttpGet("usina")]
    public async Task<IActionResult> UsinasAsync([FromQuery] string codigoDoCentro, [FromQuery] string[]? codigoDoTipoDeFonte, CancellationToken cancellationToken)
    {
        var dados = 
            await _dadosDatasetQueryService
                .GetDatasetAsync(x => 
                        x.GetUsinaConjuntoUsinaDatasetUsina(codigoDoCentro, codigoDoTipoDeFonte, cancellationToken),
                    cancellationToken);
        
        return Ok(dados ?? Enumerable.Empty<DatasetItemDto>());
    }
    
    [HttpGet("conjunto-usina")]
    public async Task<IActionResult> ConjuntosUsinaAsync([FromQuery] string codigoDoCentro, [FromQuery] string? codigoDoTipoDeFonte, CancellationToken cancellationToken)
    {
        var dados = 
            await _dadosDatasetQueryService
                .GetDatasetAsync(x => 
                        x.GetUsinaConjuntoUsinaDatasetConjuntoUsina(codigoDoCentro, codigoDoTipoDeFonte, cancellationToken),
                    cancellationToken);
        
        return Ok(dados ?? Enumerable.Empty<DatasetItemDto>());
    }
}