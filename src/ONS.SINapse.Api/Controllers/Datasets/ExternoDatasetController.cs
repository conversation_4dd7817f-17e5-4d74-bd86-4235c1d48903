using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ONS.SINapse.CacheSync.Services;

namespace ONS.SINapse.Api.Controllers.Datasets;

[ApiController]
[Route("api/consulta-externa/dataset")]
[Authorize]
public class ExternoDatasetController : ControllerBase
{
    private readonly IExternoDadosDatasetService _dadosDatasetService;

    public ExternoDatasetController(IExternoDadosDatasetService dadosDatasetService)
    {
        _dadosDatasetService = dadosDatasetService;
    }
    
    [HttpGet]
    public async Task<IActionResult> Dataset([FromQuery] string url, CancellationToken cancellationToken)
    {
        var dados = await _dadosDatasetService.DatasetAsync(url, cancellationToken);
        return Ok(dados);
    }
}


