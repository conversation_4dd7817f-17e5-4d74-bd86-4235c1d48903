using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ONS.SINapse.CacheSync.Services;
using ONS.SINapse.Shared.DTO;

namespace ONS.SINapse.Api.Controllers.Datasets;


[ApiController]
[Route("api/horarios/dataset")]
[Authorize]
public class HorarioPatamarDatasetController : ControllerBase
{
    private readonly ISinapseDadosDatasetQueryService _dadosDatasetQueryService;
    
    public HorarioPatamarDatasetController(ISinapseDadosDatasetQueryService dadosDatasetQueryService)
    {
        _dadosDatasetQueryService = dadosDatasetQueryService;
    }
    
    [HttpGet]
    public async Task<IActionResult> GetDataset(CancellationToken cancellationToken)
    {
        var dados = 
            await _dadosDatasetQueryService
                .GetDatasetAsync(x => 
                        x.GetHorarioPatamarDataset(cancellationToken),
                    cancellationToken);
                
        return Ok(dados ?? Enumerable.Empty<DatasetItemDto>());
    }
}