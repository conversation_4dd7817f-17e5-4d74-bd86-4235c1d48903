using Microsoft.AspNetCore.Mvc;
using ONS.SINapse.Business.IBusiness;
using System.Net.Mime;

namespace ONS.SINapse.Api.Controllers;

[ApiController]
[Route("api/configuracao-sistema")]
public class ConfiguracaoDeSistemaController : ControllerBase
{
    private readonly IConfiguracaoDeSistemaBusiness _configuracaoDeSistemaBusiness;

    public ConfiguracaoDeSistemaController(
        IConfiguracaoDeSistemaBusiness configuracaoDeSistemaBusiness)
    {
        _configuracaoDeSistemaBusiness = configuracaoDeSistemaBusiness;
    }

    [HttpGet]
    [Consumes(MediaTypeNames.Application.Json)]
    public IActionResult Get()
        => Ok(_configuracaoDeSistemaBusiness.Get());
}
