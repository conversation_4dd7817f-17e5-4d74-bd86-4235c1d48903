using Microsoft.AspNetCore.Mvc;
using ONS.SINapse.Business.IBusiness;
using ONS.SINapse.Business.Imp.Claims;
using ONS.SINapse.Shared.Constants;
using ONS.SINapse.Shared.DTO;

namespace ONS.SINapse.Api.Controllers
{
    [ApiController]
    [Route("api/versao")]
    [UserClaimRequirement(Operacoes.AlterarVersaoSistema)]
    public class VersaoController : ControllerBase
    {
        private readonly IAlterarVersaoBusiness _alterarVersaoBusiness;
        public VersaoController(IAlterarVersaoBusiness alterarVersaoBusiness)
        {
            _alterarVersaoBusiness = alterarVersaoBusiness;
        }

        [HttpPost]
        public async Task<IActionResult> Adicionar([FromBody] AdicionarVersaoDto adicionarVersao, CancellationToken cancellationToken)
        {
            await _alterarVersaoBusiness.Adicionar(adicionarVersao, cancellationToken);
            return Ok(new { });
        }

        [HttpPut]
        public async Task<IActionResult> Editar([FromBody] EditarVersaoDto editarVersao, CancellationToken cancellationToken)
        {
            await _alterarVersaoBusiness.Editar(editarVersao, cancellationToken);
            return Ok(new { });
        }
    }
}
