{"ApplicationSettings": {"ServerBaseUrl": "https://api.devsinapse.ons.org.br/", "ServerLocalBaseUrl": "http://localhost", "HealthcheckSettings": [{"EndpointName": "SINapse", "EndpointUrl": "http://localhost:6070/health"}, {"EndpointName": "Integração SINapse", "EndpointUrl": "http://localhost:5283/health"}, {"EndpointName": "SINapse Api de Dados", "EndpointUrl": "https://localhost:7074/health"}]}, "MongoDbSettings": {"ConnectionString": "************************************************", "DatabaseName": "sinapse", "MaxConnections": 50, "MaxConnectionIdleTimeInSeconds": 30}, "CacheSettings": {"ExpiracaoDeFonteEmMinutos": 60, "ExpiracaoDeMotivoEmMinutos": 60, "ExpiracaoDeOperacaoEmMinutos": 60}, "FirebaseSettings": {"BasePath": "https://ons-sinapse-default-rtdb.firebaseio.com/", "Base64ConfigJson": "****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "ArquivoConfigJson": "firebase-keys.json", "QuantidadeDeTentativas": 3, "SegundosEntreTentativas": 1, "TokenWrite": "C93C72CD-F54D-4263-AB1B-86ABDBF5842D", "TokenRead": "47B6CAF7-57E6-4D29-A439-3D46B4F2E49D", "TokenReadWrite": "12CDBE0E-093E-42B2-8EB2-5B22BA1E0517"}, "KafkaSettings": {"Ativo": false, "QuantidadeDeWorkersPorInstancia": 10, "SaslUsername": "_servicedsagerapura<PERSON>", "SaslPassword": "8wUxZtaRtVYp", "ConsumerName": "ONS.SINAPSE", "RequireAuth": false, "BootstrapServers": "localhost:9092", "SaslMechanism": "Plain", "SecurityProtocol": "SaslSsl", "SslCaPem": "-----<PERSON><PERSON><PERSON> CERTIFICATE-----\nMIIEgDCCAuigAwIBAgIJAIDHW9Pdn3SIMA0GCSqGSIb3DQEBCwUAME0xCzAJBgNV\nBAYTAlVTMQswCQYDVQQIDAJDQTExMC8GA1UEAwwoU0NNIExvY2FsIENBIG9uIHRz\ndC1rYWZrYS0wMDEub25zLm9yZy5icjAeFw0yMzAxMDMyMDAwMTBaFw0yODAxMDIy\nMzU5NTlaME0xCzAJBgNVBAYTAlVTMQswCQYDVQQIDAJDQTExMC8GA1UEAwwoU0NN\nIExvY2FsIENBIG9uIHRzdC1rYWZrYS0wMDEub25zLm9yZy5icjCCAaIwDQYJKoZI\nhvcNAQEBBQADggGPADCCAYoCggGBAM13JN7er3/Mzl7OnrnzYmx2k2ADxOifPLM1\nih3nh00pvrSJRMx7dYzrzwcy8Zq7yfEVLTJvUERw4d06egVdKYSZIRHa8xERhw4d\nkbVlBGz+w0zmT69zWWgaa71WssL9HDHsC5M+wWY0D9Js/8h4jM9pk9kzM0EnQ/xz\nda19ypY6D8BlcuJYRPxChnIXawfYmChES2ODr+AMzB14det2Jqf40fdcr2h7zTUo\nSMkjuCy1rTNfOFYPBPdQalTr6FKqs+gxBOHWPy6359TX5a06bVuIh8Ld3lct7fhQ\nYVBl7iQ4cQn/VMwOE5Asqb8DigTilwKsuHu7Jfp9QzcnjZC0I7fURJDWNp8jcJrp\n+2dB2HZjAfVUAZOKXAYY0fLCbtCOVLunfjYrLIIx/+/fu3BANnj2+npzK/RWaOny\nfwSCpa5Rl+eKExNwba8Yum4Ja+LHGIL3Hyq1NzKy2kG+rOAwNDb/vmep57AxXgg2\niKLB38XDBoODuoRGgGJrXNjUuMu68QIDAQABo2MwYTAPBgNVHRMBAf8EBTADAQH/\nMB0GA1UdDgQWBBQzqXe+x+A0pcQOcFKgknwEFRivrDAfBgNVHSMEGDAWgBQzqXe+\nx+A0pcQOcFKgknwEFRivrDAOBgNVHQ8BAf8EBAMCAgQwDQYJKoZIhvcNAQELBQAD\nggGBAB/Lv6+XzPdWnuKyfZ5Ds4apZWHk9IbId6/wj1q0OwG3bPhw0eRkeSXi/I5j\nrxAiYzEZ1CNCE+flKItChKSKjtzIRBpDI2ClxKFCxWoIU3BrNGKmF35v8ePDZxgk\n0i5ccEaO9Mm4UKvzMoFidPEzXAjBjknrTxa86ynXShHBZkZGO0U856jTcsqAi+Vn\n2QzDwYISijldfAt8QBaI25cIkvgdwS+stXhcj39VLOio3c0Yzy0vRimXzURWbWwj\nubZgdseG57lQns2IO1TQSqLC6VgM3uVuvTitJSH4rSGx54adzl/zi0KRt0WrWqVt\nf41yKn1pMdqcaBB+3oJXOMys95Oww73A29xEh5sbuNx3LwIZ3S92f9vIGtCKdOtJ\nj9gQaIWSlUTIraTrkcdAaQwv2YC4WZ0rNLag2vseZsT12ZRFn0JgBWmTm1jNP97u\n7fU7ZYwjcmQ/wcZM3XdVDTk9M2vnNOcu/W7hz7upMlLHYDj2H7aCldhFUEkgyufN\nL7eSmg==\n-----END CERTIFICATE-----", "SslKeystorePassword": "XhE0hbbjoVjZTLuDDNF1jEuJB1tU7C7meoi20d4xrcW"}, "KafkaTopicsSettings": {"CadastroDeSolicitacao": "ONS.SINAPSE.CADASTRO_SOLICITACAO", "TrocaDeStatusDeSolicitacao": "ONS.SINAPSE.TROCA_STATUS_SOLICITACAO.GERDIN", "ConsultaDeStatusDeSolicitacao": "ONS.SINAPSE.STATUS_SOLICITACAO", "StatusAgente": "ONS.SINAPSE.STATUS_AGENTE", "Health": "ONS.SINAPSE.HEALTHCHECK"}, "KafkaConsumersSettings": {"CadastroDeSolicitacaoConsumerProvider": 3, "TrocaDeStatusConsumerProvider": 3}, "SqsQueueSettings": {"ExtracaoDeSolicitacao": "ONS-SINAPSE-EXTRACAO-DE-DADOS-LOCAL", "EnviarSolicitacaoParaMongo": "ONS-SINAPSE-ENVIAR-SOLICITACAO-PARA-MONGO-LOCAL", "RemoverSolicitacaoDoFirebase": "ONS-SINAPSE-REMOVER-SOLICITACAO-DO-FIREBASE-LOCAL", "FinalizarSolicitacaoAutomaticamente": "ONS-SINAPSE-SOLICITACAO-FINALIZACAO-AUTO-LOCAL"}, "ONS": {"Authorization": {"Issuer": "https://popons.amcom.com.br/ons.pop.federation/", "Audience": "SINAPSE", "UseRsa": "true", "RsaModulus": "wSxNKSkhfB1XR+fD/KZxK5nLEEHHBNrbSpiNw9FtcJHkvOiBXWI+G43Y1rvp6zp2/sjEqiXbQlFuMf2d/hM9ScIrdtrykf3m0OpDvhACFgwvvdiIaWOqIZ9oJCS9uzgEq7OGwH4gQklIOUbVrjZftXc0qFRR3XwkwGPGaNLsVpzSMeJHDJJReJe4MtztgsBS//AzkSdbhBpcAwQYOdmeQZTxL76miZqIHqAWAGQZgh/y3kHdfayhMb/hSgay933ITWyV2V7TUMBByYm6MOLuSRWTuloVIiwA/Nap5tgrQFdCuc34GCNgQIocn8qbcICI21AebnbyEyo96sONodToEQ==", "RsaPublicExponent": "AQAB"}, "PopAuth": {"Origin": "http://local.amcom.com.br", "TokenURL": "https://popons.amcom.com.br/ons.pop.federation/oauth2/token", "clientId": "SINAPSE", "username": "amcom\\popons", "password": "14N@Ng80qlZqlnvLlrQ", "grantTypeRefreshToken": "refresh_token", "grantTypeAccessToken": "password"}}, "ConfiguracaoDeSistemaSettings": {"SegundosEntreNotificacoesPendentes": 30, "QuantidadeDeDiasLimiteParaConsultaDoHistoricoDeSolicitacoes": 30, "Sons": {"Sucesso": "https://devsinapse.ons.org.br/assets/sons/sucesso.mp3", "Erro": "https://devsinapse.ons.org.br/assets/sons/erro.mp3", "Alerta": "https://devsinapse.ons.org.br/assets/sons/alerta.mp3", "Broadcast": "https://devsinapse.ons.org.br/assets/sons/broadcast.mp3"}}, "VerificacaoDeGeolocalizacaoSettings": {"RaioDeAtuacaoDoCentroDoAgenteEmKm": 30, "ValidarGeolocalizacaoPorPadrao": false}, "TemplatesSettings": {"DiretorioDosTemplates": "templates", "NomeDoArquivoDeDicionario": "dicionario.json"}, "ConfiguracaoDeNotificacaoSettings": {"Imagem": null, "Icone": "assets/icon/favicon.ico", "TempoExpiracaoEmSegundos": 59, "Sufixo": null, "Prefixo": "SINapse -"}, "UsuarioDoSistemaSettings": {"Login": "sinapse", "Nome": "Sistema", "Sid": "12331ffdsfs4345"}, "Serilog": {"Using": ["Serilog.Sinks.Console"], "MinimumLevel": {"Default": "Debug", "Override": {"Microsoft": "Warning", "System": "Warning", "Microsoft.Extensions.Diagnostics.HealthChecks.DefaultHealthCheckService": "Fatal", "HealthChecks.UI.Core.Notifications.WebHookFailureNotifier": "Fatal", "HealthChecks.UI.Core.HostedService.HealthCheckReportCollector": "Fatal", "Serilog.AspNetCore.RequestLoggingMiddleware": "Information", "MassTransit": "Error"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {Message:lj} ({Properties}){Exception}{NewLine}"}}], "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId"]}, "AllowedHosts": "*", "XTokenRequirementSettings": {"LambdaAws": "CE582113-5204-46D9-9DD8-C6F35CB5AE06", "KongOns": "0B93888F-F67A-4DA0-948A-93A73D41E8FB"}, "DataSyncSettings": {"SyncWaitTimeInMinutes": 5, "CustomHeaders": {"Accept": "application/json", "Content-Type": "application/json", "User-Agent": "ONS.SINapse.Api"}, "ServiceUri": "https://localhost:7074", "DatasetCacheExpiration": 60, "Authentication": {"ServiceUri": "https://popons.amcom.com.br/ons.pop.federation/oauth2/token", "ApplicationCacheExpiration": 300, "ApplicationName": "SINAPSE", "ApplicationOrigin": "http://local.amcom.com.br", "Username": "amcom\\popons", "Password": "14N@Ng80qlZqlnvLlrQ"}}, "ComunicadoSettings": {"TempoDeVidaDeComunicadoEmDias": 30}, "ControleDeChamadaSettings": {"TempoDuracaoEmSegundos": 120, "TempoEntreTarefas": 30}, "OpenTelemetrySettings": {"ServiceVersion": "1.0.0", "ServiceEndpoint": "http://localhost:4318", "ServiceEnabled": false}}