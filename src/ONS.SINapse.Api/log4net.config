<?xml version="1.0" encoding="utf-8" ?>
<log4net>
  <appender name="AsyncRollingFileAppender" type="ONS.Core.Log.Log4net.AsyncAppender, ONS.Core.Log.Log4net">
    <appender-ref ref="NetTrace" />
    <appender-ref ref="NetConsole" />
    <appender-ref ref="NetFileAppender" />
  </appender>
  <appender name="NetFileAppender" type="log4net.Appender.RollingFileAppender">
    <param name="File" value="/tmp/log/ONS.SINapse.Api.log" />
    <rollingStyle value="Size" />
    <maxSizeRollBackups value="10" />
    <maximumFileSize value="8MB" />
    <lockingModel type="log4net.Appender.FileAppender+MinimalLock" />
    <layout type="log4net.Layout.PatternLayout">
      <param name="ConversionPattern" value="CONFIG ROLLING [%d][%t] %-5p [%C.%M] - %m%n" />
    </layout>
  </appender>
  <appender name="NetConsole" type="log4net.Appender.ConsoleAppender">
    <param name="Threshold" value="INFO" />
    <layout type="log4net.Layout.PatternLayout">
      <param name="ConversionPattern" value="CONFIG CONSOLE [%d][%t] %-5p [%C.%M] - %m%n" />
    </layout>
  </appender>
  <appender name="NetTrace" type="log4net.Appender.TraceAppender">
    <layout type="log4net.Layout.PatternLayout">
      <conversionPattern value="CONFIG TRACE [%d][%t] %-5p [%C.%M] - %m%n" />
    </layout>
  </appender>
  <root>
    <level value="DEBUG" />
    <appender-ref ref="AsyncRollingFileAppender" />
  </root>
</log4net>