{"RuleOperations": [{"Nome": "API Integração", "Operations": ["IntegracaoAgente", "IntegracaoConsultarColecaoDados", "IntegracaoConsultarDataset", "IntegracaoCriarSolicitacao"]}, {"Nome": "Administrador", "Operations": ["LogarNoSinapse", "GerenciarTags", "AlterarVersaoSistema", "DownloadViews"]}, {"Nome": "Apurador Centros", "Operations": ["ConsultaGeralDoHistorico", "ConsultarComunicado", "ConsultarHistorico", "ConsultarHistoricoAcesso", "ConsultarPainelSolicitacoesTempoReal", "LogarNoSinapse", "SelecionarMultiplosScopes"]}, {"Nome": "Apurador Agente", "Operations": ["ConsultaGeralDoHistorico", "ConsultarComunicado", "ConsultarHistorico", "ConsultarHistoricoAcesso", "ConsultarPainelSolicitacoesTempoReal", "LogarNoSinapse", "SelecionarMultiplosScopes", "ConfigurarWebhook"]}, {"Nome": "Operador Agente", "Operations": ["ConfigurarWebhook", "ConfirmarSolicitacao", "ConsultarComunicado", "ConsultarHistorico", "ConsultarPainelSolicitacoesTempoReal", "FinalizarSolicitacao", "GerenciarVisaoUsuario", "InformarImpedimentoSolicitacao", "LogarNoSinapse", "RegistrarAudiencia", "SelecionarMultiplosScopes"]}, {"Nome": "Operador Centros", "Operations": ["CancelarSolicitacao", "ConfirmarSolicitacao", "ConsultarAgentes", "ConsultarComunicado", "ConsultarHistorico", "ConsultarHistoricoAcesso", "ConsultarPainelSolicitacoesTempoReal", "ConsultarRascunhoSolicitacao", "CriarMulticast", "CriarRascunhoSolicitacao", "CriarSolicitacao", "EditarRascunhoSolicitacao", "EncaminharSolicitacao", "FinalizarSolicitacao", "GerenciarVisaoUsuario", "InformarCienciaImpedimento", "InformarImpedimentoSolicitacao", "LogarNoSinapse", "RegistrarAudiencia", "RemoverRascunhoSolicitacao"]}, {"Nome": "Operador CNOS", "Operations": ["CancelarSolicitacao", "ConsultarAgentes", "ConsultarComunicado", "ConsultarHistorico", "ConsultarHistoricoAcesso", "ConsultarPainelSolicitacoesTempoReal", "CriarBroadcast", "CriarSolicitacao", "GerenciarVisaoUsuario", "InformarCienciaImpedimento", "LogarNoSinapse", "RegistrarAudiencia", "GerenciarVisaoUsuario", "EditarRascunhoSolicitacao", "CriarRascunhoSolicitacao", "ConsultarRascunhoSolicitacao", "RemoverRascunhoSolicitacao", "ConsultarTodosAgentes"]}, {"Nome": "Sincronizador", "Operations": ["Administrar", "ExecutarSyncDone", "SincronizarBDSync"]}]}