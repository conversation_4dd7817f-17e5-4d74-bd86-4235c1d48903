<Project Sdk="Microsoft.NET.Sdk.Web">

  <!-- Propriedades específicas do projeto Web -->
  <PropertyGroup>
    <UserSecretsId>2ea36c10-b2bb-43dd-bf5f-378158fc1d19</UserSecretsId>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
  </PropertyGroup>

  <!-- Pacotes específicos do projeto -->
  <ItemGroup>
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" />
  </ItemGroup>
  
  <ItemGroup>
    <ProjectReference Include="..\ONS.SINapse.CacheSync\ONS.SINapse.CacheSync.csproj" />
    <ProjectReference Include="..\ONS.SINapse.Integracao.Shared\ONS.SINapse.Integracao.Shared.csproj" />
    <ProjectReference Include="..\ONS.SINapse.CrossCutting\ONS.SINapse.CrossCutting.csproj" />
    <ProjectReference Include="..\ONS.SINapse.Shared\ONS.SINapse.Shared.csproj" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="..\..\.dockerignore">
        <Link>.dockerignore</Link>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <Content Update="PopProvider.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
  </ItemGroup>
    <ItemGroup>
        <None Update="templates/dicionario.json">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="templates/pt_BR.aff">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="templates/pt_BR.dic">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
    </ItemGroup>
    <Target Name="CopySwaggerCustomJs" AfterTargets="Build">
        <ItemGroup>
            <SwaggerJsFiles Include="..\ONS.SINapse.Shared\Swagger\swagger-perfil-selecionado-header.js" />
        </ItemGroup>

        <Copy SourceFiles="@(SwaggerJsFiles)"
              DestinationFolder="$(ProjectDir)wwwroot\swagger"
              SkipUnchangedFiles="true" />
    </Target>
    <Target Name="CopyPermissaoUsuarioJson" AfterTargets="Build">
        <Copy
                SourceFiles="permissao-usuario.json"
                DestinationFolder="wwwroot\data"
                SkipUnchangedFiles="true" />
    </Target>
    
    
</Project>
