using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Shared.Services;

namespace ONS.SINapse.Api.Middlewares;

public class GeolocalizacaoMiddleware : IMiddleware
{
    private readonly IGeolocalizacaoService _geolocalizacaoService;

    public GeolocalizacaoMiddleware(
        IGeolocalizacaoService geolocalizacaoService)
    {
        _geolocalizacaoService = geolocalizacaoService;
    }
    
    public async Task InvokeAsync(HttpContext context, RequestDelegate next)
    {
        _geolocalizacaoService.SetCoordenadaGeograficaDoUsuario(
            context.Request.Headers.GetGeolocation());
        
        await next(context);
    }
}