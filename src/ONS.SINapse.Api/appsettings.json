{"ONSEnv": "ons.sinapse.dev", "AWS": {"Profile": "default", "Region": "us-east-1", "ResignRetries": true}, "ApplicationSettings": {"ServerBaseUrl": "https://api.devsinapse.ons.org.br/", "ServerLocalBaseUrl": "http://localhost/", "Version": "VERSION_BUILD_DEVOPS", "HealthcheckSettings": [{"EndpointName": "SINapse", "EndpointUrl": "https://api.sinapse.ons.org.br/health"}, {"EndpointName": "Integração SINapse", "EndpointUrl": "https://api.devsinapseintegracao.ons.org.br/health"}, {"EndpointName": "SINapse Api de Dados", "EndpointUrl": "https://sinapse-dados-api-tst.apps.ocpd.ons.org.br/health"}]}, "MongoDbSettings": {"DatabaseName": "sinapse", "MaxConnections": 50, "MaxConnectionIdleTimeInSeconds": 30}, "CacheSettings": {"Host": "************", "Port": "6379", "InstanceName": "sinapse", "UseRedis": false, "ExpiracaoDeFonteEmMinutos": 60, "ExpiracaoDeMotivoEmMinutos": 60, "ExpiracaoDeOperacaoEmMinutos": 60}, "FirebaseSettings": {"BasePath": "https://ons-sinapse-default-rtdb.firebaseio.com/", "Base64ConfigJson": "****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "ArquivoConfigJson": "firebase-keys.json", "QuantidadeDeTentativas": 3, "SegundosEntreTentativas": 1, "TokenWrite": "C93C72CD-F54D-4263-AB1B-86ABDBF5842D", "TokenRead": "47B6CAF7-57E6-4D29-A439-3D46B4F2E49D", "TokenReadWrite": "12CDBE0E-093E-42B2-8EB2-5B22BA1E0517"}, "KafkaSettings": {"Ativo": false, "QuantidadeDeWorkersPorInstancia": 10, "SaslUsername": "SPSAdminGIT", "ConsumerName": "ONS.SINAPSE", "RequireAuth": true, "BootstrapServers": "tst-kafka-001.ons.org.br:9093,tst-kafka-002.ons.org.br:9093,tst-kafka-003.ons.org.br:9093", "SaslMechanism": "Plain", "SecurityProtocol": "SaslSsl", "SslCaPem": "-----<PERSON><PERSON><PERSON> CERTIFICATE-----\nMIIEfzCCAuegAwIBAgIICFtsivxzZkUwDQYJKoZIhvcNAQELBQAwTTELMAkGA1UE\nBhMCVVMxCzAJBgNVBAgMAkNBMTEwLwYDVQQDDChTQ00gTG9jYWwgQ0Egb24gdHN0\nLWthZmthLTAwMS5vbnMub3JnLmJyMB4XDTI0MDEwOTIwMjcxMFoXDTI5MDEwNzIz\nNTk1OVowTTELMAkGA1UEBhMCVVMxCzAJBgNVBAgMAkNBMTEwLwYDVQQDDChTQ00g\nTG9jYWwgQ0Egb24gdHN0LWthZmthLTAwMS5vbnMub3JnLmJyMIIBojANBgkqhkiG\n9w0BAQEFAAOCAY8AMIIBigKCAYEAyFVeBKpBYCD1L8FX3BRr7cNslXYSEtTsETGf\nW/VCseLw7kl+uKJXGcpYGmLfQFoKYn4SWISQM3dFMNz40qbscHfpg07gZf2LG0Rf\nFLUtA3MUCwD3NON1YNCDHb9Q+DUVbhjneTsQW6K8Y3zBJlU7Wt2ARTBnbzDeovVJ\nXIP6U99RZ5hb/B9SRV1mOZ63a+3VEMKbqVvCcFhVin7aqku8HRF14quczBdYGewC\nXV1WyhyjJgPkVkV18iMA+yzCCW2BfwwdxpZJf2rYUS8mgnZ2foa5rZ/svmSSHIW0\ndE2L/BP31z3sTW+ugz6wTqA2ok1yuS28uWqmFve1zpKSPPnfiEuzYiRG93n9Mwp9\n6FC7GeREq7D3AHEe64YJNFZV8BY3ZiE4jOgGQiyESfa2nSS/HDR84WH3Zk88HlPm\nnv2VdNY4xd5JV1cWCiEEGy7VjggRCGCHCSmLiM+17ulhUT+Utj3LXqNAvwCMIqhd\n08vsiXksgVylk2rcYzOd4kafxX19AgMBAAGjYzBhMA8GA1UdEwEB/wQFMAMBAf8w\nHQYDVR0OBBYEFLc887rkcryFi4Awk7bv/ik/DqfHMB8GA1UdIwQYMBaAFLc887rk\ncryFi4Awk7bv/ik/DqfHMA4GA1UdDwEB/wQEAwICBDANBgkqhkiG9w0BAQsFAAOC\nAYEAbt1Hp2yEtuIGA7QW/Q5eGTI5Q9B6wKiJIlOYv94PICAxbXNRrzdnXQSXy59i\nw2JxqSxeS/S8UYwWddDlLMqocBYdfjljM034wNdck8J7mYtrDth2qMUI3VoRR16j\nP4g8nA15/utL+wDDyFimv1Ej0QSphTyAfaQGwPJnaEFR/5InWu40rJGv6Wyr4GR8\nHMqffLNQ5cHCmSLHCOBzy5Zst3W6TcV0qwEki68fBbMRl89Z0Z6iYYXXPy9vEy4s\nunDhDX+qLtBjU/WG2olWEmAyqCMn80DlVYMItmTamxMAsFBEbHpLbJGn0AolqSse\nXau8snrZPvcdBrq/Yy+TayY2+ZpZxcs+4OU0aFQ4QR34tzy/Ayn/H177jtRemXDH\nHfc2JlGUOnSEL/bYm+j4tCZVDlFeZ5ORnxsSMD32EoeR5sTBqWscjy5NkXpEeSp+\nzAilWDsjN4Kc6rLJCJAoPSRd3q0dz3l6ohFOxb9Fbbt+XnqVn6kaSCr1s3W5lGVc\ncUtW\n-----END CERTIFICATE-----"}, "KafkaTopicsSettings": {"CadastroDeSolicitacao": "ONS.SINAPSE.CADASTRO_SOLICITACAO", "TrocaDeStatusDeSolicitacao": "ONS.SINAPSE.TROCA_STATUS_SOLICITACAO.GERDIN", "ConsultaDeStatusDeSolicitacao": "ONS.SINAPSE.STATUS_SOLICITACAO", "StatusAgente": "ONS.SINAPSE.STATUS_AGENTE", "Health": "ONS.SINAPSE.HEALTHCHECK"}, "ONS": {"Authorization": {"Issuer": "https://poptst.ons.org.br/ons.pop.federation/", "Audience": "SINAPSE", "UseRsa": "true", "RsaModulus": "wSxNKSkhfB1XR+fD/KZxK5nLEEHHBNrbSpiNw9FtcJHkvOiBXWI+G43Y1rvp6zp2/sjEqiXbQlFuMf2d/hM9ScIrdtrykf3m0OpDvhACFgwvvdiIaWOqIZ9oJCS9uzgEq7OGwH4gQklIOUbVrjZftXc0qFRR3XwkwGPGaNLsVpzSMeJHDJJReJe4MtztgsBS//AzkSdbhBpcAwQYOdmeQZTxL76miZqIHqAWAGQZgh/y3kHdfayhMb/hSgay933ITWyV2V7TUMBByYm6MOLuSRWTuloVIiwA/Nap5tgrQFdCuc34GCNgQIocn8qbcICI21AebnbyEyo96sONodToEQ==", "RsaPublicExponent": "AQAB"}, "PopAuth": {"Origin": "http://local.ons.org.br", "TokenURL": "https://poptst.ons.org.br/ons.pop.federation/oauth2/token", "clientId": "SINAPSE", "username": "ons\\_servicedsagerapurac", "grantTypeRefreshToken": "refresh_token", "grantTypeAccessToken": "password"}}, "ConfiguracaoDeSistemaSettings": {"SegundosEntreNotificacoesPendentes": 30, "QuantidadeDeDiasLimiteParaConsultaDoHistoricoDeSolicitacoes": 30, "Sons": {"Sucesso": "https://devsinapse.ons.org.br/assets/sons/sucesso.mp3", "Erro": "https://devsinapse.ons.org.br/assets/sons/erro.mp3", "Alerta": "https://devsinapse.ons.org.br/assets/sons/alerta.mp3", "Broadcast": "https://devsinapse.ons.org.br/assets/sons/broadcast.mp3"}}, "VerificacaoDeGeolocalizacaoSettings": {"RaioDeAtuacaoDoCentroDoAgenteEmKm": 30, "ValidarGeolocalizacaoPorPadrao": false}, "TemplatesSettings": {"DiretorioDosTemplates": "templates", "NomeDoArquivoDeDicionario": "dicionario.json"}, "ConfiguracaoDeNotificacaoSettings": {"Imagem": null, "Icone": "assets/icon/favicon.ico", "TempoExpiracaoEmSegundos": 59, "Sufixo": null, "Prefixo": "SINapse -"}, "UsuarioDoSistemaSettings": {"Login": "sinapse", "Nome": "Sistema", "Sid": "12331ffdsfs4345"}, "FinalizacaoAutomaticaDeSolicitacaoSettings": {"MinutosAntesDeFinalizar": 30, "IntervaloDeExecucaoEmMinutos": 5}, "Serilog": {"Using": ["Serilog.Sinks.Console"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning", "Microsoft.Extensions.Diagnostics.HealthChecks.DefaultHealthCheckService": "Fatal", "HealthChecks.UI.Core.Notifications.WebHookFailureNotifier": "Fatal", "HealthChecks.UI.Core.HostedService.HealthCheckReportCollector": "Fatal", "Serilog.AspNetCore.RequestLoggingMiddleware": "Information", "MassTransit": "Error"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {Message:lj} ({Properties}){Exception}{NewLine}"}}], "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId"]}, "AllowedHosts": "*", "XTokenRequirementSettings": {"LambdaAws": "976DF373-D6A3-4E38-99FF-573AD43E72D2", "KongOns": "99C29281-8B0C-468C-92D2-660D0361FFC6"}, "DataSyncSettings": {"SyncWaitTimeInMinutes": 5, "CustomHeaders": {"Accept": "application/json", "Content-Type": "application/json", "User-Agent": "ONS.SINapse.Api"}, "ServiceUri": "https://sinapse-dados-api-tst.apps.ocpd.ons.org.br", "Authentication": {"ServiceUri": "https://poptst.ons.org.br/ons.pop.federation/oauth2/token", "ApplicationCacheExpiration": 300, "ApplicationName": "SINAPSE", "ApplicationOrigin": "http://local.ons.org.br", "Username": "<EMAIL>"}}, "ComunicadoSettings": {"TempoDeVidaDeComunicadoEmDias": 30}, "ControleDeChamadaSettings": {"TempoDuracaoEmSegundos": 120, "TempoEntreTarefas": 30}}