{"ONSEnv": "ons.sinapse.dev", "AWS": {"Profile": "default", "Region": "us-east-1", "ResignRetries": true}, "ApplicationSettings": {"ServerBaseUrl": "https://api.devsinapse.ons.org.br/", "ServerLocalBaseUrl": "http://localhost/", "Version": "VERSION_BUILD_DEVOPS", "HealthcheckSettings": [{"EndpointName": "SINapse", "EndpointUrl": "https://api.sinapse.ons.org.br/health"}, {"EndpointName": "Integração SINapse", "EndpointUrl": "https://api.devsinapseintegracao.ons.org.br/health"}, {"EndpointName": "SINapse Api de Dados", "EndpointUrl": "https://sinapse-dados-api-tst.apps.ocpd.ons.org.br/health"}]}, "MongoDbSettings": {"DatabaseName": "sinapse", "MaxConnections": 50, "MaxConnectionIdleTimeInSeconds": 30}, "CacheSettings": {"Host": "************", "Port": "6379", "InstanceName": "sinapse", "UseRedis": false, "ExpiracaoDeFonteEmMinutos": 60, "ExpiracaoDeMotivoEmMinutos": 60, "ExpiracaoDeOperacaoEmMinutos": 60}, "FirebaseSettings": {"BasePath": "https://ons-sinapse-default-rtdb.firebaseio.com/", "Base64ConfigJson": "****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "ArquivoConfigJson": "firebase-keys.json", "QuantidadeDeTentativas": 3, "SegundosEntreTentativas": 1, "TokenWrite": "C93C72CD-F54D-4263-AB1B-86ABDBF5842D", "TokenRead": "47B6CAF7-57E6-4D29-A439-3D46B4F2E49D", "TokenReadWrite": "12CDBE0E-093E-42B2-8EB2-5B22BA1E0517"}, "KafkaSettings": {"Ativo": true, "QuantidadeDeWorkersPorInstancia": 10, "SaslUsername": "SPSAdminGIT", "ConsumerName": "ONS.SINAPSE", "RequireAuth": true, "BootstrapServers": "tst-kafka-001.ons.org.br:9093,tst-kafka-002.ons.org.br:9093,tst-kafka-003.ons.org.br:9093", "SaslMechanism": "Plain", "SecurityProtocol": "SaslSsl", "SslCaPem": ""}, "KafkaTopicsSettings": {"CadastroDeSolicitacao": "ONS.SINapse.CadastroSolicitacao", "TrocaDeStatusDeSolicitacao": "ONS.SINapse.TrocaStatusSolicitacao", "ConsultaDeStatusDeSolicitacao": "ONS.SINapse.StatusSolicitacao", "StatusAgente": "ONS.SINapse.StatusAgente", "Health": "ONS.SINapse.HealthCheck", "Sistemas": [{"Nome": "GERDIN", "Topic": "ONS.SINapse.StatusSolicitacao", "Notificar": true}, {"Nome": "SINapse", "Topic": "ONS.SINapse.StatusSolicitacao", "Notificar": false}]}, "KafkaConsumersSettings": {"CadastroDeSolicitacaoConsumerProvider": 10, "TrocaDeStatusConsumerProvider": 10}, "SqsQueueSettings": {"ExtracaoDeSolicitacao": "ONS-SINAPSE-EXTRACAO-DE-DADOS", "EnviarSolicitacaoParaMongo": "ONS-SINAPSE-ENVIAR-SOLICITACAO-PARA-MONGO", "RemoverSolicitacaoDoFirebase": "ONS-SINAPSE-REMOVER-SOLICITACAO-DO-FIREBASE", "FinalizarSolicitacaoAutomaticamente": "ONS-SINAPSE-SOLICITACAO-FINALIZACAO-AUTO"}, "ONS": {"Authorization": {"Issuer": "https://poptst.ons.org.br/ons.pop.federation/", "Audience": "SINAPSE", "UseRsa": "true", "RsaModulus": "wSxNKSkhfB1XR+fD/KZxK5nLEEHHBNrbSpiNw9FtcJHkvOiBXWI+G43Y1rvp6zp2/sjEqiXbQlFuMf2d/hM9ScIrdtrykf3m0OpDvhACFgwvvdiIaWOqIZ9oJCS9uzgEq7OGwH4gQklIOUbVrjZftXc0qFRR3XwkwGPGaNLsVpzSMeJHDJJReJe4MtztgsBS//AzkSdbhBpcAwQYOdmeQZTxL76miZqIHqAWAGQZgh/y3kHdfayhMb/hSgay933ITWyV2V7TUMBByYm6MOLuSRWTuloVIiwA/Nap5tgrQFdCuc34GCNgQIocn8qbcICI21AebnbyEyo96sONodToEQ==", "RsaPublicExponent": "AQAB"}, "PopAuth": {"Origin": "http://local.ons.org.br", "TokenURL": "https://poptst.ons.org.br/ons.pop.federation/oauth2/token", "ClientId": "SINAPSE", "Username": "ons\\_servicedsagerapurac", "GrantTypeRefreshToken": "refresh_token", "GrantTypeAccessToken": "password", "UrlLogout": "https://poptst.ons.org.br/pop/Views/SignOut/SignOut.aspx?ReturnUrl=https%3A%2F%2Fwww.ons.org.br"}}, "ConfiguracaoDeSistemaSettings": {"SegundosEntreNotificacoesPendentes": 30, "QuantidadeDeDiasLimiteParaConsultaDoHistoricoDeSolicitacoes": 30, "Sons": {"Sucesso": "https://devsinapse.ons.org.br/assets/sons/sucesso.mp3", "Erro": "https://devsinapse.ons.org.br/assets/sons/erro.mp3", "Alerta": "https://devsinapse.ons.org.br/assets/sons/alerta.mp3", "Broadcast": "https://devsinapse.ons.org.br/assets/sons/broadcast.mp3"}}, "VerificacaoDeGeolocalizacaoSettings": {"RaioDeAtuacaoDoCentroDoAgenteEmKm": 30, "ValidarGeolocalizacaoPorPadrao": false}, "TemplatesSettings": {"DiretorioDosTemplates": "templates", "NomeDoArquivoDeDicionario": "dicionario.json"}, "ConfiguracaoDeNotificacaoSettings": {"TempoExpiracaoEmSegundos": 59, "Sufixo": null, "Prefixo": "SINapse -"}, "UsuarioDoSistemaSettings": {"Login": "sinapse", "Nome": "Sistema", "Sid": "12331ffdsfs4345"}, "Serilog": {"Using": ["Serilog.Sinks.Console"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning", "Microsoft.Extensions.Diagnostics.HealthChecks.DefaultHealthCheckService": "Fatal", "HealthChecks.UI.Core.Notifications.WebHookFailureNotifier": "Fatal", "HealthChecks.UI.Core.HostedService.HealthCheckReportCollector": "Fatal", "Serilog.AspNetCore.RequestLoggingMiddleware": "Information", "MassTransit": "Error"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {Message:lj} ({Properties}){Exception}{NewLine}"}}], "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId"]}, "AllowedHosts": "*", "XTokenRequirementSettings": {"LambdaAws": "976DF373-D6A3-4E38-99FF-573AD43E72D2", "KongOns": "99C29281-8B0C-468C-92D2-660D0361FFC6"}, "DataSyncSettings": {"SyncWaitTimeInMinutes": 5, "CustomHeaders": {"Accept": "application/json", "Content-Type": "application/json", "User-Agent": "ONS.SINapse.Api"}, "ServiceUri": "https://sinapse-dados-api-tst.apps.ocpd.ons.org.br", "DatasetCacheExpiration": 3600, "Authentication": {"ServiceUri": "https://poptst.ons.org.br/ons.pop.federation/oauth2/token", "ApplicationCacheExpiration": 300, "ApplicationName": "SINAPSE", "ApplicationOrigin": "http://local.ons.org.br", "Username": "<EMAIL>"}}, "ComunicadoSettings": {"TempoDeVidaDeComunicadoEmDias": 30}, "ControleDeChamadaSettings": {"TempoDuracaoEmSegundos": 120, "TempoEntreTarefas": 30}, "OpenTelemetrySettings": {"ServiceVersion": "1.0.0", "ServiceEndpoint": "http://localhost:4318", "ServiceEnabled": false}}