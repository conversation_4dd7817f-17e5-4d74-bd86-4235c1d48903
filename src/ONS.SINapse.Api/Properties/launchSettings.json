{"$schema": "https://json.schemastore.org/launchsettings.json", "profiles": {"ONS.SINapse.Api": {"commandName": "Project", "launchUrl": "swagger", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development", "OTEL_EXPORTER_OTLP_ENDPOINT": "http://localhost:4318"}, "applicationUrl": "http://localhost:6070", "dotnetRunMessages": true, "useSSL": false}, "ONS.SINapse.Api:HTTPS": {"commandName": "Project", "dotnetRunMessages": true, "launchBrowser": false, "launchUrl": "swagger", "applicationUrl": "https://localhost:6071", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development", "OTEL_EXPORTER_OTLP_ENDPOINT": "http://localhost:4318"}}, "Docker": {"commandName": "<PERSON>er", "launchUrl": "{Scheme}://{ServiceHost}:{ServicePort}/swagger", "publishAllPorts": true, "useSSL": false, "sslPort": 6070}}}