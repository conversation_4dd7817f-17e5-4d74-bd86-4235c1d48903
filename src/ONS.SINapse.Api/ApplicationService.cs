using Microsoft.Extensions.Options;
using ONS.SINapse.Business.IBusiness.TemplatesDeSolicitacao;
using ONS.SINapse.Shared.Services.Firebase;
using ONS.SINapse.Shared.Settings;

namespace ONS.SINapse.Api;
public static class ApplicationService
{
    public static IApplicationBuilder CarregarTemplates(this IApplicationBuilder applicationBuilder)
    {
        var services = applicationBuilder.ApplicationServices.CreateScope().ServiceProvider.GetServices<ITemplateDeSolicitacaoBusiness>();
        foreach (var service in services)
        {
            Task.Run(() => service.RegistrarAsync(CancellationToken.None));
        }

        return applicationBuilder;
    }

    public static IApplicationBuilder ExecuteVersionHandler(this IApplicationBuilder applicationBuilder)
    {
        var databaseFirebaseService = applicationBuilder.ApplicationServices.CreateScope().ServiceProvider.GetService<IDatabaseFirebaseService>();
        var usuarioDoSistemaSettings = applicationBuilder.ApplicationServices.GetService<IOptions<UsuarioDoSistemaSettings>>()!.Value;
        Task.Run(() => databaseFirebaseService?.VersionHandler(usuarioDoSistemaSettings.Sid, CancellationToken.None));
        return applicationBuilder;
    }
}
