using MassTransit;
using Microsoft.AspNetCore.Mvc.Razor;
using Microsoft.Extensions.Options;
using ONS.SINapse.Api;
using ONS.SINapse.Api.Middlewares;
using ONS.SINapse.Business.IBusiness.TemplatesDeSolicitacao;
using ONS.SINapse.CrossCutting;
using ONS.SINapse.Integracao.Shared.Settings;
using ONS.SINapse.Shared.Bus;
using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Shared.Filters;
using ONS.SINapse.Shared.Logging;
using ONS.SINapse.Shared.Middleware;
using ONS.SINapse.Shared.Services.Firebase;
using ONS.SINapse.Shared.Settings;
using ONS.SINapse.Shared.Telemetry;
using ONS.SINapse.Solicitacao.Workers;
using ONS.SINapse.Solicitacao.Workers.Listener;
using Serilog;
using Swashbuckle.AspNetCore.SwaggerUI;

var builder = WebApplication.CreateBuilder(args);

const string corsPolicy = "CORS_POLICY";

builder.Configuration.SetBasePath(Directory.GetCurrentDirectory())
    .AddJsonFile("hosting.json", optional: true)
    .AddJsonFile("permissao-usuario.json", optional: false, reloadOnChange: true)
    .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
#if DEBUG
    .AddJsonFile("appsettings.Development.json", optional: true)
#endif
    .AddEnvironmentVariables();


builder.Services.AddAwsSystemsManager(builder.Configuration);


DependencyInjector.Register(builder.Services, builder.Configuration);
builder.Services.AddMassTransitWithSqs(builder.Configuration);

       
builder.Services
    .AddSinapseHealthChecks(builder.Configuration)
    .AddSinapseJobHealthChecks();

builder.Services.ConfigureMetrics();
builder.Services.AddCors(options => { 
    options.AddPolicy(corsPolicy,
        corsPolicyBuilder => corsPolicyBuilder
            .SetIsOriginAllowed( _ => true)
            .AllowAnyMethod()
            .AllowAnyHeader()
            .AllowCredentials()
            .WithExposedHeaders("Nome-Arquivo")
            .Build()
    ); 
});

var jwtConfiguration = builder.Configuration.GetSection("ONS:Authorization");

builder.Services.Configure<AuthorizationSettings>(jwtConfiguration);

builder.Services.AddSwaggerGen(c => { c.ResolveConflictingActions(apiDescriptions => apiDescriptions.First()); });
builder.Services.AddControllers();

builder.Services.AddHttpContextAccessor();
builder.Services.AddTransient<GlobalExceptionHandlerMiddleware>();
builder.Services.AddTransient<OptionsMiddleware>();
builder.Services.AddTransient<GeolocalizacaoMiddleware>();
builder.Services.AddMvc().AddViewLocalization(LanguageViewLocationExpanderFormat.Suffix).AddControllersAsServices();
builder.Services.AddMvc(opts =>
{
    var filters = opts.Filters;
    filters.Add<NotificationFilter>();
    filters.Add<ResultHandlerFilter>();
});
builder.Services.AddHostedService<SolicitacaoFirebaseListener>();
builder.Services.AddHostedService<FinalizarSolicitacaoAutomaticamenteWorker>();
builder.Logging.AddSerilog();

builder.Host.UseLogging();

builder.WebHost
    .UseIISIntegration()
    .ConfigureKestrel(_ => { });

builder.AddSinapseOpenTelemetry();

var app = builder.Build();

app.UseSinapseOpenTelemetry();

if (app.Environment.IsDevelopment())
{
    app.UseDeveloperExceptionPage();
}

app.UseCustomRequestLog();

app.UseCustomRequestLog();
app.UseGlobalExceptionHandlerMiddleware();
app.ConfigureMetrics();
app.UseRouting();
app.UseAuthentication();
app.UseAuthorization();
app.UseCors(corsPolicy);
app.UseOptionsMiddleware();

app.UseMiddleware(typeof(GeolocalizacaoMiddleware));

app.UseResponseCompression();
app.UseSwagger();
app.UseSwaggerUI(s => s.DocExpansion(DocExpansion.None));
app.UseStaticFiles();
app.ConfigureHealthCheck();

var locOptions = app.Services.GetService<IOptions<RequestLocalizationOptions>>();
if (locOptions is not null)
{
    app.UseRequestLocalization(locOptions.Value);
}

app.UseResponseCompression();
            
app.CarregarTemplates();

#if !DEBUG
app.ExecuteVersionHandler();            
#endif

var appName = app.Environment.ApplicationName;

try
{
    Console.WriteLine($"Starting {appName}");
    Console.WriteLine($"Checking UTC time: {DateTime.UtcNow} - Server time: {DateTime.Now}");
    app.Run();
}
catch (Exception ex)
{
    Console.WriteLine($"{appName} Host terminated unexpectedly");
    Console.WriteLine(ex);
}
finally
{
    Console.WriteLine($"Closed {appName}");
    Console.WriteLine();
}