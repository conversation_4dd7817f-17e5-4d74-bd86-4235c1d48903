using Microsoft.AspNetCore.Mvc.Razor;
using Microsoft.Extensions.Options;
using ONS.SINapse.Api;
using ONS.SINapse.Api.Middlewares;
using ONS.SINapse.CrossCutting;
using ONS.SINapse.Integracao.Shared.Settings;
using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Shared.Filters;
using ONS.SINapse.Shared.Logging;
using ONS.SINapse.Shared.Middleware;
using ONS.SINapse.Shared.Swagger;
using ONS.SINapse.Shared.Telemetry;
using Serilog;

var builder = WebApplication.CreateBuilder(args);

const string corsPolicy = "CORS_POLICY";

builder.Configuration.SetBasePath(Directory.GetCurrentDirectory())
    .AddJsonFile("hosting.json", optional: true)
    .AddJsonFile("permissao-usuario.json", optional: false, reloadOnChange: true)
    .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
#if DEBUG
    .AddJsonFile("appsettings.Development.json", optional: true)
#endif
    .AddEnvironmentVariables();


builder.Services.AddAwsSystemsManager(builder.Configuration);


DependencyInjector.Register(builder.Services, builder.Configuration);
builder.Services.AddMassTransitWithSqs(builder.Configuration);


builder.Services
    .AddSinapseHealthChecks(builder.Configuration);

builder.Services.ConfigureMetrics();
builder.Services.AddCors(options => { 
    options.AddPolicy(corsPolicy,
        corsPolicyBuilder => corsPolicyBuilder
            .SetIsOriginAllowed( _ => true)
            .AllowAnyMethod()
            .AllowAnyHeader()
            .AllowCredentials()
            .WithExposedHeaders("Content-Disposition")
            .Build()
    ); 
});

var jwtConfiguration = builder.Configuration.GetSection("ONS:Authorization");

builder.Services.Configure<AuthorizationSettings>(jwtConfiguration);

builder.Services.AddSwaggerConfiguration();

builder.Services.AddControllers();

builder.Services.AddHttpContextAccessor();
builder.Services.AddTransient<GlobalExceptionHandlerMiddleware>();
builder.Services.AddTransient<OptionsMiddleware>();
builder.Services.AddTransient<GeolocalizacaoMiddleware>();
builder.Services.AddMvc().AddViewLocalization(LanguageViewLocationExpanderFormat.Suffix).AddControllersAsServices();
builder.Services.AddMvc(opts =>
{
    var filters = opts.Filters;
    filters.Add<NotificationFilter>();
    filters.Add<ResultHandlerFilter>();
});
builder.Logging.AddSerilog();

builder.Host.UseLogging();

builder.WebHost
    .UseIISIntegration()
    .ConfigureKestrel(_ => { });

builder.AddSinapseOpenTelemetry();

var app = builder.Build();

app.UseSinapseOpenTelemetry();

#if DEBUG
    // Use Developer Exception Page only in Development environment (safe)
    // SonarQube: csharpsquid:S4507 - acceptable use
    if (app.Environment.IsDevelopment())
    {
        app.UseDeveloperExceptionPage();
    }
#endif

app.UseCustomRequestLog();

app.UseCustomRequestLog();
app.UseGlobalExceptionHandlerMiddleware();
app.ConfigureMetrics();
app.UseRouting();
app.UseAuthentication();
app.UseAuthorization();
app.UseCors(corsPolicy);
app.UseOptionsMiddleware();

app.UseMiddleware(typeof(GeolocalizacaoMiddleware));

app.UseResponseCompression();
app.UseSwaggerConfiguration();
app.UseStaticFiles();
app.ConfigureHealthCheck();

var locOptions = app.Services.GetService<IOptions<RequestLocalizationOptions>>();
if (locOptions is not null)
{
    app.UseRequestLocalization(locOptions.Value);
}

app.UseResponseCompression();
            
app.CarregarTemplates();

#if !DEBUG
app.ExecuteVersionHandler();            
#endif

var appName = app.Environment.ApplicationName;

try
{
    Console.WriteLine($"Starting {appName}");
    Console.WriteLine($"Checking UTC time: {DateTime.UtcNow} - Server time: {DateTime.Now}");
    app.Run();
}
catch (Exception ex)
{
    Console.WriteLine($"{appName} Host terminated unexpectedly");
    Console.WriteLine(ex);
}
finally
{
    Console.WriteLine($"Closed {appName}");
    Console.WriteLine();
}