using FluentValidation.Results;
using MassTransit.Testing;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Shared.Messages;
using static MassTransit.RequestExtensions;

namespace ONS.SINapse.UnitTest.Shared.Mediator;

public class TestMediatorHandler(ITestHarness testHarness) : IMediatorHandler
{
    public Task PublicarEventoAsync<T>(T evento, CancellationToken cancellationToken) where T : Event
    {
        return testHarness.Bus.Publish(evento, cancellationToken);
    }
    
    public async Task<ValidationResult> EnviarComandoAsync<T>(T comando, CancellationToken cancellationToken) where T : Command
    {
        var result = await testHarness.Bus.Request<T, ValidationResult>(comando, cancellationToken);
        return result.Message;
    }

    public async Task<TResult> EnviarComandoAsync<TCommand, TResult>(TCommand comando, CancellationToken cancellationToken) where TCommand : Command<TResult> where TResult : CommandResult
    {
        var result = await testHarness.Bus.Request<TCommand, TResult>(comando, cancellationToken);
        return result.Message;
    }

    public async Task<TResult?> BuscarDadosAsync<TResult>(Query<TResult> query, CancellationToken cancellationToken)
    {
        var result = await testHarness.Bus.Request<Query<TResult>, QueryResult<TResult>>(query, cancellationToken);
        return result.Message.Result;
    }
}