using FluentValidation.Results;
using MassTransit.Testing;
using ONS.SINapse.Entities.ValueObjects;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Shared.Messages;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands.Firebase;
using ONS.SINapse.Solicitacao.Dtos;
using ONS.SINapse.Solicitacao.QueryHandlers.Dtos;
using ONS.SINapse.Solicitacao.QueryHandlers.Queries;
using static MassTransit.RequestExtensions;

namespace ONS.SINapse.UnitTest.Shared.Mediator;

public class TestMediatorHandler(ITestHarness testHarness) : IMediatorHandler
{
    public Task PublicarEventoAsync<T>(T evento, CancellationToken cancellationToken) where T : Event
    {
        return testHarness.Bus.Publish(evento, cancellationToken);
    }

    public async Task<ValidationResult> EnviarComandoAsync<T>(T comando, CancellationToken cancellationToken) where T : Command
    {
        if (comando is EncaminharSolicitacaoNoFirebaseCommand encaminharSolicitacaoNoFirebaseCommand)
        {
            var result = await testHarness.Bus.Request<EncaminharSolicitacaoNoFirebaseCommand, ValidationResult>(encaminharSolicitacaoNoFirebaseCommand, cancellationToken);
            return result.Message;
        }

        if (comando is TrocarDeStatusNoFirebaseCommand trocarDeStatusNoFirebaseCommand)
        {
            var result = await testHarness.Bus.Request<TrocarDeStatusNoFirebaseCommand, ValidationResult>(trocarDeStatusNoFirebaseCommand, cancellationToken);
            return result.Message;
        }

        if (comando is LeituraDeSolicitacaoNoFirebaseCommand leituraDeSolicitacaoNoFirebaseCommand)
        {
            var result = await testHarness.Bus.Request<LeituraDeSolicitacaoNoFirebaseCommand, ValidationResult>(leituraDeSolicitacaoNoFirebaseCommand, cancellationToken);
            return result.Message;
        }

        if (comando is EntregaDeSolicitacaoNoFirebaseCommand entregaDeSolicitacaoNoFirebaseCommand)
        {
            var result = await testHarness.Bus.Request<EntregaDeSolicitacaoNoFirebaseCommand, ValidationResult>(entregaDeSolicitacaoNoFirebaseCommand, cancellationToken);
            return result.Message;
        }

        if (comando is CriarSolicitacaoNoFirebaseCommand criarSolicitacaoNoFirebaseCommand)
        {
            var result = await testHarness.Bus.Request<CriarSolicitacaoNoFirebaseCommand, ValidationResult>(criarSolicitacaoNoFirebaseCommand, cancellationToken);
            return result.Message;
        }

        if (comando is CriarMensagemNoChatDeSolicitacaoFirebaseCommand criarMensagemNoChatDeSolicitacaoFirebaseCommand)
        {
            var result = await testHarness.Bus.Request<CriarMensagemNoChatDeSolicitacaoFirebaseCommand, ValidationResult>(criarMensagemNoChatDeSolicitacaoFirebaseCommand, cancellationToken);
            return result.Message;
        }

        var resultGeneric = await testHarness.Bus.Request<T, ValidationResult>(comando, cancellationToken);
        return resultGeneric.Message;
    }

    public async Task<TResult> EnviarComandoAsync<TCommand, TResult>(TCommand comando, CancellationToken cancellationToken) where TCommand : Command<TResult> where TResult : CommandResult
    {
        var result = await testHarness.Bus.Request<TCommand, TResult>(comando, cancellationToken);
        return result.Message;
    }

    public async Task<TResult?> BuscarDadosAsync<TResult>(Query<TResult> query, CancellationToken cancellationToken)
    {
        if (query is BuscarDestinatariosDeSolicitacaoQuery buscarDestinatariosDeSolicitacaoQuery)
        {
            var queryResult = await testHarness.Bus.Request<BuscarDestinatariosDeSolicitacaoQuery, QueryResult<IEnumerable<DestinatarioDeSolicitacaoDto>>>
                (buscarDestinatariosDeSolicitacaoQuery, cancellationToken);
            return (TResult)(object)queryResult.Message.Result!;
        }

        if (query is BuscarCentrosDeOperacaoEmitentesDeSolicitacaoQuery buscarCentrosDeOperacaoEmitentesDeSolicitacaoQuery)
        {
            var queryResult = await testHarness.Bus.Request<BuscarCentrosDeOperacaoEmitentesDeSolicitacaoQuery, QueryResult<List<CentroDeOperacaoDto>>>
                (buscarCentrosDeOperacaoEmitentesDeSolicitacaoQuery, cancellationToken);
            return (TResult)(object)queryResult.Message.Result!;
        }

        if (query is ConsultaHistoricoSolicitacaoQuery consultaHistoricoSolicitacaoQuery)
        {
            var queryResult = await testHarness.Bus.Request<ConsultaHistoricoSolicitacaoQuery, QueryResult<Paging<ConsultaHistoricoSolicitacaoDto>>>
                (consultaHistoricoSolicitacaoQuery, cancellationToken);
            return (TResult)(object)queryResult.Message.Result!;
        }

        if (query is ConsultaHistoricoSolicitacaoAnaliticaQuery consultaHistoricoSolicitacaoAnaliticaQuery)
        {
            var queryResult = await testHarness.Bus.Request<ConsultaHistoricoSolicitacaoAnaliticaQuery, QueryResult<ConsultaAnaliticaDeSolicitacaoDto>>
                (consultaHistoricoSolicitacaoAnaliticaQuery, cancellationToken);
            return (TResult)(object)queryResult.Message.Result!;
        }

        if (query is BuscarCadastroSolicitacaoPorIdQuery cuscarCadastroSolicitacaoPorIdQuery)
        {
            var queryResult = await testHarness.Bus.Request<BuscarCadastroSolicitacaoPorIdQuery, QueryResult<CadastroDeSolicitacaoDto>>
                (cuscarCadastroSolicitacaoPorIdQuery, cancellationToken);
            return (TResult)(object)queryResult.Message.Result!;
        }

        if (query is BuscarCadastroSolicitacaoStatusQuery buscarCadastroSolicitacaoStatusQuery)
        {
            var queryResult = await testHarness.Bus.Request<BuscarCadastroSolicitacaoStatusQuery, QueryResult<List<StatusDeSolicitacaoIntegracaoDto>>>
                (buscarCadastroSolicitacaoStatusQuery, cancellationToken);
            return (TResult)(object)queryResult.Message.Result!;
        }

        var result = await testHarness.Bus.Request<Query<TResult>, QueryResult<TResult>>(query, cancellationToken);
        return result.Message.Result;
    }
}