using AutoMapper;
using FluentValidation;
using MassTransit;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq.AutoMock;
using Newtonsoft.Json;
using ONS.SINapse.Business.IBusiness;
using ONS.SINapse.Business.IBusiness.Firebase;
using ONS.SINapse.Business.Imp.AutoMapper.Converters;
using ONS.SINapse.Business.Imp.Business.EventHandlers.Handlers;
using ONS.SINapse.Business.Imp.Workers.Messages;
using ONS.SINapse.Business.Imp.Workers.Topicos;
using ONS.SINapse.CacheSync;
using ONS.SINapse.Integracao.Shared.Helpers;
using ONS.SINapse.Integracao.Shared.Settings;
using ONS.SINapse.Repository.Imp.Context;
using ONS.SINapse.Repository.IRepository;
using ONS.SINapse.Repository.IRepository.Firebase;
using ONS.SINapse.Shared.Bus;
using ONS.SINapse.Shared.Identity;
using ONS.SINapse.Shared.Kafka;
using ONS.SINapse.Shared.Kafka.DependencyInjection;
using ONS.SINapse.Shared.Kafka.Providers;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Shared.Messages;
using ONS.SINapse.Shared.Notifications;
using ONS.SINapse.Shared.Services.Caching;
using ONS.SINapse.Shared.Services.Firebase;
using ONS.SINapse.Solicitacao;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using ONS.SINapse.Solicitacao.Dtos.Integracao;
using ONS.SINapse.Solicitacao.EventHandlers.IntegrationEvents;
using ONS.SINapse.Solicitacao.Factories;
using ONS.SINapse.Solicitacao.Factories.Integracao;
using ONS.SINapse.Solicitacao.Mapper;
using ONS.SINapse.Solicitacao.Middlewares;
using ONS.SINapse.Solicitacao.Middlewares.Solicitacao;
using ONS.SINapse.Solicitacao.Workers.Messages;
using ONS.SINapse.Solicitacao.Workers.Topicos;
using ONS.SINapse.UnitTest.Shared.Bus;
using ONS.SINapse.UnitTest.Shared.JsonConverters;

namespace ONS.SINapse.UnitTest.Shared;

public class ServicesDependencyInjectorFactory : IDisposable
{
    private readonly IServiceCollection _serviceCollection;
    private AutoMocker _mocker;

    public ServicesDependencyInjectorFactory(AutoMocker mocker)
    {
        _serviceCollection = new ServiceCollection();
        _mocker = mocker;
    }

    public void RegisterServices()
    {
        AddMassTransit();
        AddLogger();
        AddMapper();
        AddMediatorHandler();
        AddNotificationContext();
        AddUserContext();
        AddKafka();
        AddRepositories();
        AddBusiness();
        AddIProducerProvider();
        AddFirebaseServices();
        AddValidators();
        AddCache();
        AddFactory();
        AddHttpContextAccessor();
        AddOptions();
    }

    public void DefinirMocker(AutoMocker mocker)
    {
        _mocker = mocker;
    }

    public IServiceProvider BuildServiceProvider()
    {
        return _serviceCollection.BuildServiceProvider(true);
    }

    private void AddMassTransit()
    {
        _serviceCollection.AddMassTransitTestHarness(options =>
        {
            options.AddConsumer<ExtracaoDeSolicitacaoEventHandler>();
            options.AddConsumers(typeof(CommandHandler<>).Assembly, typeof(CacheSyncApplicationReference).Assembly);
            options.AddConsumers(typeof(CommandHandler<>).Assembly, typeof(SolicitacaoApplicationReference).Assembly);
            options.AddRequestClient<Command>();
            options.SetDefaultRequestTimeout(TimeSpan.FromSeconds(60));
            options.SetTestTimeouts(TimeSpan.FromSeconds(60));

            options.UsingInMemory((context, cfg) =>
            {
                cfg.UseConsumeFilter<UserDefinitionMiddleware<EncaminharSolicitacaoCommand>>(context);

                cfg.UseConsumeFilter<LoteIdDefinitionMiddleware>(context);
                cfg.UseConsumeFilter<IdDefinitionMiddleware>(context);

                var jsonSettings = new JsonSerializerSettings
                {
                    ContractResolver = new IncludePrivateContractResolver(),
                    TypeNameHandling = TypeNameHandling.Objects,
                    ConstructorHandling = ConstructorHandling.AllowNonPublicDefaultConstructor,
                    ObjectCreationHandling = ObjectCreationHandling.Replace,
                    PreserveReferencesHandling = PreserveReferencesHandling.Objects,
                    ReferenceLoopHandling = ReferenceLoopHandling.Ignore
                };

                jsonSettings.Converters.Add(new ReadOnlyCollectionConverter());
                jsonSettings.Converters.Add(new KafkaHeadersJsonConverter());

                cfg.ConfigureNewtonsoftJsonDeserializer(_ => jsonSettings);
                cfg.ConfigureNewtonsoftJsonSerializer(_ => jsonSettings);

                cfg.UseNewtonsoftJsonDeserializer();
                cfg.UseNewtonsoftJsonSerializer();
                cfg.UseNewtonsoftRawJsonDeserializer();
                cfg.UseNewtonsoftRawJsonSerializer();

                cfg.ConfigureEndpoints(context);
            });
        });

        _serviceCollection.AddScoped<ISqsBus>(sp =>
        {
            var publishEndpoint = sp.GetRequiredService<IPublishEndpoint>();
            return new TestSqsBus(publishEndpoint);
        });
    }

    private void AddLogger()
    {
        _serviceCollection.AddLogging(builder =>
        {
            builder.AddConsole();
        });
    }

    private void AddMapper()
    {
        AddMappers();
    }

    private void AddMappers()
    {
        _serviceCollection.AddTransient<IMapper>(_ => _mocker.Get<IMapper>());

        _serviceCollection
            .AddTransient<ICriarCommandStatusDeSolicitacaoRecebidoFactory,
                CriarCommandStatusDeSolicitacaoRecebidoFactory>();

        _serviceCollection
            .AddTransient<ISolicitacaoFirebaseCommandFactory,
                SolicitacaoFirebaseCommandFactory>();

        AddConverter();
    }

    private void AddConverter()
    {
        _serviceCollection.AddTransient<UsuarioConverter>(_ => _mocker.Get<UsuarioConverter>());
        _serviceCollection.AddTransient<CadastroSolicitacaoDtoToSolicitacaoConverter>(_ => _mocker.Get<CadastroSolicitacaoDtoToSolicitacaoConverter>());
    }

    private void AddMediatorHandler()
    {
        _serviceCollection.AddTransient<IMediatorHandler>(_ => _mocker.Get<IMediatorHandler>());
    }

    private void AddNotificationContext()
    {
        _serviceCollection.AddTransient<NotificationContext>(_ => _mocker.Get<NotificationContext>());
    }

    private void AddUserContext()
    {
        _serviceCollection.AddTransient(_ => _mocker.Get<IUserContext>());
        _serviceCollection.AddTransient(_ => _mocker.Get<IUsuarioJwtHelper>());
    }

    // TODO: Configurar AutoMock para Kafka
    private void AddKafka()
    {
        var sinapseKafkaOptions = new KafkaTopicsSettings
        {
            CadastroDeSolicitacao = "ONS.SINAPSE.CADASTRO_SOLICITACAO",
            TrocaDeStatusDeSolicitacao = "ONS.SINAPSE.TROCA_STATUS_SOLICITACAO.GERDIN",
            ConsultaDeStatusDeSolicitacao = "ONS.SINAPSE.STATUS_SOLICITACAO",
            StatusAgente = "ONS.SINAPSE.STATUS_AGENTE",
            Health = "ONS.SINAPSE.HEALTHCHECK"
        };

        _serviceCollection.AddKafkaProvider();
        AddTopicos(sinapseKafkaOptions);
    }

    private void AddTopicos(KafkaTopicsSettings options)
    {
        _serviceCollection
            .AddSingleton<TopicoIntegrationKafka<CadastroDeSolicitacaoExternaRecebidaIntegrationEvent>,
                CadastroDeSolicitacaoTopicoIntegrationKafka>(_ => new CadastroDeSolicitacaoTopicoIntegrationKafka(options.CadastroDeSolicitacao));

        _serviceCollection
            .AddSingleton<TopicoIntegrationKafka<TrocaDeStatusExternaIntegrationEvent>,
                TrocaDeStatusTopicoIntegrationKafka>(_ => new TrocaDeStatusTopicoIntegrationKafka(options.TrocaDeStatusDeSolicitacao));

        _serviceCollection
            .AddSingleton<TopicoIntegrationKafka<AtualizacaoStatusMessage>, ConsultaDeStatusTopicoIntegrationKafka>(_ =>
                new ConsultaDeStatusTopicoIntegrationKafka(options.ConsultaDeStatusDeSolicitacao));

        _serviceCollection
            .AddSingleton<TopicoIntegrationKafka<StatusAgenteMessage>, StatusAgenteTopicoIntegrationKafka>(_ =>
                new StatusAgenteTopicoIntegrationKafka(options.StatusAgente));

    }

    private void AddRepositories()
    {
        _serviceCollection.AddTransient<ISolicitacaoRepository>(_ => _mocker.Get<ISolicitacaoRepository>());
        _serviceCollection.AddTransient<IStatusServiceRepository>(_ => _mocker.Get<IStatusServiceRepository>());
        _serviceCollection.AddTransient<IMongoReadOnlyConnection>(_ => _mocker.Get<IMongoReadOnlyConnection>());
    }

    private void AddFirebaseServices()
    {
        _serviceCollection.AddTransient<ISolicitacaoFirebaseRepository>(_ => _mocker.Get<ISolicitacaoFirebaseRepository>());
        _serviceCollection.AddTransient<IAlertaSonoroFirebaseDatabaseService>(_ => _mocker.Get<IAlertaSonoroFirebaseDatabaseService>());
        _serviceCollection.AddTransient<INotificacaoFirebaseDatabaseService>(_ => _mocker.Get<INotificacaoFirebaseDatabaseService>());
        _serviceCollection.AddTransient<INotificacaoFirebaseBusiness>(_ => _mocker.Get<INotificacaoFirebaseBusiness>());
    }

    private void AddValidators()
    {
        _serviceCollection.AddTransient<IValidator<CadastroSolicitacaoDto>>(_ => _mocker.Get<IValidator<CadastroSolicitacaoDto>>());
        _serviceCollection.AddTransient<IValidator<ConfirmarCommand>>(_ => _mocker.Get<IValidator<ConfirmarCommand>>());
        _serviceCollection.AddTransient<IValidator<ImpedirCommand>>(_ => _mocker.Get<IValidator<ImpedirCommand>>());
        _serviceCollection.AddTransient<IValidator<FinalizarEmLoteCommand>>(_ => _mocker.Get<IValidator<FinalizarEmLoteCommand>>());
        _serviceCollection.AddTransient<IValidator<StatusDeSolicitacaoIntegracaoRecebimentoDto>>(_ => _mocker.Get<IValidator<StatusDeSolicitacaoIntegracaoRecebimentoDto>>());
        _serviceCollection.AddTransient<IValidator<InformarCienciaCommand>>(_ => _mocker.Get<IValidator<InformarCienciaCommand>>());
        _serviceCollection.AddTransient<IValidator<EncaminharSolicitacaoCommand>>(_ => _mocker.Get<IValidator<EncaminharSolicitacaoCommand>>());
        _serviceCollection.AddTransient<IValidator<TrocaDeStatusExternaIntegrationEvent>>(_ => _mocker.Get<IValidator<TrocaDeStatusExternaIntegrationEvent>>());
        _serviceCollection.AddTransient<IValidator<CadastroDeSolicitacaoExternaRecebidaIntegrationEvent>>(_ => _mocker.Get<IValidator<CadastroDeSolicitacaoExternaRecebidaIntegrationEvent>>());
    }

    private void AddBusiness()
    {
        _serviceCollection.AddTransient<IExtracaoDeSolicitacaoS3Business>(_ => _mocker.Get<IExtracaoDeSolicitacaoS3Business>());
    }

    private void AddCache()
    {
        _serviceCollection.AddTransient<ICacheService>(_ => _mocker.Get<ICacheService>());
    }

    private void AddFactory()
    {
        _serviceCollection.AddTransient<ISolicitacaoFirebaseCommandFactory, SolicitacaoFirebaseCommandFactory>();
    }

    private void AddHttpContextAccessor()
    {
        _serviceCollection.AddTransient<IHttpContextAccessor>(_ => _mocker.Get<IHttpContextAccessor>());
    }

    private void AddOptions()
    {
        _serviceCollection.AddTransient<IOptions<AuthorizationSettings>>(_ => _mocker.Get<IOptions<AuthorizationSettings>>());
    }

    private void AddIProducerProvider()
    {
        _serviceCollection.AddTransient<IProducerProvider<AtualizacaoStatusMessage>>(_ => _mocker.Get<IProducerProvider<AtualizacaoStatusMessage>>());
    }

    public void Dispose()
    {
        // TODO release managed resources here
    }
}