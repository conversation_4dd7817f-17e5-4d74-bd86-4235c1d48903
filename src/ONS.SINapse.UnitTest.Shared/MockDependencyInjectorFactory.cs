using AutoMapper;
using FluentValidation;
using MassTransit;
using MassTransit.Testing;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.Primitives;
using Moq;
using Moq.AutoMock;
using ONS.SINapse.Business.Imp.AutoMapper.Converters;
using ONS.SINapse.Business.Imp.Validators.Firebase;
using ONS.SINapse.Integracao.Shared.Settings;
using ONS.SINapse.Shared.DTO.Firebase;
using ONS.SINapse.Shared.Identity;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Shared.Notifications;
using ONS.SINapse.Solicitacao;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using ONS.SINapse.Solicitacao.Dtos.Integracao;
using ONS.SINapse.Solicitacao.EventHandlers.IntegrationEvents;
using ONS.SINapse.Solicitacao.Mapper;
using ONS.SINapse.Solicitacao.Validations;
using ONS.SINapse.UnitTest.Shared.Identity;
using ONS.SINapse.UnitTest.Shared.Mediator;

namespace ONS.SINapse.UnitTest.Shared;

public class MockDependencyInjectorFactory : IDisposable
{
    public AutoMocker Mocker { get; private set; }
    private readonly IServiceProvider _serviceProvider;
    public readonly ITestHarness Harness;
    public readonly IPublishEndpoint PublishEndpoint;
    private readonly ServicesDependencyInjectorFactory _servicesDependencyInjectorFactory;
    
    public MockDependencyInjectorFactory()
    {
        Mocker = new AutoMocker();
        _servicesDependencyInjectorFactory = new ServicesDependencyInjectorFactory(Mocker);
        _servicesDependencyInjectorFactory.RegisterServices();
        _serviceProvider = _servicesDependencyInjectorFactory.BuildServiceProvider();
        Harness = _serviceProvider.GetRequiredService<ITestHarness>();
        Harness.Start().Wait();

        using var scope = _serviceProvider.CreateScope();
        PublishEndpoint = scope.ServiceProvider.GetRequiredService<IPublishEndpoint>();
    }
    
    /// <summary>
    /// Sempre cria um AutoMocker para o controle de injeção de dependencia
    /// Não executa a chamada do método RegisterMocks(), portanto, nenhuma configuração de mock é feita
    /// </summary>
    /// <param name="mocker"></param>
    public void ResetMock(AutoMocker? mocker = null)
    {
        Mocker = mocker ?? new AutoMocker();
        _servicesDependencyInjectorFactory.DefinirMocker(Mocker);
    }

    /// <summary>
    /// Configura novamente todos os mocks ao controle de injeção de dependencia
    /// </summary>
    /// <param name="mocker"></param>
    public void RestartMock(AutoMocker? mocker = null)
    {
        ResetMock(mocker);
        RegisterMocks();
    }
    
    public void RegisterMocks()
    {
        AddMapper();
        AddMediatorHandler();
        AddNotificationContext();
        AddUserContext();
        AddRepositories();
        AddFirebaseServices();
        AddValidators();
        AddHttpContextAccessor();
        AddOptions();
    }

    private void AddRepositories()
    {
        // Configure repositories
    }

    private void AddFirebaseServices()
    {
        // Configure firebase services
    }

    private void AddValidators()
    {
        Mocker.Use<IValidator<CadastroSolicitacaoDto>>(new CadastroSolicitacaoDtoValidator(Mocker.Get<IUserContext>()));
        Mocker.Use<IValidator<EncaminharSolicitacaoCommand>>(new EncaminharSolicitacaoCommandValidator());
        Mocker.Use<IValidator<StatusDeSolicitacaoIntegracaoRecebimentoDto>>(new StatusDeSolicitacaoIntegracaoRecebimentoValidator(Mocker.Get<IUserContext>()));
        Mocker.Use<IValidator<CadastroDeSolicitacaoExternaRecebidaIntegrationEvent>>(new CadastroDeSolicitacaoExternaRecebidaIntegrationEventValidator());
        Mocker.Use<IValidator<NotificacaoConfiguracaoDto>>(new NotificacaoConfiguracaoValidator());
        Mocker.Use<IValidator<NotificacaoSonoraConfiguracaoDto>>(new NotificacaoSonoraConfiguracaoValidator());
    }
    
    private void AddUserContext()
    {
        var mock = new Mock<UserContextTest>(newExpression: () => new UserContextTest("", "usuario-anonimo", "Usuário Anonimo", "Usuário Anonimo", UserContextTest.PerfilSistema))
        {
            CallBase = true
        };
        
        Mocker.Use(mock.As<IUserContext>());
        Mocker.Use(mock.As<IUserContextTest>());
    }
   
    private void AddHttpContextAccessor()
    {
        const string token =
            "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.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.TQJshGhIpY-eeH-jbLDFK-1AMXztKman13af2hcnNQG1-CmCVpvb3YW65BtOZUYNfcSQQQkBqXAgy1WgYn8CnWoULo7J2kn-tBCMgWrTrIEzo6B36r4rRwHotAXxfv62_O32_-bcBEcZdClON0s1tSWW9OrX9CfMBd66RKVkN88_JpJxScuExDKmMZLxWv9-gE162n4ePx2yjfFcL6GqWX0sOSh-GUrm4H1J9qkggMzKCtXYN9Nyy0gsKdX-CiqMtMgEtwExuwjn4G6J1ZnUKfBzIBZTwyxG4_j8n7mp2RJBmQcs1WHQAsUfCrxCybEhi0V59uIimGu9p49Fcxza8g";
        
        var headers = new HeaderDictionary
        {
            { "Authorization", new StringValues(token) },
            { "AuthorizationUser", new StringValues(token) }
        };

        Mocker.GetMock<HttpContext>()
            .SetupGet(x => x.Request.Headers)
            .Returns(headers);
        
        Mocker.GetMock<IHttpContextAccessor>()
            .SetupGet(m => m.HttpContext)
            .Returns(Mocker.Get<HttpContext>());
    }
    
    private void AddNotificationContext()
    {
        Mocker.Use(new NotificationContext());
    }
    
    private void AddMediatorHandler()
    {
        var harness = _serviceProvider.GetRequiredService<ITestHarness>();
            
        var mock = new Mock<TestMediatorHandler>(() => new TestMediatorHandler(harness))
        {
            CallBase = true
        };
            
        Mocker.Use(mock.As<IMediatorHandler>());
    }
    
    private void AddMapper()
    {
        AddMappers();
    }
    
    private void AddMappers()
    {
        var mappingConfig = new MapperConfiguration(configuration =>
        {
            configuration.AddMaps(typeof(SolicitacaoApplicationReference).Assembly);
        });
        
        Mocker.Use<IMapper>(new Mapper(mappingConfig));
        
        AddConverter();
    }
    
    private void AddConverter()
    {
        Mocker.Use(new UsuarioConverter());
        Mocker.Use(new CadastroSolicitacaoDtoToSolicitacaoConverter());
    }

    private void AddOptions()
    {
        CreateAuthorizationSettings();
    }
    
    private void CreateAuthorizationSettings()
    {
        var settings = new AuthorizationSettings
        {
            Issuer = "https://popons.amcom.com.br/ons.pop.federation/",
            Audience = "SINAPSE",
            UseRsa = "true",
            RsaModulus = "wSxNKSkhfB1XR+fD/KZxK5nLEEHHBNrbSpiNw9FtcJHkvOiBXWI+G43Y1rvp6zp2/sjEqiXbQlFuMf2d/hM9ScIrdtrykf3m0OpDvhACFgwvvdiIaWOqIZ9oJCS9uzgEq7OGwH4gQklIOUbVrjZftXc0qFRR3XwkwGPGaNLsVpzSMeJHDJJReJe4MtztgsBS//AzkSdbhBpcAwQYOdmeQZTxL76miZqIHqAWAGQZgh/y3kHdfayhMb/hSgay933ITWyV2V7TUMBByYm6MOLuSRWTuloVIiwA/Nap5tgrQFdCuc34GCNgQIocn8qbcICI21AebnbyEyo96sONodToEQ==",
            RsaPublicExponent = "AQAB"
        };
        
        CreateMockSettings(settings);
    }

    private void CreateMockSettings<TSettings>(TSettings settings) where TSettings : class
    {
        var options = Options.Create(settings);
        Mocker.Use(options);
    }

    public void Dispose()
    {
        _servicesDependencyInjectorFactory.Dispose();
    }
}

