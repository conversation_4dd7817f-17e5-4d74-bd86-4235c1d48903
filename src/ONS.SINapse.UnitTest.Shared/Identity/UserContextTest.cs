using ONS.SINapse.Shared.Identity;

namespace ONS.SINapse.UnitTest.Shared.Identity;


public interface IUserContextTest : IUserContext
{
    void DefinirPerfil(Perfil perfil);
    void AdicionarScopeAoPerfil(Scope scope);
    void AdicionarOperationAoPerfil(string operation);
};

public class UserContextTest : IUserContextTest
{
    
    public static readonly Perfil PerfilSistema = new (new List<Scope>(), new List<string>(),"Sistema", "sistema");

    public static readonly IUserContextTest UsuarioAnonimo =
        new UserContextTest("", "usuario-anonimo", "Usuário Anonimo", "Usuário Anonimo", PerfilSistema);
    
    public UserContextTest(string token, string sid, string nome, string login, Perfil perfil)
        : this(
            [], 
            [],
            [],
            sid, 
            nome, 
            login, 
            perfil,
            token
            )
    {
    }
    
    public UserContextTest(
        List<string> roles, 
        List<string> scopes, 
        List<string> scopeRoles, 
        string sid, 
        string nome, 
        string login, 
        Perfil perfil, 
        string accessToken)
    {
        _roles = roles;
        _scopes = scopes;
        _scopeRoles = scopeRoles;
        Sid = sid;
        Nome = nome;
        Login = login;
        Perfil = perfil;
        AccessToken = accessToken;
        Centros = _scopes.Select(x => x.Split('/')[1].Trim()).ToList().AsReadOnly();
    }

    public string Sid { get; private set; }
    public string Nome { get; private set; }
    public string Login { get; private set; }
    public Perfil Perfil { get; private set; }
    public string AccessToken { get; private set; }
    public bool UsuarioAutenticado() => !string.IsNullOrWhiteSpace(AccessToken);

    private readonly List<string> _roles;
    public IReadOnlyCollection<string> Roles => _roles.AsReadOnly();

    private readonly List<string> _scopes;
    public IReadOnlyCollection<string> Scopes => _scopes.AsReadOnly();
    
    public IReadOnlyCollection<string> Centros { get; }

    private readonly List<string> _scopeRoles;
    public IReadOnlyCollection<string> ScopeRoles => _scopeRoles.AsReadOnly();

    public List<string> OperationsSelecionadas() 
        => Perfil.Operacoes
            .Distinct()
            .ToList();

    public bool PerfilSelecionadoPossuiOperacao(string operacao) 
        => OperationsSelecionadas().Contains(operacao);

    public void DefinirPerfil(Perfil perfil)
    {
        Perfil = perfil;
    }

    public void AdicionarScopeAoPerfil(Scope scope)
    {
        var scopes = Perfil.Scopes.ToList();
        var operation = Perfil.Operacoes.ToList();
        var nome = Perfil.Nome;
        var codigo = Perfil.Codigo;
        scopes.Add(scope);
        Perfil = new Perfil(scopes, operation, nome, codigo);
    }

    public void AdicionarOperationAoPerfil(string operation)
    {
        var scopes = Perfil.Scopes.ToList();
        var operations = Perfil.Operacoes.ToList();
        var nome = Perfil.Nome;
        var codigo = Perfil.Codigo;
        operations.Add(operation);
        Perfil = new Perfil(scopes, operations, nome, codigo);
    }
}