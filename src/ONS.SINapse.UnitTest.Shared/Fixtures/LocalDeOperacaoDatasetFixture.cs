using ONS.SINapse.Shared.DTO;

public class LocalDeOperacaoDatasetFixture : IDisposable
{
    public const string DescricaoPadrao = "Dataset de Teste";
    public const string FiltroPadrao = "filtro==teste";

    public static LocalDeOperacaoDataset GerarDatasetValido()
    {
        var locais = new List<LocalDeOperacaoDatasetItem>
        {
            new LocalDeOperacaoDatasetItem("EQ1", "Equipamento 1"),
            new LocalDeOperacaoDatasetItem("EQ2", "Equipamento 2")
        };

        return new LocalDeOperacaoDataset(DescricaoPadrao, FiltroPadrao, locais);
    }

    public static LocalDeOperacaoDataset GerarDatasetComLocaisVazios()
    {
        return new LocalDeOperacaoDataset(DescricaoPadrao, FiltroPadrao, new List<LocalDeOperacaoDatasetItem>());
    }

    public static LocalDeOperacaoDataset GerarDatasetComLocaisInvalidos()
    {
        var locais = new List<LocalDeOperacaoDatasetItem>
        {
            new LocalDeOperacaoDatasetItem(string.Empty, string.Empty)
        };

        return new LocalDeOperacaoDataset(DescricaoPadrao, FiltroPadrao, locais);
    }

    public virtual void Dispose()
    {
    }
}

