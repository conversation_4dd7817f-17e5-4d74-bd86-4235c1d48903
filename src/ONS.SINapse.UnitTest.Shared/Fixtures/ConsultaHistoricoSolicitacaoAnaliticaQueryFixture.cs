using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Solicitacao.QueryHandlers.Queries;

namespace ONS.SINapse.UnitTest.Shared.Fixtures;

public class ConsultaHistoricoSolicitacaoAnaliticaQueryFixture : IDisposable
{
    public ConsultaHistoricoSolicitacaoAnaliticaQuery GerarConsultaHistoricoSolicitacaoAnaliticaQuery(
        string? origem = "NE",
        string? destino = "ENGIE",
        string? mensagem = "mensagem de teste",
        StatusDeSolicitacao? status = StatusDeSolicitacao.Finalizada,
        DateTime? periodoInicial = null,
        DateTime? periodoFinal = null,
        bool? possuiImpedimento = false)
    {
        return new ConsultaHistoricoSolicitacaoAnaliticaQuery
        {
            Origem = origem,
            Destino = destino,
            Mensagem = mensagem,
            Status = status,
            PeriodoInicial = periodoInicial ?? DateTime.UtcNow.AddDays(-7),
            PeriodoFinal = periodoFinal ?? DateTime.UtcNow,
            PossuiImpedimento = possuiImpedimento
        };
    }

    public void Dispose() { }
}