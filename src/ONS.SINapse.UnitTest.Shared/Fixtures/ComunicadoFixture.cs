using ONS.SINapse.Entities.Entities;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Enums;

namespace ONS.SINapse.UnitTest.Shared.Fixtures;

public class ComunicadoFixture : IDisposable
{
    private readonly UsuarioFixture _usuarioFixture;
    private readonly ObjetoManobraFixture _objetoManobraFixture;

    public ComunicadoFixture()
    {
        _usuarioFixture = new UsuarioFixture();
        _objetoManobraFixture = new ObjetoManobraFixture();
    }

    public const string TituloPadrao = "Comunicado de Teste";
    public const string MensagemPadrao = "Esta é uma mensagem de teste para o comunicado.";
    public const string ComplementoPadrao = "Este é um complemento de teste.";
    public const string IdComunicadoPadrao = "64f1a2b3c4d5e6f7a8b9c0d1";

    public static ComunicadoDto GerarComunicadoDtoValido(
        TipoDeComunicado tipoDeComunicado = TipoDeComunicado.AreasEletricas,
        string titulo = TituloPadrao,
        string mensagem = MensagemPadrao)
    {
        var origem = new ObjetoDeManobraDto("SE", "CORS-SE");
        var destinatarios = new List<string> { "AGENTE001", "AGENTE002" };

        return new ComunicadoDto(origem, titulo, mensagem, tipoDeComunicado, destinatarios);
    }

    public static ComunicadoDto GerarComunicadoDtoTodos()
    {
        var origem = new ObjetoDeManobraDto("SE", "CORS-SE");
        var destinatarios = new List<string>();

        return new ComunicadoDto(origem, TituloPadrao, MensagemPadrao, TipoDeComunicado.Todos, destinatarios);
    }

    public static ComunicadoDto GerarComunicadoDtoCentrosRegionais()
    {
        var origem = new ObjetoDeManobraDto("SE", "CORS-SE");
        var destinatarios = new List<string> { "SE", "NE", "SUL" };

        return new ComunicadoDto(origem, TituloPadrao, MensagemPadrao, TipoDeComunicado.CentrosRegionais, destinatarios);
    }

    public static ComunicadoDto GerarComunicadoDtoInvalido()
    {
        var origem = new ObjetoDeManobraDto("", "");
        var destinatarios = new List<string>();

        return new ComunicadoDto(origem, "", "", TipoDeComunicado.AreasEletricas, destinatarios);
    }

    public static ComunicadoDto GerarComunicadoDtoComTituloLongo()
    {
        var origem = new ObjetoDeManobraDto("SE", "CORS-SE");
        var destinatarios = new List<string> { "AGENTE001" };
        var tituloLongo = new string('A', 500);

        return new ComunicadoDto(origem, tituloLongo, MensagemPadrao, TipoDeComunicado.AreasEletricas, destinatarios);
    }

    public static ComunicadoDto GerarComunicadoDtoComMensagemLonga()
    {
        var origem = new ObjetoDeManobraDto("SE", "CORS-SE");
        var destinatarios = new List<string> { "AGENTE001" };
        var mensagemLonga = new string('B', 2000);

        return new ComunicadoDto(origem, TituloPadrao, mensagemLonga, TipoDeComunicado.AreasEletricas, destinatarios);
    }

    public Comunicado GerarComunicadoValido(Usuario? usuario = null)
    {
        usuario ??= UsuarioFixture.GerarUsuarioValido();
        var comunicado = new Comunicado(usuario)
        {
            Titulo = TituloPadrao,
            Mensagem = MensagemPadrao,
            TipoDeComunicado = TipoDeComunicado.AreasEletricas,
            Origem = _objetoManobraFixture.GerarObjetoDeManobra("SE", "CORS-SE")
        };

        return comunicado;
    }

    public Comunicado GerarComunicadoComComplemento(Usuario? usuario = null)
    {
        var comunicado = GerarComunicadoValido(usuario);
        var origem = new ObjetoDeManobraDto("SE", "CORS-SE");
        comunicado.AdicionarComplemento(origem, ComplementoPadrao);
        return comunicado;
    }

    public Comunicado GerarComunicadoComLeitores(Usuario? usuario = null, int quantidadeLeitores = 2)
    {
        var comunicado = GerarComunicadoValido(usuario);
        
        for (int i = 0; i < quantidadeLeitores; i++)
        {
            var leitor = UsuarioFixture.GerarUsuarioValido($"leitor{i}@teste.com", $"Leitor {i}");
            var centros = new List<ObjetoDeManobraDto> { new("SE", "CORS-SE") };
            comunicado.AdicionarLeitor(leitor, DateTime.UtcNow, centros);
        }

        return comunicado;
    }

    public static ComplementoDeComunicadoDto GerarComplementoDeComunicadoDtoValido()
    {
        var origem = new ObjetoDeManobraDto("SE", "CORS-SE");
        return new ComplementoDeComunicadoDto(ComplementoPadrao, origem);
    }

    public static ComplementoDeComunicadoDto GerarComplementoDeComunicadoDtoInvalido()
    {
        var origem = new ObjetoDeManobraDto("SE", "CORS-SE");
        return new ComplementoDeComunicadoDto("", origem);
    }

    public static List<AreaEletricaComunicadoDto> GerarAreasEletricasValidas()
    {
        var agentes = new List<AgenteDto>
        {
            new("AGENTE001", "Agente 001"),
            new("AGENTE002", "Agente 002")
        };

        return
        [
            new("AREA001", "Área Elétrica 001", "SE", agentes),
            new("AREA002", "Área Elétrica 002", "SE", agentes)
        ];
    }

    public static List<string> GerarDestinatariosValidos()
    {
        return ["AGENTE001", "AGENTE002", "AGENTE003"];
    }

    public static List<string> GerarDestinatariosInvalidos()
    {
        return ["AGENTE_INEXISTENTE", "OUTRO_INEXISTENTE"];
    }

    public static List<string> GerarCentrosRegionaisValidos()
    {
        return ["SE", "NE", "SUL", "N", "NO"];
    }

    public static List<string> GerarCentrosRegionaisInvalidos()
    {
        return ["CENTRO_INEXISTENTE", "OUTRO_INEXISTENTE"];
    }

    public void Dispose()
    {
        _usuarioFixture?.Dispose();
        _objetoManobraFixture?.Dispose();
    }
}
