using ONS.SINapse.Entities.Entities;
using ONS.SINapse.Shared.DTO;

namespace ONS.SINapse.UnitTest.Shared.Fixtures;

public class UsuarioFixture : IDisposable
{
    public const string SidPadrao = "S-SID-USUARIO-TESTE";
    public const string LoginPadrao = "<EMAIL>";
    public const string NomePadrao = "Usuario Teste";

    public static Usuario GerarUsuarioValido() => GerarUsuarioValido(SidPadrao, LoginPadrao, NomePadrao);
    public static Usuario GerarUsuarioValido(string login, string nome) => GerarUsuarioValido(SidPadrao, login, nome);
    private static Usuario GerarUsuarioValido(string sid, string login, string nome) => new(sid, login, nome);

    public static UsuarioDto GerarUsuarioDtoValido() => GerarUsuarioDtoValido(SidPadrao, LoginPadrao, NomePadrao);
    public static UsuarioDto GerarUsuarioDtoValido(string login, string nome) => GerarUsuarioDtoValido(SidPadrao, login, nome);
    private static UsuarioDto GerarUsuarioDtoValido(string sid, string login, string nome) => new(sid, login, nome);


    public virtual void Dispose()
    {
    }
}