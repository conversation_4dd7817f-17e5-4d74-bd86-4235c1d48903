using ONS.SINapse.Entities.Entities;

namespace ONS.SINapse.UnitTest.Shared.Fixtures;

public class RespostaDaChamadaFixture : IDisposable
{
    private readonly ObjetoManobraFixture _objetoManobraFixture;

    public RespostaDaChamadaFixture()
    {
        _objetoManobraFixture = new ObjetoManobraFixture();
    }

    public RespostaDaChamada GeraRespostaDaChamada(string codigo = "CO", string nome = "NO", long chamada = 0)
        => new(_objetoManobraFixture.GerarObjetoDeManobra(codigo, nome), chamada);

    public void Dispose()
    {
        _objetoManobraFixture?.Dispose();
    }
}