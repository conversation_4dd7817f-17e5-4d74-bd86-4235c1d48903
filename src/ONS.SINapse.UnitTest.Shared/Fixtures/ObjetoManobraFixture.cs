using Bogus;
using ONS.SINapse.Entities.Entities;

namespace ONS.SINapse.UnitTest.Shared.Fixtures;

public class ObjetoManobraFixture : IDisposable
{
    public IReadOnlyCollection<ObjetoDeManobra> Centros =>
    [
        new("CN", "CNOS"),
        new("NE", "CORS-NE"),
        new("SE", "CORS-SE"),
        new("S", "CORS-S"),
        new("N", "CORS-N")
    ];

    public IReadOnlyCollection<ObjetoDeManobra> Agentes =>
    [
        new("GEN", "COTESA"),
        new("ENT", "ELETRONORTE"),
        new("GSU", "ENGIE"),
        new("ENL", "ENEL"),
        new("CHF", "CHESF")
    ];

    public IReadOnlyCollection<ObjetoDeManobra> Locais =>
    [
        new("MTCLR", "COLIDER"),
        new("SPBAR", "BARIRI"),
        new("MTUSAC", "USI S  APIACAS"),
        new("<PERSON>J<PERSON>", "JURUMIR<PERSON>"),
        new("RSUHPJ", "PASSO S JOAO")
    ];

    public ObjetoDeManobra GerarObjetoDeManobra(string codigo, string nome)
        => new(codigo, nome);

    public ObjetoDeManobra GerarObjetoDeManobra()
    {
        var objetos = Enumerable.Empty<ObjetoDeManobra>()
            .Union(Centros)
            .Union(Agentes)
            .Union(Locais);

        var faker = new Faker("pt_BR");
        return faker.PickRandom(objetos);
    }

    public (ObjetoDeManobra Origem, ObjetoDeManobra Destino, ObjetoDeManobra Local) GerarObjetosDeManobra()
    {
        var faker = new Faker("pt_BR");
        var origem = faker.PickRandom<ObjetoDeManobra>(Centros);
        var destino = faker.PickRandom<ObjetoDeManobra>(Agentes);
        var local = faker.PickRandom<ObjetoDeManobra>(Locais);

        return (origem, destino, local);
    }

    public void Dispose()
    {
    }
}