using ONS.SINapse.Entities.Entities;
using ONS.SINapse.Integracao.Shared.Enums;

namespace ONS.SINapse.UnitTest.Shared.Fixtures;

public class ControleDeChamadaFixture : IDisposable
{

    public ControleDeChamada GerarControleDeChamadaComStatusLiberadoParaChamada(int duracaoEmSegundos = 120)
        => GerarControleDeChamada(StatusControleDeChamada.LiberadoParaChamada, duracaoEmSegundos);

    public ControleDeChamada GerarControleDeChamadaComStatusChamadaEmAndamento(int duracaoEmSegundos = 120)
        => GerarControleDeChamada(StatusControleDeChamada.ChamadaEmAndamento, duracaoEmSegundos);

    public ControleDeChamada GerarControleDeChamadaComStatusProcessamentoPendente(int duracaoEmSegundos = 120)
        => GerarControleDeChamada(StatusControleDeChamada.ProcessamentoPendente, duracaoEmSegundos);

    public ControleDeChamada GerarControleDeChamadaComStatusProcessando(int duracaoEmSegundos = 120)
        => GerarControleDeChamada(StatusControleDeChamada.Processando, duracaoEmSegundos);

    public ControleDeChamada GerarControleDeChamadaComStatusProcessamentoFinalizado(int duracaoEmSegundos = 120)
        => GerarControleDeChamada(StatusControleDeChamada.ProcessamentoFinalizado, duracaoEmSegundos);

    private ControleDeChamada GerarControleDeChamada(StatusControleDeChamada status, int duracaoEmSegundos)
    => new()
    {
        Status = status,
        DuracaoEmSegundos = duracaoEmSegundos
    };


    public void Dispose()
    {
    }
}