using ONS.SINapse.Entities.Entities;
using ONS.SINapse.Shared.Identity;

namespace ONS.SINapse.UnitTest.Shared.Fixtures;

public class PerfilFixture : IDisposable
{
    public List<Scope> scopes = new([]);
    public List<string> operacoes = new([]);
    public const string nome = "Usuario Teste";
    public const string codigo = "Teste";

    public Perfil GerarPerfilValido() => GerarPerfilValido(scopes, operacoes, nome, codigo);
    public Perfil GerarPerfilValido(List<Scope> scopes, List<string> operacoes, string nome, string codigo) => new(scopes, operacoes, nome, codigo);

    public virtual void Dispose()
    {
    }
}