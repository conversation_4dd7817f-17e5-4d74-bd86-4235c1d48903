using ONS.SINapse.Shared.DTO;

namespace ONS.SINapse.UnitTest.Shared.Fixtures;

public class AlterarVersaoFixture
{
    public const string VersaoPadrao = "1.0.0";
    public const string DescricaoPadrao = "Versão inicial do sistema";
    public const string SidPadrao = "user-123";
    public const string UsuarioPadrao = "<PERSON>";

    public static AdicionarVersaoDto GerarAdicionarVersaoValido() =>
        new()
        {
            Versao = VersaoPadrao,
            Descricao = DescricaoPadrao
        };

    public static AdicionarVersaoDto GerarAdicionarVersaoComVersaoCustomizada(string versao) =>
        new()
        {
            Versao = versao,
            Descricao = DescricaoPadrao
        };

    public static AdicionarVersaoDto GerarAdicionarVersaoVazio() =>
        new()
        {
            Versao = string.Empty,
            Descricao = string.Empty
        };

    public static AdicionarVersaoDto GerarAdicionarVersaoComDadosLongos() =>
        new()
        {
            Versao = new string('1', 100) + ".0.0",
            Descricao = new string('D', 500)
        };

    public static EditarVersaoDto GerarEditarVersaoValido() =>
        new()
        {
            Descricao = "Descrição atualizada"
        };

    public static EditarVersaoDto GerarEditarVersaoComDescricaoCustomizada(string descricao) =>
        new()
        {
            Descricao = descricao
        };

    public static EditarVersaoDto GerarEditarVersaoVazio() =>
        new()
        {
            Descricao = string.Empty
        };
    
    public static IEnumerable<object[]> DadosVersaoValida()
    {
        yield return ["1.0.0", "Primeira versão"];
        yield return ["2.1.3", "Versão com correções"];
        yield return ["10.15.99", "Versão avançada"];
        yield return ["0.0.1", "Versão beta"];
    }

    public static IEnumerable<object[]> DadosDescricaoValida()
    {
        yield return ["Versão inicial"];
        yield return ["Correção de bugs críticos"];
        yield return ["Nova funcionalidade implementada"];
        yield return ["Melhoria de performance"];
    }
}
