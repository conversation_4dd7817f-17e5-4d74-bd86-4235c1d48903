using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.DTO.Firebase;

namespace ONS.SINapse.UnitTest.Shared.Fixtures;

public class AlterarVersaoFixture
{
    public const string VersaoPadrao = "1.0.0";
    public const string DescricaoPadrao = "Versão inicial do sistema";
    public const string SidPadrao = "user-123";
    public const string UsuarioPadrao = "João Silva";

    public static AdicionarVersaoDto GerarAdicionarVersaoValido() =>
        new()
        {
            Versao = VersaoPadrao,
            Descricao = DescricaoPadrao
        };

    public static AdicionarVersaoDto GerarAdicionarVersaoComVersaoCustomizada(string versao) =>
        new()
        {
            Versao = versao,
            Descricao = DescricaoPadrao
        };

    public static AdicionarVersaoDto GerarAdicionarVersaoComDescricaoCustomizada(string descricao) =>
        new()
        {
            Versao = VersaoPadrao,
            Descricao = descricao
        };

    public static AdicionarVersaoDto GerarAdicionarVersaoVazio() =>
        new()
        {
            Versao = string.Empty,
            Descricao = string.Empty
        };

    public static AdicionarVersaoDto GerarAdicionarVersaoComDadosLongos() =>
        new()
        {
            Versao = new string('1', 100) + ".0.0",
            Descricao = new string('D', 500)
        };

    public static EditarVersaoDto GerarEditarVersaoValido() =>
        new()
        {
            Descricao = "Descrição atualizada"
        };

    public static EditarVersaoDto GerarEditarVersaoComDescricaoCustomizada(string descricao) =>
        new()
        {
            Descricao = descricao
        };

    public static EditarVersaoDto GerarEditarVersaoVazio() =>
        new()
        {
            Descricao = string.Empty
        };

    public static EditarVersaoDto GerarEditarVersaoComDescricaoLonga() =>
        new()
        {
            Descricao = new string('A', 1000)
        };

    public static VersaoFirebaseDto GerarVersaoFirebaseDto(
        string versao = VersaoPadrao,
        string descricao = DescricaoPadrao,
        string sid = SidPadrao,
        string usuario = UsuarioPadrao,
        DateTime? dataCadastro = null) =>
        new(
            dataCadastro ?? DateTime.UtcNow,
            descricao,
            sid,
            usuario,
            versao
        );

    public static List<AdicionarVersaoDto> GerarListaAdicionarVersao(int quantidade)
    {
        var lista = new List<AdicionarVersaoDto>();
        for (int i = 1; i <= quantidade; i++)
        {
            lista.Add(new AdicionarVersaoDto
            {
                Versao = $"{i}.0.0",
                Descricao = $"Versão {i} do sistema"
            });
        }
        return lista;
    }

    public static List<EditarVersaoDto> GerarListaEditarVersao(int quantidade)
    {
        var lista = new List<EditarVersaoDto>();
        for (int i = 1; i <= quantidade; i++)
        {
            lista.Add(new EditarVersaoDto
            {
                Descricao = $"Descrição editada {i}"
            });
        }
        return lista;
    }

    // Dados para testes de validação
    public static IEnumerable<object[]> DadosVersaoValida()
    {
        yield return ["1.0.0", "Primeira versão"];
        yield return ["2.1.3", "Versão com correções"];
        yield return ["10.15.99", "Versão avançada"];
        yield return ["0.0.1", "Versão beta"];
    }

    public static IEnumerable<object[]> DadosDescricaoValida()
    {
        yield return ["Versão inicial"];
        yield return ["Correção de bugs críticos"];
        yield return ["Nova funcionalidade implementada"];
        yield return ["Melhoria de performance"];
    }

    public static IEnumerable<object[]> DadosVersaoInvalida()
    {
        yield return ["", "Versão vazia"];
        yield return ["   ", "Versão com espaços"];
        yield return ["abc", "Versão não numérica"];
        yield return ["1.0", "Versão incompleta"];
    }

    public static IEnumerable<object[]> DadosDescricaoInvalida()
    {
        yield return [""];
        yield return ["   "];
        yield return [new string('A', 2000)]; // Muito longa
    }
}
