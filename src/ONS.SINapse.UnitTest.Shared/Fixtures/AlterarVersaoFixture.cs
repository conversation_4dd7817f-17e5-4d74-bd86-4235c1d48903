using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.DTO.Firebase;

namespace ONS.SINapse.UnitTest.Shared.Fixtures;

public class AlterarVersaoFixture : IDisposable
{
    public const string VersaoPadrao = "1.0.0";
    public const string DescricaoPadrao = "Versão inicial do sistema";
    public const string SidPadrao = "user-123";
    public const string UsuarioPadrao = "João Silva";

    public static AdicionarVersaoDto GerarAdicionarVersaoValido() =>
        new()
        {
            Versao = VersaoPadrao,
            Descricao = DescricaoPadrao
        };

    public static AdicionarVersaoDto GerarAdicionarVersaoComVersaoCustomizada(string versao) =>
        new()
        {
            Versao = versao,
            Descricao = DescricaoPadrao
        };

    public static AdicionarVersaoDto GerarAdicionarVersaoComDescricaoCustomizada(string descricao) =>
        new()
        {
            Versao = VersaoPadrao,
            Descricao = descricao
        };

    public static AdicionarVersaoDto GerarAdicionarVersaoVazio() =>
        new()
        {
            Versao = string.Empty,
            Descricao = string.Empty
        };

    public static AdicionarVersaoDto GerarAdicionarVersaoComDadosLongos() =>
        new()
        {
            Versao = new string('1', 100) + ".0.0",
            Descricao = new string('D', 500)
        };

    public static EditarVersaoDto GerarEditarVersaoValido() =>
        new()
        {
            Descricao = "Descrição atualizada"
        };

    public static EditarVersaoDto GerarEditarVersaoComDescricaoCustomizada(string descricao) =>
        new()
        {
            Descricao = descricao
        };

    public static EditarVersaoDto GerarEditarVersaoVazio() =>
        new()
        {
            Descricao = string.Empty
        };

    public static EditarVersaoDto GerarEditarVersaoComDescricaoLonga() =>
        new()
        {
            Descricao = new string('A', 1000)
        };

    public static VersaoFirebaseDto GerarVersaoFirebaseDto(
        string versao = VersaoPadrao,
        string descricao = DescricaoPadrao,
        string sid = SidPadrao,
        string usuario = UsuarioPadrao,
        DateTime? dataCadastro = null) =>
        new(
            dataCadastro ?? DateTime.UtcNow,
            descricao,
            sid,
            usuario,
            versao
        );

    public static List<AdicionarVersaoDto> GerarListaAdicionarVersao(int quantidade)
    {
        var lista = new List<AdicionarVersaoDto>();
        for (int i = 1; i <= quantidade; i++)
        {
            lista.Add(new AdicionarVersaoDto
            {
                Versao = $"{i}.0.0",
                Descricao = $"Versão {i} do sistema"
            });
        }
        return lista;
    }

    public static List<EditarVersaoDto> GerarListaEditarVersao(int quantidade)
    {
        var lista = new List<EditarVersaoDto>();
        for (int i = 1; i <= quantidade; i++)
        {
            lista.Add(new EditarVersaoDto
            {
                Descricao = $"Descrição editada {i}"
            });
        }
        return lista;
    }

    // Dados para testes de validação
    public static IEnumerable<object[]> DadosVersaoValida()
    {
        yield return new object[] { "1.0.0", "Primeira versão" };
        yield return new object[] { "2.1.3", "Versão com correções" };
        yield return new object[] { "10.15.99", "Versão avançada" };
        yield return new object[] { "0.0.1", "Versão beta" };
    }

    public static IEnumerable<object[]> DadosDescricaoValida()
    {
        yield return new object[] { "Versão inicial" };
        yield return new object[] { "Correção de bugs críticos" };
        yield return new object[] { "Nova funcionalidade implementada" };
        yield return new object[] { "Melhoria de performance" };
    }

    public static IEnumerable<object[]> DadosVersaoInvalida()
    {
        yield return new object[] { "", "Versão vazia" };
        yield return new object[] { "   ", "Versão com espaços" };
        yield return new object[] { "abc", "Versão não numérica" };
        yield return new object[] { "1.0", "Versão incompleta" };
    }

    public static IEnumerable<object[]> DadosDescricaoInvalida()
    {
        yield return new object[] { "" };
        yield return new object[] { "   " };
        yield return new object[] { new string('A', 2000) }; // Muito longa
    }

    // Métodos de validação para testes
    public static void ValidarVersaoFirebaseDto(
        VersaoFirebaseDto versaoFirebase,
        string versaoEsperada,
        string descricaoEsperada,
        string sidEsperado,
        string usuarioEsperado)
    {
        versaoFirebase.Versao.ShouldBe(versaoEsperada);
        versaoFirebase.Descricao.ShouldBe(descricaoEsperada);
        versaoFirebase.Sid.ShouldBe(sidEsperado);
        versaoFirebase.Usuario.ShouldBe(usuarioEsperado);
        versaoFirebase.DataCadastro.ShouldBeInRange(DateTime.UtcNow.AddMinutes(-1), DateTime.UtcNow.AddMinutes(1));
    }

    public static void ValidarAdicionarVersaoDto(AdicionarVersaoDto dto, string versaoEsperada, string descricaoEsperada)
    {
        dto.Versao.ShouldBe(versaoEsperada);
        dto.Descricao.ShouldBe(descricaoEsperada);
    }

    public static void ValidarEditarVersaoDto(EditarVersaoDto dto, string descricaoEsperada)
    {
        dto.Descricao.ShouldBe(descricaoEsperada);
    }

    public virtual void Dispose()
    {
        // Cleanup if needed
    }
}
