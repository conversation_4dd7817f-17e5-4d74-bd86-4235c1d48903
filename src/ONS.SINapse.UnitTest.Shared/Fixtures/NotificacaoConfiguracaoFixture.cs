using ONS.SINapse.Shared.DTO.Firebase;

namespace ONS.SINapse.UnitTest.Shared.Fixtures;

public class NotificacaoConfiguracaoFixture : IDisposable
{
    public const string TipoPadrao = "Alerta";
    public const string AudioPadrao = "audio_teste.mp3";
    public const bool AtivoPadrao = true;

    public static NotificacaoConfiguracaoDto GerarNotificacaoConfiguracaoValida() =>
        new(
        [
                GerarNotificacaoSonoraValida()
        ]);

    public static NotificacaoConfiguracaoDto GerarNotificacaoConfiguracaoComListaVazia() =>
        new([]);

    public static NotificacaoConfiguracaoDto GerarNotificacaoConfiguracaoInvalida() =>
        new([GerarNotificacaoSonoraInvalida()]);

    public static NotificacaoSonoraConfiguracaoDto GerarNotificacaoSonoraValida() =>
        new(T<PERSON><PERSON>Padrao, AudioPadrao, AtivoPadrao);

    public static NotificacaoSonoraConfiguracaoDto GerarNotificacaoSonoraInvalida() =>
        new(string.Empty, string.Empty, AtivoPadrao);

    public virtual void Dispose()
    {
    }
}
