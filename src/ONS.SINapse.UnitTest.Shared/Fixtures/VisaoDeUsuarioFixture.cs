using ONS.SINapse.Shared.DTO;

namespace ONS.SINapse.UnitTest.Shared.Fixtures;

public class VisaoDeUsuarioFixture : IDisposable
{
    public static VisaoDeUsuarioDto GerarVisaoValida(string codigoCentroAgente = "NE", string nome = "VisaoTeste")
    {
        return new VisaoDeUsuarioDto
        {
            CodigoCentroAgente = codigoCentroAgente,
            Nome = nome,
            EquipamentosDeManobra = new List<EquipamentoVisaoDeUsuarioDto>
            {
                new EquipamentoVisaoDeUsuarioDto("EQ1", "Equipamento 1", new TipoDeEquipamentoVisaoDeUsuarioDto("TIPO1", "Tipo 1"))
            },
            Tags = new List<EquipamentoVisaoDeUsuarioDto>
            {
                new EquipamentoVisaoDeUsuarioDto("TAG1", "Tag 1", new TipoDeEquipamentoVisaoDeUsuarioDto("TIPO2", "Tipo 2"))
            }
        };
    }

    public static VisaoDeUsuarioDto GerarVisaoSemEquipamentosETags(string codigoCentroAgente = "NE", string nome = "VisaoInvalida")
    {
        return new VisaoDeUsuarioDto
        {
            CodigoCentroAgente = codigoCentroAgente,
            Nome = nome,
            EquipamentosDeManobra = new List<EquipamentoVisaoDeUsuarioDto>(),
            Tags = new List<EquipamentoVisaoDeUsuarioDto>()
        };
    }

    public static VisaoDeUsuarioDto GerarVisaoComNomeEmUso(string codigoCentroAgente = "NE")
    {
        return GerarVisaoValida(codigoCentroAgente, "NomeEmUso");
    }

    public void Dispose()
    {
    }
}
