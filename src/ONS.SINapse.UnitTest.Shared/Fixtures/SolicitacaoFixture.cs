using ONS.SINapse.Entities.Entities;
using ONS.SINapse.Shared.Factories;

namespace ONS.SINapse.UnitTest.Shared.Fixtures;

public class SolicitacaoFixture : IDisposable
{
    private readonly ObjetoManobraFixture _objetoManobraFixture;
    private readonly UsuarioFixture _usuarioFixture;

    public SolicitacaoFixture()
    {
        _objetoManobraFixture = new ObjetoManobraFixture();
        _usuarioFixture = new UsuarioFixture();
    }

    public Entities.Entities.Solicitacao GerarSolicitacaoPendente(ObjetoDeManobra? origem = null, ObjetoDeManobra? destino = null) 
        => GerarSolicitacao(origem, destino);
    

    public Entities.Entities.Solicitacao GerarSolicitacaoConfirmada(ObjetoDeManobra? origem = null, ObjetoDeManobra? destino = null, Usuario? usuario = null)
    {
        usuario ??= UsuarioFixture.GerarUsuarioValido();

        var solicitacao = GerarSolicitacaoPendente(origem, destino);
        
        solicitacao.Confirmar(usuario);
        
        return solicitacao;
    }

    public Entities.Entities.Solicitacao GerarSolicitacaoImpedida(string motivo, Usuario? usuario = null)
    {
        usuario ??= UsuarioFixture.GerarUsuarioValido();
        
        var solicitacao = GerarSolicitacaoPendente();
        
        solicitacao.Impedir(motivo, usuario);
        
        return solicitacao;
    }

    public Entities.Entities.Solicitacao GerarSolicitacaoConfirmadaImpedida(string motivo, Usuario? usuario = null)
    {
        usuario ??= UsuarioFixture.GerarUsuarioValido();
        
        var solicitacao = GerarSolicitacaoConfirmada();
        
        solicitacao.Impedir(motivo, usuario);
        
        return solicitacao;
    }

    public Entities.Entities.Solicitacao GerarSolicitacaoCienciaInformada(Usuario? usuario = null)
    {
        usuario ??= UsuarioFixture.GerarUsuarioValido();
        var solicitacao = GerarSolicitacaoImpedida("Motivo para informar Ciência", 
            UsuarioFixture.GerarUsuarioValido("<EMAIL>", "Solicitação Impedida"));
        
        solicitacao.InformarCiencia(usuario);
        
        return solicitacao;
    }

    public Entities.Entities.Solicitacao GerarSolicitacaoCancelada(Usuario? usuario = null)
    {
        usuario ??= UsuarioFixture.GerarUsuarioValido();
        var solicitacao = GerarSolicitacaoPendente();
        solicitacao.Cancelar(usuario);
        
        return solicitacao;
    }

    public Entities.Entities.Solicitacao GerarSolicitacaoEncaminhada(ObjetoDeManobra? origem = null, Usuario ? usuario = null)
    {
        usuario ??= UsuarioFixture.GerarUsuarioValido();
        var solicitacao = GerarSolicitacaoPendente(origem);
        solicitacao.Encaminhar(usuario);

        return solicitacao;
    }

    private Entities.Entities.Solicitacao GerarSolicitacao(ObjetoDeManobra? origem = null, ObjetoDeManobra? destino = null)
    {
        var objetosDeManobra = _objetoManobraFixture.GerarObjetosDeManobra();
        origem ??= objetosDeManobra.Origem;
        destino ??= objetosDeManobra.Destino;
        
        var usuario = UsuarioFixture.GerarUsuarioValido();
        var mensagem = $"{objetosDeManobra.Local} | Elevar Geração";
        var encaminharPara = objetosDeManobra.Destino;
        string? informacaoAdicional = null;
        var loteId = Guid.NewGuid().ToString();
        string? motivo = null;
        string? solicitacaoOrigem = null;
        string[] tags = ["Geração", "Elevação"];
        const string sistemaDeOrigem = Entities.Entities.Solicitacao.SistemaDeOrigemInterno;

        var solicitacao = new Entities.Entities.Solicitacao(
            IdSolicitacaoFactory.GerarNovoId(origem.Codigo, destino.Codigo),
            usuario,
            origem,
            destino,
            mensagem,
            tags,
            sistemaDeOrigem
        )
        {
            Local = objetosDeManobra.Local,
            EncaminharPara = encaminharPara,
            InformacaoAdicional = informacaoAdicional,
            LoteId = loteId,
            Motivo = motivo,
            SolicitacaoDeOrigemId = solicitacaoOrigem
        };

        return solicitacao;
    }
    
    
    
    public void Dispose()
    {
        _objetoManobraFixture.Dispose();
        _usuarioFixture.Dispose();
    }
}

