using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Solicitacao.QueryHandlers.Queries;

namespace ONS.SINapse.UnitTest.Shared.Fixtures;

public class ConsultaHistoricoSolicitacaoQueryFixture : IDisposable
{
    public ConsultaHistoricoSolicitacaoQuery GerarConsultaHistoricoSolicitacaoQuery(
        string? origem = "NE",
        string? destino = "ENGIE",
        string? mensagem = "Mensagem de teste",
        StatusDeSolicitacao? status = StatusDeSolicitacao.Finalizada,
        DateTime? periodoInicial = null,
        DateTime? periodoFinal = null,
        int pagina = 1,
        int limite = 50)
    {
        return new()
        {
            Origem = origem,
            Destino = destino,
            Mensagem = mensagem,
            Status = status,
            PeriodoInicial = periodoInicial ?? DateTime.UtcNow.AddDays(-7),
            PeriodoFinal = periodoFinal ?? DateTime.UtcNow,
            Pagina = pagina,
            Limite = limite
        };
    }

    public void Dispose() { }
}
