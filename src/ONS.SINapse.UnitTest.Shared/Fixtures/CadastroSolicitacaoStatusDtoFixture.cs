using ONS.SINapse.Entities.Entities;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Solicitacao.QueryHandlers.Dtos;

namespace ONS.SINapse.UnitTest.Shared.Fixtures;

public class CadastroSolicitacaoStatusDtoFixture : IDisposable
{
    public CadastroSolicitacaoStatusDto GerarCadastroSolicitacaoStatusDto(
        string? id = null,
        string? codigoExterno = null,
        StatusDeSolicitacao statusAtual = StatusDeSolicitacao.Confirmada,
        string? justificativa = null,
        ICollection<HistoricoDeStatusDeSolicitacao>? historicos = null,
        List<ChatDeSolicitacao>? chat = null,
        Usuario? usuario = null)
    {
        var usuarioFinal = usuario ?? new Usuario("sid-123", "usuario.teste", "Usuário Teste");

        historicos ??= new List<HistoricoDeStatusDeSolicitacao>
        {
            new HistoricoDeStatusDeSolicitacao(usuarioFinal)
            {
                Status = statusAtual,
                StatusAnterior = StatusDeSolicitacao.Pendente,
                DataDeAlteracao = DateTime.UtcNow
            }
        };

        chat ??= new List<ChatDeSolicitacao>
        {
            new ChatDeSolicitacao("Mensagem de teste", usuarioFinal, new ObjetoDeManobra("OBJ1", "Origem"), statusAtual)
        };

        return new CadastroSolicitacaoStatusDto
        {
            Id = id ?? Guid.NewGuid().ToString(),
            CodigoExterno = codigoExterno ?? "EXT123",
            StatusAtualId = statusAtual,
            Justificativa = justificativa ?? "Sem justificativa",
            HistoricosDeStatus = historicos,
            Chat = chat
        };
    }

    public void Dispose() { }
}