using ONS.SINapse.Entities.Entities.DadosCadastrais;

namespace ONS.SINapse.UnitTest.Shared.Fixtures;

public class AgenteFixture : IDisposable
{
    public Agente GerarAgente(string codigo = "CO", 
                              string nome = "NO",        
                              ICollection<string> centros = null,
                              ICollection<string> areasEletricas = null,
                              List<CentroDoAgente> centrosDoAgente = null,
                              bool validarGeolocalizacao = true)
        => new(codigo, nome, centros, areasEletricas, centrosDoAgente, validarGeolocalizacao);
    public void Dispose()
    {
    }
}