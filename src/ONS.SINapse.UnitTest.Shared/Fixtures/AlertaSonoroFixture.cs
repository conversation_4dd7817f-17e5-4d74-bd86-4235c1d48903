using ONS.SINapse.Shared.DTO.Firebase;
using ONS.SINapse.Shared.Identity;

namespace ONS.SINapse.UnitTest.Shared.Fixtures;

public class AlertaSonoroFixture
{
    public const string SidPadrao = "user-123";
    public const string Nome<PERSON>ra<PERSON> = "<PERSON><PERSON><PERSON>";
    public const string Login<PERSON>rao = "j.silva";
    public const string TokenPadrao = "token-teste";

    public static List<Scope> GerarScopesValidos() =>
        new()
        {
            new("CENTROS", "NE", "CORS-NE"),
            new("CENTROS", "SE", "CORS-SE"),
            new("CENTROS", "SUL", "CORS-S")
        };

    public static List<Scope> GerarScopeUnico() =>
        new()
        {
            new("CENTROS", "N", "CORS-NCO")
        };

    public static List<Scope> GerarScopesComCaracteresEspeciais() =>
        new()
        {
            new("CENTROS", "SE", "CORS-SE"),
            new("CENTROS", "NE", "CORS-NE"),
            new("CENTROS", "S", "CORS-S")
        };

    public static List<Scope> GerarScopesDuplicados() =>
        new()
        {
            new("CENTROS", "DUPLICADO", "Duplicado 1"),
            new("CENTROS", "DUPLICADO", "Duplicado 2"),
            new("CENTROS", "UNICO", "Único")
        };

    public static List<Scope> GerarScopesComCodigosLongos() =>
        new()
        {
            new("CENTROS", new string('A', 100), "Código Muito Longo")
        };

    public static List<Scope> GerarScopesVazios() =>
        new();

    public static List<Scope> GerarScopesParaPerformance(int quantidade)
    {
        var scopes = new List<Scope>();
        for (int i = 1; i <= quantidade; i++)
        {
            scopes.Add(new Scope("CENTROS", $"PERF-{i:000}", $"Performance {i}"));
        }
        return scopes;
    }

    public static List<Scope> GerarScopesParametrizados(int quantidade)
    {
        var scopes = new List<Scope>();
        for (int i = 1; i <= quantidade; i++)
        {
            scopes.Add(new Scope("CENTROS", $"SCOPE-{i}", $"Scope {i}"));
        }
        return scopes;
    }

    public static Perfil GerarPerfilComScopes(List<Scope> scopes) =>
        new(scopes, new List<string>(), "Operador", "operador");

    public static Perfil GerarPerfilComOperacoes(List<Scope> scopes) =>
        new(scopes, new List<string> { "CRIAR_SOLICITACAO", "EDITAR_SOLICITACAO", "EXCLUIR_SOLICITACAO" }, "Operador Completo", "operador-completo");

    public static Perfil GerarPerfilSupervisor(List<Scope> scopes) =>
        new(scopes, new List<string>(), "Supervisor", "supervisor");

    public static Perfil GerarPerfilAdministrador(List<Scope> scopes) =>
        new(scopes, new List<string>(), "Administrador", "administrador");

    public static AlertaSonoroRealtimeDto GerarAlertaSonoroDto(string sid = SidPadrao, string usuario = NomePadrao, bool ativo = true) =>
        new(sid, usuario, DateTime.UtcNow, ativo);

    public static Dictionary<string, AlertaSonoroRealtimeDto> GerarDicionarioAlertas(List<Scope> scopes, string sid = SidPadrao, string usuario = NomePadrao, bool ativo = true)
    {
        var dicionario = new Dictionary<string, AlertaSonoroRealtimeDto>();
        
        foreach (var scope in scopes)
        {
            var chave = scope.Codigo.ToLower();
            if (!dicionario.ContainsKey(chave))
            {
                dicionario[chave] = GerarAlertaSonoroDto(sid, usuario, ativo);
            }
        }
        
        return dicionario;
    }
}
