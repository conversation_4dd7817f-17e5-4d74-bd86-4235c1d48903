using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Solicitacao.QueryHandlers.Queries;

namespace ONS.SINapse.UnitTest.Shared.Fixtures;

public class HistoricoConsultaAnaliticaDtoFixture : IDisposable
{
    public HistoricoConsultaAnaliticaDto GerarHistoricoConsultaAnaliticaDto(
        string? codigo = null,
        string origem = "ORIGEM123",
        string destino = "DESTINO456",
        string mensagem = "Mensagem de teste",
        DateTime? dataDeCadastro = null,
        string usuarioDeCadastro = "usuario.cadastro",
        StatusDeSolicitacao status = StatusDeSolicitacao.Finalizada,
        DateTime? dataAlteracaoStatus = null,
        string usuarioAlteracaoStatus = "usuario.alteracao",
        string entreguePara = "usuario.destino",
        DateTime? dataEntrega = null,
        string lidaPeloUsuario = "usuario.destino",
        DateTime? dataLeitura = null,
        string motivoImpedimento = "Nenhum",
        string informacaoAdicional = "Informação extra")
    {
        return new HistoricoConsultaAnaliticaDto
        {
            Codigo = codigo ?? Guid.NewGuid().ToString(),
            Origem = origem,
            Destino = destino,
            Mensagem = mensagem,
            DataDeCadastro = dataDeCadastro ?? DateTime.UtcNow.AddMinutes(-30),
            UsuarioDeCadastro = usuarioDeCadastro,
            CodigoStatus = (int)status,
            DataDaAlteracaoDeStatus = dataAlteracaoStatus ?? DateTime.UtcNow.AddMinutes(-10),
            UsuarioDaAlteracaoDeStatus = usuarioAlteracaoStatus,
            EntregueParaOUsuario = entreguePara,
            DataDeEntrega = dataEntrega ?? DateTime.UtcNow.AddMinutes(-5),
            LidaPeloUsuario = lidaPeloUsuario,
            DataDeLeitura = dataLeitura ?? DateTime.UtcNow,
            MotivoImpedimento = motivoImpedimento,
            InformacaoAdicional = informacaoAdicional
        };
    }

    public void Dispose() { }
}

