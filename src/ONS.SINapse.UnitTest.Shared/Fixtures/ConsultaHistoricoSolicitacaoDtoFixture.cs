using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Solicitacao.QueryHandlers.Dtos;

namespace ONS.SINapse.UnitTest.Shared.Fixtures;

public class ConsultaHistoricoSolicitacaoDtoFixture : IDisposable
{
    public ConsultaHistoricoSolicitacaoDto GerarConsultaHistoricoSolicitacaoDto(
        string? id = null,
        string origemCodigo = "NE",
        string origemNome = "CORS-NE",
        string destinoCodigo = "ENGIE",
        string destinoNome = "ENGIE",
        string mensagem = "Mensagem de teste",
        string mensagemNormalizada = "mensagem de teste",
        string? informacaoAdicional = "Informação adicional",
        bool finalizadaAutomaticamente = false,
        int codigoStatus = (int)StatusDeSolicitacao.Finalizada,
        DateTime? dataCriacao = null,
        DateTime? dataUltimaAlteracao = null,
        string? nomeResponsavel = "Usuário Responsável"
    )
    {
        var origem = new ConsultaHistoricoSolicitacaoObjetoManobraDto { Nome = origemNome, Codigo = origemCodigo };
        var destino = new ConsultaHistoricoSolicitacaoObjetoManobraDto { Nome = destinoNome, Codigo = destinoCodigo };

        var usuario = new ConsultaHistoricoSolicitacaoUsuarioDto(nomeResponsavel ?? "Usuário");
        var historico = new ConsultaHistoricoSolicitacaoHistoricoDto(usuario)
        {
            DataDeAlteracao = dataUltimaAlteracao ?? DateTime.UtcNow
        };

        var dto = new ConsultaHistoricoSolicitacaoDto(
            id ?? Guid.NewGuid().ToString(),
            origem,
            destino,
            mensagem,
            mensagemNormalizada
        )
        {
            InformacaoAdicional = informacaoAdicional,
            FinalizadaAutomaticamente = finalizadaAutomaticamente,
            CodigoStatus = codigoStatus,
            DataDeCriacao = dataCriacao ?? DateTime.UtcNow.AddMinutes(-15),
            Historicos = [historico]
        };

        return dto;
    }

    public void Dispose() { }
}
