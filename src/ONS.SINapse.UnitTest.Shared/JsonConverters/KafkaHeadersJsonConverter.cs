using System.Text;
using Confluent.Kafka;
using Newtonsoft.Json;

namespace ONS.SINapse.UnitTest.Shared.JsonConverters;

public class KafkaHeadersJsonConverter : JsonConverter<Headers>
{
    public override void Write<PERSON>son(JsonWriter writer, Headers? value, JsonSerializer serializer)
    {
        if (value == null)
        {
            writer.WriteNull();
            return;
        }
        
        var dict = new Dictionary<string, string?>();
        
        foreach (var header in value)
            dict[header.Key] = header.GetValueBytes() != null ? Encoding.UTF8.GetString(header.GetValueBytes()) : null;

        serializer.Serialize(writer, dict);
    }

    public override Headers ReadJson(JsonReader reader, Type objectType, Headers? existingValue, bool hasExistingValue, JsonSerializer serializer)
    {
        var dict = serializer.Deserialize<Dictionary<string, string>>(reader);
        
        if (dict == null)
            return [];
        
        var headers = new Headers();
        
        foreach (var kvp in dict)
            headers.Add(kvp.Key, Encoding.UTF8.GetBytes(kvp.Value));

        return headers;
    }
}
