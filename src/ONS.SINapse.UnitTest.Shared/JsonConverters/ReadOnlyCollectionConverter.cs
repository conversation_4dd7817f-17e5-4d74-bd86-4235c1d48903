using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Collections.ObjectModel;

namespace ONS.SINapse.UnitTest.Shared.JsonConverters;

public class ReadOnlyCollectionConverter : JsonConverter
{
    public override bool CanConvert(Type objectType)
    {
        // Se for IReadOnlyCollection<T>
        return objectType.IsGenericType &&
               objectType.GetGenericTypeDefinition() == typeof(IReadOnlyCollection<>);
    }

    public override object? ReadJson(JsonReader reader, Type objectType, object? existingValue, JsonSerializer serializer)
    {
        // Carrega o token inteiro (pode ser um objeto com metadados ou um array)
        var token = JToken.Load(reader);

        // Se for um objeto (com metadados, como $type e $values)
        if (token.Type == JTokenType.Object)
        {
            var obj = (JObject)token;
            // Remove o metadado $type, se existir
            obj.Remove("$type");

            // Se houver um $values, essa é a coleção real
            if (obj.TryGetValue("$values", out var valuesToken))
            {
                token = valuesToken;
            }
        }

        // Obtém o tipo de elemento (por exemplo, string)
        var elementType = objectType.GetGenericArguments()[0];
        // Cria o tipo concreto: List<elementType>
        var listType = typeof(List<>).MakeGenericType(elementType);
        // Desserializa o token (que agora deverá ser um array) para uma List<elementType>
        var list = token.ToObject(listType, serializer);

        var readOnlyType = typeof(ReadOnlyCollection<>).MakeGenericType(elementType);
        var constructor = readOnlyType.GetConstructor(new[] { typeof(IList<>).MakeGenericType(elementType) });
        if (constructor == null)
            throw new JsonSerializationException($"Não foi possível encontrar o construtor para {readOnlyType.Name}");

        return constructor.Invoke(new[] { list });
    }

    public override void WriteJson(JsonWriter writer, object? value, JsonSerializer serializer)
    {
        if (value == null)
        {
            writer.WriteNull();
            return;
        }

        var elementType = value.GetType().GetGenericArguments()[0];
        var listType = typeof(List<>).MakeGenericType(elementType);
        var list = Activator.CreateInstance(listType);
        foreach (var item in (IEnumerable<object>)value)
        {
            listType.GetMethod("Add")?.Invoke(list, new[] { item });
        }

        serializer.Serialize(writer, list);
    }
}
