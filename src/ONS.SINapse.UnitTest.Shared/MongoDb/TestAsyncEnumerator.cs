namespace ONS.SINapse.UnitTest.Shared.MongoDb;

public class TestAsyncEnumerator<T> : IAsyncEnumerator<T>
{
    private readonly IEnumerator<T> _enumerator;

    public TestAsyncEnumerator(IEnumerator<T> enumerator)
    {
        _enumerator = enumerator ?? throw new ArgumentNullException(nameof(enumerator));
    }

    public T Current => _enumerator.Current;

    public ValueTask DisposeAsync()
    {
        _enumerator.Dispose();
        GC.SuppressFinalize(this);
        return new ValueTask();
    }

    public ValueTask<bool> MoveNextAsync() => new(_enumerator.MoveNext());
}