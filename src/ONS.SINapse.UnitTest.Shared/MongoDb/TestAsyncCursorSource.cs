using System.Linq.Expressions;
using MongoDB.Driver;

namespace ONS.SINapse.UnitTest.Shared.MongoDb;

public class TestAsyncCursorSource<TDocument> : TestAsyncEnumerableMongoCore<TDocument>, IAsyncCursorSource<TDocument>
{
    public TestAsyncCursorSource(Expression expression) : base(expression)
    {
    }

    public TestAsyncCursorSource(IEnumerable<TDocument> enumerable) : base(enumerable)
    {
    }

    public IAsyncCursor<TDocument> ToCursor(CancellationToken cancellationToken = new())
    {
        return new TestAsyncCursor<TDocument>(this.AsEnumerable().GetEnumerator());
    }

    public Task<IAsyncCursor<TDocument>> ToCursorAsync(CancellationToken cancellationToken = new())
    {
        return Task.FromResult(ToCursor(cancellationToken));
    }
}