using MongoDB.Driver;

namespace ONS.SINapse.UnitTest.Shared.MongoDb;

public class TestAsyncCursor<TDocument>(IEnumerator<TDocument> enumerator) : IAsyncCursor<TDocument>
{
    public IEnumerable<TDocument> Current => new List<TDocument> { enumerator.Current };

    public bool MoveNext(CancellationToken cancellationToken = default)
    {
        return enumerator.MoveNext();
    }

    public Task<bool> MoveNextAsync(CancellationToken cancellationToken = default)
    {
        return Task.FromResult(enumerator.MoveNext());
    }
    
    private bool _disposed;
    
    public void Dispose()
    {
        if(_disposed) return;
        
        _disposed = true;
        
        enumerator.Dispose();
        GC.SuppressFinalize(this);
    }
}