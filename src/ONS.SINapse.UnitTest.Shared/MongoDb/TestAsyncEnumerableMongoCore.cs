using System.Linq.Expressions;


namespace ONS.SINapse.UnitTest.Shared.MongoDb;

public class TestAsyncEnumerableMongoCore<T> : 
    TestQueryProvider<T>,
    IAsyncEnumerable<T>
{
    public TestAsyncEnumerableMongoCore(Expression expression)
        : base(expression)
    {
    }

    public TestAsyncEnumerableMongoCore(IEnumerable<T> enumerable)
        : base(enumerable)
    {
    }

    public IAsyncEnumerator<T> GetAsyncEnumerator(CancellationToken cancellationToken = default)
    {
        return new TestAsyncEnumerator<T>(this.AsEnumerable().GetEnumerator());
    }
}