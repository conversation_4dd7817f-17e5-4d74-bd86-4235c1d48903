using System.Collections;
using System.Linq.Expressions;

namespace ONS.SINapse.UnitTest.Shared.MongoDb;

public abstract class TestQueryProvider<T> : 
    IOrderedQueryable<T>,
    IQueryProvider
{
    private IEnumerable<T>? _enumerable;

    protected TestQueryProvider(Expression expression) => Expression = expression;

    protected TestQueryProvider(IEnumerable<T> enumerable)
    {
        var list = enumerable.ToList();
        _enumerable = list;
        Expression = list.AsQueryable().Expression;
    }

    public IQueryable CreateQuery(Expression expression)
    {
        return expression is MethodCallExpression methodCallExpression
            ? (IQueryable)CreateInstance(methodCallExpression.Method.ReturnType.GetGenericArguments().First(),
                expression)
            : CreateQuery<T>(expression);
    }

    public IQueryable<TEntity> CreateQuery<TEntity>(Expression expression)
    {
        return (IQueryable<TEntity>) CreateInstance(typeof (TEntity), expression);
    }

    private object CreateInstance(Type tElement, Expression expression)
    {
        return Activator.CreateInstance(GetType().GetGenericTypeDefinition().MakeGenericType(tElement), expression)!;
    }

    public object Execute(Expression expression)
    {
        return CompileExpressionItem<object>(expression);
    }

    public TResult Execute<TResult>(Expression expression)
    {
        return CompileExpressionItem<TResult>(expression);
    }

    IEnumerator<T> IEnumerable<T>.GetEnumerator()
    {
        _enumerable ??= CompileExpressionItem<IEnumerable<T>>(Expression);
        return _enumerable.GetEnumerator();
    }

    IEnumerator IEnumerable.GetEnumerator()
    {
        _enumerable ??= CompileExpressionItem<IEnumerable<T>>(Expression);
        return _enumerable.GetEnumerator();
    }

    public Type ElementType => typeof (T);

    public Expression Expression { get; }

    public IQueryProvider Provider => this;

    private static TResult CompileExpressionItem<TResult>(Expression expression)
    {
        var body = new TestExpressionVisitor().Visit(expression);
        if (body == null)
            throw new InvalidOperationException("body is null");
        return Expression.Lambda<Func<TResult>>(body, (IEnumerable<ParameterExpression>?) null).Compile()();
    }
}

public class TestExpressionVisitor : ExpressionVisitor;
