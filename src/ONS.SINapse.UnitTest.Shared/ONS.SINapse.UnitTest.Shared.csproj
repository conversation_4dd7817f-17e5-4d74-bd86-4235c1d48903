<Project Sdk="Microsoft.NET.Sdk">

    <!-- Propriedades específicas do projeto -->
    <PropertyGroup>
        <!-- Propriedades específicas serão herdadas do Directory.Build.props -->
    </PropertyGroup>

    <!-- Pacotes de teste -->
    <ItemGroup>
        <PackageReference Include="Bogus" />
        <PackageReference Include="MassTransit.Newtonsoft" />
        <PackageReference Include="Moq" />
        <PackageReference Include="Moq.AutoMock" />
        <PackageReference Include="Shouldly" />
        <PackageReference Include="xunit" />
    </ItemGroup>

    <!-- Pacotes compartilhados -->
    <ItemGroup>
        <PackageReference Include="Amazon.Extensions.Configuration.SystemsManager" />
        <PackageReference Include="AspNetCore.HealthChecks.Redis" />
        <PackageReference Include="AspNetCore.HealthChecks.UI" />
        <PackageReference Include="AspNetCore.HealthChecks.UI.Client" />
        <PackageReference Include="AspNetCore.HealthChecks.UI.Core" />
        <PackageReference Include="AspNetCore.HealthChecks.UI.InMemory.Storage" />
        <PackageReference Include="AutoMapper" />
        <PackageReference Include="AWSSDK.S3" />
        <PackageReference Include="CsvHelper" />
        <PackageReference Include="FirebaseDatabase.net" />
        <PackageReference Include="FluentValidation" />
        <PackageReference Include="HunspellSharp" />
        <PackageReference Include="librdkafka.redist" />
        <PackageReference Include="LinqKit.Core" />
        <PackageReference Include="LiteDB" />
        <PackageReference Include="MassTransit" />
        <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" />
        <PackageReference Include="Microsoft.AspNetCore.Authentication.WsFederation" />
        <PackageReference Include="Microsoft.Extensions.DependencyInjection" />
        <PackageReference Include="Microsoft.Extensions.Caching.Memory" />
        <PackageReference Include="Microsoft.Extensions.Caching.StackExchangeRedis" />
        <PackageReference Include="Microsoft.IdentityModel.Tokens" />
        <PackageReference Include="MongoDB.Bson" />
        <PackageReference Include="MongoDB.Driver" />
        <PackageReference Include="Newtonsoft.Json" />
        <PackageReference Include="Refit" />
        <PackageReference Include="Refit.HttpClientFactory" />
        <PackageReference Include="Refit.Newtonsoft.Json" />
        <PackageReference Include="Scrutor" />
        <PackageReference Include="Serilog.AspNetCore" />
        <PackageReference Include="System.IdentityModel.Tokens.Jwt" />
        <PackageReference Include="System.Linq.Expressions" />
    </ItemGroup>

    <!-- App Metrics -->
    <ItemGroup>
        <PackageReference Include="App.Metrics" />
        <PackageReference Include="App.Metrics.AspNetCore" />
        <PackageReference Include="App.Metrics.AspNetCore.Reporting" />
        <PackageReference Include="App.Metrics.AspNetCore.Tracking" />
        <PackageReference Include="App.Metrics.Core" />
        <PackageReference Include="App.Metrics.Extensions.Configuration" />
        <PackageReference Include="App.Metrics.Extensions.HealthChecks" />
        <PackageReference Include="App.Metrics.Formatters.Json" />
    </ItemGroup>

    <!-- Firebase e outras bibliotecas -->
    <ItemGroup>
        <PackageReference Include="FirebaseAdmin" />
        <PackageReference Include="FirebaseAuthentication.net" />
        <PackageReference Include="GeoCoordinate.NetCore" />
        <PackageReference Include="Polly" />
    </ItemGroup>
    <ItemGroup>
      <ProjectReference Include="..\ONS.SINapse.Business.Imp\ONS.SINapse.Business.Imp.csproj" />
      <ProjectReference Include="..\ONS.SINapse.CacheSync\ONS.SINapse.CacheSync.csproj" />
      <ProjectReference Include="..\ONS.SINapse.Integracao.Shared\ONS.SINapse.Integracao.Shared.csproj" />
      <ProjectReference Include="..\ONS.SINapse.Shared\ONS.SINapse.Shared.csproj" />
      <ProjectReference Include="..\ONS.SINapse.Solicitacao\ONS.SINapse.Solicitacao.csproj" />
    </ItemGroup>
</Project>
