using MassTransit;
using ONS.SINapse.Shared.Bus;

namespace ONS.SINapse.UnitTest.Shared.Bus;

public class TestSqsBus : ISqsBus
{
    private readonly IPublishEndpoint _publishEndpoint;

    public TestSqsBus(IPublishEndpoint publishEndpoint)
    {
        _publishEndpoint = publishEndpoint;
    }

    public Task Publish<T>(T message, CancellationToken cancellationToken = default) where T : class
        => _publishEndpoint.Publish(message, cancellationToken);

    Uri IBus.Address => throw new NotImplementedException();
    IBusTopology IBus.Topology => throw new NotImplementedException();
    Task<ISendEndpoint> ISendEndpointProvider.GetSendEndpoint(Uri address) => throw new NotImplementedException();
    ConnectHandle ISendObserverConnector.ConnectSendObserver(ISendObserver observer) => throw new NotImplementedException();
    ConnectHandle IPublishObserverConnector.ConnectPublishObserver(IPublishObserver observer) => throw new NotImplementedException();
    ConnectHandle IConsumeObserverConnector.ConnectConsumeObserver(IConsumeObserver observer) => throw new NotImplementedException();
    ConnectHandle IReceiveObserverConnector.ConnectReceiveObserver(IReceiveObserver observer) => throw new NotImplementedException();
    ConnectHandle IReceiveEndpointObserverConnector.ConnectReceiveEndpointObserver(IReceiveEndpointObserver observer) => throw new NotImplementedException();
    HostReceiveEndpointHandle IReceiveConnector.ConnectReceiveEndpoint(string queueName, Action<IReceiveEndpointConfigurator>? configureEndpoint) => throw new NotImplementedException();
    HostReceiveEndpointHandle IReceiveConnector.ConnectReceiveEndpoint(IEndpointDefinition definition, IEndpointNameFormatter? endpointNameFormatter, Action<IReceiveEndpointConfigurator>? configureEndpoint) => throw new NotImplementedException();
    ConnectHandle IConsumePipeConnector.ConnectConsumePipe<T>(IPipe<ConsumeContext<T>> pipe) => throw new NotImplementedException();
    ConnectHandle IConsumePipeConnector.ConnectConsumePipe<T>(IPipe<ConsumeContext<T>> pipe, ConnectPipeOptions options) => throw new NotImplementedException();
    ConnectHandle IRequestPipeConnector.ConnectRequestPipe<T>(Guid requestId, IPipe<ConsumeContext<T>> pipe) => throw new NotImplementedException();
    ConnectHandle IEndpointConfigurationObserverConnector.ConnectEndpointConfigurationObserver(IEndpointConfigurationObserver observer) => throw new NotImplementedException();
    void IProbeSite.Probe(ProbeContext context) => throw new NotImplementedException();
    Task IPublishEndpoint.Publish<T>(T message, IPipe<PublishContext<T>> publishPipe, CancellationToken cancellationToken) => throw new NotImplementedException();
    Task IPublishEndpoint.Publish<T>(T message, IPipe<PublishContext> publishPipe, CancellationToken cancellationToken) => throw new NotImplementedException();
    Task IPublishEndpoint.Publish(object message, CancellationToken cancellationToken) => throw new NotImplementedException();
    Task IPublishEndpoint.Publish(object message, IPipe<PublishContext> publishPipe, CancellationToken cancellationToken) => throw new NotImplementedException();
    Task IPublishEndpoint.Publish(object message, Type messageType, CancellationToken cancellationToken) => throw new NotImplementedException();
    Task IPublishEndpoint.Publish(object message, Type messageType, IPipe<PublishContext> publishPipe, CancellationToken cancellationToken) => throw new NotImplementedException();
    Task IPublishEndpoint.Publish<T>(object values, CancellationToken cancellationToken) => throw new NotImplementedException();
    Task IPublishEndpoint.Publish<T>(object values, IPipe<PublishContext<T>> publishPipe, CancellationToken cancellationToken) => throw new NotImplementedException();
    Task IPublishEndpoint.Publish<T>(object values, IPipe<PublishContext> publishPipe, CancellationToken cancellationToken) => throw new NotImplementedException();
    Task<ISendEndpoint> IPublishEndpointProvider.GetPublishSendEndpoint<T>() => throw new NotImplementedException();
    public ConnectHandle ConnectConsumeMessageObserver<T>(IConsumeMessageObserver<T> observer) where T : class => throw new NotImplementedException();
}
