using MassTransit;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using ONS.SINapse.Business.Imp.Business.EventHandlers.Handlers;
using ONS.SINapse.Shared.Bus;
using ONS.SINapse.Shared.Settings;
using ONS.SINapse.Solicitacao.EventHandlers.Handlers;

namespace ONS.SINapse.CrossCutting;

/// <summary>
/// Classe de extensão responsável pela injeção de dependências e configuração do MassTransit utilizando Amazon SQS como transporte.
/// </summary>
public static class MassTransitInjector
{
    /// <summary>
    /// Adiciona e configura o MassTransit utilizando o transporte Amazon SQS.
    /// </summary>
    /// <param name="services">Coleção de serviços da aplicação.</param>
    /// <param name="configuration">Instância da configuração da aplicação.</param>
    /// <param name="configureConsumers">Ação opcional para registrar consumidores específicos.</param>
    /// <param name="configureBus">Ação opcional para configuração adicional do barramento.</param>
    /// <returns>Instância modificada de <see cref="IServiceCollection"/> com o MassTransit configurado.</returns>
    /// <exception cref="InvalidOperationException">Lançada quando as configurações AWS não são encontradas.</exception>
    public static IServiceCollection AddMassTransitWithSqs(
        this IServiceCollection services,
        IConfiguration configuration,
        Action<IBusRegistrationConfigurator>? configureConsumers = null,
        Action<IBusRegistrationContext, IAmazonSqsBusFactoryConfigurator>? configureBus = null)
    {
        var awsSettings = configuration.GetSection("AWS").Get<AwsSettings>()
                          ?? throw new InvalidOperationException("As configurações AWS estão ausentes.");

        services.AddMassTransit<ISqsBus>(x =>
        {
            configureConsumers?.Invoke(x);

            x.UsingAmazonSqs((context, cfg) =>
            {
                cfg.Host(awsSettings.Region, h =>
                {
                    h.AccessKey(awsSettings.AccessKey);
                    h.SecretKey(awsSettings.SecretKey);
                });

                configureBus?.Invoke(context, cfg);

                cfg.ConfigureEndpoints(context);
            });
        });

        return services;
    }

    /// <summary>
    /// Registra os consumidores de eventos utilizados na aplicação.
    /// </summary>
    /// <returns>Ação que registra os consumidores com o <see cref="IBusRegistrationConfigurator"/>.</returns>
    public static Action<IBusRegistrationConfigurator> ConfigureConsumers()
    {
        return x =>
        {
            x.AddConsumer<ExtracaoDeSolicitacaoEventHandler>();
            x.AddConsumer<EnviarSolicitacaoAoMongoEventHandler>();
            x.AddConsumer<RemoverSolicitacoesFirebaseEventHandler>();
            x.AddConsumer<SolicitacaoFinalizacaoAutoEventHandler>();
            x.AddConsumer<MarcarSolicitacoesAtrasadasHandler>();
        };
    }

    /// <summary>
    /// Configura os endpoints e opções adicionais do barramento Amazon SQS.
    /// </summary>
    /// <returns>Ação que configura o <see cref="IAmazonSqsBusFactoryConfigurator"/> com base nas configurações de fila.</returns>
    /// <exception cref="InvalidOperationException">Lançada quando as configurações de filas SQS não são encontradas.</exception>
    public static Action<IBusRegistrationContext, IAmazonSqsBusFactoryConfigurator> ConfigureBus()
    {
        return (context, cfg) =>
        {
            var configuration = context.GetRequiredService<IConfiguration>();
            var queueSettings = configuration.GetSection(nameof(SqsQueueSettings))
                                    .Get<SqsQueueSettings>()
                                ?? throw new InvalidOperationException("As configurações das filas SQS estão ausentes.");

            cfg.ReceiveEndpoint(queueSettings.ExtracaoDeSolicitacao, e =>
            {
                e.ConfigureConsumer<ExtracaoDeSolicitacaoEventHandler>(context);
                cfg.UseRawJsonDeserializer(isDefault: true);
            });

            cfg.ReceiveEndpoint(queueSettings.EnviarSolicitacaoParaMongo, e =>
            {
                e.ConfigureConsumer<EnviarSolicitacaoAoMongoEventHandler>(context);
                cfg.UseRawJsonDeserializer(isDefault: true);
            });

            cfg.ReceiveEndpoint(queueSettings.RemoverSolicitacaoDoFirebase, e =>
            {
                e.ConfigureConsumer<RemoverSolicitacoesFirebaseEventHandler>(context);
                cfg.UseRawJsonSerializer(isDefault: true);
                cfg.UseRawJsonDeserializer(isDefault: true);
            });
            
            cfg.ReceiveEndpoint(queueSettings.FinalizarSolicitacaoAutomaticamente, e =>
            {
                e.ConfigureConsumer<SolicitacaoFinalizacaoAutoEventHandler>(context);
                cfg.UseRawJsonSerializer(isDefault: true);
                cfg.UseRawJsonDeserializer(isDefault: true);
            });
            
            cfg.ReceiveEndpoint(queueSettings.NotificacaoDeSolicitacaoAtrasada, e =>
            {
                e.ConfigureConsumer<MarcarSolicitacoesAtrasadasHandler>(context);
                cfg.UseRawJsonSerializer(isDefault: true);
                cfg.UseRawJsonDeserializer(isDefault: true);
            });

            // Configurações adicionais do consumidor
            cfg.AutoDelete = true;
            cfg.PrefetchCount = 1;
            cfg.ConcurrentMessageLimit = 1;
        };
    }
}
