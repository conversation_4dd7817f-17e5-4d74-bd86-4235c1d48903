using MassTransit;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using ONS.SINapse.Business.Imp.Business.EventHandlers.Handlers;
using ONS.SINapse.Shared.Bus;
using ONS.SINapse.Shared.Settings;

namespace ONS.SINapse.CrossCutting;

public static class SqsInjector
{
    public static void Register(IServiceCollection services, IConfiguration configuration)
    {
        services
            .RegisterConsumersSqs(configuration);
    }

    private static IServiceCollection RegisterConsumersSqs(this IServiceCollection services, IConfiguration configuration)
    {
        var awsSection = configuration.GetSection("AWS");
        var awsSettings = awsSection.Get<AwsSettings>();
        ArgumentNullException.ThrowIfNull(awsSettings);

        var sqsTopicSection = configuration.GetSection(nameof(SqsTopicsSettings));
        var sqsTopicSettings = sqsTopicSection.Get<SqsTopicsSettings>();
        ArgumentNullException.ThrowIfNull(sqsTopicSettings);

        services.AddMassTransit<ISqsBus>(x =>
        {
            x.AddConsumer<ExtracaoDeSolicitacaoEventHandler>();

            x.UsingAmazonSqs((context, cfg) =>
            {
                cfg.Host(awsSettings.Region, h =>
                {
                    h.AccessKey(awsSettings.AccessKey);
                    h.SecretKey(awsSettings.SecretKey);
                });

                cfg.ReceiveEndpoint(sqsTopicSettings.ExtracaoDeSolicitacao, e =>
                {
                    e.ConfigureConsumer<ExtracaoDeSolicitacaoEventHandler>(context);

                    cfg.UseRawJsonDeserializer(isDefault: true);
                });

                cfg.AutoDelete = true;
                cfg.PrefetchCount = 1;
                cfg.ConcurrentMessageLimit = 1;
            });
        });

        return services;
    }
}
