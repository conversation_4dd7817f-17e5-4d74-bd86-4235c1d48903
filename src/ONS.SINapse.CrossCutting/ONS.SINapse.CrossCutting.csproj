<Project Sdk="Microsoft.NET.Sdk">

  <!-- Propriedades específicas do projeto -->
  <PropertyGroup>
    <!-- Propriedades específicas serão herdadas do Directory.Build.props -->
  </PropertyGroup>

  <!-- Pacotes específicos do CrossCutting -->
  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.WsFederation" />
  </ItemGroup>

  <!-- Referências de projeto -->
  <ItemGroup>
    <ProjectReference Include="..\ONS.SINapse.Business.Imp\ONS.SINapse.Business.Imp.csproj" />
    <ProjectReference Include="..\ONS.SINapse.CacheSync\ONS.SINapse.CacheSync.csproj" />
    <ProjectReference Include="..\ONS.SINapse.Repository.Imp\ONS.SINapse.Repository.Imp.csproj" />
    <ProjectReference Include="..\ONS.SINapse.Solicitacao\ONS.SINapse.Solicitacao.csproj" />
  </ItemGroup>

</Project>
