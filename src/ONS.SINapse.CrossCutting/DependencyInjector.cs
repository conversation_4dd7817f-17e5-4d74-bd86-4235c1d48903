using System.Globalization;
using System.IO.Compression;
using System.Security.Cryptography;
using Firebase.Database;
using FluentValidation;
using MassTransit;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Localization;
using Microsoft.AspNetCore.Mvc.Infrastructure;
using Microsoft.AspNetCore.ResponseCompression;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.IdentityModel.Tokens;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using ONS.SINapse.Business.Imp;
using ONS.SINapse.Business.Imp.AutoMapper;
using ONS.SINapse.Business.Imp.AutoMapper.Converters;
using ONS.SINapse.Business.Imp.AutoMapper.S3;
using ONS.SINapse.Business.Imp.Business.EventHandlers.Handlers;
using ONS.SINapse.Business.Imp.Validators.Firebase;
using ONS.SINapse.Business.Imp.Workers.Messages;
using ONS.SINapse.Business.Imp.Workers.Topicos;
using ONS.SINapse.CacheSync;
using ONS.SINapse.CacheSync.Configurations;
using ONS.SINapse.Integracao.Shared.Settings;
using ONS.SINapse.Repository.Imp;
using ONS.SINapse.Shared;
using ONS.SINapse.Shared.Converters;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.DTO.Firebase;
using ONS.SINapse.Shared.DTO.PerfilDoUsuario;
using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Shared.Factories;
using ONS.SINapse.Shared.Identity;
using ONS.SINapse.Shared.Kafka;
using ONS.SINapse.Shared.Kafka.DependencyInjection;
using ONS.SINapse.Shared.Messages;
using ONS.SINapse.Shared.Services;
using ONS.SINapse.Shared.Services.Firebase;
using ONS.SINapse.Shared.Settings;
using ONS.SINapse.Solicitacao;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using ONS.SINapse.Solicitacao.Converters;
using ONS.SINapse.Solicitacao.Dtos.Integracao;
using ONS.SINapse.Solicitacao.EventHandlers.IntegrationEvents;
using ONS.SINapse.Solicitacao.Factories;
using ONS.SINapse.Solicitacao.Factories.Integracao;
using ONS.SINapse.Solicitacao.Mapper;
using ONS.SINapse.Solicitacao.Middlewares;
using ONS.SINapse.Solicitacao.Middlewares.Solicitacao;
using ONS.SINapse.Solicitacao.Validations;
using ONS.SINapse.Solicitacao.Workers.Messages;
using ONS.SINapse.Solicitacao.Workers.Providers.Consumers;
using ONS.SINapse.Solicitacao.Workers.Topicos;

namespace ONS.SINapse.CrossCutting;

public static class DependencyInjector
{
    public static void Register(IServiceCollection services, IConfiguration configuration)
    {
        services
            .RegisterSharedLayer(configuration)
            .RegisterServices()
            .ConfigureLocalizationOptions()
            .ConfigureCompression()
            .RegisterContextAccessor()
            .RegisterCredencials(configuration)
            .RegisterBusinessLayer()
            .RegisterRepositoryLayer(configuration)
            .RegisterValidators()
            .AddSinapseDados(configuration)
            .RegisterConsumers()
            .RegisterMappers()
            .RegisterProviders(configuration)
            .RegisterFirebaseServices(configuration);

    }
    
    private static IServiceCollection RegisterConsumers(this IServiceCollection services)
    {
        services.AddMassTransit(x =>
        {
            x.AddConsumer<ExtracaoDeSolicitacaoEventHandler>();

            x.UsingInMemory((context, cfg) =>
            {
                cfg.ConfigureEndpoints(context);
            });
        });

        services.AddMediator(options =>
        {
            options.AddConsumers(typeof(CommandHandler<>).Assembly, typeof(CacheSyncApplicationReference).Assembly);
            options.AddConsumers(typeof(CommandHandler<>).Assembly, typeof(SolicitacaoApplicationReference).Assembly);
            options.AddRequestClient<Command>();
            options.SetDefaultRequestTimeout(TimeSpan.FromSeconds(60));
            options.ConfigureMediator((context, cfg) =>
            {
                cfg.UseConsumeFilter<UserDefinitionMiddleware<EncaminharSolicitacaoCommand>>(context);
                cfg.UseConsumeFilter<LoteIdDefinitionMiddleware>(context);
                cfg.UseConsumeFilter<IdDefinitionMiddleware>(context);
            });
        });
        
        return services;
    }
    
    private static IServiceCollection RegisterMappers(this IServiceCollection services)
    {
        services.AddAutoMapper(configuration =>
            {
                configuration.AddProfile<EntityToDto>();
                configuration.AddProfile<RascunhoSolicitacaoMapper>();
                configuration.AddProfile<SolicitacaoS3Mapper>();
                configuration.AddProfile<DtoToFirebaseDto>();
                configuration.AddMaps(typeof(SolicitacaoApplicationReference).Assembly);
            }, 
            Array.Empty<Type>(),
            ServiceLifetime.Singleton);
        
        services.RegisterConverter();

        return services;
    }
    
    private static void RegisterConverter(this IServiceCollection services)
    {
        services.AddSingleton<AgenteDisponivelDoPerfilDtoConverter>();
        services.AddSingleton<ComunicadoDetalheBaseDtoConverter<ComunicadoDetalheDto>>();
        services.AddSingleton<ComunicadoDtoConverter>();
        services.AddSingleton<ComunicadoItemDtoConverter>();
        services.AddSingleton<TemplateDeSolicitacaoStep1DtoConverter>();
        services.AddSingleton<UsuarioConverter>();
        services.AddSingleton<CadastroSolicitacaoDtoToSolicitacaoConverter>();
        services.AddSingleton<AdicionarVersaoDtoConverter>();
        services.AddSingleton<RascunhoSolicitacaoDtoConverter>();
    }
    
    private static IServiceCollection RegisterFirebaseServices(this IServiceCollection services,IConfiguration configuration)
    {
        services.AddScoped<ISolicitacaoFirebaseDatabaseService, SolicitacaoFirebaseDatabaseService>();
        services.AddScoped<INotificacaoFirebaseDatabaseService, NotificacaoFirebaseDatabaseService>();
        services.AddSingleton<IControleDeChamadaRealtimeService, ControleDeChamadaRealtimeService>();
        services.AddScoped<IAlertaSonoroFirebaseDatabaseService, AlertaSonoroFirebaseDatabaseService>();
        services.AddScoped<IVisaoUsuarioFirebaseDatabaseService, VisaoUsuarioFirebaseDatabaseService>();
        services.AddScoped<ITagFirebaseService, TagFirebaseService>();
        services.AddScoped<IDatabaseFirebaseService, DatabaseFirebaseService>();
        services.AddScoped<IGeolocalizacaoService, GeolocalizacaoService>();
        services.AddSingleton<IFirebaseAuthService, FirebaseAuthService>();
        services.Decorate<IFirebaseAuthService, FirebaseAuthCacheService>();
        
        var firebaseSection = configuration.GetSection(nameof(FirebaseSettings));
        var firebaseSettings = firebaseSection.Get<FirebaseSettings>();
        ArgumentNullException.ThrowIfNull(firebaseSettings);

        services.AddSingleton<FirebaseClient>(provider =>
        {
            var authenticationService = provider.GetRequiredService<IFirebaseAuthService>();
            return ObterFirebaseClient(provider, firebaseSettings, authenticationService);
        });
        
        return services;
    }
    
    private static FirebaseClient ObterFirebaseClient(IServiceProvider provider, FirebaseSettings firebaseSettings, IFirebaseAuthService authenticationService)
    {
        return new FirebaseClient(firebaseSettings.BasePath, new FirebaseOptions
        {
            AuthTokenAsyncFactory = async () =>
            {
                var token = await authenticationService.GetFirebaseTokenAsync(string.Empty, CancellationToken.None)
                    .ConfigureAwait(false);
                
                return token.FirebaseToken;
            },
            JsonSerializerSettings = new JsonSerializerSettings
            {
                ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
                NullValueHandling = NullValueHandling.Ignore,
                ContractResolver = new CamelCasePropertyNamesContractResolver(),
                Converters = new List<JsonConverter>
                {
                    new NotificacaoChaveJsonConverter(),
                    new AlertaSonoroChaveJsonConverter(),
                }
            },
            HttpClientFactory = new FirebaseHttpClientFactory(provider.GetRequiredService<IHttpClientFactory>())
        });
    }
    
    private static IServiceCollection RegisterValidators(this IServiceCollection services)
    {
        services.AddScoped<IValidator<RascunhoSolicitacaoDto>, RascunhoSolicitacaoValidator>();
        services.AddScoped<IValidator<CadastroSolicitacaoDto>, CadastroSolicitacaoDtoValidator>();
        
        services.AddScoped<IValidator<CadastroDeSolicitacaoExternaRecebidaIntegrationEvent>, 
            CadastroDeSolicitacaoExternaRecebidaIntegrationEventValidator>();
        
        services.AddScoped<IValidator<StatusDeSolicitacaoIntegracaoRecebimentoDto>, 
            StatusDeSolicitacaoIntegracaoRecebimentoValidator>();
        
        services.AddScoped<IValidator<EncaminharSolicitacaoCommand>, EncaminharSolicitacaoCommandValidator>();
        
        return services;
    }

    private static IServiceCollection RegisterProviders(this IServiceCollection services, IConfiguration configuration)
    {
        var optionsSection = configuration.GetSection(nameof(KafkaTopicsSettings));

        ArgumentNullException.ThrowIfNull(optionsSection);
        
        var sinapseKafkaOptions = optionsSection.Get<KafkaTopicsSettings>();
        ArgumentNullException.ThrowIfNull(sinapseKafkaOptions);
        
        services.AddKafkaProvider();
        
        services.AddScoped<ICadastroDeSolicitacaoConsumerProvider, CadastroDeSolicitacaoConsumerProvider>();
        services.AddScoped<ITrocaDeStatusConsumerProvider, TrocaDeStatusConsumerProvider>();
        
        services.RegisterTopicos(sinapseKafkaOptions);
        
        return services;
    }

    private static void RegisterTopicos(this IServiceCollection services, KafkaTopicsSettings options)
    {
        services
            .AddSingleton<TopicoIntegrationKafka<CadastroDeSolicitacaoExternaRecebidaIntegrationEvent>,
                CadastroDeSolicitacaoTopicoIntegrationKafka>(_ => new CadastroDeSolicitacaoTopicoIntegrationKafka(options.CadastroDeSolicitacao));
        
        services
            .AddSingleton<TopicoIntegrationKafka<TrocaDeStatusExternaIntegrationEvent>,
                TrocaDeStatusTopicoIntegrationKafka>(_ => new TrocaDeStatusTopicoIntegrationKafka(options.TrocaDeStatusDeSolicitacao));

        services
            .AddSingleton<TopicoIntegrationKafka<AtualizacaoStatusMessage>, ConsultaDeStatusTopicoIntegrationKafka>(_ =>
                new ConsultaDeStatusTopicoIntegrationKafka(options.ConsultaDeStatusDeSolicitacao));

        services
            .AddSingleton<TopicoIntegrationKafka<StatusAgenteMessage>, StatusAgenteTopicoIntegrationKafka>(_ => 
                new StatusAgenteTopicoIntegrationKafka(options.StatusAgente));

        services.AddSingleton<TopicoIntegrationKafka<CadastroSolicitacaoMessage>, CadastrarSolicitacaoTopicoKafka>(_ =>
            new CadastrarSolicitacaoTopicoKafka(options.CadastroDeSolicitacao));

    }
    
    private static IServiceCollection RegisterServices(this IServiceCollection services)
    {
        services.AddLocalization();
        services.AddHttpClient();
        services.AddHttpClient("firebase-sinapse")
            .AddFirebaseRequestHandler();
        services.AddEndpointsApiExplorer();

        services
            .AddScoped<ICriarCommandStatusDeSolicitacaoRecebidoFactory,
                CriarCommandStatusDeSolicitacaoRecebidoFactory>(); 
        services
            .AddScoped<ISolicitacaoFirebaseCommandFactory,
                SolicitacaoFirebaseCommandFactory>();
        
        return services;
    }

    private static IServiceCollection RegisterCredencials(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddSinapseAuthorization(configuration);

        return services;
    }

    private static IServiceCollection RegisterContextAccessor(this IServiceCollection services)
    {
        services.AddHttpContextAccessor();
        services.AddSingleton<IHttpContextAccessor, HttpContextAccessor>();
        services.AddSingleton<IActionContextAccessor, ActionContextAccessor>();

        return services;
    }

    private static IServiceCollection ConfigureLocalizationOptions(this IServiceCollection services)
    {
        services.Configure<RequestLocalizationOptions>(options =>
        {
            var supportedCultures = new List<CultureInfo>
            {
                new ("pt-BR")
            };

            options.DefaultRequestCulture = new RequestCulture("pt-BR");
            options.SupportedCultures = supportedCultures;
            options.SupportedUICultures = supportedCultures;
        });

        return services;
    }

    private static IServiceCollection ConfigureCompression(this IServiceCollection services)
    {
        services.Configure<GzipCompressionProviderOptions>(options => options.Level = CompressionLevel.Optimal);
        services.AddResponseCompression(options =>
        {
            options.Providers.Add<GzipCompressionProvider>();
        });

        return services;
    }
}

public static class AuthorizationConfiguration
{
    public static void AddSinapseAuthorization(this IServiceCollection services, IConfiguration configuration)
    {
        var jwtConfiguration = configuration.GetSection("ONS:Authorization");

        services.Configure<AuthorizationServiceSettings>(jwtConfiguration);
        var jwtTokenSettings = jwtConfiguration.Get<AuthorizationServiceSettings>();

        var authorizationOptions = new AuthorizationServiceOptions
        (
            jwtTokenSettings!.Issuer,
            new[] { jwtTokenSettings.Audience },
            string.Empty,
            jwtTokenSettings.UseRsa,
            jwtTokenSettings.RsaPublicExponent,
            jwtTokenSettings.RsaModulus
        );
        
        var tokenValidationParameters = BuildTokenValidationParameters(authorizationOptions);
        services
            .AddAuthentication("Bearer")
            .AddJwtBearer(optionsToken =>
            {
                optionsToken.TokenValidationParameters = tokenValidationParameters;
                optionsToken.Events = new JwtBearerEvents
                {
                    OnMessageReceived = ValidarTokenEnviadoNaQueryStringAsync
                };
            });
        
        services.AddSingleton<IAuthorizationHandler, ClaimAuthorizeHandler>();
        
        var rulesOperationsConfig = new List<RuleOperations>();
        configuration.GetSection(nameof(RuleOperations)).Bind(rulesOperationsConfig);
    
        services.AddSingleton<IRulesOperations, RulesOperations>(_ => new RulesOperations(rulesOperationsConfig));
        
        services.AddScoped<IUserContext, UserContext>(provider =>
        {
            var accessor = provider.GetRequiredService<IHttpContextAccessor>();
            var rules = provider.GetRequiredService<IRulesOperations>();
            return (UserContext.UserContextFactory.Create(accessor, rules) as UserContext)!;
        });

        services.AddSingleton<IUserContextAccessor, UserContextAccessor>();
    }
    
    /// <summary>
    /// Usado para validar o token mesmo vindo da query na requisição http, muito comum em conexão via websocket
    /// </summary>
    /// <param name="context">Mensagem de um evento jwt</param>
    private static Task ValidarTokenEnviadoNaQueryStringAsync(MessageReceivedContext context)
    {
        var accessToken = context.Request.Query["access_token"];
                        
        if(!string.IsNullOrEmpty(context.Token)) return Task.CompletedTask;
                        
        if (!string.IsNullOrEmpty(accessToken))
        {
            context.Token = accessToken;
        }
        return Task.CompletedTask;
    }
    
    private static SecurityKey BuildSecurityKey(AuthorizationServiceOptions options)
    {
        SecurityKey securityKey;
        if (options.UseRsa)
        {
           var numArray1 = Convert.FromBase64String(options.RsaPublicKeyModulus64);
            var numArray2 = Convert.FromBase64String(options.RsaPublicKeyExponent64);
            var rsa = RSA.Create();
            rsa.ImportParameters(new RSAParameters()
            {
                Modulus = numArray1,
                Exponent = numArray2
            });
            securityKey = new RsaSecurityKey(rsa);
        }
        else
            securityKey = new SymmetricSecurityKey(Convert.FromBase64String(options.Secret));
        return securityKey;
    }

    private static TokenValidationParameters BuildTokenValidationParameters(
        AuthorizationServiceOptions options)
    {
        var securityKey = BuildSecurityKey(options);
        return new TokenValidationParameters()
        {
            ClockSkew = TimeSpan.Zero,
            ValidIssuer = options.Issuer,
            ValidAudiences = options.Audiences,
            IssuerSigningKey = securityKey
        };
    }
}




