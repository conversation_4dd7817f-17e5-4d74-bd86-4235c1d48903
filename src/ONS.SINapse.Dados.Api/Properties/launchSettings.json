{"$schema": "http://json.schemastore.org/launchsettings.json", "profiles": {"http": {"commandName": "Project", "dotnetRunMessages": true, "launchBrowser": false, "launchUrl": "swagger", "applicationUrl": "http://localhost:7073", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development", "OTEL_EXPORTER_OTLP_ENDPOINT": "http://ons.sinapse.dados.seq:5341/ingest/otlp", "OTEL_EXPORTER_OTLP_PROTOCOL": "http/protobuf"}}, "https": {"commandName": "Project", "dotnetRunMessages": true, "launchBrowser": false, "launchUrl": "swagger", "applicationUrl": "https://localhost:7074;http://localhost:7073", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development", "OTEL_EXPORTER_OTLP_ENDPOINT": "localhost:4317", "OTEL_EXPORTER_OTLP_PROTOCOL": "http/protobuf", "ConfigITamb": "Desenv", "ConfigITuser": "<EMAIL>", "ConfigITpwd": "P@ssw0rd", "ConfigITr": "http://configIT.ons.org.br/ons.configit.servico.odata/api/", "ONS__IgnoreConfigIT": "false"}}}}