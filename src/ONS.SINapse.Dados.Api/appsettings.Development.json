{"AllowedHosts": "*", "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "ConnectionStrings": {"Redis": "localhost:6379,defaultDatabase=0,password=teste", "SqlServerConnection": "Server=************;Database=SINAPSE;User Id=consultasinapse;Password=consultasinapse;TrustServerCertificate=True;", "SqlServerReadOnly": "Server=************;Database=SINAPSE;User Id=consultasinapse;Password=consultasinapse;TrustServerCertificate=True;"}, "ONS": {"IgnoreConfigIT": true, "Authorization": {"Issuer": "https://popons.amcom.com.br/ons.pop.federation/", "Audience": "SINAPSE", "UseRsa": "true", "RsaModulus": "wSxNKSkhfB1XR+fD/KZxK5nLEEHHBNrbSpiNw9FtcJHkvOiBXWI+G43Y1rvp6zp2/sjEqiXbQlFuMf2d/hM9ScIrdtrykf3m0OpDvhACFgwvvdiIaWOqIZ9oJCS9uzgEq7OGwH4gQklIOUbVrjZftXc0qFRR3XwkwGPGaNLsVpzSMeJHDJJReJe4MtztgsBS//AzkSdbhBpcAwQYOdmeQZTxL76miZqIHqAWAGQZgh/y3kHdfayhMb/hSgay933ITWyV2V7TUMBByYm6MOLuSRWTuloVIiwA/Nap5tgrQFdCuc34GCNgQIocn8qbcICI21AebnbyEyo96sONodToEQ==", "RsaPublicExponent": "AQAB"}, "PopAuth": {"Origin": "http://local.amcom.com.br", "TokenURL": "https://popons.amcom.com.br/ons.pop.federation/oauth2/token", "clientId": "SINAPSE", "username": "amcom\\popons", "password": "14N@Ng80qlZqlnvLlrQ", "grantTypeRefreshToken": "refresh_token", "grantTypeAccessToken": "password"}}, "ApplicationSettings": {"SinapseDadosHealthCheckUrl": "https://localhost:7074/health", "SinapseHealthCheckUrl": "", "SinapseIntegracaoHealthCheckUrl": ""}, "OpenTelemetrySettings": {"ServiceName": "ons.sinapse.dados", "ServiceVersion": "1.0.0", "ServiceEnabled": true}, "OutputCacheSettings": {"ResponseExpirationTime": 5}}