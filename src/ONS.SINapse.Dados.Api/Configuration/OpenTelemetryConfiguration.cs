using ONS.SINapse.Dados.Shared.Settings;
using OpenTelemetry;
using OpenTelemetry.Exporter;
using OpenTelemetry.Logs;
using OpenTelemetry.Metrics;
using OpenTelemetry.Resources;
using OpenTelemetry.Trace;

namespace ONS.SINapse.Dados.Api.Configuration;

public static class OpenTelemetryConfiguration
{
    private const string DefaultServiceVariableName = "OTEL_EXPORTER_OTLP_ENDPOINT";
    private const OtlpExportProtocol DefaultExportProtocol = OtlpExportProtocol.Grpc;
    public static WebApplicationBuilder AddSinapseOpenTelemetry(this WebApplicationBuilder builder)
    {
        OpenTelemetrySettings options = builder.Configuration
                                            .GetSection(nameof(OpenTelemetrySettings))
                                            .Get<OpenTelemetrySettings>()
                                        ?? throw new NullReferenceException(nameof(OpenTelemetrySettings));
        
        if(!options.ServiceEnabled)
        {
            return builder;
        }

        string? endpoint = Environment.GetEnvironmentVariable(DefaultServiceVariableName);
        
        if (string.IsNullOrEmpty(endpoint))
        {
            return builder;
        }

        ResourceBuilder? resourceBuilder = ResourceBuilder.CreateDefault()
            .AddService(options.ServiceName, options.ServiceVersion)
            .AddTelemetrySdk();
        
        builder.Services.AddOpenTelemetry()
            .AddSinapseMetrics(resourceBuilder)
            .AddSinapseTrace(resourceBuilder);
        
        builder.Logging.AddSinapseLogsOpenTelemetry(resourceBuilder);
        
        return builder;
    }

    private static ILoggingBuilder AddSinapseLogsOpenTelemetry(this ILoggingBuilder loggingBuilder, ResourceBuilder resourceBuilder)
    {
        loggingBuilder
            .ClearProviders()
            .AddOpenTelemetry(options =>
            {
                options
                    .SetResourceBuilder(resourceBuilder)
                    .AddOtlpExporter(exporter =>
                    {
                        exporter.Protocol = DefaultExportProtocol;
                    })
                    .AddConsoleExporter();

                options.IncludeFormattedMessage = true;
                options.IncludeScopes = true;
                options.ParseStateValues = true;
            });
        
        return loggingBuilder;
    }
    
    private static OpenTelemetryBuilder AddSinapseMetrics(this OpenTelemetryBuilder openTelemetryBuilder, ResourceBuilder resourceBuilder)
    {
        openTelemetryBuilder.WithMetrics(builder =>
        {
            builder.AddMeter("ons.sinapse.dados")
                .SetResourceBuilder(resourceBuilder)
                .AddHttpClientInstrumentation()
                .AddSqlClientInstrumentation()
                .AddAspNetCoreInstrumentation()
                .AddOtlpExporter(exporter =>
                {
                    exporter.Protocol = DefaultExportProtocol;
                });
        });
        
        return openTelemetryBuilder;
    }
    
    private static OpenTelemetryBuilder AddSinapseTrace(this OpenTelemetryBuilder openTelemetryBuilder, ResourceBuilder resourceBuilder)
    {
        openTelemetryBuilder.WithTracing(tracerProviderBuilder =>
        {
            tracerProviderBuilder
                .AddSource("ons.sinapse.dados")
                .SetResourceBuilder(resourceBuilder)
                .AddAspNetCoreInstrumentation((options) =>
                    {
                        options.Filter = (httpContext) =>
                        {
                            var routes = new List<string>
                            {
                                "/swagger",
                                "/angularsettings",
                                "/health"
                            };

                            return !routes.Exists(httpContext.Request.Path.Value!.ToLower().Contains);
                        };
                        
                        options.RecordException = true;
                    }
                )
                .AddSqlClientInstrumentation(options =>
                {
                    options.SetDbStatementForText = true;
                    options.RecordException = true;
                })
                .AddHttpClientInstrumentation(options => options.RecordException = true)
                .AddAspNetCoreInstrumentation()
                .SetErrorStatusOnException()
                .AddOtlpExporter(exporter =>
                {
                    exporter.Protocol = DefaultExportProtocol;
                });
        });
        
        return openTelemetryBuilder;
    }
}
