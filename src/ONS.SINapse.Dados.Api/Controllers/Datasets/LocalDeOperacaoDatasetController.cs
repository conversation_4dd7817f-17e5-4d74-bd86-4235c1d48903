using System.Net.Mime;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ONS.SINapse.Dados.Business.Dtos.Dataset;
using ONS.SINapse.Dados.Business.Interfaces.Dataset;
using ONS.SINapse.Dados.Shared.Identity;
using ONS.SINapse.Dados.Shared.Identity.Claims;

namespace ONS.SINapse.Dados.Api.Controllers.Datasets;

[Route("api/local-de-operacao/dataset")]
[Authorize]
[ClaimRequirement(PopClaimTypes.Role, PopClaimValues.Roles.ApiIntegracao)]
[ProducesResponseType(typeof(DatasetItemDto), 200)]
public class LocalDeOperacaoDatasetController : ControllerBase
{
    private readonly ILocaisDeOperacaoDatasetBusiness _locaisDeOperacaoDatasetBusiness;

    public LocalDeOperacaoDatasetController(ILocaisDeOperacaoDatasetBusiness locaisDeOperacaoDatasetBusiness)
    {
        _locaisDeOperacaoDatasetBusiness = locaisDeOperacaoDatasetBusiness;
    }
    
    [HttpGet]
    [Consumes(MediaTypeNames.Application.Json)]
    public async Task<IActionResult> LocalDeOperacaoDataset([FromQuery]string codigoDoCentroAgente, CancellationToken cancellationToken) 
        => Ok(await _locaisDeOperacaoDatasetBusiness.BuscarLocalDeOperacaoAsync(codigoDoCentroAgente, cancellationToken));
    
}