using System.Net.Mime;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ONS.SINapse.Dados.Business.Dtos.Dataset;
using ONS.SINapse.Dados.Business.Interfaces.Dataset;
using ONS.SINapse.Dados.Shared.Identity;
using ONS.SINapse.Dados.Shared.Identity.Claims;

namespace ONS.SINapse.Dados.Api.Controllers.Datasets;

[Route("api/equipamentos/dataset")]
[Authorize]
[ClaimRequirement(PopClaimTypes.Role, PopClaimValues.Roles.ApiIntegracao)]
[ProducesResponseType(typeof(DatasetItemDto), 200)]
public class EquipamentoDatasetController : ControllerBase
{
    private readonly IEquipamentoDatasetBusiness _equipamentoBusiness;

    public EquipamentoDatasetController(IEquipamentoDatasetBusiness equipamentoBusiness)
    {
        _equipamentoBusiness = equipamentoBusiness;
    }
    
    [HttpGet("transformacao")]
    [Consumes(MediaTypeNames.Application.Json)]
    public async Task<IActionResult> GetTransformacaoDataset([FromQuery] string codigoDoCentro, CancellationToken cancellationToken)
        => Ok(await _equipamentoBusiness.ObterTransformacaoAsync(codigoDoCentro, cancellationToken));
    
    [HttpGet("compensador")]
    [Consumes(MediaTypeNames.Application.Json)]
    public async Task<IActionResult> CompensadoresDataset([FromQuery] string codigoDoCentro, CancellationToken cancellationToken)
        => Ok(await _equipamentoBusiness.ObterCompensadoresAsync(codigoDoCentro, cancellationToken));
    
    [HttpGet("instalacao-compensador")]
    [Consumes(MediaTypeNames.Application.Json)]
    public async Task<IActionResult> InstalacaoCompensadoresDataset([FromQuery] string codigoDoCentro, CancellationToken cancellationToken)
        => Ok(await _equipamentoBusiness.ObterInstalacaoCompensadoresAsync(codigoDoCentro, cancellationToken));
    
    [HttpGet("banco-capacitor")]
    [Consumes(MediaTypeNames.Application.Json)]
    public async Task<IActionResult> BancoDeCapacitorDataset([FromQuery] string codigoDoCentro, CancellationToken cancellationToken)
        => Ok(await _equipamentoBusiness.ObterBancoDeCapacitoresAsync(codigoDoCentro, cancellationToken));
    
    [HttpGet("reator")]
    [Consumes(MediaTypeNames.Application.Json)]
    public async Task<IActionResult> ReatorDataset([FromQuery] string codigoDoCentro, CancellationToken cancellationToken)
        => Ok(await _equipamentoBusiness.ObterReatoresAsync(codigoDoCentro, cancellationToken));

    [HttpGet("elo")]
    [Consumes(MediaTypeNames.Application.Json)]
    public async Task<IActionResult> EloDataset([FromQuery] string codigoDoCentro, CancellationToken cancellationToken)
       => Ok(await _equipamentoBusiness.ObterElosAsync(codigoDoCentro, cancellationToken));
}
