using System.Net.Mime;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ONS.SINapse.Dados.Business.Dtos.Dataset;
using ONS.SINapse.Dados.Business.Interfaces.Dataset;
using ONS.SINapse.Dados.Shared.Identity;
using ONS.SINapse.Dados.Shared.Identity.Claims;

namespace ONS.SINapse.Dados.Api.Controllers.Datasets;

[Route("api/agentes/dataset")]
[Authorize]
[ClaimRequirement(PopClaimTypes.Role, PopClaimValues.Roles.ApiIntegracao)]
[ProducesResponseType(typeof(DatasetItemDto), 200)]
public class AgenteDatasetController : ControllerBase
{
    private readonly IAgenteDatasetBusiness _agenteDatasetBusiness;
    
    public AgenteDatasetController(IAgenteDatasetBusiness agenteDatasetBusiness)
    {
        _agenteDatasetBusiness = agenteDatasetBusiness;
    }
    
    [HttpGet]
    [Consumes(MediaTypeNames.Application.Json)]
    public async Task<IActionResult> GetAgentesDataset(string codigoDoCentro, CancellationToken cancellationToken = default) 
        => Ok(await _agenteDatasetBusiness.ObterAgentesAsync(codigoDoCentro, cancellationToken));
}