using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ONS.SINapse.Dados.Business.Dtos.Dataset;
using ONS.SINapse.Dados.Business.Interfaces.Dataset;
using ONS.SINapse.Dados.Shared.Identity;
using ONS.SINapse.Dados.Shared.Identity.Claims;

namespace ONS.SINapse.Dados.Api.Controllers.Datasets;

[Route("api/usinas/dataset")]
[Authorize]
[ClaimRequirement(PopClaimTypes.Role, PopClaimValues.Roles.ApiIntegracao)]
[ProducesResponseType(typeof(DatasetItemDto), 200)]
public class UsinaConjuntoUsinaDatasetController : ControllerBase
{
    private readonly IUsinaConjuntoUsinaDatasetBusiness _usinaConjuntoUsinaDatasetBusiness;

    public UsinaConjuntoUsinaDatasetController(IUsinaConjuntoUsinaDatasetBusiness usinaConjuntoUsinaDatasetBusiness)
    {
        _usinaConjuntoUsinaDatasetBusiness = usinaConjuntoUsinaDatasetBusiness;
    }

    [HttpGet("unidades-geradoras")]
    public async Task<IActionResult> UgeAsync([FromQuery] string codigoDoCentro, CancellationToken cancellationToken)
    {
        var dados =
            await _usinaConjuntoUsinaDatasetBusiness.ObterUgesAsync(codigoDoCentro, cancellationToken);
        
        return Ok(dados);
    }
    
    [HttpGet("cag")]
    public async Task<IActionResult> CagAsync([FromQuery] string codigoDoCentro, [FromQuery] string[]? codigoDoTipoDeFonte, CancellationToken cancellationToken)
    {
        var dados = 
            await _usinaConjuntoUsinaDatasetBusiness.ObterUsinaAsync(codigoDoCentro, codigoDoTipoDeFonte, true, cancellationToken);
        
        return Ok(dados);
    }

    [HttpGet("usina")]
    public async Task<IActionResult> UsinasAsync([FromQuery] string codigoDoCentro, [FromQuery] string[]? codigoDoTipoDeFonte, CancellationToken cancellationToken)
    {
        var dados = 
            await _usinaConjuntoUsinaDatasetBusiness.ObterUsinaAsync(codigoDoCentro, codigoDoTipoDeFonte, cancellationToken: cancellationToken);
        
        return Ok(dados);
    }
    
    [HttpGet("conjunto-usina")]
    public async Task<IActionResult> ConjuntosUsinaAsync([FromQuery] string codigoDoCentro, [FromQuery] string? codigoDoTipoDeFonte, CancellationToken cancellationToken)
    {
        var dados = 
            await _usinaConjuntoUsinaDatasetBusiness.ObterConjuntoUsinaAsync(codigoDoCentro, codigoDoTipoDeFonte, cancellationToken: cancellationToken);
        
        return Ok(dados);
    }
}