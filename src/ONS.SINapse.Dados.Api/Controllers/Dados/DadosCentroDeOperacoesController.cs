using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ONS.SINapse.Dados.Business.Dtos.EspelhoTabelaQuente;
using ONS.SINapse.Dados.Business.Queries;
using ONS.SINapse.Dados.Business.Queries.Filters;
using ONS.SINapse.Dados.Business.Queries.Filters.DadosCadastrais;
using ONS.SINapse.Dados.Shared.Identity;
using ONS.SINapse.Dados.Shared.Identity.Claims;

namespace ONS.SINapse.Dados.Api.Controllers.Dados;

[Route("api/dados")]
[Authorize]
[ClaimRequirement(PopClaimTypes.Role, PopClaimValues.Roles.ApiIntegracao)]
public class DadosCentroDeOperacoesController : ControllerBase
{
    private readonly IDadosQuery<EspelhoCentroDeOperacaoDto, DadosCentroDeOperacaoFilter> _dadosCentroDeOperacaoQuery;

    public DadosCentroDeOperacoesController(IDadosQuery<EspelhoCentroDeOperacaoDto, DadosCentroDeOperacaoFilter> dadosCentroDeOperacaoQuery)
    {
        _dadosCentroDeOperacaoQuery = dadosCentroDeOperacaoQuery;
    }
    
    [HttpGet("centro-operacao")]
    public async Task<IActionResult> ObterCentrosDeOperacao([FromQuery] DadosCentroDeOperacaoFilter? filter, CancellationToken cancellationToken)
    {
        return Ok(await _dadosCentroDeOperacaoQuery.ObterDadosAsync(filter, cancellationToken));
    }
}