using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ONS.SINapse.Dados.Business.Dtos.EspelhoTabelaQuente;
using ONS.SINapse.Dados.Business.Queries;
using ONS.SINapse.Dados.Business.Queries.Filters.DadosCadastrais;
using ONS.SINapse.Dados.Shared.Identity;
using ONS.SINapse.Dados.Shared.Identity.Claims;

namespace ONS.SINapse.Dados.Api.Controllers.Dados
{
    [Route("api/dados")]
    [Authorize]
    [ClaimRequirement(PopClaimTypes.Role, PopClaimValues.Roles.ApiIntegracao)]
    public class DadosUsinaController : ControllerBase
    {
        private readonly IDadosQuery<EspelhoUsinaDto, DadosUsinaFilter> _dadosUsinaQuery;
        private readonly IDadosQuery<EspelhoConjuntoDeUsinaDto, DadosConjuntoDeUsinaFilter> _dadosConjuntoDeUsinaQuery;

        public DadosUsinaController(
            IDadosQuery<EspelhoUsinaDto, DadosUsinaFilter> dadosUsinaQuery,
            IDadosQuery<EspelhoConjuntoDeUsinaDto, DadosConjuntoDeUsinaFilter> dadosConjuntoDeUsinaQuery)
        {
            _dadosUsinaQuery = dadosUsinaQuery;
            _dadosConjuntoDeUsinaQuery = dadosConjuntoDeUsinaQuery;
        }

        [HttpGet("usina")]
        public async Task<IActionResult> ObterUsina([FromQuery] DadosUsinaFilter? filter, CancellationToken cancellationToken)
        {
            return Ok(await _dadosUsinaQuery.ObterDadosAsync(filter, cancellationToken));
        }

        [HttpGet("conjunto-usina")]
        public async Task<IActionResult> ObterConJuntoUsina([FromQuery] DadosConjuntoDeUsinaFilter? filter, CancellationToken cancellationToken)
        {
            return Ok(await _dadosConjuntoDeUsinaQuery.ObterDadosAsync(filter, cancellationToken));
        }
    }
}
