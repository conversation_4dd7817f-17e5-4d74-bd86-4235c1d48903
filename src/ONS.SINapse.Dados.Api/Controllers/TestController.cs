using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using MongoDB.Bson;
using MongoDB.Driver;
using ONS.SINapse.Dados.Shared.Extensions;
using ONS.SINapse.Dados.Shared.Identity;
using ONS.SINapse.Dados.Shared.Identity.Claims;
using StackExchange.Redis;

namespace ONS.SINapse.Dados.Api.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
[ClaimRequirement(PopClaimTypes.Role, PopClaimValues.Roles.Sincronizador)]
public class TestController : ControllerBase
{
    private readonly IUserContext _userContext;
    private readonly IDatabase _redis;
    private readonly IMongoClient _mongoClient;
    private const string HealthStatus = "Healthy";

    public TestController(IUserContext userContext, IDatabase redis, IMongoClient mongoClient)
    {
        _userContext = userContext;
        _redis = redis;
        _mongoClient = mongoClient;
    }
    
    [HttpGet(Name = "check")]
    public string Get() => HealthStatus;

    [HttpGet("authorization")]
    public IActionResult Authorization()
    {
        return Ok(_userContext.Usuario());
    }

    [HttpGet("redis-health")]
    public IActionResult Redis()
    {
        var result = _redis.Execute("ping");
        return Ok(result.ToString());
    }
    
    [HttpGet("redis-dados")]
    public IActionResult Redis([FromQuery] string chave)
    {
        var result = _redis.StringGet(chave);
        return Ok(result.ToString());
    }
    
    private const string DatabaseName = "admin";
    private static readonly BsonDocumentCommand<BsonDocument> Command = new(BsonDocument.Parse("{ping:1}"));
    
    [HttpGet("mongo-health")]
    public async Task<IActionResult> Mongo(CancellationToken cancellationToken)
    {
        try
        {
            await _mongoClient
                .GetDatabase(DatabaseName)
                .RunCommandAsync(Command, cancellationToken: cancellationToken)
                .ConfigureAwait(false);
            
            return Ok("PONG");
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message, inner = ex.InnerException?.Message });
        }
    }
}