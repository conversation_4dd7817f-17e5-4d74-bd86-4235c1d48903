using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.OutputCaching;
using ONS.SINapse.Dados.Shared.Cache.Policy;

namespace ONS.SINapse.Dados.Api.Controllers;

[ApiController]
[Route("api/angularsettings")]
public class AngularSettingsController : ControllerBase
{
    /// <summary>
    /// Retorna o status de configuração Angular.
    /// </summary>
    /// <returns>Objeto com o status de configuração.</returns>
    [HttpGet]
    [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
    [OutputCache(PolicyName = nameof(DatasetOutputCachePolicy))]
    public IActionResult Get()
    {
        return Ok(new
        {
            Message = "Teste de cache",
            CachedTime = DateTime.UtcNow
        });
    }
}
