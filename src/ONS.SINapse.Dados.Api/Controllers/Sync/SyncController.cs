using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ONS.SINapse.Dados.Entities;
using ONS.SINapse.Dados.Repository.Context;
using ONS.SINapse.Dados.Shared.Identity;
using ONS.SINapse.Dados.Shared.Identity.Claims;
using ONS.SINapse.Dados.SyncData;
using ONS.SINapse.Dados.SyncData.Syncs.EntriesSync;

namespace ONS.SINapse.Dados.Api.Controllers.Sync;

[ApiController]
[Route("api/sync")]
public class SyncController : ControllerBase
{
    private readonly ISyncEntryHandler _syncEntryHandler;
    private readonly IEntrySyncService _entrySyncService;
    private readonly IRedisContext _redisContext;

    public SyncController(ISyncEntryHandler syncEntryHandler, IEntrySyncService entrySyncService, IRedisContext redisContext)
    {
        _syncEntryHandler = syncEntryHandler;
        _entrySyncService = entrySyncService;
        _redisContext = redisContext;
    }
   

    [HttpPost]
    [XTokenRequirement(XTokenRequirementOrigin.KongOns)]
    public async Task<IActionResult> Sync([FromQuery] string executor, [FromBody] List<SyncRecord> data)
    {
        await _entrySyncService.SincronizarDadosAsync(executor, data, CancellationToken.None);
        return Ok();
    }
    
    
    [HttpPost("done")]
    [ClaimRequirement(PopClaimTypes.Role, PopClaimValues.Roles.ApiIntegracao)]
    public async Task<bool> Done(IEnumerable<string> data, CancellationToken cancellationToken)
        => await _syncEntryHandler.DoneAsync(data, cancellationToken);

    [HttpGet("done")]
    [ClaimRequirement(PopClaimTypes.Role, PopClaimValues.Roles.ApiIntegracao)]
    public async Task<IActionResult> Done(CancellationToken cancellationToken)
        => Ok(await _syncEntryHandler.DoneAsync(cancellationToken));

    [HttpGet("cache-keys")]
    [Authorize]
    public List<string> GetCacheKeys()
        => _redisContext.GetKeysAsync();

    [HttpDelete("cache-keys/{key}")]
    [ClaimRequirement(PopClaimTypes.Role, PopClaimValues.Roles.ApiIntegracao)]
    public Task GetCacheKeys(string key)
        => _redisContext.InvalidarAsync(key);
}