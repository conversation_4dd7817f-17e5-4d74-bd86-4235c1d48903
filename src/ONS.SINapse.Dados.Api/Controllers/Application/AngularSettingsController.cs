using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using ONS.SINapse.Dados.Shared.Settings;

namespace ONS.SINapse.Dados.Api.Controllers.Application;

[Route("api/angularsettings")]
public class AngularSettingsController(
    IOptions<ConfiguracoesPopAuthSettings> authorizationSettings, IConfiguration configuration)
    : ControllerBase
{
    //TODO: Remover IConfiguration de ambiente e melhoria na autenticação
    
    private ConfiguracoesPopAuthSettings _authorizationSettings = authorizationSettings.Value;


    [HttpGet]
    public IActionResult Get()
    {
        return Ok(
            new
            {
                _authorizationSettings.Origin,
                _authorizationSettings.TokenUrl,
                _authorizationSettings.ClientId,
                _authorizationSettings.UserName,
                _authorizationSettings.GrantTypeRefreshToken,
                _authorizationSettings.GrantTypeAccessToken
            });
    }

    [HttpGet("configuration")]
    public IActionResult GetConfiguration()
    {
        return Ok(configuration.AsEnumerable().ToDictionary(k => k.Key, v => v.Value));
    }
    
}