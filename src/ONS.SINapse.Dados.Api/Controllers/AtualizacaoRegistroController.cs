using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ONS.SINapse.Dados.SyncData.Services;

namespace ONS.SINapse.Dados.Api.Controllers;

[Authorize]
[ApiController]
[Route("api/atualizacao-dados")]
public class AtualizacaoRegistroController : ControllerBase
{
    private readonly IAtualizacaoDeDadosService _atualizacaoDeDadosService;

    public AtualizacaoRegistroController(IAtualizacaoDeDadosService atualizacaoDeDadosService)
    {
        _atualizacaoDeDadosService = atualizacaoDeDadosService;
    }

    [HttpGet("ultima-atualizacao")]
    public async Task<IActionResult> Buscar([FromQuery] string collection, CancellationToken cancellationToken)
    {
        return Ok(await _atualizacaoDeDadosService.BuscarAsync(collection, cancellationToken));
    }
    
    [HttpGet("listar-ultima-atualizacao")]
    public async Task<IActionResult> Buscar([FromQuery] string[] collection, CancellationToken cancellationToken)
    {
        return Ok(await _atualizacaoDeDadosService.BuscarAsync(collection, cancellationToken));
    }
}