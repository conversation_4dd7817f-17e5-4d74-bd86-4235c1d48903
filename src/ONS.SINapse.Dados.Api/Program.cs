
using ONS.SINapse.Dados.Api.Configuration;
using ONS.SINapse.Dados.CrossCutting;
using ONS.SINapse.Dados.Shared.Diagnostics;
using ONS.SINapse.Dados.Shared.Middleware;

var builder = WebApplication.CreateBuilder(args);

builder.WebHost.ConfigureSinapseDadosApplication();

// Add services to the container.

DependencyInjector.Register(builder.Services, builder.Configuration);

builder.Services.AddCors(options => { 
    options.AddPolicy("CORS_POLICY",
        corsPolicyBuilder => corsPolicyBuilder
            .SetIsOriginAllowed( _ => true)
            .AllowAnyMethod()
            .AllowAnyHeader()
            .AllowCredentials()
            .Build()
    ); 
});

builder.Services.AddControllers();

builder.Services.AddSwaggerConfiguration();

builder.Services.AddSinapseHealthCheck(builder.Configuration);

builder.Services.AddTransient<ExceptionMiddleware>();

builder.AddSinapseOpenTelemetry();

var app = builder.Build();

app.UseSwaggerConfiguration();

app.UseHttpsRedirection();

app.UseAuthentication();
app.UseAuthorization();

app.MapControllers();

app.UseSinapseHealthCheck();

app.UseMiddleware<ExceptionMiddleware>();

app.Run();