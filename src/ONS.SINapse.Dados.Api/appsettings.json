{"AllowedHosts": "*", "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "MongoDbSettings": {"ConnectionString": "*************************************************************************************************************************************************************************************************************************************", "DatabaseName": "SINAPSE", "MaxConnections": 50, "MaxConnectionIdleTimeInSeconds": 30}, "ConnectionStrings": {"Redis": "redis.sinapse.svc.cluster.local:6379,password=ons123,defaultDatabase=0"}, "ONS": {"IgnoreConfigIT": false, "Authorization": {"Issuer": "", "Audience": "SINAPSE", "UseRsa": "true", "RsaModulus": "", "RsaPublicExponent": ""}, "PopAuth": {"Origin": "http://local.ons.org.br", "TokenURL": "", "clientId": "SINAPSE", "username": "", "password": "", "grantTypeRefreshToken": "refresh_token", "grantTypeAccessToken": "password"}}, "DataSyncSettings": {"SyncWaitTimeInMinutes": 5}, "XTokenRequirementSettings": {"LambdaAws": "976DF373-D6A3-4E38-99FF-573AD43E72D2", "KongOns": "99C29281-8B0C-468C-92D2-660D0361FFC6"}, "ApplicationSettings": {"OpenshiftHealthCheckUrl": "", "ApiSiNapseHealthCheckUrl": "", "ApiIntegracaoHealthCheckUrl": ""}, "OpenTelemetrySettings": {"ServiceName": "ons.sinapse.dados.api", "ServiceVersion": "1.0.0", "ServiceEnabled": true}}