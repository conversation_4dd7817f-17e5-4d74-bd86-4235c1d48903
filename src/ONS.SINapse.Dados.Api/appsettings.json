{"AllowedHosts": "*", "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "ConnectionStrings": {"Redis": "", "SqlServerConnection": "Server=************;Database=SINAPSE;User Id=consultasinapse;Password=consultasinapse;TrustServerCertificate=True;", "SqlServerReadOnly": "Server=************;Database=SINAPSE;User Id=consultasinapse;Password=consultasinapse;TrustServerCertificate=True;"}, "ONS": {"IgnoreConfigIT": false, "Authorization": {"Issuer": "https://poptst.ons.org.br/ons.pop.federation/", "Audience": "SINAPSE", "UseRsa": "true", "RsaModulus": "", "RsaPublicExponent": ""}, "PopAuth": {"Origin": "http://local.ons.org.br", "TokenURL": "https://poptst.ons.org.br/ons.pop.federation/oauth2/token", "clientId": "SINAPSE", "username": "ons\\_serviceodsinapse", "password": "", "grantTypeRefreshToken": "refresh_token", "grantTypeAccessToken": "password"}}, "ApplicationSettings": {"SinapseDadosHealthCheckUrl": "https://sinapse-dados-api-tst.apps.ocpd.ons.org.br/health", "SinapseHealthCheckUrl": "https://api.devsinapse.ons.org.br/health", "SinapseIntegracaoHealthCheckUrl": "https://api.devsinapseintegracao.ons.org.br/health"}, "OpenTelemetrySettings": {"ServiceName": "ons.sinapse.dados.api", "ServiceVersion": "1.0.0", "ServiceEnabled": true}, "OutputCacheSettings": {"ResponseExpirationTime": 5}}