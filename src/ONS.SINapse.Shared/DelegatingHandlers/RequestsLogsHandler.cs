using System.Text;
using Microsoft.Extensions.Logging;
using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Shared.Logs;

namespace ONS.SINapse.Shared.DelegatingHandlers;

public class RequestsLogsHandler<TLogger> : DelegatingHandler where TLogger : ApiLogs
{
    private readonly ILogger<TLogger> _logger;
    private readonly TLogger _loggerOptions;

    public RequestsLogsHandler(ILogger<TLogger> logger, TLogger loggerOptions)
    {
        _logger = logger;
        _loggerOptions = loggerOptions;
    }

    protected override async Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
    {
        var message = new StringBuilder();

        message = await RequestLoggerAsync(message, request, cancellationToken);
        
        var result = await base.SendAsync(request, cancellationToken);
        
        message = await ResponseLoggerAsync(message, result, cancellationToken);
        
        _logger.LogInformation("Request realizada... \n {message}", message.ToString());
        
        return result;
    }

    private async Task<StringBuilder> RequestLoggerAsync(StringBuilder message, HttpRequestMessage request, CancellationToken cancellationToken)
    {
        var options = _loggerOptions.Request;
        
        if (options is null) return message;
        
        if(!options.SemUri)
            message.AppendLine("Request Uri: " + request.RequestUri?.RemoveAuthToken());
        
        if(!options.SemMethod)
            message.AppendLine("Request Method: " + request.Method);
        
        if(!options.SemContent && request.Content is not null)
            message.AppendLine("Request Content: "+ await request.Content.ReadAsStringAsync(cancellationToken));
        
        if(!options.SemHeader)
            message.AppendLine("Request Headers: " + request.Headers);

        return message;
    }

    private async Task<StringBuilder> ResponseLoggerAsync(StringBuilder message, HttpResponseMessage response, CancellationToken cancellationToken)
    {
        var options = _loggerOptions.Response;

        if (options is null) return message;
        
        if(response.IsSuccessStatusCode && options.ApenasRespostaComErro) return message;
        
        if(!options.SemStatus)
            message.AppendLine($"Status Retornado: {response.StatusCode}");
        
        if(!options.SemHeader)
            message.AppendLine("Request Headers: " + response.Headers);

        if (options.SemContent) return message;
        
        try
        {
            message.AppendLine($"Content Retornado: {await response.Content.ReadAsStringAsync(cancellationToken)}");
        }
        catch (Exception)
        {
            message.AppendLine("Content Retornado: Nenhum content pode ser lido do resulta da requisição.");
        }

        return message;
    }
}