using System.Net;
using System.Runtime.Serialization;

namespace ONS.SINapse.Shared.DelegatingHandlers;

public class FirebaseHttpMessageHandler : DelegatingHandler
{
    protected override async Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
    {
        var response = await base.SendAsync(request, cancellationToken);
        
        if (response.StatusCode == HttpStatusCode.Unauthorized)
        {
            // O realtime database do Firebase irá retornar 401 quando o token estiver inválido ou quando as regras de validação de dados não forem atendidas.
            // não é possivel destinguir entre os dois casos, então é necessário retornar a mensagem genérica.
            throw new FirebaseHttpException("Erro ao acessar o Firebase: ou o token está inválido ou a operação não foi permitida devido validação de dados.");
        }
        
        return response;
    }
}

[Serializable]
public class FirebaseHttpException : Exception
{
    public FirebaseHttpException()
    { }

    public FirebaseHttpException(string message)
        : base(message)
    { }

    public FirebaseHttpException(string message, Exception innerException)
        : base(message, innerException)
    { }

    protected FirebaseHttpException(SerializationInfo info, StreamingContext context)
        : base(info.GetString("Message"), (Exception)info.GetValue("InnerException", typeof(Exception))!)
    {
    }
}