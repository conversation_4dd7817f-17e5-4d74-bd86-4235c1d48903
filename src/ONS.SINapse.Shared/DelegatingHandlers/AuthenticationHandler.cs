using System.Net.Http.Headers;
using ONS.SINapse.Shared.Services;
using ONS.SINapse.Shared.Settings;

namespace ONS.SINapse.Shared.DelegatingHandlers;


public class AuthenticationHandler<TApiOptions> : DelegatingHandler where TApiOptions : ApiOptions
{
    private readonly IAuthenticationProvider<TApiOptions> _provider;
    
    public AuthenticationHandler(IAuthenticationProvider<TApiOptions> provider) => _provider = provider;

    protected override async Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
    {
        var token = await _provider.ObterToken();
        request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", token.AccessToken);
        return await base.SendAsync(request, cancellationToken).ConfigureAwait(false);
    }
}
