using MongoDB.Bson.Serialization.Attributes;

namespace ONS.SINapse.Shared.Contracts;

public class StatusServiceContract
{
    [BsonRequired]
    [BsonElement("_id")]
    public string Service { get; set; }
    [BsonElement("health")]
    public bool Health { get; set; }
    [BsonElement("exception")]
    public Exception? Exception { get; set; }
    [BsonElement("error")]
    public string? Error { get; set; }
    [BsonElement("din_criacao")]
    public DateTime Date { get; set; }

    public StatusServiceContract(string service, bool health, Exception? exception = null, string? error = null)
    {
        Service = service;
        Health = health;
        Date = DateTime.UtcNow;
        Exception = exception;
        Error = error ?? exception?.Message;
    }
}