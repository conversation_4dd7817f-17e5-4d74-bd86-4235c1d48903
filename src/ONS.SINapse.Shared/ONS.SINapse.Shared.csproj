<Project Sdk="Microsoft.NET.Sdk">

    <!-- Propriedades específicas do projeto (se necessário) -->
    <PropertyGroup>
        <!-- Propriedades específicas serão herdadas do Directory.Build.props -->
    </PropertyGroup>

    <!-- Pacotes NuGet - versões gerenciadas centralmente -->
    <ItemGroup>
        <PackageReference Include="Amazon.Extensions.Configuration.SystemsManager" />
        <PackageReference Include="AspNetCore.HealthChecks.Redis" />
        <PackageReference Include="AspNetCore.HealthChecks.UI" />
        <PackageReference Include="AspNetCore.HealthChecks.UI.Client" />
        <PackageReference Include="AspNetCore.HealthChecks.UI.Core" />
        <PackageReference Include="AspNetCore.HealthChecks.UI.InMemory.Storage" />
        <PackageReference Include="AutoMapper" />
        <PackageReference Include="AWSSDK.S3" />
        <PackageReference Include="Confluent.Kafka" />
        <PackageReference Include="CsvHelper" />
        <PackageReference Include="FirebaseDatabase.net" />
        <PackageReference Include="FluentValidation" />
        <PackageReference Include="HunspellSharp" />
        <PackageReference Include="librdkafka.redist" />
        <PackageReference Include="LinqKit.Core" />
        <PackageReference Include="LiteDB" />
        <PackageReference Include="MassTransit" />
        <PackageReference Include="MassTransit.AmazonSQS" />
        <PackageReference Include="MassTransit.Newtonsoft" />
        <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" />
        <PackageReference Include="Microsoft.AspNetCore.Authentication.WsFederation" />
        <PackageReference Include="Microsoft.Extensions.DependencyInjection" />
        <PackageReference Include="Microsoft.Extensions.Caching.Memory" />
        <PackageReference Include="Microsoft.Extensions.Caching.StackExchangeRedis" />
        <PackageReference Include="Microsoft.IdentityModel.Tokens" />
        <PackageReference Include="MongoDB.Bson" />
        <PackageReference Include="MongoDB.Driver" />
        <PackageReference Include="MongoDB.Driver.Core.Extensions.DiagnosticSources" />
        <PackageReference Include="Nanoid" />
        <PackageReference Include="Newtonsoft.Json" />
        <PackageReference Include="OpenTelemetry" />
        <PackageReference Include="OpenTelemetry.Api" />
        <PackageReference Include="OpenTelemetry.Exporter.Console" />
        <PackageReference Include="OpenTelemetry.Exporter.OpenTelemetryProtocol" />
        <PackageReference Include="OpenTelemetry.Exporter.Prometheus.AspNetCore" />
        <PackageReference Include="OpenTelemetry.Extensions.Hosting" />
        <PackageReference Include="OpenTelemetry.Instrumentation.AspNetCore" />
        <PackageReference Include="OpenTelemetry.Instrumentation.Http" />
        <PackageReference Include="OpenTelemetry.Instrumentation.Process" />
        <PackageReference Include="OpenTelemetry.Instrumentation.Runtime" />
        <PackageReference Include="Refit" />
        <PackageReference Include="Refit.HttpClientFactory" />
        <PackageReference Include="Refit.Newtonsoft.Json" />
        <PackageReference Include="Scrutor" />
        <PackageReference Include="Serilog.AspNetCore" />
        <PackageReference Include="Swashbuckle.AspNetCore" />
        <PackageReference Include="System.IdentityModel.Tokens.Jwt" />
        <PackageReference Include="System.Linq.Expressions" />
    </ItemGroup>

    <!-- App Metrics -->
    <ItemGroup>
        <PackageReference Include="App.Metrics" />
        <PackageReference Include="App.Metrics.AspNetCore" />
        <PackageReference Include="App.Metrics.AspNetCore.Reporting" />
        <PackageReference Include="App.Metrics.AspNetCore.Tracking" />
        <PackageReference Include="App.Metrics.Core" />
        <PackageReference Include="App.Metrics.Extensions.Configuration" />
        <PackageReference Include="App.Metrics.Extensions.HealthChecks" />
        <PackageReference Include="App.Metrics.Formatters.Json" />
    </ItemGroup>

    <!-- Firebase e outras bibliotecas -->
    <ItemGroup>
        <PackageReference Include="FirebaseAdmin" />
        <PackageReference Include="FirebaseAuthentication.net" />
        <PackageReference Include="GeoCoordinate.NetCore" />
        <PackageReference Include="Polly" />
    </ItemGroup>
    <ItemGroup>
        <Content Include="Resources\monitor-ui.css">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </Content>
    </ItemGroup>
    <ItemGroup>
        <ProjectReference Include="..\ONS.SINapse.Integracao.Shared\ONS.SINapse.Integracao.Shared.csproj" />
    </ItemGroup>
</Project>
