<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>
    <ItemGroup>
		<PackageReference Include="Amazon.Extensions.Configuration.SystemsManager" Version="6.2.2" />
        <PackageReference Include="AspNetCore.HealthChecks.Redis" Version="8.0.1" />
        <PackageReference Include="AspNetCore.HealthChecks.SqlServer" Version="8.0.2" />
        <PackageReference Include="AspNetCore.HealthChecks.UI" Version="8.0.2" />
        <PackageReference Include="AspNetCore.HealthChecks.UI.Client" Version="8.0.1" />
        <PackageReference Include="AspNetCore.HealthChecks.UI.Core" Version="8.0.1" />
        <PackageReference Include="AspNetCore.HealthChecks.UI.InMemory.Storage" Version="8.0.1" />
        <PackageReference Include="AutoMapper" Version="13.0.1" />
        <PackageReference Include="AWSSDK.S3" Version="3.7.410.6" />
        <PackageReference Include="Confluent.Kafka" Version="2.8.0" />
        <PackageReference Include="CsvHelper" Version="33.0.1" />
        <PackageReference Include="FirebaseDatabase.net" Version="4.2.0" />
        <PackageReference Include="FluentValidation" Version="11.11.0" />
        <PackageReference Include="HunspellSharp" Version="1.0.1" />
        <PackageReference Include="librdkafka.redist" Version="2.8.0" />
        <PackageReference Include="LinqKit.Core" Version="1.2.7" />
        <PackageReference Include="MassTransit" Version="8.3.2" />
		<PackageReference Include="MassTransit.AmazonSQS" Version="8.3.2" />
		<PackageReference Include="MassTransit.Newtonsoft" Version="8.3.2" />
		<PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.11" />
        <PackageReference Include="Microsoft.AspNetCore.Authentication.WsFederation" Version="8.0.11" />
        <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.1" />
        <PackageReference Include="Microsoft.Extensions.Caching.StackExchangeRedis" Version="8.0.11" />
        <PackageReference Include="Microsoft.IdentityModel.Tokens" Version="8.2.1" />
        <PackageReference Include="MongoDB.Bson" Version="3.0.0" />
        <PackageReference Include="MongoDB.Driver" Version="3.0.0" />
        <PackageReference Include="Nanoid" Version="3.1.0" />
        <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
		<PackageReference Include="OpenTelemetry" Version="1.9.0" />
		<PackageReference Include="OpenTelemetry.Exporter.Console" Version="1.9.0" />
		<PackageReference Include="OpenTelemetry.Exporter.OpenTelemetryProtocol" Version="1.9.0" />
        <PackageReference Include="OpenTelemetry.Exporter.Prometheus.AspNetCore" Version="1.9.0-beta.2" />
		<PackageReference Include="OpenTelemetry.Extensions.Hosting" Version="1.9.0" />
		<PackageReference Include="OpenTelemetry.Instrumentation.AspNetCore" Version="1.9.0" />
		<PackageReference Include="OpenTelemetry.Instrumentation.Http" Version="1.9.0" />
		<PackageReference Include="OpenTelemetry.Instrumentation.Process" Version="1.11.0-beta.2" />
        <PackageReference Include="OpenTelemetry.Instrumentation.Runtime" Version="1.11.1" />
		<PackageReference Include="Refit" Version="8.0.0" />
        <PackageReference Include="Refit.HttpClientFactory" Version="8.0.0" />
        <PackageReference Include="Refit.Newtonsoft.Json" Version="8.0.0" />
        <PackageReference Include="Scrutor" Version="5.0.2" />
        <PackageReference Include="Serilog.AspNetCore" Version="8.0.3" />
        <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.2.1" />
        <PackageReference Include="System.Linq.Expressions" Version="4.3.0" />
    </ItemGroup>
    <ItemGroup>
        <PackageReference Include="App.Metrics" Version="4.3.0" />
        <PackageReference Include="App.Metrics.AspNetCore" Version="4.3.0" />
        <PackageReference Include="App.Metrics.AspNetCore.Reporting" Version="4.0.0" />
        <PackageReference Include="App.Metrics.AspNetCore.Tracking" Version="4.3.0" />
        <PackageReference Include="App.Metrics.Core" Version="4.3.0" />
        <PackageReference Include="App.Metrics.Extensions.Configuration" Version="4.3.0" />
        <PackageReference Include="App.Metrics.Extensions.HealthChecks" Version="4.3.0" />
        <PackageReference Include="App.Metrics.Formatters.Json" Version="4.3.0" />
    </ItemGroup>
    <ItemGroup>
        <PackageReference Include="FirebaseAdmin" Version="3.1.0" />
        <PackageReference Include="FirebaseAuthentication.net" Version="3.7.2" />
        <PackageReference Include="GeoCoordinate.NetCore" Version="1.0.0.1" />
    </ItemGroup>
    <ItemGroup>
        <PackageReference Include="Polly" Version="8.5.0" />
    </ItemGroup>
    <ItemGroup>
        <Content Include="Resources\monitor-ui.css">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </Content>
    </ItemGroup>
    <ItemGroup>
        <ProjectReference Include="..\ONS.SINapse.Integracao.Shared\ONS.SINapse.Integracao.Shared.csproj" />
    </ItemGroup>
</Project>
