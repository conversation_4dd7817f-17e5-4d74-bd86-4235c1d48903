<Project Sdk="Microsoft.NET.Sdk">

    <!-- Propriedades específicas do projeto (se necessário) -->
    <PropertyGroup>
        <!-- Propriedades específicas serão herdadas do Directory.Build.props -->
    </PropertyGroup>

    <!-- Pacotes realmente compartilhados entre projetos -->
    <ItemGroup>
        <PackageReference Include="Amazon.Extensions.Configuration.SystemsManager" />
        <PackageReference Include="AWSSDK.S3" />
        <PackageReference Include="HunspellSharp" />
        <PackageReference Include="LiteDB" />
        <PackageReference Include="Microsoft.Extensions.DependencyInjection" />
        <PackageReference Include="Microsoft.IdentityModel.Tokens" />
        <PackageReference Include="Nanoid" />
        <PackageReference Include="Newtonsoft.Json" />
        <PackageReference Include="Scrutor" />
        <PackageReference Include="System.IdentityModel.Tokens.Jwt" />
        <PackageReference Include="System.Linq.Expressions" />
        <!-- Dependências necessárias para código compartilhado -->
        <PackageReference Include="MassTransit" />
        <PackageReference Include="FluentValidation" />
        <PackageReference Include="MongoDB.Bson" />
        <PackageReference Include="MongoDB.Driver" />
        <PackageReference Include="FirebaseDatabase.net" />
        <PackageReference Include="FirebaseAdmin" />
        <PackageReference Include="Google.Apis.FirebaseCloudMessaging.v1" />
        <PackageReference Include="Microsoft.AspNetCore.Http.Abstractions" />
        <PackageReference Include="Microsoft.AspNetCore.Mvc.Abstractions" />
        <PackageReference Include="Microsoft.Extensions.Caching.Abstractions" />
        <PackageReference Include="Microsoft.Extensions.Hosting.Abstractions" />
        <PackageReference Include="Microsoft.Extensions.Diagnostics.HealthChecks" />
        <PackageReference Include="Swashbuckle.AspNetCore.SwaggerGen" />
        <PackageReference Include="OpenTelemetry" />
        <PackageReference Include="OpenTelemetry.Api" />
        <PackageReference Include="OpenTelemetry.Exporter.Console" />
        <PackageReference Include="OpenTelemetry.Exporter.OpenTelemetryProtocol" />
        <PackageReference Include="OpenTelemetry.Extensions.Hosting" />
        <PackageReference Include="OpenTelemetry.Exporter.Prometheus.AspNetCore" />
        <PackageReference Include="OpenTelemetry.Instrumentation.AspNetCore" />
        <PackageReference Include="OpenTelemetry.Instrumentation.Http" />
        <PackageReference Include="OpenTelemetry.Instrumentation.Process" />
        <PackageReference Include="OpenTelemetry.Instrumentation.Runtime" />
        <PackageReference Include="StackExchange.Redis" />
        <PackageReference Include="Microsoft.Extensions.Caching.StackExchangeRedis" />
        <PackageReference Include="Serilog.AspNetCore" />
        <PackageReference Include="AspNetCore.HealthChecks.Redis" />
        <PackageReference Include="AspNetCore.HealthChecks.UI" />
        <PackageReference Include="AspNetCore.HealthChecks.UI.Client" />
        <PackageReference Include="AspNetCore.HealthChecks.UI.Core" />
        <PackageReference Include="AspNetCore.HealthChecks.UI.InMemory.Storage" />
        <PackageReference Include="App.Metrics" />
        <PackageReference Include="App.Metrics.AspNetCore" />
        <PackageReference Include="App.Metrics.Core" />
        <PackageReference Include="App.Metrics.Extensions.Configuration" />
        <PackageReference Include="App.Metrics.Extensions.HealthChecks" />
        <PackageReference Include="App.Metrics.Formatters.Json" />
        <PackageReference Include="FirebaseAuthentication.net" />
        <PackageReference Include="Swashbuckle.AspNetCore" />
    </ItemGroup>

    <!-- Utilitários realmente compartilhados -->
    <ItemGroup>
        <PackageReference Include="GeoCoordinate.NetCore" />
        <PackageReference Include="Polly" />
    </ItemGroup>
    <ItemGroup>
        <Content Include="Resources\monitor-ui.css">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </Content>
    </ItemGroup>
    <ItemGroup>
        <ProjectReference Include="..\ONS.SINapse.Integracao.Shared\ONS.SINapse.Integracao.Shared.csproj" />
    </ItemGroup>
</Project>
