using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using ONS.SINapse.Shared.Settings;
using OpenTelemetry;
using OpenTelemetry.Exporter;
using OpenTelemetry.Logs;
using OpenTelemetry.Metrics;
using OpenTelemetry.Resources;
using OpenTelemetry.Trace;

namespace ONS.SINapse.Shared.Telemetry;

public static class OpenTelemetryConfiguration
{
    private const string DefaultServiceVariableName = "OTEL_EXPORTER_OTLP_ENDPOINT";
    private const OtlpExportProtocol DefaultExportProtocol = OtlpExportProtocol.HttpProtobuf;
    public static WebApplicationBuilder AddSinapseOpenTelemetry(this WebApplicationBuilder builder)
    {
        var serviceName = builder.Environment.ApplicationName;
        var section = builder.Configuration
            .GetSection(nameof(OpenTelemetrySettings));

        var options = section.Get<OpenTelemetrySettings>()
                      ?? throw new NullReferenceException(nameof(OpenTelemetrySettings));

        builder.Services.Configure<OpenTelemetrySettings>(section);

        if (!options.IsValid())
        {
            Console.WriteLine("OpenTelemetry disabled");
            Console.WriteLine(
                "OpenTelemetry ServiceEndpoint {0}, ServiceName {1}", 
                options.ServiceEndpoint,
                serviceName
            );
            return builder;
        }
        
        var endpoint = Environment.GetEnvironmentVariable(DefaultServiceVariableName);

        if (string.IsNullOrEmpty(endpoint))
            Environment.SetEnvironmentVariable(DefaultServiceVariableName, options.ServiceEndpoint);

        Console.WriteLine(
            "Starting OpenTelemetry with ServiceEndpoint {0} and ServiceName {1}", 
            options.ServiceEndpoint,
            serviceName
        );

        var resourceBuilder = ResourceBuilder.CreateDefault()
            .AddService(serviceName, options.ServiceVersion)
            .AddTelemetrySdk();

        builder.Services.AddOpenTelemetry()
            .AddSinapseMetrics(resourceBuilder, serviceName)
            .AddSinapseTrace(resourceBuilder, serviceName);

        builder.Logging.AddSinapseLogsOpenTelemetry(resourceBuilder);

        return builder;
    }

    public static WebApplication UseSinapseOpenTelemetry(this WebApplication app)
    {
        var options = app.Services.GetRequiredService<IOptions<OpenTelemetrySettings>>().Value;

        if (!options.IsValid()) return app;

        app.MapPrometheusScrapingEndpoint();
        app.UseOpenTelemetryPrometheusScrapingEndpoint();

        return app;
    }

    private static ILoggingBuilder AddSinapseLogsOpenTelemetry(this ILoggingBuilder loggingBuilder, ResourceBuilder resourceBuilder)
    {
        loggingBuilder
            .ClearProviders()
            .AddOpenTelemetry(options =>
            {
                options
                    .SetResourceBuilder(resourceBuilder)
                    .AddOtlpExporter(exporter =>
                    {
                        exporter.Protocol = DefaultExportProtocol;
                    })
                    .AddConsoleExporter();

                options.IncludeFormattedMessage = true;
                options.IncludeScopes = true;
                options.ParseStateValues = true;
            });

        return loggingBuilder;
    }

    private static OpenTelemetryBuilder AddSinapseMetrics(this OpenTelemetryBuilder openTelemetryBuilder, ResourceBuilder resourceBuilder, string meterName)
    {
        openTelemetryBuilder.WithMetrics(builder =>
        {
            builder.AddMeter(meterName)
                .SetResourceBuilder(resourceBuilder)
                .AddHttpClientInstrumentation()
                .AddAspNetCoreInstrumentation()
                .AddRuntimeInstrumentation()
                .AddProcessInstrumentation()
                .AddPrometheusExporter()
                .AddOtlpExporter(exporter =>
                {
                    exporter.Protocol = DefaultExportProtocol;
                });
        });

        return openTelemetryBuilder;
    }

    private static OpenTelemetryBuilder AddSinapseTrace(this OpenTelemetryBuilder openTelemetryBuilder, ResourceBuilder resourceBuilder, string serviceName)
    {
        openTelemetryBuilder.WithTracing(tracerProviderBuilder =>
        {
            tracerProviderBuilder
                .AddSource(serviceName)
                .SetResourceBuilder(resourceBuilder)
                .AddAspNetCoreInstrumentation((options) =>
                {
                    options.Filter = (httpContext) =>
                    {
                        var routes = new List<string>
                            {
                                "/swagger",
                                "/angularsettings",
                                "/health"
                            };

                        return !routes.Exists(httpContext.Request.Path.Value!.ToLower().Contains);
                    };

                    options.RecordException = true;
                }
                )
                .AddHttpClientInstrumentation(options => options.RecordException = true)
                .AddAspNetCoreInstrumentation()
                .SetErrorStatusOnException()
                .AddOtlpExporter(exporter =>
                {
                    exporter.Protocol = DefaultExportProtocol;
                });
        });

        return openTelemetryBuilder;
    }
}
