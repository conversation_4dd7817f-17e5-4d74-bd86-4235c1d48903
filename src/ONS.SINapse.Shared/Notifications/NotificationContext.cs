using FluentValidation.Results;

namespace ONS.SINapse.Shared.Notifications;


public class NotificationContext
{
	private readonly List<Notification> _notifications;
	public IReadOnlyCollection<Notification> Notifications => _notifications;
	public bool HasNotifications => _notifications.Count > 0;
	public int Count => _notifications.Count;

	public NotificationContext()
	{
		_notifications = new List<Notification>();
	}

	public void AddNotification(string message)
    {
		var notification = new Notification(message);
		_notifications.Add(notification);
    }
	
	public void AddNotifications(string[] messages)
	{
		messages.ToList().ForEach(AddNotification);
	}

	public void AddNotifications(ValidationResult validationResult)
	{
		var notifications = validationResult.Errors
			.Select(error => new Notification(
				error.ErrorMessage,
				error.PropertyName 
			));

		_notifications.AddRange(notifications);
	}

	public void Clear()
	{
		_notifications.Clear();
	}
}
