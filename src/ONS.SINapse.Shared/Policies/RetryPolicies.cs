using ONS.SINapse.Shared.Settings;
using <PERSON>;
using Polly<PERSON>Retry;

namespace ONS.SINapse.Shared.Policies;

public static class RetryPolicies
{
    public static AsyncRetryPolicy FirebaseAsync(FirebaseSettings settings)
    {
        return Policy
            .Handle<Exception>()
            .WaitAndRetryAsync(
                settings.QuantidadeDeTentativas,
                i => TimeSpan.FromSeconds(settings.SegundosEntreTentativas));
    }
    public static RetryPolicy Firebase(FirebaseSettings settings)
    {
        return Policy
            .Handle<Exception>()
            .WaitAndRetry(
                settings.QuantidadeDeTentativas,
                i => TimeSpan.FromSeconds(settings.SegundosEntreTentativas));
    }
}
