
using ONS.SINapse.Shared.Extensions;

namespace ONS.SINapse.Shared.Helpers;

public static class EnumHelper
{
    public static Dictionary<short, string> ToDictionary<TEnum>()
        where TEnum : struct
    {
        if (!typeof(TEnum).IsEnum)
            throw new ArgumentException("Enum inválido.");

        return Enum.GetValues(typeof(TEnum))
            .Cast<object>()
            .ToDictionary(k => (short)k, v => ((Enum)v).GetDescription());
    }
}
