using GeoCoordinatePortable;

namespace ONS.SINapse.Shared.Helpers;

public record CoordenadaGeografica(double Latitude, double Longitude) 
{
    public double DistanciaEmKm(CoordenadaGeografica coordenadaGeografica)
    {
        double distance = double.MaxValue;

        try
        {
            GeoCoordinate point1 = new(Latitude, Longitude);
            GeoCoordinate point2 = new(coordenadaGeografica.Latitude, coordenadaGeografica.Longitude);
            distance = point1.GetDistanceTo(point2);
            return distance / 1000;
        }
        catch
        {
            return distance;
        }
    }

    public double DistanciaEmMetros(CoordenadaGeografica coordenadaGeografica)
    {
        double distance = double.MaxValue;

        try
        {
            return DistanciaEmKm(coordenadaGeografica) * 1000;
        }
        catch
        {
            return distance;
        }
    }

    public static implicit operator CoordenadaGeografica((double,double) coordenada) => new CoordenadaGeografica(coordenada.Item1, coordenada.Item2);
}