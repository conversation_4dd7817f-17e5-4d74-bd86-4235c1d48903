using System.Text;

namespace ONS.SINapse.Shared.Helpers;

public static class ArquivoCsvHelper
{
    public static byte[] GerarArquivo(IEnumerable<IEnumerable<string>> linhas, string separador = ";", IEnumerable<string>? cabecalho = null)
    {
        var conteudoDoArquivo = new StringBuilder();

        if (cabecalho is not null && cabecalho.Any())
        {
            conteudoDoArquivo
                .Append(string.Join(separador, cabecalho))
                .Append(Environment.NewLine);
        }

        foreach (var linha in linhas)
        {
            var linhaComEscape = linha
                .Select(campo => campo?.Replace(separador, ""));

            conteudoDoArquivo
                .Append(string.Join(separador, linhaComEscape))
                .Append(Environment.NewLine);
        }

        var data = Encoding.UTF8.GetBytes(conteudoDoArquivo.ToString());
        return Encoding.UTF8.GetPreamble().Concat(data).ToArray();
    }
}