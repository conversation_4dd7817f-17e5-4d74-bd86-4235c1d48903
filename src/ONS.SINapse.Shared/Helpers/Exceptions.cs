namespace ONS.SINapse.Shared.Helpers;

public static class Exceptions
{
    public static void ThrowIf<TException>(bool condition, string message)
        where TException : Exception, new()
    {
        if (condition) 
        {
            var exception = (TException)Activator
                .CreateInstance(typeof(TException), message)!;

            throw exception;
        }
    }

    public static void ThrowIf(bool condition, string message) 
        => ThrowIf<Exception>(condition, message);
}