using Microsoft.AspNetCore.Http;

namespace ONS.SINapse.Shared.Extensions;

public static class HttpContextExtension
{
    public static string ObterConnectionToken(this IHttpContextAccessor httpContextAccessor)
    {
        var context = httpContextAccessor.HttpContext;

        if (context is null) return string.Empty;
        var hasToken = context.Request.Query.TryGetValue("x-token", out var token);
        return hasToken ? token.ToString() : string.Empty;
    }
}