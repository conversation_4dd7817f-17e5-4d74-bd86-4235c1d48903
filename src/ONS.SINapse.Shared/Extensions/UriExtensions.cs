namespace ONS.SINapse.Shared.Extensions;

public static class UriExtensions
{
    public static IEnumerable<(string Chave, string? Valor)> SplitQueryString(this Uri address)
    {
        var query = address.Query.TrimStart('?');
        if (string.IsNullOrWhiteSpace(query))
            yield break;

        foreach (var element in query!.Split('&').Select(x => x.Split('='))
                     .Select(x => (x.First().ToLowerInvariant(), x.Skip(1).FirstOrDefault())))
            yield return element;
    }
    
    public static Dictionary<string, string?> SplitDictionaryQueryString(this Uri address)
    {
        return address
            .SplitQueryString()
            .ToDictionary(k => k.Chave, v => v.Valor);
    }
}