using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using ONS.SINapse.Shared.DelegatingHandlers;
using ONS.SINapse.Shared.Logs;
using ONS.SINapse.Shared.Settings;

namespace ONS.SINapse.Shared.Extensions;

public static class HttpClientBuilderExtensions
{
    public static void AddLoggerRequestHandler<TApiLogs>(this IHttpClientBuilder builder,
        Action<TApiLogs> configureOptions)
        where TApiLogs : ApiLogs, new()
    {
        var options = new TApiLogs();
        configureOptions(options);

        builder.Services.TryAddTransient<RequestsLogsHandler<TApiLogs>>();
        builder.Services.TryAddSingleton(options);

        builder.AddHttpMessageHandler<RequestsLogsHandler<TApiLogs>>();
    }

    public static IHttpClientBuilder AddAuthenticationRequestHandler<TApiOptions>(this IHttpClientBuilder builder, IConfiguration configuration) where TApiOptions : ApiOptions
    {
        builder.Services.AddApiAuthentication<TApiOptions>(configuration);
        builder.AddHttpMessageHandler<AuthenticationHandler<TApiOptions>>();
        return builder;
    }
    
    public static void AddFirebaseRequestHandler(this IHttpClientBuilder builder)
    {
        builder.Services.AddTransient<FirebaseHttpMessageHandler>();
        builder.AddHttpMessageHandler<FirebaseHttpMessageHandler>();
        builder.AddLoggerRequestHandler<FirebaseLogs>(options =>
        {
            options.AddRequestLog(requestLogs =>
            {
                requestLogs.SemUri = false;
                requestLogs.SemMethod = true;
                requestLogs.SemContent = false;
                requestLogs.SemHeader = true;
            });
            options.AddResposeLog(responseLogs =>
            {
                responseLogs.SemContent = true;
                responseLogs.SemHeader = true;
                responseLogs.ApenasRespostaComErro = true;
            });
        });
    }
}