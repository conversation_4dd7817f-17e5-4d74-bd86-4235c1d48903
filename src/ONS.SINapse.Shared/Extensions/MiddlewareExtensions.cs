using Microsoft.AspNetCore.Builder;
using ONS.SINapse.Shared.Middleware;

namespace ONS.SINapse.Shared.Extensions
{
    public static class MiddlewareExtensions
    {
        public static IApplicationBuilder UseGlobalExceptionHandlerMiddleware(this IApplicationBuilder app)
        {
            return app.UseMiddleware<GlobalExceptionHandlerMiddleware>();
        }

        public static IApplicationBuilder UseOptionsMiddleware(this IApplicationBuilder app)
        {
            return app.UseMiddleware(typeof(OptionsMiddleware));
        }
    }
}