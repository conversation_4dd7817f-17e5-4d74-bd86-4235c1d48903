using System.Runtime.InteropServices;

namespace ONS.SINapse.Shared.Extensions;

public static class DateExtensions
{
    private static readonly TimeZoneInfo AmericaTimeZoneInfo = DefineAmericaTimeZoneInfo();
    private static bool IsWindows() =>
        RuntimeInformation.IsOSPlatform(OSPlatform.Windows);

    private static TimeZoneInfo DefineAmericaTimeZoneInfo()
    {
        return IsWindows() ? TimeZoneInfo.FindSystemTimeZoneById("E. South America Standard Time") : TimeZoneInfo.FindSystemTimeZoneById("America/Sao_Paulo");
    }

    public static DateTime? ToDateTime(this string dateTimeString)
    {
        if (string.IsNullOrWhiteSpace(dateTimeString)) return null;
        return DateTime.TryParse(dateTimeString, out DateTime data) ? data : null;
    }

    public static DateTime ToSouthAmericaStandardTime(this DateTime source)
    {
        return TimeZoneInfo.ConvertTime(source, source.GetTimeZoneInfo(), AmericaTimeZoneInfo);
    }

    public static DateTimeOffset ToBrasiliaOffset(this DateTime source)
    {
        var dt = source.Kind == DateTimeKind.Utc
            ? TimeZoneInfo.ConvertTimeFromUtc(source, AmericaTimeZoneInfo)
            : TimeZoneInfo.ConvertTime(source, AmericaTimeZoneInfo);

        var offset = TimeZoneInfo.Local.BaseUtcOffset;
        try
        {
            offset = AmericaTimeZoneInfo.GetUtcOffset(dt);
        }
        catch
        {
            // ignored
        }

        return new DateTimeOffset(dt, offset: offset);
    }

    public static string ToFormattedSouthAmericaStandardTime(this DateTime source, string format = "dd/MM/yyyy HH:mm:ss")
    {
        return source.ToSouthAmericaStandardTime().ToString(format);
    }

    public static string ToFormattedSouthAmericaWithOffset(this DateTime source, string format = "dd/MM/yyyy HH:mm:ss zzz")
    {
        return source.ToBrasiliaOffset().ToString(format);
    }

    public static DateTime Tomorrow(this DateTime source) => source.Date.AddDays(1);

    private static TimeZoneInfo GetTimeZoneInfo(this DateTime source)
    {
        return source.Kind == DateTimeKind.Local ? TimeZoneInfo.Local : TimeZoneInfo.Utc;
    }

    public static DateTime GetDateTimeLastHour(this DateTime? dateTime)
    {
        return dateTime?.GetDateTimeLastHour() ?? DateTime.UtcNow.GetDateTimeLastHour();
    }

    public static DateTime GetDateTimeLastHour(this DateTime dateTime)
    {
        if (dateTime == DateTime.MinValue)
        {
            return DateTime.UtcNow.GetDateTimeLastHour();
        }

        var timezone = TimeZoneInfo.FindSystemTimeZoneById("E. South America Standard Time");

        var endDate = DateTime.SpecifyKind(dateTime.Date.AddDays(1), DateTimeKind.Unspecified);

        return TimeZoneInfo.ConvertTimeToUtc(endDate, timezone);
    }
    
    public static DateTime GetFirstHour(this DateTime dateTime)
    {
        var timezone = TimeZoneInfo.FindSystemTimeZoneById("E. South America Standard Time");

        var endDate = DateTime.SpecifyKind(dateTime.Date, DateTimeKind.Unspecified);

        return TimeZoneInfo.ConvertTimeToUtc(endDate, timezone);
    }

    public static bool IsValid(this DateTime? dateTime) => dateTime is not null && dateTime.Value.IsValid();

    public static bool IsValid(this DateTime dateTime)
    {
        if (dateTime == DateTime.MinValue) return false;
        return dateTime != DateTime.MaxValue;
    }
}
