
namespace ONS.SINapse.Shared.Extensions;

public static class DateExtentions
{
    private static readonly TimeZoneInfo AmericaTimeZoneInfo = TimeZoneInfo.FindSystemTimeZoneById("E. South America Standard Time");
    public static DateTime? ToDateTime(this string dateTimeString)
    {
        if (string.IsNullOrWhiteSpace(dateTimeString)) return null;

        return DateTime.TryParse(dateTimeString, out DateTime data) ? data : null;
    }

    public static DateTime ToSouthAmericaStandardTime(this DateTime source)
    {
        return TimeZoneInfo.ConvertTime(source, source.GetTimeZoneInfo(), AmericaTimeZoneInfo);
    }

    public static string ToFormattedSouthAmericaStandardTime(this DateTime source, string format = "dd/MM/yyyy HH:mm:ss")
    {
        return source.ToSouthAmericaStandardTime().ToString(format);
    }

    public static DateTime Tomorrow(this DateTime source) => source.Date.AddDays(1);

    private static TimeZoneInfo GetTimeZoneInfo(this DateTime source)
    {
        return source.Kind.Equals(DateTimeKind.Local) ? TimeZoneInfo.Local : TimeZoneInfo.Utc;
    }

    public static DateTime GetDateTimeLastHour(this DateTime? dateTime)
    {
        if (dateTime is null)
        {
            return DateTime.Now.GetDateTimeLastHour();
        }

        return dateTime.Value.GetDateTimeLastHour();
    }

    public static DateTime GetDateTimeLastHour(this DateTime dateTime)
    {
        if (dateTime == DateTime.MinValue)
        {            
            return DateTime.Now.GetDateTimeLastHour();
        }

        return new DateTime(dateTime.Year, dateTime.Month, dateTime.Day, 23, 59, 59);
    }

    public static bool IsValid(this DateTime? dateTime) => dateTime is not null && dateTime.Value.IsValid();

    public static bool IsValid(this DateTime dateTime)
    {
        if (dateTime == DateTime.MinValue) return false;
        if (dateTime == DateTime.MaxValue) return false;
        
        return true;
    }
}