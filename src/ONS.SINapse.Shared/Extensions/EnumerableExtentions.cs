using System.Linq.Expressions;

namespace ONS.SINapse.Shared.Extensions
{
    public static class EnumerableExtentions
    {
        public static bool Empty<TValue>(this IEnumerable<TValue>? list)
            where TValue : class
        {
            return list is null || !list.Any();
        }
        
        public static bool IsNullOrEmpty<TValue>(this ICollection<TValue>? list)
            where TValue : class
        {
            return list is null || list.Count == 0;
        }
        
        public static IEnumerable<TResult> OrderBy<TResult>(this IEnumerable<TResult> dados, IDictionary<string, bool> sortBy)
        {
            var first = sortBy.First();

            var ordered = first.Value 
                ? dados.OrderBy(CreateExpression<TResult>(first.Key))
                : dados.OrderByDescending(CreateExpression<TResult>(first.Key));
        
            foreach (var sorter in sortBy)
            {
                var sort = sorter.Key;
                var ascending = sorter.Value;

                ordered = ascending ? ordered.ThenBy(CreateExpression<TResult>(sort)) : ordered.ThenByDescending(CreateExpression<TResult>(sort));
            }

            return ordered;
        }

        private static Func<TResult, object> CreateExpression<TResult>(string ordered)
        {
            var parameter = Expression.Parameter(typeof(TResult), "x"); 
            
            return Expression.Lambda<Func<TResult, object>>(
                Expression.Convert(
                    Expression.Property(parameter, ordered),
                    typeof(object)),
                parameter
            ).Compile();
        }
    }
}
