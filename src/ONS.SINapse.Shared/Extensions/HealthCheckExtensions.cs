using System.Net.Mime;
using System.Text.Json;
using HealthChecks.UI.Client;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Diagnostics.HealthChecks;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using ONS.SINapse.Shared.Diagnostics;
using ONS.SINapse.Shared.Diagnostics.Kafka;
using ONS.SINapse.Shared.Settings;

namespace ONS.SINapse.Shared.Extensions
{
    public static class HealthCheckExtensions
    {
        private static IHealthChecksBuilder AddSinapseHealthChecks(this IHealthChecksBuilder healthChecksBuilder, List<HealthcheckSettings> applications)
        {
            healthChecksBuilder.AddMongo();
            healthChecksBuilder.AddFirebase();
            healthChecksBuilder.AddKafka();
            
            healthChecksBuilder.Services.AddHealthChecksUI(setupSettings: settings =>
            {
                applications.ForEach(a => settings.AddHealthCheckEndpoint(a.EndpointName, a.EndpointUrl));
                settings.SetEvaluationTimeInSeconds(60);
                settings.UseApiEndpointHttpMessageHandler(_ => new HttpClientHandler
                {
                    ServerCertificateCustomValidationCallback = (_, cert, _, _) => cert != null,
                    UseDefaultCredentials = false,
                    AllowAutoRedirect = true,
                    PreAuthenticate = true
                });
            } ).AddInMemoryStorage();
            
            return healthChecksBuilder;
        }
        
        public static IHealthChecksBuilder AddSinapseHealthChecks(this IServiceCollection services, IConfiguration configuration)
        {
            var healthChecksBuilder = services.AddHealthChecks();
            
            var healthUrl = configuration
                                .GetSection(nameof(ApplicationSettings))
                                .Get<ApplicationSettings>()?.HealthcheckSettings
                            ?? new List<HealthcheckSettings>();

            return healthChecksBuilder.AddSinapseHealthChecks(healthUrl);
        }
        
        public static IApplicationBuilder ConfigureHealthCheck(this IApplicationBuilder app)
        {
            app.UseEndpoints(endpoints =>
            {
                endpoints.MapControllers();
                endpoints.MapHealthChecks("/health", new HealthCheckOptions()
                {
                    Predicate = _ => true,
                    ResponseWriter = UIResponseWriter.WriteHealthCheckUIResponse
                }).WithMetadata(new AllowAnonymousAttribute());
            });
  
           app.UseHealthChecks("/status",
               new HealthCheckOptions()
               {
                   ResponseWriter = async (context, report) =>
                   {
                       var result = JsonSerializer.Serialize(
                           new
                           {
                               statusApplication = report.Status.ToString(),
                               healthChecks = report.Entries.Select(e => new
                               {
                                   check = e.Key,
                                   ErrorMessage = e.Value.Exception?.Message,
                                   status = Enum.GetName(typeof(HealthStatus), e.Value.Status)
                               })
                           });
                       context.Response.ContentType = MediaTypeNames.Application.Json;
                       await context.Response.WriteAsync(result);
                   }
               });
           
           app.UseHealthChecksUI(opts =>
           {
               opts.UIPath = "/monitor";
               opts.AddCustomStylesheet(Path.Combine(AppContext.BaseDirectory,"Resources","monitor-ui.css"));
           });
            
            return app;
        }
    }
}