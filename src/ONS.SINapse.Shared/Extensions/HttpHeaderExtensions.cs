using Microsoft.AspNetCore.Http;
using System.Globalization;

namespace ONS.SINapse.Shared.Extensions;

public static class HttpHeaderExtensions
{
    public static (double, double)? GetGeolocation(this IHeaderDictionary headers) 
    {
        var latLongHeader = headers["Latlong"].FirstOrDefault();
        
        if (latLongHeader is null)
            return null;
        
        var latLong = latLongHeader.Split(',');

        CultureInfo esUsCulture = CultureInfo.GetCultureInfo("es-us");

        try
        {
            return (
                double.Parse(latLong[0].Trim(), esUsCulture),
                double.Parse(latLong[1].Trim(), esUsCulture)
            );
        }
        catch
        {
            return null;
        }
    } 
}