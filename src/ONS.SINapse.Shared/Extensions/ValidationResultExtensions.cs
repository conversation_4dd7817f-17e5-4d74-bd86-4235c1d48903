using FluentValidation.Results;

namespace ONS.SINapse.Shared.Extensions;

public static class ValidationResultExtensions
{
    /// <summary>
    /// Adiciona uma mensagem com severidade de erro
    /// </summary>
    /// <param name="validationResult">Validation atual</param>
    /// <param name="message">Mensagem de erro</param>
    /// <param name="property">Nome da propriedade</param>
    public static void Adicionar<PERSON>rro(this ValidationResult validationResult, string message, string? property = null)
    {
        var failure = new ValidationFailure(property, message);
        validationResult.Errors.Add(failure);
    }
    
    public static ValidationResult AdicionarErro(this ValidationResult validationResult, ValidationResult errorValidationResul)
    {
        if (errorValidationResul.IsValid) return validationResult;
        validationResult.Errors.AddRange(errorValidationResul.Errors);
        return validationResult;
    }

    public static ValidationResult AdicionarErro(this ValidationResult validationResult, Exception ex, string message)
    {
        validationResult.Errors.Add(new ValidationExptionFailure(ex, string.Empty, message));
        return validationResult;
    }
}

public class ValidationExptionFailure : ValidationFailure
{
    public Exception? Exception { get; set; }
    
    /// <summary>
    /// Creates a new validation failure.
    /// </summary>
    public ValidationExptionFailure() {

    }

    /// <summary>
    /// Creates a new validation failure.
    /// </summary>
    public ValidationExptionFailure(Exception exception, string propertyName, string errorMessage) 
        : this(exception, propertyName, errorMessage, null) 
    {

    }

    /// <summary>
    /// Creates a new ValidationFailure.
    /// </summary>
    public ValidationExptionFailure(Exception exception, string propertyName, string errorMessage, object? attemptedValue)
        : base(propertyName, errorMessage, attemptedValue)
    {
        Exception = exception;
    }
}

