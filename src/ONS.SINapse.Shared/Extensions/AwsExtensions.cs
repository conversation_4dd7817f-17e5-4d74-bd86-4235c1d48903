using Amazon.Extensions.NETCore.Setup;
using Amazon.Runtime;
using Amazon.S3;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using ONS.SINapse.Shared.Settings;

namespace ONS.SINapse.Shared.Extensions
{
    public static class AwsExtensions
    {
        /// <summary>
        /// Load Parameter Store from AWS SystemsManager.
        /// </summary>
        /// <param name="config"></param>
        /// <param name="awsSettings"></param>
        /// <returns></returns>
        private static void LoadAwsSystemsManager(this ConfigurationManager config, AwsSettings awsSettings)
        {
            var env = Environment.GetEnvironmentVariable("ONSEnv") ??
                      config["ONSEnv"] ?? 
                throw new InvalidOperationException("ONSEnv is null");
            
            var awsOptions = new AWSOptions
            {
                Credentials = new BasicAWSCredentials(awsSettings.AccessKey, awsSettings.SecretKey),
                Region = Amazon.RegionEndpoint.GetBySystemName(awsSettings.Region)
            };

            var path = Path.Combine("/", env);

            // DOCS: https://aws.amazon.com/blogs/developer/net-core-configuration-provider-for-aws-systems-manager/
            config.AddSystemsManager(configureSource =>
            {
                // Parameter Store prefix to pull configuration data from.
                configureSource.Path = path;
                Console.WriteLine("Loading configuration from Parameter Store (from AWS SystemManager) path " + configureSource.Path);
                Console.WriteLine($"SSM Parameter Store AwsOptions: {configureSource.AwsOptions}");

                // Reload configuration data every 5 minutes.
                //configureSource.ReloadAfter = TimeSpan.FromMinutes(5); // consumes resources $$$$

                // Use custom logic to set AWS credentials and Region. Otherwise, the AWS SDK for .NET's default logic will be used find credentials.
                configureSource.AwsOptions = awsOptions;

                // Configure if the configuration data is optional.
#if DEBUG
                configureSource.Optional = true;
#else
                configureSource.Optional = false;
#endif

                configureSource.OnLoadException += _ =>
                {
                    // Add custom error handling. For example, look at the exceptionContext.Exception and decide
                    // whether to ignore the error or tell the provider to attempt to reload.
                    Console.WriteLine("Error adding Parameter Store (from AWS SystemManager) for " + env);
                };

                // Implement custom parameter process, which transforms Parameter Store names into names for the .NET Core configuration system.
            });
            Console.WriteLine("Loaded Parameter Store (from AWS SystemManager) from path " + path);
        }

        public static void AddAwsSystemsManager(this IServiceCollection services, ConfigurationManager configuration)
        {
            var awsSettings = configuration.GetSection("AWS").Get<AwsSettings>();
            if (awsSettings == null)
                throw new InvalidOperationException("AWS section is null");

            configuration.LoadAwsSystemsManager(awsSettings);
            
            services.AddAWSService<IAmazonS3>(new AWSOptions
            {
                Credentials = new BasicAWSCredentials(awsSettings.AccessKey, awsSettings.SecretKey),
                Region = Amazon.RegionEndpoint.GetBySystemName(awsSettings.Region)
            });
        }
        
    }
}
