using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;

namespace ONS.SINapse.Shared.Extensions;

public static class WorkerExtensions
{
    public static async Task StartWorkersAsync<TWorker>(
        this IApplicationBuilder app,
        int instances,
        CancellationToken stoppingToken)
        where TWorker : BackgroundService
    {
        var services = app.ApplicationServices;

        for (var i = 0; i < instances; i++)
        {
            var worker = ActivatorUtilities.CreateInstance<TWorker>(services);
            await Task.Factory.StartNew(async () => await worker.StartAsync(stoppingToken),
                stoppingToken,
                TaskCreationOptions.LongRunning,
                TaskScheduler.Default);
        }
    }
}