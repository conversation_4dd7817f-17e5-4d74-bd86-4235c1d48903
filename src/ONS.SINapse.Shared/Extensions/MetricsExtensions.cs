using App.Metrics;
using MassTransit.Configuration;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.DependencyInjection;

namespace ONS.SINapse.Shared.Extensions
{
    public static class MetricsExtensions
    {
        public static IApplicationBuilder ConfigureMetrics(this IApplicationBuilder app)
        {
            app.UseMetricsAllEndpoints();
            app.UseMetricsAllMiddleware();
            return app;
        }
        
        public static IServiceCollection ConfigureMetrics(this IServiceCollection services)
        {
            var metrics = new MetricsBuilder()
                .Configuration.Configure(
                    options =>
                    {
                        options.WithGlobalTags((globalTags, info) =>
                        {
                            globalTags.Add("app", info.EntryAssemblyName);
                            globalTags.Add("env", "local");
                        });
                    }).Build();

            services.AddMetrics(metrics);
            services.AddMetricsTrackingMiddleware();
            services.AddMetricsReportingHostedService();
            services.AddMetricsEndpoints();
            services.AddAppMetricsHealthPublishing();
            return services;
        }
    }
}