namespace ONS.SINapse.Shared.Extensions;

public static class DictionaryExtensions
{
    public static TValue? GetOrDefault<TKey, TValue>(this IDictionary<TKey, TValue> dictionary, TKey key, TValue? defaultValue = default)
    {
        dictionary.TryGetValue(key, out var value);
        return value ?? defaultValue;
    }
    
    /// <summary>
    /// Transforma uma lista de dicionários em um único dicionário,
    /// assumindo que todas as chaves em todos os dicionários da lista são globalmente únicas.
    /// </summary>
    /// <param name="values">A lista de dicionários a serem combinados.</param>
    /// <returns>Um único dicionário contendo todos os pares chave-valor dos dicionários da lista.</returns>
    public static Dictionary<string, object?> AggregateUniqueDictionaries(this List<Dictionary<string, object?>> values)
    {
        var aggregatedDictionary = new Dictionary<string, object?>();

        foreach (var kvp in values.SelectMany(dict => dict))
        {
            aggregatedDictionary[kvp.Key] = kvp.Value;
        }

        return aggregatedDictionary;
    }
}