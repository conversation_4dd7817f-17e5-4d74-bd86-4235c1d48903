using System.Text;
using System.Text.RegularExpressions;

namespace ONS.SINapse.Shared.Extensions;

public static partial class StringExtensions
{
    /// <summary>
    ///  Returns true if the string is a Guid, or false if it's null or not a string representing a Guid. 
    /// </summary>
    /// <param name="value">string | null</param>
    /// <returns>bool</returns>
    public static bool IsGuid(this string? value)
        => Guid.TryParse(value, out _);

    public static string RemoverCaracteresEspeciais(this string? value)
    {
        var normalizedString = !string.IsNullOrEmpty(value) ? value.Normalize(NormalizationForm.FormD) : string.Empty;

        return NonSpacingMarkRegex().Replace(normalizedString, string.Empty).Normalize(NormalizationForm.FormC);
    }
    
    /// <summary>
    ///  Returns string with only letters.
    /// </summary>
    /// <param name="value"></param>
    /// <returns> string </returns>
    public static string RemoveNonLetter(this string value)
    {
        if (string.IsNullOrEmpty(value))
        {
            return value;
        }

        return NonAlphanumericStart().Replace(value, "");
    }

    [GeneratedRegex(@"^[^a-zA-Z]+")]
    private static partial Regex NonAlphanumericStart();

    [GeneratedRegex(@"\p{M}", RegexOptions.Compiled)]
    private static partial Regex NonSpacingMarkRegex();
}