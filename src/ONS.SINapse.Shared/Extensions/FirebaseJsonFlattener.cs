using System.Globalization;
using System.Text.Json;
using System.Text.Json.Nodes;
using System.Text.Json.Serialization;

namespace ONS.SINapse.Shared.Extensions;

public static class FirebaseJsonFlattener
{
    private static readonly JsonSerializerOptions JsonSerializerOptions = new()
    {
        PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
        ReferenceHandler = ReferenceHandler.IgnoreCycles,
        DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull
    };

    /// <summary>
    /// Flattens a given object into a dictionary using default options.
    /// Null and empty string values are included, and the root prefix is "/".
    /// </summary>
    /// <param name="data">The object to flatten.</param>
    /// <returns>A dictionary representing the flattened object.</returns>
    public static Dictionary<string, object?> Flatten(this object data)
        => Flatten(data, true, "/");

    /// <summary>
    /// Flattens a given object into a dictionary, specifying a custom root prefix.
    /// Null and empty string values are included by default.
    /// </summary>
    /// <param name="data">The object to flatten.</param>
    /// <param name="root">The root prefix to prepend to all keys (e.g., "/my_collection").</param>
    /// <returns>A dictionary representing the flattened object.</returns>
    public static Dictionary<string, object?> Flatten(this object data, string root)
        => Flatten(data, true, root);

    /// <summary>
    /// Flattens a given object into a dictionary, controlling whether null and empty values are included.
    /// The root prefix defaults to "/".
    /// </summary>
    /// <param name="data">The object to flatten.</param>
    /// <param name="includeNullAndEmptyValues">
    /// If set to <c>true</c>, null values and empty/whitespace strings will be included in the flattened dictionary.
    /// Otherwise, they will be omitted.
    /// </param>
    /// <returns>A dictionary representing the flattened object.</returns>
    public static Dictionary<string, object?> Flatten(this object data, bool includeNullAndEmptyValues)
        => Flatten(data, includeNullAndEmptyValues, "/");

    /// <summary>
    /// Flattens a given object into a dictionary, with full control over the inclusion of null/empty values and the root prefix.
    /// </summary>
    /// <param name="data">The object to flatten. This object will be serialized to JSON and then flattened.</param>
    /// <param name="includeNullAndEmptyValues">
    /// If set to <c>true</c>, null values and empty/whitespace strings will be included in the flattened dictionary.
    /// Otherwise, they will be omitted.
    /// </param>
    /// <param name="root">The root prefix to prepend to all keys (e.g., "/my_collection"). If the data itself is null
    /// and <paramref name="includeNullAndEmptyValues"/> is <c>true</c>, this root will be added as a key with a null value.
    /// </param>
    /// <returns>A dictionary where keys are Firebase-like paths (e.g., "/field/subfield") and values are the corresponding data.</returns>
    /// <remarks>
    /// This method is particularly useful for preparing data for Firebase Realtime Database's
    /// multi-path update operations, which expect a dictionary of paths to values.
    /// </remarks>
    public static Dictionary<string, object?> Flatten(this object data, bool includeNullAndEmptyValues, string root)
    {
        var result = new Dictionary<string, object?>();

        // Use as opções de serialização que incluem PropertyNamingPolicy.CamelCase
        JsonNode? node = JsonSerializer.SerializeToNode(data, JsonSerializerOptions);

        // ... (Restante do método Flatten e FlattenNode permanecem os mesmos) ...

        // Se o restante do seu código em FlattenNode depende de incluir/excluir nulos
        // através do JsonPropertyNamingPolicy.CamelCase, você pode ajustar o DefaultIgnoreCondition
        // nas _jsonSerializerOptions, ou manter a lógica atual de includeNullAndEmptyValues.
        // A lógica atual é boa pois dá controle granular via parâmetro.

        // Se o entire input 'data' is null, SerializeToNode will return null.
        if (node is null)
        {
            if (includeNullAndEmptyValues)
            {
                result[root.TrimEnd('/')] = null;
            }
            return result;
        }

        root = root.TrimEnd('/');
        FlattenNode(node, root, result, includeNullAndEmptyValues);
        return result;
    }

    /// <summary>
    /// Recursively flattens a JsonNode into the result dictionary.
    /// </summary>
    /// <param name="node">The current JsonNode being processed (can be an object, array, or value).</param>
    /// <param name="prefix">The current path prefix for the node (e.g., "/users/123").</param>
    /// <param name="result">The dictionary to accumulate the flattened key-value pairs.</param>
    /// <param name="includeNullAndEmptyValues">Determines if null values and empty/whitespace strings should be included.</param>
    private static void FlattenNode(JsonNode node, string prefix, Dictionary<string, object?> result, bool includeNullAndEmptyValues)
    {
        switch (node)
        {
            case JsonObject obj:
                FlattenObject(obj, prefix, result, includeNullAndEmptyValues);
                break;
            case JsonArray array:
                FlattenArray(array, prefix, result, includeNullAndEmptyValues);
                break;
            case JsonValue valueNode:
                FlattenValue(valueNode, prefix, result, includeNullAndEmptyValues);
                break;
        }
    }

    private static void FlattenObject(JsonObject obj, string prefix, Dictionary<string, object?> result, bool includeNullAndEmptyValues)
    {
        foreach (KeyValuePair<string, JsonNode?> kvp in obj)
        {
            string newPrefix = $"{prefix}/{kvp.Key}";

            if (kvp.Value == null)
            {
                if (includeNullAndEmptyValues)
                    result[newPrefix] = null;
            }
            else
            {
                FlattenNode(kvp.Value, newPrefix, result, includeNullAndEmptyValues);
            }
        }
    }

    private static void FlattenArray(JsonArray array, string prefix, Dictionary<string, object?> result, bool includeNullAndEmptyValues)
    {
        for (int i = 0; i < array.Count; i++)
        {
            string newPrefix = $"{prefix}/{i}";

            if (array[i] == null)
            {
                if (includeNullAndEmptyValues)
                    result[newPrefix] = null;
            }
            else
            {
                FlattenNode(array[i]!, newPrefix, result, includeNullAndEmptyValues);
            }
        }
    }

    private static void FlattenValue(JsonValue valueNode, string prefix, Dictionary<string, object?> result, bool includeNullAndEmptyValues)
    {
        object? value = ExtractPrimitiveValue(valueNode);

        if (!includeNullAndEmptyValues && (value == null || (value is string s && string.IsNullOrWhiteSpace(s))))
            return;

        result[prefix] = value;
    }

    private static object? ExtractPrimitiveValue(JsonValue valueNode)
    {
        if (valueNode.TryGetValue(out string? stringValue))
        {
            if (DateTime.TryParse(stringValue, CultureInfo.InvariantCulture, DateTimeStyles.AdjustToUniversal, out DateTime dt))
                return dt;
            if (Guid.TryParse(stringValue, out Guid guid))
                return guid;
            return stringValue;
        }

        if (valueNode.TryGetValue(out int intValue)) return intValue;
        if (valueNode.TryGetValue(out long longValue)) return longValue;
        if (valueNode.TryGetValue(out double doubleValue)) return doubleValue;
        if (valueNode.TryGetValue(out bool boolValue)) return boolValue;
        if (valueNode.TryGetValue(out JsonElement jsonElement))
        {
            return ExtractFromJsonElement(jsonElement);
        }

        return null;
    }

    private static object? ExtractFromJsonElement(JsonElement element)
    {
        if (element.ValueKind == JsonValueKind.Null)
            return null;

        try
        {
            return JsonSerializer.Deserialize<object?>(element.GetRawText(), JsonSerializerOptions);
        }
        catch (JsonException)
        {
            return element;
        }
    }
}