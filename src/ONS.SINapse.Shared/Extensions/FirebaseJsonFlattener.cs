using System.Globalization;
using System.Text.Json;
using System.Text.Json.Nodes;

namespace ONS.SINapse.Shared.Extensions;

/// <summary>
/// Provides utility methods to flatten JSON objects into a dictionary with a key structure
/// similar to Firebase Realtime Database paths. This is particularly useful for performing
/// atomic multi-path updates in Firebase.
/// </summary>
public static class FirebaseJsonFlattener
{
    /// <summary>
    /// Flattens a given object into a dictionary using default options.
    /// Null and empty string values are included, and the root prefix is "/".
    /// </summary>
    /// <param name="data">The object to flatten.</param>
    /// <returns>A dictionary representing the flattened object.</returns>
    public static Dictionary<string, object?> Flatten(this object data)
        => Flatten(data, true, "/");

    /// <summary>
    /// Flattens a given object into a dictionary, specifying a custom root prefix.
    /// Null and empty string values are included by default.
    /// </summary>
    /// <param name="data">The object to flatten.</param>
    /// <param name="root">The root prefix to prepend to all keys (e.g., "/my_collection").</param>
    /// <returns>A dictionary representing the flattened object.</returns>
    public static Dictionary<string, object?> Flatten(this object data, string root)
        => Flatten(data, true, root);

    /// <summary>
    /// Flattens a given object into a dictionary, controlling whether null and empty values are included.
    /// The root prefix defaults to "/".
    /// </summary>
    /// <param name="data">The object to flatten.</param>
    /// <param name="includeNullAndEmptyValues">
    /// If set to <c>true</c>, null values and empty/whitespace strings will be included in the flattened dictionary.
    /// Otherwise, they will be omitted.
    /// </param>
    /// <returns>A dictionary representing the flattened object.</returns>
    public static Dictionary<string, object?> Flatten(this object data, bool includeNullAndEmptyValues)
        => Flatten(data, includeNullAndEmptyValues, "/");

    /// <summary>
    /// Flattens a given object into a dictionary, with full control over the inclusion of null/empty values and the root prefix.
    /// </summary>
    /// <param name="data">The object to flatten. This object will be serialized to JSON and then flattened.</param>
    /// <param name="includeNullAndEmptyValues">
    /// If set to <c>true</c>, null values and empty/whitespace strings will be included in the flattened dictionary.
    /// Otherwise, they will be omitted.
    /// </param>
    /// <param name="root">The root prefix to prepend to all keys (e.g., "/my_collection"). If the data itself is null
    /// and <paramref name="includeNullAndEmptyValues"/> is <c>true</c>, this root will be added as a key with a null value.
    /// </param>
    /// <returns>A dictionary where keys are Firebase-like paths (e.g., "/field/subfield") and values are the corresponding data.</returns>
    /// <remarks>
    /// This method is particularly useful for preparing data for Firebase Realtime Database's
    /// multi-path update operations, which expect a dictionary of paths to values.
    /// </remarks>
    public static Dictionary<string, object?> Flatten(this object data, bool includeNullAndEmptyValues, string root)
    {
        var result = new Dictionary<string, object?>();
        JsonNode? node = JsonSerializer.SerializeToNode(data);

        // If the entire input 'data' is null, SerializeToNode will return null.
        // In this case, if we're configured to include null, we add the root itself as null.
        if (node is null)
        {
            if (includeNullAndEmptyValues)
            {
                result[root.TrimEnd('/')] = null;
            }
            return result;
        }

        // Ensure the root prefix does not end with a slash for consistent path generation.
        root = root.TrimEnd('/');

        FlattenNode(node, root, result, includeNullAndEmptyValues);
        return result;
    }

    /// <summary>
    /// Recursively flattens a JsonNode into the result dictionary.
    /// </summary>
    /// <param name="node">The current JsonNode being processed (can be an object, array, or value).</param>
    /// <param name="prefix">The current path prefix for the node (e.g., "/users/123").</param>
    /// <param name="result">The dictionary to accumulate the flattened key-value pairs.</param>
    /// <param name="includeNullAndEmptyValues">Determines if null values and empty/whitespace strings should be included.</param>
    private static void FlattenNode(JsonNode node, string prefix, Dictionary<string, object?> result, bool includeNullAndEmptyValues)
    {
        switch (node)
        {
            case JsonObject obj:
                foreach (KeyValuePair<string, JsonNode?> kvp in obj)
                {
                    string newPrefix = $"{prefix}/{kvp.Key}";
                    if (kvp.Value == null)
                    {
                        if (includeNullAndEmptyValues)
                        {
                            result[newPrefix] = null;
                        }
                    }
                    else
                    {
                        FlattenNode(kvp.Value, newPrefix, result, includeNullAndEmptyValues);
                    }
                }
                break;

            case JsonArray array:
                for (int i = 0; i < array.Count; i++)
                {
                    string newPrefix = $"{prefix}/{i}";
                    if (array[i] == null)
                    {
                        if (includeNullAndEmptyValues)
                        {
                            result[newPrefix] = null;
                        }
                    }
                    else
                    {
                        FlattenNode(array[i]!, newPrefix, result, includeNullAndEmptyValues);
                    }
                }
                break;

            case JsonValue valueNode:
                object? value;

                // --- MODIFICAÇÃO CHAVE AQUI ---
                // Tentar converter o JsonValue para o tipo CLR apropriado
                if (valueNode.TryGetValue(out string? stringValue))
                {
                    // Tentar parsing de DateTime ou Guid para evitar string pura se for o caso
                    if (DateTime.TryParse(stringValue, CultureInfo.InvariantCulture, DateTimeStyles.AdjustToUniversal, out DateTime dtValue))
                    {
                        value = dtValue;
                    }
                    else if (Guid.TryParse(stringValue, out Guid guidValue))
                    {
                        value = guidValue;
                    }
                    else
                    {
                        value = stringValue;
                    }
                }
                else if (valueNode.TryGetValue(out int intValue))
                {
                    value = intValue;
                }
                else if (valueNode.TryGetValue(out long longValue))
                {
                    value = longValue;
                }
                else if (valueNode.TryGetValue(out double doubleValue))
                {
                    value = doubleValue;
                }
                else if (valueNode.TryGetValue(out bool boolValue))
                {
                    value = boolValue;
                }
                else if (valueNode.TryGetValue(out JsonElement jsonElementValue)) // Catchall para outros tipos ou se os TryGetValue falharem
                {
                    // Isso é o que provavelmente estava causando o problema.
                    // Para garantir que JsonElement seja serializado corretamente,
                    // podemos tentar extrair o valor novamente ou deixá-lo como null se for JTokenType.Null
                    if (jsonElementValue.ValueKind == JsonValueKind.Null)
                    {
                        value = null;
                    }
                    else
                    {
                        // Para outros tipos não explicitamente tratados, podemos tentar obter um objeto "genérico"
                        // O ToObject() pode ser a chave aqui para forçar o System.Text.Json a converter
                        // para um tipo primitivo CLR, ou um dicionário/lista C# se for um objeto/array interno.
                        // Usar um JsonConverter para o Dictionary<string,object?> no FirebaseClient
                        // ainda será necessário para lidar com essas estruturas.
                        try
                        {
                            // Tente desserializar para object para pegar o tipo "real"
                            value = JsonSerializer.Deserialize<object?>(jsonElementValue.GetRawText());
                        }
                        catch (JsonException)
                        {
                            // Se falhar a desserialização para object, mantenha o JsonElement
                            // (que o Newtonsoft.Json ainda pode tratar mal, mas é um fallback)
                            value = jsonElementValue;
                        }
                    }
                }
                else
                {
                    // Fallback se TryGetValue não conseguir inferir o tipo (ex: JsonNull)
                    value = null;
                }
                // --- FIM DA MODIFICAÇÃO CHAVE ---

                if (!includeNullAndEmptyValues)
                {
                    if (value == null || value is string s && string.IsNullOrWhiteSpace(s))
                    {
                        return;
                    }
                }
                
                // Se o valor for um objeto complexo (que não foi achado para um primitivo),
                // mas é parte de um array ou objeto aninhado que foi flatten, ele pode ser um Dictionary<string, object?>
                // ou List<object?>. Neste caso, o Newtonsoft.Json terá seu próprio converter.
                result[prefix] = value;
                break;
        }
    }
}
