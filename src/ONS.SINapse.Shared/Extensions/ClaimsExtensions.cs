using System.Security.Claims;
using ONS.SINapse.Shared.Identity;

namespace ONS.SINapse.Shared.Extensions
{
    public static class ClaimsExtensions
    {
        public static string ObterSidDeUsuario(this ClaimsPrincipal userClaims)
            => userClaims.FindFirst(PopClaimTypes.Sid)?.Value ?? string.Empty;
        
        public static string ObterNomeDeUsuario(this ClaimsPrincipal userClaims)
            => userClaims.FindFirst(PopClaimTypes.GivenName)?.Value ?? string.Empty;
        
        public static string ObterLoginDeUsuario(this ClaimsPrincipal userClaims)
            => userClaims.FindFirst(PopClaimTypes.NameIdentifier)?.Value ?? string.Empty;
        
        public static IDictionary<string, List<string>> ObterScopeOperationFormatado(this ClaimsPrincipal userClaims)
        {
            var scopeOperations = userClaims.ObterScopeOperations();
            return FormatarScopeOperations(scopeOperations);
        }

        public static IDictionary<(string Codigo, string Nome), List<string>> ObterScopeRolesFormatado(this ClaimsPrincipal userClaims)
        {
            var scopeRoles = userClaims.ObterScopeRoles();
            return FormatarScopeRole(scopeRoles);
        }

        public static string[] ObterScopeOperations(this ClaimsPrincipal userClaims) 
            => userClaims.Claims.GetValuesOfType(PopClaimTypes.ScopeOperation).ToArray();

        public static string[] ObterScopeRoles(this ClaimsPrincipal userClaims) 
            => userClaims.Claims.GetValuesOfType(PopClaimTypes.ScopeRole).ToArray();

        public static string[] ObterScopes(this ClaimsPrincipal userClaims) 
            => userClaims.Claims.GetValuesOfType(PopClaimTypes.Scope).ToArray();

        public static string[] ObterOperations(this ClaimsPrincipal userClaims)
            => userClaims.Claims.GetValuesOfType(PopClaimTypes.Operation).ToArray();

        public static string[] ObterRoles(this ClaimsPrincipal userClaims)
            => userClaims.Claims.GetValuesOfType(PopClaimTypes.Role).ToArray();
        
        public static ClaimsPrincipal RemoverRole(this ClaimsPrincipal userClaims, string role)
        {
            var claims = userClaims.Claims.Where(x => !x.Value.Contains(role));
            userClaims = new ClaimsPrincipal(new ClaimsIdentity(claims));
            return userClaims;
        }
        
        private static List<string> GetValuesOfType(this IEnumerable<Claim> claims, string type)
        {
            return claims.Where(x => x.Type == type).Select(x => x.Value).Distinct().ToList();
        }
        
        private static Dictionary<string, List<string>> FormatarScopeOperations(string[] scopeOperations)
        {
            var limpeza = scopeOperations
                .Select(x =>
                {
                    var sp = x.Split("|");
                    var pair = new KeyValuePair<string, string>(sp[0], sp[1].Remove(0, 3));
                    return pair;
                })
                .GroupBy(group => group.Key,
                    by => by.Value,
                    (group, by) => new KeyValuePair<string,List<string>>(group, by.ToList()));
        
            return new Dictionary<string, List<string>>(limpeza);
        }
    
        private static Dictionary<(string Codigo, string Nome), List<string>> FormatarScopeRole(string[] scopeRoles)
        {
            var limpeza = scopeRoles
                .Select(x =>
                {
                    var sp = x.Split("|");
                    
                    var perfil = sp[1].Remove(0, 3);

                    var codigoPerfil = perfil.RemoverCaracteresEspeciais();
                    
                    var pair = new KeyValuePair<(string Codigo, string Nome), string>(
                        (codigoPerfil, perfil), sp[0]);
                    return pair;
                })
                .GroupBy(group => group.Key,
                    by => by.Value,
                    (group, by) => new KeyValuePair<(string Codigo, string Nome),List<string>>(group, by.ToList()));
        
            return new Dictionary<(string Codigo, string Nome), List<string>>(limpeza);
        }
    }
}