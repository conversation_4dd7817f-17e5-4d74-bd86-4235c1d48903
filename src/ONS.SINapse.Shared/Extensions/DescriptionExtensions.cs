
using System.ComponentModel;
using System.Reflection;
using System.Text;

namespace ONS.SINapse.Shared.Extensions;

public static class DescriptionExtensions
{
    public static string GetDescription<T>(this T source)
    {
        if(source == null)
            return string.Empty;

        FieldInfo fi = source.GetType()!.GetField(source.ToString()!)!;

        DescriptionAttribute[] attributes = (DescriptionAttribute[])fi.GetCustomAttributes(
            typeof(DescriptionAttribute), false);

        if (attributes.Length > 0)
        {
            var utf8 = Encoding.UTF8;
            var utfBytes = utf8.GetBytes(attributes[0].Description);
            return utf8.GetString(utfBytes, 0, utfBytes.Length);  
        }
        else return source?.ToString() ?? string.Empty;
    }
}
