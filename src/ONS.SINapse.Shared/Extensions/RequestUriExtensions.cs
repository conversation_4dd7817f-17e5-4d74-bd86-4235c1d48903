using System.Web;

namespace ONS.SINapse.Shared.Extensions;

public static class RequestUriExtensions
{
    /// <summary>
    /// Remove o token de autenticação (parâmetro "auth") de uma URL.
    /// </summary>
    /// <param name="url">A URL que contém o parâmetro de autenticação.</param>
    /// <returns>A URL sem o parâmetro "auth".</returns>
    public static Uri RemoveAuthToken(this Uri url)
    {
        var query = HttpUtility.ParseQueryString(url.Query);
        
        if(query.Count == 0 || query["auth"] == null)
        {
            return url;
        }
        
        query.Remove("auth");
        
        var uriBuilder = new UriBuilder(url)
        {
            Query = query.ToString()
        };

        return uriBuilder.Uri;
    }
}