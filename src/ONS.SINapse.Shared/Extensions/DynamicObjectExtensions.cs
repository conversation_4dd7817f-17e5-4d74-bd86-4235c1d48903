using System.Dynamic;
using System.Text.Json;

namespace ONS.SINapse.Shared.Extensions;

public static class DynamicObjectExtensions
{
    public static dynamic? ConvertJsonElementToDynamic(this JsonElement element)
    {
        switch (element.ValueKind)
        {
            case JsonValueKind.Object:
                var expandoObject = new ExpandoObject() as IDictionary<string, object>;
                foreach (var property in element.EnumerateObject())
                    expandoObject[property.Name] = ConvertJsonElementToDynamic(property.Value) ?? string.Empty;
                return expandoObject;

            case JsonValueKind.Array:
                var list = element.EnumerateArray().Select(item => ConvertJsonElementToDynamic(item)).Cast<object>().ToList();
                return list;

            case JsonValueKind.String:
                return element.GetString()!;

            case JsonValueKind.Number:
                if (element.TryGetInt32(out int intValue))
                    return intValue;
                if (element.TryGetInt64(out long longValue))
                    return longValue;
                if (element.TryGetDouble(out double doubleValue))
                    return doubleValue;
                break;

            case JsonValueKind.True:
                return true;

            case JsonValueKind.False:
                return false;

            case JsonValueKind.Null:
                return default;
            
            case JsonValueKind.Undefined:
                break;
            default:
                throw new InvalidOperationException($"Unsupported JsonValueKind: {element.ValueKind}");
        }

        throw new InvalidOperationException($"Unsupported JsonValueKind: {element.ValueKind}");
    }
}