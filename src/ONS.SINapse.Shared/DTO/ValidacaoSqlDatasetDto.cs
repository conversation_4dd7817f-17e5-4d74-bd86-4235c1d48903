using Microsoft.AspNetCore.Mvc;

namespace ONS.SINapse.Shared.DTO;

/// <summary>
/// Objeto de validação de query SQL.
/// </summary>
public sealed record ValidacaoSqlDatasetDto
{
    /// <summary>
    /// Nome da view de dataset a ser validada. Sem prefixo "vw_", sem sufixo "_dataset".
    /// <example>
    /// conjunto_usina
    /// </example>
    /// </summary>
    [FromRoute(Name = "view")]
    public required string ViewName { get; init; }
    
    /// <summary>
    /// Query SQL a ser validada.
    /// <type>SqlQueryDto</type>
    /// </summary>
    [FromBody]
    public required SqlQueryDto SqlQuery { get; init; }

    /// <summary>
    /// Query RSQL para filtrar os dados.
    /// <example>
    /// ?query=origem_codigo==N;tipo==usina;tipofonte==UTE
    /// </example>
    /// </summary>
    [FromQuery(Name = "query")]
    public string? RsqlQuery { get; init; }
}

/// <summary>
/// Objeto que representa uma query SQL.
/// </summary>
/// <notice>
///     A query SQL deve ser uma query de seleção (SELECT) que retorne os mesmos campos da view de dataset.
///     A query não deve conter cláusula ORDER BY.
/// </notice>
/// <param name="Sql"></param>
public sealed record SqlQueryDto(string Sql);