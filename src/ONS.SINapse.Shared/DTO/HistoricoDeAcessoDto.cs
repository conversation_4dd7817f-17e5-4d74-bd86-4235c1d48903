namespace ONS.SINapse.Shared.DTO;


public record FiltroHistoricoDeAcesso
{
    private const int QuantidadeDeMesesPadrao = 1; 
    
    public FiltroHistoricoDeAcesso()
    {
        Fim = DateTime.Today;
        Inicio = Fim?.AddMonths(QuantidadeDeMesesPadrao * -1);
    }
    
    public FiltroHistoricoDeAcesso(string centroAgente)
        : this()
    {
        CentroAgente = centroAgente;
    }
    
    public DateTime? Inicio { get; set; }
    public DateTime? Fim { get; set; }
    public string? CentroAgente { get; set; }
}

public record HistoricosDeAcesso(ObjetoDeManobraDto CentroAgente, DateTime Inicio, DateTime? Fim)
{
    public TimeSpan? Duracao => Fim.HasValue ? Fim - Inicio : DateTime.UtcNow - Inicio;
}
