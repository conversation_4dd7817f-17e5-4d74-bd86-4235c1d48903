using System.Collections;
using System.Diagnostics.CodeAnalysis;
using ONS.SINapse.Shared.Extensions;

namespace ONS.SINapse.Shared.DTO.PerfilDoUsuario;

public interface IRulesOperations : IReadOnlyDictionary<string, RuleOperations>
{
    public IReadOnlyCollection<RuleOperations> RuleOperations { get; }
}


public class RulesOperations : IRulesOperations
{
    public RulesOperations(List<RuleOperations> ruleOperations)
    {
        _ruleOperationDictionary = ruleOperations
            .ToDictionary(k => k.Codigo, v => v);

        Count = ruleOperations.Count;
        
        RuleOperations = ruleOperations;
    }
    
    private readonly IReadOnlyDictionary<string, RuleOperations> _ruleOperationDictionary;
    
    public IReadOnlyCollection<RuleOperations> RuleOperations { get; }
    public IEnumerator<KeyValuePair<string, RuleOperations>> GetEnumerator()
    {
        return _ruleOperationDictionary.GetEnumerator();
    }

    IEnumerator IEnumerable.GetEnumerator()
    {
        return GetEnumerator();
    }

    public int Count { get; }
    
    public bool ContainsKey(string key) => _ruleOperationDictionary.ContainsKey(key);

    public bool TryGetValue(string key, [MaybeNullWhen(false)] out RuleOperations value) 
        => _ruleOperationDictionary.TryGetValue(key, out value);

    public RuleOperations this[string key] => _ruleOperationDictionary[key];

    public IEnumerable<string> Keys => _ruleOperationDictionary.Keys;
    
    public IEnumerable<RuleOperations> Values => _ruleOperationDictionary.Values;
}


public class RuleOperations
{
    public RuleOperations()
    {
        Nome = string.Empty;
        Operations = [];
    }

    public string Codigo => Nome.RemoverCaracteresEspeciais().Trim();
    public string Nome { get; set; }
    public string[] Operations { get; set; }
}