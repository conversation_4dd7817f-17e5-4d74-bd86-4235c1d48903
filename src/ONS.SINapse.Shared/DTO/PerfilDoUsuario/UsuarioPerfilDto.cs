using Newtonsoft.Json;
using ONS.SINapse.Shared.Constants;

namespace ONS.SINapse.Shared.DTO.PerfilDoUsuario;

public class UsuarioPerfilDto
{
    public UsuarioPerfilDto(List<PerfilDto> perfis, string sid, string nome, string login)
    {
        _perfis = perfis;
        Sid = sid;
        Nome = nome;
        Login = login;
    }

    public string Sid { get; private set; }
    public string Nome { get; private set; }
    public string Login { get; private set; }

    [JsonIgnore]
    private readonly List<PerfilDto> _perfis;
    public IReadOnlyCollection<PerfilDto> Perfis => _perfis.AsReadOnly();
    
}

public class PerfilDto
{
    public PerfilDto(List<ScopeDto> scopes, List<string> operations, string nome, string codigo)
    {
        _scopes = scopes;
        Nome = nome;
        Codigo = codigo;
        
        _operations = operations;
        PermiteLogar = operations.Contains(Operacoes.LogarNoSinapse);
        MultiplosScopes = operations.Contains(Operacoes.SelecionarMultiplosScopes);
    }

    public string Nome { get; private set; }
    public string Codigo { get; private set; }
    public bool PermiteLogar { get; private set; }
    public bool MultiplosScopes { get; private set; }
    
    [JsonIgnore]
    private readonly List<string> _operations;
    public IReadOnlyCollection<string> Operations => _operations.AsReadOnly();
    
    
    [JsonIgnore]
    private readonly List<ScopeDto> _scopes;
    public IReadOnlyCollection<ScopeDto> Scopes => _scopes.AsReadOnly();
}

public class ScopeDto
{
    public ScopeDto(string nome, string codigo, string tipo)
    {
        Nome = nome;
        Codigo = codigo;
        Tipo = tipo;
    }

    public string Nome { get; private set; }
    public string Codigo { get; private set; }
    public string Tipo { get; private set; }
}