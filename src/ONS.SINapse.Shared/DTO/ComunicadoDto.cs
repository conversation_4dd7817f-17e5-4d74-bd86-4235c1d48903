using ONS.SINapse.Shared.Enums;

namespace ONS.SINapse.Shared.DTO;

public class ComunicadoDto
{
    public ComunicadoDto(
        ObjetoDeManobraDto origem, string titulo, string mensagem, TipoDeComunicado tipoDeComunicado, ICollection<string> destinatarios)
    {
        Origem = origem;
        Titulo = titulo;
        Mensagem = mensagem;
        TipoDeComunicado = tipoDeComunicado;
        Destinatarios = destinatarios;
    }

    public ObjetoDeManobraDto Origem { get; set; }
    public string Titulo { get; set; }
    public string Mensagem { get; set; }
    public TipoDeComunicado TipoDeComunicado { get; set; }

    public ICollection<string> Destinatarios { get; set; }
}