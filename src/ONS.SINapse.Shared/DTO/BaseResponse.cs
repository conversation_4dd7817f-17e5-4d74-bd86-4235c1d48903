using Newtonsoft.Json;

namespace ONS.SINapse.Shared.DTO
{
    public class BaseResponse<TData> where TData : class
    {       
        public BaseResponse(TData data, bool success, int statusCode, IEnumerable<string>? messages = null)
        {
            Success = success;
            Messages = messages ?? new List<string>();
            Data = data;
            StatusCode = statusCode;
        }

        [JsonProperty("success")]
        public bool Success { get; set; }
        [JsonProperty("statusCode")]
        public int StatusCode { get; set; }
        [JsonProperty("data")]
        public TData Data { get; set; }
        
        [JsonProperty("messages")]
        public IEnumerable<string> Messages { get; set; }
    }
}