using ONS.SINapse.Shared.Enums;

namespace ONS.SINapse.Shared.DTO;

public abstract class ComunicadoDetalheBaseDto
{
    protected ComunicadoDetalheBaseDto()
    {
        Id = string.Empty;
        Titulo = string.Empty;
        Mensagem = string.Empty;
        EnviadoPor = string.Empty;
        LoginUsuario = string.Empty;
        Sid = string.Empty;
        EnviadoPara = string.Empty;
        Origem = new ObjetoDeManobraDto(string.Empty, string.Empty);
        Complementos = new List<ComplementoComunicadoDto>();
        Leitores = new List<LeitorComunicadoDto>();
        Destinatarios = [];
    }
    
    public string Id { get; set; }
    public string Titulo { get; set; }
    public string Mensagem { get; set; }
    public string EnviadoPor { get; set; } 
    public string LoginUsuario { get; set; }
    public string Sid { get; set; }
    public TipoDeComunicado TipoDeComunicado { get; set; }
    public ObjetoDeManobraDto Origem { get; set; }
    public string EnviadoPara { get; set; }
    public DateTime DataDeEnvio { get; set; }
    public DateTime DataDeAtualizacao { get; set; }
    public bool PossuiComplemento { get; set; }
    public ICollection<ComplementoComunicadoDto> Complementos { get; set; }
    public ICollection<LeitorComunicadoDto> Leitores { get; set; }
    public long QuantidadeDeLeitores { get; set; }
    public ICollection<DestinatarioDto> Destinatarios { get; set; }

}

public class DestinatarioDto
{
    public DestinatarioDto(
        string codigo,
        string nomeCurto,
        string centro,
        string? nomeAreaEletrica) 
    {
        Codigo = codigo;
        NomeCurto = nomeCurto;
        Centro = centro;
        NomeAreaEletrica = nomeAreaEletrica;
    }

    public string Codigo { get; set; }
    public string NomeCurto { get; set; }
    public string Centro { get; set; }
    public string? NomeAreaEletrica { get; set; }
}