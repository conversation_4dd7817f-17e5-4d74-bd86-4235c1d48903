using MongoDB.Bson.Serialization.Attributes;
using ONS.SINapse.Shared.CustomAttributes;

namespace ONS.SINapse.Shared.DTO;

[BsonCollection("c_solicitacao")]
public class DestinatarioDeSolicitacaoDto : IBsonCollection
{
    [BsonElement("codigo")]
    public string Codigo { get; set; }
    
    [BsonElement("descricao")]
    public string Descricao { get; set; }

    public DestinatarioDeSolicitacaoDto(string codigo, string descricao)
    {
        Codigo = codigo;
        Descricao = descricao;
    }
}