
using Newtonsoft.Json;

namespace ONS.SINapse.Shared.DTO
{
    public class PopAutorizacaoTokenDto
    {
        public PopAutorizacaoTokenDto(string accessToken, string tokenType, double expiresIn, string refreshToken)
        {
            AccessToken = accessToken;
            TokenType = tokenType;
            ExpiresIn = expiresIn;
            RefreshToken = refreshToken;
        }

        [JsonProperty("access_token")]
        public string AccessToken { get; init; }
        
        [JsonProperty("token_type")]
        public string TokenType { get; init; }
        
        [JsonProperty("expires_in")]
        public double ExpiresIn { get; init; }
        
        [JsonProperty("refresh_token")]
        public string RefreshToken { get; init; }
    }
}