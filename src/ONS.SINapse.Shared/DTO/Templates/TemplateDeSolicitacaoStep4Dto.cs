namespace ONS.SINapse.Shared.DTO.Templates;
using System.Collections.Generic;

public class TemplateDeSolicitacaoStep4Dto
{
    public TemplateDeSolicitacaoStep4Dto(List<Step4FieldDto> fields)
    {
        Fields = fields;
    }

    public List<Step4FieldDto> Fields { get; set; }
}

public class Step4FieldDto : StepFieldDto
{
    public Step4FieldDto(
        string label, 
        string description, 
        string fieldName, 
        string component, 
        string type,
        ICollection<Step4ColumnDto> columns
    ) : base(label, description, fieldName, component, type)
    {
        Columns = columns;
    }

    public string? MessageOutput { get; set; }
    public ICollection<Step4ColumnDto> Columns { get; set; }
}

public class Step4ColumnDto : Step4FieldDto
{
    public Step4ColumnDto(
        string label, 
        string description, 
        string fieldName, 
        string component, 
        string type, 
        ICollection<Step4ColumnDto> columns
    ) : base(label, description, fieldName, component, type, columns)
    {
    }

    public IncrementStep4Dto? Increment { get; set; }
}

public class IncrementStep4Dto
{
    public IncrementStep4Dto(int value, string type)
    {
        Value = value;
        Type = type;
    }

    public int Value { get; set; }
    public string Type { get; set; }
}