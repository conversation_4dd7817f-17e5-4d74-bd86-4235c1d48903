namespace ONS.SINapse.Shared.DTO.Templates;

public class TemplateDeSolicitacaoStep1Dto
{
    public TemplateDeSolicitacaoStep1Dto(string origin, string description)
    {
        Origin = origin;
        Description = description;
    }

    public string Origin { get; set; }
    public string Description { get; set; }
    public ICollection<TemplateDeSolicitacaoItemDto> Items { get; set; } = new List<TemplateDeSolicitacaoItemDto>();
}

public record TemplateDeSolicitacaoItemDto
{
    public TemplateDeSolicitacaoItemDto(string description, VariableGroup variables)
    { 
        Description = description;
        Variables = variables;
    }

    public string Description { get; set; }
    public VariableGroup Variables { get; set; }
}

public record VariableGroup
{
    public VariableGroup(string title)
    {
        Title = title;
    }

    public string Title { get; set; }
    public ICollection<VariableItem> Items { get; set; } = new List<VariableItem>();
}

public record VariableItem
{
    public VariableItem(string description, ActionGroup actions) 
    {
        Description = description;
        Actions = actions;
    }

    public string Description { get; set; }
    public ActionGroup Actions { get; set; }
}

public record ActionGroup
{
    public ActionGroup(string title, ICollection<ActionItem> items)
    { 
        Title = title;
        Items = items;
    }

    public string Title { get; set; }
    public ICollection<ActionItem> Items { get; set; } = new List<ActionItem>();
}

public record ActionItem
{
    public ActionItem(
        string description,
        string step3,
        string step4,
        string step5,
        string[] tags)
    {
        Description = description;
        Step3 = step3;
        Step4 = step4;
        Step5 = step5;
        Tags = tags;
    }

    public string Description { get; set; }
    public string Step3 { get; set; }
    public string Step4 { get; set; }
    public string Step5 { get; set; }
    public string[] Tags { get; set; }
}