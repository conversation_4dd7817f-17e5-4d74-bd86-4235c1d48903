namespace ONS.SINapse.Shared.DTO.Templates;

public class TemplateDeSolicitacaoStep1ArquivoDto : TemplateArquivoDto 
{
    public TemplateDeSolicitacaoStep1ArquivoDto(string nomeDaPasta, Step1DoArquivoDto step1, Step2DoArquivoDto step2) : base(nomeDaPasta)
    {
        Step1 = step1;
        Step2 = step2;
    }

    public Step1DoArquivoDto Step1 { get; init; }
    public Step2DoArquivoDto Step2 { get; init; }
}

public record Step1DoArquivoDto
{
    public Step1DoArquivoDto(string origin, string description, string help)
    { 
        Origin = origin;
        Description = description;
        Help = help;
    }

    public string Origin { get; set; }
    public string Description { get; init; }
    public int Order { get; init; }
    public string Help { get; init; }
}

public record Step2DoArquivoDto
{
    public Step2DoArquivoDto(Step2VariablesDoArquivoDto variables)
    { 
        Variables = variables;
    }

    public Step2VariablesDoArquivoDto Variables { get; init; }
}

public record Step2VariablesDoArquivoDto
{
    public Step2VariablesDoArquivoDto(string title, List<Step2VariableItemArquivoDto> items)
    { 
        Title = title;
        Items = items;
    }

    public string Title { get; init; }
    public List<Step2VariableItemArquivoDto> Items { get; init; }
}

public record Step2VariableItemArquivoDto
{
    public Step2VariableItemArquivoDto(string description, Step2ActionsArquivoDto actions)
    { 
        Description = description;
        Actions = actions;
    }

    public string Description { get; init; }
    public Step2ActionsArquivoDto Actions { get; init; }
}

public record Step2ActionsArquivoDto
{
    public Step2ActionsArquivoDto(string title, List<Step2ActionItemArquivoDto> items)
    { 
        Title = title;
        Items = items;
    }

    public string Title { get; init; }
    public List<Step2ActionItemArquivoDto> Items { get; init; }
}

public record Step2ActionItemArquivoDto
{
    public Step2ActionItemArquivoDto(
        string description,
        string step3,
        string step4,
        string step5,
        string[] tags)
    {
        Description = description;
        Step3 = step3;
        Step4 = step4;
        Step5 = step5;
        Tags = tags;
    }

    public string Description { get; init; }
    public string Step3 { get; init; }
    public string Step4 { get; init; }
    public string Step5 { get; init; }
    public string[] Tags { get; init; }
}