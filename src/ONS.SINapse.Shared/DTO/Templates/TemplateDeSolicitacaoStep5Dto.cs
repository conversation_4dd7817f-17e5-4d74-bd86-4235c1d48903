namespace ONS.SINapse.Shared.DTO.Templates;
using System.Collections.Generic;

public class TemplateDeSolicitacaoStep5Dto
{
    public TemplateDeSolicitacaoStep5Dto(List<Step5FieldDto> fields)
    {
        Fields = fields;
    }

    public List<Step5FieldDto> Fields { get; set; }
}

public class Step5FieldDto : StepFieldDto
{
    public Step5FieldDto(
        string label, 
        string description, 
        string fieldName, 
        string component, 
        string type,
        string datasetUrl,
        ICollection<DatasetItemDto> dataset
    ) : base(label, description, fieldName, component, type)
    {
        DatasetUrl = datasetUrl;
        Dataset = dataset;
    }

    public string DatasetUrl { get; set; }
    public ICollection<DatasetItemDto> Dataset { get; set; }
}