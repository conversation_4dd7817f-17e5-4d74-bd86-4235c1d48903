namespace ONS.SINapse.Shared.DTO.Templates;

public class TemplateDeSolicitacaoStep3Dto
{
    public TemplateDeSolicitacaoStep3Dto(List<Step3FieldDto> fields)
    {
        Fields = fields;
    }

    public List<Step3FieldDto> Fields { get; set; }
}

public class Step3FieldDto : StepFieldDto
{
    public Step3FieldDto(
        string label, 
        string description, 
        string fieldName, 
        string component, 
        string type, 
        string datasetUrl,
        ICollection<DatasetItemDto> dataset
    ) : base(label, description, fieldName, component, type)
    {
        DatasetUrl = datasetUrl;
        Dataset = dataset;
    }

    public string DatasetUrl { get; set; }
    public ICollection<DatasetItemDto> Dataset { get; set; }
}