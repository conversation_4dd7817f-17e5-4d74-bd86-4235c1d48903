namespace ONS.SINapse.Shared.DTO.Templates;

public class StepFieldDto
{
    public StepFieldDto(string label, string description, string fieldName, string component, string type)
    {
        Label = label;
        Description = description;
        FieldName = fieldName;
        Component = component;
        Type = type;
    }

    public string Label { get; set; }
    public string Description { get; set; }
    public string FieldName { get; set; }
    public string Component { get; set; }
    public string Type { get; set; }
    public bool Required { get; set; }
    public bool Multiple { get; set; }
    public bool Searchable { get; set; }
    public bool Readonly { get; set; }
    public string? DefaultValue { get; set; }
    public string? Min { get; set; }
    public string? Max { get; set; }
    public int? Step { get; set; }
    public bool HalfTime { get; set; }
    public int? MinLength { get; set; }
    public int? MaxLength { get; set; }
    public Dictionary<string, object> Style { get; set; } = new();
}