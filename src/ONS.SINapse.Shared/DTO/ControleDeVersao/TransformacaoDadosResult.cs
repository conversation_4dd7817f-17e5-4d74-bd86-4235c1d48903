using Newtonsoft.Json.Linq;

namespace ONS.SINapse.Shared.DTO.ControleDeVersao
{
    public record TransformacaoDadosResult
    {
        public TransformacaoDadosResult(Exception exception, IDictionary<string, JToken?> rascunhos)
        {
            Mensagem = exception.ToString();
            Sucesso = false;
            <PERSON><PERSON><PERSON><PERSON><PERSON> = rascunhos;
        }

        public TransformacaoDadosResult(IDictionary<string, JToken?> rascunhos)
        {
            Mensagem = string.Empty;
            Sucesso = true;
            <PERSON><PERSON><PERSON><PERSON><PERSON> = rascunhos;
        }

        public string Mensagem { get; set; }
        public bool Sucesso { get; set; }
        public IDictionary<string, JToken?> <PERSON><PERSON><PERSON><PERSON><PERSON> { get; set; }
    }
}
