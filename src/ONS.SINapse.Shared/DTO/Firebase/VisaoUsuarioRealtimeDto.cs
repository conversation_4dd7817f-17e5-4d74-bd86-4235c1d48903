namespace ONS.SINapse.Shared.DTO.Firebase;

public class VisaoUsuarioRealtimeDto
{
    public VisaoUsuarioRealtimeDto(
        string sid,
        string id,
        string nome,
        string codigoCentroAgente,
        string nomeCentroAgente,
        IReadOnlyCollection<EquipamentoVisaoUsuarioRealtimeDto> equipamentosDeManobra,
        IReadOnlyCollection<EquipamentoVisaoUsuarioRealtimeDto> tags
    )
    {
        Sid = sid;
        Id = id;
        Nome = nome;
        CentroAgente = new CentroAgenteVisaoUsuarioRealtimeDto(codigoCentroAgente, nomeCentroAgente);
        EquipamentosDeManobra = equipamentosDeManobra;
        Tags = tags;
    }

    public string Id { get; }
    public string Nome { get; }
    public CentroAgenteVisaoUsuarioRealtimeDto CentroAgente { get; set; }
    public string Sid { get; }
    
    public IReadOnlyCollection<EquipamentoVisaoUsuarioRealtimeDto> EquipamentosDeManobra { get; }
    public IReadOnlyCollection<EquipamentoVisaoUsuarioRealtimeDto> Tags { get; }
}

public record EquipamentoVisaoUsuarioRealtimeDto(string Valor, string Descricao, TipoDeEquipamentoVisaoUsuarioRealtimeDto Tipo);

public record TipoDeEquipamentoVisaoUsuarioRealtimeDto(string Codigo, string Descricao);

public record CentroAgenteVisaoUsuarioRealtimeDto(string Codigo, string Descricao);
