namespace ONS.SINapse.Shared.DTO.Firebase;

public class RascunhoSolicitacaoDto
{
    public Guid? Id { get; set; }
    public string? Nome { get; set; }
    public string? Proprietario { get; set; }
    public bool? Valido { get; set; }
    public string? Centro { get; set; }
    public List<DadosDoRascunhoSolicitacaoDto>? DadosDoRascunho { get; set; }
}

public class DadosDoRascunhoSolicitacaoDto
{
    public int? Posicao { get;  set; }
    public List<dynamic>? Dados { get; set; }
}