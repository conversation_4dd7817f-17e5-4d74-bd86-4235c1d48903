using Newtonsoft.Json;
using ONS.SINapse.Shared.Extensions;

namespace ONS.SINapse.Shared.DTO.Firebase;

public class TagRealtimeDto
{
    public string Id { get; init; }
    
    public string Sid { get; init; }
    
    [JsonProperty("tag")]
    public string Tag { get; private set; }
    
    [JsonProperty("nome")]
    public string Nome { get; private set; }
    
    public TagRealtimeDto()
    {
        Id = Guid.NewGuid().ToString();
        Sid = string.Empty;
        Tag = string.Empty;
        Nome = string.Empty;
    }

    public TagRealtimeDto(string sid, string nome)
    {
        Id = Guid.NewGuid().ToString();
        Sid = sid;
        Nome = nome;
        Tag = nome;
        AlterarNome(nome);
    }

    public void AlterarNome(string nome)
    {
        Tag = nome.Trim().RemoverCaracteresEspeciais().ToLower();
        Nome = nome.Trim();
    }
}