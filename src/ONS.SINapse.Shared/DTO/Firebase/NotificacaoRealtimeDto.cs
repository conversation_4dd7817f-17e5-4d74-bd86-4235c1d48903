
namespace ONS.SINapse.Shared.DTO.Firebase;


public class NotificacaoRealtimeDto
{
    public const string TodosDevemSerNotificados = "Todos";
    public string Destination { get; set; }
    public string Tag { get;  init; }
    public string Title { get;  set; }
    public string Body { get;  init; }
    public string? Image { get; set; }
    public string? Icon { get; set; }
    public bool Renotify { get; set; }
    public bool Silent { get; set; }
    public bool RequireInteraction { get; set; }
    public long Timestamp { get; set; } = DateTimeOffset.Now.ToUnixTimeSeconds();
    public long ExpirationDate { get; set; }
    public NotificationFirebaseDataDto? Data { get; set; }
    
    public NotificacaoRealtimeDto(string destination, string title, string body, NotificationFirebaseDataDto? data = null, bool silent = false)
    {
        Tag = Guid.NewGuid().ToString();
        Destination = destination;
        Title = title;
        Body = body;
        Data = data;
        Silent = silent;
    }

    public NotificacaoRealtimeDto(string title, string body, bool silent, bool requireInteraction, int expirationInSeconds)
    {
        Tag = Guid.NewGuid().ToString();
        Title = title;
        Body = body;
        Silent = silent;
        RequireInteraction = requireInteraction;
        ExpirationDate = DateTimeOffset.Now.AddSeconds(expirationInSeconds).ToUnixTimeSeconds();
        Destination = string.Empty;
    }
}