
namespace ONS.SINapse.Shared.DTO.Firebase;

public class Notification
{
    
    public required string Tag { get; set; }
    public required string Title { get; set; }
    public required string Body { get; set; }
    public long ExpirationDate { get; set; }
    public long Timestamp { get; set; }
    public required string Tipo { get; set; }
    public string? Local { get; set; }
    
}

public class NotificacaoRealtimeDto
{
    public const string TodosDevemSerNotificados = "Todos";
    public string Destination { get; set; }
    public string Tag { get;  init; }
    public string Title { get;  set; }
    public string Body { get;  init; }
    public long Timestamp { get; set; } = DateTimeOffset.Now.ToUnixTimeSeconds();
    public long ExpirationDate { get; set; }
    public string Tipo { get; set; }
    public string? Local { get; set; }

    public NotificacaoRealtimeDto(string destination, string title, string body, string tipo, string? local)
    {
        Tag = Guid.NewGuid().ToString();
        Destination = destination;
        Title = title;
        Body = body;
        Tipo = tipo;
        Local = local;
    }

    public NotificacaoRealtimeDto(string destination, string titulo, string comunicadoTitulo, string comunicadoName): 
        this(destination, titulo, comunicadoTitulo, comunicadoName, string.Empty)
    {
       
    }
    
    public Notification ToNotification()
    {
        return new Notification
        {
            Tag = Tag,
            Title = Title,
            Body = Body,
            ExpirationDate = ExpirationDate,
            Timestamp = Timestamp,
            Tipo = Tipo,
            Local = Local
        };
    }
}