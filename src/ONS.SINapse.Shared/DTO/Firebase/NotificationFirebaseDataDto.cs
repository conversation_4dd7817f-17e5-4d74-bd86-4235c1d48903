namespace ONS.SINapse.Shared.DTO.Firebase;

public class NotificationFirebaseDataDto
{
    public NotificationFirebaseDataDto()
    {
        Tipo = string.Empty;
        Id = string.Empty;
        Mensagem = string.Empty;
        Sid = string.Empty;
        Emitente = string.Empty;
        DataCriacao = string.Empty;
        DataAtualizacao = string.Empty;
        SomNotificacao = string.Empty;
        Status = string.Empty;
    }

    public string Tipo { get; set; }
    public string Id { get; set; }
    public string Mensagem { get; set; }
    public string Sid { get; set; }
    public string Emitente { get; set; }
    public string DataCriacao { get; set; }
    public string DataAtualizacao { get; set; }
    public string SomNotificacao { get; set; }
    public string Status { get; set; }
    public short StatusId { get; set; }
    public string? Local { get; set; }
}
