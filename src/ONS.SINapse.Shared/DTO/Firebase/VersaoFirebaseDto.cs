namespace ONS.SINapse.Shared.DTO.Firebase
{
    public class VersaoFirebaseDto
    {
        public DateTime DataCadastro { get; private set; }
        public string Descricao { get; private set; }
        public string Sid { get; private set; }
        public string Usuario { get; private set; }
        public string Vers<PERSON> { get; private set; }

        public VersaoFirebaseDto(DateTime dataCadastro, string descricao, string sid, string usuario, string versao)
        {
            DataCadastro = dataCadastro;
            Descricao = descricao;
            Sid = sid;
            Usuario = usuario;
            Versao = versao;
        }
    }
}
