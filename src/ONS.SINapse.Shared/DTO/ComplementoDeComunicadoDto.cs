using System.ComponentModel.DataAnnotations;

namespace ONS.SINapse.Shared.DTO;

public class ComplementoDeComunicadoDto
{
    public ComplementoDeComunicadoDto(string mensagem, ObjetoDeManobraDto origem) 
    {
        Mensagem = mensagem;
        Origem = origem;
    }

    [Required(ErrorMessage = "Campo Mensagem é obrigatório.")]
    public string Mensagem { get; set; }

    public ObjetoDeManobraDto Origem { get; set; }
    
}