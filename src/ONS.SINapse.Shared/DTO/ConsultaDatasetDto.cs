using Microsoft.AspNetCore.Mvc;

namespace ONS.SINapse.Shared.DTO;

public sealed record ConsultaDatasetDto
{
    /// <summary>
    /// Nome da view de dataset a ser consultada. Sem prefixo "vw_", sem sufixo "_dataset".
    /// <example>
    /// conjunto_usina
    /// </example>
    /// </summary>
    [FromRoute(Name = "view")]
    public required string ViewName { get; init; }
    /// <summary>
    /// Query RSQL para filtrar os dados.
    /// <example>
    /// ?query=origem_codigo==N;tipo==usina;tipofonte==UTE
    /// </example>
    /// </summary>
    [FromQuery(Name = "query")]
    public string? RsqlQuery { get; init; }
}