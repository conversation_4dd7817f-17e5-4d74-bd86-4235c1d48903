namespace ONS.SINapse.Shared.DTO;

public record LocalDeOperacaoDataset(string Id, string Label, IEnumerable<LocalDeOperacaoDatasetItem> Locais);

public record LocalDeOperacaoDatasetItem
{
    
    public string Codigo { get; set; }
    public string Nome { get; set; }

    // Json Serialization
    protected LocalDeOperacaoDatasetItem()
    {
        Codigo = string.Empty;
        Nome = string.Empty;
    }
    
    public LocalDeOperacaoDatasetItem(string codigo, string prefixoNome, string nome)
    {
        Codigo = codigo;
        Nome = $"[{prefixoNome}] - {nome}";
    }
    
    public LocalDeOperacaoDatasetItem(string codigo, string nome)
    {
        Codigo = codigo;
        Nome = nome;
    }
}