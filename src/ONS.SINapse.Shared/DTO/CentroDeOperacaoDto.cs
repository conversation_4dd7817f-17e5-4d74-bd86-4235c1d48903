using System.Collections.Concurrent;

namespace ONS.SINapse.Shared.DTO;

public class CentroDeOperacaoDto
{
    public CentroDeOperacaoDto()
    {
        Codigo = string.Empty;
        Nome = string.Empty;
    }

    public const string CodigoCNOS = "CN";

    public string Codigo { get; set; }

    public string Nome { get; set; }

    public bool IsCnos => Codigo.Equals(CodigoCNOS, StringComparison.OrdinalIgnoreCase);
}

public static class CentrosDeOperacoes
{
    private static readonly ConcurrentDictionary<string, CentroDeOperacaoDto> _centros =
        new ConcurrentDictionary<string, CentroDeOperacaoDto>(new Dictionary<string, CentroDeOperacaoDto>
        {
            {
                "SE",
                new()
                {
                    Codigo = "SE",
                    Nome = "COSR-SE"
                }
            },
            {
                "NE",
                new()
                {
                    Codigo = "NE",
                    Nome = "COSR-NE"
                }
            },
            {
                "N",
                new()
                {
                    Codigo = "N",
                    Nome = "COSR-NCO"
                }
            },
            {
                "CN",
                new()
                {
                    Codigo = "CN",
                    Nome = "CNOS"
                }
            },
            {
                "S",
                new()
                {
                    Codigo = "S",
                    Nome = "COSR-S"
                }
            },
        });

    public static IReadOnlyDictionary<string, CentroDeOperacaoDto> Centros => _centros.AsReadOnly();

    public static IEnumerable<CentroDeOperacaoDto> GetCentrosDeOperacao() => Centros.Select(x => x.Value);

    public static CentroDeOperacaoDto? GetCentroDeOperacaoByCodigo(string? codigo)
    {
        if(string.IsNullOrEmpty(codigo)) return null;
        
        _ = Centros.TryGetValue(codigo, out var result);
        return result;
    }
}
