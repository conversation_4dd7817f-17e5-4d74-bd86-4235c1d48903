using Newtonsoft.Json;

namespace ONS.SINapse.Shared.DTO;

public record DatasetItemDto
{
    public string Id { get; set; }
    public ManeuverObjectDto Destination { get; set; }
    public string Description { get; set; }
    public string Label { get; set; }
    public string Optional { get; set; }
    public bool DefaultValue { get; set; }
    public ManeuverObjectDto? Local { get; set; }
    public ManeuverObjectDto? EncaminharPara { get; set; }
    [JsonProperty("definirStatus")]
    private readonly string? DefinirStatus;

    // Constructor for Json Deserialization
    [JsonConstructor]
    public DatasetItemDto(string id, ManeuverObjectDto destination, string description, string label)
    {
        Id = id;
        Destination = destination;
        Description = description;
        Label = label;
        Optional = string.Empty;
    }
    
    public DatasetItemDto(string id, ManeuverObjectDto destination, string description, string label, ManeuverObjectDto local, ManeuverObjectDto? encaminharPara, string? definirStatus)
        : this(id, destination, description, label)
    {
        Local = local;
        EncaminharPara = encaminharPara;
        DefinirStatus = definirStatus;
    }
    
    public DatasetItemDto(string id, ManeuverObjectDto destination, string description, string label, bool defaultValue)
        : this(id, destination, description, label)
    {
        DefaultValue = defaultValue;
    }

    public string? ObterDefinirStatus() => DefinirStatus;
}

public record ManeuverObjectDto(string Code, string Name)
{
    public string Code { get; private set; } = Code;
    public string Name { get; private set; } = Name;
}