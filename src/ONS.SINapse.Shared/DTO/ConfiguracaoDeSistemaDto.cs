namespace ONS.SINapse.Shared.DTO;

public class ConfiguracaoDeSistemaDto
{
    public ConfiguracaoDeSistemaDto(
        string origin,
        string clientId,
        string grantTypeAccessToken,
        string grantTypeRefreshToken,
        string tokenUrl,
        string urlLogout)
    {
        Origin = origin;
        ClientId = clientId;
        GrantTypeAccessToken = grantTypeAccessToken;
        GrantTypeRefreshToken = grantTypeRefreshToken;
        TokenUrl = tokenUrl;
        UrlLogout = urlLogout;
    }

    public string Origin { get; set; }
    public string ClientId { get; set; }
    public string GrantTypeAccessToken { get; set; }
    public string GrantTypeRefreshToken { get; set; }
    public string TokenUrl { get; set; }
    public string UrlLogout { get; set; }
}