
namespace ONS.SINapse.Shared.DTO.ExtracaoDeDados;


// Será serializado usando snack case
public record SolicitacaoExtracaoDadoDto(
	string IdSolicitacao,
	string? CodExterno,
	string CodSistemaorigem,
	short CodStatus,
	string DscStatus,
	DateTime DataAtualizacao,
	DateTime DinCriacao,
	string? DscDetalheimpedimento,
	string? DscInformacaoadicional,
	string DscMensagem,
	string DscMensagemnormalizada,
	string? DscMotivo,
	bool FlgEncaminhada,
	bool FlgExterna,
	bool FlgFinalizadaautomaticamente,
	string? IdLote,
	string? IdSolicitacaoorigem,
	string CodOrigem,
	string NomOrigem,
	string CodDestino,
	string NomDestino,
	string? CodDestinoencaminhamento,
	string? NomDestinoencaminhamento,
	string? CodLocal,
	string? NomLocal,
	string CodSid,
	string LgnUsuario,
	string NomUsuario,
	string? Tag,
    DateTime? DataInicioCadastro
)
{
	public string CodSolicitacao => IdSolicitacao.Split('-')[2];
}



// Será serializado usando snack case
public record ChatExtracaoDadoDto(
	string IdSolicitacao,
	string DscMensagem,
	short? CodStatus,
	string? DscStatus,
	string CodSid,
	string LgnUsuario,
	string NomUsuario,
	string? CodSidleitura,
	string? LgnUsuarioleitura,
	string? NomUsuarioleitura,
	DateTime? DinRegistroleitura,
	string? CodSidentrega,
	string? LgnUsuarioentrega,
	string? NomUsuarioentrega,
	DateTime? DinRegistroentrega,
	DateTime DinEnvio,
	DateTime? DinEntregadestino,
	DateTime? DinLeituradestino
);



// Será serializado usando snack case
public record HistoricoExtracaoDadoDto(
	string IdSolicitacao,
	short CodStatus,
	string DscStatus,
	short? CodStatusanterior,
	string? DscStatusanterior,
	string CodSid,
	string LgnUsuario,
	string NomUsuario,
	DateTime DinAlteracao
);

public record DadosExtracaoDadoDto(
	List<SolicitacaoExtracaoDadoDto> Solicitacoes, 
	List<ChatExtracaoDadoDto> Chats, 
	List<HistoricoExtracaoDadoDto> Historicos
);
