
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Shared.Converters;
using ONS.SINapse.Shared.DTO.Solicitacao;

namespace ONS.SINapse.Shared.DTO;

public sealed class ChatDeSolicitacaoFirebaseDto : FlattenableJson
{
    public ChatDeSolicitacaoFirebaseDto(string idSolicitacao, List<ChatDeSolicitacaoDto>? chat)
        : base(idSolicitacao)
    {
        Chat = chat ?? [];
    }
    public List<ChatDeSolicitacaoDto> Chat { get; private set; }
}

public class StatusDeSolicitacaoFirebaseDto : FlattenableJson
{
    public StatusDeSolicitacaoFirebaseDto(
        string id,
        StatusDeSolicitacao status,
        DateTime updatedAt,
        List<ChatDeSolicitacaoDto>? chat,
        List<HistoricoDeStatusDeSolicitacaoDto> historicosDeStatus)
        : base(id)
    {
        Status = status;
        UpdatedAt = updatedAt;
        Chat = chat ?? [];
        HistoricosDeStatus = historicosDeStatus;
    }

    public StatusDeSolicitacao Status { get; private set; }
    public DateTime UpdatedAt { get; private set; }
    public List<ChatDeSolicitacaoDto> Chat { get; private set; }
    public List<HistoricoDeStatusDeSolicitacaoDto> HistoricosDeStatus { get; private set; }
    public string? Impedimento { get; private set; }
    
    public void AdicionarImpedimento(string impedimento)
    {
        Impedimento = impedimento;
    }
}

public sealed class EncaminharSolicitacaoFirebaseDto : StatusDeSolicitacaoFirebaseDto
{
    public EncaminharSolicitacaoFirebaseDto(
        string id,
        StatusDeSolicitacao status,
        DateTime updatedAt,
        List<ChatDeSolicitacaoDto>? chat,
        List<HistoricoDeStatusDeSolicitacaoDto> historicosDeStatus)
        : base(id, status, updatedAt, chat, historicosDeStatus) => Encaminhada = true;

    public bool Encaminhada { get; private set; }
}