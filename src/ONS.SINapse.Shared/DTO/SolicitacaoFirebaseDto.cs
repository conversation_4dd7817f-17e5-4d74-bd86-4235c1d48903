using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Shared.DTO.Solicitacao;

namespace ONS.SINapse.Shared.DTO;

public record EntregaChatDto(List<EntregaChatItemDto> Chat);
public record EntregaChatItemDto(DateTime DataEHoraDeEntregaAoDestinatario, RegistroUsuarioHoraDto? PrimeiraEntrega);

public record LeituraChatDto(List<LeituraChatItemDto> Chat);
public record LeituraChatItemDto(DateTime DataEHoraDeLeituraDoDestinatario, RegistroUsuarioHoraDto? PrimeiraLeitura);

public class StatusDeSolicitacaoFirebaseDto
{
    public StatusDeSolicitacaoFirebaseDto(
        StatusDeSolicitacao status,
        DateTime updatedAt,
        Dictionary<int, ChatDeSolicitacaoItemDto> chat,
        List<HistoricoDeStatusDeSolicitacaoDto> historicosDeStatus,
        string? detalheDoImpedimento = null)
    {
        Status = status;
        UpdatedAt = updatedAt;
        Chat = chat;
        HistoricosDeStatus = historicosDeStatus;
        DetalheDoImpedimento = detalheDoImpedimento;
    }

    public StatusDeSolicitacao Status { get; private set; }
    public DateTime UpdatedAt { get; private set; }
    public Dictionary<int, ChatDeSolicitacaoItemDto> Chat { get; private set; }
    public List<HistoricoDeStatusDeSolicitacaoDto> HistoricosDeStatus { get; private set; }
    public string? DetalheDoImpedimento { get; private set; }
}