
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Shared.Converters;
using ONS.SINapse.Shared.DTO.Solicitacao;

namespace ONS.SINapse.Shared.DTO;

public sealed class ChatDeSolicitacaoFirebaseDto : FlattenableJson
{
    public ChatDeSolicitacaoFirebaseDto(string idSolicitacao, List<ChatDeSolicitacaoDto>? chat)
        : base(idSolicitacao)
    {
        Chat = chat ?? [];
    }
    public List<ChatDeSolicitacaoDto> Chat { get; private set; }
}

public class StatusDeSolicitacaoFirebaseDto : FlattenableJson
{
    public StatusDeSolicitacaoFirebaseDto(
        string id,
        StatusDeSolicitacao status,
        DateTime updatedAt,
        List<ChatDeSolicitacaoDto>? chat,
        List<HistoricoDeStatusDeSolicitacaoDto> historicosDeStatus,
        string? detalheDoImpedimento = null)
        : base(id)
    {
        Status = status;
        UpdatedAt = updatedAt;
        Chat = chat ?? [];
        HistoricosDeStatus = historicosDeStatus;
        DetalheDoImpedimento = detalheDoImpedimento;
    }

    public StatusDeSolicitacao Status { get; private set; }
    public DateTime UpdatedAt { get; private set; }
    public List<ChatDeSolicitacaoDto> Chat { get; private set; }
    public List<HistoricoDeStatusDeSolicitacaoDto> HistoricosDeStatus { get; private set; }
    public string? DetalheDoImpedimento { get; private set; }
    
    public void AdicionarImpedimento(string impedimento)
    {
        DetalheDoImpedimento = impedimento;
    }
}

public sealed class EncaminharSolicitacaoFirebaseDto : StatusDeSolicitacaoFirebaseDto
{
    public EncaminharSolicitacaoFirebaseDto(
        string id,
        StatusDeSolicitacao status,
        DateTime updatedAt,
        List<ChatDeSolicitacaoDto>? chat,
        List<HistoricoDeStatusDeSolicitacaoDto> historicosDeStatus)
        : base(id, status, updatedAt, chat, historicosDeStatus) => Encaminhada = true;

    public bool Encaminhada { get; private set; }
}

public class StatusDeSolicitacaoSimplificadoDto : FlattenableJson
{
    public StatusDeSolicitacaoSimplificadoDto(
        string id,
        StatusDeSolicitacao status,
        DateTime updatedAt,
        List<ChatDeSolicitacaoPainelDto>? chat,
        List<StatusPainelDto> historicosDeStatus,
        string? detalheDoImpedimento = null)
        : base(id)
    {
        Status = status;
        UpdatedAt = updatedAt;
        Chat = chat ?? [];
        HistoricosDeStatus = historicosDeStatus;
        DetalheDoImpedimento = detalheDoImpedimento;
    }

    public StatusDeSolicitacao Status { get; private set; }
    public DateTime UpdatedAt { get; private set; }
    public List<ChatDeSolicitacaoPainelDto> Chat { get; private set; }
    public List<StatusPainelDto> HistoricosDeStatus { get; private set; }
    public string? DetalheDoImpedimento { get; private set; }
    
    public void AdicionarImpedimento(string impedimento)
    {
        DetalheDoImpedimento = impedimento;
    }
}

public sealed class ChatDeSolicitacaoSimplificadoFirebaseDto : FlattenableJson
{
    public ChatDeSolicitacaoSimplificadoFirebaseDto(string idSolicitacao, List<ChatDeSolicitacaoPainelDto>? chat)
        : base(idSolicitacao)
    {
        Chat = chat ?? [];
    }
    public List<ChatDeSolicitacaoPainelDto> Chat { get; private set; }
}