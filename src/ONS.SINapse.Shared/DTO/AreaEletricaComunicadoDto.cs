namespace ONS.SINapse.Shared.DTO;

public class AreaEletricaComunicadoDto
{
    public AreaEletricaComunicadoDto(
        string codigo, string nome, string codigoDoCentroDeOperacao, IEnumerable<AgenteDto> agentes)
    {
        Codigo = codigo;
        Nome = nome;
        CodigoDoCentroDeOperacao = codigoDoCentroDeOperacao;
        Agentes = agentes;
    }

    public string Codigo { get; set; }
    public string Nome { get; set; }
    public string CodigoDoCentroDeOperacao { get; set; }
    public IEnumerable<AgenteDto> Agentes { get; set; }
}

public class AgenteDto 
{
    public AgenteDto(string codigo, string nome)
    {
        Codigo = codigo;
        Nome = nome;
    }

    public string Codigo { get; set; }
    public string Nome { get; set; }
}