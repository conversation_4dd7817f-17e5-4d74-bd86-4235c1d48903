
namespace ONS.SINapse.Shared.DTO;

public record VisaoDeUsuarioDto
{
    public string CodigoCentroAgente { get; set; } = string.Empty;
    public string Nome { get; set; } = string.Empty;
    public List<EquipamentoVisaoDeUsuarioDto> EquipamentosDeManobra {get; set;} = new();
    public List<EquipamentoVisaoDeUsuarioDto> Tags { get; set; } = new();
}

public record EquipamentoVisaoDeUsuarioDto(string Valor, string Descricao, TipoDeEquipamentoVisaoDeUsuarioDto Tipo);

public record TipoDeEquipamentoVisaoDeUsuarioDto(string Codigo, string Descricao);

public record SelecaoDeVisaoDeUsuarioDto(string CodigoCentroAgente, string Id, string[] EquipamentosDeManobra);
public record VisaoDeUsuarioSelecionadoDto(string CodigoCentroAgente, string Id, string[] EquipamentosDeManobra, ErroDeVisaoDeUsuarioDto[] Erros);

public record ErroDeVisaoDeUsuarioDto(string Codigo, string Mensagem);