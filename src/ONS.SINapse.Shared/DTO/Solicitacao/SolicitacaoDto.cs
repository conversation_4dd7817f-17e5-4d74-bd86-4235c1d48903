using ONS.SINapse.Integracao.Shared.Enums;

namespace ONS.SINapse.Shared.DTO.Solicitacao;

public record EncaminharSolicitacaoDto(
    StatusDeSolicitacao Status,
    DateTime UpdatedAt,
    List<ChatDeSolicitacaoItemDto>? Chat,
    List<HistoricoDeStatusDeSolicitacaoDto> HistoricosDeStatus,
    bool Encaminhada);

public class SolicitacaoDto
{
    public SolicitacaoDto(
        string id,
        UsuarioDto usuario,
        ObjetoDeManobraDto origem,
        ObjetoDeManobraDto destino,
        string mensagem,
        string[] tags,
        string sistemaDeOrigem)
    {
        Id = id;
        HistoricosDeStatus = new List<HistoricoDeStatusDeSolicitacaoDto>();
        UsuarioDeCriacao = usuario;
        Origem = origem;
        Destino = destino;
        Mensagem = mensagem;
        MensagemNormalizada = mensagem;
        Tags = tags;
        SistemaDeOrigem = sistemaDeOrigem;
        Chat = [];
    }

    public string Id { get; protected set; }
    public DateTime CreatedAt { get; init; }
    public ObjetoDeManobraDto Origem { get; init; }
    public ObjetoDeManobraDto Destino { get; init; }
    public ObjetoDeManobraDto? Local { get; init; }
    public ObjetoDeManobraDto? EncaminharPara { get; init; }
    public string? InformacaoAdicional { get; init; }
    public string Mensagem { get; init; }
    public string MensagemNormalizada { get; init; }
    public StatusDeSolicitacao Status { get; init; }
    public bool FinalizadaAutomaticamente { get; init; }
    public string? DetalheDoImpedimento { get; init; }
    public UsuarioDto UsuarioDeCriacao { get; init; }
    public ICollection<HistoricoDeStatusDeSolicitacaoDto> HistoricosDeStatus { get; init; }
    public List<ChatDeSolicitacaoItemDto> Chat { get; init; }
    public DateTime UpdatedAt { get; init; }
    public bool IsExterna { get; init; }
    public string? CodigoExterno { get; init; }
    public string? LoteId { get; init; }
    public string SistemaDeOrigem { get; init; }
    public string? Motivo { get; init; }
    public bool Encaminhada { get; init; }
    public string? SolicitacaoDeOrigemId { get; init; }
    public string[] Tags { get; init; }
    public DateTime? DataInicioCadastro { get; init; }
}
