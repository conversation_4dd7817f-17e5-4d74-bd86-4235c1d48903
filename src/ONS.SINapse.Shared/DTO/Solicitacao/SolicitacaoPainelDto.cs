using ONS.SINapse.Integracao.Shared.Enums;

namespace ONS.SINapse.Shared.DTO.Solicitacao;

/// <summary>
/// Utilizado para criar solicitação para exibição no painel de solicitações em tempo real.
/// Quantidade de dados reduzida para otimizar a performance e consumir menos banda.
/// </summary>
public class SolicitacaoPainelDto
{
    public SolicitacaoPainelDto(
        string id,
        ObjetoDeManobraDto origem,
        ObjetoDeManobraDto destino,
        string mensagem,
        List<StatusPainelDto> historico,
        List<MensagenChatDeSolicitacaoPainelDto> chat,
        string sistemaDeOrigem)
    {
        Id = id;
        Origem = origem;
        Destino = destino;
        Mensagem = mensagem;
        HistoricosDeStatus = historico;
        Chat = chat;
        SistemaDeOrigem = sistemaDeOrigem;
    }

    public string Id { get; set; }
    public required DateTime CreatedAt { get; set; }
    public ObjetoDeManobraDto Origem { get; set; }
    public ObjetoDeManobraDto Destino { get; set; }
    public ObjetoDeManobraDto? Local { get; set; }
    public ObjetoDeManobraDto? EncaminharPara { get; set; }
    public string? InformacaoAdicional { get; set; }
    public string Mensagem { get; set; }
    public required StatusDeSolicitacao Status { get; set; }
    public List<StatusPainelDto> HistoricosDeStatus { get; set; }
    public List<MensagenChatDeSolicitacaoPainelDto> Chat { get; set; }
    public required DateTime UpdatedAt { get; set; }
    public string SistemaDeOrigem { get; set; }
    public required string? Motivo { get; set; }
    public required bool Encaminhada { get; set; }
    public required string[] Tags { get; set; }
}

/// <summary>
/// Utilizado para criar histórico de status para exibição no painel de solicitações em tempo real.
/// </summary>
public class HistoricoDeStatusPainelDto
{
    public HistoricoDeStatusPainelDto(IEnumerable<StatusPainelDto> historicoDeStatus)
    {
        HistoricoDeStatus = historicoDeStatus.ToArray();
    }

    public StatusPainelDto[] HistoricoDeStatus { get; init; }
}
public record StatusPainelDto(StatusDeSolicitacao Status);

public record ChatDeSolicitacaoPainelDto(List<MensagenChatDeSolicitacaoPainelDto> Chat);
public sealed class MensagenChatDeSolicitacaoPainelDto
{ 
    public MensagenChatDeSolicitacaoPainelDto(string mensagem, string usuario, string origem, StatusDeSolicitacao? status,  DateTime dataEHoraDeEnvio)
    {
        Mensagem = mensagem;
        UsuarioRemetente = usuario;
        Origem = origem;
        Status = status;
        DataEHoraDeEnvio = dataEHoraDeEnvio;
    }

    public string Mensagem { get; init; }
    public string UsuarioRemetente { get; init; }
    public string Origem { get; init; }
    public StatusDeSolicitacao? Status { get; init; }
    public DateTime DataEHoraDeEnvio { get; init; }
    public bool Lida { get; init; }
    public bool Entregue { get; init; }
}

public record TrocaDeStatusPainelDto(
    StatusDeSolicitacao Status, 
    DateTime UpdatedAt, 
    List<MensagenChatDeSolicitacaoPainelDto> Chat, 
    List<StatusPainelDto> HistoricosDeStatus, 
    string? DetalheDoImpedimento
    );
public record EncaminharSolicitacaoPainelDto(
    StatusDeSolicitacao Status, 
    DateTime UpdatedAt, 
    List<MensagenChatDeSolicitacaoPainelDto> Chat, 
    List<StatusPainelDto> HistoricosDeStatus,
    bool Encaminhada
    );

public record LeituraChatPainelDto(List<LeituraChatPainelItemDto> Chat);
public record LeituraChatPainelItemDto(bool Lida);

public record EntregaChatPainelDto(List<EntregaChatPainelItemDto> Chat);
public record EntregaChatPainelItemDto(bool Entregue);