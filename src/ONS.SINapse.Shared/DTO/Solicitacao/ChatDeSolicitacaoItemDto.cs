using ONS.SINapse.Integracao.Shared.Enums;

namespace ONS.SINapse.Shared.DTO.Solicitacao;

public record ChatDeSolicitacaoCompletoDto(List<ChatDeSolicitacaoDto> Chat);
public sealed class ChatDeSolicitacaoDto
{ 
    public ChatDeSolicitacaoDto(string mensagem, UsuarioDto usuario, ObjetoDeManobraDto origem, StatusDeSolicitacao? status,  DateTime? dataEHoraDeEnvio = null)
    {
        Mensagem = mensagem;
        UsuarioRemetente = usuario;
        Origem = origem;
        Status = status;
        DataEHoraDeEnvio = dataEHoraDeEnvio ?? DateTime.UtcNow;
    }

    public string Mensagem { get; init; }
    public UsuarioDto UsuarioRemetente { get; init; }
    public ObjetoDeManobraDto Origem { get; init; }
    public StatusDeSolicitacao? Status { get; init; }
    public DateTime DataEHoraDeEnvio { get; init; }
    public RegistroUsuarioHoraDto? PrimeiraLeitura { get; init; }
    public RegistroUsuarioHoraDto? PrimeiraEntrega { get; init; }
    public DateTime? DataEHoraDeEntregaAoDestinatario { get; init; }
    public DateTime? DataEHoraDeLeituraDoDestinatario { get; init; }
}

public record RegistroUsuarioHoraDto
{
    public UsuarioDto Usuario { get; init; }
    public DateTime Data { get; init; }

    public RegistroUsuarioHoraDto(UsuarioDto usuario, DateTime data)
    {
        Usuario = usuario;
        Data = data;
    }
}
