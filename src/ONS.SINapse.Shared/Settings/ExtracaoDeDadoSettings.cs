namespace ONS.SINapse.Shared.Settings;

public class ExtracaoDeDadoSettings
{
    public ExtracaoDeDadoSettings()
    {
        Directories = [];
        Bucket = string.Empty;
    }

    public bool RemoverRegistrosAposExtracao { get; set; }
    public int TempoDeVidaDosRegistrosEmHoras { get; set; }
    public int RegistrosPorArquivo { get; set; }
    public int RegistrosPorExecucao { get; set; }

    public string Bucket { get; set; }
    public DirectorySettings[] Directories { get; set; }
}

public class DirectorySettings
{
    public DirectorySettings()
    {
        Directory = string.Empty;
        Name = string.Empty;
    }

    public string Directory { get; set; }
    public string Name { get; set; }
}