
namespace ONS.SINapse.Shared.Settings;

public class FirebaseSettings
{
    public FirebaseSettings()
    {
        AuthSecret = string.Empty;
        BasePath = string.Empty;
        ArquivoConfigJson = string.Empty;
        Base64ConfigJson = string.Empty;
        TokenWrite = string.Empty;
        TokenRead = string.Empty;
        TokenReadWrite = string.Empty;
    }

    public string AuthSecret { get; init; }
    public string BasePath { get; init; }
    public string ArquivoConfigJson { get; init; }
    public string Base64ConfigJson { get; init; }
    public string TokenWrite { get; init; }
    public string TokenRead { get; init; }
    public string TokenReadWrite { get; init; }
    public int QuantidadeDeTentativas { get; init; }
    public int SegundosEntreTentativas { get; init; }
}
