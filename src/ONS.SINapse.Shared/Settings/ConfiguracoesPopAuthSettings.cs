namespace ONS.SINapse.Shared.Settings;

public class ConfiguracoesPopAuthSettings
{
    public ConfiguracoesPopAuthSettings()
    {
        Origin = string.Empty;
        TokenUrl = string.Empty;
        ClientId = string.Empty;
        UserName = string.Empty;
        Password = string.Empty;
        GrantTypeRefreshToken = string.Empty;
        GrantTypeAccessToken = string.Empty;
        UrlLogout = string.Empty;
    }

    public string Origin { get; set; }
    public string TokenUrl { get; set; }
    public string ClientId { get; set; }
    public string UserName { get; set; }
    public string Password { get; set; }
    public string GrantTypeRefreshToken { get; set; }
    public string GrantTypeAccessToken { get; set; }
    public string UrlLogout { get; set; }
}