namespace ONS.SINapse.Shared.Settings;

public class ConfiguracaoDeSistemaSettings
{
    public ConfiguracaoDeSistemaSettings()
    {
        Sons = new SonsDoSistemaSettings();
    }

    public int SegundosEntreNotificacoesPendentes { get; init; }
    public int QuantidadeDeDiasLimiteParaConsultaDoHistoricoDeSolicitacoes { get; init; }
    public SonsDoSistemaSettings Sons { get; init; }
}

public class SonsDoSistemaSettings
{
    public SonsDoSistemaSettings()
    {
        Sucesso = string.Empty;
        Erro = string.Empty;
        Alerta = string.Empty;
        Broadcast = string.Empty;
    }

    public string Sucesso { get; init; }
    public string Erro { get; init; }
    public string Alerta { get; init; }
    public string Broadcast { get; init; }
}