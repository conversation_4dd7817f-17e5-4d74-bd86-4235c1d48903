namespace ONS.SINapse.Shared.Settings;

public class KafkaSettings
{
    public required bool Ativo { get; init; }
    public required int QuantidadeDeWorkersPorInstancia { get; init; }
    public required bool RequireAuth { get; init; }
    public required string BootstrapServers { get; init; }
    public required string SecurityProtocol { get; init; }
    public required string SslCaPem { get; init; }
    public required string SslKeystorePassword { get; init; }
    public required string SaslMechanism { get; init; }
    public required string SaslUsername { get; init; }
    public required string SaslPassword { get; init; }
    public required string ConsumerName { get; init; }
    
    public bool IsKafkaInactive() => !Ativo;
}
