namespace ONS.SINapse.Shared.Settings;

public class CacheSettings
{
    public CacheSettings() 
    {
        Host = string.Empty;
        Port = string.Empty;
        Password = string.Empty;
        InstanceName = string.Empty;
    }

    public int ExpiracaoDeFonteEmMinutos { get; set; }
    public int ExpiracaoDeMotivoEmMinutos { get; set; }
    public int ExpiracaoDeOperacaoEmMinutos { get; set; }
    public string Host { get; set; }
    public string Port { get; set; }
    public string Password { get; set; }
    public string InstanceName { get; set; }
    public bool UseRedis { get; set; }
}