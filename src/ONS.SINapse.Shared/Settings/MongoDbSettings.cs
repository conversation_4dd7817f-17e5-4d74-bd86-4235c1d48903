namespace ONS.SINapse.Shared.Settings;

public class MongoDbSettings
{
    public MongoDbSettings()
    {
        ConnectionString = string.Empty;
        DatabaseName = string.Empty;
        MaxConnections = 0;
        MaxConnectionIdleTimeInSeconds = 0;
    }

    public string ConnectionString { get; set; }
    public string DatabaseName { get; set; }
    public int MaxConnections { get; set; }
    public int MaxConnectionIdleTimeInSeconds { get; set; }
}