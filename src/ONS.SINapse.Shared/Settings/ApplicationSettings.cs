namespace ONS.SINapse.Shared.Settings;

public class ApplicationSettings
{
    public ApplicationSettings()
    {
        ServerBaseUrl = string.Empty;
        ServerLocalBaseUrl = string.Empty;
        HealthcheckSettings = [];
        Version = string.Empty;
    }

    public string ServerBaseUrl { get; set; }
    
    public string ServerLocalBaseUrl { get; set; }
    
    public List<HealthcheckSettings> HealthcheckSettings { get; set; }
    
    public string Version { get; set; }
}

public class HealthcheckSettings
{
    public HealthcheckSettings(string endpointName, string endpointUrl)
    {
        EndpointName = endpointName;
        EndpointUrl = endpointUrl;
    }

    public string EndpointName { get; set; }
    public string EndpointUrl { get; set; }
}