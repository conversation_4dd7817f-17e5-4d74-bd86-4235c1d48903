namespace ONS.SINapse.Shared.Settings;

public class AuthenticationSettings
{
    public AuthenticationSettings(string serviceUri, int applicationCacheExpiration, string applicationName, string applicationOrigin, string username, string password)
    {
        ServiceUri = serviceUri;
        ApplicationCacheExpiration = applicationCacheExpiration;
        ApplicationName = applicationName;
        ApplicationOrigin = applicationOrigin;
        Username = username;
        Password = password;
    }

    public string ServiceUri { get; set; }
    public int ApplicationCacheExpiration { get; set; }
    public string ApplicationName { get; set; }
    public string ApplicationOrigin { get; set; }
    public string Username { get; set; }
    public string Password { get; set; }
}