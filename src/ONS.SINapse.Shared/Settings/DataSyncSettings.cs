namespace ONS.SINapse.Shared.Settings;

public class DataSyncSettings : ApiOptions
{
    public DataSyncSettings()
    {
        SyncWaitTimeInMinutes = 5;
        CustomHeaders = new Dictionary<string, string>();
        ServiceUri = string.Empty;
        DatasetCacheExpiration = 0;
    }
    
    public int SyncWaitTimeInMinutes { get; set; }
}

public abstract class ApiOptions
{
    protected ApiOptions()
    {
        CustomHeaders = new Dictionary<string, string>();
        ServiceUri = string.Empty;
        DatasetCacheExpiration = 0;
        Authentication = new AuthenticationSettings(string.Empty, 0, string.Empty, string.Empty, string.Empty, string.Empty);
    }

    public Dictionary<string, string>? CustomHeaders { get; set; }
    public string ServiceUri { get; set; }
    public int DatasetCacheExpiration { get; set; }
    public AuthenticationSettings Authentication { get; set; }
}