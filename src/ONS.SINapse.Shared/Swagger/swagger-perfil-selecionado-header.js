
// Limpa o header armazenado ao recarregar a página
window.addEventListener('beforeunload', () => {
    localStorage.removeItem('perfilSelecionado');
});

(function () {
    const waitForRender = setInterval(() => {
        const section = document.querySelector('section.schemes.wrapper.block.col-12');
        const authorizeBtn = section?.querySelector('.auth-wrapper button');
        if (!section || !authorizeBtn || section.querySelector('#perfilSelecionadoBtn')) return;

        const wrapper = document.createElement('div');
        wrapper.className = 'perfil-selecionado-wrapper';
        wrapper.style.display = 'flex';
        wrapper.style.justifyContent = 'flex-end';
        wrapper.style.marginTop = '5px';

        const btn = document.createElement('button');
        btn.id = "perfilSelecionadoBtn";
        btn.className = authorizeBtn.className;
        btn.innerText = "Perfil Selecionado";
        btn.style.marginLeft = "10px";
        btn.style.minHeight = authorizeBtn.offsetHeight + 'px';
        btn.style.paddingTop = window.getComputedStyle(authorizeBtn).paddingTop;
        btn.style.paddingBottom = window.getComputedStyle(authorizeBtn).paddingBottom;
        wrapper.appendChild(btn);
        section.appendChild(wrapper);

        const style = document.createElement('style');
        style.innerHTML = `
      .perfil-selecionado-modal {
        position: fixed;
        top: 0; left: 0;
        width: 100%; height: 100%;
        background: rgba(0, 0, 0, 0.4);
        display: none;
        justify-content: center;
        align-items: center;
        z-index: 9999;
      }
      .perfil-selecionado-modal .modal-content {
        background: #ffffff;
        border-radius: 4px;
        padding: 20px;
        width: 420px;
        box-shadow: 0 0 15px rgba(0, 0, 0, 0.3);
        font-family: sans-serif;
      }
      .perfil-selecionado-modal h4 {
        margin-top: 0;
        font-size: 1.25em;
        font-weight: bold;
      }
      .perfil-selecionado-modal label {
        display: block;
        margin-top: 10px;
        font-weight: 600;
      }
      .perfil-selecionado-modal input,
      .perfil-selecionado-modal select {
        width: 100%;
        padding: 8px;
        margin-top: 5px;
        border: 1px solid #ccc;
        border-radius: 3px;
        font-size: 14px;
      }
      .perfil-selecionado-modal .actions {
        margin-top: 15px;
        text-align: right;
        display: flex;
        justify-content: space-between;
      }
      .perfil-selecionado-modal button {
        flex: 1;
        margin-left: 5px;
        padding: 8px 0;
        border: none;
        border-radius: 3px;
        cursor: pointer;
        font-size: 14px;
        transition: background 0.3s, opacity 0.3s;
      }
      .perfil-selecionado-modal .btn-ok { background: #89bf04; color: white; }
      .perfil-selecionado-modal .btn-clear { background: #f0ad4e; color: white; }
      .perfil-selecionado-modal .btn-cancel { background: #ccc; color: black; }

      .perfil-selecionado-modal button:disabled {
        background: #ddd !important;
        color: #888 !important;
        cursor: not-allowed !important;
        opacity: 0.6;
      }
    `;
        document.head.appendChild(style);

        const modal = document.createElement('div');
        modal.className = 'perfil-selecionado-modal';
        modal.id = 'perfilModal';
        modal.innerHTML = `
      <div class="modal-content">
        <h4>Selecionar Perfil</h4>
        <label for="dropdownPerfis">Perfil</label>
        <select id="dropdownPerfis">
          <option value="">-- carregando perfis --</option>
        </select>
        <label for="centroOrigem">Codigo Escopo</label>
        <input type="text" id="centroOrigem" placeholder="CN" />
        <label for="centroDestino">Nome Escopo</label>
        <input type="text" id="centroDestino" placeholder="CNOS" />
        <div class="actions">
          <button id="okBtn" class="btn-ok" disabled>Confirmar</button>
          <button id="limparBtn" class="btn-clear" disabled>Limpar</button>
          <button id="cancelarBtn" class="btn-cancel">Cancelar</button>
        </div>
      </div>
    `;
        document.body.appendChild(modal);

        const dropdown = modal.querySelector('#dropdownPerfis');
        const origem = modal.querySelector('#centroOrigem');
        const destino = modal.querySelector('#centroDestino');
        const btnOk = modal.querySelector('#okBtn');
        const btnClear = modal.querySelector('#limparBtn');
        const btnCancel = modal.querySelector('#cancelarBtn');

        const validateFields = () => {
            const filled = dropdown.value && origem.value && destino.value;
            btnOk.disabled = !filled;
        };

        const validateClearButton = () => {
            btnClear.disabled = !localStorage.getItem('perfilSelecionado');
        };

        [dropdown, origem, destino].forEach(el => {
            el.addEventListener('input', () => {
                validateFields();
            });
        });

        btn.onclick = () => {
            modal.style.display = 'flex';
            validateFields();
            validateClearButton();
        };

        btnCancel.onclick = () => modal.style.display = 'none';

        btnClear.onclick = () => {
            dropdown.value = '';
            origem.value = '';
            destino.value = '';
            localStorage.removeItem('perfilSelecionado');
            validateFields();
            validateClearButton();
        };

        btnOk.onclick = () => {
            if (dropdown.value && origem.value && destino.value) {
                localStorage.setItem('perfilSelecionado', encodeURIComponent(`${dropdown.value}/CENTROS/${origem.value}/${destino.value}`));
                modal.style.display = 'none';
            }
        };

        fetch("/data/permissao-usuario.json")
            .then(res => res.json())
            .then(data => {
                const nomesComLogar = data.RuleOperations
                    .filter(op => op.Operations.includes("LogarNoSinapse"))
                    .map(op => op.Nome);
                dropdown.innerHTML = `<option value="">-- selecione um perfil --</option>`;
                nomesComLogar.forEach(nome => {
                    const option = document.createElement('option');
                    option.value = nome;
                    option.text = nome;
                    dropdown.appendChild(option);
                });
            })
            .catch(err => {
                console.error("Erro ao carregar permissao-usuario.json:", err);
                dropdown.innerHTML = `<option value="">-- erro ao carregar perfis --</option>`;
            });

        clearInterval(waitForRender);
    }, 500);

    window.swaggerCustomInit = function () {
        if (window.ui) {
            const originalInterceptor = window.ui.getConfigs().requestInterceptor;
            window.ui.getConfigs().requestInterceptor = (req) => {
                const valor = localStorage.getItem('perfilSelecionado');
                if (valor) {
                    req.headers['Perfil-Selecionado'] = valor;
                }
                return originalInterceptor ? originalInterceptor(req) : req;
            };
        }
    };

    const initInterceptorInterval = setInterval(() => {
        if (window.ui && typeof window.swaggerCustomInit === "function") {
            clearInterval(initInterceptorInterval);
            window.swaggerCustomInit();
        }
    }, 500);
})();
