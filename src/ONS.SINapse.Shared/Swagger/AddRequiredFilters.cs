using Microsoft.OpenApi.Any;
using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;

namespace ONS.SINapse.Shared.Swagger;

public class AddRequiredHeadersFilter : IOperationFilter
{
    public void Apply(OpenApiOperation operation, OperationFilterContext context)
    {
        if (operation.Parameters == null)
            operation.Parameters = new List<OpenApiParameter>();

        operation.Parameters.Add(new OpenApiParameter
        {
            Name = "User-Agent",
            In = ParameterLocation.Header,
            Required = false,
            Description = "User-Agent do navegador ou aplicação cliente",
            Schema = new OpenApiSchema
            {
                Type = "string",
                Default = new OpenApiString("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
            }
        });
    }
}
