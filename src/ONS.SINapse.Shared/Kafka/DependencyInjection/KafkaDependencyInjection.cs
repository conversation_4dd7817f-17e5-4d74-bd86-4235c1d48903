using Microsoft.Extensions.DependencyInjection;
using ONS.SINapse.Shared.Kafka.Providers;

namespace ONS.SINapse.Shared.Kafka.DependencyInjection;

public static class KafkaDependencyInjection
{
    public static IServiceCollection AddKafkaProvider(this IServiceCollection services)
    {
        services.AddSingleton(typeof(IProducerProvider<>), typeof(ProducerProvider<>));

        services.AddSingleton(typeof(IConsumerProvider<>), typeof(ConsumerProvider<>));
        
        return services;
    }
}