namespace ONS.SINapse.Shared.Kafka;

public abstract class TopicoIntegrationKafka<TIntegration> where TIntegration : IIntegrationKafka
{
    protected TopicoIntegrationKafka(string topico)
    {
        Topico = topico;
    }

    public string Topico { get; private set; }

    public virtual void AlterarValor(string novoTopico) => Topico = novoTopico;
    
    public override string ToString()
    {
        return Topico;
    }

    public Type GetTypeIntegration() => typeof(TIntegration);
}