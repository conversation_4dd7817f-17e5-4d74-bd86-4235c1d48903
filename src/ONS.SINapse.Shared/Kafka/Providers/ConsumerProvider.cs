using Confluent.Kafka;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using ONS.SINapse.Shared.Messages;
using ONS.SINapse.Shared.Settings;

namespace ONS.SINapse.Shared.Kafka.Providers;

public interface IConsumerProvider<out TEvent> : IDisposable where TEvent : IntegrationKafkaEvent, new()
{
    Task SubscribeAsync(Func<TEvent, Task> onMessage, CancellationToken cancellationToken);
}


public class ConsumerProvider<TEvent> : IConsumerProvider<TEvent> where TEvent : IntegrationKafkaEvent, new()
{
    private readonly ILogger<ConsumerProvider<TEvent>> _logger;
    private readonly IConsumer<Ignore, TEvent>? _consumer;
    private readonly TopicoIntegrationKafka<TEvent> _topicoIntegrationKafka;
    private readonly KafkaSettings _options;
    
    public ConsumerProvider(IOptions<KafkaSettings> options, 
        ILogger<ConsumerProvider<TEvent>> logger,
        TopicoIntegrationKafka<TEvent> topico)
    {
        _logger = logger;
        _topicoIntegrationKafka = topico;
        _options = options.Value;
        _consumer ??= Connect();
    }

    public async Task SubscribeAsync(Func<TEvent, Task> onMessage, CancellationToken cancellationToken)
    {
        if(!EstaConectado()) return;
        
        try
        {
            var result = await Task.Run(() => _consumer!.Consume(cancellationToken), cancellationToken);
            
            await onMessage(result.Message.Value);
            
            _consumer!.Commit(result);
        }
        catch (Exception ex)
        {
            _logger.LogInformation(ex, "Operação foi cancelada.");
        }
    }

    private void OnError(IConsumer<Ignore, TEvent> consumer, Error error)
    {
        _logger.LogError("[OnError] - [{Name}] - {Reason}", consumer.Name, error.Reason);
    }

    private IConsumer<Ignore, TEvent>? Connect()
    {
        if (!_options.Ativo)
        {
            _logger.LogWarning("Nenhuma conexão pode ser feita, Kafka está desabilitado.");
            return null;
        }
        
        var consumer = BuildConsumer(_options);
        consumer.Subscribe(_topicoIntegrationKafka.ToString());
        return consumer;
    }

    private bool EstaConectado() => _consumer is not null && _options.Ativo;
    
    private IConsumer<Ignore, TEvent> BuildConsumer(KafkaSettings options)
    {
        var consumerName = string.IsNullOrEmpty(options.ConsumerName) ? "ONS.SINAPSE.CONSUMER" : options.ConsumerName+".CONSUMER";
        
        var group = $"{consumerName}.{typeof(TEvent).Name}";
        
        var config = new ConsumerConfig
        {
            BootstrapServers = options.BootstrapServers,
            EnableAutoCommit = false,
            EnableAutoOffsetStore = true,
            AutoOffsetReset = AutoOffsetReset.Earliest,
            GroupId = group
        };
        
        if (options.RequireAuth)
        {
            config.SecurityProtocol = (SecurityProtocol)Enum.Parse(typeof(SecurityProtocol), options.SecurityProtocol);
            config.SslCaPem = options.SslCaPem;
            config.SslKeystorePassword = options.SslKeystorePassword;
            config.SaslMechanism = (SaslMechanism)Enum.Parse(typeof(SaslMechanism), options.SaslMechanism);
            config.SaslUsername = options.SaslUsername;
            config.SaslPassword = options.SaslPassword;
        }


        _logger.LogInformation("Parametro BootstrapServers {BootstrapServers} Publicado e finalizado", options.BootstrapServers);
        _logger.LogInformation("Parametro ConsumerName {ConsumerName} Publicado e finalizado", options.ConsumerName);
        _logger.LogInformation("Parametro Certificado {SslCaPem} Publicado e finalizado", options.SslCaPem);
        _logger.LogInformation("Parametro SslKeystorePassword {SslKeystorePassword} Publicado e finalizado", options.SslKeystorePassword);
        _logger.LogInformation("Parametro SaslMechanism {SaslMechanism} Publicado e finalizado", options.SaslMechanism);
        _logger.LogInformation("Parametro SaslUsername {SaslUsername} Publicado e finalizado", options.SaslUsername);
        _logger.LogInformation("Parametro SaslPassword {SaslPassword} Publicado e finalizado", options.SaslPassword);

        var builder = new ConsumerBuilder<Ignore, TEvent>(config)
            .SetKeyDeserializer(Deserializers.Ignore)
            .SetValueDeserializer(new KafkaValueDeserializer<TEvent>())
            .SetLogHandler((consumer, log) => _logger.LogInformation("[{Name}] - {Message}", consumer.Name, log.Message))
            .SetErrorHandler(OnError);

        return builder.Build();
    }
    


    private bool _disposed;
    protected virtual void Dispose(bool disposing)
    {
        if (_disposed) return;

        if (disposing)
        {
            _consumer?.Close();
            _consumer?.Dispose();
        }

        _disposed = true;
    }
    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }
}