using Confluent.Kafka;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using ONS.SINapse.Shared.Settings;
using System.Diagnostics;
using System.Text;
using System.Text.Json;

namespace ONS.SINapse.Shared.Kafka.Providers;

public interface IProducerProvider<TMessage> : IDisposable where TMessage : IIntegrationKafka
{
    Task PublishAsync(TMessage message, IDictionary<string, string> headers, CancellationToken cancellationToken);
    void AtualizarTopico(Action<TopicoIntegrationKafka<TMessage>> options);
    bool EstaConectado();
}


public class ProducerProvider<TMessage> : IProducerProvider<TMessage> where TMessage : IIntegrationKafka
{
    private readonly ILogger<ProducerProvider<TMessage>> _logger;
    private readonly TopicoIntegrationKafka<TMessage> _topico;
    private readonly KafkaSettings _options;
    private readonly IProducer<string, TMessage>? _producer;
    private const string KafkaActivitySourceName = "ONS.SINapse.Kafka";

    public ProducerProvider(ILogger<ProducerProvider<TMessage>> logger, 
        TopicoIntegrationKafka<TMessage> topico, IOptions<KafkaSettings> options)
    {
        _logger = logger;
        _topico = topico;
        _options = options.Value;
        _producer ??= Connect();
    }
    
    public async Task PublishAsync(TMessage message, IDictionary<string, string> headers, CancellationToken cancellationToken)
    {
        if (!EstaConectado()) return;

        using var activitySource = new ActivitySource(KafkaActivitySourceName);
        using Activity? activity = activitySource.StartActivity("Kafka Producer", ActivityKind.Producer);

        activity?.SetTag("messaging.system", "kafka");
        activity?.SetTag("messaging.destination", _topico.ToString());
        activity?.SetTag("messaging.destination_kind", "topic");
        activity?.SetTag("messaging.message", JsonSerializer.Serialize(message));

        var requestHeader = BuildKafkaHeaders(headers, activity);

        var request = new Message<string, TMessage>
        {
            Key = Guid.NewGuid().ToString(),
            Value = message,
            Headers = requestHeader
        };
        
        await _producer!.ProduceAsync(_topico.ToString(), request, cancellationToken);
    }

    public void AtualizarTopico(Action<TopicoIntegrationKafka<TMessage>> options)
    {
        options(_topico);
    }

    private IProducer<string, TMessage>? Connect()
    {
        if (!_options.Ativo)
        {
            _logger.LogWarning("Nenhuma conexão pode ser feita, Kafka está desabilitado.");
            return null;
        }
        
        var builder = new ProducerBuilder<string, TMessage>(BuildProducerConfiguration())
            .SetKeySerializer(Serializers.Utf8)
            .SetValueSerializer(new KafkaValueSerializer<TMessage>())
            .SetLogHandler((producer, log) => _logger.LogInformation("[{Name}] - {Message}", producer.Name, log.Message))
            .SetErrorHandler(OnError);

        return builder.Build();
    }

    public bool EstaConectado() => _producer is not null && _options.Ativo;
    
    private void OnError(IProducer<string, TMessage> consumer, Error error)
    {
        _logger.LogError("[OnError][{Name}] - {Reason}", consumer.Name, error.Reason);
    }   
    
    private ProducerConfig BuildProducerConfiguration() => BuildProducerConfiguration(typeof(TMessage).Name, _options);

    private static ProducerConfig BuildProducerConfiguration(string nameProducer, KafkaSettings options)
    {
        var config = new ProducerConfig
        {
            BootstrapServers = options.BootstrapServers,
            ClientId = "ONS.SINAPSE.PRODUCER." + nameProducer
        };

        if (!options.RequireAuth) return config;
        
        config.SecurityProtocol = (SecurityProtocol)Enum.Parse(typeof(SecurityProtocol), options.SecurityProtocol);
        config.SslCaPem = options.SslCaPem;
        config.SslKeystorePassword = options.SslKeystorePassword;
        config.SaslMechanism = (SaslMechanism)Enum.Parse(typeof(SaslMechanism), options.SaslMechanism);
        config.SaslUsername = options.SaslUsername;
        config.SaslPassword = options.SaslPassword;

        return config;
    }

    private static Headers BuildKafkaHeaders(IDictionary<string, string> headers, Activity? activity)
    {
        var requestHeader = new Headers();

        if (headers.Any())
        {
            foreach (var (key, value) in headers)
            {
                requestHeader.Add(key, Encoding.UTF8.GetBytes(value));
            }
        }

        if (activity != null)
        {
            var context = activity.Context;
            requestHeader.Add("traceparent", Encoding.UTF8.GetBytes($"00-{context.TraceId}-{context.SpanId}-01"));
        }

        return requestHeader;
    }

    private bool _disposed;
    protected virtual void Dispose(bool disposing)
    {
        if (_disposed) return;

        if (disposing)
        {
            _producer?.Dispose();
        }

        _disposed = true;
    }
    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }
}