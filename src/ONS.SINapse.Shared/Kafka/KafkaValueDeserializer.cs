using System.Text.Json;
using System.Text.Json.Serialization;
using Confluent.Kafka;

namespace ONS.SINapse.Shared.Kafka;

/// <summary>
/// Classe que deserializa os retornos em bytes do Kafka
/// Obs: Utiliza biblioteca System.Text.Json para deserializar
/// </summary>
/// <typeparam name="T">Objeto deserializado a retornar do kafka</typeparam>
public class KafkaValueDeserializer<T> : IDeserializer<T> where T : IntegrationKafkaEvent, new()
{
    public T Deserialize(ReadOnlySpan<byte> data, bool isNull, SerializationContext context)
    {
        if (isNull) return new T();
        
        var evento = JsonSerializer.Deserialize<T>(data, DefaultJsonSerializerOptions.JsonSerializerOptions);
        return evento ?? new T();
    }
}

public static class DefaultJsonSerializerOptions
{
    public static readonly JsonSerializerOptions JsonSerializerOptions = new()
    {
        AllowTrailingCommas = false,
        DefaultIgnoreCondition = JsonIgnoreCondition.Never,
        IgnoreReadOnlyFields = true,
        IgnoreReadOnlyProperties = true,
        IncludeFields = false,
        MaxDepth = 0,
        NumberHandling = JsonNumberHandling.Strict,
        PropertyNameCaseInsensitive = true,
        PropertyNamingPolicy = null,
        ReadCommentHandling = JsonCommentHandling.Disallow,
        ReferenceHandler = ReferenceHandler.IgnoreCycles,
        UnknownTypeHandling = JsonUnknownTypeHandling.JsonElement,
        WriteIndented = false
    };
}