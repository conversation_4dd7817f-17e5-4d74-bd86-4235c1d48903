using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.Options;
using MongoDB.Bson;
using MongoDB.Driver;
using ONS.SINapse.Shared.Settings;

namespace ONS.SINapse.Shared.Diagnostics;

public class MongoDBHealthCheck: IHealthCheck
{
    private readonly IMongoClient _mongoClient;
    private readonly MongoDbSettings _mongoDbSettings;
    private static readonly BsonDocumentCommand<BsonDocument> Command = new(BsonDocument.Parse("{ping:1}"));

    public MongoDBHealthCheck(IMongoClient mongoClient, IOptions<MongoDbSettings> options)
    {
        _mongoClient = mongoClient;
        _mongoDbSettings = options.Value;
    }
    
    public async Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default)
    {
        try
        {
            await _mongoClient
                .GetDatabase(_mongoDbSettings.DatabaseName)
                .RunCommandAsync(Command, cancellationToken: cancellationToken)
                .ConfigureAwait(false);
            
            return HealthCheckResult.Healthy("MongoDB connection is healthy");
        }
        catch (Exception ex)
        {
            return HealthCheckResult.Unhealthy($"MongoDB connection is unhealthy. Error: {ex.Message}", exception: ex);
        }
    }
}

public static class MongoHealthCheckExtension
{
    private const int TimeoutInSeconds = 10;
    private static readonly string[] tags = ["mongodb"];
    public static IHealthChecksBuilder AddMongo(this IHealthChecksBuilder builder)
    {
        builder.Services.TryAddSingleton<MongoDBHealthCheck>();
        
        builder.AddCheck<MongoDBHealthCheck>("MongoDB", tags: tags,
            timeout: TimeSpan.FromSeconds(TimeoutInSeconds));
        
        return builder;
    }
}