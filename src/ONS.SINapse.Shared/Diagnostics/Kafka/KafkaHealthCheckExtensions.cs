using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using ONS.SINapse.Integracao.Shared.Settings;
using ONS.SINapse.Shared.Kafka;

namespace ONS.SINapse.Shared.Diagnostics.Kafka;

public static class KafkaHealthCheckExtensions
{
    private const int TimeoutInSeconds = 10;
    private static readonly string[] tags = ["kafka"];
    public static IHealthChecksBuilder AddKafka(this IHealthChecksBuilder builder)
    {
        builder.Services.AddSingleton<KafkaHealthCheck>();

        builder.Services
            .AddSingleton<TopicoIntegrationKafka<KafkaHealthCheckMessage>, KafkaHealthCheckTopicoIntegration>(
                provider =>
                {
                    var options = provider.GetRequiredService<IOptions<KafkaTopicsSettings>>().Value;
                    return new KafkaHealthCheckTopicoIntegration(options.Health);
                });
        
        builder.AddCheck<KafkaHealthCheck>("Kafka", tags: tags,
            timeout: TimeSpan.FromSeconds(TimeoutInSeconds));
        
        return builder;
    }
}