using Microsoft.Extensions.Diagnostics.HealthChecks;
using ONS.SINapse.Shared.Kafka.Providers;

namespace ONS.SINapse.Shared.Diagnostics.Kafka;

public class KafkaHealthCheck : IHealthCheck, IDisposable
{
    private readonly IProducerProvider<KafkaHealthCheckMessage> _producer;
    
    public KafkaHealthCheck(IProducerProvider<KafkaHealthCheckMessage> producer)
    {
        _producer = producer;
    }
    
    public async Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default)
    {
        if (!_producer.EstaConectado())
        {
            return HealthCheckResult.Healthy("Kafka is not active.");
        }

        try
        {
            await _producer.PublishAsync(new KafkaHealthCheckMessage(), new Dictionary<string, string>(), cancellationToken);

            return HealthCheckResult.Healthy("Kafka connection is healthy.");

        }
        catch (Exception ex)
        {
            return HealthCheckResult.Unhealthy($"Kafka connection is unhealthy. Error: {ex.Message}", exception: ex);
        }
    }

    private bool _disposed;
    protected virtual void Dispose(bool disposing)
    {
        if (_disposed) return;

        if (disposing)
        {
            _producer.Dispose();
        }

        _disposed = true;
    }
    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }
}