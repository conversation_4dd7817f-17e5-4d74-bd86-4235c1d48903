using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using MongoDB.Driver;
using ONS.SINapse.Shared.Constants;
using ONS.SINapse.Shared.Diagnostics.Base;
using ONS.SINapse.Shared.Settings;

namespace ONS.SINapse.Shared.Diagnostics;

public class FinalizarSolicitacoesAutomaticamenteHealthCheck : LimpezaDeDadosJobHealthCheck
{
    public FinalizarSolicitacoesAutomaticamenteHealthCheck(IMongoDatabase mongoDatabase,
        IOptions<FinalizacaoAutomaticaDeSolicitacaoSettings> finalizacaoAutomaticaDeSolicitacaoSettings) : base(mongoDatabase, finalizacaoAutomaticaDeSolicitacaoSettings)
    {
    }
    protected override string GetServiceName()
    {
        return JobService.FinalizarSolicitacaoAutomaticamente;
    }
}

public static class FinalizarSolicitacoesAutomaticamenteHealthCheckExtension
{
    private const int TimeoutInSeconds = 10;
    private static readonly string[] tags = ["finalizar-solicitacoes", "job"];
    public static IHealthChecksBuilder AddFinalizarSolicitacoesAutomaticamente(this IHealthChecksBuilder builder)
    {
        builder.Services.AddScoped<FinalizarSolicitacoesAutomaticamenteHealthCheck>();
        
        builder.AddCheck<FinalizarSolicitacoesAutomaticamenteHealthCheck>(
            JobService.FinalizarSolicitacaoAutomaticamente, 
            tags: tags,
            timeout: TimeSpan.FromSeconds(TimeoutInSeconds));
        
        return builder;
    }
}
