using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.Options;
using MongoDB.Driver;
using ONS.SINapse.Shared.Contracts;
using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Shared.Settings;

namespace ONS.SINapse.Shared.Diagnostics.Base;

public abstract class LimpezaDeDadosJobHealthCheck : IHealthCheck
{
    private readonly IMongoDatabase _mongoDatabase;
    private readonly int _intervaloDeExecucaoEmMinutos;
    
    protected LimpezaDeDadosJobHealthCheck(IMongoDatabase mongoDatabase, IOptions<FinalizacaoAutomaticaDeSolicitacaoSettings> finalizacaoAutomaticaDeSolicitacaoSettings)
    {
        _mongoDatabase = mongoDatabase;
        _intervaloDeExecucaoEmMinutos = finalizacaoAutomaticaDeSolicitacaoSettings.Value.IntervaloDeExecucaoEmMinutos;
    }
    
    protected abstract string GetServiceName();
    
    public Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = new CancellationToken())
    {
        var serviceName = GetServiceName();
        var service = _mongoDatabase
            .GetCollection<StatusServiceContract>("c_statusservice")
            .AsQueryable().FirstOrDefault(x => x.Service == serviceName);
            
        if (service is null)
        { 
            return Task.FromResult(HealthCheckResult.Unhealthy($"Serviço {serviceName} não executado."));
        }
        
        if (service.Date.AddMinutes(_intervaloDeExecucaoEmMinutos + 1 ) < DateTime.Now)
        {
            return Task.FromResult(HealthCheckResult.Unhealthy($"Serviço {serviceName} parado. Última execução em {service.Date.ToFormattedSouthAmericaStandardTime()}."));
        }

        if (service.Health)
        {
            return Task.FromResult(HealthCheckResult.Healthy($"Serviço {serviceName} está saudável."));
        }
           
        return Task.FromResult(HealthCheckResult.Unhealthy($"Serviço {serviceName} com falha. {service.Error}", service.Exception));
    }
}