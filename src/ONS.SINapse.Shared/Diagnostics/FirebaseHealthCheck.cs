using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using ONS.SINapse.Shared.Services.Firebase;

namespace ONS.SINapse.Shared.Diagnostics;

public class FirebaseHealthCheck : IHealthCheck
{
    private readonly IFirebaseAuthService _firebaseAuthService;

    public FirebaseHealthCheck(IFirebaseAuthService firebaseAuthService)
    {
        _firebaseAuthService = firebaseAuthService;
    }
    
    public async Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default)
    {
        try
        {
            var token = await _firebaseAuthService.GetFirebaseTokenAsync(nameof(FirebaseHealthCheck),
                cancellationToken);
            
            return string.IsNullOrEmpty(token.FirebaseToken) 
                ? HealthCheckResult.Unhealthy("Firebase connection is unhealthy. Token is null.") 
                : HealthCheckResult.Healthy("Firebase connection is healthy.");
            
        }
        catch (Exception e)
        {
            return HealthCheckResult.Unhealthy($"Firebase connection is unhealthy. Error: {e.Message}", exception: e);
        }
    }
}

public static class FirebaseHealthCheckExtensions
{
    private const int TimeoutInSeconds = 10;
    private static readonly string[] tags = ["firebase"];
    public static IHealthChecksBuilder AddFirebase(this IHealthChecksBuilder builder)
    {
        builder.Services.AddScoped<FirebaseHealthCheck>();
        
        builder.AddCheck<FirebaseHealthCheck>("Firebase", tags: tags,
            timeout: TimeSpan.FromSeconds(TimeoutInSeconds));
        
        return builder;
    }
}

