namespace ONS.SINapse.Shared.Logs;

public abstract class ApiLogs
{
    /// <summary>
    /// Define se deve logar o response, caso null nada será logado
    /// </summary>
    public ApiResponseLogs? Response { get; private set; }    
    
    /// <summary>
    /// Define se deve logar o request, caso null nada será logado
    /// </summary>
    public ApiRequestLogs? Request { get; private set; }

    public virtual void AddResposeLog(Action<ApiResponseLogs>? options = null)
    {
        Response = new ApiResponseLogs();
        
        if (options is null) return;
        
        options(Response);
    }

    public virtual void AddRequestLog(Action<ApiRequestLogs>? options = null)
    {
        Request = new ApiRequestLogs();
        
        if (options is null) return;
        
        options(Request);
    }
}


public class ApiResponseLogs
{
    /// <summary>
    /// Define se deve gerar logs do Content
    /// </summary>
    public bool SemContent { get; set; }
    
    /// <summary>
    /// Define se deve gerar logs dos Headers
    /// </summary>
    public bool SemHeader { get; set; }
    
    /// <summary>
    /// Define se deve gerar logs do Status
    /// </summary>
    public bool SemStatus { get; set; }
    
    /// <summary>
    /// Define se deve gerar logs apenas de respostas com erro
    /// </summary>
    public bool ApenasRespostaComErro { get; set; }
}

public class ApiRequestLogs
{
    /// <summary>
    /// Define se deve gerar logs do Content
    /// </summary>
    public bool SemContent { get; set; }
    
    /// <summary>
    /// Define se deve gerar logs dos Headers
    /// </summary>
    public bool SemHeader { get; set; }
    
    /// <summary>
    /// Define se deve gerar logs da uri
    /// </summary>
    public bool SemUri { get; set; }
    
    /// <summary>
    /// Define se deve gerar logs do Method
    /// </summary>
    public bool SemMethod { get; set; }
}