
namespace ONS.SINapse.Shared.Constants;

public static class CacheRedisKey
{
    public const string KeyIComunicado = "Sinapse.ComunicadoRepository.GetAsync_";
    public const string KeyISolicitacaoPaged = "Sinapse.SolicitacaoRepository.GetPagedAsync_";
    public const string KeyISolicitacaoPagedCount = "Sinapse.SolicitacaoRepository.GetPagedAsync_Count_";
    public const string KeyISolicitacaoRepository = "ONS.SINapse.Repository.IRepository.ISolicitacaoRepository_";
    public const string KeyIPerfilDeUsuarioBusiness = "ONS.SINapse.Business.Imp.Business.IPerfilDeUsuarioBusiness_";
    public const string KeyICentroDeOperacaoRepository = "ONS.SINapse.Business.Imp.Business.ICentroDeOperacaoRepository_";
    public const string KeyAreaEletrica = "ONS.SINapse.Entities.Entities.AreaEletrica";
    public const string KeyCentroDeOperacao = "ONS.SINapse.Entities.Entities.CentroDeOperacao";
    public const string KeyEquipamento = "ONS.SINapse.Entities.Entities.Equipamento";
    public const string KeyAgente = "ONS.SINapse.Entities.Entities.Agente";
    public const string KeyEstacao = "ONS.SINapse.Entities.Entities.Estacao";
    public const string KeyUsina = "ONS.SINapse.Entities.Entities.Usina";
    public const string KeyConjuntoDeUsina= "ONS.SINapse.Entities.Entities.ConjuntoDeUsina";

    public static string[] GetKeys()
    {
        return [
            KeyIComunicado,
            KeyISolicitacaoPaged,
            KeyISolicitacaoPagedCount,
            KeyISolicitacaoRepository,
            KeyIPerfilDeUsuarioBusiness,
            KeyICentroDeOperacaoRepository,
            KeyAreaEletrica,
            KeyCentroDeOperacao,
            KeyEquipamento,
            KeyAgente,
            KeyEstacao,
            KeyUsina,
            KeyConjuntoDeUsina
        ];
    }
    

    public static string KeyGetAgentesDestinatariosDeSolicitacao(string codigoDoCentro)
    {
        return $"{KeyISolicitacaoRepository}_{codigoDoCentro}";
    }
}
