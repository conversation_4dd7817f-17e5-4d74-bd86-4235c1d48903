namespace ONS.SINapse.Shared.Constants
{
    public static class ColecoesFirebase
    {
        public const string Solicitacao = "c_solicitacao";
        public const string CadastroSolicitacao = "c_cadastrosolicitacao";
        public const string Notificacao = "c_notificacao";
        public const string AlertaSonoro = "c_alertasonoro";
        public const string VisaoUsuario = "c_visaousuario";
        public const string Tag ="c_tag";
        public const string Ra<PERSON><PERSON><PERSON><PERSON> ="c_rascunho";
        public const string ControleDeChamada = "c_controledechamada";
        public const string Versao = "c_versao";
        public const string ApiAgente = "c_apiagente";
        public const string RascunhoFavorito = "c_rascunhofavorito";
        public const string Version = "version";
        public const string ConfiguracaoUsuario = "c_configuracaousuario";
        public const string Root = "";
        
        public static string[] ColecoesPermanentes => [VisaoUsuario, <PERSON>, <PERSON>, <PERSON>e<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Solicitacao, CadastroSolicita<PERSON>o, <PERSON><PERSON><PERSON><PERSON><PERSON>, ConfiguracaoUsuario];
    }
}
