namespace ONS.SINapse.Shared.Constants
{
    public static class Operacoes
    {
        public const string CancelarSolicitacao = nameof(CancelarSolicitacao);
        public const string ConfirmarSolicitacao = nameof(ConfirmarSolicitacao);
        public const string ConsultarComunicado = nameof(ConsultarComunicado);
        public const string ConsultarHistorico = nameof(ConsultarHistorico);
        public const string ConsultarPainelSolicitacoesTempoReal = nameof(ConsultarPainelSolicitacoesTempoReal);
        public const string CriarBroadcast = nameof(CriarBroadcast);
        public const string CriarMulticast = nameof(CriarMulticast);
        public const string CriarSolicitacao = nameof(CriarSolicitacao);
        public const string FinalizarSolicitacao = nameof(FinalizarSolicitacao);
        public const string InformarCienciaImpedimento = nameof(InformarCienciaImpedimento);
        public const string InformarImpedimentoSolicitacao = nameof(InformarImpedimentoSolicitacao);
        public const string IntegracaoCriarSolicitacao = nameof(IntegracaoCriarSolicitacao);
        public const string IntegracaoAgente = nameof(IntegracaoAgente);
        public const string ConsultarHistoricoAcesso = nameof(ConsultarHistoricoAcesso);
        public const string GerenciarVisaoUsuario = nameof(GerenciarVisaoUsuario);
        public const string RegistrarAudiencia = nameof(RegistrarAudiencia);
        public const string ConsultaGeralDoHistorico = nameof(ConsultaGeralDoHistorico);
        public const string ConsultarAgentes = nameof(ConsultarAgentes);
        public const string GerenciarTags = nameof(GerenciarTags);
        public const string CriarRascunhoSolicitacao = nameof(CriarRascunhoSolicitacao);
        public const string RemoverRascunhoSolicitacao = nameof(RemoverRascunhoSolicitacao);
        public const string EditarRascunhoSolicitacao = nameof(EditarRascunhoSolicitacao);
        public const string EnviarMensagemViaChat = nameof(EnviarMensagemViaChat);
        public const string LogarNoSinapse = nameof(LogarNoSinapse);
        public const string SelecionarMultiplosScopes = nameof(SelecionarMultiplosScopes);
        public const string ConsultarTodosAgentes = nameof(ConsultarTodosAgentes);
        public const string AlterarVersaoSistema = nameof(AlterarVersaoSistema);
        public const string Administrar = nameof(Administrar);
        public const string DownloadViews = nameof(DownloadViews);
        public const string AprovarEnvioSolicitacao = nameof(AprovarEnvioSolicitacao);
    }

    public static class Roles
    {
        public const string ApiIntegracao = "API Integração";
        public const string OperadorCentro = "Operador Centros";
        public const string OperadorCnos = "Operador CNOS";
        public const string OperadorAgente = "Operador Agente";
        public const string ApuradorCentros = "Apurador Centros";
        public const string ApuradorAgente = "Apurador Agente";
        public const string AcompanharPainelTempoReal = "Acompanhar painel  de solicitações do Tempo Real";
        public const string Administrador = nameof(Administrador);
    }

    public static class TypeScope
    {
        public const string Centros = "CENTROS";
        public const string Agentes = "AGENTES";
        public const string Ons = "ONS";
    }
}