using ONS.SINapse.Integracao.Shared.Enums;

namespace ONS.SINapse.Shared.Constants;

public static class MensagensPredefinidasDoChat
{   
    public const string SolicitacaoCriada = "Solicitação criada";
    public const string SolicitacaoCancelada = "A solicitação foi cancelada pelo solicitante";
    public const string SolicitacaoConfirmada = "A solicitação foi confirmada";
    public const string SolicitacaoFinalizada = "A solicitação foi finalizada";
    public const string SolicitacaoComCienciaInformada = "Informada a ciência do impedimento";
    private const string SolicitacaoImpedidaPrefixo = "Informado o impedimento para a solicitação:";
    public const string SolicitacaoAguardandoEnvio = "A solicitação está aguardando envio";
    public const string SolicitacaoEnviada = "A solicitação foi enviada";
    public const string SolicitacaoEnvioCancelado = "O envio da solicitação foi cancelado";

    public static string SolicitacaoImpedida(string motivo)
        => $"{SolicitacaoImpedidaPrefixo} {motivo}";
    public static string MensagemDoStatus(StatusDeSolicitacao statusDeSolicitacao, string? motivo = null)
        => statusDeSolicitacao switch
        {
            StatusDeSolicitacao.Pendente => SolicitacaoCriada,
            StatusDeSolicitacao.Cancelada => SolicitacaoCancelada,
            StatusDeSolicitacao.Confirmada => SolicitacaoConfirmada,
            StatusDeSolicitacao.Finalizada => SolicitacaoFinalizada,
            StatusDeSolicitacao.CienciaInformada => SolicitacaoComCienciaInformada,
            StatusDeSolicitacao.Impedida => SolicitacaoImpedida(motivo ?? string.Empty),
            StatusDeSolicitacao.AguardandoEnvio => SolicitacaoAguardandoEnvio,
            StatusDeSolicitacao.Enviada => SolicitacaoEnviada,
            StatusDeSolicitacao.EnvioCancelado => SolicitacaoEnvioCancelado,
            StatusDeSolicitacao.Erro => string.Empty,
            _ => throw new Exception("Status de solicitação não mapeado")
        };
}
