using System.Collections;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;

namespace ONS.SINapse.Shared.Converters;


/// <summary>
/// Utilizado para achatar dados de uma collection no firebase
/// Isso permite o sistema atualizar somente campos especificos da collection no firebase
/// Um PATCH enviado ao firebase no formato json abaixo:
/// {
///     "GSU/usuario/nome": "Qualquer"
/// }
/// Atualizaria a collection agente na chave gsu apenas o campo nome do objeto usuário
/// </summary>
/// <typeparam name="T"></typeparam>
public class FirebaseFlattenObjectConverter<T> : JsonConverter where T : FlattenableJson
{
    private void FlattenObject(object? obj, string currentPath, JsonWriter writer, IContractResolver contractResolver)
    {
        if (obj == null) return;

        var type = obj.GetType();

        // Se for uma coleção, itera sobre os elementos
        if (obj is IEnumerable enumerable and not string)
        {
            var index = 0;
            foreach (var item in enumerable)
            {
                FlattenObject(item, $"{currentPath}/{index}", writer, contractResolver);
                index++;
            }
        }
        else if (IsSimpleType(type)) // Tipos simples
        {
            writer.WritePropertyName(currentPath.TrimStart('/'));
            writer.WriteValue(obj);
        }
        else // Tipos complexos
        {
            ProcessComplexTypes(obj, currentPath, writer, contractResolver);
        }
    }

    private void ProcessComplexTypes(object obj, string currentPath, JsonWriter writer, IContractResolver contractResolver) 
    {
        var type = obj.GetType();
        var contract = (JsonObjectContract)contractResolver.ResolveContract(type);
        foreach (var property in contract.Properties.OrderBy(x => x.Order ?? 0))
        {
            if (!property.Readable || property.Ignored) continue;

            var propertyValue = property.ValueProvider?.GetValue(obj);
            var pathIdProvider = PropertyPathIdProvider(currentPath, property, propertyValue, out currentPath);

            if (pathIdProvider.Ignored) continue;

            FlattenObject(propertyValue, pathIdProvider.CurrentPath, writer, contractResolver);
        }
    }

    private static PathIdProvider PropertyPathIdProvider(string currentPath, JsonProperty? property, object? propertyValue, out string newCurrentPath)
    {
        newCurrentPath = currentPath;
        
        if (property is null)
            return new PathIdProvider(currentPath, false);
        
        var pathIdAttribute = (JsonFlattenPathIdAttribute?)property.AttributeProvider?.GetAttributes(typeof(JsonFlattenPathIdAttribute), false).FirstOrDefault();
        
        var isPathId = pathIdAttribute is not null;
        
        string fieldPath;
        
        if (!isPathId)
        {
            fieldPath = string.IsNullOrEmpty(currentPath)
                ? property.PropertyName ?? string.Empty
                : $"{currentPath}/{property.PropertyName}";
            
            return new PathIdProvider(fieldPath, false);
        } 
        
        if(string.IsNullOrEmpty(propertyValue?.ToString())) 
        {
            fieldPath = string.IsNullOrEmpty(currentPath)
                ? property.PropertyName ?? string.Empty
                : $"{currentPath}/{property.PropertyName}";
            
            return new PathIdProvider(fieldPath, false);
        }
        
        newCurrentPath += $"/{propertyValue}";
        return new PathIdProvider(newCurrentPath, pathIdAttribute!.Ignored);
    }
    
    private static bool IsSimpleType(Type type)
    {
        return type.IsPrimitive || type.IsEnum || type == typeof(string) || type == typeof(decimal) || type == typeof(DateTime) || type == typeof(DateTime?);
    }

    public override void WriteJson(JsonWriter writer, object? value, JsonSerializer serializer)
    {
        switch (value)
        {
            case null:
                return;
            case IEnumerable enumerable:
                var chats = enumerable.OfType<T>().ToList();
                
                if (chats.Count == 0) return;                
                
                writer.WriteStartObject();
                Convert(writer, chats, serializer);
                writer.WriteEndObject();
                
                break;
            case T solicitacao:
                
                writer.WriteStartObject();
                Convert(writer, solicitacao, serializer);
                writer.WriteEndObject();
                
                break;
            default: return;
        }
    }
    
    private void Convert(JsonWriter writer, List<T> value, JsonSerializer serializer)
    {
        foreach (var chatDeSolicitacaoFirebaseDto in value)
        {
            Convert(writer, chatDeSolicitacaoFirebaseDto, serializer);
        }
    }

    private void Convert(JsonWriter writer, T value, JsonSerializer serializer)
    {
        FlattenObject(value, string.Empty, writer, serializer.ContractResolver);
    }
    
    public override object? ReadJson(JsonReader reader, Type objectType, object? existingValue, JsonSerializer serializer)
    {
        throw new NotImplementedException();
    }

    public override bool CanConvert(Type objectType)
    {
        return
            typeof(IEnumerable<T>).IsAssignableFrom(objectType) ||
            typeof(T).IsAssignableFrom(objectType);
    }

    public override bool CanRead => false;
    
    private sealed record PathIdProvider(string CurrentPath, bool Ignored);
}

/// <summary>
/// Usado para informar que a classe/dto será achatada e tendo cada um dos seus campos atualizado individualmente
/// </summary>
public abstract class FlattenableJson
{
    protected FlattenableJson(string pathId)
    {
        PathId = pathId;
    }
    
    
    /// <summary>
    /// Header do caminho em questão, valor que indica qual objeto da collection será atualizado.
    /// </summary>
    [JsonFlattenPathId]
    [JsonProperty(Order = -1)]
    public string PathId { get; }
}

[AttributeUsage(AttributeTargets.Field | AttributeTargets.Property)]
public class JsonFlattenPathIdAttribute : Attribute
{
    /// <summary>
    /// Indica que o campo será tratado como um header de objeto durante a serialização com o Flatten converter.
    /// Exemplo de classe:
    /// <code>
    /// public class Teste
    /// {
    ///     public string Id { get; set; }
    ///     public string Nome { get; set; }
    /// }
    /// </code>
    /// Ao serializar com Flatten converter, o resultado será algo como:
    /// <code>
    /// {
    ///     "xpto/id": "valor-do-id",
    ///     "xpto/nome": "nome-do-cara"
    /// }
    /// </code>
    /// Com este atributo, o valor do campo especificado será usado como prefixo no caminho, alterando o resultado para:
    /// <code>
    /// {
    ///     "xpto/valor-do-id/nome": "nome-do-cara"
    /// }
    /// </code>
    /// </summary>
    /// <param name="ignored">
    /// Define como o campo será tratado:
    /// - **true**: o campo será ignorado no caminho, e apenas o valor será exibido no prefixo.
    ///   Exemplo:
    ///   <code>
    ///   {
    ///       "xpto/valor-do-id/nome": "nome-do-cara"
    ///   }
    ///   </code>
    /// - **false**: o campo será incluído no caminho com seu valor.
    ///   Exemplo:
    ///   <code>
    ///   {
    ///       "xpto/valor-do-id/id": "valor-do-id",
    ///       "xpto/valor-do-id/nome": "nome-do-cara"
    ///   }
    ///   </code>
    /// </param>
    public JsonFlattenPathIdAttribute(bool ignored = true)
    {
        Ignored = ignored;
    }

    public bool Ignored { get; }
}