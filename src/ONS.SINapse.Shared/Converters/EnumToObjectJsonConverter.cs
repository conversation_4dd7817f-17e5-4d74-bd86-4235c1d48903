using System.Text.Json;
using System.Text.Json.Serialization;
using ONS.SINapse.Shared.Extensions;

namespace ONS.SINapse.Shared.Converters;

public class EnumToObjectJsonConverter<TEnum> : JsonConverter<TEnum> where TEnum : struct, Enum
{
    public override TEnum Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
    {
        using var doc = JsonDocument.ParseValue(ref reader);
        var root = doc.RootElement;

        if (!root.TryGetProperty("id", out var idElement))
            throw new JsonException($"Erro ao desserializar {typeof(TEnum).Name}");
        
        var codigo = idElement.GetInt32();
        
        if (Enum.IsDefined(typeof(TEnum), codigo))
            return (TEnum)Enum.ToObject(typeof(TEnum), codigo);

        throw new JsonException($"Erro ao desserializar {typeof(TEnum).Name}");
    }

    public override void Write(Utf8JsonWriter writer, TEnum value, JsonSerializerOptions options)
    {
        writer.WriteStartObject();
        writer.WriteNumber("codigo", Convert.ToInt32(value));
        writer.WriteString("descricao", value.GetDescription());
        writer.WriteEndObject();
    }
}