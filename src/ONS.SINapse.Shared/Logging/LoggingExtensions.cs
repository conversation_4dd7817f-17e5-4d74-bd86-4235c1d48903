using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.Hosting;
using Serilog;
using Serilog.Events;

namespace ONS.SINapse.Shared.Logging;

public static class LoggingExtensions
{
    private static readonly string[] paths = ["swagger", "keep-alive"];
    public static IHostBuilder UseLogging(this IHostBuilder webHostBuilder)
        => webHostBuilder
            .UseSerilog((context, configuration) =>
                configuration.ReadFrom.Configuration(context.Configuration));

    public static IApplicationBuilder UseCustomRequestLog(this IApplicationBuilder applicationBuilder)
    {
        return applicationBuilder
            .UseSerilogRequestLogging(options =>
            {
                options.GetLevel = (httpContext, _, _) =>
                {
                    var ignorePaths = paths;
                    var path = httpContext.Request.Path.ToString();

                    var existsPathsToIgnore = ignorePaths.Any(ignorePath => path.Contains(ignorePath));
                    if (existsPathsToIgnore) return LogEventLevel.Debug;

                    var statusCode = httpContext.Response.StatusCode;
                    var methodOptions =
                        httpContext.Request.Method.Equals("options", StringComparison.OrdinalIgnoreCase);

                    if (methodOptions) return LogEventLevel.Debug;

                    return statusCode switch
                    {
                        >= 500 => LogEventLevel.Fatal,
                        >= 400 => LogEventLevel.Error,
                        >= 300 => LogEventLevel.Warning,
                        >= 200 => LogEventLevel.Debug,
                        _ => LogEventLevel.Debug
                    };
                };

                options.EnrichDiagnosticContext = (diagnosticContext, httpContext) =>
                {
                    diagnosticContext.Set("Host", httpContext.Request.Host.Value);
                    diagnosticContext.Set("Scheme", httpContext.Request.Scheme);
                    if (httpContext.Response.StatusCode >= 300)
                    {
                        diagnosticContext.Set("Headers", httpContext.Request.Headers);
                    }
                };
            });
    }
}