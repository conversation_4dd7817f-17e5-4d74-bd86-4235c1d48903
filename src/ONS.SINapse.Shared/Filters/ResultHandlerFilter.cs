using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using ONS.SINapse.Shared.DTO;
using System.Net;

namespace ONS.SINapse.Shared.Filters;

public class ResultHandlerFilter : IAsyncResultFilter
{
    public async Task OnResultExecutionAsync(ResultExecutingContext context, ResultExecutionDelegate next)
    {
        var result = context.Result;
        if (result is OkObjectResult jsonResult)
        {
            var body = jsonResult.Value;
            var statusCode = body is null
                ? (int)HttpStatusCode.NoContent
                : (int)HttpStatusCode.OK;

            var response = new BaseResponse<object>(body!, true, statusCode);

            await context.HttpContext.Response.WriteAsJsonAsync(response);
            return;
        }

        await next();
    }
}