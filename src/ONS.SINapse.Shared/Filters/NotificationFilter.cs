using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using System.Net;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Notifications;

namespace ONS.SINapse.Shared.Filters;

public class NotificationFilter : IAsyncResultFilter
{
    private readonly NotificationContext _notificationContext;

    public NotificationFilter(NotificationContext notificationContext)
    {
        _notificationContext = notificationContext;
    }

    public async Task OnResultExecutionAsync(ResultExecutingContext context, ResultExecutionDelegate next)
    {
        if (_notificationContext.HasNotifications)
        {
            var statusCode = (int)HttpStatusCode.OK;
            context.HttpContext.Response.StatusCode = statusCode;
            context.HttpContext.Response.ContentType = "application/json";

            var result = context.Result as ObjectResult;
            var messages = _notificationContext.Notifications.Select(n => n.Message);
            var response = new BaseResponse<object>(result?.Value ?? new {}, false, statusCode, messages);
            var json = JsonConvert.SerializeObject(response,
                new JsonSerializerSettings 
                { 
                    ContractResolver = new CamelCasePropertyNamesContractResolver() 
                });

            await context.HttpContext.Response.WriteAsync(json);

            return;
        }

        await next();
    }
}
