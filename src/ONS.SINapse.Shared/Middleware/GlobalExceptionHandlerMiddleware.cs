using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using ONS.SINapse.Shared.DTO;
using System.Net;

namespace ONS.SINapse.Shared.Middleware
{
    public class GlobalExceptionHandlerMiddleware : IMiddleware
    {
        private readonly ILogger<GlobalExceptionHandlerMiddleware> _logger;

        public GlobalExceptionHandlerMiddleware(ILogger<GlobalExceptionHandlerMiddleware> logger)
        {
            _logger = logger;
        }

        public async Task InvokeAsync(HttpContext context, RequestDelegate next)
        {
            try
            {
                await next(context);
            }
            catch (Exception ex)
            {
                _logger.LogCritical(ex, "[EXCEPTION] {Message} (InnerException: {InnerException})", 
                    ex.Message, ex.InnerException);
                await HandleExceptionAsync(context, ex);
            }
        }

        private static async Task<Task> HandleExceptionAsync(HttpContext context, Exception exception)
        {
            var statusCode = HttpStatusCode.InternalServerError;

            IEnumerable<string> mensagens;

            if (exception is Refit.ApiException apiEx)
            {
                statusCode = apiEx.StatusCode;
                var conteudo = await apiEx.GetContentAsAsync<dynamic>();
                string detalhe = conteudo?.detail ?? apiEx.Message;

                mensagens = [detalhe];
            }
            else
            {
                mensagens = [exception.InnerException?.Message ?? exception.Message];
            }

            context.Response.ContentType = "application/json";
            context.Response.StatusCode = (int) statusCode;

            var response = new BaseResponse<object>(exception, false, (int)statusCode, mensagens);
            var json = JsonConvert.SerializeObject(response,
                new JsonSerializerSettings
                {
                    ContractResolver = new CamelCasePropertyNamesContractResolver(),
                    ReferenceLoopHandling = ReferenceLoopHandling.Ignore
                });

            return context.Response.WriteAsync(json);
        }
    }
}