using Microsoft.AspNetCore.Http;

namespace ONS.SINapse.Shared.Middleware
{
    public class OptionsMiddleware : IMiddleware
    {
        private static readonly string[] headers = ["text/plain"];
        public async Task InvokeAsync(HttpContext context, RequestDelegate next)
        {
            if (context.Request.Method == "OPTIONS")
            {
                context.Response.Headers.Append("Content-Type", headers);
                context.Response.StatusCode = 204;
            }
            else
            {
                await next(context);
            }
        }
    }
}