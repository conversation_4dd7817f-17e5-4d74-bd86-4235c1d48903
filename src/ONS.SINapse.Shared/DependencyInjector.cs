using Microsoft.Extensions.DependencyInjection;
using ONS.SINapse.Integracao.Shared.Settings;
using Microsoft.Extensions.Configuration;
using ONS.SINapse.Integracao.Shared.Helpers;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Shared.Notifications;
using ONS.SINapse.Shared.Services;
using ONS.SINapse.Shared.Services.Caching;
using ONS.SINapse.Shared.Settings;
using StackExchange.Redis;

namespace ONS.SINapse.Shared;

public static class DependencyInjector
{
    public static IServiceCollection RegisterSharedLayer(this IServiceCollection services, IConfiguration configuration)
    {
        RegisterNotification(services);
        RegisterSettings(services, configuration);
        RegisterCaching(services, configuration);
        RegisterMediator(services);
        services.AddScoped<IUsuarioJwtHelper, UsuarioJwtHelper>();
        services.AddSingleton<ICorretorOrtograficoService, CorretorOrtograficoService>();
        return services;
    }

    private static void RegisterCaching(IServiceCollection services, IConfiguration configuration)
    {
        services.AddDistributedMemoryCache();
        services.AddSingleton<ICacheService, DistributedCacheService>();

        var cacheSettings = configuration.GetSection(nameof(CacheSettings)).Get<CacheSettings>();
        ArgumentNullException.ThrowIfNull(cacheSettings);

        if (cacheSettings.UseRedis)
        {
            services.AddStackExchangeRedisCache(options =>

            {
                var configurationOptions = ConfigurationOptions.Parse($"{cacheSettings.Host}:{cacheSettings.Port},password={cacheSettings.Password},abortConnect=false");

                configurationOptions.ReconnectRetryPolicy =
                    new
                 ExponentialRetry(5000);

                configurationOptions.ConnectRetry = 3;
                configurationOptions.AbortOnConnectFail = false;
                options.InstanceName = cacheSettings.InstanceName;
                options.ConfigurationOptions = configurationOptions;
            });
        }
    }
    private static void RegisterSettings(IServiceCollection services, IConfiguration configuration)
    {
        services.Configure<ConfiguracoesPopAuthSettings>(configuration.GetSection("ONS:PopAuth"));
        services.Configure<AuthorizationSettings>(configuration.GetSection("ONS:Authorization"));
        services.Configure<MongoDbSettings>(configuration.GetSection(nameof(MongoDbSettings)));
        services.Configure<CacheSettings>(configuration.GetSection(nameof(CacheSettings)));
        services.Configure<FirebaseSettings>(configuration.GetSection(nameof(FirebaseSettings)));
        services.Configure<ExtracaoDeDadoSettings>(configuration.GetSection(nameof(ExtracaoDeDadoSettings)));
        services.Configure<TemplatesSettings>(configuration.GetSection(nameof(TemplatesSettings)));
        services.Configure<ApplicationSettings>(configuration.GetSection(nameof(ApplicationSettings)));
        services.Configure<ConfiguracaoDeSistemaSettings>(configuration.GetSection(nameof(ConfiguracaoDeSistemaSettings)));
        services.Configure<UsuarioDoSistemaSettings>(configuration.GetSection(nameof(UsuarioDoSistemaSettings)));
        services.Configure<ConfiguracaoDeNotificacaoSettings>(configuration.GetSection(nameof(ConfiguracaoDeNotificacaoSettings)));
        services.Configure<VerificacaoDeGeolocalizacaoSettings>(configuration.GetSection(nameof(VerificacaoDeGeolocalizacaoSettings)));
        services.Configure<KafkaSettings>(configuration.GetSection(nameof(KafkaSettings)));
        services.Configure<KafkaTopicsSettings>(configuration.GetSection(nameof(KafkaTopicsSettings)));
        services.Configure<XTokenRequirementSettings>(configuration.GetSection(nameof(XTokenRequirementSettings)));
        services.Configure<DataSyncSettings>(configuration.GetSection(nameof(DataSyncSettings)));
        services.Configure<ComunicadoSettings>(configuration.GetSection(nameof(ComunicadoSettings)));
        services.Configure<ControleDeChamadaSettings>(configuration.GetSection(nameof(ControleDeChamadaSettings)));
        services.Configure<SqsQueueSettings>(configuration.GetSection(nameof(SqsQueueSettings)));
        services.Configure<AwsSettings>(configuration.GetSection("AWS"));
    }

    private static void RegisterNotification(IServiceCollection services)
    {
        services.AddScoped<NotificationContext>();
    }

    private static void RegisterMediator(IServiceCollection services)
    {
        services.AddScoped<IMediatorHandler, MediatorHandler>();
    }
}