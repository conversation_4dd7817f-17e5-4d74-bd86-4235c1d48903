using MassTransit;
using MassTransit.Mediator;
using Microsoft.Extensions.Logging;
using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Shared.Messages;
using ONS.SINapse.Shared.Notifications;
using Event = ONS.SINapse.Shared.Messages.Event;
using ValidationResult = FluentValidation.Results.ValidationResult;

namespace ONS.SINapse.Shared.Mediator
{
    public class MediatorHandler : IMediatorHandler
    {
        private readonly IMediator _mediator;
        private readonly ILogger<MediatorHandler> _logger;
        private readonly NotificationContext _notificationContext;

        public MediatorHandler(IMediator mediator, ILogger<MediatorHandler> logger, NotificationContext notificationContext)
        {
            _mediator = mediator;
            _logger = logger;
            _notificationContext = notificationContext;
        }

        public async Task<ValidationResult> EnviarComandoAsync<T>(T comando, CancellationToken cancellationToken) where T : Command
        {
            _logger.LogInformation("Enviando Comando {MessageType}", comando.MessageType);

            var result = await _mediator.SendRequest(comando, cancellationToken);

            if (!comando.ValidationResult.IsValid)
                result.AdicionarErro(comando.ValidationResult);
            
            _logger.LogInformation("Comando {MessageType} Enviado e finalizado Result Messages: {Result}", comando.MessageType,
                result
                    .Errors
                    .Select(failure => $"{failure.Severity} - {failure.ErrorMessage}"));
            
            if(!result.IsValid)
                _notificationContext.AddNotifications(result);
            
            return result;
        }

        public async Task<TResult> EnviarComandoAsync<TCommand, TResult>(TCommand comando, CancellationToken cancellationToken)
            where TCommand : Command<TResult>
            where TResult : CommandResult
        {
            _logger.LogInformation("Enviando Comando {MessageType}", comando.MessageType);

            var result = await _mediator.SendRequest(comando, cancellationToken);

            if (!comando.ValidationResult.IsValid)
                result.ValidationResult.AdicionarErro(comando.ValidationResult);
            
            _logger.LogInformation("Comando {MessageType} Enviado e finalizado Result Messages: {Result}", comando.MessageType,
                result
                    .ValidationResult
                    .Errors
                    .Select(failure => $"{failure.Severity} - {failure.ErrorMessage}"));
            
            if(!result.ValidationResult.IsValid)
                _notificationContext.AddNotifications(result.ValidationResult);
            
            return result;
        }
        
        
        public async Task PublicarEventoAsync<T>(T evento, CancellationToken cancellationToken) where T : Event
        {
            _logger.LogInformation("Publicando Evento {MessageType}", evento.MessageType);
            
            await _mediator.Publish(evento, cancellationToken);
            
            _logger.LogInformation("Evento {MessageType} Publicado.", evento.MessageType);
        }
        
        public async Task<TResult?> BuscarDadosAsync<TResult>(Query<TResult> query, CancellationToken cancellationToken)
        {
            var typeName = typeof(TResult).Name;
            _logger.LogInformation("Buscando dados {TypeName}", typeName);

            var result = await _mediator.SendRequest(query, cancellationToken);
            
            _logger.LogInformation("Dados retornados {TypeName}", typeName);

            if (result.ValidationResult.IsValid && result.Result is not null) return result.Result;

            if (!result.ValidationResult.IsValid)
            {
                _logger.LogInformation("Dados retornados com erro: {Result}", result
                    .ValidationResult
                    .Errors
                    .Select(failure => $"{failure.Severity} - {failure.ErrorMessage}"));
                
                _notificationContext.AddNotifications(result.ValidationResult);
            }
            
            if(result.Result is not null) return result.Result;
            
            _logger.LogInformation("Nenhum registro encontrado {TypeName}", typeName);
            _notificationContext.AddNotification("Nenhum registro encontrado.");

            return result.Result;
        }
    }
}