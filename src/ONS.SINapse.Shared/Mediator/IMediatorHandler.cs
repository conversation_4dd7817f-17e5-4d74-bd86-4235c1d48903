using FluentValidation.Results;
using ONS.SINapse.Shared.Messages;

namespace ONS.SINapse.Shared.Mediator
{
    public interface IMediatorHandler
    {
        Task PublicarEventoAsync<T>(T evento, CancellationToken cancellationToken) where T : Event;
        Task<ValidationResult> EnviarComandoAsync<T>(T comando, CancellationToken cancellationToken) where T : Command;

        Task<TResult> EnviarComandoAsync<TCommand, TResult>(TCommand comando, CancellationToken cancellationToken)
            where TCommand : Command<TResult>
            where TResult : CommandResult;
        
        Task<TResult?> BuscarDadosAsync<TResult>(Query<TResult> query, CancellationToken cancellationToken);
    }
}