using Microsoft.Extensions.Caching.Distributed;
using Newtonsoft.Json;
using System.Collections.Concurrent;

namespace ONS.SINapse.Shared.Services.Caching;

public class DistributedCacheService : ICacheService
{
    private readonly IDistributedCache _distributedCache;
    private static readonly ConcurrentDictionary<string, bool> CacheKeys = new();

    public DistributedCacheService(IDistributedCache distributedCache)
    {
        _distributedCache = distributedCache;
    }

    public async Task<T?> GetAsync<T>(string key, CancellationToken cancellationToken = default) 
        where T : class
    {
        var cachedValue = await _distributedCache.GetStringAsync(key, cancellationToken);

        if (cachedValue is null) return null;

        var value = JsonConvert.DeserializeObject<T>(cachedValue!);

        return value;
    }

    public Task SetAsync<T>(string key, T value, CancellationToken cancellationToken = default) 
        where T : class =>
        SetValueAsync(key, value, cancellationToken: cancellationToken);

    public Task SetAsync<T>(string key, T value, TimeSpan expiration, CancellationToken cancellationToken = default) 
        where T : class =>
        SetValueAsync(key, value, expiration, cancellationToken);
    
    public Task SetAsync<T>(string key, T value, DateTimeOffset expiration, CancellationToken cancellationToken = default) 
        where T : class =>
        SetValueAsync(key, value, expiration, cancellationToken);

    public Task SetAsync<T>(string key, T value, int seconds, CancellationToken cancellationToken = default) 
        where T : class =>
        SetValueAsync(key, value, TimeSpan.FromSeconds(seconds), cancellationToken);

    private async Task RemoveAsync(string key, CancellationToken cancellationToken = default)
    {   
        await _distributedCache.RemoveAsync(key, cancellationToken);
        
        CacheKeys.TryRemove(key, out _);
    }

    public Task RemoveByPrefixAsync(string prefixKey, CancellationToken cancellationToken = default)
    {   
        var tasks = CacheKeys.Keys
            .Where(key => key.StartsWith(prefixKey))
            .Select(key => RemoveAsync(key, cancellationToken));

        return Task.WhenAll(tasks);
    }
    
    private Task SetValueAsync<T>(string key, T value, TimeSpan expiration, CancellationToken cancellationToken = default) 
        where T : class
    {
        DistributedCacheEntryOptions options = new()
        {
            AbsoluteExpirationRelativeToNow = expiration
        };

        return SetValueAsync(key, value, options, cancellationToken);
    }
    
    private Task SetValueAsync<T>(string key, T value, DateTimeOffset expiration, CancellationToken cancellationToken = default) 
        where T : class
    {
        DistributedCacheEntryOptions options = new()
        {
            AbsoluteExpiration = expiration
        };

        return SetValueAsync(key, value, options, cancellationToken);
    }
    
    private async Task SetValueAsync<T>(string key, T value, DistributedCacheEntryOptions? options = null, CancellationToken cancellationToken = default) 
        where T : class
    {
        options ??= new DistributedCacheEntryOptions();

        var cachedValue = JsonConvert.SerializeObject(value);
        await _distributedCache.SetStringAsync(key, cachedValue, options, cancellationToken);

        CacheKeys.TryAdd(key, true);
    }
}