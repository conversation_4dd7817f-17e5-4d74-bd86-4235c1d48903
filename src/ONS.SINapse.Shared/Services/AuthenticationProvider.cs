using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Services.Caching;
using ONS.SINapse.Shared.Settings;
using Refit;

namespace ONS.SINapse.Shared.Services;

public interface IAuthenticationProvider<out TOptions> where TOptions : ApiOptions
{
    TOptions ObterApiOptions();
    Task<PopAutorizacaoTokenDto> ObterToken();
}

internal class AuthenticationProvider<TOptions> : IAuthenticationProvider<TOptions> where TOptions : ApiOptions
{
    private readonly IAuthenticationService<TOptions> _authenticationService;
    private readonly TOptions _apiOptions;
    
    public AuthenticationProvider(IAuthenticationService<TOptions> authenticationService, TOptions apiOptions)
    {
        _authenticationService = authenticationService;
        _apiOptions = apiOptions ?? throw new ArgumentNullException(nameof(apiOptions), "Erro de configuração de api.");
    }
    
    public TOptions ObterApiOptions() => _apiOptions;

    public Task<PopAutorizacaoTokenDto> ObterToken()
    {
        if(_apiOptions.Authentication is null) throw new InvalidOperationException("Erro de configuração de autenticação da api.");
        
        var form = new Dictionary<string, object>
        {
            { "client_id", _apiOptions.Authentication.ApplicationName },
            { "grant_type", "password" },
            { "username", _apiOptions.Authentication.Username },
            { "password", _apiOptions.Authentication.Password }
        };
        
        return _authenticationService.ObterToken(form, _apiOptions.Authentication.ApplicationOrigin);
    }
}

internal class AuthenticationCacheProvider<TOptions> : IAuthenticationProvider<TOptions> where TOptions : ApiOptions
{
    private readonly IAuthenticationProvider<TOptions> _inner;
    private readonly ICacheService _cacheService;

    public AuthenticationCacheProvider(IAuthenticationProvider<TOptions> inner, ICacheService cacheService)
    {
        _inner = inner;
        _cacheService = cacheService;
    }

    public TOptions ObterApiOptions() => _inner.ObterApiOptions();
    
    public async Task<PopAutorizacaoTokenDto> ObterToken()
    {
        var apiOptions = _inner.ObterApiOptions();
        var keyCache = $"token: {apiOptions.Authentication.ApplicationName}";

        var token = await _cacheService.GetAsync<PopAutorizacaoTokenDto>(keyCache);

        if (token is not null)
            return token;

        token = await _inner.ObterToken();
        
        await _cacheService.SetAsync(keyCache, token, TimeSpan.FromSeconds(apiOptions.Authentication.ApplicationCacheExpiration));

        return token;
    }
}

//TOptions deve ser mantido para identificar qual service pertence a qual serviço de autenticação (AuthenticationProvider).
internal interface IAuthenticationService<TOptions> where TOptions : ApiOptions
{
    [Post("")]
    Task<PopAutorizacaoTokenDto> ObterToken([Body(BodySerializationMethod.UrlEncoded)] Dictionary<string, object> form, [Header("Origin")] string header);
    public Type GetTypeIntegration() => typeof(TOptions);
}
