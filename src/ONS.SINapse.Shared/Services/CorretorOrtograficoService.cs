using HunspellSharp;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using ONS.SINapse.Shared.Settings;

namespace ONS.SINapse.Shared.Services;

public interface ICorretorOrtograficoService : IDisposable
{
    string CorrigirOrtografia(string texto);
}
public sealed class CorretorOrtograficoService : ICorretorOrtograficoService
{
    private readonly Hunspell _hunspell;
    private bool _disposed;
    private readonly Dictionary<string, string> _dicionario;

    public CorretorOrtograficoService(IOptions<TemplatesSettings> templatesSettingsOptions)
    {
        _dicionario = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase);
        _hunspell = Init(templatesSettingsOptions.Value);
    }

    private Hunspell Init(TemplatesSettings settings)
    {
        var diretorioDosTemplates = settings.DiretorioDosTemplates;
        var affFilePath = Path.Combine(diretorioDosTemplates, "pt_BR.aff");
        var dicFilePath = Path.Combine(diretorioDosTemplates, "pt_BR.dic");
        var hunspell = new Hunspell(affFilePath, dicFilePath);
        
        var arquivoDicionario = Path.Combine(diretorioDosTemplates, settings.NomeDoArquivoDeDicionario);
        
        if (!File.Exists(arquivoDicionario)) return hunspell;
        
        var json = File.ReadAllText(arquivoDicionario);
        var dicionario = JsonConvert.DeserializeObject<Dictionary<string, string>>(json) ?? new Dictionary<string, string>();
        foreach (var (key, value) in dicionario)
        {
            _dicionario[key.Trim()] = value.Trim();
        }

        return hunspell;
    }


    public string CorrigirOrtografia(string texto)
    {
        if (string.IsNullOrEmpty(texto))
        {
            return texto;
        }

        var palavras = texto.Split(' ');
        for (var i = 0; i < palavras.Length; i++)
        {
            var palavra = palavras[i];
            
            if (_dicionario.TryGetValue(palavra, out var palavraCorrigida))
            {
                palavras[i] = palavraCorrigida;
                continue;
            }

            if (_hunspell.Spell(palavra)) continue;
            
            var segestoes = _hunspell.Suggest(palavra);
            if (segestoes.Count > 0)
            {
                palavras[i] = segestoes[0];
            }
        }

        return string.Join(' ', palavras);
    }

    private void Dispose(bool disposing)
    {
        if (_disposed) return;
        if (disposing)
        {
            _hunspell?.Dispose();
        }

        _disposed = true;
    }

    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }
}