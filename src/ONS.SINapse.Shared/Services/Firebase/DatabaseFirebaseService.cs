using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using ONS.SINapse.Shared.Constants;
using ONS.SINapse.Shared.Services.Firebase.Base;
using ONS.SINapse.Shared.Settings;

namespace ONS.SINapse.Shared.Services.Firebase;

public interface IDatabaseFirebaseService
{
    Task VersionHandler(string sid, CancellationToken cancellationToken);
}

public class DatabaseFirebaseService : FirebaseDatabaseService, IDatabaseFirebaseService
{
    private const string VerificandoVersao = "[FIREBASE|RESET] Verificando a versão do firebase.";
    private const string VersaoAtualEDesejada = "[FIREBASE|RESET] Versão atual: {CurrentVersion}; Versão desejada: {Version}.";
    private const string ResetIniciado = "[FIREBASE|RESET] Iniciando reset das collections do firebase.";
    private const string ResetConcluido = "[FIREBASE|RESET] Reset das collections do firebase concluído.";
    private const string ResetComErro = "[FIREBASE|RESET] Não foi possível fazer o reset das collections do firebase";

    private readonly ILogger<DatabaseFirebaseService> _logger;
    private readonly ApplicationSettings _applicationSettings;

    public DatabaseFirebaseService(IOptions<FirebaseSettings> firebaseSettings,
        IFirebaseAuthService firebaseAuthService, 
        IHttpClientFactory httpClientFactory,
        ILogger<DatabaseFirebaseService> logger,
        IOptions<ApplicationSettings> applicationSettings
    ) : base(firebaseSettings, firebaseAuthService, httpClientFactory)
    {
        _logger = logger;
        _applicationSettings = applicationSettings.Value;
    }

    public async Task VersionHandler(string sid, CancellationToken cancellationToken)
    {
        try
        {
            string version = _applicationSettings.Version;

            _logger.LogInformation(VerificandoVersao);
            string[] path = { PropriedadesDoFirebase.Version };
            var currentVersion = await GetAsync<string>(sid, path, cancellationToken);

            _logger.LogInformation(VersaoAtualEDesejada, currentVersion, version);
            if (!string.IsNullOrEmpty(currentVersion) && currentVersion.Equals(version)) return;

            _logger.LogInformation(ResetIniciado);
            IReadOnlyCollection<string> collectionNames = await GetAsync(sid, cancellationToken);
            IEnumerable<Task> dropTasks = collectionNames
                .Except(ColecoesFirebase.ColecoesPermanentes)
                .Select(collectionName => DropAsync(collectionName, sid, cancellationToken));

            await Task.WhenAll(dropTasks);
            await SetAsync(PropriedadesDoFirebase.Version, version, sid, cancellationToken);
            _logger.LogInformation(ResetConcluido);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, ResetComErro);
        }
    }
}
