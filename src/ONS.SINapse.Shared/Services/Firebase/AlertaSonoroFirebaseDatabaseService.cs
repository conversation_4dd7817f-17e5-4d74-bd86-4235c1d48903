using Firebase.Database;
using Firebase.Database.Query;
using ONS.SINapse.Shared.Constants;
using ONS.SINapse.Shared.DTO.Firebase;

namespace ONS.SINapse.Shared.Services.Firebase;

public interface IAlertaSonoroFirebaseDatabaseService
{
    Task SetAsync(IDictionary<string, AlertaSonoroRealtimeDto> document, CancellationToken cancellationToken);
}

public class AlertaSonoroFirebaseDatabaseService : IAlertaSonoroFirebaseDatabaseService
{
    private readonly ChildQuery _firebaseCollection;
    private const string CollectionName = ColecoesFirebase.AlertaSonoro;
    
    public AlertaSonoroFirebaseDatabaseService(FirebaseClient firebaseClient)
    {
        _firebaseCollection = firebaseClient.Child(CollectionName);
    }
    
    public Task SetAsync(IDictionary<string, AlertaSonoroRealtimeDto> document, CancellationToken cancellationToken)
    {
        return _firebaseCollection
            .PatchAsync(document)
            .WaitAsync(cancellationToken);
    }
}