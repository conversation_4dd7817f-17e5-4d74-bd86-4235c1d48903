using Firebase.Database;
using Firebase.Database.Query;
using ONS.SINapse.Shared.Constants;
using ONS.SINapse.Shared.DTO.Firebase;

namespace ONS.SINapse.Shared.Services.Firebase;
public interface IControleDeChamadaRealtimeService
{
    Task AddUpdateAsync(ControleDeChamadaRealtimeDto document, CancellationToken cancellationToken);
}

public class ControleDeChamadaRealtimeService : IControleDeChamadaRealtimeService
{
    private readonly ChildQuery _firebaseCollection;

    public ControleDeChamadaRealtimeService(FirebaseClient firebaseClient)
    {
        _firebaseCollection = firebaseClient.Child(ColecoesFirebase.ControleDeChamada);
    }

    public Task AddUpdateAsync(ControleDeChamadaRealtimeDto document, CancellationToken cancellationToken)
    {
        return _firebaseCollection
            .PutAsync(document)
            .WaitAsync(cancellationToken);
    }
}