using System.IdentityModel.Tokens.Jwt;
using IdentityModel;

namespace ONS.SINapse.Shared.Services.Firebase;

public record FirebaseSinapseCustomToken(string AccessToken)
{
    public long Expiration => InternalExpiration();
    public long IssuedAt => InternalIssued();
    
    public DateTimeOffset CreatedAt => DateTimeOffset.FromUnixTimeSeconds(IssuedAt);
    public DateTimeOffset ExpiredAt => DateTimeOffset.FromUnixTimeSeconds(Expiration);

    private JwtSecurityToken JwtSecurityToken => new(AccessToken);
    private long InternalExpiration()
        => Convert.ToInt64(JwtSecurityToken.Claims.First(claim => claim.Type == JwtClaimTypes.Expiration).Value);

    private long InternalIssued()
        => Convert.ToInt64(JwtSecurityToken.Claims.First(claim => claim.Type == JwtClaimTypes.IssuedAt).Value);

}