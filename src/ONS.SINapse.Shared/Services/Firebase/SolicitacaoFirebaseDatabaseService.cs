using Microsoft.Extensions.Options;
using ONS.SINapse.Shared.Constants;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.DTO.Solicitacao;
using ONS.SINapse.Shared.Services.Firebase.Base;
using ONS.SINapse.Shared.Settings;

namespace ONS.SINapse.Shared.Services.Firebase;

public interface ISolicitacaoFirebaseDatabaseService
{
    Task SetAsync(string id, SolicitacaoDto document, string sid, CancellationToken cancellationToken);
    Task DeleteAsync(string id, string sid, CancellationToken cancellationToken);
    Task<Dictionary<string, SolicitacaoDto>> GetPorStatusIdAsync(short statusId, string sid, CancellationToken cancellationToken);
}

public class SolicitacaoFirebaseDatabaseService : FirebaseDatabaseService<SolicitacaoDto>, ISolicitacaoFirebaseDatabaseService
{
    public SolicitacaoFirebaseDatabaseService(
        IOptions<FirebaseSettings> firebaseSettings, 
        IFirebaseAuthService firebaseAuthService, 
        IHttpClientFactory httpClientFactory) 
        : base(firebaseSettings, firebaseAuthService, httpClientFactory)
    {
    }

    protected override string CollectionName => ColecoesFirebase.Solicitacao;
    public Task<Dictionary<string, SolicitacaoDto>> GetPorStatusIdAsync(short statusId, string sid, CancellationToken cancellationToken)
    {
        return GetAsync<Dictionary<string, SolicitacaoDto>>(sid, new[] { CollectionName },
            $"orderBy=\"statusId\"&equalTo={statusId}", cancellationToken);
    }
}
