using Firebase.Database;
using Firebase.Database.Query;
using ONS.SINapse.Shared.Constants;
using ONS.SINapse.Shared.DTO.Firebase;

namespace ONS.SINapse.Shared.Services.Firebase;

public interface IVisaoUsuarioFirebaseDatabaseService
{
    Task SetAsync(VisaoUsuarioRealtimeDto document, CancellationToken cancellationToken);
    Task DeleteAsync(string id, string codigoCentroAgente, CancellationToken cancellationToken);
    Task<IDictionary<string, VisaoUsuarioRealtimeDto>> VisoesEmUsoAsync(string nome, string codigoCentroAgente, CancellationToken cancellationToken);
}

public class VisaoUsuarioFirebaseDatabaseService : IVisaoUsuarioFirebaseDatabaseService
{
    private readonly ChildQuery _firebaseCollection;
    public VisaoUsuarioFirebaseDatabaseService(FirebaseClient firebaseClient)
    {
        _firebaseCollection = firebaseClient.Child(ColecoesFirebase.VisaoUsuario);
    }

    public Task SetAsync(VisaoUsuarioRealtimeDto document, CancellationToken cancellationToken)
    {
        return _firebaseCollection
            .Child(document.CentroAgente.Codigo.ToLower)
            .Child(document.Id)
            .PutAsync(document)
            .WaitAsync(cancellationToken);
    }
    

    public Task DeleteAsync(string id, string codigoCentroAgente, CancellationToken cancellationToken)
    {
        return _firebaseCollection
            .Child(codigoCentroAgente.ToLower)
            .Child(id)
            .DeleteAsync()
            .WaitAsync(cancellationToken);
    }

    public async Task<IDictionary<string, VisaoUsuarioRealtimeDto>> VisoesEmUsoAsync(string nome, string codigoCentroAgente, CancellationToken cancellationToken)
    {
        var visao = await _firebaseCollection
            .Child(codigoCentroAgente.ToLower)
            .OrderBy("nome")
            .EqualTo(nome)
            .OnceAsync<VisaoUsuarioRealtimeDto>()
            .WaitAsync(cancellationToken)
            .ConfigureAwait(false);

        return visao.ToDictionary(k => k.Key, v => v.Object);
    }
}