using Firebase.Database;
using Firebase.Database.Query;
using ONS.SINapse.Shared.Constants;
using ONS.SINapse.Shared.DTO.Firebase;

namespace ONS.SINapse.Shared.Services.Firebase;

public interface INotificacaoFirebaseDatabaseService
{
    Task SetAsync(NotificacaoRealtimeDto document, CancellationToken cancellationToken);
    Task SetAsync(Dictionary<string, Dictionary<string, NotificacaoRealtimeDto>> document, CancellationToken cancellationToken);
    Task DeleteAllAsync(CancellationToken cancellationToken);
}

public class NotificacaoFirebaseDatabaseService :  INotificacaoFirebaseDatabaseService
{
    private readonly ChildQuery _firebaseCollection;

    public NotificacaoFirebaseDatabaseService(FirebaseClient firebaseClient)
    {
        _firebaseCollection = firebaseClient.Child(ColecoesFirebase.Notificacao);
    }
    
    public Task DeleteAllAsync(CancellationToken cancellationToken)
    {
        return _firebaseCollection
            .DeleteAsync()
            .WaitAsync(cancellationToken);
    }

    public Task SetAsync(NotificacaoRealtimeDto document, CancellationToken cancellationToken)
    {
        var notification = document.ToNotification();
        
        return _firebaseCollection
            .Child(document.Destination)
            .Child(Guid.NewGuid().ToString)
            .PutAsync(notification)
            .WaitAsync(cancellationToken);
    }

    public Task SetAsync(Dictionary<string, Dictionary<string, NotificacaoRealtimeDto>> document, CancellationToken cancellationToken)
    {
        Dictionary<string, Dictionary<string, Notification>> documents = [];
        
        foreach (var (destination, notifications) in document)
        {
            documents[destination] = notifications
                .ToDictionary(k => k.Key, v => v.Value.ToNotification());
        }

        return _firebaseCollection
            .PatchAsync(documents)
            .WaitAsync(cancellationToken);
    }
}