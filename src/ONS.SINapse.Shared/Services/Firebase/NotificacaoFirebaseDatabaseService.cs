using Firebase.Database;
using Firebase.Database.Query;
using ONS.SINapse.Shared.Constants;
using ONS.SINapse.Shared.DTO.Firebase;

namespace ONS.SINapse.Shared.Services.Firebase;

public interface INotificacaoFirebaseDatabaseService
{
    Task SetAsync(NotificacaoRealtimeDto document, CancellationToken cancellationToken);
    Task SetAsync(Dictionary<string, Dictionary<string, NotificacaoRealtimeDto>> document, CancellationToken cancellationToken);
    Task DeleteAllAsync(CancellationToken cancellationToken);
}

public class NotificacaoFirebaseDatabaseService :  INotificacaoFirebaseDatabaseService
{
    private readonly ChildQuery _firebaseCollection;

    public NotificacaoFirebaseDatabaseService(FirebaseClient firebaseClient)
    {
        _firebaseCollection = firebaseClient.Child(ColecoesFirebase.Notificacao);
    }
    
    public Task DeleteAllAsync(CancellationToken cancellationToken)
    {
        return _firebaseCollection
            .DeleteAsync()
            .WaitAsync(cancellationToken);
    }

    public Task SetAsync(NotificacaoRealtimeDto document, CancellationToken cancellationToken)
    {
        return _firebaseCollection
            .Child(document.Destination)
            .Child(Guid.NewGuid().ToString)
            .PutAsync(document)
            .WaitAsync(cancellationToken);
    }

    public Task SetAsync(Dictionary<string, Dictionary<string, NotificacaoRealtimeDto>> document, CancellationToken cancellationToken)
    {
        return _firebaseCollection
            .PatchAsync(document)
            .WaitAsync(cancellationToken);
    }
}