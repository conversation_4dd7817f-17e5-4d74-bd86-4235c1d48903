using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using System.Text;
using Firebase.Database;
using Firebase.Database.Query;
using ONS.SINapse.Shared.Policies;
using ONS.SINapse.Shared.Settings;
using static System.Net.Mime.MediaTypeNames;

namespace ONS.SINapse.Shared.Services.Firebase.Base;

public abstract class FirebaseDatabaseService
{
    private readonly HttpClient _client;
    private readonly FirebaseSettings _firebaseSettings;
    private readonly IFirebaseAuthService _firebaseAuthService;

    protected FirebaseDatabaseService(
        IOptions<FirebaseSettings> firebaseSettings,
        IFirebaseAuthService firebaseAuthService,
        IHttpClientFactory httpClientFactory)
    {
        _client = httpClientFactory.CreateClient();
        _client.BaseAddress = new Uri(firebaseSettings.Value.BasePath);
        _firebaseSettings = firebaseSettings.Value;
        _firebaseAuthService = firebaseAuthService;
    }
    
    protected FirebaseClient GetFirebaseClient(string? uid ) =>
        new(_firebaseSettings.BasePath, new FirebaseOptions()
        {
            AuthTokenAsyncFactory = async () =>
            {
                var token = await _firebaseAuthService.GetFirebaseTokenAsync(uid ?? Guid.NewGuid().ToString(), default);
                return token.FirebaseToken;
            }
        });

    private static string GetRoute(string auth, params string[] path) => 
        GetRoute(auth, string.Empty, path);

    private static string GetRoute(string auth, string query, string[] path)
    {
        StringBuilder stringBuilder = new();

        if (path != null && path.Length > 0)
        {
            string fullPath = string.Join('/', path);
            stringBuilder.Append(fullPath);
        }

        stringBuilder.Append($".json?");

        if (!string.IsNullOrWhiteSpace(query))
            stringBuilder.Append($"{query}&");
        
        stringBuilder.Append($"auth={auth}");

        return stringBuilder.ToString();
    }

    private static StringContent GetContent<TDocument>(TDocument document)
        where TDocument : class
        => new StringContent(
            JsonConvert.SerializeObject(document,
                new JsonSerializerSettings
                {
                    ContractResolver = new CamelCasePropertyNamesContractResolver()
                }
            ),
            Encoding.UTF8,
            Application.Json
        );

    protected async Task SetAsync<TDocument>(string collectionName, string id, TDocument document, string sid, CancellationToken cancellationToken)
        where TDocument : class
    {
        await RetryPolicies
            .FirebaseAsync(_firebaseSettings)
            .ExecuteAsync(async () =>
            {
                var content = GetContent(document);
                var auth = await _firebaseAuthService.GetFirebaseTokenAsync(sid, cancellationToken);
                var route = GetRoute(auth.FirebaseToken, collectionName, id);
                var response = await _client.PutAsync(route, content, cancellationToken);
                response.EnsureSuccessStatusCode();
            });
    }

    protected async Task SetAsync<TValue>(string collectionName, TValue value, string sid, CancellationToken cancellationToken)
        where TValue : class
    {
        await RetryPolicies
            .FirebaseAsync(_firebaseSettings)
            .ExecuteAsync(async () =>
            {
                var content = GetContent(value);
                var auth = await _firebaseAuthService.GetFirebaseTokenAsync(sid, cancellationToken);
                var route = GetRoute(auth.FirebaseToken, collectionName);
                var response = await _client.PutAsync(route, content, cancellationToken);
                response.EnsureSuccessStatusCode();
            });
    }

    protected async Task DeleteAsync(string collectionName, string id, string sid, CancellationToken cancellationToken)
    {
        await RetryPolicies
            .FirebaseAsync(_firebaseSettings)
            .ExecuteAsync(async () =>
            {
                var auth = await _firebaseAuthService.GetFirebaseTokenAsync(sid, cancellationToken);
                var route = GetRoute(auth.FirebaseToken, collectionName, id);
                var response = await _client.DeleteAsync(route, cancellationToken);
                response.EnsureSuccessStatusCode();
            });
    }

    protected async Task DropAsync(string collectionName, string sid, CancellationToken cancellationToken)
    {
        await RetryPolicies
            .FirebaseAsync(_firebaseSettings)
            .ExecuteAsync(async () =>
            {
                var auth = await _firebaseAuthService.GetFirebaseTokenAsync(sid, cancellationToken);
                var route = GetRoute(auth.FirebaseToken, collectionName);
                var response = await _client.DeleteAsync(route, cancellationToken);
                response.EnsureSuccessStatusCode();
            });
    }
    
    protected async Task<IEnumerable<TDocument?>> GetAllAsync<TDocument>(string collectionName, string sid, CancellationToken cancellationToken)
        where TDocument : class
    {
        string[] path = { collectionName };
        return await GetAllAsync<TDocument>(sid, path, cancellationToken);
    }

    protected async Task<TDocument?> GetAsync<TDocument>(string collectionName, string id, string sid, CancellationToken cancellationToken)
        where TDocument : class
    {
        string[] path = { collectionName, id };
        return await GetAsync<TDocument>(sid, path, cancellationToken);
    }

    protected async Task<IReadOnlyCollection<string>> GetAsync(string sid, CancellationToken cancellationToken)
    {
        var data = await GetAsync<Dictionary<string, object>>(sid, Array.Empty<string>(), cancellationToken);

        return data!.Select(item => item.Key)
            .ToList()
            .AsReadOnly();
    }

    internal Task<TValue?> GetAsync<TValue>(string sid, string[] path, CancellationToken cancellationToken)
        where TValue : class
    {
        return RetryPolicies
            .FirebaseAsync(_firebaseSettings)
            .ExecuteAsync(async () =>
            {
                var auth = await _firebaseAuthService.GetFirebaseTokenAsync(sid, cancellationToken);
                var route = GetRoute(auth.FirebaseToken, path);
                var response = await _client.GetAsync(route, cancellationToken);
                response.EnsureSuccessStatusCode();

                string json = await response.Content.ReadAsStringAsync();
                return JsonConvert.DeserializeObject<TValue>(json);
            });
    }
    
    internal Task<TValue> GetAsync<TValue>(string sid, string[] path, string query, CancellationToken cancellationToken)
        where TValue : class
    {
        return RetryPolicies
            .FirebaseAsync(_firebaseSettings)
            .ExecuteAsync(async () =>
            {
                var auth = await _firebaseAuthService.GetFirebaseTokenAsync(sid, cancellationToken);
                var route = GetRoute(auth.FirebaseToken, query, path);
                var response = await _client.GetAsync(route, cancellationToken);
                response.EnsureSuccessStatusCode();

                string json = await response.Content.ReadAsStringAsync();
                return JsonConvert.DeserializeObject<TValue>(json)!;
            });
    }
    
    internal Task<IEnumerable<TValue?>> GetAllAsync<TValue>(string sid, string[] path, CancellationToken cancellationToken)
        where TValue : class
    {
        return RetryPolicies
            .FirebaseAsync(_firebaseSettings)
            .ExecuteAsync(async () =>
            {
                var auth = await _firebaseAuthService.GetFirebaseTokenAsync(sid, cancellationToken);
                var route = GetRoute(auth.FirebaseToken, path);
                var response = await _client.GetAsync(route, cancellationToken);
                response.EnsureSuccessStatusCode();

                var json = await response.Content.ReadAsStringAsync();
                var dict = JsonConvert.DeserializeObject<Dictionary<string, TValue>>(json);
                if(dict == null)
                    return Enumerable.Empty<TValue>();

                return (IEnumerable<TValue?>) new List<TValue>(dict.Values);
            });
    }
}


public abstract class FirebaseDatabaseService<TDocument> : FirebaseDatabaseService
    where TDocument : class
{
    protected FirebaseDatabaseService(
        IOptions<FirebaseSettings> firebaseSettings,
        IFirebaseAuthService firebaseAuthService,
        IHttpClientFactory httpClientFactory)
        : base(firebaseSettings, firebaseAuthService, httpClientFactory)
    {
    }

    protected abstract string CollectionName { get; }

    public Task SetAsync(string id, TDocument document, string sid, CancellationToken cancellationToken) 
        => SetAsync(CollectionName, id, document, sid, cancellationToken);

    public Task DeleteAsync(string id, string sid, CancellationToken cancellationToken) 
        => DeleteAsync(CollectionName, id, sid, cancellationToken);

    public Task<TDocument?> GetAsync(string id, string sid, CancellationToken cancellationToken) 
        => GetAsync<TDocument>(CollectionName, id, sid, cancellationToken);

    public Task<IEnumerable<TDocument?>> GetAllAsync(string sid, CancellationToken cancellationToken)
        => GetAllAsync<TDocument>(CollectionName, sid, cancellationToken);
    
    public async Task<bool> ExistsDocumentWithPropertyValueAsync(string propertyName, string propertyValue, string sid, CancellationToken cancellationToken)
    {
        var firebaseClient = GetFirebaseClient(sid);
        var snapshot = await firebaseClient
            .Child(CollectionName)
            .OrderBy(propertyName)
            .EqualTo(propertyValue)
            .OnceAsync<object>();
        return snapshot.Count != 0;
    }
}
