using Microsoft.Extensions.Options;
using ONS.SINapse.Shared.Constants;
using ONS.SINapse.Shared.DTO.Firebase;
using ONS.SINapse.Shared.Services.Firebase.Base;
using ONS.SINapse.Shared.Settings;

namespace ONS.SINapse.Shared.Services.Firebase;

public interface ITagFirebaseService
{
    Task SetAsync(TagRealtimeDto document, CancellationToken cancellationToken);
    Task<TagRealtimeDto?> GetAsync(string id, string sid, CancellationToken cancellationToken);
    Task<TagRealtimeDto?[]> GetAsync(string[] visoesId, string sid, CancellationToken cancellationToken);
    Task<IEnumerable<TagRealtimeDto?>> GetAllAsync(string sid, CancellationToken cancellationToken);
    Task DeleteAsync(string id, string sid, CancellationToken cancellationToken);
    Task<bool> ExistsDocumentWithPropertyValueAsync(string propertyName, string propertyValue, string sid, CancellationToken cancellationToken);
    
}
public class TagFirebaseService : FirebaseDatabaseService<TagRealtimeDto>, ITagFirebaseService
{
    public TagFirebaseService(IOptions<FirebaseSettings> firebaseSettings, IFirebaseAuthService firebaseAuthService, IHttpClientFactory httpClientFactory) : base(firebaseSettings, firebaseAuthService, httpClientFactory)
    {
    }

    protected override string CollectionName => ColecoesFirebase.Tag;
    
    public Task SetAsync(TagRealtimeDto document, CancellationToken cancellationToken)
    => SetAsync(document.Id, document, document.Sid, cancellationToken);

    public async Task<TagRealtimeDto?[]> GetAsync(string[] visoesId, string sid, CancellationToken cancellationToken)
    {
        var tasks = visoesId.Select(id => GetAsync(id, sid, cancellationToken));
        var tags = await Task.WhenAll(tasks);
        return tags.Where(v => v != null).ToArray();
    }
}