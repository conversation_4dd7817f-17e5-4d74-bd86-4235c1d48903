using ONS.SINapse.Shared.DTO.Firebase;
using ONS.SINapse.Shared.Services.Caching;

namespace ONS.SINapse.Shared.Services.Firebase;

public class FirebaseAuthCacheService : IFirebaseAuthService
{
    private readonly IFirebaseAuthService _firebaseAuthService;
    private readonly ICacheService _cacheService;

    public FirebaseAuthCacheService(IFirebaseAuthService firebaseAuthService, ICacheService cacheService)
    {
        _firebaseAuthService = firebaseAuthService;
        _cacheService = cacheService;
    }

    public async Task<FirebaseSinapseCustomToken> GetTokenCustomizadoParaLeituraAsync(string uid, CancellationToken cancellationToken)
    {
        var cacheKey = $"token-leitura-{uid}";
        var cacheToken = await _cacheService
            .GetAsync<FirebaseSinapseCustomToken>(cacheKey, cancellationToken)
            .ConfigureAwait(false);

        if (cacheToken is not null) return cacheToken;

        var newToken = await _firebaseAuthService
            .GetTokenCustomizadoParaLeituraAsync(uid, cancellationToken)
            .ConfigureAwait(false);

        await _cacheService
            .SetAsync(cacheKey, newToken, newToken.ExpiredAt.AddMinutes(-10), cancellationToken)
            .ConfigureAwait(false);
        
        return newToken;
    }

    public async Task<FirebaseSignInResultDto> GetFirebaseTokenAsync(string uid, CancellationToken cancellationToken)
    {
        var cacheKey = $"token-firebase-{uid}";
        var cacheToken = await _cacheService
            .GetAsync<FirebaseSignInResultDto>(cacheKey, cancellationToken)
            .ConfigureAwait(false);

        if (cacheToken is not null) return cacheToken;

        var newToken = await _firebaseAuthService
            .GetFirebaseTokenAsync(uid, cancellationToken)
            .ConfigureAwait(false);

        var expirationTime = newToken.Created.AddSeconds(newToken.ExpiresIn).AddMinutes(-10);
        
        await _cacheService
            .SetAsync(cacheKey, newToken, expirationTime, cancellationToken)
            .ConfigureAwait(false);
        
        return newToken;
    }
}