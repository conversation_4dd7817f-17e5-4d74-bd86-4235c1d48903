using FirebaseAdmin;
using Google.Apis.Auth.OAuth2;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Text;
using Firebase.Auth;
using ONS.SINapse.Shared.DTO.Firebase;
using ONS.SINapse.Shared.Policies;
using ONS.SINapse.Shared.Settings;

namespace ONS.SINapse.Shared.Services.Firebase;

public class FirebaseAuthService : IFirebaseAuthService
{
    private const string FirebaseAppInstanceName = "ONS.SINapse";
    private readonly FirebaseSettings _firebaseSettings;
    private readonly ILogger<FirebaseAuthService> _logger;
    private readonly FirebaseApp _firebaseApp;

    public FirebaseAuthService(
        IOptions<FirebaseSettings> firebaseSettings,
        ILogger<FirebaseAuthService> logger)
    {
        _firebaseSettings = firebaseSettings.Value;
        _logger = logger;
        _firebaseApp = CriarInstancia();
    }

    private FirebaseApp CriarInstancia()
    {
        return RetryPolicies
            .Firebase(_firebaseSettings)
            .Execute(() =>
            {
                var path = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, _firebaseSettings.ArquivoConfigJson);
                File.WriteAllText(path, Encoding.UTF8.GetString(Convert.FromBase64String(_firebaseSettings.Base64ConfigJson)));
                
                _logger.LogDebug("[FIREBASE] Criando instância do Firebase.");
                return FirebaseApp.GetInstance(FirebaseAppInstanceName) ?? FirebaseApp.Create(new AppOptions
                {
                    Credential = GoogleCredential.FromFile(path)
                }, FirebaseAppInstanceName);
            });
    }

    private async Task<FirebaseSinapseCustomToken> CriarTokenPersonalizadoAsync(string uid, IDictionary<string, object> claims, CancellationToken cancellationToken)
    {
        if (string.IsNullOrEmpty(uid))
            uid = FirebaseAppInstanceName;
        
        var token = await RetryPolicies
            .FirebaseAsync(_firebaseSettings)
            .ExecuteAsync(async () =>
            {
                _logger.LogDebug("[FIREBASE] Criando token customizado da instância do Firebase.");
                
                try
                {
                    return await FirebaseAdmin.Auth.FirebaseAuth
                        .GetAuth(_firebaseApp)
                        .CreateCustomTokenAsync(uid, claims, cancellationToken);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Erro ao criar token customizado");
                    throw;
                }
            });
        
        ArgumentException.ThrowIfNullOrWhiteSpace(token);
        
        return new FirebaseSinapseCustomToken(token);
    }

    public Task<FirebaseSignInResultDto> GetFirebaseTokenAsync(string uid, CancellationToken cancellationToken) =>
        SignInFirebaseAsync(uid, cancellationToken)
            .ContinueWith(task => task.Result, cancellationToken);

    private async Task<FirebaseSignInResultDto> SignInFirebaseAsync(string uid, CancellationToken cancellationToken)
    {
        return await RetryPolicies
            .FirebaseAsync(_firebaseSettings)
            .ExecuteAsync(async () =>
            {
                var authProvider = new FirebaseAuthProvider(new FirebaseConfig(_firebaseSettings.AuthSecret));
                var token = await GetTokenCustomizadoParaEscritaELeituraAsync(uid, cancellationToken);
                _logger.LogDebug("[FIREBASE] Logando no firebase através do custom token.");
                var auth = await authProvider.SignInWithCustomTokenAsync(token.AccessToken);
                
                return new FirebaseSignInResultDto
                (
                    auth.FirebaseToken,
                    auth.RefreshToken,
                    auth.ExpiresIn,
                    auth.Created
                );
            });
    }

    public Task<FirebaseSinapseCustomToken> GetTokenCustomizadoParaLeituraAsync(string uid, CancellationToken cancellationToken) 
        => CriarTokenPersonalizadoAsync(uid,
            new Dictionary<string, object>
            {
                { "read", true },
                { "tokenSinapse", _firebaseSettings.TokenRead }
            },
            cancellationToken);

    private Task<FirebaseSinapseCustomToken> GetTokenCustomizadoParaEscritaELeituraAsync(string uid, CancellationToken cancellationToken)
        => CriarTokenPersonalizadoAsync(uid,
            new Dictionary<string, object>
            {
                { "write", true },
                { "read", true },
                { "tokenSinapse", _firebaseSettings.TokenReadWrite }
            },
            cancellationToken);
}