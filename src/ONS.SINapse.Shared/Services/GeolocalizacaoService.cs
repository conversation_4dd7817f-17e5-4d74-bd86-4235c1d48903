using Microsoft.Extensions.Options;
using ONS.SINapse.Shared.Helpers;
using ONS.SINapse.Shared.Settings;

namespace ONS.SINapse.Shared.Services;

public interface IGeolocalizacaoService
{
    void SetCoordenadaGeograficaDoUsuario(CoordenadaGeografica? coordenadaGeograficaDoUsuario);
    CoordenadaGeografica? GetCoordenadaGeograficaDoUsuario();
}

public sealed class GeolocalizacaoService : IGeolocalizacaoService
{
    private CoordenadaGeografica? _coordenadaGeograficaDoUsuario = null;
    private readonly VerificacaoDeGeolocalizacaoSettings _verificacaoDeGeolocalizacaoSettings;

    public GeolocalizacaoService(
        IOptions<VerificacaoDeGeolocalizacaoSettings> verificacaoDeGeolocalizacaoSettings)
    {
        _verificacaoDeGeolocalizacaoSettings = verificacaoDeGeolocalizacaoSettings.Value;
    }
    
    public void SetCoordenadaGeograficaDoUsuario(CoordenadaGeografica? coordenadaGeograficaDoUsuario)
    {
        _coordenadaGeograficaDoUsuario = coordenadaGeograficaDoUsuario;
    }

    public CoordenadaGeografica? GetCoordenadaGeograficaDoUsuario()
    {
        if (_coordenadaGeograficaDoUsuario is null
            && _verificacaoDeGeolocalizacaoSettings.ValidarGeolocalizacaoPorPadrao)
        {
            throw new ArgumentException("Geolocalização não informada.");
        }

        return _coordenadaGeograficaDoUsuario;
    }
}