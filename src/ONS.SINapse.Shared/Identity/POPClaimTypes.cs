
using System.Security.Claims;

#nullable disable

namespace ONS.SINapse.Shared.Identity;
public static class PopClaimTypes
{
  public const string TipoUsuario = "http://schemas.xmlsoap.org/ws/2015/07/identity/claims/usertype";
  public const string StatusUsuario = "http://schemas.xmlsoap.org/ws/2015/07/identity/claims/userstate";
  public const string Ticket = "http://schemas.xmlsoap.org/ws/2015/07/identity/claims/userticket";
  public const string ScopeRole = "http://schemas.xmlsoap.org/ws/2015/07/identity/claims/scoperole";
  public const string ScopeOperation = "http://schemas.xmlsoap.org/ws/2015/07/identity/claims/scopeoperation";
  public const string Operation = "http://schemas.xmlsoap.org/ws/2015/07/identity/claims/operation";
  public const string Matricula = "http://schemas.xmlsoap.org/ws/2015/07/identity/claims/matricula";
  public const string LoginPeoplesoft = "http://schemas.xmlsoap.org/ws/2015/07/identity/claims/loginpeoplesoft";
  public const string Gerencia = "http://schemas.xmlsoap.org/ws/2015/07/identity/claims/gerencia";
  public const string Gerente = "http://schemas.xmlsoap.org/ws/2015/07/identity/claims/gerente";
  public const string GerenciaExecutiva = "http://schemas.xmlsoap.org/ws/2015/07/identity/claims/gerenciaexecutiva";
  public const string CargoONS = "http://schemas.xmlsoap.org/ws/2015/07/identity/claims/cargoons";
  public const string Group = "http://schemas.xmlsoap.org/ws/2015/07/identity/claims/group";
  public const string PrimaryGroupSid = "http://schemas.microsoft.com/ws/2008/06/identity/claims/primarygroupsid";
  public const string Scope = "http://schemas.xmlsoap.org/ws/2015/07/identity/claims/scope";
  public const string Role = "http://schemas.microsoft.com/ws/2008/06/identity/claims/role";
  public const string StringType = "http://www.w3.org/2001/XMLSchema#string";
  public const string Name = "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name";
  public const string GivenName = "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/givenname";
  public const string Surname = "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/surname";
  public const string NameIdentifier = "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier";
  public const string WindowsAccountName = "http://schemas.microsoft.com/ws/2008/06/identity/claims/windowsaccountname";
  public const string Email = "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress";
  public const string Upn = "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/upn";
  public const string Sid = "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/sid";
  public const string PrimarySid = "http://schemas.microsoft.com/ws/2008/06/identity/claims/primarysid";
  public const string GroupSid = "http://schemas.microsoft.com/ws/2008/06/identity/claims/groupsid";
  public const string SeparadorConcatenacao = "|#$%";
  public const string Authentication = "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/authentication";
  public const string AuthorizationDecision = "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/authorizationdecision";

  public static string ObterClaim(this ClaimsPrincipal usuario, string type)
  {
    return usuario.ObterClaim(type, (string) null);
  }

  public static string ObterClaim(this ClaimsPrincipal usuario, string type, string defaultvalue)
  {
    string str = defaultvalue;
    if (usuario != null && usuario.HasClaim((Predicate<Claim>) (c => c.Type == type)))
      str = usuario.FindFirst(type).Value;
    return str;
  }

  public static class SharePoint
  {
    public const string Title = "http://schemas.wingtip.com/sharepoint/2009/08/claims/title";
    public const string ClaimName = "http://schemas.wingtip.com/sharepoint/2009/08/claims/name";
    public const string UserLogonName = "http://schemas.microsoft.com/sharepoint/2009/08/claims/userlogonname";
  }
}
