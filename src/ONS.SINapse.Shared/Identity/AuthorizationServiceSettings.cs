namespace ONS.SINapse.Shared.Identity;

public class AuthorizationServiceSettings
{
    public AuthorizationServiceSettings() 
    {
        Issuer = string.Empty;
        Audience = string.Empty;
        Secret = string.Empty;
        RsaModulus = string.Empty;
        RsaPublicExponent = string.Empty;
    } // Usado por DI via appsettings configuration em startup project
    
    public AuthorizationServiceSettings(string issuer, string audiences, string secret, string rsaPublicKeyExponent64, string rsaPublicKeyModulus64)
    {
        Issuer = issuer;
        Audience = audiences;
        Secret = secret;
        RsaModulus = rsaPublicKeyExponent64;
        RsaPublicExponent = rsaPublicKeyModulus64;
    }

    public string Secret { get; set; }
    public string Issuer { get; set; }
    public string Audience { get; set; }
    public bool UseRsa { get; set; } = true;
    public string RsaModulus { get; set; }
    public string RsaPublicExponent { get; set; }
    
}