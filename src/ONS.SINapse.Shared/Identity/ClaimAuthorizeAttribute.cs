using Microsoft.AspNetCore.Authorization;

namespace ONS.SINapse.Shared.Identity;

public class ClaimAuthorizeAttribute : 
    AuthorizeAttribute,
    IAuthorizationRequirement,
    IClaimAuthorizeAttribute
{
    public string ClaimType { get; set; }

    public string[] ClaimValues { get; set; }

    public ClaimAuthorizeAttribute(string type, string value)
    {
        ClaimType = type;
        ClaimValues = value.Split(',');
    }
}