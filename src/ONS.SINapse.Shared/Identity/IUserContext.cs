using System.Web;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using ONS.SINapse.Shared.DTO.PerfilDoUsuario;
using ONS.SINapse.Shared.Extensions;

namespace ONS.SINapse.Shared.Identity;

/// <summary>
/// Para acessar o usuário independente do scope da injeção de dependencia.
/// Cria um scope de injeção de dependencia próprio e busca a informação do usuário logado
/// </summary>
public interface IUserContextAccessor
{
    IUserContext UserContext { get; }
}

public class UserContextAccessor : IUserContextAccessor
{
    private readonly IServiceScopeFactory _scopeFactory;

    public UserContextAccessor(IServiceScopeFactory scopeFactory)
    {
        _scopeFactory = scopeFactory;
    }

    public IUserContext UserContext => ObterUserContext();

    private IUserContext ObterUserContext()
    {
        using var scope = _scopeFactory.CreateScope();
        return scope.ServiceProvider.GetRequiredService<IUserContext>();
    }
    
}


public interface IUserContext
{
    public string Sid { get; }
    public string Nome { get; }
    public string Login { get; }
    public Perfil Perfil { get; }
    public IReadOnlyCollection<string> Roles { get; }
    public IReadOnlyCollection<string> Scopes { get; }
    public IReadOnlyCollection<string> Centros { get; }
    public IReadOnlyCollection<string> ScopeRoles { get; }
    public List<string> OperationsSelecionadas();
    public bool PerfilSelecionadoPossuiOperacao(string operacao);
    public string AccessToken { get; }
    public bool UsuarioAutenticado();
}

public sealed class UserContext : IUserContext
{
    private UserContext(string token, string sid, string nome, string login)
        : this(
            Array.Empty<string>(), 
            Array.Empty<string>(),
            Array.Empty<string>(),
            token,
            sid, 
            nome, 
            login)
    {
    }

    private UserContext(
        string[] roles, 
        string[] scopes, 
        string[] scopeRoles,
        string token,
        string sid, 
        string nome, 
        string login)
    {
        _roles = roles.ToList();
        _scopes = scopes.ToList();
        _scopeRoles = scopeRoles.ToList();
        AccessToken = token;
        Sid = sid;
        Nome = nome;
        Login = login;
        Perfil = new Perfil([], [], string.Empty, string.Empty);
        Centros = _scopes.Select(x => x.Split('/')[1].Trim()).ToList().AsReadOnly();
    }

    public string Sid { get; private set; }
    public string Nome { get; private set; }
    public string Login { get; private set; }
    public required Perfil Perfil { get; init; }
    public string AccessToken { get; private set; }
    public bool UsuarioAutenticado() => !string.IsNullOrWhiteSpace(AccessToken);

    [JsonIgnore]
    private readonly List<string> _roles;
    public IReadOnlyCollection<string> Roles => _roles.AsReadOnly();
    
    [JsonIgnore]
    private readonly List<string> _scopes;
    public IReadOnlyCollection<string> Scopes => _scopes.AsReadOnly();
    
    public IReadOnlyCollection<string> Centros { get; }
    
    [JsonIgnore]
    private readonly List<string> _scopeRoles;
    public IReadOnlyCollection<string> ScopeRoles => _scopeRoles.AsReadOnly();

    public List<string> OperationsSelecionadas() 
        => Perfil.Operacoes
            .Distinct()
            .ToList();

    public bool PerfilSelecionadoPossuiOperacao(string operacao) 
        => OperationsSelecionadas().Contains(operacao);

    public static class UserContextFactory
    {
        public static readonly Perfil PerfilSistema = new ([], [],"Sistema", "sistema");

        public static readonly IUserContext UsuarioAnonimo =
            new UserContext("", "usuario-anonimo", "Usuário Anonimo", "Usuário Anonimo") { Perfil = PerfilSistema };
        
        public static IUserContext Create(IHttpContextAccessor httpContextAccessor, IRulesOperations rulesOperations)
        {
            var httpContext = httpContextAccessor.HttpContext;
            
            if(httpContext is null)
                return UsuarioAnonimo;
            
            if(!httpContext.User.Identity?.IsAuthenticated ?? false)
                return UsuarioAnonimo;
            
            var token = httpContext.Request.Headers.Authorization.ToString()["Bearer ".Length..];
            
            // Centro De Operacao: CENTROS/NE
            var scopeRoleFormatado = httpContext.User.ObterScopeRolesFormatado();
            
            var roles = httpContext.User.ObterRoles();
            var scopes = httpContext.User.ObterScopes();
            var scopeRoles = httpContext.User.ObterScopeRoles();
            // Corrige erro gerados devido acentuação gráfica no nome do escopo ou perfil selecionado
            var perfilSelecionado = HttpUtility.UrlDecode(httpContext.Request.Headers["perfil-selecionado"]);

            var perfilSistema = CreatePerfilSistema(roles, scopes, scopeRoles, token, httpContext, null);

            if (string.IsNullOrWhiteSpace(perfilSelecionado))
                return perfilSistema;
            
            var perfil = ObterPerfilUsuario(perfilSelecionado, scopeRoleFormatado, rulesOperations);

            if (perfil is null)
                return perfilSistema;
            
            perfilSistema = CreatePerfilSistema(roles, scopes, scopeRoles, token, httpContext, perfil);
            
            return perfilSistema;
        }

        private static UserContext CreatePerfilSistema(
            string[] roles,
            string[] scopes,
            string[] scopeRoles,
            string token,
            HttpContext httpContext,
            Perfil? perfil)
        {
            return new UserContext(
                roles,
                scopes,
                scopeRoles,
                token,
                httpContext.User.ObterSidDeUsuario(),
                httpContext.User.ObterNomeDeUsuario(),
                httpContext.User.ObterLoginDeUsuario())
            {
                Perfil = perfil ?? PerfilSistema
            };
        }

        private static Perfil? ObterPerfilUsuario(string perfilSelecionado, IDictionary<(string Codigo, string Nome), List<string>> scopeRole, IRulesOperations rulesOperations)
        {
            return perfilSelecionado
                .Split(',')
                .Select(x =>
                {
                    var dadosPerfil = x.Split('/');
                    var perfil = dadosPerfil[0].Trim();
                    var tipoScope = dadosPerfil[1].Trim();
                    var codigoScope = dadosPerfil[2].Trim();
                    var nomeScope = dadosPerfil[3].Trim();

                    var perfilKey = scopeRole.Keys.First(k => k.Codigo == perfil);
            
                    var operationsSelecionado = rulesOperations[perfilKey.Codigo].Operations.ToList();
            
                    return new { Perfil = new
                    {
                        Codigo = perfil,
                        Nome = perfil,
                        Operacoes = operationsSelecionado
                    }, Scope = new Scope(tipoScope, codigoScope, nomeScope) };
                })
                .GroupBy(
                    group => group.Perfil.Codigo,
                    by => new { by.Scope, by.Perfil },
                    (perfil, scopes) => new Perfil(scopes.Select(x => x.Scope).ToList(), scopes.First().Perfil.Operacoes, scopes.First().Perfil.Nome, scopes.First().Perfil.Codigo)
                ).FirstOrDefault();
        }
    }
}

public class Perfil
{
    public Perfil(List<Scope> scopes, List<string> operacoes, string nome, string codigo)
    {
        _scopes = scopes;
        _operacoes = operacoes;
        Nome = nome;
        Codigo = codigo;
    }

    public string Nome { get; private set; }
    public string Codigo { get; private set; }
    
    private readonly List<string> _operacoes;
    public IReadOnlyCollection<string> Operacoes => _operacoes.AsReadOnly();
    
    private readonly List<Scope> _scopes;
    public IReadOnlyCollection<Scope> Scopes => _scopes.AsReadOnly();
    public IReadOnlyCollection<string> Centros => Scopes.Select(x => x.Codigo).ToList().AsReadOnly();
    
    /// <summary>
    /// Compara se o perfil selecionado é da role passada
    /// </summary>
    /// <param name="perfil"> Role que o perfil deve estar selecionado </param>
    /// <returns></returns>
    public bool Igual(string perfil) => Igual(this, perfil);

    private static bool Igual(Perfil perfil, string role) => perfil.Codigo == role.RemoverCaracteresEspeciais();

    public override string ToString() => $"Codigo: {Codigo}, Nome: {Nome}";
}


public class Scope
{
    public Scope(string tipo, string codigo, string nome)
    {
        Tipo = tipo;
        Codigo = codigo;
        Nome = nome;
    }

    public string Tipo { get; private set; }
    public string Codigo { get; private set; }
    public string Nome { get; private set; }
}


