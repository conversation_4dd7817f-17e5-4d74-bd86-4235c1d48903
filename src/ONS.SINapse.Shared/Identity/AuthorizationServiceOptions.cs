namespace ONS.SINapse.Shared.Identity;

public class AuthorizationServiceOptions
{
    public AuthorizationServiceOptions(string issuer, IEnumerable<string> audiences, string secret, bool useRsa, string rsaPublicKeyExponent64, string rsaPublicKeyModulus64)
    {
        Issuer = issuer;
        Audiences = audiences;
        Secret = secret;
        UseRsa = useRsa;
        RsaPublicKeyExponent64 = rsaPublicKeyExponent64;
        RsaPublicKeyModulus64 = rsaPublicKeyModulus64;
    }

    public string Issuer { get; set; }

    public IEnumerable<string> Audiences { get; set; }

    public string Secret { get; set; }

    public bool UseRsa { get; set; }

    public string RsaPublicKeyExponent64 { get; set; }

    public string RsaPublicKeyModulus64 { get; set; }
}