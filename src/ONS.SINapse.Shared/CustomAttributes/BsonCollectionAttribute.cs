using System.Reflection;

namespace ONS.SINapse.Shared.CustomAttributes
{
    [AttributeUsage(AttributeTargets.Class, Inherited = false)]
    public class BsonCollectionAttribute : Attribute
    {
        public string CollectionName { get; }

        public BsonCollectionAttribute(string collectionName)
        {
            CollectionName = collectionName;
        }
    }

    public static class BsonCollectionHelper
    {
        public static string? GetCollectionName(this Type documentType) => documentType.GetCustomAttribute<BsonCollectionAttribute>()?.CollectionName;
    }
}
