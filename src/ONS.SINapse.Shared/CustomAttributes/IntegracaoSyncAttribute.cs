namespace ONS.SINapse.Shared.CustomAttributes;

[AttributeUsage(AttributeTargets.Method | AttributeTargets.Class)]
public class IntegracaoSyncAttribute : Attribute
{
    /// <summary>
    /// Serve como identificador de:
    /// <list type="table">
    ///     <item>versão do cache local</item>
    ///     <item>versão retornada pela api</item>
    ///     <item>dados em cache local</item>
    ///     <item>método de busca de dados na api</item>
    /// </list>
    /// </summary>
    public string[] Identificadores { get; }
    
    /// <summary>
    /// Atributo para vincular a api de dados com o cache local
    /// </summary>
    /// <param name="identificadores">
    /// Serve como identificador de:
    /// <list type="table">
    ///     <item>versão do cache local</item>
    ///     <item>versão retornada pela api</item>
    ///     <item>dados em cache local</item>
    ///     <item>método de busca de dados na api</item>
    /// </list>
    /// </param>
    public IntegracaoSyncAttribute(params string[] identificadores)
    {
        Identificadores = identificadores;
    }

    public string IdentificadorUnico()
    {
        return IdentificadorUnico(Identificadores);
    }

    public static string IdentificadorUnico(params string[] identificadores) => string.Join("_", identificadores);
}

/// <summary>
/// Api's com esse atributo terão o cache ignorados em sua implementação
/// </summary>
[AttributeUsage(AttributeTargets.Method | AttributeTargets.Class)]
public class IntegracaoSyncIgnoreCacheAttribute : Attribute
{
}