using FluentValidation;
using FluentValidation.Results;
using MassTransit.Mediator;

namespace ONS.SINapse.Shared.Messages;

public abstract class CommandHandler<TCommand> : MediatorRequestHandler<TCommand, ValidationResult> where TCommand : Command
{
    protected ValidationResult ValidationResult = new();

    protected virtual void AdicionarErro(string mensagem)
    {
        ValidationResult.Errors.Add(new ValidationFailure(string.Empty, mensagem));
    }
        
    protected virtual void AdicionarWarning(string mensagem)
    {
        ValidationResult.Errors.Add(new ValidationFailure(string.Empty, mensagem) { Severity = Severity.Warning });
    }
}

public abstract class CommandHandler<TCommand, TResult> :
    MediatorRequestHandler<TCommand, TResult>
    where TCommand : Command<TResult>
    where TResult : CommandResult, new()
{
    protected TResult Result = new();

    protected virtual void AdicionarErro(string mensagem)
    {
        Result.ValidationResult.Errors.Add(new ValidationFailure(string.Empty, mensagem));
    }

    protected virtual void AdicionarValidation(ValidationResult validationResult) 
        => Result.AdicionarValidation(validationResult);

}