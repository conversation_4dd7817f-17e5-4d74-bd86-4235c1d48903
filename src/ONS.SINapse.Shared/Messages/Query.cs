using FluentValidation.Results;
using MassTransit.Mediator;
using ONS.SINapse.Shared.Extensions;

namespace ONS.SINapse.Shared.Messages;

public abstract class Query<TResult> : Request<QueryResult<TResult>>
{
    
}


public class QueryResult<TResult>
{
    public QueryResult()
    {
        ValidationResult = new ValidationResult();
    }

    public QueryResult(TResult result) : this()
    {
        Result = result;
    }
    
    public TResult? Result { get; set; }
    public ValidationResult ValidationResult { get; }

    public void AdicionarErro(string erro) => ValidationResult.AdicionarErro(erro);
}