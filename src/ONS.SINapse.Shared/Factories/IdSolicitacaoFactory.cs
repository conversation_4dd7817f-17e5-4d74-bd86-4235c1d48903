using System.Text.RegularExpressions;
using NanoidDotNet;

namespace ONS.SINapse.Shared.Factories;

public static partial class IdSolicitacaoFactory
{
    private const int RandomIdLength = 6;
    private const string DateFormat = "yyyyMMddHHmmss";
    private const string RandomAlphabet = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
    private static readonly Regex Regex = ValidarIdRegex();
    
    public static string GerarNovoId(string origem, string destino)
    {
        var datetime = DateTime.Now.ToString(DateFormat);
        var aleatorio = GenerateRandomString(RandomIdLength);

        var id = $"{datetime}-{origem.ToUpper()}-{aleatorio}-{destino.ToUpper()}";

        return id;
    }
    public static bool EstaValido(string id) 
        => !string.IsNullOrEmpty(id) && Regex.IsMatch(id);
    
    [GeneratedRegex(@"^\d{14}-[A-Z0-9]+-[A-Za-z0-9]{6}-[A-Z0-9]+$", RegexOptions.Compiled)]
    private static partial Regex ValidarIdRegex();
    
    private static string GenerateRandomString(int length)
    {
        return Nanoid.Generate(RandomAlphabet, length);
    }
}