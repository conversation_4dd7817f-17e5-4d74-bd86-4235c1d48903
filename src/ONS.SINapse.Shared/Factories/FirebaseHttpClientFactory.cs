using Firebase;
using IHttpClientFactory = System.Net.Http.IHttpClientFactory;

namespace ONS.SINapse.Shared.Factories;

public class FirebaseHttpClientFactory: Firebase.IHttpClientFactory
{
    private readonly IHttpClientFactory _httpClientFactory;
    public FirebaseHttpClientFactory(IHttpClientFactory httpClientFactory)
    {
        _httpClientFactory = httpClientFactory;
    }
    public IHttpClientProxy GetHttpClient(TimeSpan? timeout)
    {
        var client = _httpClientFactory.CreateClient("firebase-sinapse");
        if(timeout.HasValue) client.Timeout = timeout.Value;
        return new CustomHttpClientProxy(client);
    }
}

public class CustomHttpClientProxy : IHttpClientProxy
{
    private readonly HttpClient _httpClient;

    public CustomHttpClientProxy(HttpClient httpClient)
    {
        _httpClient = httpClient;
    }
    
    public HttpClient GetHttpClient() => _httpClient;


    private bool _disposed;
    protected virtual void Dispose(bool disposing)
    {
        if (_disposed) return;

        if (disposing)
        {
            _httpClient.Dispose();
        }

        _disposed = true;
    }
    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }
}

