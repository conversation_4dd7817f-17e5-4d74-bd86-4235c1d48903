using FluentValidation;
using ONS.SINapse.Shared.DTO;

namespace ONS.SINapse.Business.Imp.Validators;

public class VisaoDeUsuarioDtoValidator : AbstractValidator<VisaoDeUsuarioDto>
{
    public VisaoDeUsuarioDtoValidator()
    {
        RuleFor(c => c.Nome)
            .NotEmpty()
            .WithMessage("Nome da visão não informado.")
            .MinimumLength(2)
            .WithMessage("Nome da visão deve ter no mínimo {MinLength} caracteres.")
            .MaximumLength(20)
            .WithMessage("Nome da visão deve ter no máximo {MaxLength} caracteres.");
        RuleFor(c => c.CodigoCentroAgente)
            .NotEmpty()
            .WithMessage("Centro agente da visão não informado.");
        RuleFor(c => c)
            .Must(c => c.EquipamentosDeManobra.Count != 0 || c.Tags.Count != 0)
            .WithMessage("Informe pelo menos um, tag ou equipamento de manobra.");

        When(c => c.EquipamentosDeManobra.Count != 0, () =>
        {
            RuleForEach(c => c.EquipamentosDeManobra)
                .SetValidator(new FiltroDeVisaoDeUsuarioDtoValidator());
        });
    }

    private sealed class FiltroDeVisaoDeUsuarioDtoValidator : AbstractValidator<EquipamentoVisaoDeUsuarioDto>
    {
        public FiltroDeVisaoDeUsuarioDtoValidator()
        {
            const string mensagemVazia = "O campo {PropertyName} não pode ser vazio.";
            
            RuleFor(dto => dto.Valor)
                .NotEmpty()
                .WithMessage(mensagemVazia);

            RuleFor(dto => dto.Descricao)
                .NotEmpty()
                .WithMessage(mensagemVazia);
            
            RuleFor(dto => dto.Tipo)
                .NotEmpty()
                .WithMessage(mensagemVazia);
        }
    }
}