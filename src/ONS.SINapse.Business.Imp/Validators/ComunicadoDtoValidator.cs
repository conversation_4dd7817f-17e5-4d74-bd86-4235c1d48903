using FluentValidation;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Enums;

namespace ONS.SINapse.Business.Imp.Validators;

public class ComunicadoDtoValidator : AbstractValidator<ComunicadoDto>
{
    public ComunicadoDtoValidator()
    {
        RuleFor(c => c.Titulo)
            .NotEmpty()
            .WithMessage("Título do comunicado não informado.");
        RuleFor(c => c.<PERSON>)
            .NotEmpty()
            .WithMessage("Mensagem do cuminicado não informada.");
    }
}