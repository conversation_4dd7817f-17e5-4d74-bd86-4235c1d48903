using FluentValidation;
using ONS.SINapse.Shared.DTO;

namespace ONS.SINapse.Business.Imp.Validators;

public class ComunicadoDtoValidator : AbstractValidator<ComunicadoDto>
{
    public ComunicadoDtoValidator()
    {
        RuleFor(c => c.Titulo)
            .NotEmpty()
            .WithMessage("Título do comunicado não informado.");
        RuleFor(c => c.Mensagem)
            .NotEmpty()
            .WithMessage("Mensagem do cuminicado não informada.");
    }
}