using FluentValidation;
using ONS.SINapse.Shared.DTO.Firebase;

namespace ONS.SINapse.Business.Imp.Validators.Firebase;

public class NotificacaoConfiguracaoValidator : AbstractValidator<NotificacaoConfiguracaoDto>
{
    private const string ValorNaoPodeSerNulo = "Campo '{PropertyName}' não pode ser nulo.";

    public NotificacaoConfiguracaoValidator()
    {
        RuleFor(x => x.Sonora)
            .NotNull()
            .WithMessage(ValorNaoPodeSerNulo)
            .NotEmpty()
            .WithMessage("A lista '{PropertyName}' não pode estar vazia.")
            .ForEach(x => x.SetValidator(new NotificacaoSonoraConfiguracaoValidator()));
    }
}
