using FluentValidation;
using ONS.SINapse.Shared.DTO.Firebase;

namespace ONS.SINapse.Business.Imp.Validators.Firebase;

public class NotificacaoSonoraConfiguracaoValidator : AbstractValidator<NotificacaoSonoraConfiguracaoDto>
{
    private const string ValorNaoPodeSerVazio = "Campo '{PropertyName}' não pode estar vazio.";
    private const string ValorNaoPodeSerNulo = "Campo '{PropertyName}' não pode ser nulo.";

    public NotificacaoSonoraConfiguracaoValidator()
    {
        RuleFor(x => x.Tipo)
            .NotNull()
            .WithMessage(ValorNaoPodeSerNulo)
            .NotEmpty()
            .WithMessage(ValorNaoPodeSerVazio);

        RuleFor(x => x.Audio)
            .NotNull()
            .WithMessage(ValorNaoPodeSerNulo)
            .NotEmpty()
            .WithMessage(ValorNaoPodeSerVazio);
    }
}