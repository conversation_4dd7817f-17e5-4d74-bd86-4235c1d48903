using FluentValidation;
using ONS.SINapse.Repository.IRepository;
using ONS.SINapse.Shared.DTO.Firebase;
using ONS.SINapse.Shared.Identity;

namespace ONS.SINapse.Business.Imp.Validators.Firebase;

public class RascunhoSolicitacaoValidator : AbstractValidator<RascunhoSolicitacaoDto>
{
    private readonly IRascunhoSolicitacaoRepository _rascunhoSolicitacaoRepository;
    private readonly IUserContext _perfilDoUsuario;

    private const string ValorNaoPodeSerVazio = "Campo '{PropertyName}' não pode estar vazio.";
    private const string ValorNaoPodeSerNulo = "Campo '{PropertyName}' não pode ser nulo.";
    private const string ValorJaExiste = "Campo '{PropertyName}' com valor '{PropertyValue}' já está em uso.";
    private const string ValorDeveSerMaiorQueOPreenchido =
        "Valor do campo '{PropertyName}' deve ser maior que '{PropertyValue}'";
    
    public RascunhoSolicitacaoValidator(IRascunhoSolicitacaoRepository rascunhoSolicitacaoRepository, IUserContext perfilDoUsuario)
    {
        _rascunhoSolicitacaoRepository = rascunhoSolicitacaoRepository;
        _perfilDoUsuario = perfilDoUsuario;
        
        RuleSet(RegrasDeValidacoes.ValidarId, () =>
        {
            RuleFor(x => x.Id)
                .NotNull()
                .WithMessage(ValorNaoPodeSerNulo)
                .NotEmpty()
                .WithMessage(ValorNaoPodeSerVazio);
        });
        
        RuleSet(RegrasDeValidacoes.CampoValidoTemQueSerTrue, () =>
        {
            RuleFor(x => x.Valido)
                .Must(x => (x.HasValue && x.Value) || !x.HasValue)
                .WithMessage("Campo precisa estar nulo ou preenchido com o valor true.");

        });
        
        RuleFor(x => x.Centro)
            .NotNull()
            .WithMessage(ValorNaoPodeSerNulo)
            .NotEmpty()
            .WithMessage(ValorNaoPodeSerVazio);
        
        RuleFor(x => x.Nome)
            .NotNull()
            .WithMessage(ValorNaoPodeSerNulo)
            .NotEmpty()
            .WithMessage(ValorNaoPodeSerVazio)
            .MustAsync(ValidarNomeAsync)
            .WithMessage(ValorJaExiste);
        
        RuleFor(x => x.Proprietario)
            .NotNull()
            .WithMessage(ValorNaoPodeSerNulo)
            .NotEmpty()
            .WithMessage(ValorNaoPodeSerVazio)
            .Must(ValidarProprietario)
            .WithMessage("Você só pode cadastrar rascunho pro seu usuário ou centro selecionado.");

        RuleFor(x => x.DadosDoRascunho)
            .NotNull()
            .WithMessage(ValorNaoPodeSerNulo)
            .NotEmpty()
            .WithMessage(ValorNaoPodeSerVazio)
            .ForEach(x => x.SetValidator(new DadosDoRascunhoSolicitacaoValidator()));

    }

    private async Task<bool> ValidarNomeAsync(RascunhoSolicitacaoDto rascunhoDto, string? nome, CancellationToken cancellationToken)
    {
        if(string.IsNullOrEmpty(nome)) return false;
        
        var rascunhos = await _rascunhoSolicitacaoRepository.ObterRascunhoPorNomeAsync(nome, cancellationToken);

        return !rascunhos.Any(x => x.Proprietario == rascunhoDto.Proprietario && x.Nome == rascunhoDto.Nome);
    }

    private bool ValidarProprietario(RascunhoSolicitacaoDto rascunhoDto, string? proprietario)
    {
        return _perfilDoUsuario.Perfil.Scopes.Any(x => x.Codigo == proprietario) || _perfilDoUsuario.Sid == proprietario;
    }

    private sealed class DadosDoRascunhoSolicitacaoValidator : AbstractValidator<DadosDoRascunhoSolicitacaoDto>
    {
        public DadosDoRascunhoSolicitacaoValidator()
        {
            RuleFor(x => x.Posicao)
                .NotNull()
                .WithMessage(ValorNaoPodeSerNulo)
                .NotEmpty()
                .WithMessage(ValorNaoPodeSerVazio)
                .GreaterThan(0)
                .WithMessage(ValorDeveSerMaiorQueOPreenchido);

            RuleFor(x => x.Dados)
                .NotNull()
                .WithMessage(ValorNaoPodeSerNulo)
                .NotEmpty()
                .WithMessage(ValorNaoPodeSerVazio);
        }
    }

    public static class RegrasDeValidacoes
    {
        public const string ValidarId = "Id";
        public const string CampoValidoTemQueSerTrue = nameof(CampoValidoTemQueSerTrue);
    }
    
}