using FluentValidation;
using ONS.SINapse.Shared.DTO;

namespace ONS.SINapse.Business.Imp.Validators;

public class TagDtoValidator : AbstractValidator<TagDto>
{
    public TagDtoValidator()
    {
        RuleFor(t => t.Nome)
            .NotEmpty()
            .WithMessage("Nome da tag não informado.")
            .MinimumLength(1)
            .WithMessage("Nome da tag deve ter no mínimo {MinLength} caracter.")
            .MaximumLength(40)
            .WithMessage("Nome da tag deve ter no máximo {MaxLength} caracteres.");
    }
}