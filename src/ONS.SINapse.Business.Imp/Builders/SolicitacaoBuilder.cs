using ONS.SINapse.Entities.Entities;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Shared.Factories;

namespace ONS.SINapse.Business.Imp.Builders;

public class SolicitacaoBuilder
{
    public SolicitacaoBuilder() 
    {
        Usuario = new Usuario(string.Empty, string.Empty, string.Empty);
        Origem = new ObjetoDeManobra(string.Empty, string.Empty);
        Destino = new ObjetoDeManobra(string.Empty, string.Empty);
        SistemaDeOrigem = string.Empty;
        CodigoExterno = string.Empty;
        Mensagem = string.Empty;
    }

    private Usuario Usuario { get; set; }
    private ObjetoDeManobra Origem { get; set; }
    private ObjetoDeManobra Destino { get; set; }
    private ObjetoDeManobra? Local { get; set; }
    private ObjetoDeManobra? Agente { get; set; }
    private string? InformacaoAdicional { get; set; }
    private string SistemaDeOrigem { get; set; }
    private string CodigoExterno { get; set; }
    private bool Externo { get; set; }
    private string Mensagem { get; set; }
    private string? LoteId { get; set; }
    private string? Motivo { get; set; }
    private string? SolicitacaoDeOrigemId { get; set; }
    private bool Encaminhada { get; set; }
    private string[] Tags { get; set; } = [];
    public bool RequerAprovacaoEnvio { get; set; }
    public StatusDeSolicitacao Status { get; set; } = StatusDeSolicitacao.Pendente;
    public DateTime? DataInicioCadastro { get; set; }

    public Solicitacao Build(string? id = null)
    {
        var solicitacao = new Solicitacao(
            id ?? IdSolicitacaoFactory.GerarNovoId(Origem.Codigo, Destino.Codigo),
            Usuario,
            Origem,
            Destino,
            Mensagem,
            Tags,
            Solicitacao.SistemaDeOrigemInterno
        )
        {
            Local = Local,
            EncaminharPara = Agente,
            InformacaoAdicional = InformacaoAdicional,
            LoteId = LoteId,
            Motivo = Motivo,
            SolicitacaoDeOrigemId = SolicitacaoDeOrigemId,
            DataInicioCadastro = DataInicioCadastro
        };
        
        if(Externo)
            solicitacao.DefinirComoSolicitacaoExterna(SistemaDeOrigem, CodigoExterno);
        
        solicitacao.SetStatusInicial(Usuario, Status);
        
        return solicitacao;
    }
    
    public SolicitacaoBuilder WithUsuario(Usuario usuario)
    {
        Usuario = usuario;
        return this;
    }
    
    public SolicitacaoBuilder WithOrigem(ObjetoDeManobra origem)
    {
        Origem = origem;
        return this;
    }
    
    public SolicitacaoBuilder WithDestino(ObjetoDeManobra destino)
    {
        Destino = destino;
        return this;
    }
    
    public SolicitacaoBuilder WithInformacaoAdicional(string? informacaoAdicional)
    {
        InformacaoAdicional = informacaoAdicional;
        return this;
    }

    public SolicitacaoBuilder WithMensagem(string mensagem)
    {
        Mensagem = mensagem;
        return this;
    }
    
    public SolicitacaoBuilder WithLoteId(string? loteId)
    {
        LoteId = loteId;
        return this;
    }

    public SolicitacaoBuilder WithSistemaExterno(string sistema, string codigo)
    {
        SistemaDeOrigem = sistema;
        CodigoExterno = codigo;
        Externo = true;
        return this;
    }
    
    
    public SolicitacaoBuilder WithMotivo(string? motivo)
    {
        Motivo = motivo;
        return this;
    }

    public SolicitacaoBuilder WithSolicitacaoDeOrigemId(string? solicitacaoDeOrigemId)
    {
        SolicitacaoDeOrigemId = solicitacaoDeOrigemId;
        return this;
    }
    public SolicitacaoBuilder WithEncaminhada(bool encaminhada)
    {
        Encaminhada = encaminhada;
        return this;
    }
    
    public SolicitacaoBuilder WithRequerAprovacaoEnvio(bool requerAprovacao)
    {
        RequerAprovacaoEnvio = requerAprovacao;
        Status = requerAprovacao ? StatusDeSolicitacao.AguardandoEnvio : StatusDeSolicitacao.Pendente;
        return this;
    }


    public SolicitacaoBuilder WithLocal(ObjetoDeManobra? local)
    {
        Local = local;
        return this;
    }
    
    public SolicitacaoBuilder WithAgente(ObjetoDeManobra? agente)
    {
        Agente = agente;
        return this;
    }

    public SolicitacaoBuilder WithTag(string[] tags)
    {
        Tags = tags;
        return this;
    }

    public SolicitacaoBuilder WithDataInicioCadastro(DateTime? dataInicioCadastro)
    {
        DataInicioCadastro = dataInicioCadastro;
        return this;
    }
}