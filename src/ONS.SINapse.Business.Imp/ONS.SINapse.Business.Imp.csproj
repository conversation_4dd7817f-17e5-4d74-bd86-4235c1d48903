<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="BenchmarkDotNet.Annotations" Version="0.14.0" />
    <PackageReference Include="librdkafka.redist" Version="2.8.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\ONS.SINapse.Business\ONS.SINapse.Business.csproj" />
    <ProjectReference Include="..\ONS.SINapse.Repository.Imp\ONS.SINapse.Repository.Imp.csproj" />
    <ProjectReference Include="..\ONS.SINapse.Repository\ONS.SINapse.Repository.csproj" />
    <ProjectReference Include="..\ONS.SINapse.Shared\ONS.SINapse.Shared.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Compile Remove="Business\AgenteAcessivelBusiness.cs" />
  </ItemGroup>
  
</Project>
