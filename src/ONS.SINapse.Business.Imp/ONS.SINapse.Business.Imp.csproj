<Project Sdk="Microsoft.NET.Sdk">

  <!-- Propriedades específicas do projeto -->
  <PropertyGroup>
    <!-- Propriedades específicas serão herdadas do Directory.Build.props -->
  </PropertyGroup>

  <!-- Pacotes NuGet -->
  <ItemGroup>
    <PackageReference Include="BenchmarkDotNet.Annotations" />
    <PackageReference Include="librdkafka.redist" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\ONS.SINapse.Business\ONS.SINapse.Business.csproj" />
    <ProjectReference Include="..\ONS.SINapse.Repository.Imp\ONS.SINapse.Repository.Imp.csproj" />
    <ProjectReference Include="..\ONS.SINapse.Repository\ONS.SINapse.Repository.csproj" />
    <ProjectReference Include="..\ONS.SINapse.Shared\ONS.SINapse.Shared.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Compile Remove="Business\AgenteAcessivelBusiness.cs" />
  </ItemGroup>
  
</Project>
