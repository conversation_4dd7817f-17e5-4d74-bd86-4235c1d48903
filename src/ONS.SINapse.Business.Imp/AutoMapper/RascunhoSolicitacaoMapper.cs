using AutoMapper;
using ONS.SINapse.Business.Imp.AutoMapper.Converters;
using ONS.SINapse.Entities.Entities;
using ONS.SINapse.Shared.DTO.Firebase;

namespace ONS.SINapse.Business.Imp.AutoMapper;

public class RascunhoSolicitacaoMapper : Profile
{
    public RascunhoSolicitacaoMapper()
    {
        CreateMap<DadosDoRascunhoSolicitacaoDto, DadosDoRascunhoSolicitacao>()
            .ConstructUsing(ctor => new DadosDoRascunhoSolicitacao(ctor.Posicao!.Value, ctor.Dados!))
            .IgnoreAllPropertiesWithAnInaccessibleSetter();

        CreateMap<RascunhoSolicitacaoDto, RascunhoSolicitacao>()
            .ConvertUsing<RascunhoSolicitacaoDtoConverter>();
    }
}