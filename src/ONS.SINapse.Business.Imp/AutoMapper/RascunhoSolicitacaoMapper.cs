using AutoMapper;
using ONS.SINapse.Entities.Entities;
using ONS.SINapse.Shared.DTO.Firebase;

namespace ONS.SINapse.Business.Imp.AutoMapper;

public class RascunhoSolicitacaoMapper : Profile
{
    public RascunhoSolicitacaoMapper()
    {
        CreateMap<DadosDoRascunhoSolicitacaoDto, DadosDoRascunhoSolicitacao>()
            .ConstructUsing(ctor => new DadosDoRascunhoSolicitacao(ctor.Posicao!.Value, ctor.Dados!))
            .IgnoreAllPropertiesWithAnInaccessibleSetter();

        CreateMap<RascunhoSolicitacaoDto, RascunhoSolicitacao>()
            .ConstructUsing(ctor =>
                ctor.Id.HasValue
                    ? new RascunhoSolicitacao(ctor.Id.Value, ctor.Nome!, ctor.Proprietario!, ctor.Centro!)
                    : new RascunhoSolicitacao(ctor.Nome!, ctor.Proprietario!, ctor.Centro!)
            )
            .IgnoreAllPropertiesWithAnInaccessibleSetter()
            .AfterMap((dto, entity, context) =>
                {
                    entity.AdicionarDadosDeRascunho(
                        context.Mapper.Map<List<DadosDoRascunhoSolicitacao>>(dto.DadosDoRascunho));

                    if (dto.Valido is false)
                        entity.Invalidar();
                }
            );
    }
}