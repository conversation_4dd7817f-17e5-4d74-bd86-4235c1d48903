using AutoMapper;
using ONS.SINapse.Entities.Entities;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Extensions;

namespace ONS.SINapse.Business.Imp.AutoMapper.Converters;

public class ComunicadoDetalheBaseDtoConverter<TDto> : ITypeConverter<Comunicado, TDto>
    where TDto : ComunicadoDetalheBaseDto, new()
{
    public TDto Convert(Comunicado source, TDto destination, ResolutionContext context)
    {
        return new TDto
        {
            Id = source.Id,
            Titulo = source.Titulo,
            Mensagem = source.Mensagem,
            EnviadoPor = source.UsuarioDeCriacao.Nome,
            LoginUsuario = source.UsuarioDeCriacao.Login,
            Sid =  source.UsuarioDeCriacao.Sid,
            TipoDeComunicado = source.TipoDeComunicado,
            Origem = new ObjetoDeManobraDto(source.Origem.Codigo, source.Origem.Nome),
            EnviadoPara = source.TipoDeComunicado.GetDescription(),
            PossuiComplemento = source.Complementos?.Count > 0,
            DataDeEnvio = source.CreatedAt,
            DataDeAtualizacao = source.UpdatedAt,
            Complementos = GetComplementosDto(source),
            Leitores = GetLeitoresDto(source),
            QuantidadeDeLeitores = source.Leitores.Count,
            Destinatarios = GetDestinatarios(source)
        };
    }

    private static List<DestinatarioDto> GetDestinatarios(Comunicado comunicado)
    {
        return comunicado.Destinatarios?.Select(destinatario => new DestinatarioDto(
            destinatario.Codigo, 
            destinatario.NomeCurto, 
            destinatario.Centro, 
            destinatario.NomeAreasEletricas != null ? string.Join(',', destinatario.NomeAreasEletricas) : "")
        ).ToList() ?? [];
    }
    private static List<ComplementoComunicadoDto> GetComplementosDto(Comunicado comunicado)
    {
        var complementos = comunicado.Complementos;
        return complementos?.Select(complemento => new ComplementoComunicadoDto
        {
            Mensagem = complemento.Mensagem,
            DataEnvio = complemento.DataDoComplemento
        }).ToList() ?? new List<ComplementoComunicadoDto>();
    }
    
    private static List<LeitorComunicadoDto> GetLeitoresDto(Comunicado comunicado)
    {
        var leitores = comunicado.Leitores;
        return leitores?.Select(leitor => new LeitorComunicadoDto
        (
            leitor.Usuario.Login,
            leitor.Usuario.Sid,
            leitor.Usuario.Nome,
            leitor.Centros.Select(x => new ObjetoDeManobraDto(x.Codigo, x.Nome)).ToList(),
            leitor.DataCriacao,
            leitor.DataDoComunicado
        )).ToList() ?? new List<LeitorComunicadoDto>();
    }
}