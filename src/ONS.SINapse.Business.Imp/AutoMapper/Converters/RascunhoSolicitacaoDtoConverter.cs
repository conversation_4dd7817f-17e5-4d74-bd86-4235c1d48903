using AutoMapper;
using ONS.SINapse.Entities.Entities;
using ONS.SINapse.Shared.DTO.Firebase;
using ONS.SINapse.Shared.Identity;

namespace ONS.SINapse.Business.Imp.AutoMapper.Converters;

public class RascunhoSolicitacaoDtoConverter : ITypeConverter<RascunhoSolicitacaoDto, RascunhoSolicitacao>
{
    private readonly IUserContextAccessor _userContextAccessor;

    public RascunhoSolicitacaoDtoConverter(IUserContextAccessor userContextAccessor)
    {
        _userContextAccessor = userContextAccessor;
    }

    public RascunhoSolicitacao Convert(RascunhoSolicitacaoDto source, RascunhoSolicitacao destination, ResolutionContext context)
    {
        var user = _userContextAccessor.UserContext;
        var atualizadoPor = new AtualizadoPor
        {
            Nome = user.Nome,
            Sid = user.Sid
        };

        var rascunho = source.Id.HasValue
            ? new RascunhoSolicitacao(source.Id.Value, source.Nome!, source.Proprietario!, source.Centro!, atualizadoPor)
            : new RascunhoSolicitacao(source.Nome!, source.Proprietario!, source.Centro!, atualizadoPor);

        var dados = context.Mapper.Map<List<DadosDoRascunhoSolicitacao>>(source.DadosDoRascunho);
        rascunho.AdicionarDadosDeRascunho(dados);

        if (source.Valido is false)
            rascunho.Invalidar();

        return rascunho;
    }
}
