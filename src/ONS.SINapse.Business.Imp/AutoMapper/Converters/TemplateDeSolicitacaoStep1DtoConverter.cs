using AutoMapper;
using ONS.SINapse.Shared.DTO.Templates;

namespace ONS.SINapse.Business.Imp.AutoMapper.Converters;

public class TemplateDeSolicitacaoStep1DtoConverter : ITypeConverter<IEnumerable<TemplateDeSolicitacaoStep1ArquivoDto>, IEnumerable<TemplateDeSolicitacaoStep1Dto>>
{
    public IEnumerable<TemplateDeSolicitacaoStep1Dto> Convert(IEnumerable<TemplateDeSolicitacaoStep1ArquivoDto> source, IEnumerable<TemplateDeSolicitacaoStep1Dto> destination, ResolutionContext context)
    {
        var templatesAgrupados = source
            .OrderBy(template => template.Step1.Order)
            .GroupBy(template => new
            {
                template.Step1.Origin,
                NomeDaPasta = template.NomeDaPasta
            });

        List<TemplateDeSolicitacaoStep1Dto> steps1Dto = new();
        foreach (var templateAgrupado in templatesAgrupados)
        {
            TemplateDeSolicitacaoStep1Dto step1Dto = new(string.Empty, string.Empty);
            step1Dto.Origin = templateAgrupado.Key.Origin.Trim();
            step1Dto.Description = templateAgrupado.Key.NomeDaPasta.Trim();

            foreach (var template in templateAgrupado.ToList())
            {
                List<VariableItem> variableItems = new();
                foreach (var variable in template.Step2.Variables.Items)
                {
                    List<ActionItem> actionItems = new();
                    foreach (var action in variable.Actions.Items)
                    {
                        ActionItem actionItem = new(string.Empty, string.Empty, string.Empty, string.Empty, [])
                        {
                            Description = action.Description.Trim(),
                            Step3 = action.Step3,
                            Step4 = action.Step4,
                            Step5 = action.Step5,
                            Tags = action.Tags ?? Array.Empty<string>()
                        };

                        actionItems.Add(actionItem);
                    }

                    ActionGroup actionGroup = new(
                        variable.Actions.Title.Trim(),
                        actionItems
                    );

                    VariableItem item = new(
                        variable.Description.Trim(),
                        actionGroup
                    );

                    variableItems.Add(item);
                }

                VariableGroup variableGroup = new(template.Step2.Variables.Title.Trim())
                {
                    Items = variableItems
                };

                step1Dto.Items.Add(new TemplateDeSolicitacaoItemDto
                (
                    template.Step1.Description.Trim(),
                    variableGroup
                ));
            }

            steps1Dto.Add(step1Dto);
        }

        return steps1Dto;
    }
}