using AutoMapper;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.DTO.Firebase;
using ONS.SINapse.Shared.Identity;

namespace ONS.SINapse.Business.Imp.AutoMapper.Converters
{
    public class AdicionarVersaoDtoConverter : ITypeConverter<AdicionarVersaoDto, VersaoFirebaseDto>
    {
        private readonly IUserContextAccessor _userContextAccessor;

        public AdicionarVersaoDtoConverter(IUserContextAccessor userContextAccessor)
        {
            _userContextAccessor = userContextAccessor;
        }

        public VersaoFirebaseDto Convert(AdicionarVersaoDto source, VersaoFirebaseDto destination, ResolutionContext context)
        {
            var user = _userContextAccessor.UserContext;
            return new VersaoFirebaseDto(
                DateTime.UtcNow,
                source.Descricao,
                user.Sid,
                user.Nome,
                source.Versao
            );
        }
    }
}
