using AutoMapper;
using ONS.SINapse.Entities.Entities;
using ONS.SINapse.Integracao.Shared.Dtos;

namespace ONS.SINapse.Business.Imp.AutoMapper.Converters
{
    public class UsuarioConverter : ITypeConverter<UsuarioJwtDto, Usuario>
    {
        public Usuario Convert(UsuarioJwtDto source, Usuario destination, ResolutionContext context) => new(
            source.Sid,
            source.Login,
            source.Nome
        );
    }
}
