using AutoMapper;
using Microsoft.Extensions.DependencyInjection;
using ONS.SINapse.Entities.Entities;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Shared.Identity;

namespace ONS.SINapse.Business.Imp.AutoMapper.Converters;

public class ComunicadoItemDtoConverter : ITypeConverter<Comunicado, ComunicadoItemDto>
{
    private readonly IUserContextAccessor _userContextAccessor;

    public ComunicadoItemDtoConverter(IUserContextAccessor userContextAccessor)
    {
        _userContextAccessor = userContextAccessor;
    }
    
    public ComunicadoItemDto Convert(Comunicado source, ComunicadoItemDto destination, ResolutionContext context)
    {
        var userContext = _userContextAccessor.UserContext;
        return new ComunicadoItemDto(source.Id, new ObjetoDeManobraDto(source.Origem.Codigo, source.Origem.Nome), source.Titulo, source.Mensagem)
        {
            DataDeEnvio = source.CreatedAt,
            QuantidadeDeComplementos = source.Complementos?.Count ?? 0,
            Lido = source.IsComunicadoLido(userContext.Usuario())
        };
    }
}