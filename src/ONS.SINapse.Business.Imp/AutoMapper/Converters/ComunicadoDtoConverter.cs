using AutoMapper;
using ONS.SINapse.Entities.Entities;
using ONS.SINapse.Shared.DTO;

namespace ONS.SINapse.Business.Imp.AutoMapper.Converters
{
    public class ComunicadoDtoConverter : ITypeConverter<ComunicadoDto, Comunicado>
    {
        public Comunicado Convert(ComunicadoDto source, Comunicado destination, ResolutionContext context)
        {
            return new Comunicado(new Usuario(string.Empty, string.Empty, string.Empty))
            {
                Titulo = source.Titulo,
                Mensagem = source.Mensagem,
                TipoDeComunicado = source.TipoDeComunicado,
            };
        }
    }
}
