using AutoMapper;
using ONS.SINapse.Entities.Entities;
using ONS.SINapse.Entities.Entities.DadosCadastrais;
using ONS.SINapse.Shared.DTO;

namespace ONS.SINapse.Business.Imp.AutoMapper.Converters;

public class AgenteDisponivelDoPerfilDtoConverter : ITypeConverter<Agente, AgenteDisponivelDoPerfilDto>
{
    public AgenteDisponivelDoPerfilDto Convert(Agente source, AgenteDisponivelDoPerfilDto destination, ResolutionContext context) 
        => new(source.Codigo, source.Nome, source.Centros);
}