using AutoMapper;
using ONS.SINapse.Entities.Entities;
using ONS.SINapse.Shared.DTO.ExtracaoDeDados;
using ONS.SINapse.Shared.Extensions;

namespace ONS.SINapse.Business.Imp.AutoMapper.S3;

public class SolicitacaoS3Mapper : Profile
{
    public SolicitacaoS3Mapper()
    {
        CreateMap<List<Solicitacao>, DadosExtracaoDadoDto>()
            .ConstructUsing(ctor => ConstructDadosAnaliticos(ctor));
    }

    private static DadosExtracaoDadoDto ConstructDadosAnaliticos(List<Solicitacao> solicitacoes)
    {
        var solicitacoesDto = solicitacoes.Select(ConstructSolicitacao).ToList();
        var chatDto = solicitacoes.SelectMany(s => s.Chat.Select(c => ConstructChat(s.Id, c))).ToList();
        var historicosDto = solicitacoes.SelectMany(s => s.HistoricosDeStatus.Select(h => ConstructHistorico(s.Id, h))).ToList();
        
        return new DadosExtracaoDadoDto(solicitacoesDto, chatDto, historicosDto);
    }

    private static HistoricoExtracaoDadoDto ConstructHistorico(string idSolicitacao, HistoricoDeStatusDeSolicitacao historico)
    {
        return new HistoricoExtracaoDadoDto(
            idSolicitacao,
            (short)historico.Status,
            historico.Status.GetDescription(),
            (short?)historico.StatusAnterior,
            historico.StatusAnterior?.GetDescription(),
            historico.Usuario.Sid,
            historico.Usuario.Login,
            historico.Usuario.Nome,
            historico.DataDeAlteracao
        );
    }
    
    private static ChatExtracaoDadoDto ConstructChat(string idSolicitacao, ChatDeSolicitacaoItemDto chat)
    {
        return new ChatExtracaoDadoDto(
            idSolicitacao,
            chat.Mensagem,
            (short?)chat.Status,
            chat.Status?.GetDescription(),
            chat.UsuarioRemetente.Sid,
            chat.UsuarioRemetente.Login,
            chat.UsuarioRemetente.Nome,
            chat.PrimeiraLeitura?.Usuario.Sid,
            chat.PrimeiraLeitura?.Usuario.Login,
            chat.PrimeiraLeitura?.Usuario.Nome,
            chat.PrimeiraLeitura?.Data,
            chat.PrimeiraEntrega?.Usuario.Sid,
            chat.PrimeiraEntrega?.Usuario.Login,
            chat.PrimeiraEntrega?.Usuario.Nome,
            chat.PrimeiraEntrega?.Data,
            chat.DataEHoraDeEnvio,
            chat.DataEHoraDeEntregaAoDestinatario,
            chat.DataEHoraDeLeituraDoDestinatario
        );
    }
    
    private static SolicitacaoExtracaoDadoDto ConstructSolicitacao(Solicitacao solicitacao)
    {
        return new SolicitacaoExtracaoDadoDto(
            solicitacao.Id,
            solicitacao.CodigoExterno,
            solicitacao.SistemaDeOrigem,
            (short)solicitacao.Status,
            solicitacao.Status.GetDescription(),
            solicitacao.UpdatedAt,
            solicitacao.CreatedAt,
            solicitacao.DetalheDoImpedimento,
            solicitacao.InformacaoAdicional,
            solicitacao.Mensagem,
            solicitacao.MensagemNormalizada,
            solicitacao.Motivo,
            solicitacao.Encaminhada,
            solicitacao.IsExterna,
            solicitacao.FinalizadaAutomaticamente,
            solicitacao.LoteId,
            solicitacao.SolicitacaoDeOrigemId,
            solicitacao.Origem.Codigo,
            solicitacao.Origem.Nome,
            solicitacao.Destino.Codigo,
            solicitacao.Destino.Nome,
            solicitacao.EncaminharPara?.Codigo,
            solicitacao.EncaminharPara?.Nome,
            solicitacao.Local?.Codigo,
            solicitacao.Local?.Nome,
            solicitacao.UsuarioDeCriacao.Sid,
            solicitacao.UsuarioDeCriacao.Login,
            solicitacao.UsuarioDeCriacao.Nome,
            solicitacao.Tags.Length > 0 ? string.Join("|", solicitacao.Tags) : null
        );
    }
    
}