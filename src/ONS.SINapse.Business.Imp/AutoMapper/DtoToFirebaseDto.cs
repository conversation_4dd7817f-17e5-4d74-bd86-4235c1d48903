using AutoMapper;
using ONS.SINapse.Business.Imp.AutoMapper.Converters;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.DTO.Firebase;

namespace ONS.SINapse.Business.Imp.AutoMapper
{
    public class DtoToFirebaseDto : Profile
    {
        public DtoToFirebaseDto() 
        { 
            MapDtoToFirebaseDto();
        }

        private void MapDtoToFirebaseDto()
        {
            CreateMap<AdicionarVersaoDto, VersaoFirebaseDto>()
                .ConvertUsing<AdicionarVersaoDtoConverter>();
        }
    }
}
