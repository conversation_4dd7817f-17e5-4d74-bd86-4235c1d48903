using AutoMapper;
using ONS.SINapse.Business.Imp.AutoMapper.Converters;
using ONS.SINapse.Entities.Entities;
using ONS.SINapse.Entities.Entities.DadosCadastrais;
using ONS.SINapse.Integracao.Shared.Dtos;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.DTO.Templates;

namespace ONS.SINapse.Business.Imp.AutoMapper;

public class EntityToDto : Profile
{
    public EntityToDto()
    {
        MapEntityToDto();
        MapDtoToEntity();
    }

    private void MapDtoToEntity()
    {
        CreateMap<ComunicadoDto, Comunicado>().ConvertUsing<ComunicadoDtoConverter>();
    }

    private void MapEntityToDto()
    {
        CreateMap<Comunicado, ComunicadoItemDto>().ConvertUsing<ComunicadoItemDtoConverter>();
        CreateMap<Comunicado, ComunicadoDetalheDto>().ConvertUsing<ComunicadoDetalheBaseDtoConverter<ComunicadoDetalheDto>>();
        CreateMap<Agente, AgenteDisponivelDoPerfilDto>().ConvertUsing<AgenteDisponivelDoPerfilDtoConverter>();
        CreateMap<UsuarioJwtDto, Usuario>().ConvertUsing<UsuarioConverter>();
        CreateMap<IEnumerable<TemplateDeSolicitacaoStep1ArquivoDto>, IEnumerable<TemplateDeSolicitacaoStep1Dto>>().ConvertUsing<TemplateDeSolicitacaoStep1DtoConverter>();
    }

}