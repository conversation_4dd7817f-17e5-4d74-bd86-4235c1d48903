using ONS.SINapse.Business.IBusiness;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Notifications;
using ONS.SINapse.Shared.Services.Firebase;
using ONS.SINapse.Business.Imp.Validators;
using ONS.SINapse.Repository.IRepository.DadosCadastrais;
using ONS.SINapse.Shared.DTO.Firebase;
using ONS.SINapse.Shared.Identity;

namespace ONS.SINapse.Business.Imp.Business;

public class VisaoDeUsuarioBusiness : IVisaoDeUsuarioBusiness
{
    private const string CentroAgenteNaoPertenceAoPerfil = "Centro agente da visão não pertence ao perfil do usuário.";
    private const string NomeDaVisaoEmUso = "Nome da visão já está em uso.";
    private readonly NotificationContext _notificationContext;
    private readonly IVisaoUsuarioFirebaseDatabaseService _visaoUsuarioFirebaseDatabaseService;
    private readonly IAgenteRepository _agenteRepository;
    private readonly IUserContext _userContext;

    public VisaoDeUsuarioBusiness(
        IVisaoUsuarioFirebaseDatabaseService visaoUsuarioFirebaseDatabaseService,
        IAgenteRepository agenteRepository,
        IUserContext userContext,
        NotificationContext notificationContext)
    {
        _visaoUsuarioFirebaseDatabaseService = visaoUsuarioFirebaseDatabaseService;
        _agenteRepository = agenteRepository;
        _userContext = userContext;
        _notificationContext = notificationContext;
    }

    public async Task<IDictionary<string, VisaoUsuarioRealtimeDto>> VisoesEmUsoAsync(string nome, string codigoCentroAgente, CancellationToken cancellationToken)
    {
        return await _visaoUsuarioFirebaseDatabaseService.VisoesEmUsoAsync(nome, codigoCentroAgente, cancellationToken);
    }
    
    public async Task<VisaoDeUsuarioDto> AddAsync(VisaoDeUsuarioDto dto, CancellationToken cancellationToken)
    {
        if (!await ValidateDtoAsync(dto, cancellationToken))
            return dto;
        
        var visoes = await VisoesEmUsoAsync(dto.Nome, dto.CodigoCentroAgente, cancellationToken);

        if (visoes.Any())
        {
            _notificationContext.AddNotification(NomeDaVisaoEmUso);
            return dto;
        }

        if (CentroAgenteNaoPertenceAoPerfilDoUsuario(dto.CodigoCentroAgente))
        {
            _notificationContext.AddNotification(CentroAgenteNaoPertenceAoPerfil);
            return dto;
        }

        var document = await ConvertVisaoUsuarioRealtimeDtoAsync(Guid.NewGuid().ToString(), dto, cancellationToken);
        await _visaoUsuarioFirebaseDatabaseService.SetAsync(document, cancellationToken);

        return dto;
    }

    public async Task<VisaoDeUsuarioDto> UpdateAsync(string id, string codigoCentroAgenteAtual, VisaoDeUsuarioDto dto,
        CancellationToken cancellationToken)
    {
        if (!await ValidateDtoAsync(dto, cancellationToken))
            return dto;
        
        if (CentroAgenteNaoPertenceAoPerfilDoUsuario(dto.CodigoCentroAgente))
        {
            _notificationContext.AddNotification(CentroAgenteNaoPertenceAoPerfil);
            return dto;
        }

        var visoes = await VisoesEmUsoAsync(dto.Nome, dto.CodigoCentroAgente, cancellationToken);

        if (visoes.Any(x => x.Key != id))
        {
            _notificationContext.AddNotification(NomeDaVisaoEmUso);
            return dto;
        }
        
        var document = await ConvertVisaoUsuarioRealtimeDtoAsync(id, dto, cancellationToken);
        
        if(dto.CodigoCentroAgente != codigoCentroAgenteAtual)
            await _visaoUsuarioFirebaseDatabaseService.DeleteAsync(id, codigoCentroAgenteAtual, cancellationToken);

        await _visaoUsuarioFirebaseDatabaseService.SetAsync(document, cancellationToken);

        return dto;
    }

    public async Task DeleteAsync(string id, string codigoCentroAgente, CancellationToken cancellationToken)
    {
        if (CentroAgenteNaoPertenceAoPerfilDoUsuario(codigoCentroAgente))
        {
            _notificationContext.AddNotification(CentroAgenteNaoPertenceAoPerfil);
            return;
        }

        await _visaoUsuarioFirebaseDatabaseService.DeleteAsync(id, codigoCentroAgente, cancellationToken);
    }


    private async Task<bool> ValidateDtoAsync(VisaoDeUsuarioDto dto, CancellationToken cancellationToken)
    {
        var validator = new VisaoDeUsuarioDtoValidator();
        var result = await validator.ValidateAsync(dto, cancellationToken);
        if (!result.IsValid)
            _notificationContext.AddNotifications(result);
        return result.IsValid;
    }

    private bool CentroAgenteNaoPertenceAoPerfilDoUsuario(string codigoCentroAgente)
        => !CentroAgentePertenceAoPerfilDoUsuario(codigoCentroAgente);
    
    private bool CentroAgentePertenceAoPerfilDoUsuario(string codigoCentroAgente)
    {
        return _userContext.Perfil.Scopes.Any(x =>
            x.Codigo.Equals(codigoCentroAgente, StringComparison.CurrentCultureIgnoreCase));
    }

    private async Task<VisaoUsuarioRealtimeDto> ConvertVisaoUsuarioRealtimeDtoAsync(string id, VisaoDeUsuarioDto dto, CancellationToken cancellationToken)
    {
        var codigoCentroAgente = _userContext.Centros.FirstOrDefault(x =>
            x.Equals(dto.CodigoCentroAgente, StringComparison.CurrentCultureIgnoreCase));

        var agente = await _agenteRepository
            .GetOneAsync(x => x.Codigo.Equals(codigoCentroAgente, StringComparison.CurrentCultureIgnoreCase),
                cancellationToken).ConfigureAwait(false);

        var centro = CentrosDeOperacoes.GetCentroDeOperacaoByCodigo(codigoCentroAgente);
        
        var nomeCentroAgente = agente?.Nome ?? centro?.Nome ?? codigoCentroAgente ?? string.Empty;
        
        return new VisaoUsuarioRealtimeDto(
            _userContext.Sid, id, dto.Nome, dto.CodigoCentroAgente, nomeCentroAgente,
            dto.EquipamentosDeManobra
                .Select(f => 
                    new EquipamentoVisaoUsuarioRealtimeDto(
                        f.Valor, 
                        f.Descricao, 
                        new TipoDeEquipamentoVisaoUsuarioRealtimeDto(f.Tipo.Codigo, f.Tipo.Descricao)))
                .ToList(),
            dto.Tags
                .Select(f => 
                    new EquipamentoVisaoUsuarioRealtimeDto(
                        f.Valor, 
                        f.Descricao, 
                        new TipoDeEquipamentoVisaoUsuarioRealtimeDto(f.Tipo.Codigo, f.Tipo.Descricao)))
                .ToList()
        );
    }
}