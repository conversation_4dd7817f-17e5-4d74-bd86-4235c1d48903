using ONS.SINapse.Business.IBusiness;
using ONS.SINapse.Business.IBusiness.Firebase;
using ONS.SINapse.Entities.Entities;
using ONS.SINapse.Shared.DTO.Firebase;
using ONS.SINapse.Shared.Enums;

namespace ONS.SINapse.Business.Imp.Business;

public class NotificacaoDeComunicadoBusiness : INotificacaoDeComunicadoBusiness
{
    private readonly INotificacaoFirebaseBusiness _notificacaoFirebaseBusiness;
    private const string TituloNotificacaoNovo = "Novo comunicado";
    private const string TituloNotificacaoComplemento = "Comunicado atualizado";
    public NotificacaoDeComunicadoBusiness(INotificacaoFirebaseBusiness notificacaoFirebaseBusiness)
    {
        _notificacaoFirebaseBusiness = notificacaoFirebaseBusiness;
    }

    public async Task NotificarComunicadoAsync(Comunicado comunicado, string sid, CancellationToken cancellationToken)
    {
        var titulo = comunicado.HasComplemento ? TituloNotificacaoComplemento : TituloNotificacaoNovo;
        await SendNotificationAsync(comunicado, titulo, cancellationToken);
    }

    private Task SendNotificationAsync(Comunicado comunicado, string titulo, CancellationToken cancellationToken)
    {
        var notificacoes = new List<NotificacaoRealtimeDto>();

        if (comunicado.TipoDeComunicado == TipoDeComunicado.Todos)
        {
            notificacoes.Add(new NotificacaoRealtimeDto(NotificacaoRealtimeDto.TodosDevemSerNotificados, titulo, comunicado.Titulo, nameof(Comunicado)));
        }
        else
        {
            var destinos = new List<string> { comunicado.Origem.Codigo };

            var destinatarios = comunicado.Destinatarios?.Select(x => x.Codigo);
                
            if(destinatarios is not null)
                destinos.AddRange(destinatarios);

            notificacoes = destinos
                .Select(destino => new NotificacaoRealtimeDto(destino, titulo, comunicado.Titulo, nameof(Comunicado)))
                .ToList();
        }

        return _notificacaoFirebaseBusiness.SendAsync(notificacoes, cancellationToken);
    }
}