using ONS.SINapse.Business.IBusiness;
using ONS.SINapse.Shared.DTO;

namespace ONS.SINapse.Business.Imp.Business;

public class AgenteOnlineBusiness : IAgenteOnlineBusiness
{
    private readonly IRespostaDaChamadaBusiness _respostaDaChamadaBusiness;

    public AgenteOnlineBusiness(
        IRespostaDaChamadaBusiness respostaDaChamadaBusiness)
    {
        _respostaDaChamadaBusiness = respostaDaChamadaBusiness;
    }
    
    public async Task<IEnumerable<AgenteOnlineDto>> ObterAgentesOnlineAsync(CancellationToken cancellationToken)
    {
        var centroAgentesOnline = await _respostaDaChamadaBusiness.ObterAgentesOnline(cancellationToken).ConfigureAwait(false);
        return centroAgentesOnline.Select(x => new AgenteOnlineDto(x.CentroAgente.Codigo, x.Equipamentos));
    }

    public async Task<string[]> ObterCodigoAgentesOnlineAsync(CancellationToken cancellationToken)
    {
        return 
            await _respostaDaChamadaBusiness.ObterCodigoAgentesOnlineAsync(cancellationToken)
                .ConfigureAwait(false);
    }

    public async Task<IEnumerable<string>> BuscarAgentesInativosAsync(string[] codigoCentroAgente, CancellationToken cancellationToken)
    {
        return
            await _respostaDaChamadaBusiness.ObterCentroAgentesInativos(codigoCentroAgente, cancellationToken)
                .ConfigureAwait(false);
    }

    private bool _disposed;
    protected virtual void Dispose(bool disposing)
    {
        if (_disposed) return;
        _disposed = true;
    }
    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }
}