using AutoMapper;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using ONS.SINapse.Business.IBusiness.TemplatesDeSolicitacao;
using ONS.SINapse.Shared.DTO.Templates;
using ONS.SINapse.Shared.Notifications;
using ONS.SINapse.Shared.Services.Caching;
using ONS.SINapse.Shared.Settings;

namespace ONS.SINapse.Business.Imp.Business.TemplatesDeSolicitacao;

public class TemplateDeSolicitacaoStep3Business 
    : TemplateDeSolicitacaoBaseBusiness<TemplateDeSolicitacaoStep3Business, TemplateDeSolicitacaoStep3ArquivoDto>, ITemplateDeSolicitacaoStep3Business
{
    private readonly ILogger _logger;
    private readonly NotificationContext _notificationContext;

    public TemplateDeSolicitacaoStep3Business(ICacheService cacheService,
       IOptions<TemplatesSettings> templatesSettings, 
       I<PERSON>apper mapper, 
       ILogger<TemplateDeSolicitacaoStep3Business> logger,
       NotificationContext notificationContext
       ) : base(cacheService, templatesSettings, mapper, logger)
    {
        _logger = logger;
        _notificationContext = notificationContext;
    }

    protected override string Identificador => "step-3";
    protected override async Task SalvarNoCacheAsync(
        IEnumerable<TemplateDeSolicitacaoStep3ArquivoDto> templateArquivoDtos, CancellationToken cancellationToken)
    {    
        var templatesTask = templateArquivoDtos
            .Select(template => CacheService.SetAsync(
                CacheKeyComTemplateId(template.Id), template.Step3, cancellationToken))
            .ToList();
        
        await Task.WhenAll(templatesTask);
    }
    
    private string CacheKeyComTemplateId(string templateId) => $"{_cacheKey}:{templateId}";
    
    public async Task<TemplateDeSolicitacaoStep3Dto> ObterPorIdAsync(string templateId, CancellationToken cancellationToken)
    {
        var result = await CacheService.GetAsync<TemplateDeSolicitacaoStep3Dto>(
            CacheKeyComTemplateId(templateId), cancellationToken);
        
        if (result is null)
        {
            _notificationContext.AddNotification($"Template {templateId} não localizado.");
            _logger.LogError("Template {templateId} não localizado.", templateId);
            return new TemplateDeSolicitacaoStep3Dto([]);
        }

        return result;
    }
}