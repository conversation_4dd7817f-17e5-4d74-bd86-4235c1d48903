using System.IO.Compression;
using System.Text;
using ONS.SINapse.Business.IBusiness.TemplatesDeSolicitacao;
using ONS.SINapse.CacheSync.Services;
using ONS.SINapse.Shared.DTO;

namespace ONS.SINapse.Business.Imp.Business.TemplatesDeSolicitacao;

public class ExportacaoDatasetViewsBusiness(ISinapseDadosDatasetService dadosDatasetService)
    : IExportacaoDatasetViewsBusiness
{
    public async Task<Stream> ExportarAsync(CancellationToken cancellationToken)
    {
        var views = await ObterViewsAsync(cancellationToken);

        var zipStream = new MemoryStream();

        using (var archive = new ZipArchive(zipStream, ZipArchiveMode.Create, leaveOpen: true))
        {
            foreach (var view in views)
            {
                var fileName = $"{view.Name}.sql";
                var entryPath = $"views/{fileName}";
                var entry = archive.CreateEntry(entryPath, CompressionLevel.Optimal);

                await using var entryStream = entry.Open();
                await using var writer = new StreamWriter(entryStream, Encoding.UTF8);
                await writer.WriteAsync(view.Definition);
            }
        }

        zipStream.Position = 0;
        return zipStream;
    }

    public async Task<ICollection<DatasetViewsDot>> ObterViewsAsync(CancellationToken cancellationToken)
    {
        return await dadosDatasetService.GetDatasetViews(cancellationToken);
    }
}