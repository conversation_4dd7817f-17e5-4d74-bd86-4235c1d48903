using AutoMapper;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using ONS.SINapse.Business.IBusiness.TemplatesDeSolicitacao;
using ONS.SINapse.Shared.DTO.Templates;
using ONS.SINapse.Shared.Notifications;
using ONS.SINapse.Shared.Services.Caching;
using ONS.SINapse.Shared.Settings;

namespace ONS.SINapse.Business.Imp.Business.TemplatesDeSolicitacao;

public class TemplateDeSolicitacaoStep4Business 
    : TemplateDeSolicitacaoBaseBusiness<TemplateDeSolicitacaoStep4Business, TemplateDeSolicitacaoStep4ArquivoDto>, ITemplateDeSolicitacaoStep4Business
{
    private readonly ILogger _logger;
    private readonly NotificationContext _notificationContext;

    public TemplateDeSolicitacaoStep4Business(ICacheService cacheService,
       IOptions<TemplatesSettings> templatesSettings, 
       I<PERSON>apper mapper, 
       ILogger<TemplateDeSolicitacaoStep4Business> logger,
       NotificationContext notificationContext
       ) : base(cacheService, templatesSettings, mapper, logger)
    {
        _logger = logger;
        _notificationContext = notificationContext;
    }

    protected override string Identificador => "step-4";
    
    protected override async Task SalvarNoCacheAsync(
        IEnumerable<TemplateDeSolicitacaoStep4ArquivoDto> templateArquivoDtos, CancellationToken cancellationToken)
    {    
        var templatesTask = templateArquivoDtos
            .Select(template => CacheService.SetAsync(
                CacheKeyComTemplateId(template.Id), template.Step4, cancellationToken))
            .ToList();
        
        await Task.WhenAll(templatesTask);
    }
    
    private string CacheKeyComTemplateId(string templateId) => $"{_cacheKey}:{templateId}";
    
    public async Task<TemplateDeSolicitacaoStep4Dto> ObterPorIdAsync(string templateId, CancellationToken cancellationToken)
    {
        var result = await CacheService.GetAsync<TemplateDeSolicitacaoStep4Dto>(
            CacheKeyComTemplateId(templateId), cancellationToken);
        
        if (result is null)
        {
            _notificationContext.AddNotification($"Template {templateId} não localizado.");
            _logger.LogError("Template {templateId} não localizado.", templateId);
            return new TemplateDeSolicitacaoStep4Dto([]);
        }

        return result;
    }
}