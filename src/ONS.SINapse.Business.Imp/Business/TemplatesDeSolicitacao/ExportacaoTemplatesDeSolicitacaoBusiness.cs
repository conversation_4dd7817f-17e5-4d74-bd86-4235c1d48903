using System.IO.Compression;
using Microsoft.Extensions.Options;
using ONS.SINapse.Business.IBusiness.TemplatesDeSolicitacao;
using ONS.SINapse.Shared.Settings;

namespace ONS.SINapse.Business.Imp.Business.TemplatesDeSolicitacao;

public class ExportacaoTemplatesDeSolicitacaoBusiness : IExportacaoTemplatesDeSolicitacaoBusiness
{
    private readonly TemplatesSettings _templatesSettings;

    public const string MensagemArquivoNaoExiste = "Não foi possivel encontrar diretório dos templates";
    
    public ExportacaoTemplatesDeSolicitacaoBusiness(IOptions<TemplatesSettings> templatesSettings)
    {
        _templatesSettings = templatesSettings.Value;
    }
    
    public async Task<Stream> ExportarAsync(CancellationToken cancellationToken)
    {
        var caminhoDosTemplates = ObterCaminhoDosTemplates();
        
        var stream = new MemoryStream();
        
        stream = await CompactarTemplatesAsync(stream, caminhoDosTemplates, cancellationToken);
        stream.Position = 0;
        return stream;
    }

    private string ObterCaminhoDosTemplates()
    {
        var caminhoDosTemplates =
            Path.Combine(Directory.GetCurrentDirectory(), _templatesSettings.DiretorioDosTemplates); 
        
        if(!Directory.Exists(caminhoDosTemplates))
            throw new DirectoryNotFoundException($"{MensagemArquivoNaoExiste} no caminho {caminhoDosTemplates}.");
        
        return caminhoDosTemplates;
    }

    private static async Task<MemoryStream> CompactarTemplatesAsync(MemoryStream stream, string caminhoDosTemplates, CancellationToken cancellationToken)
    {
        using var zip = new ZipArchive(stream, ZipArchiveMode.Create, leaveOpen: true);
        
        foreach (var filePath in Directory.EnumerateFiles(caminhoDosTemplates, "*", SearchOption.AllDirectories))
        {
            // Calcula o nome relativo do arquivo dentro do ZIP Ex: template/step-1/cn-geração
            var entryName = Path.GetRelativePath(caminhoDosTemplates, filePath);
                    
            // Adiciona o arquivo ao ZIP
            var entry = zip.CreateEntry(entryName);

            // Copia o conteúdo do arquivo para o entry
            await using var entryStream = entry.Open();
            await using var fileStream = File.OpenRead(filePath);
            await fileStream.CopyToAsync(entryStream, cancellationToken);
        }
        
        return stream;
    }
}