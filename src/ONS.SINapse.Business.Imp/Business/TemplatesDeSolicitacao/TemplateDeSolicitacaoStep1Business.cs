using AutoMapper;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using ONS.SINapse.Business.IBusiness.TemplatesDeSolicitacao;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.DTO.Templates;
using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Shared.Services;
using ONS.SINapse.Shared.Services.Caching;
using ONS.SINapse.Shared.Settings;

namespace ONS.SINapse.Business.Imp.Business.TemplatesDeSolicitacao;

public class TemplateDeSolicitacaoStep1Business 
    : TemplateDeSolicitacaoBaseBusiness<TemplateDeSolicitacaoStep1Business, TemplateDeSolicitacaoStep1ArquivoDto>, ITemplateDeSolicitacaoStep1Business
{
    private readonly ICorretorOrtograficoService _corretorOrtograficoService;

    public TemplateDeSolicitacaoStep1Business(
        ICacheService cacheService,
        IOptions<TemplatesSettings> templatesSettings,
        IMapper mapper,
        ICorretorOrtograficoService corretorOrtograficoService,
        ILogger<TemplateDeSolicitacaoStep1Business> logger) 
        : base(cacheService, templatesSettings, mapper, logger)
    {
        _corretorOrtograficoService = corretorOrtograficoService;
    }

    protected override string Identificador => "step-1";
    protected override async Task SalvarNoCacheAsync(IEnumerable<TemplateDeSolicitacaoStep1ArquivoDto> templateArquivoDtos, CancellationToken cancellationToken)
    {
        var templates = Mapper.Map<IEnumerable<TemplateDeSolicitacaoStep1Dto>>(templateArquivoDtos);
        var templatesTask = templates
            .GroupBy(template => template.Origin)
            .Select(template => CacheService.SetAsync(CacheKeyPorOrigem(template.Key), template.ToList(), cancellationToken))
            .ToList();
        
        await Task.WhenAll(templatesTask);
    }

    public async Task<IEnumerable<TemplateDeSolicitacaoStep1Dto>> ObterPorOrigemAsync(string centro, CancellationToken cancellationToken)
    {
        if (string.IsNullOrEmpty(centro))
            return Array.Empty<TemplateDeSolicitacaoStep1Dto>();

        var centroOperacao = CentrosDeOperacoes.GetCentroDeOperacaoByCodigo(centro);
        
        var templates = await CacheService.GetAsync<IEnumerable<TemplateDeSolicitacaoStep1Dto>>(
            CacheKeyPorOrigem(centroOperacao?.Nome ?? string.Empty), cancellationToken)
                ?? Array.Empty<TemplateDeSolicitacaoStep1Dto>();
        return templates
            .OrderBy(t => t.Description, StringComparer.OrdinalIgnoreCase)
            .Select(t =>
            {
                var description = t.Description.RemoveNonLetter();
                t.Description = _corretorOrtograficoService.CorrigirOrtografia(description);
                return t;
            }).ToList();
    }

    private string CacheKeyPorOrigem(string origem) => $"{_cacheKey}:{origem}";
}