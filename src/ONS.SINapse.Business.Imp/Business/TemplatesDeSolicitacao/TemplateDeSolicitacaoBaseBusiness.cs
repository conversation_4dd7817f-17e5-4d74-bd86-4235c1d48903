using AutoMapper;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using ONS.SINapse.Business.IBusiness.TemplatesDeSolicitacao;
using ONS.SINapse.Shared.DTO.Templates;
using ONS.SINapse.Shared.Services.Caching;
using ONS.SINapse.Shared.Settings;

namespace ONS.SINapse.Business.Imp.Business.TemplatesDeSolicitacao;

public abstract class TemplateDeSolicitacaoBaseBusiness<TBusiness, TArquivoDto> : ITemplateDeSolicitacaoBusiness
    where TBusiness : class
    where TArquivoDto : TemplateArquivoDto
{
    private const string ExtensaoPermitida = "json";
    
    protected readonly ICacheService CacheService;
    protected readonly IMapper Mapper;
    private readonly TemplatesSettings _templatesSettings;
    private readonly ILogger<TBusiness> _logger;

    protected TemplateDeSolicitacaoBaseBusiness(
        ICacheService cacheService,
        IOptions<TemplatesSettings> templatesSettings,
        IMapper mapper,
        ILogger<TBusiness> logger)
    {
        CacheService = cacheService;
        Mapper = mapper;
        _templatesSettings = templatesSettings.Value;
        _logger = logger;
    }

    protected abstract string Identificador { get; }
    protected abstract Task SalvarNoCacheAsync(IEnumerable<TArquivoDto> templateArquivoDtos, CancellationToken cancellationToken);
    protected string _cacheKey => $"templates:{Identificador}";

    public async Task RegistrarAsync(CancellationToken cancellationToken)
    {
        try
        {
            var templatesDeArquivosDto = await ObterTemplatesDoArquivo(cancellationToken);
            if (templatesDeArquivosDto.Count == 0) return;

            _logger.LogInformation("[TEMPLATE] Registrando templates no cache");
            
            await SalvarNoCacheAsync(templatesDeArquivosDto, cancellationToken);
        }
        catch(Exception exception)
        {
            _logger.LogError("[TEMPLATE] Não foi possível carregar os templates do {Identificador} ({Message})", Identificador, exception.Message);
        }
    }

    private async Task<IReadOnlyCollection<TArquivoDto>> ObterTemplatesDoArquivo(
        CancellationToken cancellationToken)
    {
        var diretorios = Directory.GetDirectories(_templatesSettings.DiretorioDosTemplates);
        List<TArquivoDto> templates = new();

        foreach (var diretorio in diretorios)
        {
            string nomeDaPasta = Path.GetFileName(diretorio);
            string[] caminhos = Directory.GetFiles(diretorio, $"*{Identificador}.{ExtensaoPermitida}");
            
            foreach (var caminho in caminhos)
            {
                try
                {   
                    TArquivoDto template = (await LerArquivoAsync(nomeDaPasta, caminho, cancellationToken))!;
                    templates.Add(template);
                }
                catch(Exception exception)
                {
                    _logger.LogError("[TEMPLATE] Erro ao carregar o template: {caminho} ({Message})", 
                        caminho, exception.Message);
                }
            }
        }

        return templates.OrderBy(t => t.NomeDaPasta, StringComparer.OrdinalIgnoreCase).ToList();
    }

    private static async Task<TArquivoDto?> LerArquivoAsync(
        string nomeDaPasta, string caminhoDoArquivo, CancellationToken cancellationToken)
    {
        string nomeDoArquivo = Path.GetFileName(caminhoDoArquivo);
        string conteudo = await File.ReadAllTextAsync(caminhoDoArquivo, cancellationToken);
        TArquivoDto? dto = JsonConvert.DeserializeObject<TArquivoDto>(conteudo);

        if (dto == null) return null;

        dto.NomeDaPasta = nomeDaPasta;
        dto.NomeDoArquivo = nomeDoArquivo;
        return dto;
    }
}