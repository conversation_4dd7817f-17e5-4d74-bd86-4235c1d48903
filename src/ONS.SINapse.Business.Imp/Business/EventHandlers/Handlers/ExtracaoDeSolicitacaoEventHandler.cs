using MassTransit;
using Microsoft.Extensions.Logging;
using ONS.SINapse.Business.IBusiness;
using ONS.SINapse.Business.Imp.Business.EventHandlers.Events;

namespace ONS.SINapse.Business.Imp.Business.EventHandlers.Handlers;

public class ExtracaoDeSolicitacaoEventHandler : IConsumer<ExtracaoDeSolicitacaoEvent>
{
    private readonly ILogger<ExtracaoDeSolicitacaoEventHandler> _logger;
    private readonly IExtracaoDeSolicitacaoS3Business _extracaoDeSolicitacaoS3Business;

    public ExtracaoDeSolicitacaoEventHandler(ILogger<ExtracaoDeSolicitacaoEventHandler> logger, IExtracaoDeSolicitacaoS3Business extracaoDeSolicitacaoS3Business)
    {
        _logger = logger;
        _extracaoDeSolicitacaoS3Business = extracaoDeSolicitacaoS3Business;
    }

    public Task Consume(ConsumeContext<ExtracaoDeSolicitacaoEvent> context)
    {
        _logger.LogInformation("[ExtracaoDeSolicitacaoEventHandler] Mensagem consumida {Event}", nameof(ExtracaoDeSolicitacaoEvent));

        return _extracaoDeSolicitacaoS3Business.ExtrairDadosAsync(context.CancellationToken);
    }
}