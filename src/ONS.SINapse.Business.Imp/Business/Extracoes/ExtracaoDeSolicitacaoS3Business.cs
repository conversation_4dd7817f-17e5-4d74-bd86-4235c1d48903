using System.Linq.Expressions;
using System.Runtime.CompilerServices;
using System.Text.Json;
using System.Text.Json.Serialization;
using Amazon.S3;
using AutoMapper;
using Microsoft.Extensions.Options;
using MongoDB.Driver;
using ONS.SINapse.Business.IBusiness;
using ONS.SINapse.Entities.Entities;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Repository.IRepository;
using ONS.SINapse.Shared.DTO.ExtracaoDeDados;
using ONS.SINapse.Shared.Settings;

namespace ONS.SINapse.Business.Imp.Business.Extracoes;

public class ExtracaoDeSolicitacaoS3Business : IExtracaoDeSolicitacaoS3Business
{
    private readonly IAmazonS3 _client;
    private readonly ISolicitacaoRepository _solicitacaoRepository;
    private readonly IMapper _mapper;
    private readonly ExtracaoDeDadoSettings _extracaoDeDadoSettings;
    private readonly JsonSerializerOptions _jsonSerializerOptions;

    public ExtracaoDeSolicitacaoS3Business(
        IAmazonS3 client, 
        ISolicitacaoRepository solicitacaoRepository,
        IMapper mapper,
        IOptions<ExtracaoDeDadoSettings> extracaoDeDadoSettings)
    {
        _client = client;
        _solicitacaoRepository = solicitacaoRepository;
        _mapper = mapper;
        _extracaoDeDadoSettings = extracaoDeDadoSettings.Value;
        _jsonSerializerOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.SnakeCaseLower,
            ReferenceHandler = ReferenceHandler.IgnoreCycles
        };
    }


    public async Task ExtrairDadosAsync(CancellationToken cancellationToken)
    {
        var buckets = _extracaoDeDadoSettings
            .Directories.ToDictionary(k => k.Name, v => v);
        
        await foreach (var dados in ObterDadosExtracaoDadosAsync(cancellationToken))
        {
            var serializarSolicitacao = SerializarAsync(dados.Solicitacoes, cancellationToken);
            var serializarChat = SerializarAsync(dados.Chats, cancellationToken);
            var serializarHistorico = SerializarAsync(dados.Historicos, cancellationToken);
            
            await Task.WhenAll(serializarSolicitacao, serializarChat, serializarHistorico).ConfigureAwait(false);
            
            if(serializarSolicitacao.Result != Stream.Null) 
                await UploadAsync(serializarSolicitacao.Result, buckets["solicitacao"], cancellationToken).ConfigureAwait(false);
            
            if(serializarChat.Result != Stream.Null)
                await UploadAsync(serializarChat.Result, buckets["chat_solicitacao"], cancellationToken).ConfigureAwait(false);
            
            if(serializarHistorico.Result != Stream.Null)
                await UploadAsync(serializarHistorico.Result, buckets["historico_solicitacao"], cancellationToken).ConfigureAwait(false);

            var idsSolicitacoes = dados.Solicitacoes
                .Select(x => x.IdSolicitacao)
                .Distinct()
                .ToList();
            
            if(_extracaoDeDadoSettings.RemoverRegistrosAposExtracao)
                await _solicitacaoRepository.
                    DeleteManyAsync(idsSolicitacoes, cancellationToken)
                    .ConfigureAwait(false);
            else
                await _solicitacaoRepository
                    .EnviadoAoAnaliticoAsync(idsSolicitacoes, cancellationToken)
                    .ConfigureAwait(false);
        }
    }
    
    private Task UploadAsync(Stream stream, DirectorySettings directorySettings, CancellationToken cancellationToken)
    {
        // O Path.Combine respeita o sistema operacional. Ele utiliza automaticamente os separadores de diretório corretos
        // porém, o Amazon S3 não aceita o uso de barras invertidas (\) como separador de diretório.
        // Portanto, o replace de \\ é obrigatório nesse caso.
        var key = Path.Combine(directorySettings.Directory, $"{directorySettings.Name}_{Guid.NewGuid():N}_{DateTime.UtcNow:yyyyMMddHHmmss}.json")
            .Replace("\\", "/");
        return _client.UploadObjectFromStreamAsync(_extracaoDeDadoSettings.Bucket, key, stream, new Dictionary<string, object>(), cancellationToken);
    }

    private async IAsyncEnumerable<DadosExtracaoDadoDto> ObterDadosExtracaoDadosAsync([EnumeratorCancellation] CancellationToken cancellationToken)
    {
        var options = new FindOptions
        {
            BatchSize = _extracaoDeDadoSettings.RegistrosPorArquivo,
            CursorType = CursorType.NonTailable
        };
        
        var cursor = await _solicitacaoRepository.FindAsync(GerarFiltroSolicitacao(), _extracaoDeDadoSettings.RegistrosPorExecucao, options, cancellationToken);
        
        while (await cursor.MoveNextAsync(cancellationToken))
        {
            if(cursor.Current is null || !cursor.Current.Any()) continue;
            yield return _mapper.Map<DadosExtracaoDadoDto>(cursor.Current);
        }
    }
    
    private async Task<Stream> SerializarAsync<T>(List<T> dado, CancellationToken cancellationToken)
    {
        if(dado.Count <= 0) return Stream.Null;

        var stream = new MemoryStream();
                
        await JsonSerializer.SerializeAsync(
            stream,
            dado,
            dado.GetType(),
            _jsonSerializerOptions,
            cancellationToken
        );
        
        stream.Position = 0;
        
        return stream;
    }

    private Expression<Func<Solicitacao, bool>> GerarFiltroSolicitacao()
    {
        var dataLimite = DateTime.UtcNow.AddHours(-_extracaoDeDadoSettings.TempoDeVidaDosRegistrosEmHoras);
        var statusFinais = new[]
        {
            StatusDeSolicitacao.Cancelada, 
            StatusDeSolicitacao.Finalizada, 
            StatusDeSolicitacao.EnvioCancelado, 
            StatusDeSolicitacao.Enviada
        };

        return solicitacao =>
            !solicitacao.EnviadoAoAnalitico &&
            solicitacao.CreatedAt <= dataLimite &&
            statusFinais.Contains(solicitacao.Status);
    }
}