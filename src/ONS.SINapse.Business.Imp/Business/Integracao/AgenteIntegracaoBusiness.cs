using ONS.SINapse.Business.IBusiness;
using ONS.SINapse.Business.IBusiness.Integracao;
using ONS.SINapse.Integracao.Shared.Dtos;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Repository.IRepository.DadosCadastrais;
using ONS.SINapse.Shared.Extensions;

namespace ONS.SINapse.Business.Imp.Business.Integracao;

public sealed class AgenteIntegracaoBusiness : IAgenteIntegracaoBusiness
{
    private readonly IAgenteRepository _agenteRepository;
    private readonly IAgenteOnlineBusiness _agenteOnlineBusiness;


    public AgenteIntegracaoBusiness(
        IAgenteRepository agenteRepository,
        IAgenteOnlineBusiness agenteOnlineBusiness
        )
    {
        _agenteRepository = agenteRepository;
        _agenteOnlineBusiness = agenteOnlineBusiness;
    }

    public async Task<AgentesOnlineIntegracaoOutputDto> GetAgentesOnlineAsync(AgentesOnlineIntegracaoDto dto, CancellationToken cancellationToken)
    {
        var agentesId = dto.AgentesId?.ToList() ?? new List<string>();
        
        var agentes = (agentesId.Count > 0 
            ? await _agenteRepository.GetAsync(agente => agentesId.Contains(agente.Codigo), cancellationToken)
            : await _agenteRepository.GetAsync(cancellationToken)).ToList();


        var agentesInativos =
            await _agenteOnlineBusiness.BuscarAgentesInativosAsync(agentes.Select(x => x.Codigo).ToArray(), cancellationToken);

        var agentesOnlineDto = await _agenteOnlineBusiness.ObterAgentesOnlineAsync(cancellationToken);
        var agentesOnline = agentesOnlineDto.Select(r => r.Codigo);

        DateTime dataDaConsulta = DateTime.UtcNow;

        var agentesResult = agentes.Select(agente =>
        {
            StatusDeAgente status;

            if (agentesOnline.Contains(agente.Codigo))
                status = StatusDeAgente.Online;
            else if (agentesInativos.Contains(agente.Codigo))
                status = StatusDeAgente.Inativo;
            else
                status = StatusDeAgente.Offline;

            return new AgentesOnlineIntegracaoItemOutputDto
            (
                agente.Codigo,
                dataDaConsulta,
                (short)status,
                status.GetDescription()
            );
        });
        
        return new AgentesOnlineIntegracaoOutputDto(agentesResult);
    }
}