using AutoMapper;
using ONS.SINapse.Business.IBusiness;
using ONS.SINapse.Repository.IRepository;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.DTO.Firebase;

namespace ONS.SINapse.Business.Imp.Business
{
    public class AlterarVersaoBusiness : IAlterarVersaoBusiness
    {
        private readonly IVersaoFirebaseRepository _versaoFirebaseRepository;
        private readonly IMapper _mapper;

        public AlterarVersaoBusiness(
            IVersaoFirebaseRepository versaoFirebaseRepository,
            IMapper mapper) 
        {
            _versaoFirebaseRepository = versaoFirebaseRepository;
            _mapper = mapper;
        }

        public async Task Adicionar(AdicionarVersaoDto adicionarVersao, CancellationToken cancellationToken)
        {
            var versaoFirebase = _mapper.Map<VersaoFirebaseDto>(adicionarVersao);
            await _versaoFirebaseRepository.AddAsync(versaoFirebase, cancellationToken);
        }

        public async Task Editar(EditarVersaoDto editarVersao, CancellationToken cancellationToken)
        {
            await _versaoFirebaseRepository.UpdateAsync(editarVersao, cancellationToken);
        }
    }
}
