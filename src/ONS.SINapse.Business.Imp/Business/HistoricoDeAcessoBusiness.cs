using System.Linq.Expressions;
using ONS.SINapse.Business.IBusiness;
using ONS.SINapse.Entities.Entities;
using ONS.SINapse.Repository.IRepository;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Shared.Notifications;

namespace ONS.SINapse.Business.Imp.Business;

public class HistoricoDeAcessoBusiness : IHistoricoDeAcessoBusiness
{
    private readonly IHistoricoDeAcessoRepository _historicoDeAcessoRepository;
    private readonly NotificationContext _notificationContext;

    public HistoricoDeAcessoBusiness(IHistoricoDeAcessoRepository historicoDeAcessoRepository, NotificationContext notificationContext)
    {
        _historicoDeAcessoRepository = historicoDeAcessoRepository;
        _notificationContext = notificationContext;
    }

    public async Task<IEnumerable<HistoricosDeAcesso>> ObterHistoricoDeAcessoAsync(FiltroHistoricoDeAcesso? filtro, CancellationToken cancellationToken)
    {
        if (!ValidarFiltro(ref filtro))
            return Enumerable.Empty<HistoricosDeAcesso>();

        Expression<Func<HistoricoAcesso, bool>> predicate = historico =>
            (
                (historico.Inicio >= filtro!.Inicio!.Value.Date && historico.Fim < filtro.Fim!.Value.Tomorrow()) ||
                (historico.Inicio <= filtro.Fim!.Value.Tomorrow() && historico.Fim == null)
            ) &&
            (
                filtro.CentroAgente == null ||
                historico.CentroAgente.Codigo == filtro.CentroAgente
            );

        var historicos = await _historicoDeAcessoRepository.GetAsync(predicate, cancellationToken).ConfigureAwait(false);
        
        return historicos
            .Select(x => new HistoricosDeAcesso(new ObjetoDeManobraDto(x.CentroAgente.Codigo, x.CentroAgente.Nome), x.Inicio, x.Fim));
    }

    private bool ValidarFiltro(ref FiltroHistoricoDeAcesso? filtro)
    {
        filtro ??= new FiltroHistoricoDeAcesso();
        
        if(filtro.Inicio is null && filtro.Fim is null) 
            filtro = filtro.CentroAgente is null 
                ? new FiltroHistoricoDeAcesso() 
                : new FiltroHistoricoDeAcesso(filtro.CentroAgente);

        if (filtro.Inicio <= filtro.Fim) return true;
        
        _notificationContext.AddNotification("Data de inicio não pode ser maior que a data fim.");
        return false;
    }
    
}