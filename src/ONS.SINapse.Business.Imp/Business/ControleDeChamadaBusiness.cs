using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using ONS.SINapse.Business.IBusiness;
using ONS.SINapse.Entities.Entities;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Repository.IRepository;
using ONS.SINapse.Shared.DTO.Firebase;
using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Shared.Services.Firebase;
using ONS.SINapse.Shared.Settings;

namespace ONS.SINapse.Business.Imp.Business;

public class ControleDeChamadaBusiness : IControleDeChamadaBusiness
{
    private readonly IControleDeChamadaRepository _controleDeChamadaRepository;
    private readonly IControleDeChamadaRealtimeService _controleDeChamadaRealtimeService;
    private readonly ILogger<ControleDeChamadaBusiness> _logger;
    private readonly ControleDeChamadaSettings _controleDeChamadaSettings;
    private readonly IRespostaDaChamadaBusiness _respostaDaChamadaBusiness;

    public ControleDeChamadaBusiness(
        IControleDeChamadaRepository controleDeChamadaRepository,
        IControleDeChamadaRealtimeService controleDeChamadaRealtimeService,
        ILogger<ControleDeChamadaBusiness> logger,
        IOptions<ControleDeChamadaSettings> controleDeChamadaSettings,
        IRespostaDaChamadaBusiness respostaDaChamadaBusiness
        )
    {
        _controleDeChamadaRepository = controleDeChamadaRepository;
        _controleDeChamadaRealtimeService = controleDeChamadaRealtimeService;
        _logger = logger;
        _controleDeChamadaSettings = controleDeChamadaSettings.Value;
        _respostaDaChamadaBusiness = respostaDaChamadaBusiness;
    }

    public async Task Executar(CancellationToken stoppingToken)
    {
        var chamada = await BuscarControleDeChamada(stoppingToken);

        switch (chamada.Status)
        {
            case StatusControleDeChamada.LiberadoParaChamada:
                await IniciarChamada(chamada, stoppingToken);
                break;

            case StatusControleDeChamada.ChamadaEmAndamento:
                await FinalizarChamada(chamada, stoppingToken);
                break;

            case StatusControleDeChamada.ProcessamentoPendente:
                await IniciarProcessamentoDaChamada(chamada, stoppingToken);
                break;

            case StatusControleDeChamada.Processando:
                await _respostaDaChamadaBusiness.ProcessarChamada(chamada, stoppingToken);
                await FinalizarProcessamentoDaChamada(chamada, stoppingToken);
                break;

            case StatusControleDeChamada.ProcessamentoFinalizado:
                await _respostaDaChamadaBusiness.PublicarListaDeChamada(chamada, stoppingToken);
                await LiberarParaChamada(chamada, stoppingToken);
                break;
        }
    }

    public async Task ExecutarDelay(CancellationToken cancellationToken)
    {
        var delay = TimeSpan.FromSeconds(_controleDeChamadaSettings.TempoEntreTarefasEmSegundos);
        await Task.Delay(delay, cancellationToken);
    }

    private async Task<ControleDeChamada> CriarControleDeChamada(CancellationToken cancellationToken)
        => await _controleDeChamadaRepository.AddAsync(new ControleDeChamada(_controleDeChamadaSettings.TempoDuracaoEmSegundos), cancellationToken);

    private async Task<ControleDeChamada> BuscarControleDeChamada(CancellationToken cancellationToken)
    {
        var chamada = await _controleDeChamadaRepository.GetOneAsync(c => true, cancellationToken);
        if (chamada != null)
            return chamada;

        return await CriarControleDeChamada(cancellationToken);
    }

    private async Task LiberarParaChamada(ControleDeChamada chamada, CancellationToken cancellationToken)
    {
        chamada.LiberarParaChamada();
        await _controleDeChamadaRepository.UpdateAsync(chamada.Id, chamada, cancellationToken);
    }

    private async Task IniciarChamada(ControleDeChamada chamada, CancellationToken cancellationToken)
    {
        if (!chamada.EstaLiberadoParaChamada()) return;

        chamada.IniciarChamada();
        await _controleDeChamadaRepository.UpdateAsync(chamada.Id, chamada, cancellationToken);
        await _controleDeChamadaRealtimeService.AddUpdateAsync(new ControleDeChamadaRealtimeDto(chamada.NumeroDaChamada), cancellationToken);
        _logger.LogInformation("Chamada iniciada as {time}", DateTimeOffset.Now);
    }

    private async Task FinalizarChamada(ControleDeChamada chamada, CancellationToken cancellationToken)
    {
        var delay = chamada.TempoRestante();
        _logger.LogInformation("Aguardando {delay} a partir de: {time} para tentar finalizar a chamada", delay, DateTimeOffset.Now);
        
        await Task.Delay(delay, cancellationToken);
        if (!chamada.PermiteFinalizarChamada())
        {
            _logger.LogInformation("Chamada não pode ser finalizada, status: {Status}", chamada.Status.GetDescription());
            return;
        }
        chamada.FinalizarChamada();
        await _controleDeChamadaRepository.UpdateAsync(chamada.Id, chamada, cancellationToken);
        _logger.LogInformation("Chamada finalizada as {time}", DateTimeOffset.Now);
    }

    private async Task IniciarProcessamentoDaChamada(ControleDeChamada chamada, CancellationToken cancellationToken)
    {
        if (!chamada.EstaPendenteDeProcessamento())
            return;

        chamada.IniciarProcessamento();
        await _controleDeChamadaRepository.UpdateAsync(chamada.Id, chamada, cancellationToken);
        _logger.LogInformation("Processamento iniciado as {time}", DateTimeOffset.Now);
    }

    private async Task FinalizarProcessamentoDaChamada(ControleDeChamada chamada, CancellationToken cancellationToken)
    {
        chamada.FinalizarProcessamento();
        await _controleDeChamadaRepository.UpdateAsync(chamada.Id, chamada, cancellationToken);
        _logger.LogInformation("Processamento finalizado as {time}", DateTimeOffset.Now);
    }
}