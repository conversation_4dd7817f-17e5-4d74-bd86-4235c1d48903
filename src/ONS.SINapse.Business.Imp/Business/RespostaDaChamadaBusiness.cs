using Microsoft.Extensions.Logging;
using ONS.SINapse.Business.IBusiness;
using ONS.SINapse.Entities.Entities;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Repository.IRepository;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Extensions;
using System.Linq.Expressions;
using System.Text;
using ONS.SINapse.Business.Imp.Workers.Messages;
using ONS.SINapse.Shared.Kafka.Providers;

namespace ONS.SINapse.Business.Imp.Business;

public class RespostaDaChamadaBusiness : IRespostaDaChamadaBusiness
{
    private readonly IRespostaDaChamadaRepository _respostaDaChamadaRepository;
    private readonly IControleDeChamadaRepository _controleDeChamadaRepository;
    private readonly IHistoricoDeAcessoRepository _historicoDeAcessoRepository;
    private readonly ILogger<RespostaDaChamadaBusiness> _logger;
    private readonly IProducerProvider<StatusAgenteMessage> _producer;

    public RespostaDaChamadaBusiness(
        IRespostaDaChamadaRepository respostaDaChamadaRepository,
        IControleDeChamadaRepository controleDeChamadaRepository,
        IHistoricoDeAcessoRepository historicoDeAcessoRepository,
        ILogger<RespostaDaChamadaBusiness> logger,
        IProducerProvider<StatusAgenteMessage> producer)
    {
        _respostaDaChamadaRepository = respostaDaChamadaRepository;
        _controleDeChamadaRepository = controleDeChamadaRepository;
        _historicoDeAcessoRepository = historicoDeAcessoRepository;
        _logger = logger;
        _producer = producer;
    }
    public async Task<ICollection<RespostaDaChamada>> BuscarPorCentro(string[] codigo, CancellationToken cancellationToken)
    {
        return await _respostaDaChamadaRepository.GetAsync(c => codigo.Contains(c.CentroAgente.Codigo), cancellationToken);
    }

    public async Task ResponderChamada(ResponderChamadaDto dto, CancellationToken cancellationToken)
    {
        var chamada = await _controleDeChamadaRepository.GetOneAsync(c => true, cancellationToken);
        if (chamada == null || !chamada.EstaEmChamada() || dto.Numero != chamada.NumeroDaChamada)
        {
            return;
        }

        var codigos = dto.Resposta
            .Select(r => (ObjetoDeManobra)r.CentroAgente)
            .Distinct()
            .ToArray();

        var centros = await _respostaDaChamadaRepository.FindOrCreate(codigos, cancellationToken);

        foreach (var centro in centros)
        {
            var resposta = dto.Resposta.FirstOrDefault(r => r.CentroAgente.Codigo == centro.CentroAgente.Codigo);

            if (resposta != null)
            {
                centro.ResponderChamada(chamada.NumeroDaChamada, resposta.Equipamentos);
            }
        }

        await _respostaDaChamadaRepository.BulkUpdateAsync(centros, cancellationToken);
    }

    public async Task<ICollection<RespostaDaChamada>> ProcessarChamada(ControleDeChamada chamada, CancellationToken cancellationToken)
    {
        if (!chamada.EstaProcessando())
        {
            return new List<RespostaDaChamada>();
        }
        var respostas = await _respostaDaChamadaRepository.GetAsync(cancellationToken);
        var presentesChamadaAtual = respostas.Where(c => c.RespondeuChamada(chamada.NumeroDaChamada)).ToList();
        var ausentesChamadaAtual = respostas.Where(c => !c.RespondeuChamada(chamada.NumeroDaChamada)).ToList();
        ausentesChamadaAtual.ForEach(c => c.RegistrarAusencia());
        presentesChamadaAtual.ForEach(c => c.RegistrarPresenca());
        await _respostaDaChamadaRepository.BulkUpdateAsync(respostas, cancellationToken);
        return respostas;
    }

    public async Task PublicarListaDeChamada(ControleDeChamada chamada, CancellationToken cancellationToken)
    {
        if (!chamada.EstaProcessado())
            return;

        var centros = (
                await _respostaDaChamadaRepository.BuscarPendentesDeNotificacaoAsync(cancellationToken)
                    .ConfigureAwait(false)
                )
            .ToList();
        
        if (centros.Count == 0)
        {
            _logger.LogInformation("Nenhum centro para notificar.");
            return;
        }

        var logMessage = new StringBuilder();
        centros.ForEach(c =>
        {
            c.Notificar();
            logMessage.AppendFormat("Centro {0} notificado com status {1}", c.CentroAgente, c.Status.GetDescription());
            logMessage.Append(Environment.NewLine);
        });
        
        await _respostaDaChamadaRepository.BulkUpdateAsync(centros, cancellationToken);

        _logger.LogInformation("Centros notificados: {LogMessage}", logMessage.ToString());

        var centroAgenteOnline = centros
            .Where(c => c.Status == StatusDeCentroAgente.Online)
            .Select(a => a.CentroAgente)
            .ToList();
        
        var centroAgenteOffline = centros
            .Where(c => c.Status == StatusDeCentroAgente.Offline)
            .Select(a => a.CentroAgente)
            .ToList();

        if (centroAgenteOnline.Count != 0)
        {
            await PublicarAgenteKafka(centroAgenteOnline.Select(x => x.Codigo), StatusDeAgente.Online, cancellationToken);
            await IniciarAcesso(centroAgenteOnline, cancellationToken).ConfigureAwait(false);
        }

        if (centroAgenteOffline.Count != 0)
        {
            await PublicarAgenteKafka(centroAgenteOffline.Select(x => x.Codigo), StatusDeAgente.Offline,
                cancellationToken);
            
            await FinalizarAcesso(centroAgenteOffline, cancellationToken).ConfigureAwait(false);
        }
    }

    public async Task<ICollection<RespostaDaChamada>> ObterAgentesOnline(CancellationToken cancellationToken)
    {
        var chamada = await _controleDeChamadaRepository.GetOneAsync(x => true, cancellationToken);

        if (chamada is null) return new List<RespostaDaChamada>();

        var predicate = GetPredicateAgentesOnline(chamada);

        return await _respostaDaChamadaRepository.GetAsync(predicate, cancellationToken);
    }

    public async Task<string[]> ObterCodigoAgentesOnlineAsync(CancellationToken cancellationToken)
    {
        var chamada = await _controleDeChamadaRepository.GetOneAsync(x => true, cancellationToken);

        if (chamada is null) return [];

        var predicate = GetPredicateAgentesOnline(chamada);

        return await _respostaDaChamadaRepository.ObterCodigoAgentesOnlineAsync(predicate, cancellationToken);
    }

    public async Task<string[]> ObterCentroAgentesInativos(string[] codigoCentroAgente, CancellationToken cancellationToken)
    {
        var respostas = await _respostaDaChamadaRepository.GetAsync(
            resp => codigoCentroAgente.Contains(resp.CentroAgente.Codigo),
            cancellationToken)
            .ConfigureAwait(false);
        
        return codigoCentroAgente.Except(respostas.Select(x => x.CentroAgente.Codigo)).ToArray();
    }

    private Task IniciarAcesso(IEnumerable<ObjetoDeManobra> centroAgentes, CancellationToken cancellationToken)
    {
        return _historicoDeAcessoRepository.BulkCreateAsync(centroAgentes.Select(HistoricoAcesso.IniciarAcesso), cancellationToken);
    }

    private async Task FinalizarAcesso(IEnumerable<ObjetoDeManobra> centroAgentes, CancellationToken cancellationToken)
    {
        var codigos = centroAgentes.Select(x => x.Codigo).ToList();
        
        Expression<Func<HistoricoAcesso, bool>> filter =
            x => codigos.Contains(x.CentroAgente.Codigo) && x.Fim == null;
            
        var historicos = await _historicoDeAcessoRepository.GetAsync(filter, cancellationToken).ConfigureAwait(false);
        
        if(historicos.Count == 0) return;
        
        foreach (var historicoAcesso in historicos)
        {
            historicoAcesso.FinalizarAcesso();
        }
        
        await _historicoDeAcessoRepository.BulkUpdateAsync(historicos, cancellationToken).ConfigureAwait(false);
    }
    
    private async Task PublicarAgenteKafka(IEnumerable<string> agentes, StatusDeAgente status, CancellationToken cancellationToken)
    {
        if(!_producer.EstaConectado()) return;
        
        try
        {
            Dictionary<string, string> headers = new()
            {
                { "type", "StatusAgente" }
            };

            var tasks = agentes.Select(codigoDoAgente =>
            {
                var agente = new StatusAgenteMessage(
                    codigoDoAgente,
                    DateTime.Now,
                    (short)status,
                    status.GetDescription()
                );

                return _producer.PublishAsync(agente, headers, cancellationToken);
            });

            await Task.WhenAll(tasks);
        }
        catch (Exception exception)
        {
            _logger.LogError(exception, "[RespostaDaChamadaBusiness][PublicarAgenteKafka] Erro ao enviar mensagem de status do agente para o Kafka.");
        }
    }

    private static Expression<Func<RespostaDaChamada, bool>> GetPredicateAgentesOnline(ControleDeChamada chamada)
    {
        var centros = CentrosDeOperacoes.GetCentrosDeOperacao();
        var codigoCentros = centros.Select(c => c.Codigo);

        Expression<Func<RespostaDaChamada, bool>> predicate = resposta =>
            resposta.Status == StatusDeCentroAgente.Online &&
            resposta.NumeroDaChamada == chamada.NumeroDaChamada &&
            !codigoCentros.Contains(resposta.CentroAgente.Codigo);

        return predicate;
    }
}