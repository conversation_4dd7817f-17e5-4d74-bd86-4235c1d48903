using ONS.SINapse.Business.IBusiness;
using ONS.SINapse.Business.Imp.Validators;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.DTO.Firebase;
using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Shared.Identity;
using ONS.SINapse.Shared.Notifications;
using ONS.SINapse.Shared.Services.Firebase;

namespace ONS.SINapse.Business.Imp.Business;

public class TagBusiness : ITagBusiness
{
    private const string TagNaoLocalizada = "Tag não localizada.";
    private readonly ITagFirebaseService _tagFirebaseService;
    private readonly NotificationContext _notificationContext;
    private readonly IUserContext _usuarioBusiness;

    public TagBusiness(
        ITagFirebaseService tagFirebaseService, 
        NotificationContext notificationContext,
        IUserContext usuarioBusiness
        ) 
    {
        _tagFirebaseService = tagFirebaseService;
        _notificationContext = notificationContext;
        _usuarioBusiness = usuarioBusiness;
    }
    private Task<bool> ExistsAsync(string tag, string sid, CancellationToken cancellationToken) 
        => _tagFirebaseService
            .ExistsDocumentWithPropertyValueAsync("tag", tag, sid, cancellationToken);

    public async Task<TagRealtimeDto?> AddAsync(TagDto dto, CancellationToken cancellationToken)
    {
        var usuario =  _usuarioBusiness.Usuario();
        
        if (!ValidateAsync(dto, cancellationToken).Result)
        {
            return null;
        }
        
        if(ExistsAsync(dto.Tag,  usuario.Sid, cancellationToken).Result)
        {
            _notificationContext.AddNotification($"Tag '{dto.Nome}' já existe.");
            return null;
        }

        var tagRealtimeDto = new TagRealtimeDto(usuario.Sid, dto.Nome);
        await _tagFirebaseService.SetAsync(tagRealtimeDto, cancellationToken);
        return tagRealtimeDto;
    }

    public async Task<TagRealtimeDto?> GetAsync(string id, CancellationToken cancellationToken)
    {
        var usuario =  _usuarioBusiness.Usuario();
        return await _tagFirebaseService.GetAsync(id, usuario.Sid, cancellationToken);
    }

    public async Task<IEnumerable<TagRealtimeDto?>> GetAllAsync(CancellationToken cancellationToken)
    {
        var usuario =  _usuarioBusiness.Usuario();
        return await _tagFirebaseService.GetAllAsync(usuario.Sid, cancellationToken);
    }

    public async Task<TagRealtimeDto?> UpdateAsync(string id, TagDto dto, CancellationToken cancellationToken)
    {
        var usuario =  _usuarioBusiness.Usuario();
        
        if (!ValidateAsync(dto, cancellationToken).Result)
        {
            return null;
        }

        var tagExistent = await _tagFirebaseService.GetAsync(id, usuario.Sid, cancellationToken);

        if (tagExistent is null)
        {
            _notificationContext.AddNotification(TagNaoLocalizada);
            return null;
        }

        if (tagExistent.Tag.Equals(dto.Tag))
        {
            return tagExistent;
        }

        if (ExistsAsync(dto.Nome, usuario.Sid, cancellationToken).Result)
        {
            _notificationContext.AddNotification($"Tag '{dto.Nome}' já existe.");
            return null;
        }

        tagExistent.AlterarNome(dto.Nome);
        await _tagFirebaseService.SetAsync(tagExistent, cancellationToken);

        return tagExistent;
    }

    public async Task DeleteAsync(string id, CancellationToken cancellationToken)
    {
        var usuario =  _usuarioBusiness.Usuario();
        var tag = await _tagFirebaseService.GetAsync(id, usuario.Sid, cancellationToken);
        if (tag is null)
        {
            _notificationContext.AddNotification(TagNaoLocalizada);
            return;
        }

        await _tagFirebaseService.DeleteAsync(tag.Id, usuario.Sid, cancellationToken);
    }
    
    private async Task<bool> ValidateAsync(TagDto dto, CancellationToken cancellationToken)
    {
        var validator = new TagDtoValidator();
        var result = await validator.ValidateAsync(dto, cancellationToken);
        if (!result.IsValid)
            _notificationContext.AddNotifications(result);
        return result.IsValid;
    }
}