using ONS.SINapse.Business.IBusiness;
using ONS.SINapse.CacheSync.Services;
using ONS.SINapse.Repository.IRepository.DadosCadastrais;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Extensions;
using System.Security.Claims;
using Microsoft.AspNetCore.Http;
using ONS.SINapse.Shared.DTO.PerfilDoUsuario;

namespace ONS.SINapse.Business.Imp.Business;

public class PerfilDeUsuarioBusiness : IPerfilDeUsuarioBusiness
{
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly IRulesOperations _rulesOperations;
    private readonly IAgenteRepository _agenteRepository;
    private readonly ISinapseDadosDatasetQueryService _sinapseDadosDatasetQueryService;

    public PerfilDeUsuarioBusiness(
        IHttpContextAccessor httpContextAccessor,
        IRulesOperations rulesOperations,
        IAgenteRepository agenteRepository,
        ISinapseDadosDatasetQueryService sinapseDadosDatasetQueryService
        )
    {
        _httpContextAccessor = httpContextAccessor;
        _rulesOperations = rulesOperations;
        _agenteRepository = agenteRepository;
        _sinapseDadosDatasetQueryService = sinapseDadosDatasetQueryService;
    }

    public async Task<UsuarioPerfilDto> ObterPerfilAsync(CancellationToken cancellationToken)
    {
        var principal = ObterClaimsPrincipal();

        var login = principal.ObterLoginDeUsuario();
        var sid = principal.ObterSidDeUsuario();
        var name = principal.ObterNomeDeUsuario();
        
        // Centro De Operacao: CENTROS/NE
        var scopeRoleFormatado = principal.ObterScopeRolesFormatado();

        var centroAgente =
            scopeRoleFormatado.SelectMany(x => x.Value
                .Select(y => y.Split("/")[1].Trim()))
                .ToList();

        var agentes = await _agenteRepository.GetAsync(centroAgente, cancellationToken);
        var centros = CentrosDeOperacoes
            .GetCentrosDeOperacao()
            .Where(x => centroAgente.Contains(x.Codigo));
        
        var centrosAgentes = agentes
            .Distinct()
            .ToDictionary(k => k.Codigo, v => v.Nome)
            .UnionBy(centros.Distinct().ToDictionary(k => k.Codigo, v => v.Nome), pair => pair.Key)
            .DistinctBy(x => x.Key)
            .ToDictionary(x => x.Key, x => x.Value);

        var perfis = scopeRoleFormatado
            .Where(perfil => _rulesOperations.ContainsKey(perfil.Key.Codigo))
            .Select(perfil =>
            {
                var codigo = perfil.Key.Codigo;
                var nome = perfil.Key.Nome;
                var operations = _rulesOperations[codigo].Operations.ToList();
                var scopos = perfil.Value
                    .Select(scope =>
                    {
                        var split = scope.Split("/");
                        var scodigo = split[1].Trim();
                        var stipo = split[0].Trim();
                        _ = centrosAgentes.TryGetValue(scodigo, out var snome);
                        return new ScopeDto(snome ?? scodigo, scodigo, stipo);
                    })
                    .OrderBy(x => x.Nome)
                    .ToList();
                return new PerfilDto(scopos, operations, nome, codigo);
            })
            .OrderBy(x => x.Nome)
            .ToList();

        return new UsuarioPerfilDto(perfis, sid, name, login);
    }
    
    public async Task<List<VisaoDeUsuarioSelecionadoDto>> AlterarVisoesAsync(ICollection<SelecaoDeVisaoDeUsuarioDto> visoes,
        CancellationToken cancellationToken)
    {
        var tasks = visoes
            .GroupBy(
                group => group.CodigoCentroAgente,
                by => new { by.Id, by.EquipamentosDeManobra },
                async (group, by) =>
                {
                    var locais = (await _sinapseDadosDatasetQueryService
                            .GetDatasetAsync(
                                x => x.GetLocalDeOperacaoDataset(group, cancellationToken)
                                , cancellationToken))
                        ?.SelectMany(s => s.Locais.Select(ss => ss.Codigo))
                        .Distinct();

                    return by.Select(x =>
                    {
                        var validator = x.EquipamentosDeManobra
                            .Select(s =>
                            {
                                var valido = locais?.Contains(s) ?? false;
                                
                                return new { Equipamento = s, valido, Erro = valido ? "" : "Equipamento não localizado." };
                            });

                        return new { CentroAgente = group, x.Id, Validacao = validator };
                    });
                })
            .ToList();

        foreach (var task in tasks)
        {
            await task.ConfigureAwait(false);
        }

        var result = tasks.SelectMany(x =>
                x.Result.Select(s =>
                    new VisaoDeUsuarioSelecionadoDto(
                        s.CentroAgente,
                        s.Id,
                        s.Validacao.Select(ss => ss.Equipamento).ToArray(),
                        s.Validacao
                            .Where(ss => !ss.valido)
                            .Select(ss => new ErroDeVisaoDeUsuarioDto(ss.Equipamento, ss.Erro))
                            .ToArray())))
            .ToList();

        return result;
    }
    
    private ClaimsPrincipal ObterClaimsPrincipal()
    {
        var context = _httpContextAccessor.HttpContext;
        return context is null ? new ClaimsPrincipal() : context.User;
    }
}