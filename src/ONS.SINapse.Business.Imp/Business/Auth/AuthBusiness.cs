using ONS.SINapse.Business.IBusiness;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Notifications;
using ONS.SINapse.Shared.Services.Firebase;

namespace ONS.SINapse.Business.Imp.Business.Auth;

public class AuthBusiness : IAuthBusiness
{
    private readonly IFirebaseAuthService _firebaseAuthService;
    private readonly NotificationContext _notificationContext;

    public AuthBusiness(
        IFirebaseAuthService firebaseAuthService,
        NotificationContext notificationContext)
    {
        _firebaseAuthService = firebaseAuthService;
        _notificationContext = notificationContext;
    }

    public async Task<AuthResultDto?> AuthorizeAsync(CancellationToken cancellationToken)
    {
        var firebaseCustomToken = await _firebaseAuthService.GetTokenCustomizadoParaLeituraAsync(string.Empty, cancellationToken);

        if (!string.IsNullOrWhiteSpace(firebaseCustomToken.AccessToken))
            return new AuthResultDto(firebaseCustomToken.AccessToken);
        
        _notificationContext.AddNotification("Não foi possivel se autenticar no firebase, token está invalido.");
        return null;

    }
}