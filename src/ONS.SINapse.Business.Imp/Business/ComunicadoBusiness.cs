using AutoMapper;
using Microsoft.Extensions.Options;
using ONS.SINapse.Business.IBusiness;
using ONS.SINapse.Business.Imp.Validators;
using ONS.SINapse.Entities.Entities;
using ONS.SINapse.Repository.Imp.Specifications;
using ONS.SINapse.Repository.IRepository;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Enums;
using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Shared.Notifications;
using ONS.SINapse.Shared.Settings;
using System.Linq.Expressions;
using ONS.SINapse.CacheSync.Services;
using ONS.SINapse.Shared.Constants;
using ONS.SINapse.Shared.Identity;

namespace ONS.SINapse.Business.Imp.Business;

public class ComunicadoBusiness : IComunicadoBusiness
{
    private readonly IComunicadoRepository _comunicadoRepository;
    private readonly IMapper _mapper;
    private readonly NotificationContext _notificationContext;
    private readonly IUserContext _userContext;
    private readonly ComunicadoSettings _comunicadoSettings;
    private readonly INotificacaoDeComunicadoBusiness _notificacaoDeComunicadoBusiness;
    private readonly ISinapseDadosDatasetQueryService _sinapseDadosDatasetQueryService;

    public ComunicadoBusiness(
        IComunicadoRepository comunicadoRepository, 
        NotificationContext notificationContext,
        IUserContext userContext, 
        IMapper mapper, 
        IOptions<ComunicadoSettings> comunicadoOptionsSettings,
        INotificacaoDeComunicadoBusiness notificacaoDeComunicadoBusiness,
        ISinapseDadosDatasetQueryService sinapseDadosDatasetQueryService)
    {
        _comunicadoRepository = comunicadoRepository;
        _notificationContext = notificationContext;
        _userContext = userContext;
        _mapper = mapper;
        _comunicadoSettings = comunicadoOptionsSettings.Value;
        _notificacaoDeComunicadoBusiness = notificacaoDeComunicadoBusiness;
        _sinapseDadosDatasetQueryService = sinapseDadosDatasetQueryService;
    }
    
    public async Task<IEnumerable<ComunicadoItemDto>> GetAsync(CancellationToken cancellationToken)
    {
        var dto = GetConsultaDeComunicadosDto();
        var predicate = new ConsultaDeComunicadosSpecification(dto).Criteria();

        var comunicados = await _comunicadoRepository.GetAsync(predicate, cancellationToken);

        var comunicadosDto = _mapper.Map<IEnumerable<ComunicadoItemDto>>(
            comunicados.OrderByDescending(s => s.UpdatedAt).ToList()
        ).ToList();

        return comunicadosDto;
    }

    public async Task<ComunicadoDetalheDto> AddAsync(ComunicadoDto dto, CancellationToken cancellationToken)
    {
        if (!Validar(dto)) return new ComunicadoDetalheDto();

        var destinatarios = dto.Destinatarios.Where(d => !string.IsNullOrEmpty(d)).Distinct().ToList();

        var comunicado = _mapper.Map<Comunicado>(dto);
        comunicado.UsuarioDeCriacao = _userContext.Usuario();
        comunicado.Origem = new ObjetoDeManobra(dto.Origem.Codigo, dto.Origem.Nome);

        if (!ValidarOperacaoDoUsuario(comunicado)) return new ComunicadoDetalheDto();

        if (!await AdicionarDestinatarios(comunicado, destinatarios, cancellationToken)) return new ComunicadoDetalheDto();

        await _comunicadoRepository.AddAsync(comunicado, cancellationToken);
        await NotificarComunicadoAsync(comunicado, comunicado.UsuarioDeCriacao.Sid, cancellationToken);

        return GetComunicadoDetalheDto(comunicado, comunicado.UsuarioDeCriacao);
    }

    private async Task NotificarComunicadoAsync(Comunicado comunicado, string sid, CancellationToken cancellationToken)
    {
        await _notificacaoDeComunicadoBusiness.NotificarComunicadoAsync(comunicado, sid, cancellationToken);
    }

    private async Task<bool> AdicionarDestinatarios(
        Comunicado comunicado,
        ICollection<string> destinatarios,
        CancellationToken cancellationToken)
    {
        if (comunicado.TipoDeComunicado is TipoDeComunicado.Todos)
        {
            return AdicionarDestinatariosParaTodos(destinatarios);
        }

        if (destinatarios.Empty())
        {
            _notificationContext.AddNotification("Deve ser enviado um ou mais destinatários.");
            return false;
        }

        if (comunicado.TipoDeComunicado is TipoDeComunicado.CentrosRegionais)
        {
            return AdicionarDestinatariosPorCentroAsync(comunicado, destinatarios);
        }
        
        return await AdicionarDestinatariosPorAgente(comunicado, destinatarios, cancellationToken).ConfigureAwait(false);
    }

    private bool AdicionarDestinatariosParaTodos(ICollection<string> destinatarios)
    {
        if (destinatarios.Empty()) return true;
        _notificationContext.AddNotification($"Tipo de comunicado não permite envio de lista de destinatários.");
        return false;
    }

    private bool AdicionarDestinatariosPorCentroAsync(Comunicado comunicado, ICollection<string> destinatarios)
    {
        var centros = CentrosDeOperacoes.GetCentrosDeOperacao()
               .Where(centro => destinatarios.Contains(centro.Codigo))
               .ToList();

        if (centros.Empty())
        {
            _notificationContext.AddNotification("Centros de operações não localizados: " +
                                                 string.Join(", ", destinatarios));
            return false;
        }

        var centrosNaoEncontrados = destinatarios.Except(centros.Select(a => a.Codigo)).ToList();

        if (centrosNaoEncontrados.Empty())
            return centros.All(centro =>
                comunicado.AdicionarDestinatario(centro.Codigo, centro.Nome, centro.Codigo, null));

        _notificationContext.AddNotification("Centros de operações não localizados: " +
                                             string.Join(", ", centrosNaoEncontrados));
        return false;
    }
    
    private async Task<bool> AdicionarDestinatariosPorAgente(Comunicado comunicado, ICollection<string> destinatarios, CancellationToken cancellationToken)
    {
        var codigoOnsCentro = comunicado.Origem.Codigo;
        
        var agentes = (await _sinapseDadosDatasetQueryService
            .GetDatasetAsync(
                x => x.GetComunicadoAreaEletrica(new [] { codigoOnsCentro }, cancellationToken),
                cancellationToken)
            .ConfigureAwait(false) ?? Enumerable.Empty<AreaEletricaComunicadoDto>())
            .SelectMany(x => x.Agentes.Select(agente => new
            {
                AreaNome = x.Nome,
                AreaCodigo = x.Codigo,
                AgenteNome = agente.Nome,
                AgenteCodigo = agente.Codigo
            }))
            .GroupBy(group => new { Codigo = group.AgenteCodigo, Nome = group.AgenteNome },
                by => by.AreaNome,
                (group, by) =>
                    new
                    {
                        Agente = group,
                        Areas = by.ToList()
                    })
            .Distinct()
            .ToList();

        if (agentes.Count == 0)
        {
            _notificationContext.AddNotification($"Nenhum registro localizado para o centro informado. Centro: {codigoOnsCentro}");
            return false;
        }
        
        var encontrados = agentes
            .Where(x => destinatarios.Contains(x.Agente.Codigo))
            .ToList();
        
        if (encontrados.Count == 0)
        {
            _notificationContext.AddNotification("Nenhum agente localizado.");
            return false;
        }

        var agentesNaoEncontrados = destinatarios.Except(encontrados.Select(x => x.Agente.Codigo)).ToList();
        
        if (agentesNaoEncontrados.Count == 0)
        {
            return encontrados
                .All(dados =>
                    comunicado.AdicionarDestinatario(
                        dados.Agente.Codigo,
                        dados.Agente.Nome,
                        codigoOnsCentro,
                        dados.Areas
                    )
                );
        }

        _notificationContext.AddNotification("Agentes não localizados: " + string.Join(", ", agentesNaoEncontrados));
        return false;
    }

    private bool ValidarOperacaoDoUsuario(Comunicado comunicado)
    {
        var isValid = comunicado.TipoDeComunicado switch
        {
            TipoDeComunicado.Todos or TipoDeComunicado.CentrosRegionais => _userContext.PerfilSelecionadoPossuiOperacao(Operacoes.CriarBroadcast),
            TipoDeComunicado.AreasEletricas => _userContext.PerfilSelecionadoPossuiOperacao(Operacoes.CriarMulticast),
            _ => false
        };

        if (!isValid)
            _notificationContext.AddNotification(
                $"Perfil {_userContext.Perfil.Nome} não pode enviar comunicado para {comunicado.TipoDeComunicado.GetDescription()}."
            );

        return isValid;
    }

    public async Task<ComunicadoDetalheDto> GetByIdAsync(string id, CancellationToken cancellationToken)
    {
        var comunicado = await _comunicadoRepository.GetOneAsync(id, cancellationToken);

        if (comunicado is null)
        {
            _notificationContext.AddNotification("Comunicado informado não existe.");
            return new ComunicadoDetalheDto();
        }

        var usuario = _userContext.Usuario();
        var comunicadoDetalheDto =
            GetComunicadoDetalheDto(comunicado, usuario);

        return comunicadoDetalheDto;
    }

    public async Task<long> ObterTotalDeComunicadosNaoLidosPeloUsuario(CancellationToken cancellationToken)
    {
        var destinatarios = _userContext.Perfil.Centros;

        var usuario = _userContext.Usuario();
        Expression<Func<Comunicado, bool>> predicate = c =>
            !usuario.Sid.Equals(c.UsuarioDeCriacao.Sid) &&
            !c.ListaDeLeitores.Any(l => l.DataDoComunicado == c.UpdatedAt && usuario.Sid.Equals(l.Usuario.Sid)) &&
            (c.TipoDeComunicado == TipoDeComunicado.Todos || (c.Destinatarios != null &&
                                                              c.Destinatarios.Any(d =>
                                                                  destinatarios.Contains(d.Codigo))));
        return await _comunicadoRepository.CountAsync(predicate, cancellationToken);
    }

    public async Task<ComunicadoDetalheDto> RegistrarLeituraAsync(string id,
        CancellationToken cancellationToken)
    {
        try
        {
            var usuario = _userContext.Usuario();
            var comunicado = await _comunicadoRepository.GetOneAsync(id, cancellationToken);
            
            if (comunicado is null)
            {
                _notificationContext.AddNotification("Comunicado não encontrado.");
                return new ComunicadoDetalheDto();
            }

            var centros = _userContext.Perfil.Scopes.Select(x => new ObjetoDeManobraDto(x.Codigo, x.Nome)).ToList();
            
            comunicado.AdicionarLeitor(usuario, DateTime.Now, centros);
            await _comunicadoRepository.UpdateAsync(id, comunicado, cancellationToken);

            return GetComunicadoDetalheDto(comunicado, usuario);
        }
        catch (Exception)
        {
            _notificationContext.AddNotification("Comunicado informado não existe.");
        }

        return new ComunicadoDetalheDto();
    }

    public async Task<ComunicadoDetalheDto> AdicionarComplentoAsync(string id,
        ComplementoDeComunicadoDto complemento, CancellationToken cancellationToken)
    {
        var usuario = _userContext.Usuario();
        var comunicado = await _comunicadoRepository.GetOneAsync(id, cancellationToken);
        
        if (comunicado is null)
        {
            _notificationContext.AddNotification("Comunicado não encontrado.");
            return new ComunicadoDetalheDto();
        }
        
        if (string.IsNullOrEmpty(complemento.Mensagem))
        {
            _notificationContext.AddNotification("Campo Mensagem obrigatória.");
            return GetComunicadoDetalheDto(comunicado, usuario);
        }

        try
        {
            if (PossuiPermissaoDeAdicionarComplemento(comunicado))
            {
                comunicado.AdicionarComplemento(complemento.Origem, complemento.Mensagem);
                await _comunicadoRepository.UpdateAsync(id, comunicado, cancellationToken);
                await NotificarComunicadoAsync(comunicado, usuario.Sid, cancellationToken);
            }
        }
        catch (Exception e)
        {
            _notificationContext.AddNotification($"Erro ao adicionar complemento.\n {e.Message}");
        }

        return GetComunicadoDetalheDto(comunicado, usuario);
    }

    private async Task<ICollection<Comunicado>> BuscarPorDataDeAtualizacaoMenorQue(DateTime dataUltimaAtualizacao,
        CancellationToken cancellationToken)
    {
        return await _comunicadoRepository
            .GetAsync(c => c.UpdatedAt < dataUltimaAtualizacao, cancellationToken);
    }


    private async Task<ICollection<string>> ExcluirPorDataDeAtualizacaoMenorQue(DateTime dataDeAtualizacao,
        CancellationToken cancellationToken)
    {
        var comunicados = (await BuscarPorDataDeAtualizacaoMenorQue(dataDeAtualizacao, cancellationToken))
            .Select(c => c.Id).ToList();

        await _comunicadoRepository.DeleteManyAsync(c => comunicados.Contains(c.Id), cancellationToken);

        return comunicados;
    }

    public async Task<ICollection<string>> ExcluirComunicadosAntigos(CancellationToken cancellationToken)
    {
        var dataDeAtualizacaoLimite = DateTime.Now.AddDays(-_comunicadoSettings.TempoDeVidaDeComunicadoEmDias);
        return await ExcluirPorDataDeAtualizacaoMenorQue(dataDeAtualizacaoLimite, cancellationToken);
    }

    public async Task<IEnumerable<AreaEletricaComunicadoDto>> GetAreasEletricas(CancellationToken cancellationToken)
    {
            return
                await _sinapseDadosDatasetQueryService
                    .GetDatasetAsync(
                        x => x.GetComunicadoAreaEletrica(_userContext.Perfil.Centros.ToArray(), cancellationToken),
                        cancellationToken)
                    .ConfigureAwait(false) ?? Enumerable.Empty<AreaEletricaComunicadoDto>();
    }

    private bool PossuiPermissaoDeAdicionarComplemento(Comunicado comunicado)
    {
        if (PossuiPermissaoDeAdicionarComplemento(comunicado.Origem.Codigo))
            return true;
        _notificationContext.AddNotification("Usuário sem permissão para adicionar complemento neste comunicado.");
        return false;
    }

    private bool PossuiPermissaoDeAdicionarComplemento(string centroComunicado)
    {
        return _userContext.Perfil.Scopes.Any(x => x.Codigo.Equals(centroComunicado, StringComparison.CurrentCultureIgnoreCase));
    }

    private bool Validar(ComunicadoDto dto)
    {
        var validator = new ComunicadoDtoValidator();

        var result = validator.Validate(dto);
        _notificationContext.AddNotifications(result);

        return result.IsValid;
    }

    private ComunicadoDetalheDto GetComunicadoDetalheDto(Comunicado comunicado, Usuario usuario)
    {
        var dto = _mapper.Map<ComunicadoDetalheDto>(comunicado);
        dto.PermiteAdicionarComplemento =
            PossuiPermissaoDeAdicionarComplemento(comunicado.Origem.Codigo);
        dto.Lido = comunicado.IsComunicadoLido(usuario);

        var comunicadoDoUsuario = _userContext.Perfil.Centros.Contains(comunicado.Origem.Codigo);
        
        if (!comunicadoDoUsuario)
            dto.Leitores.Clear();

        return dto;
    }

    private ConsultaDeComunicadosDto GetConsultaDeComunicadosDto()
    {
        var destinatarios = _userContext.Perfil.Centros;

        return new ConsultaDeComunicadosDto
        {
            DataDeAtualizacaoLimite = DateTime.Now.AddDays(-_comunicadoSettings.TempoDeVidaDeComunicadoEmDias).Date,
            CodigosDosDestinatarios = destinatarios,
            CodigoCentroEmitente = destinatarios
        };
    }
}