using ONS.SINapse.Business.IBusiness;
using ONS.SINapse.Entities.Entities.DadosCadastrais;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Repository.IRepository.DadosCadastrais;
using ONS.SINapse.Shared.Constants;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Identity;

namespace ONS.SINapse.Business.Imp.Business;

public class StatusAgenteBusiness : IStatusAgenteBusiness
{
    private readonly IUserContext _userContext;
    private readonly IAgenteRepository _agenteRepository;
    private readonly IAgenteOnlineBusiness _agenteOnlineBusiness;

    public StatusAgenteBusiness(IUserContext userContext,
        IAgenteRepository agenteRepository,
        IAgenteOnlineBusiness agenteOnlineBusiness)
    {
        _userContext = userContext;
        _agenteRepository = agenteRepository;
        _agenteOnlineBusiness = agenteOnlineBusiness;
    }
    
    public async Task<ICollection<StatusAgenteDto>> GetStatusAgentes(CancellationToken cancellationToken)
    {
        var codigoDosCentros = _userContext.Perfil.Centros.ToList();
        
        var agentes = (await _agenteRepository.GetAsync(
            x => 
                x.Centros.Any(codigoDosCentros.Contains) || _userContext.PerfilSelecionadoPossuiOperacao(Operacoes.ConsultarTodosAgentes) 
            , cancellationToken)).ToList();
        
        var agentesInativosTask = _agenteOnlineBusiness.BuscarAgentesInativosAsync(agentes.Select(x => x.Codigo).ToArray(), cancellationToken);
        var agentesOnlineDtpTask = _agenteOnlineBusiness.ObterCodigoAgentesOnlineAsync(cancellationToken);

        await Task.WhenAll(agentesInativosTask, agentesOnlineDtpTask);

        var agentesInativos = agentesInativosTask.Result;
        var agentesOnlineDtp = agentesOnlineDtpTask.Result;

        return agentes.Select(a => new StatusAgenteDto
        (
            a.Codigo,
            a.Nome,
            GetStatusAgente(a.Codigo, agentesOnlineDtp, agentesInativos)
        )).OrderBy(a=> a.Nome).ToList();
    }

    private static string GetStatusAgente(string codigo, string[] agentesOnlineDtp, IEnumerable<string> agentesInativos)
    {
        string status;

        if (agentesOnlineDtp.Contains(codigo))
            status = "Online";
        else if (agentesInativos.Contains(codigo))
            status = "Inativo";
        else
            status = "Offline";

        return status;
    }
}