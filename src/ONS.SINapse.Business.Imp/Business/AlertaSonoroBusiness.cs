using ONS.SINapse.Business.IBusiness;
using ONS.SINapse.Shared.DTO.Firebase;
using ONS.SINapse.Shared.Identity;
using ONS.SINapse.Shared.Services.Firebase;

namespace ONS.SINapse.Business.Imp.Business;

public class AlertaSonoroBusiness : IAlertaSonoroBusiness
{
    private readonly IUserContext _userContext;
    private readonly IAlertaSonoroFirebaseDatabaseService _alertaSonoroFirebaseDatabaseService;

    public AlertaSonoroBusiness(
        IUserContext userContext,
        IAlertaSonoroFirebaseDatabaseService alertaSonoroFirebaseDatabaseService)
    {
        _userContext = userContext;
        _alertaSonoroFirebaseDatabaseService = alertaSonoroFirebaseDatabaseService;
    }
    
    public Task AtivarAsync(CancellationToken cancellationToken)
        => AtivarDesativarAsync(true, cancellationToken);

    public Task DesativarAsync(CancellationToken cancellationToken)
        => AtivarDesativarAsync(false, cancellationToken);
    
    private Task AtivarDesativarAsync(bool ativo, CancellationToken cancellationToken)
    {
        if(_userContext.Perfil.Scopes.Count == 0)
            return Task.CompletedTask;
        
        var alertas = _userContext
            .Perfil
            .Scopes
            .ToDictionary(k => k.Codigo.ToLower(), v => new AlertaSonoroRealtimeDto
            (
                _userContext.Sid,
                _userContext.Nome,
                DateTime.Now,
                ativo
            ));

        return _alertaSonoroFirebaseDatabaseService.SetAsync(alertas, cancellationToken);
    }
}