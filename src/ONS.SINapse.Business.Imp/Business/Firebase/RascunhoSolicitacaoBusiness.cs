using AutoMapper;
using FluentValidation;
using ONS.SINapse.Business.IBusiness.Firebase;
using ONS.SINapse.Business.Imp.Validators.Firebase;
using ONS.SINapse.Entities.Entities;
using ONS.SINapse.Repository.IRepository;
using ONS.SINapse.Shared.DTO.Firebase;
using ONS.SINapse.Shared.Identity;
using ONS.SINapse.Shared.Notifications;

namespace ONS.SINapse.Business.Imp.Business.Firebase;

public class RascunhoSolicitacaoBusiness : IRascunhoSolicitacaoBusiness 
{
    private readonly IRascunhoSolicitacaoRepository _rascunhoSolicitacaoRepository;
    private readonly IRascunhoFavoritoSolicitacaoRepository _rascunhoFavoritoSolicitacaoRepository;
    private readonly NotificationContext _notificationContext;
    private readonly IMapper _mapper;
    private readonly IValidator<RascunhoSolicitacaoDto> _rascunhoValidator;
    private readonly IUserContext _userContext;

    public RascunhoSolicitacaoBusiness(IRascunhoSolicitacaoRepository rascunhoSolicitacaoRepository,
        IRascunhoFavoritoSolicitacaoRepository rascunhoFavoritoSolicitacaoRepository,
        NotificationContext notificationContext,
        IMapper mapper,
        IValidator<RascunhoSolicitacaoDto> rascunhoValidator,
        IUserContext userContext)
    {
        _rascunhoSolicitacaoRepository = rascunhoSolicitacaoRepository;
        _rascunhoFavoritoSolicitacaoRepository = rascunhoFavoritoSolicitacaoRepository;
        _notificationContext = notificationContext;
        _mapper = mapper;
        _rascunhoValidator = rascunhoValidator;
        _userContext = userContext;
    }
    
    public async Task AdicionarRascunhoAsync(RascunhoSolicitacaoDto rascunhoDto, CancellationToken cancellationToken)
    {
        var validation = 
            await _rascunhoValidator.ValidateAsync(rascunhoDto, options =>
            {
                options.IncludeRuleSets(RascunhoSolicitacaoValidator.RegrasDeValidacoes.CampoValidoTemQueSerTrue);
                options.IncludeRulesNotInRuleSet();
            }, cancellationToken);

        if (!validation.IsValid)
        {
            _notificationContext.AddNotifications(validation);
            return;
        }

        var rascunho = _mapper.Map<RascunhoSolicitacao>(rascunhoDto);

        await _rascunhoSolicitacaoRepository.AddUpdateAsync(rascunho, cancellationToken);
    }

    public async Task EditarRascunhoAsync(RascunhoSolicitacaoDto rascunhoDto, CancellationToken cancellationToken)
    {
        var validation = 
            await _rascunhoValidator.ValidateAsync(rascunhoDto, options =>
            {
                options.IncludeRuleSets(RascunhoSolicitacaoValidator.RegrasDeValidacoes.ValidarId);
                options.IncludeRuleSets(RascunhoSolicitacaoValidator.RegrasDeValidacoes.CampoValidoTemQueSerTrue);
                options.IncludeRulesNotInRuleSet();
            }, cancellationToken);

        if (!validation.IsValid)
        {
            _notificationContext.AddNotifications(validation);
            return;
        }

        var rascunho = _mapper.Map<RascunhoSolicitacao>(rascunhoDto);

        await _rascunhoSolicitacaoRepository.AddUpdateAsync(rascunho, cancellationToken);
    }

    public Task InvalidarRascunhoAsync(Guid id, CancellationToken cancellationToken)
    {
        return _rascunhoSolicitacaoRepository.InvalidarAsync(id, cancellationToken);
    }
    
    
    public async Task RemoverRascunhoAsync(Guid id, CancellationToken cancellationToken)
    {
        await _rascunhoSolicitacaoRepository.DeleteAsync(id, cancellationToken);
        await RemoverRascunhoFavoritoAsync(id, _userContext.Perfil.Scopes.First().Codigo, cancellationToken);
    }

    public Task FavoritarRascunhoAsync(FavoritarRascunhoSolicitacaoDto favoritarRascunhoSolicitacao, CancellationToken cancellationToken)
    {
        var path = $"{favoritarRascunhoSolicitacao.CentroSelecionado.Centro}/{_userContext.Sid}/{favoritarRascunhoSolicitacao.Id}";
        return _rascunhoFavoritoSolicitacaoRepository.AddUpdateAsync(path, cancellationToken);
    }

    public Task RemoverRascunhoFavoritoAsync(DesfavoritarRascunhoSolicitacaoDto desfavoritarRascunhoSolicitacao, CancellationToken cancellationToken)
    {
        var path = $"{desfavoritarRascunhoSolicitacao.Centro}/{_userContext.Sid}/{desfavoritarRascunhoSolicitacao.Id}";
        return _rascunhoFavoritoSolicitacaoRepository.DeleteAsync(path, cancellationToken);
    }

    private async Task RemoverRascunhoFavoritoAsync(Guid idRascunho, string centro, CancellationToken cancellationToken)
    {
        var usuarios = await _rascunhoFavoritoSolicitacaoRepository
            .GetRascunhosPorCentroAsync(centro, cancellationToken);

        foreach (var usuario in usuarios)
        {
            var usuarioKey = usuario.Key;
            var rascunhos = usuario.Value;

            if (rascunhos != null && rascunhos.ContainsKey(idRascunho.ToString()))
            {
                var path = $"{centro}/{usuarioKey}/{idRascunho}";
                await _rascunhoFavoritoSolicitacaoRepository.DeleteAsync(path, cancellationToken);
            }
        }
    }
}