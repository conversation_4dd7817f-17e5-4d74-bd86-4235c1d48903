using AutoMapper;
using FluentValidation;
using ONS.SINapse.Business.IBusiness.Firebase;
using ONS.SINapse.Business.Imp.Validators.Firebase;
using ONS.SINapse.Entities.Entities;
using ONS.SINapse.Repository.IRepository;
using ONS.SINapse.Shared.DTO.Firebase;
using ONS.SINapse.Shared.Notifications;

namespace ONS.SINapse.Business.Imp.Business.Firebase;

public class RascunhoSolicitacaoBusiness : IRascunhoSolicitacaoBusiness 
{
    private readonly IRascunhoSolicitacaoRepository _rascunhoSolicitacaoRepository;
    private readonly NotificationContext _notificationContext;
    private readonly IMapper _mapper;
    private readonly IValidator<RascunhoSolicitacaoDto> _rascunhoValidator;

    public RascunhoSolicitacaoBusiness(IRascunhoSolicitacaoRepository rascunhoSolicitacaoRepository, 
        NotificationContext notificationContext,
        I<PERSON><PERSON>per mapper,
        IValidator<RascunhoSolicitacaoDto> rascunhoValidator)
    {
        _rascunhoSolicitacaoRepository = rascunhoSolicitacaoRepository;
        _notificationContext = notificationContext;
        _mapper = mapper;
        _rascunhoValidator = rascunhoValidator;
    }
    
    public async Task AdicionarRascunhoAsync(RascunhoSolicitacaoDto rascunhoDto, CancellationToken cancellationToken)
    {
        var validation = 
            await _rascunhoValidator.ValidateAsync(rascunhoDto, options =>
            {
                options.IncludeRuleSets(RascunhoSolicitacaoValidator.RegrasDeValidacoes.CampoValidoTemQueSerTrue);
                options.IncludeRulesNotInRuleSet();
            }, cancellationToken);

        if (!validation.IsValid)
        {
            _notificationContext.AddNotifications(validation);
            return;
        }

        var rascunho = _mapper.Map<RascunhoSolicitacao>(rascunhoDto);

        await _rascunhoSolicitacaoRepository.AddUpdateAsync(rascunho, cancellationToken);
    }

    public async Task EditarRascunhoAsync(RascunhoSolicitacaoDto rascunhoDto, CancellationToken cancellationToken)
    {
        var validation = 
            await _rascunhoValidator.ValidateAsync(rascunhoDto, options =>
            {
                options.IncludeRuleSets(RascunhoSolicitacaoValidator.RegrasDeValidacoes.ValidarId);
                options.IncludeRuleSets(RascunhoSolicitacaoValidator.RegrasDeValidacoes.CampoValidoTemQueSerTrue);
                options.IncludeRulesNotInRuleSet();
            }, cancellationToken);

        if (!validation.IsValid)
        {
            _notificationContext.AddNotifications(validation);
            return;
        }

        var rascunho = _mapper.Map<RascunhoSolicitacao>(rascunhoDto);

        await _rascunhoSolicitacaoRepository.AddUpdateAsync(rascunho, cancellationToken);
    }

    public Task InvalidarRascunhoAsync(Guid id, CancellationToken cancellationToken)
    {
        return _rascunhoSolicitacaoRepository.InvalidarAsync(id, cancellationToken);
    }
    
    
    public Task RemoverRascunhoAsync(Guid id, CancellationToken cancellationToken)
    {
        return _rascunhoSolicitacaoRepository.DeleteAsync(id, cancellationToken);
    }
}