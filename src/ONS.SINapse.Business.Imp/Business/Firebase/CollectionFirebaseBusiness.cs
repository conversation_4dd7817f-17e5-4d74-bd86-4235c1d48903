using Firebase.Database;
using Microsoft.AspNetCore.Http;
using Newtonsoft.Json;
using ONS.SINapse.Business.IBusiness.Firebase;
using ONS.SINapse.Repository.IRepository.Firebase;
using ONS.SINapse.Shared.DTO;
using System.Text;

namespace ONS.SINapse.Business.Imp.Business.Firebase
{
    public class CollectionFirebaseBusiness : ICollectionFirebaseBusiness
    {
        private readonly ICollectionFirebaseRepository _collectionFirebaseRepository;

        public CollectionFirebaseBusiness(
            ICollectionFirebaseRepository collectionFirebaseRepository) => _collectionFirebaseRepository = collectionFirebaseRepository;

        public async Task PatchAsync(string collectionName, IFormFile jsonFile, CancellationToken cancellationToken)
        {
            using var streamReader = new StreamReader(jsonFile.OpenReadStream());
            var jsonData = await streamReader.ReadToEndAsync(cancellationToken);

            await _collectionFirebaseRepository.PatchAsync(collectionName, jsonData, cancellationToken);
        }

        public async Task PutAsync(string collectionName, IFormFile jsonFile, CancellationToken cancellationToken)
        {
            using var streamReader = new StreamReader(jsonFile.OpenReadStream());
            var jsonData = await streamReader.ReadToEndAsync(cancellationToken);

            await _collectionFirebaseRepository.PutAsync(collectionName, jsonData, cancellationToken);
        }

        public async Task DeleteAsync(
            string collectionName, CancellationToken cancellationToken) => await _collectionFirebaseRepository.DeleteAsync(collectionName, cancellationToken);

        public async Task<ArquivoCollectionDto> GetAsync(string collectionName, CancellationToken cancellationToken)
        {
            var collectionData = await _collectionFirebaseRepository.GetAsync(collectionName, cancellationToken);
            var memoryStream = FirebaseCollectionToStream(collectionData);

            return new ArquivoCollectionDto(memoryStream, collectionName + ".json");
        }

        private static MemoryStream FirebaseCollectionToStream(IReadOnlyCollection<FirebaseObject<object>> collectionData)
        {
            var result = FirebaseCollectionToDictionary(collectionData);
            var json = JsonConvert.SerializeObject(result, Formatting.Indented);
            return new MemoryStream(Encoding.UTF8.GetBytes(json));
        }

        private static Dictionary<string, object> FirebaseCollectionToDictionary(IReadOnlyCollection<FirebaseObject<object>> collectionData)
        {
            var result = new Dictionary<string, object>();

            foreach (var item in collectionData)
            {
                result[item.Key] = item.Object;
            }

            return result;
        }
    }
}
