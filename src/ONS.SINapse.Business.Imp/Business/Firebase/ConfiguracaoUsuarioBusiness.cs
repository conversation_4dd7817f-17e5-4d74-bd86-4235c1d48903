using FluentValidation;
using ONS.SINapse.Business.IBusiness.Firebase;
using ONS.SINapse.Entities.Entities;
using ONS.SINapse.Repository.IRepository;
using ONS.SINapse.Shared.DTO.Firebase;
using ONS.SINapse.Shared.Identity;
using ONS.SINapse.Shared.Notifications;

namespace ONS.SINapse.Business.Imp.Business.Firebase;

public class ConfiguracaoUsuarioBusiness : IConfiguracaoUsuarioBusiness
{
    private readonly IConfiguracaoUsuarioRepository _configuracaoUsuarioRepository;
    private readonly IValidator<NotificacaoConfiguracaoDto> _notificacaoValidator;
    private readonly IUserContext _userContext;
    private readonly NotificationContext _notificationContext;

    public ConfiguracaoUsuarioBusiness(IConfiguracaoUsuarioRepository configuracaoUsuarioRepository,
        IValidator<NotificacaoConfiguracaoDto> notificacaoValidator,
        IUserContext userContext,
        NotificationContext notificationContext)
    {
        _configuracaoUsuarioRepository = configuracaoUsuarioRepository;
        _notificacaoValidator = notificacaoValidator;
        _userContext = userContext;
        _notificationContext = notificationContext;
    }
    
    public async Task AdicionarConfiguracaoNotificacaoAsync(NotificacaoConfiguracaoDto notificacaoConfiguracao, CancellationToken cancellationToken)
    {
        var validation = 
            await _notificacaoValidator.ValidateAsync(notificacaoConfiguracao, cancellationToken);

        if (!validation.IsValid)
        {
            _notificationContext.AddNotifications(validation);
            return;
        }

        var notificacao = NotificacaoConfiguracao.FromDto(notificacaoConfiguracao);
        var configuracao = new ConfiguracaoUsuario(notificacao);

        await _configuracaoUsuarioRepository.AddUpdateAsync(configuracao, _userContext.Sid, cancellationToken);
    }
}
