using Microsoft.Extensions.Options;
using ONS.SINapse.Business.IBusiness.Firebase;
using ONS.SINapse.Shared.DTO.Firebase;
using ONS.SINapse.Shared.Services.Firebase;
using ONS.SINapse.Shared.Settings;

namespace ONS.SINapse.Business.Imp.Business.Firebase;

public class NotificacaoFirebaseBusiness : INotificacaoFirebaseBusiness
{
    private readonly INotificacaoFirebaseDatabaseService _notificacaoFirebaseDatabaseService;
    private readonly IOptions<ConfiguracaoDeNotificacaoSettings> _configuracaoDeNotificacaoOptions;

    public NotificacaoFirebaseBusiness(
        INotificacaoFirebaseDatabaseService notificacaoFirebaseDatabaseService,
        IOptions<ConfiguracaoDeNotificacaoSettings> configuracaoDeNotificacaoOptions)
    {
        _notificacaoFirebaseDatabaseService = notificacaoFirebaseDatabaseService;
        _configuracaoDeNotificacaoOptions = configuracaoDeNotificacaoOptions;
    }

    private ConfiguracaoDeNotificacaoSettings GetConfiguracaoDeNotificacao()
    {
        return _configuracaoDeNotificacaoOptions.Value;
    }

    public Task SendAsync(IEnumerable<NotificacaoRealtimeDto> notificacoes, CancellationToken cancellationToken)
    {
        var config = GetConfiguracaoDeNotificacao();
        
        var collection = notificacoes
            .GroupBy(
                group => group.Destination,
                by => by)
            .ToDictionary(k => k.Key, v =>
            {
                return v.ToDictionary(_ => Guid.NewGuid().ToString(), notificacaoRealtimeDto =>
                {

                    if (notificacaoRealtimeDto.ExpirationDate < 1)
                    {
                        notificacaoRealtimeDto.ExpirationDate = DateTimeOffset.Now
                            .AddSeconds(config.TempoExpiracaoEmSegundos).ToUnixTimeSeconds();
                    }

                    AdicionarPrefixo(notificacaoRealtimeDto, config);
                    AdicionarSufixo(notificacaoRealtimeDto, config);

                    return notificacaoRealtimeDto;
                });
            });

        return _notificacaoFirebaseDatabaseService.SetAsync(collection, cancellationToken);
    }
    
    public Task DeleteAllAsync(CancellationToken cancellationToken)
    {
        return _notificacaoFirebaseDatabaseService.DeleteAllAsync(cancellationToken);
    }

    private static void AdicionarPrefixo(NotificacaoRealtimeDto notificacaoRealtimeDto,
        ConfiguracaoDeNotificacaoSettings config)
    {
        if (config.Prefixo == null)
        {
            return;
        }

        if (notificacaoRealtimeDto.Title.StartsWith(config.Prefixo))
        {
            return;
        }
        notificacaoRealtimeDto.Title = $"{config.Prefixo} {notificacaoRealtimeDto.Title}".Trim();
    }
    
    private static void AdicionarSufixo(NotificacaoRealtimeDto notificacaoRealtimeDto,
        ConfiguracaoDeNotificacaoSettings config)
    {
        if (config.Sufixo == null)
        {
            return;
        }

        if (notificacaoRealtimeDto.Title.EndsWith(config.Sufixo))
        {
            return;
        }
        notificacaoRealtimeDto.Title = $"{notificacaoRealtimeDto.Title} {config.Sufixo}".Trim();
    }
}