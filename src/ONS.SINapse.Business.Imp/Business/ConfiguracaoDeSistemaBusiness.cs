using Microsoft.Extensions.Options;
using ONS.SINapse.Business.IBusiness;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Settings;

namespace ONS.SINapse.Business.Imp.Business;

public class ConfiguracaoDeSistemaBusiness : IConfiguracaoDeSistemaBusiness
{
    private readonly ConfiguracoesPopAuthSettings _configuracaoDePopAuth;

    public ConfiguracaoDeSistemaBusiness(
        IOptions<ConfiguracoesPopAuthSettings> configuracaoDePopAuth)
    {
        _configuracaoDePopAuth = configuracaoDePopAuth.Value;
    }

    public ConfiguracaoDeSistemaDto Get()
    {
        return new ConfiguracaoDeSistemaDto
        (
            _configuracaoDePopAuth.Origin,
            _configuracaoDePopAuth.ClientId,
            _configuracaoDePopAuth.GrantTypeAccessToken,
            _configuracaoDePopAuth.GrantTypeRefreshToken,
            _configuracaoDePopAuth.TokenUrl,
            _configuracaoDePopAuth.UrlLogout
        );
    }
}
