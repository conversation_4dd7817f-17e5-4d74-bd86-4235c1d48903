using Microsoft.Extensions.DependencyInjection;
using ONS.SINapse.Business.IBusiness;
using ONS.SINapse.Business.IBusiness.Firebase;
using ONS.SINapse.Business.IBusiness.Integracao;
using ONS.SINapse.Business.IBusiness.TemplatesDeSolicitacao;
using ONS.SINapse.Business.Imp.Business;
using ONS.SINapse.Business.Imp.Business.Auth;
using ONS.SINapse.Business.Imp.Business.Extracoes;
using ONS.SINapse.Business.Imp.Business.Firebase;
using ONS.SINapse.Business.Imp.Business.Integracao;
using ONS.SINapse.Business.Imp.Business.TemplatesDeSolicitacao;

namespace ONS.SINapse.Business.Imp;

public static class DependencyInjector
{
    public static IServiceCollection RegisterBusinessLayer(this IServiceCollection services)
    {
        services.RegisterBusiness();
        
        return services;
    }

    private static void RegisterBusiness(this IServiceCollection services)
    {
        services.AddScoped<IPerfilDeUsuarioBusiness, PerfilDeUsuarioBusiness>();
        services.AddScoped<IVisaoDeUsuarioBusiness, VisaoDeUsuarioBusiness>();
        services.AddScoped<IComunicadoBusiness, ComunicadoBusiness>();
        services.AddScoped<INotificacaoDeComunicadoBusiness, NotificacaoDeComunicadoBusiness>();
        services.AddScoped<IConfiguracaoDeSistemaBusiness, ConfiguracaoDeSistemaBusiness>();
        services.AddScoped<IAuthBusiness, AuthBusiness>();
        services.AddScoped<IAgenteIntegracaoBusiness, AgenteIntegracaoBusiness>();
        services.AddScoped<INotificacaoFirebaseBusiness, NotificacaoFirebaseBusiness>();
        services.AddScoped<ITemplateDeSolicitacaoBusiness, TemplateDeSolicitacaoStep1Business>();
        services.AddScoped<ITemplateDeSolicitacaoStep1Business, TemplateDeSolicitacaoStep1Business>();
        services.AddScoped<ITemplateDeSolicitacaoBusiness, TemplateDeSolicitacaoStep3Business>();
        services.AddScoped<ITemplateDeSolicitacaoStep3Business, TemplateDeSolicitacaoStep3Business>();
        services.AddScoped<ITemplateDeSolicitacaoBusiness, TemplateDeSolicitacaoStep4Business>();
        services.AddScoped<ITemplateDeSolicitacaoStep4Business, TemplateDeSolicitacaoStep4Business>();
        services.AddScoped<ITemplateDeSolicitacaoBusiness, TemplateDeSolicitacaoStep5Business>();
        services.AddScoped<ITemplateDeSolicitacaoStep5Business, TemplateDeSolicitacaoStep5Business>();
        services.AddScoped<IStatusAgenteBusiness, StatusAgenteBusiness>();
        services.AddScoped<IAlertaSonoroBusiness, AlertaSonoroBusiness>();
        services.AddScoped<ITagBusiness, TagBusiness>();
        services.AddScoped<IRascunhoSolicitacaoBusiness, RascunhoSolicitacaoBusiness>();
        services.AddScoped<IAgenteOnlineBusiness, AgenteOnlineBusiness>();
        services.AddScoped<IRespostaDaChamadaBusiness, RespostaDaChamadaBusiness>();
        services.AddScoped<IControleDeChamadaBusiness, ControleDeChamadaBusiness>();
        services.AddScoped<IHistoricoDeAcessoBusiness, HistoricoDeAcessoBusiness>();
        services.AddScoped<IAlterarVersaoBusiness, AlterarVersaoBusiness>();
        services.AddScoped<IExtracaoDeSolicitacaoS3Business, ExtracaoDeSolicitacaoS3Business>();
        services.AddScoped<ICollectionFirebaseBusiness, CollectionFirebaseBusiness>();
        services.AddScoped<IExportacaoTemplatesDeSolicitacaoBusiness, ExportacaoTemplatesDeSolicitacaoBusiness>();
    }
}