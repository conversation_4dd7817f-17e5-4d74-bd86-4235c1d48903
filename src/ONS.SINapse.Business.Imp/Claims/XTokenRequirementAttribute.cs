using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using ONS.SINapse.Shared.Settings;

namespace ONS.SINapse.Business.Imp.Claims;


public enum XTokenRequirementOrigin
{
     LambdaAws, 
     KongOns
}

public class XTokenRequirementAttribute : TypeFilterAttribute
{
    public XTokenRequirementAttribute(XTokenRequirementOrigin origin)
        : base(typeof(XTokenRequirementFilter))
    {
        Arguments = new object[] { origin };
    }
}

public class XTokenRequirementFilter : IAuthorizationFilter
{
    private readonly XTokenRequirementOrigin _origin;
    private readonly XTokenRequirementSettings _settings;

    public XTokenRequirementFilter(XTokenRequirementOrigin origin,
        IOptions<XTokenRequirementSettings> settings)
    {
        _origin = origin;
        _settings = settings.Value;
    }

    public void OnAuthorization(AuthorizationFilterContext context)
    {
        context.HttpContext.Request.Headers.TryGetValue("X-Token", out var xToken);

        if (string.IsNullOrEmpty(xToken))
        {
            context.Result = new ForbidResult();
            return;
        }

        var hasClaim = _origin switch
        {
            XTokenRequirementOrigin.LambdaAws => xToken == _settings.LambdaAws,
            XTokenRequirementOrigin.KongOns => xToken == _settings.KongOns,
            _ => false
        };

        if (!hasClaim)
        {
            context.Result = new ForbidResult();
        }
    }
}