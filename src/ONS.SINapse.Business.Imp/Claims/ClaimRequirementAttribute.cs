using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using ONS.SINapse.Shared.Identity;

namespace ONS.SINapse.Business.Imp.Claims;

public class ClaimRequirementAttribute : TypeFilterAttribute
{
    public ClaimRequirementAttribute(string claimType, bool validAll, params string[] claimValue)
        : base(typeof(ClaimRequirementFilter))
    {
        Arguments = new object[] { new ClaimValidation(claimType, claimValue, validAll) };
    }
    
    public ClaimRequirementAttribute(string claimType, params string[] claimValue)
        : base(typeof(ClaimRequirementFilter))
    {
        Arguments = new object[] { new ClaimValidation(claimType, claimValue, true) };
    }
    
}

public class ClaimRequirementFilter : IAuthorizationFilter
{
    private readonly ClaimValidation _claim;
    private readonly ILogger<UserClaimRequirementFilter> _logger;

    public ClaimRequirementFilter(ClaimValidation claim, ILogger<UserClaimRequirementFilter> logger)
    {
        _claim = claim;
        _logger = logger;
    }

    public void OnAuthorization(AuthorizationFilterContext context)
    {
        if (!context.HttpContext.User.Identity?.IsAuthenticated ?? false)
        {
            _logger.LogError("Usuário não autenticado.");
            context.Result = new StatusCodeResult(401);
            return;
        }
        
        if(_claim.Claims.Length == 0) return;
        
        bool hasClaim;

        if (_claim.ValidarTodos)
            hasClaim = _claim.Claims
                .All(x => context.HttpContext.User.Claims.Any(n => n.Type == _claim.Tipo && n.Value == x));
        else
            hasClaim = _claim.Claims
                .Any(x => context.HttpContext.User.Claims.Any(n => n.Type == _claim.Tipo && n.Value == x));
        
        if (hasClaim) return;
        
        _logger.LogError("Usuário não tem permissão para acessar.");
        context.Result = new ForbidResult();
    }
}


public class UserClaimRequirementAttribute : TypeFilterAttribute
{
    public UserClaimRequirementAttribute(bool validarTodos, params string[] operations) : base(typeof(UserClaimRequirementFilter))
    {
        Arguments = new object[] { new ClaimValidation(PopClaimTypes.Operation, operations, validarTodos) };
    }
    
    public UserClaimRequirementAttribute(params string[] operations) : base(typeof(UserClaimRequirementFilter))
    {
        Arguments = new object[] { new ClaimValidation(PopClaimTypes.Operation, operations, true) };
    }
}

public class UserClaimRequirementFilter : IAuthorizationFilter 
{
    private readonly ClaimValidation _claims;
    private readonly IUserContext _userContext;
    private readonly ILogger<UserClaimRequirementFilter> _logger;

    public UserClaimRequirementFilter(ClaimValidation claims, IUserContext userContext, ILogger<UserClaimRequirementFilter> logger)
    {
        _claims = claims;
        _userContext = userContext;
        _logger = logger;
    }
    
    public void OnAuthorization(AuthorizationFilterContext context)
    {
        if (!context.HttpContext.User.Identity?.IsAuthenticated ?? false)
        {
            _logger.LogError("Usuário não autenticado.");
            context.Result = new StatusCodeResult(401);
            return;
        }
        
        if(_claims.Claims.Length == 0) return;

        var operationSelecionadas = _userContext
            .OperationsSelecionadas();

        bool hasSelecionado;
        
        if (_claims.ValidarTodos)
            hasSelecionado = _claims.Claims
                .All(x =>
                {
                    var contain = operationSelecionadas.Contains(x);
                    if (!contain)
                        _logger.LogError("Usuário não possui a operation {Claim} para o perfil selecionado {Perfil}", x,
                            _userContext.Perfil);

                    return contain;
                });
        else
            hasSelecionado = _claims.Claims
                .Any(x =>
                {
                    var contain = operationSelecionadas.Contains(x);
                    if (!contain)
                        _logger.LogInformation("Usuário não possui a operation {Claim} para o perfil selecionado {Perfil}", x,
                            _userContext.Perfil);

                    return contain;
                });
        
        if (hasSelecionado) return;
        
        _logger.LogError("Usuário com perfil selecionado não tem permissão para acessar.");
        context.Result = new ForbidResult();
    }
}

public record ClaimValidation(string Tipo, string[] Claims, bool ValidarTodos);