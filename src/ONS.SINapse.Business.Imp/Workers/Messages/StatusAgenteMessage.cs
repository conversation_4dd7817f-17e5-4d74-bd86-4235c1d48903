using ONS.SINapse.Shared.Kafka;

namespace ONS.SINapse.Business.Imp.Workers.Messages;

public class StatusAgenteMessage : IIntegrationKafka
{
    public StatusAgenteMessage(
        string ageId,
        DateTime dataDaConsulta,
        short status,
        string descricaoDoStatus)
    { 
        AgeId = ageId;
        DataDaConsulta = dataDaConsulta;
        Status = status;
        DescricaoDoStatus = descricaoDoStatus;
    }

    public string AgeId { get; set; }
    public DateTime DataDaConsulta { get; set; }
    public short Status { get; set; }
    public string DescricaoDoStatus { get; set; }
}