using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using ONS.SINapse.Business.IBusiness;

namespace ONS.SINapse.Business.Imp.Workers.ControleDeChamada;

public class IniciarChamadaWorker : BackgroundService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<IniciarChamadaWorker> _logger;

    public IniciarChamadaWorker(
        IServiceProvider serviceProvider,
        ILogger<IniciarChamadaWorker> logger
    )
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        using var scope = _serviceProvider.CreateScope();
        
        var controleDeChamadaBusiness = scope.ServiceProvider.GetRequiredService<IControleDeChamadaBusiness>();
        
        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await controleDeChamadaBusiness.Executar(stoppingToken);
            }
            catch (Exception ex)
            {
                _logger.LogInformation("Erro ao executar Chamadas: {Message}", ex.Message);
            }
            finally
            {
                await controleDeChamadaBusiness.ExecutarDelay(stoppingToken);
            }
        }
    }
}