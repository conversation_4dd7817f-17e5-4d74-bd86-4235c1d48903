using ONS.SINapse.Dados.Entities;
using ONS.SINapse.Dados.Repository.Base;

namespace ONS.SINapse.Dados.SyncData.Services;

public interface IAtualizacaoDeDadosService
{
    Task AtualizarAsync(string collection, CancellationToken cancellationToken);
    Task<string> BuscarAsync(string collection, CancellationToken cancellationToken);
    Task<Dictionary<string, string>> BuscarAsync(string[] collections, CancellationToken cancellationToken);
}

public class AtualizacaoDeDadosService : IAtualizacaoDeDadosService
{
    private readonly IMongoRepository<RegistroDeAtualizacaoDeDados> _repository;

    public AtualizacaoDeDadosService(IMongoRepository<RegistroDeAtualizacaoDeDados> repository)
    {
        _repository = repository;
    }

    public async Task AtualizarAsync(string collection, CancellationToken cancellationToken)
    {
        var registro = 
            await _repository.GetOneAsync(x => x.Collection.Equals(collection, StringComparison.OrdinalIgnoreCase), cancellationToken);

        if (registro is null)
        {
            await InserirAsync(collection, cancellationToken);
            return;
        }

        await AtualizarAsync(registro, cancellationToken);
    }

    public async Task<string> BuscarAsync(string collection, CancellationToken cancellationToken)
    {
        var registro = 
            await _repository.GetOneAsync(x => x.Collection.Equals(collection, StringComparison.OrdinalIgnoreCase), cancellationToken);

        return registro?.Versao ?? string.Empty;
    }
    
    public async Task<Dictionary<string, string>> BuscarAsync(string[] collections, CancellationToken cancellationToken)
    {
        var registros = await _repository.GetAsync(x => collections.Contains(x.Collection), cancellationToken)
            .ConfigureAwait(false);
        
        return registros.ToDictionary(k => k.Collection, v => v.Versao);
    }
    
    private Task<RegistroDeAtualizacaoDeDados> InserirAsync(string collection, CancellationToken cancellationToken)
    {
        var registro = new RegistroDeAtualizacaoDeDados(collection);
        return _repository.AddAsync(registro, cancellationToken);
    }

    private Task AtualizarAsync(RegistroDeAtualizacaoDeDados registro, CancellationToken cancellationToken)
    {
        registro.AtualizarVersao();
        return _repository.UpdateAsync(registro, cancellationToken);
    }
}