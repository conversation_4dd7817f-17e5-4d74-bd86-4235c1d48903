using ONS.SINapse.Dados.Business.Sync;
using ONS.SINapse.Dados.Entities;
using ONS.SINapse.Dados.SyncData.Constants;
using ONS.SINapse.Dados.SyncData.Services;
using ONS.SINapse.Dados.SyncData.Syncs.QueriesSync.Base;

namespace ONS.SINapse.Dados.SyncData.Syncs.QueriesSync;

public interface IAreaEletricaQuerySync : IQuerySync
{
}

public sealed class AreaEletricaQuerySync : QuerySync, IAreaEletricaQuerySync
{
    private readonly ISyncBusiness<AreaEletrica> _areaEletricaSyncBusiness;

    public AreaEletricaQuerySync(ISyncBusiness<AreaEletrica> areaEletricaSyncBusiness, IAtualizacaoDeDadosService atualizacaoDeDadosService)
        : base(atualizacaoDeDadosService)
    {
        _areaEletricaSyncBusiness = areaEletricaSyncBusiness;
    }

    protected override IEnumerable<string> Dependencias
        => new[]
        {
            TabelasSync.AreaEletrica,
            TabelasSync.Instalacao
        };

    public override string TabelaDestino => ColecoesDeDestinoSync.AreaEletrica;

    protected override async Task SyncAsync(CancellationToken cancellationToken)
        => await _areaEletricaSyncBusiness.SaveAsync(cancellationToken);
}