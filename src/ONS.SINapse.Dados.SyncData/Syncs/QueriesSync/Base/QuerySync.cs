using ONS.SINapse.Dados.SyncData.Services;

namespace ONS.SINapse.Dados.SyncData.Syncs.QueriesSync.Base;

public interface IQuerySync
{
    Task HandleAsync(IEnumerable<string> tiposDeObjetos, CancellationToken cancellationToken);
    bool CanHandle(IEnumerable<string> tiposDeObjetos);
    string TabelaDestino { get; }
}

public abstract class QuerySync : IQuerySync
{
    private readonly IAtualizacaoDeDadosService _atualizacaoDeDadosService;

    protected QuerySync(IAtualizacaoDeDadosService atualizacaoDeDadosService)
    {
        _atualizacaoDeDadosService = atualizacaoDeDadosService;
    }
    
    protected abstract IEnumerable<string> Dependencias { get; }
    public abstract string TabelaDestino { get; }
    
    public bool CanHandle(IEnumerable<string> tiposDeObjetos)
    {
        var dependencias = Dependencias.Select(dependencia => dependencia.ToLower());
        return tiposDeObjetos.Any(tipoDeObjeto => dependencias.Contains(tipoDeObjeto.ToLower()));
    }

    public async Task HandleAsync(IEnumerable<string> tiposDeObjetos, CancellationToken cancellationToken)
    {
        if (!CanHandle(tiposDeObjetos)) return;

        await SyncAsync(cancellationToken);
        await _atualizacaoDeDadosService.AtualizarAsync(TabelaDestino, cancellationToken);
    }

    protected abstract Task SyncAsync(CancellationToken cancellationToken);
}