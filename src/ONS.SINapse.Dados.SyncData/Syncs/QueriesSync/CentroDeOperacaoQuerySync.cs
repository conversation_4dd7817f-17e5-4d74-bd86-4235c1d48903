using ONS.SINapse.Dados.Business.Sync;
using ONS.SINapse.Dados.Entities;
using ONS.SINapse.Dados.SyncData.Constants;
using ONS.SINapse.Dados.SyncData.Services;
using ONS.SINapse.Dados.SyncData.Syncs.QueriesSync.Base;

namespace ONS.SINapse.Dados.SyncData.Syncs.QueriesSync;

public interface ICentroDeOperacaoQuerySync : IQuerySync
{
}

public sealed class CentroDeOperacaoQuerySync : QuerySync, ICentroDeOperacaoQuerySync
{
    private readonly ISyncBusiness<CentroDeOperacao> _centroDeOperacaoSyncBusiness;

    public CentroDeOperacaoQuerySync(ISyncBusiness<CentroDeOperacao> centroDeOperacaoSyncBusiness, IAtualizacaoDeDadosService atualizacaoDeDadosService)
        : base(atualizacaoDeDadosService)
    {
        _centroDeOperacaoSyncBusiness = centroDeOperacaoSyncBusiness;
    }

    protected override IEnumerable<string> Dependencias
        => new[] { TabelasSync.CentroDeOperacao };

    public override string TabelaDestino => ColecoesDeDestinoSync.CentroDeOperacao;

    protected override async Task SyncAsync(CancellationToken cancellationToken) 
        => await _centroDeOperacaoSyncBusiness.SaveAsync(cancellationToken);
}