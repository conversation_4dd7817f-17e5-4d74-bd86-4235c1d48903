using ONS.SINapse.Dados.Business.Sync;
using ONS.SINapse.Dados.Entities;
using ONS.SINapse.Dados.SyncData.Constants;
using ONS.SINapse.Dados.SyncData.Services;
using ONS.SINapse.Dados.SyncData.Syncs.QueriesSync.Base;

namespace ONS.SINapse.Dados.SyncData.Syncs.QueriesSync;

public interface IEloQuerySync : IQuerySync
{
    
}

public class EloQuerySync : QuerySync, IEloQuerySync
{
    private readonly ISyncBusiness<Elo> _eloSyncBusiness;

    public EloQuerySync(ISyncBusiness<Elo> eloSyncBusiness, IAtualizacaoDeDadosService atualizacaoDeDadosService)
        : base(atualizacaoDeDadosService)
    {
        _eloSyncBusiness = eloSyncBusiness;
    }

    protected override IEnumerable<string> Dependencias => new[] { TabelasSync.Elo };
    public override string TabelaD<PERSON><PERSON> => ColecoesDeDestinoSync.Elo;
    
    protected override async Task SyncAsync(CancellationToken cancellationToken)
        => await _eloSyncBusiness.SaveAsync(cancellationToken);
}