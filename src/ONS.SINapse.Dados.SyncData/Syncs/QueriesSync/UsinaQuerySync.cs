using ONS.SINapse.Dados.Business.Sync;
using ONS.SINapse.Dados.Entities;
using ONS.SINapse.Dados.SyncData.Constants;
using ONS.SINapse.Dados.SyncData.Services;
using ONS.SINapse.Dados.SyncData.Syncs.QueriesSync.Base;

namespace ONS.SINapse.Dados.SyncData.Syncs.QueriesSync;

public interface IUsinaQuerySync : IQuerySync
{
}

public class UsinaQuerySync : QuerySync, IUsinaQuerySync
{
    private readonly ISyncBusiness<Usina> _usinaSyncBusiness;
    public UsinaQuerySync(ISyncBusiness<Usina> usinaSyncBusiness, IAtualizacaoDeDadosService atualizacaoDeDadosService)
        : base(atualizacaoDeDadosService)
    {
        _usinaSyncBusiness = usinaSyncBusiness;
    }

    protected override IEnumerable<string> Dependencias
        => new[]
        {
            TabelasSync.Usina,
            TabelasSync.UnidadeGeradora,
            TabelasSync.Equipamento,
            TabelasSync.Agente,
            TabelasSync.NivelDeTensao,
            TabelasSync.NivelDeTensaoDeUsina,
            TabelasSync.Bacia,
            TabelasSync.Aprovacao,
            TabelasSync.Reservatorio,
            TabelasSync.TipoDeUnidadeGeradora
        };

    public override string TabelaDestino => ColecoesDeDestinoSync.Usina;

    protected override async Task SyncAsync(CancellationToken cancellationToken)
        => await _usinaSyncBusiness.SaveAsync(cancellationToken);
}