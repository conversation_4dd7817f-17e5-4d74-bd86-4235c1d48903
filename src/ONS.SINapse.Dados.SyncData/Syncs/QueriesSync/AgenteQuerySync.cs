using ONS.SINapse.Dados.Business.Sync;
using ONS.SINapse.Dados.Entities;
using ONS.SINapse.Dados.SyncData.Constants;
using ONS.SINapse.Dados.SyncData.Services;
using ONS.SINapse.Dados.SyncData.Syncs.QueriesSync.Base;

namespace ONS.SINapse.Dados.SyncData.Syncs.QueriesSync;

public interface IAgenteQuerySync : IQuerySync
{
}

public sealed class AgenteQuerySync : QuerySync, IAgenteQuerySync
{
    private readonly ISyncBusiness<Agente> _agenteSyncBusiness;

    public AgenteQuerySync(ISyncBusiness<Agente> agenteSyncBusiness, IAtualizacaoDeDadosService atualizacaoDeDadosService)
        : base(atualizacaoDeDadosService)
    {
        _agenteSyncBusiness = agenteSyncBusiness;
    }

    protected override IEnumerable<string> Dependencias
        => new[] { TabelasSync.Agente, TabelasSync.Equipamento };

    public override string TabelaDestino => ColecoesDeDestinoSync.Agente;

    protected override async Task SyncAsync(CancellationToken cancellationToken)
        => await _agenteSyncBusiness.SaveAsync(cancellationToken);
    
}