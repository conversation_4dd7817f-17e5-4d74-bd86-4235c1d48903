using ONS.SINapse.Dados.Business.Sync;
using ONS.SINapse.Dados.Entities;
using ONS.SINapse.Dados.SyncData.Constants;
using ONS.SINapse.Dados.SyncData.Services;
using ONS.SINapse.Dados.SyncData.Syncs.QueriesSync.Base;

namespace ONS.SINapse.Dados.SyncData.Syncs.QueriesSync;

public interface IEquipamentoQuerySync : IQuerySync
{
    
}

public class EquipamentoQuerySync : QuerySync, IEquipamentoQuerySync
{
    private readonly ISyncBusiness<Equipamento> _equipamentoSyncBusiness;

    public EquipamentoQuerySync(ISyncBusiness<Equipamento> equipamentoSyncBusiness, IAtualizacaoDeDadosService atualizacaoDeDadosService)
        : base(atualizacaoDeDadosService)
    {
        _equipamentoSyncBusiness = equipamentoSyncBusiness;
    }

    protected override IEnumerable<string> Dependencias => new[] { TabelasSync.Equipamento };
    public override string TabelaDestino => ColecoesDeDestinoSync.Equipamento;
    
    protected override async Task SyncAsync(CancellationToken cancellationToken)
        => await _equipamentoSyncBusiness.SaveAsync(cancellationToken);
}