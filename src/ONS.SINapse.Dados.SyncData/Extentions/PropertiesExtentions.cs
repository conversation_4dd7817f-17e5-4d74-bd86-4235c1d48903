using ONS.SINapse.Dados.Entities;

namespace ONS.SINapse.Dados.SyncData.Extentions;

internal static class PropertiesExtentions
{
    public static Dictionary<string, string?> ToDictionary(this IEnumerable<SyncDatabaseProperty> props)
    {
        return props.ToDictionary(prod => prod.Nome.Trim(), prod => prod.Valor?.Trim());
    }

    public static string? GetValueByKey(this IEnumerable<SyncDatabaseProperty> props, string key)
    {
        return props.ToDictionary().FirstOrDefault(d => d.Key == key.Trim()).Value?.Trim();
    }
    
    public static int ParseIntValue(this Dictionary<string, string?> props, string campo)
    {
        var field = props.GetValueOrDefault(campo);
        if (string.IsNullOrEmpty(field)) return 0;

        return int.TryParse(field, out var result) ? result : 0;
    }
    
    public static string ParseStringValue(this Dictionary<string, string?> props, string campo)
    {
        var field = props.GetValueOrDefault(campo);
        return string.IsNullOrEmpty(field) ? string.Empty : field;
    }
    public static DateTime? ParseDateTimeValue(this Dictionary<string, string?> props, string campo)
    {
        var field = props.GetValueOrDefault(campo);
        if (string.IsNullOrEmpty(field)) return null;

        return DateTime.TryParse(field, result:out var result) ? result : null;
    }
}