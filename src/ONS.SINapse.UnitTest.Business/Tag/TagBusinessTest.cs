using ONS.SINapse.Business.Imp.Business;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.DTO.Firebase;
using ONS.SINapse.Shared.Notifications;

namespace ONS.SINapse.UnitTest.Business.Tag;

public partial class TagBusinessTest
{
    [Fact(DisplayName = "[TagBusiness] - Deve adicionar tag válida que não existe")]
    [Trait("Business", "TagBusiness")]
    public async Task AddAsync_DeveAdicionarTagValida()
    {
        ConfigurarMocksTagBusiness(tagJaExiste: false);
        var sut = _dependencyInjectorFactory.Mocker.CreateInstance<TagBusiness>();
        var dto = new TagDto { Nome = "MinhaTag" };

        var result = await sut.AddAsync(dto, CancellationToken.None);

        Assert.NotNull(result);
        Assert.Equal("MinhaTag", result.Nome);
    }

    [Fact(DisplayName = "[TagBusiness] - Não deve adicionar tag já existente")]
    [Trait("Business", "TagBusiness")]
    public async Task AddAsync_DeveNotificarSeTagJaExiste()
    {
        ConfigurarMocksTagBusiness(tagJaExiste: true);
        var sut = _dependencyInjectorFactory.Mocker.CreateInstance<TagBusiness>();
        var dto = new TagDto { Nome = "MinhaTag" };

        var result = await sut.AddAsync(dto, CancellationToken.None);

        Assert.Null(result);
        Assert.Contains(_dependencyInjectorFactory.Mocker.Get<NotificationContext>().Notifications,
            n => n.Message.Contains("Tag 'MinhaTag' já existe."));
    }

    [Fact(DisplayName = "[TagBusiness] - Não deve adicionar tag inválida")]
    [Trait("Business", "TagBusiness")]
    public async Task AddAsync_DeveNotificarSeTagInvalida()
    {
        ConfigurarMocksTagBusiness();
        var sut = _dependencyInjectorFactory.Mocker.CreateInstance<TagBusiness>();
        var dto = new TagDto { Nome = "" };

        var result = await sut.AddAsync(dto, CancellationToken.None);

        Assert.Null(result);
        Assert.Contains(_dependencyInjectorFactory.Mocker.Get<NotificationContext>().Notifications,
            n => n.Message.Contains("Nome da tag não informado."));
    }

    [Fact(DisplayName = "[TagBusiness] - Deve atualizar tag com nome diferente")]
    [Trait("Business", "TagBusiness")]
    public async Task UpdateAsync_DeveAtualizarNomeDiferente()
    {
        var tagExistente = new TagRealtimeDto("TestSid", "OldName");
        ConfigurarMocksTagBusiness(tagExistente: tagExistente, tagJaExiste: false);

        var sut = _dependencyInjectorFactory.Mocker.CreateInstance<TagBusiness>();
        var dto = new TagDto { Nome = "NewName" };

        var result = await sut.UpdateAsync("id", dto, CancellationToken.None);

        Assert.NotNull(result);
        Assert.Equal("NewName", result.Nome);
    }

    [Fact(DisplayName = "[TagBusiness] - Deve retornar tag existente se nome igual")]
    [Trait("Business", "TagBusiness")]
    public async Task UpdateAsync_DeveRetornarTagSeNomeIgual()
    {
        var tagExistente = new TagRealtimeDto("TestSid", "SameName");
        ConfigurarMocksTagBusiness(tagExistente: tagExistente);

        var sut = _dependencyInjectorFactory.Mocker.CreateInstance<TagBusiness>();
        var dto = new TagDto { Nome = "SameName" };

        var result = await sut.UpdateAsync("id", dto, CancellationToken.None);

        Assert.NotNull(result);
        Assert.Equal("SameName", result.Nome);
    }

    [Fact(DisplayName = "[TagBusiness] - Não deve atualizar se nova tag já existe")]
    [Trait("Business", "TagBusiness")]
    public async Task UpdateAsync_DeveNotificarSeNovaTagJaExiste()
    {
        var tagExistente = new TagRealtimeDto("TestSid", "OldName");
        ConfigurarMocksTagBusiness(tagExistente: tagExistente, tagJaExiste: true);

        var sut = _dependencyInjectorFactory.Mocker.CreateInstance<TagBusiness>();
        var dto = new TagDto { Nome = "NewName" };

        var result = await sut.UpdateAsync("id", dto, CancellationToken.None);

        Assert.Null(result);
        Assert.Contains(_dependencyInjectorFactory.Mocker.Get<NotificationContext>().Notifications,
            n => n.Message.Contains("Tag 'NewName' já existe."));
    }

    [Fact(DisplayName = "[TagBusiness] - Não deve atualizar se tag não localizada")]
    [Trait("Business", "TagBusiness")]
    public async Task UpdateAsync_DeveNotificarSeTagNaoEncontrada()
    {
        ConfigurarMocksTagBusiness(tagExistente: null);

        var sut = _dependencyInjectorFactory.Mocker.CreateInstance<TagBusiness>();
        var dto = new TagDto { Nome = "Name" };

        var result = await sut.UpdateAsync("id", dto, CancellationToken.None);

        Assert.Null(result);
        Assert.Contains(_dependencyInjectorFactory.Mocker.Get<NotificationContext>().Notifications,
            n => n.Message.Contains("Tag não localizada"));
    }

    [Fact(DisplayName = "[TagBusiness] - Não deve atualizar tag inválida")]
    [Trait("Business", "TagBusiness")]
    public async Task UpdateAsync_DeveNotificarSeTagInvalida()
    {
        var tagExistente = new TagRealtimeDto("TestSid", "OldName");
        ConfigurarMocksTagBusiness(tagExistente: tagExistente);

        var sut = _dependencyInjectorFactory.Mocker.CreateInstance<TagBusiness>();
        var dto = new TagDto { Nome = "" };

        var result = await sut.UpdateAsync("id", dto, CancellationToken.None);

        Assert.Null(result);
        Assert.Contains(_dependencyInjectorFactory.Mocker.Get<NotificationContext>().Notifications,
            n => n.Message.Contains("Nome da tag não informado."));
    }

    [Fact(DisplayName = "[TagBusiness] - Não deve deletar se tag não localizada")]
    [Trait("Business", "TagBusiness")]
    public async Task DeleteAsync_DeveNotificarSeTagNaoEncontrada()
    {
        ConfigurarMocksTagBusiness(tagExistente: null);

        var sut = _dependencyInjectorFactory.Mocker.CreateInstance<TagBusiness>();

        await sut.DeleteAsync("id", CancellationToken.None);

        Assert.Contains(_dependencyInjectorFactory.Mocker.Get<NotificationContext>().Notifications,
            n => n.Message.Contains("Tag não localizada"));
    }

    [Fact(DisplayName = "[TagBusiness] - Deve retornar tag existente")]
    [Trait("Business", "TagBusiness")]
    public async Task GetAsync_DeveRetornarTag()
    {
        var tag = new TagRealtimeDto("TestSid", "MinhaTag");
        ConfigurarMocksTagBusiness(tagExistente: tag);

        var sut = _dependencyInjectorFactory.Mocker.CreateInstance<TagBusiness>();

        var result = await sut.GetAsync("id", CancellationToken.None);

        Assert.NotNull(result);
        Assert.Equal("MinhaTag", result.Nome);
    }

    [Fact(DisplayName = "[TagBusiness] - Deve retornar todas as tags")]
    [Trait("Business", "TagBusiness")]
    public async Task GetAllAsync_DeveRetornarTodasTags()
    {
        var tags = new List<TagRealtimeDto?>
        {
            new TagRealtimeDto("TestSid", "Tag1"),
            new TagRealtimeDto("TestSid", "Tag2")
        };

        ConfigurarMocksTagBusiness(allTags: tags);

        var sut = _dependencyInjectorFactory.Mocker.CreateInstance<TagBusiness>();

        var result = await sut.GetAllAsync(CancellationToken.None);

        Assert.Equal(2, result.Count());
    }
}
