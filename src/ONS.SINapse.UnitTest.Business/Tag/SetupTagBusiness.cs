using ONS.SINapse.Shared.DTO.Firebase;
using ONS.SINapse.Shared.Services.Firebase;
using ONS.SINapse.UnitTest.Business.Fixtures.Collections;
using ONS.SINapse.UnitTest.Shared;

namespace ONS.SINapse.UnitTest.Business.Tag;

[Collection(nameof(TagBusinessCollection))]
public partial class TagBusinessTest
{
    private readonly MockDependencyInjectorFactory _dependencyInjectorFactory;

    public TagBusinessTest()
    {
        _dependencyInjectorFactory = new MockDependencyInjectorFactory();
        _dependencyInjectorFactory.RegisterMocks();
    }

    private void ConfigurarMocksTagBusiness(
        bool tagJaExiste = false,
        TagRealtimeDto? tagExistente = null,
        IEnumerable<TagRealtimeDto?>? allTags = null
    )
    {
        _dependencyInjectorFactory.Mocker.GetMock<ITagFirebaseService>()
            .Setup(t => t.ExistsDocumentWithPropertyValueAsync(
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<CancellationToken>()
            ))
            .ReturnsAsync(tagJaExiste);

        _dependencyInjectorFactory.Mocker.GetMock<ITagFirebaseService>()
            .Setup(t => t.GetAsync(
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<CancellationToken>()
            ))
            .ReturnsAsync(tagExistente);

        _dependencyInjectorFactory.Mocker.GetMock<ITagFirebaseService>()
            .Setup(t => t.GetAllAsync(
                It.IsAny<string>(),
                It.IsAny<CancellationToken>()
            ))
            .ReturnsAsync(allTags ?? []);
    }

}
