using ONS.SINapse.Shared.Identity;
using ONS.SINapse.UnitTest.Shared;

namespace ONS.SINapse.UnitTest.Business.QueryHandler.BuscarDestinatariosDeSolicitacao;

[Collection(nameof(BuscarDestinatariosDeSolicitacaoCollection))]
public partial class BuscarDestinatariosDeSolicitacaoTest
{
    private readonly MockDependencyInjectorFactory _dependencyInjectorFactory;

    public BuscarDestinatariosDeSolicitacaoTest()
    {
        _dependencyInjectorFactory = new MockDependencyInjectorFactory();
        _dependencyInjectorFactory.RegisterMocks();
    }

    private void ConfigurarMockUserContext(string destino)
    {
        var perfil = new Perfil([new Scope("CENTROS", destino, destino)], [], destino, destino);
        _dependencyInjectorFactory.Mocker
            .GetMock<IUserContext>()
            .SetupGet(x => x.Perfil)
            .Returns(perfil);
    }
}
