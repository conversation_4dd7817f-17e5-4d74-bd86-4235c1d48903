using Microsoft.Extensions.DependencyInjection;
using MongoDB.Driver;
using ONS.SINapse.Repository.Imp.Context;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Solicitacao.QueryHandlers.Queries;

namespace ONS.SINapse.UnitTest.Business.QueryHandler.BuscarDestinatariosDeSolicitacao;

public partial class BuscarDestinatariosDeSolicitacaoTest
{
    [Fact(DisplayName = "[Buscar Destinatarios De Solicitacao] - Deve buscar destinatarios de Solicitacao")]
    [Trait("Business", "Buscar Destinatarios De Solicitacao")]
    public async Task DeveBuscarDestinatariosDeSolicitacao()
    {
        // Arrange

        ConfigurarMockUserContext("NE");

        _dependencyInjectorFactory.Mocker
            .GetMock<IMongoReadOnlyConnection>()
            .Setup(x => x.AggregateAsync(
                It.IsAny<PipelineDefinition<DestinatarioDeSolicitacaoDto, DestinatarioDeSolicitacaoDto>>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync([new DestinatarioDeSolicitacaoDto("Test", "Test unitário")]);

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        var query = new BuscarDestinatariosDeSolicitacaoQuery();

        // Act
        var response = await mediator.BuscarDadosAsync(query, CancellationToken.None);

        // Assert
        Assert.NotNull(response);

        response.Count().ShouldBe(1);

        var destinatario = response.First();
        destinatario.Codigo.ShouldBe("Test");
        destinatario.Descricao.ShouldBe("Test unitário");

        _dependencyInjectorFactory.Mocker.GetMock<IMongoReadOnlyConnection>().Verify(
            repository => repository.AggregateAsync(It.IsAny<PipelineDefinition<DestinatarioDeSolicitacaoDto, DestinatarioDeSolicitacaoDto>>(), It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact(DisplayName = "[Buscar Destinatarios De Solicitacao] - Deve retornar lista vazia")]
    [Trait("Business", "Buscar Destinatarios De Solicitacao")]
    public async Task DeveRetornarListaVazia()
    {
        // Arrange

        ConfigurarMockUserContext("NE");

        _dependencyInjectorFactory.Mocker
            .GetMock<IMongoReadOnlyConnection>()
            .Setup(x => x.AggregateAsync(
                It.IsAny<PipelineDefinition<DestinatarioDeSolicitacaoDto, DestinatarioDeSolicitacaoDto>>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        var query = new BuscarDestinatariosDeSolicitacaoQuery();

        // Act
        var response = await mediator.BuscarDadosAsync(query, CancellationToken.None);

        // Assert
        Assert.NotNull(response);

        response.Count().ShouldBe(0);

        var destinatario = response.FirstOrDefault();
        destinatario.ShouldBe(null);

        _dependencyInjectorFactory.Mocker.GetMock<IMongoReadOnlyConnection>().Verify(
            repository => repository.AggregateAsync(It.IsAny<PipelineDefinition<DestinatarioDeSolicitacaoDto, DestinatarioDeSolicitacaoDto>>(), It.IsAny<CancellationToken>()),
            Times.Once);
    }
}
