using Microsoft.Extensions.DependencyInjection;
using Moq;
using ONS.SINapse.Repository.Imp.Context;
using ONS.SINapse.Repository.IRepository.Firebase;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Solicitacao.QueryHandlers.Dtos;
using ONS.SINapse.Solicitacao.QueryHandlers.Queries;

namespace ONS.SINapse.UnitTest.Business.QueryHandler.ObterCadastroSolicitacaoStatus;

public partial class ObterCadastroSolicitacaoStatusTest
{
    [Fact(DisplayName = "[Obter Cadastro Solicitacao Status] - Deve buscar cadastro de solicitacoes")]
    [Trait("Business", "Obter Cadastro Solicitacao Status")]
    public async Task DeveBuscarCadastroDeSolicitacoes()
    {
        // Arrange

        var objetoDeManobra = _objetoManobraFixture.GerarObjetoDeManobraDto();
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoPendente(objetoDeManobra);

        var cadastroSolicitacao = _cadastroSolicitacaoStatusDtoFixture.GerarCadastroSolicitacaoStatusDto();
        var cadastroSolicitacaoQueryable = new List<CadastroSolicitacaoStatusDto> { cadastroSolicitacao }.AsQueryable();

        _dependencyInjectorFactory.Mocker
            .GetMock<IMongoReadOnlyConnection>()
            .Setup(x => x.ObterQueryable<CadastroSolicitacaoStatusDto>())
            .Returns(cadastroSolicitacaoQueryable);

        _dependencyInjectorFactory.Mocker
            .GetMock<IMongoReadOnlyConnection>()
            .Setup(x => x.ExecutarConsultaAsync(It.IsAny<IQueryable<CadastroSolicitacaoStatusDto>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([cadastroSolicitacao]);

        _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoFirebaseRepository>()
            .Setup(x => x.GetByIdAsync(It.IsAny<string[]>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([solicitacao]);

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        var query = new BuscarCadastroSolicitacaoStatusQuery([solicitacao.Id]);

        // Act

        var response = await mediator.BuscarDadosAsync(query, CancellationToken.None);

        // Assert

        Assert.NotNull(response);

        var resultSolicitacao = response.FirstOrDefault();
        resultSolicitacao.ShouldNotBeNull();

        resultSolicitacao.Id.ShouldNotBeNull(solicitacao.Id);
        resultSolicitacao.Historicos.ShouldNotBeEmpty();

        response
            .SelectMany(x => x.Erros)
            .ShouldBeEmpty();

        _dependencyInjectorFactory.Mocker.GetMock<IMongoReadOnlyConnection>().Verify(
            repository => repository.ObterQueryable<CadastroSolicitacaoStatusDto>(),
            Times.Once);

        _dependencyInjectorFactory.Mocker.GetMock<IMongoReadOnlyConnection>().Verify(
            repository => repository.ExecutarConsultaAsync(It.IsAny<IQueryable<CadastroSolicitacaoStatusDto>>(), It.IsAny<CancellationToken>()),
            Times.Once);

        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoFirebaseRepository>().Verify(
            repository => repository.GetByIdAsync(It.IsAny<string[]>(), It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact(DisplayName = "[Obter Cadastro Solicitacao Status] - Deve retornar solicitacao inexistente")]
    [Trait("Business", "Obter Cadastro Solicitacao Status")]
    public async Task DeveRetornarSolicitacaoInexistente()
    {
        // Arrange

        var objetoDeManobra = _objetoManobraFixture.GerarObjetoDeManobraDto();
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoPendente(objetoDeManobra);

        var cadastroSolicitacao = _cadastroSolicitacaoStatusDtoFixture.GerarCadastroSolicitacaoStatusDto();
        var cadastroSolicitacaoQueryable = new List<CadastroSolicitacaoStatusDto> { cadastroSolicitacao }.AsQueryable();

        _dependencyInjectorFactory.Mocker
            .GetMock<IMongoReadOnlyConnection>()
            .Setup(x => x.ObterQueryable<CadastroSolicitacaoStatusDto>())
            .Returns(cadastroSolicitacaoQueryable);

        _dependencyInjectorFactory.Mocker
            .GetMock<IMongoReadOnlyConnection>()
            .Setup(x => x.ExecutarConsultaAsync(It.IsAny<IQueryable<CadastroSolicitacaoStatusDto>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoFirebaseRepository>()
            .Setup(x => x.GetByIdAsync(It.IsAny<string[]>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        var query = new BuscarCadastroSolicitacaoStatusQuery([solicitacao.Id]);

        // Act

        var response = await mediator.BuscarDadosAsync(query, CancellationToken.None);

        // Assert

        Assert.Null(response);

        _dependencyInjectorFactory.Mocker.GetMock<IMongoReadOnlyConnection>().Verify(
            repository => repository.ObterQueryable<CadastroSolicitacaoStatusDto>(),
            Times.Once);

        _dependencyInjectorFactory.Mocker.GetMock<IMongoReadOnlyConnection>().Verify(
            repository => repository.ExecutarConsultaAsync(It.IsAny<IQueryable<CadastroSolicitacaoStatusDto>>(), It.IsAny<CancellationToken>()),
            Times.Once);

        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoFirebaseRepository>().Verify(
            repository => repository.GetByIdAsync(It.IsAny<string[]>(), It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact(DisplayName = "[Obter Cadastro Solicitacao Status] - Deve retornar solicitacoes nao encontradas")]
    [Trait("Business", "Obter Cadastro Solicitacao Status")]
    public async Task DeveRetornarSolicitacoesNaoEncontradas()
    {
        // Arrange

        var objetoDeManobra = _objetoManobraFixture.GerarObjetoDeManobraDto();
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoPendente(objetoDeManobra);
        var solicitacaoNaoEncontrada = _solicitacaoFixture.GerarSolicitacaoPendente(objetoDeManobra);

        var cadastroSolicitacao = _cadastroSolicitacaoStatusDtoFixture.GerarCadastroSolicitacaoStatusDto();
        var cadastroSolicitacaoQueryable = new List<CadastroSolicitacaoStatusDto> { cadastroSolicitacao }.AsQueryable();

        _dependencyInjectorFactory.Mocker
            .GetMock<IMongoReadOnlyConnection>()
            .Setup(x => x.ObterQueryable<CadastroSolicitacaoStatusDto>())
            .Returns(cadastroSolicitacaoQueryable);

        _dependencyInjectorFactory.Mocker
            .GetMock<IMongoReadOnlyConnection>()
            .Setup(x => x.ExecutarConsultaAsync(It.IsAny<IQueryable<CadastroSolicitacaoStatusDto>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([cadastroSolicitacao]);

        _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoFirebaseRepository>()
            .Setup(x => x.GetByIdAsync(It.IsAny<string[]>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([solicitacao]);

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        var query = new BuscarCadastroSolicitacaoStatusQuery([solicitacaoNaoEncontrada.Id]);

        // Act

        var response = await mediator.BuscarDadosAsync(query, CancellationToken.None);

        // Assert

        Assert.NotNull(response);

        response.Count.ShouldBe(3);

        var erros = response
            .SelectMany(x => x.Erros);

        erros.ShouldNotBeEmpty();
        erros.First().ShouldBe($"Solicitação de código {solicitacaoNaoEncontrada.Id} não encontrada.");

        foreach (var item in response)
        {
            if (item.Id == solicitacaoNaoEncontrada.Id)
            {
                item.Historicos.ShouldBeEmpty();
                break;
            }

            item.Historicos.ShouldNotBeEmpty();
            item.Historicos.ShouldNotBeNull();
        }

        _dependencyInjectorFactory.Mocker.GetMock<IMongoReadOnlyConnection>().Verify(
            repository => repository.ObterQueryable<CadastroSolicitacaoStatusDto>(),
            Times.Once);

        _dependencyInjectorFactory.Mocker.GetMock<IMongoReadOnlyConnection>().Verify(
            repository => repository.ExecutarConsultaAsync(It.IsAny<IQueryable<CadastroSolicitacaoStatusDto>>(), It.IsAny<CancellationToken>()),
            Times.Once);

        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoFirebaseRepository>().Verify(
            repository => repository.GetByIdAsync(It.IsAny<string[]>(), It.IsAny<CancellationToken>()),
            Times.Once);
    }
}
