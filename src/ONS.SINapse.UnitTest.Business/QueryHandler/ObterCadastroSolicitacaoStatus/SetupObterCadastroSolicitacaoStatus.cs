using ONS.SINapse.UnitTest.Business.Fixtures.Collections;
using ONS.SINapse.UnitTest.Shared;
using ONS.SINapse.UnitTest.Shared.Fixtures;

namespace ONS.SINapse.UnitTest.Business.QueryHandler.ObterCadastroSolicitacaoStatus;

[Collection(nameof(ObterCadastroSolicitacaoStatusCollection))]
public partial class ObterCadastroSolicitacaoStatusTest
{
    private readonly MockDependencyInjectorFactory _dependencyInjectorFactory;
    private readonly SolicitacaoFixture _solicitacaoFixture;
    private readonly ObjetoManobraFixture _objetoManobraFixture;
    private readonly CadastroSolicitacaoStatusDtoFixture _cadastroSolicitacaoStatusDtoFixture;

    public ObterCadastroSolicitacaoStatusTest(
        SolicitacaoFixture solicitacaoFixture,
        ObjetoManobraFixture objetoManobraFixture,
        CadastroSolicitacaoStatusDtoFixture cadastroSolicitacaoStatusDtoFixture)
    {
        _dependencyInjectorFactory = new MockDependencyInjectorFactory();
        _dependencyInjectorFactory.RegisterMocks();

        _solicitacaoFixture = solicitacaoFixture;
        _objetoManobraFixture = objetoManobraFixture;
        _cadastroSolicitacaoStatusDtoFixture = cadastroSolicitacaoStatusDtoFixture;
    }
}
