using Microsoft.Extensions.DependencyInjection;
using MongoDB.Driver;
using ONS.SINapse.Repository.Imp.Context;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Solicitacao.QueryHandlers.Queries;
using System.Linq.Expressions;

namespace ONS.SINapse.UnitTest.Business.QueryHandler.ObterCadastroSolicitacaoPorId;

public partial class ObterCadastroSolicitacaoPorIdTest
{
    [Fact(DisplayName = "[Obter Cadastro Solicitacao] - Deve buscar cadastro de solicitacoes")]
    [Trait("Business", "Obter Cadastro Solicitacao")]
    public async Task DeveBuscarCadastroDeSolicitacoes()
    {
        // Arrange

        var centro = _objetoManobraFixture.GerarObjetoDeManobra("NE", "CORS-NE");
        ConfigurarMockUserContext(centro.Codigo);


        var solicitacao = _solicitacaoFixture.GerarSolicitacaoPendente(centro);
        var solicitacoes = new List<Entities.Entities.Solicitacao> { solicitacao }.AsQueryable();

        _dependencyInjectorFactory.Mocker
            .GetMock<IMongoReadOnlyConnection>()
            .Setup(x => x.ObterQueryable<Entities.Entities.Solicitacao>())
            .Returns(solicitacoes);

        _dependencyInjectorFactory.Mocker
            .GetMock<IMongoReadOnlyConnection>()
            .Setup(x => x.BuscarFirstOrDefaultAsync(
                It.IsAny<IQueryable<Entities.Entities.Solicitacao>>(),
                It.IsAny<Expression<Func<Entities.Entities.Solicitacao, bool>>>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(solicitacao);

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        var query = new BuscarCadastroSolicitacaoPorIdQuery(solicitacao.Id);

        // Act

        var response = await mediator.BuscarDadosAsync(query, CancellationToken.None);

        // Assert

        Assert.NotNull(response);

        response.Solicitacao.ShouldNotBeNull();
        response.Solicitacao.Id.ShouldNotBeNull(solicitacao.Id);

        response.IsSolicitante.ShouldBeTrue();
        response.IsDestinatario.ShouldBeFalse();

        _dependencyInjectorFactory.Mocker.GetMock<IMongoReadOnlyConnection>().Verify(
            repository => repository.ObterQueryable<Entities.Entities.Solicitacao>(),
            Times.Once);

        _dependencyInjectorFactory.Mocker.GetMock<IMongoReadOnlyConnection>().Verify(
            repository => repository.BuscarFirstOrDefaultAsync(
                It.IsAny<IQueryable<Entities.Entities.Solicitacao>>(),
                It.IsAny<Expression<Func<Entities.Entities.Solicitacao, bool>>>(),
                It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact(DisplayName = "[Obter Cadastro Solicitacao] - Deve retornar solicitacao nao existe")]
    [Trait("Business", "Obter Cadastro Solicitacao")]
    public async Task DeveRetornarSolicitacaoNaoExiste()
    {
        // Arrange

        var centro = _objetoManobraFixture.GerarObjetoDeManobra("NE", "CORS-NE");
        ConfigurarMockUserContext(centro.Codigo);


        var solicitacao = _solicitacaoFixture.GerarSolicitacaoPendente(centro);
        var solicitacoes = new List<Entities.Entities.Solicitacao> { }.AsQueryable();

        _dependencyInjectorFactory.Mocker
            .GetMock<IMongoReadOnlyConnection>()
            .Setup(x => x.ObterQueryable<Entities.Entities.Solicitacao>())
            .Returns(solicitacoes);

        _dependencyInjectorFactory.Mocker
            .GetMock<IMongoReadOnlyConnection>()
            .Setup(x => x.BuscarFirstOrDefaultAsync(
                It.IsAny<IQueryable<Entities.Entities.Solicitacao>>(),
                It.IsAny<Expression<Func<Entities.Entities.Solicitacao?, bool>>>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync((Entities.Entities.Solicitacao?)null);

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        var query = new BuscarCadastroSolicitacaoPorIdQuery(solicitacao.Id);

        // Act

        var response = await mediator.BuscarDadosAsync(query, CancellationToken.None);

        // Assert

        Assert.Null(response);

        _dependencyInjectorFactory.Mocker.GetMock<IMongoReadOnlyConnection>().Verify(
            repository => repository.ObterQueryable<Entities.Entities.Solicitacao>(),
            Times.Once);

        _dependencyInjectorFactory.Mocker.GetMock<IMongoReadOnlyConnection>().Verify(
            repository => repository.BuscarFirstOrDefaultAsync(
                It.IsAny<IQueryable<Entities.Entities.Solicitacao>>(),
                It.IsAny<Expression<Func<Entities.Entities.Solicitacao, bool>>>(),
                It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact(DisplayName = "[Obter Cadastro Solicitacao] - Deve retornar que usuario nao tem permissao")]
    [Trait("Business", "Obter Cadastro Solicitacao")]
    public async Task DeveRetornarQueUsuarioNaoTemPermissao()
    {
        // Arrange

        var centro = _objetoManobraFixture.GerarObjetoDeManobra("NE", "CORS-NE");
        var centroDestino = _objetoManobraFixture.GerarObjetoDeManobra("NE", "CORS-NE");
        ConfigurarMockUserContext("N");

        var solicitacao = _solicitacaoFixture.GerarSolicitacaoPendente(centro, centroDestino);
        var solicitacoes = new List<Entities.Entities.Solicitacao> { }.AsQueryable();

        _dependencyInjectorFactory.Mocker
            .GetMock<IMongoReadOnlyConnection>()
            .Setup(x => x.ObterQueryable<Entities.Entities.Solicitacao>())
            .Returns(solicitacoes);

        _dependencyInjectorFactory.Mocker
            .GetMock<IMongoReadOnlyConnection>()
            .Setup(x => x.BuscarFirstOrDefaultAsync(
                It.IsAny<IQueryable<Entities.Entities.Solicitacao>>(),
                It.IsAny<Expression<Func<Entities.Entities.Solicitacao?, bool>>>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(solicitacao);

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        var query = new BuscarCadastroSolicitacaoPorIdQuery(solicitacao.Id);

        // Act

        var response = await mediator.BuscarDadosAsync(query, CancellationToken.None);

        // Assert

        Assert.Null(response);

        _dependencyInjectorFactory.Mocker.GetMock<IMongoReadOnlyConnection>().Verify(
            repository => repository.ObterQueryable<Entities.Entities.Solicitacao>(),
            Times.Once);

        _dependencyInjectorFactory.Mocker.GetMock<IMongoReadOnlyConnection>().Verify(
            repository => repository.BuscarFirstOrDefaultAsync(
                It.IsAny<IQueryable<Entities.Entities.Solicitacao>>(),
                It.IsAny<Expression<Func<Entities.Entities.Solicitacao, bool>>>(),
                It.IsAny<CancellationToken>()),
            Times.Once);
    }
}
