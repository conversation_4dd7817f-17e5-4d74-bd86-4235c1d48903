using Microsoft.Extensions.DependencyInjection;
using ONS.SINapse.Repository.Imp.Context;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Solicitacao.QueryHandlers.Dtos;

namespace ONS.SINapse.UnitTest.Business.QueryHandler.ConsultaHistorico;

public partial class ConsultaHistoricoTest
{
    [Fact(DisplayName = "[Consulta Historico] - Deve buscar historico de solicitacoes")]
    [Trait("Business", "Consulta Historico")]
    public async Task DeveBuscarHistoricoDeSolicitacoes()
    {
        // Arrange

        ConfigurarMockUserContext("NE");
        MockAppSettings(30);

        var consultaHistorico = new List<ConsultaHistoricoSolicitacaoDto> { _consultaHistoricoSolicitacaoDtoFixture.GerarConsultaHistoricoSolicitacaoDto() };

        _dependencyInjectorFactory.Mocker
            .GetMock<IMongoReadOnlyConnection>()
            .Setup(x => x.ObterQueryable<ConsultaHistoricoSolicitacaoDto>())
            .Returns(consultaHistorico.AsQueryable());

        _dependencyInjectorFactory.Mocker
            .GetMock<IMongoReadOnlyConnection>()
            .Setup(x => x.ExecutarConsultaAsync(It.IsAny<IQueryable<ConsultaHistoricoSolicitacaoDto>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(consultaHistorico);

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        var query = _consultaHistoricoSolicitacaoQueryFixture.GerarConsultaHistoricoSolicitacaoQuery();

        // Act

        var response = await mediator.BuscarDadosAsync(query, CancellationToken.None);

        // Assert

        Assert.NotNull(response);

        response.Items.Count.ShouldBe(1);
        response.Items.First().ShouldNotBeNull();
        response.Page.ShouldBe(1);
        response.Size.ShouldBe(1);

        _dependencyInjectorFactory.Mocker.GetMock<IMongoReadOnlyConnection>().Verify(
            repository => repository.ObterQueryable<ConsultaHistoricoSolicitacaoDto>(),
            Times.Once);

        _dependencyInjectorFactory.Mocker.GetMock<IMongoReadOnlyConnection>().Verify(
            repository => repository.ExecutarConsultaAsync(It.IsAny<IQueryable<ConsultaHistoricoSolicitacaoDto>>(), It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact(DisplayName = "[Consulta Historico] - Deve retornar erro data invalida")]
    [Trait("Business", "Consulta Historico")]
    public async Task DeveRetornarErroDataInvalida()
    {
        // Arrange

        ConfigurarMockUserContext("NE");
        MockAppSettings(30);

        var consultaHistorico = new List<ConsultaHistoricoSolicitacaoDto> { _consultaHistoricoSolicitacaoDtoFixture.GerarConsultaHistoricoSolicitacaoDto() };

        _dependencyInjectorFactory.Mocker
            .GetMock<IMongoReadOnlyConnection>()
            .Setup(x => x.ObterQueryable<ConsultaHistoricoSolicitacaoDto>())
            .Returns(consultaHistorico.AsQueryable());

        _dependencyInjectorFactory.Mocker
            .GetMock<IMongoReadOnlyConnection>()
            .Setup(x => x.ExecutarConsultaAsync(It.IsAny<IQueryable<ConsultaHistoricoSolicitacaoDto>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(consultaHistorico);

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        var query = _consultaHistoricoSolicitacaoQueryFixture.GerarConsultaHistoricoSolicitacaoQuery(periodoInicial: DateTime.MinValue);

        // Act

        var response = await mediator.BuscarDadosAsync(query, CancellationToken.None);

        // Assert

        Assert.NotNull(response);

        response.Items.Count.ShouldBe(0);
        response.Page.ShouldBe(query.Pagina);
        response.Size.ShouldBe(0);

        _dependencyInjectorFactory.Mocker.GetMock<IMongoReadOnlyConnection>().Verify(
            repository => repository.ObterQueryable<ConsultaHistoricoSolicitacaoDto>(),
            Times.Never);

        _dependencyInjectorFactory.Mocker.GetMock<IMongoReadOnlyConnection>().Verify(
            repository => repository.ExecutarConsultaAsync(It.IsAny<IQueryable<ConsultaHistoricoSolicitacaoDto>>(), It.IsAny<CancellationToken>()),
            Times.Never);
    }

    [Fact(DisplayName = "[Consulta Historico] - Deve retornar erro intervalo maior que o limite de dias")]
    [Trait("Business", "Consulta Historico")]
    public async Task DeveRetornarErrointervaloMaiorQueLimiteDeDias()
    {
        // Arrange

        ConfigurarMockUserContext("NE");
        MockAppSettings(1);

        var consultaHistorico = new List<ConsultaHistoricoSolicitacaoDto> { _consultaHistoricoSolicitacaoDtoFixture.GerarConsultaHistoricoSolicitacaoDto() };

        _dependencyInjectorFactory.Mocker
            .GetMock<IMongoReadOnlyConnection>()
            .Setup(x => x.ObterQueryable<ConsultaHistoricoSolicitacaoDto>())
            .Returns(consultaHistorico.AsQueryable());

        _dependencyInjectorFactory.Mocker
            .GetMock<IMongoReadOnlyConnection>()
            .Setup(x => x.ExecutarConsultaAsync(It.IsAny<IQueryable<ConsultaHistoricoSolicitacaoDto>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(consultaHistorico);

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        var query = _consultaHistoricoSolicitacaoQueryFixture.GerarConsultaHistoricoSolicitacaoQuery();

        // Act

        var response = await mediator.BuscarDadosAsync(query, CancellationToken.None);

        // Assert

        Assert.NotNull(response);

        response.Items.Count.ShouldBe(0);
        response.Page.ShouldBe(query.Pagina);
        response.Size.ShouldBe(0);

        _dependencyInjectorFactory.Mocker.GetMock<IMongoReadOnlyConnection>().Verify(
            repository => repository.ObterQueryable<ConsultaHistoricoSolicitacaoDto>(),
            Times.Never);

        _dependencyInjectorFactory.Mocker.GetMock<IMongoReadOnlyConnection>().Verify(
            repository => repository.ExecutarConsultaAsync(It.IsAny<IQueryable<ConsultaHistoricoSolicitacaoDto>>(), It.IsAny<CancellationToken>()),
            Times.Never);
    }
}
