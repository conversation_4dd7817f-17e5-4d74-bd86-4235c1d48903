using Microsoft.Extensions.Options;
using ONS.SINapse.Shared.Identity;
using ONS.SINapse.Shared.Settings;
using ONS.SINapse.UnitTest.Business.Fixtures.Collections;
using ONS.SINapse.UnitTest.Shared;
using ONS.SINapse.UnitTest.Shared.Fixtures;

namespace ONS.SINapse.UnitTest.Business.QueryHandler.ConsultaHistorico;

[Collection(nameof(ConsultaHistoricoCollection))]
public partial class ConsultaHistoricoTest
{
    private readonly MockDependencyInjectorFactory _dependencyInjectorFactory;
    private readonly ConsultaHistoricoSolicitacaoQueryFixture _consultaHistoricoSolicitacaoQueryFixture;
    private readonly ConsultaHistoricoSolicitacaoDtoFixture _consultaHistoricoSolicitacaoDtoFixture;

    public ConsultaHistoricoTest(
        ConsultaHistoricoSolicitacaoQueryFixture consultaHistoricoSolicitacaoQueryFixture, 
        ConsultaHistoricoSolicitacaoDtoFixture consultaHistoricoSolicitacaoDtoFixture)
    {
        _dependencyInjectorFactory = new MockDependencyInjectorFactory();
        _dependencyInjectorFactory.RegisterMocks();

        _consultaHistoricoSolicitacaoQueryFixture = consultaHistoricoSolicitacaoQueryFixture;
        _consultaHistoricoSolicitacaoDtoFixture = consultaHistoricoSolicitacaoDtoFixture;
    }

    private void ConfigurarMockUserContext(string destino)
    {
        var perfil = new Perfil([new Scope("CENTROS", destino, destino)], [], destino, destino);
        _dependencyInjectorFactory.Mocker
            .GetMock<IUserContext>()
            .SetupGet(x => x.Perfil)
            .Returns(perfil);
    }

    private void MockAppSettings(int days)
    {
        var configuracaoDeSistemaSettings = new ConfiguracaoDeSistemaSettings
        {
            SegundosEntreNotificacoesPendentes = days,
            QuantidadeDeDiasLimiteParaConsultaDoHistoricoDeSolicitacoes = days,
            Sons = new SonsDoSistemaSettings()
        };

        var optionsMock = Options.Create(configuracaoDeSistemaSettings);
        _dependencyInjectorFactory.Mocker.Use<IOptions<ConfiguracaoDeSistemaSettings>>(optionsMock);
    }
}
