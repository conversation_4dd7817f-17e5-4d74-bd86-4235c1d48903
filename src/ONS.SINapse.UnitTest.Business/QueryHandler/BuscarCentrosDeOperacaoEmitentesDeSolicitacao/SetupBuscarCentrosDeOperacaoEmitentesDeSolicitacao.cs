using ONS.SINapse.Shared.Identity;
using ONS.SINapse.UnitTest.Business.Fixtures.Collections;
using ONS.SINapse.UnitTest.Shared;
using ONS.SINapse.UnitTest.Shared.Fixtures;

namespace ONS.SINapse.UnitTest.Business.QueryHandler.BuscarCentrosDeOperacaoEmitentesDeSolicitacao;

[Collection(nameof(BuscarCentrosDeOperacaoEmitentesDeSolicitacaoCollection))]
public partial class BuscarCentrosDeOperacaoEmitentesDeSolicitacaoTest
{
    private readonly MockDependencyInjectorFactory _dependencyInjectorFactory;
    private readonly SolicitacaoFixture _solicitacaoFixture;
    private readonly ObjetoManobraFixture _objetoManobraFixture;

    public BuscarCentrosDeOperacaoEmitentesDeSolicitacaoTest(SolicitacaoFixture solicitacaoFixture, ObjetoManobraFixture objetoManobraFixture)
    {
        _dependencyInjectorFactory = new MockDependencyInjectorFactory();
        _dependencyInjectorFactory.RegisterMocks();

        _solicitacaoFixture = solicitacaoFixture;
        _objetoManobraFixture = objetoManobraFixture;
    }

    private void ConfigurarMockUserContext(string destino)
    {
        var perfil = new Perfil([new Scope("CENTROS", destino, destino)], [], destino, destino);
        _dependencyInjectorFactory.Mocker
            .GetMock<IUserContext>()
            .SetupGet(x => x.Perfil)
            .Returns(perfil);
    }
}
