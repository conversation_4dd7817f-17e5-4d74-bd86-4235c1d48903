using Microsoft.Extensions.DependencyInjection;
using MongoDB.Driver;
using MongoDB.Driver.Linq;
using ONS.SINapse.Repository.Imp.Context;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Solicitacao.QueryHandlers.Queries;

namespace ONS.SINapse.UnitTest.Business.QueryHandler.BuscarCentrosDeOperacaoEmitentesDeSolicitacao;

public partial class BuscarCentrosDeOperacaoEmitentesDeSolicitacaoTest
{
    [Fact(DisplayName = "[Buscar Centros De Operacao Emitentes De Solicitacao] - Deve buscar centros de operacao emitentes")]
    [Trait("Business", "Buscar Centros De Operacao Emitentes De Solicitacao")]
    public async Task DeveBuscarCentrosDeOperacaoEmitentes()
    {
        // Arrange

        ConfigurarMockUserContext("NE");

        var solicitacoes = new List<Entities.Entities.Solicitacao> { _solicitacaoFixture.GerarSolicitacaoPendente() }.AsQueryable();
        var objetoDeManobra = _objetoManobraFixture.GerarObjetoDeManobraDto();

        _dependencyInjectorFactory.Mocker
            .GetMock<IMongoReadOnlyConnection>()
            .Setup(x => x.ObterQueryable<Entities.Entities.Solicitacao>())
            .Returns(solicitacoes);

        _dependencyInjectorFactory.Mocker
            .GetMock<IMongoReadOnlyConnection>()
            .Setup(x => x.ExecutarConsultaAsync(It.IsAny<IQueryable<CentroSolicitacaoDto>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([new CentroSolicitacaoDto { Centro = objetoDeManobra, Count = 2 }]);

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        var query = new BuscarCentrosDeOperacaoEmitentesDeSolicitacaoQuery();

        // Act

        var response = await mediator.BuscarDadosAsync(query, CancellationToken.None);

        // Assert
        
        Assert.NotNull(response);

        response.Count.ShouldBe(1);

        var destinatario = response.First();
        destinatario.Codigo.ShouldBe(objetoDeManobra.Codigo);
        destinatario.Nome.ShouldBe(objetoDeManobra.Nome);
        destinatario.IsCnos.ShouldBeFalse();

        _dependencyInjectorFactory.Mocker.GetMock<IMongoReadOnlyConnection>().Verify(
            repository => repository.ObterQueryable<Entities.Entities.Solicitacao>(),
            Times.Once);

        _dependencyInjectorFactory.Mocker.GetMock<IMongoReadOnlyConnection>().Verify(
            repository => repository.ExecutarConsultaAsync(It.IsAny<IQueryable<CentroSolicitacaoDto>>(), It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact(DisplayName = "[Buscar Centros De Operacao Emitentes De Solicitacao] - Deve buscar centros de operacao emitentes CNOS")]
    [Trait("Business", "Buscar Centros De Operacao Emitentes De Solicitacao")]
    public async Task DeveBuscarCentrosDeOperacaoEmitentesCnos()
    {
        // Arrange

        ConfigurarMockUserContext("NE");

        var solicitacoes = new List<Entities.Entities.Solicitacao> { _solicitacaoFixture.GerarSolicitacaoPendente() }.AsQueryable();
        var objetoDeManobra = _objetoManobraFixture.GerarObjetoDeManobraDto("CN", "CNOS");

        _dependencyInjectorFactory.Mocker
            .GetMock<IMongoReadOnlyConnection>()
            .Setup(x => x.ObterQueryable<Entities.Entities.Solicitacao>())
            .Returns(solicitacoes);

        _dependencyInjectorFactory.Mocker
            .GetMock<IMongoReadOnlyConnection>()
            .Setup(x => x.ExecutarConsultaAsync(It.IsAny<IQueryable<CentroSolicitacaoDto>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([new CentroSolicitacaoDto { Centro = objetoDeManobra, Count = 2 }]);

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        var query = new BuscarCentrosDeOperacaoEmitentesDeSolicitacaoQuery();

        // Act

        var response = await mediator.BuscarDadosAsync(query, CancellationToken.None);

        // Assert

        Assert.NotNull(response);

        response.Count.ShouldBe(1);

        var destinatario = response.First();
        destinatario.Codigo.ShouldBe(objetoDeManobra.Codigo);
        destinatario.Nome.ShouldBe(objetoDeManobra.Nome);
        destinatario.IsCnos.ShouldBeTrue();

        _dependencyInjectorFactory.Mocker.GetMock<IMongoReadOnlyConnection>().Verify(
            repository => repository.ObterQueryable<Entities.Entities.Solicitacao>(),
            Times.Once);

        _dependencyInjectorFactory.Mocker.GetMock<IMongoReadOnlyConnection>().Verify(
            repository => repository.ExecutarConsultaAsync(It.IsAny<IQueryable<CentroSolicitacaoDto>>(), It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact(DisplayName = "[Buscar Centros De Operacao Emitentes De Solicitacao] - Deve retornar lista vazia")]
    [Trait("Business", "Buscar Centros De Operacao Emitentes De Solicitacao")]
    public async Task DeveRetornarListaVazia()
    {
        // Arrange

        ConfigurarMockUserContext("NE");

        var solicitacoes = new List<Entities.Entities.Solicitacao> { }.AsQueryable();
        var objetoDeManobra = _objetoManobraFixture.GerarObjetoDeManobraDto();

        _dependencyInjectorFactory.Mocker
            .GetMock<IMongoReadOnlyConnection>()
            .Setup(x => x.ObterQueryable<Entities.Entities.Solicitacao>())
            .Returns(solicitacoes);

        _dependencyInjectorFactory.Mocker
            .GetMock<IMongoReadOnlyConnection>()
            .Setup(x => x.ExecutarConsultaAsync(It.IsAny<IQueryable<CentroSolicitacaoDto>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        var query = new BuscarCentrosDeOperacaoEmitentesDeSolicitacaoQuery();

        // Act
        var response = await mediator.BuscarDadosAsync(query, CancellationToken.None);

        // Assert
        Assert.NotNull(response);

        response.Count.ShouldBe(0);

        var destinatario = response.FirstOrDefault();
        destinatario.ShouldBe(null);

        _dependencyInjectorFactory.Mocker.GetMock<IMongoReadOnlyConnection>().Verify(
           repository => repository.ObterQueryable<Entities.Entities.Solicitacao>(),
           Times.Once);

        _dependencyInjectorFactory.Mocker.GetMock<IMongoReadOnlyConnection>().Verify(
            repository => repository.ExecutarConsultaAsync(It.IsAny<IQueryable<CentroSolicitacaoDto>>(), It.IsAny<CancellationToken>()),
            Times.Once);
    }
}
