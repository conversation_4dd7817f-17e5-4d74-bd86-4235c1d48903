using ONS.SINapse.Shared.Identity;
using ONS.SINapse.UnitTest.Business.Fixtures.Collections;
using ONS.SINapse.UnitTest.Shared;
using ONS.SINapse.UnitTest.Shared.Fixtures;

namespace ONS.SINapse.UnitTest.Business.QueryHandler.ConsultaHistoricoSolicitacaoAnalitica;

[Collection(nameof(ConsultaHistoricoSolicitacaoAnaliticaCollection))]
public partial class ConsultaHistoricoSolicitacaoAnaliticaTest
{
    private readonly MockDependencyInjectorFactory _dependencyInjectorFactory;
    private readonly ConsultaHistoricoSolicitacaoAnaliticaQueryFixture _consultaHistoricoSolicitacaoAnaliticaQueryFixture;
    private readonly HistoricoConsultaAnaliticaDtoFixture _historicoConsultaAnaliticaDtoFixture;

    public ConsultaHistoricoSolicitacaoAnaliticaTest(
        ConsultaHistoricoSolicitacaoAnaliticaQueryFixture consultaHistoricoSolicitacaoAnaliticaQueryFixture,
        HistoricoConsultaAnaliticaDtoFixture historicoConsultaAnaliticaDtoFixture)
    {
        _dependencyInjectorFactory = new MockDependencyInjectorFactory();
        _dependencyInjectorFactory.RegisterMocks();

        _consultaHistoricoSolicitacaoAnaliticaQueryFixture = consultaHistoricoSolicitacaoAnaliticaQueryFixture;
        _historicoConsultaAnaliticaDtoFixture = historicoConsultaAnaliticaDtoFixture;
    }

    private void ConfigurarMockUserContext(string destino)
    {
        var perfil = new Perfil([new Scope("CENTROS", destino, destino)], [], destino, destino);
        _dependencyInjectorFactory.Mocker
            .GetMock<IUserContext>()
            .SetupGet(x => x.Perfil)
            .Returns(perfil);
    }
}
