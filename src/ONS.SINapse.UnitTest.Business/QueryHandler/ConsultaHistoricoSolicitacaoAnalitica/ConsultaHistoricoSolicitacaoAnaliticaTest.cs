using Microsoft.Extensions.DependencyInjection;
using MongoDB.Driver;
using MongoDB.Driver.Linq;
using ONS.SINapse.Repository.Imp.Context;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Solicitacao.QueryHandlers.Queries;

namespace ONS.SINapse.UnitTest.Business.QueryHandler.ConsultaHistoricoSolicitacaoAnalitica;

public partial class ConsultaHistoricoSolicitacaoAnaliticaTest
{
    [Fact(DisplayName = "[Consulta Historico Analitica] - Deve buscar historico analitico de solicitacoes")]
    [Trait("Business", "Consulta Historico Analitica")]
    public async Task DeveBuscarHistoricoAnaliticoDeSolicitacoes()
    {
        // Arrange

        ConfigurarMockUserContext("NE");

        var consultaHistoricoAnalitico = _historicoConsultaAnaliticaDtoFixture.GerarHistoricoConsultaAnaliticaDto();

        _dependencyInjectorFactory.Mocker
            .GetMock<IMongoReadOnlyConnection>()
            .Setup(x => x.AggregateAsync(It.IsAny<PipelineDefinition<HistoricoConsultaAnaliticaDto, HistoricoConsultaAnaliticaDto>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([consultaHistoricoAnalitico]);

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        var query = _consultaHistoricoSolicitacaoAnaliticaQueryFixture.GerarConsultaHistoricoSolicitacaoAnaliticaQuery();

        // Act

        var response = await mediator.BuscarDadosAsync(query, CancellationToken.None);

        // Assert

        Assert.NotNull(response);

        response.Filtro.ShouldNotBeNull();
        response.Resultado.Count.ShouldBe(1);
        response.Resultado.First().ShouldNotBeNull();
        response.TotalDeSolicitacoes.ShouldBe(1);
        response.TotalDeItens.ShouldBe(1);

        _dependencyInjectorFactory.Mocker.GetMock<IMongoReadOnlyConnection>().Verify(
            repository => repository.AggregateAsync(It.IsAny<PipelineDefinition<HistoricoConsultaAnaliticaDto, HistoricoConsultaAnaliticaDto>>(), It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact(DisplayName = "[Consulta Historico Analitica] - Deve buscar historico analitico de solicitacoes vazio")]
    [Trait("Business", "Consulta Historico Analitica")]
    public async Task DeveBuscarHistoricoDeSolicitacoesVazio()
    {
        // Arrange

        ConfigurarMockUserContext("NE");

        _dependencyInjectorFactory.Mocker
            .GetMock<IMongoReadOnlyConnection>()
            .Setup(x => x.AggregateAsync(It.IsAny<PipelineDefinition<HistoricoConsultaAnaliticaDto, HistoricoConsultaAnaliticaDto>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        var query = _consultaHistoricoSolicitacaoAnaliticaQueryFixture.GerarConsultaHistoricoSolicitacaoAnaliticaQuery(status: null, possuiImpedimento: true);

        // Act

        var response = await mediator.BuscarDadosAsync(query, CancellationToken.None);

        // Assert

        Assert.NotNull(response);

        response.Filtro.ShouldNotBeNull();
        response.Resultado.Count.ShouldBe(0);
        response.Resultado.FirstOrDefault().ShouldBeNull();
        response.TotalDeSolicitacoes.ShouldBe(0);
        response.TotalDeItens.ShouldBe(0);

        _dependencyInjectorFactory.Mocker.GetMock<IMongoReadOnlyConnection>().Verify(
            repository => repository.AggregateAsync(It.IsAny<PipelineDefinition<HistoricoConsultaAnaliticaDto, HistoricoConsultaAnaliticaDto>>(), It.IsAny<CancellationToken>()),
            Times.Once);
    }
}
