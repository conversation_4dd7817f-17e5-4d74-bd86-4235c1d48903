using FluentValidation.Results;
using Moq;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Repository.IRepository.Firebase;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Shared.Messages;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands.Firebase;
using ONS.SINapse.Solicitacao.Dtos.Integracao;
using ONS.SINapse.Solicitacao.EventHandlers.Events;
using ONS.SINapse.UnitTest.Shared.Fixtures;

namespace ONS.SINapse.UnitTest.Business.TrocaDeStatusSolicitacao.CancelarEnvio;

public partial class CancelarEnvioSolicitacaoTest
{
    [Fact(DisplayName = "[CancelarEnvio] - Deve retornar um erro em Cancelar Envio de solicitação no firebase")]
    [Trait("Business", "Cancelar Envio")]
    public async Task CancelarEnvio_DeveRetornarUmErro_FirebaseCommand()
    {
        // Arrange
        var objetoDeManobraOrigem = _objetoManobraFixture.GerarObjetoDeManobra("CN", "CNOS");
        var objetoDeManobraDestino = _objetoManobraFixture.GerarObjetoDeManobra("NE", "COSR-NE");
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoAguardandoEnvio(objetoDeManobraOrigem, objetoDeManobraDestino);

        var usuario = UsuarioFixture.GerarUsuarioDtoValido();

        ConfigurarMockUserContext(usuario, objetoDeManobraDestino.Codigo);

        _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoFirebaseRepository>()
            .Setup(repository => repository.GetByIdAsync(It.IsAny<string[]>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([solicitacao]);

        var result = new ValidationResult();
        result.AdicionarErro("Erro ao enviar command para o firebase");

        var mediatorMock = _dependencyInjectorFactory.Mocker.GetMock<IMediatorHandler>();

        mediatorMock
            .Setup(x => x.EnviarComandoAsync(
                It.Is<Command>(cmd => cmd is TrocarDeStatusNoFirebaseCommand),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(result);

        var command = new CancelarEnvioEmLoteCommand([
          new StatusDeSolicitacaoIntegracaoRecebimentoDto(
                solicitacao.Id,
                StatusDeSolicitacao.Enviada,
                null,
                new ObjetoDeManobraDto(objetoDeManobraOrigem.Codigo, objetoDeManobraOrigem.Nome),
                usuario,
                "Teste"
           )
        ]);

        // Act
        var response =
            await mediatorMock.Object
                .EnviarComandoAsync<CancelarEnvioEmLoteCommand, StatusDeSolicitacaoIntegracaoEnvioEmLoteDto>(
                    command, CancellationToken.None);

        // Assert
        Assert.NotNull(response);

        response.Solicitacoes
            .All(x => x.Erros.Length > 0)
            .ShouldBeTrue();

        response.Solicitacoes
            .All(x => x.Status == StatusDeSolicitacao.Erro)
            .ShouldBeTrue();

        var statusDeSolicitacaoIntegracaoRecebidaEvent =
            await _dependencyInjectorFactory.Harness.Published.Any<StatusDeSolicitacaoIntegracaoRecebidaEvent>();

        var solicitacaoConcluidaEvent =
            await _dependencyInjectorFactory.Harness.Published.Any<SolicitacaoConcluidaEvent>();

        statusDeSolicitacaoIntegracaoRecebidaEvent.ShouldBeTrue();
        solicitacaoConcluidaEvent.ShouldBeFalse();

        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoFirebaseRepository>().Verify(
            repository => repository.GetByIdAsync(It.IsAny<string[]>(), It.IsAny<CancellationToken>()),
            Times.AtLeastOnce());
    }
}
