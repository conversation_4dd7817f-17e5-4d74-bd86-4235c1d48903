using Microsoft.Extensions.DependencyInjection;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Repository.IRepository.Firebase;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands.Firebase;
using ONS.SINapse.Solicitacao.Dtos.Integracao;
using ONS.SINapse.Solicitacao.EventHandlers.Events;
using ONS.SINapse.UnitTest.Shared.Fixtures;

namespace ONS.SINapse.UnitTest.Business.TrocaDeStatusSolicitacao.CancelarEnvio;

public partial class CancelarEnvioSolicitacaoTest
{
    [Fact(DisplayName = "[CancelarEnvio] - Deve retornar erro quando solicitação não existir")]
    [Trait("Business", "CancelarEnvio")]
    public async Task CancelarEnvio_DeveRetornarErro_SolicitacaoNaoExiste()
    {
        // Arrange
        var objetoDeManobra = _objetoManobraFixture.GerarObjetoDeManobra("CN", "CNOS");
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoAguardandoEnvio(objetoDeManobra);
        var origem = solicitacao.Origem.Codigo;
        var usuario = UsuarioFixture.GerarUsuarioDtoValido();

        ConfigurarMockUserContext(usuario, origem);

        _dependencyInjectorFactory.Mocker
                 .GetMock<ISolicitacaoFirebaseRepository>()
                 .Setup(repository => repository.GetByIdAsync(It.IsAny<string[]>(), It.IsAny<CancellationToken>()))
                 .ReturnsAsync([]);

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        var command = new CancelarEnvioEmLoteCommand([
           new StatusDeSolicitacaoIntegracaoRecebimentoDto(
                solicitacao.Id,
                StatusDeSolicitacao.Enviada,
                null,
                new ObjetoDeManobraDto(objetoDeManobra.Codigo, objetoDeManobra.Nome),
                usuario,
                "Teste"
           )
        ]);

        // Act
        var response = await mediator.EnviarComandoAsync<CancelarEnvioEmLoteCommand, StatusDeSolicitacaoIntegracaoEnvioEmLoteDto>(command, CancellationToken.None);

        // Assert
        await ValidarDadosCancelarEnvio(response, 
            $"Não foi possível alterar o status da solicitação {solicitacao.Id} para Envio Cancelado. Solicitação não existe.");
    }

    [Fact(DisplayName = "[CancelarEnvio] - Deve retornar erro quando solicitante for inválido")]
    [Trait("Business", "CancelarEnvio")]
    public async Task CancelarEnvio_DeveRetornarErro_SolicitanteInvaido()
    {
        // Arrange
        var objetoDeManobra = _objetoManobraFixture.GerarObjetoDeManobra("CN", "CNOS");
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoAguardandoEnvio(objetoDeManobra);
        var origem = solicitacao.Origem.Codigo;
        var usuario = UsuarioFixture.GerarUsuarioDtoValido();

        ConfigurarMockUserContext(usuario, origem);

        _dependencyInjectorFactory.Mocker
                 .GetMock<ISolicitacaoFirebaseRepository>()
                 .Setup(repository => repository.GetByIdAsync(It.IsAny<string[]>(), It.IsAny<CancellationToken>()))
                 .ReturnsAsync([solicitacao]);

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        var command = new CancelarEnvioEmLoteCommand([
           new StatusDeSolicitacaoIntegracaoRecebimentoDto(
                solicitacao.Id,
                StatusDeSolicitacao.Enviada,
                null,
                new ObjetoDeManobraDto("NE", "CORS-NE"),
                usuario,
                "Teste"
           )
        ]);

        // Act
        var response = await mediator.EnviarComandoAsync<CancelarEnvioEmLoteCommand, StatusDeSolicitacaoIntegracaoEnvioEmLoteDto>(command, CancellationToken.None);

        // Assert
        await ValidarDadosCancelarEnvio(response,
            $"Não foi possível alterar o status da solicitação {solicitacao.Id} para Envio Cancelado. Operação não permitida, usuário não é o solicitante.");
    }

    [Fact(DisplayName = "[CancelarEnvio] - Deve retornar erro quando status for inválido")]
    [Trait("Business", "CancelarEnvio")]
    public async Task CancelarEnvio_DeveRetornarErro_StatusInvalido()
    {
        // Arrange
        var objetoDeManobra = _objetoManobraFixture.GerarObjetoDeManobra("CN", "CNOS");
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoPendente(objetoDeManobra);
        var origem = solicitacao.Origem.Codigo;
        var usuario = UsuarioFixture.GerarUsuarioDtoValido();

        ConfigurarMockUserContext(usuario, origem);

        _dependencyInjectorFactory.Mocker
                 .GetMock<ISolicitacaoFirebaseRepository>()
                 .Setup(repository => repository.GetByIdAsync(It.IsAny<string[]>(), It.IsAny<CancellationToken>()))
                 .ReturnsAsync([solicitacao]);

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        var command = new CancelarEnvioEmLoteCommand([
           new StatusDeSolicitacaoIntegracaoRecebimentoDto(
                solicitacao.Id,
                StatusDeSolicitacao.EnvioCancelado,
                null,
                new ObjetoDeManobraDto("NE", "CORS-NE"),
                usuario,
                "Teste"
           )
        ]);

        // Act
        var response = await mediator.EnviarComandoAsync<CancelarEnvioEmLoteCommand, StatusDeSolicitacaoIntegracaoEnvioEmLoteDto>(command, CancellationToken.None);

        // Assert
        await ValidarDadosCancelarEnvio(response,
            $"Não foi possível alterar o status da solicitação {solicitacao.Id} para {StatusDeSolicitacao.EnvioCancelado.GetDescription()}. Solicitação se encontra com status {solicitacao.Status.GetDescription()}.");
    }

    private async Task ValidarDadosCancelarEnvio(StatusDeSolicitacaoIntegracaoEnvioEmLoteDto response, string message)
    {
        response.ShouldNotBeNull();

        var erros = response.Solicitacoes
            .SelectMany(x => x.Erros)
            .ToList();

        erros.Any(x => x.Contains(message)).ShouldBeTrue();

        erros.ShouldNotBeEmpty();

        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoFirebaseRepository>().Verify(
            repository => repository.GetByIdAsync(It.IsAny<string[]>(), It.IsAny<CancellationToken>()),
            Times.Once);

        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoFirebaseRepository>().Verify(
            repository => repository.AddUpdateAsync(It.IsAny<Dictionary<string, object?>>(), It.IsAny<CancellationToken>()),
            Times.Never);

        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoFirebaseRepository>().Verify(
            repository => repository.AddUpdateAsync(It.IsAny<List<Entities.Entities.Solicitacao>>(), It.IsAny<CancellationToken>()),
            Times.Never);

        var StatusDeSolicitacaoIntegracaoRecebidaEventPublished =
            await _dependencyInjectorFactory.Harness.Published.Any<StatusDeSolicitacaoIntegracaoRecebidaEvent>();

        var trocarDeStatusNoFirebaseCommandPublished =
            await _dependencyInjectorFactory.Harness.Published.Any<TrocarDeStatusNoFirebaseCommand>();

        StatusDeSolicitacaoIntegracaoRecebidaEventPublished.ShouldBeTrue();
        trocarDeStatusNoFirebaseCommandPublished.ShouldBeFalse();
    }
}
