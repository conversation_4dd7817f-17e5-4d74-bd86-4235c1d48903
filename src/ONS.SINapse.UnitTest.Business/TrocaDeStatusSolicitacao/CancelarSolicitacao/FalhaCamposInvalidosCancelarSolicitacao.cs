using Microsoft.Extensions.DependencyInjection;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Repository.IRepository;
using ONS.SINapse.Repository.IRepository.Firebase;

using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands.Firebase;
using ONS.SINapse.Solicitacao.Dtos.Integracao;
using ONS.SINapse.Solicitacao.EventHandlers.Events;
using ONS.SINapse.UnitTest.Shared.Fixtures;

namespace ONS.SINapse.UnitTest.Business.TrocaDeStatusSolicitacao.CancelarSolicitacao;

public partial class CancelarSolicitacaoTest
{
    [Fact(DisplayName = "[Cancelar] - Deve retornar o erro 'nenhuma solicitação encontrada'")]
    [Trait("Business", "Troca De Status Solicitacao")]
    public async Task CancelarSolicitacao_DeveRetornarUmErro_NenhumaSolicitacaoEncontrada()
    {
        // Arrange

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        var command = new CancelarEmLoteCommand([]);

        // Act
        var response =
            await mediator.EnviarComandoAsync<CancelarEmLoteCommand, StatusDeSolicitacaoIntegracaoEnvioEmLoteDto>(
                command, CancellationToken.None);

        // Assert

        response.ShouldNotBeNull();
        response.Solicitacoes.ShouldBeEmpty();

        var repositoryMock = _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoRepository>();

        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoFirebaseRepository>().Verify(
            repository => repository.GetByIdAsync(It.IsAny<string[]>(), It.IsAny<CancellationToken>()),
            Times.Never);

        var canceladaPublished =
            _dependencyInjectorFactory.Harness.Published.Any<SolicitacaoConcluidaEvent>();

        var canceladaKafkaPublished =
            _dependencyInjectorFactory.Harness.Published.Any<StatusDeSolicitacaoIntegracaoRecebidaEvent>();

        await Task.WhenAll(canceladaPublished, canceladaKafkaPublished);

        canceladaPublished.Result.ShouldBeFalse();
        canceladaKafkaPublished.Result.ShouldBeFalse();
    }

    [Fact(DisplayName = "[Cancelar] - Deve retornar o erro 'usuário deve ser preenchido'")]
    [Trait("Business", "Troca De Status Solicitacao")]
    public async Task CancelarSolicitacao_DeveRetornarUmErro_UsuarioDeveSerPreenchido()
    {
        // Arrange
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoPendente();
        var origem = solicitacao.Origem.Codigo;

        _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoFirebaseRepository>()
            .Setup(repository => repository.GetByIdAsync(It.IsAny<string[]>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([solicitacao]);

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        var command = new CancelarEmLoteCommand([
            new StatusDeSolicitacaoIntegracaoRecebimentoDto(
                solicitacao.Id,
                solicitacao.Status,
                null,
                new ObjetoDeManobraDto(origem, origem),
                null,
                "SINapse"
            )
        ]);

        // Act
        var response =
            await mediator.EnviarComandoAsync<CancelarEmLoteCommand, StatusDeSolicitacaoIntegracaoEnvioEmLoteDto>(
                command, CancellationToken.None);

        // Assert
        await ValidarCancelarSolicitacao(response, "Usuario");
    }

    [Fact(DisplayName = "[Cancelar] - Deve retornar o erro 'nenhum centro encontrado'")]
    [Trait("Business", "Troca De Status Solicitacao")]
    public async Task CancelarSolicitacao_DeveRetornarUmErro_NenhumCentroEncontrado()
    {
        // Arrange
        
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoPendente();
        
        var usuario = UsuarioFixture.GerarUsuarioDtoValido();

        _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoFirebaseRepository>()
            .Setup(repository => repository.GetByIdAsync(It.IsAny<string[]>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([solicitacao]);

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        var command = new CancelarEmLoteCommand([
            new StatusDeSolicitacaoIntegracaoRecebimentoDto(
                solicitacao.Id,
                solicitacao.Status,
                null,
                null,
                usuario,
                "SINapse"
            )
        ]);

        // Act
        
        var response =
            await mediator.EnviarComandoAsync<CancelarEmLoteCommand, StatusDeSolicitacaoIntegracaoEnvioEmLoteDto>(
                command, CancellationToken.None);

        // Assert

        await ValidarCancelarSolicitacao(response, "Centro");
    }

    private async Task ValidarCancelarSolicitacao(StatusDeSolicitacaoIntegracaoEnvioEmLoteDto response,
        string campo)
    {
        Assert.NotNull(response);

        response.Solicitacoes
            .SelectMany(x => x.Erros)
            .Any(x => x == $"Campo {campo} precisa ser preenchido.")
            .ShouldBeTrue();

        response.Solicitacoes
            .All(x => x.Erros.Length > 0)
            .ShouldBeTrue();

        response.Solicitacoes
            .All(x => x.Status == StatusDeSolicitacao.Erro)
            .ShouldBeTrue();

        var solicitacaoCanceladaEventPublished =
            _dependencyInjectorFactory.Harness.Published.Any<SolicitacaoConcluidaEvent>();

        var canceladaKafkaPublished =
            _dependencyInjectorFactory.Harness.Published.Any<StatusDeSolicitacaoIntegracaoRecebidaEvent>();

        var commandPublished =
            _dependencyInjectorFactory.Harness.Published.Any<TrocarDeStatusNoFirebaseCommand>();

        await Task.WhenAll(solicitacaoCanceladaEventPublished, canceladaKafkaPublished, commandPublished);

        solicitacaoCanceladaEventPublished.Result.ShouldBeFalse();
        canceladaKafkaPublished.Result.ShouldBeTrue();
        commandPublished.Result.ShouldBeFalse();
    }
}