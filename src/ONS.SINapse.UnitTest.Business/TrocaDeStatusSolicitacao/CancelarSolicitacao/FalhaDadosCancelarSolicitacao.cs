using System.Linq.Expressions;
using Microsoft.Extensions.DependencyInjection;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Repository.IRepository.InMemory;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using ONS.SINapse.Solicitacao.Dtos.Integracao;
using ONS.SINapse.Solicitacao.EventHandlers.Events;
using ONS.SINapse.UnitTest.Shared.Fixtures;


namespace ONS.SINapse.UnitTest.Business.TrocaDeStatusSolicitacao.CancelarSolicitacao;

public partial class CancelarSolicitacaoTest
{
    [Fact(DisplayName = "[Cancelar] - Deve retornar erro quando solicitação não existir")]
    [Trait("Business", "Troca De Status Solicitacao")]
    public async Task CancelarEmLote_DeveRetornarErro_SolicitacaoNaoExistente()
    {
        // Arrange
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoPendente();
        var origem = solicitacao.Origem.Codigo;
        var usuario = UsuarioFixture.GerarUsuarioDtoValido();
        
        ConfigurarMockUserContext(usuario, origem);

        _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoMemoryRepository>()
            .Setup(repository => repository.GetList(It.IsAny<Expression<Func<Entities.Entities.Solicitacao, bool>>>()))
            .Returns([]);

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        var command = new CancelarEmLoteCommand([new StatusDeSolicitacaoIntegracaoRecebimentoDto(
            solicitacao.Id,
            solicitacao.Status,
            null,
            new ObjetoDeManobraDto(origem, origem),
            usuario,
            "SINapse"
        )]);

        // Act
        var response = await mediator.EnviarComandoAsync<CancelarEmLoteCommand, StatusDeSolicitacaoIntegracaoEnvioEmLoteDto>(command, CancellationToken.None);

        // Assert
        await ValidarDadosCancelarSolicitacao(response,
            $"Não foi possível alterar o status da solicitação {solicitacao.Id} para Cancelada. Solicitação não existe.");
    }

    [Fact(DisplayName = "[Cancelar] - Deve retornar erro quando status da solicitação for inválido")]
    [Trait("Business", "Troca De Status Solicitacao")]
    public async Task CancelarEmLote_DeveRetornarErro_StatusInvalido()
    {
        // Arrange
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoCancelada();
        var origem = solicitacao.Origem.Codigo;
        var usuario = UsuarioFixture.GerarUsuarioDtoValido();

        ConfigurarMockUserContext(usuario, origem);

        _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoMemoryRepository>()
            .Setup(repository => repository.GetList(It.IsAny<Expression<Func<Entities.Entities.Solicitacao, bool>>>()))
            .Returns([solicitacao]);

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        var command = new CancelarEmLoteCommand([new StatusDeSolicitacaoIntegracaoRecebimentoDto(
            solicitacao.Id,
            solicitacao.Status,
            null,
            new ObjetoDeManobraDto(origem, origem),
            usuario,
            "SINapse"
        )]);

        // Act
        
        var response = 
            await mediator.EnviarComandoAsync<CancelarEmLoteCommand, StatusDeSolicitacaoIntegracaoEnvioEmLoteDto>(command, CancellationToken.None);
        
        // Assert
        await ValidarDadosCancelarSolicitacao(response,
            $"Não foi possível alterar o status da solicitação {solicitacao.Id} para Cancelada. Solicitação se encontra com status {solicitacao.Status.GetDescription()}.");
    }

    [Fact(DisplayName = "[Cancelar] - Deve retornar erro quando usuário não for solicitante da própria solicitação")]
    [Trait("Business", "Troca De Status Solicitacao")]
    public async Task CancelarEmLote_DeveRetornarErro_SolicitanteInvalido()
    {
        // Arrange
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoPendente();
        var destino = solicitacao.Destino.Codigo;

        var usuario = UsuarioFixture.GerarUsuarioDtoValido();

        ConfigurarMockUserContext(usuario, destino);

        _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoMemoryRepository>()
            .Setup(repository => repository.GetList(It.IsAny<Expression<Func<Entities.Entities.Solicitacao, bool>>>()))
            .Returns([solicitacao]);

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        var command = new CancelarEmLoteCommand([new StatusDeSolicitacaoIntegracaoRecebimentoDto(
            solicitacao.Id,
            solicitacao.Status,
            null,
            new ObjetoDeManobraDto(destino, destino),
            usuario,
            "SINapse"
        )]);

        // Act
        var response = await mediator.EnviarComandoAsync<CancelarEmLoteCommand, StatusDeSolicitacaoIntegracaoEnvioEmLoteDto>(command, CancellationToken.None);

        // Assert
        await ValidarDadosCancelarSolicitacao(response,
            $"Não foi possível alterar o status da solicitação {solicitacao.Id} para Cancelada. Operação não permitida, usuário não é o solicitante.");
    }

    private async Task ValidarDadosCancelarSolicitacao(StatusDeSolicitacaoIntegracaoEnvioEmLoteDto response, string message)
    {
        response.ShouldNotBeNull();

        response.Solicitacoes
            .Where(x => x.Status == StatusDeSolicitacao.Erro)
            .ToList().ShouldNotBeEmpty();

        response.Solicitacoes.First().Erros.First()
            .ShouldBe(message);

        response.Solicitacoes
            .All(x => x.Status == StatusDeSolicitacao.Cancelada)
            .ShouldBeFalse();

        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoMemoryRepository>().Verify(
            repository => repository.GetList(It.IsAny<Expression<Func<Entities.Entities.Solicitacao, bool>>>()),
            Times.Once);
        
        var solicitacaoCanceladaComErroEventPublished =
            await _dependencyInjectorFactory.Harness.Published.Any<StatusDeSolicitacaoIntegracaoRecebidaEvent>();

        var solicitacaoCanceladaEventPublished =
            await _dependencyInjectorFactory.Harness.Published.Any<SolicitacaoConcluidaEvent>();

        var commandPublicado =
            await _dependencyInjectorFactory.Harness.Published.Any<EnviarTrocaDeStatusAoFirebaseCommand>();

        solicitacaoCanceladaComErroEventPublished.ShouldBeTrue();
        solicitacaoCanceladaEventPublished.ShouldBeFalse();
        commandPublicado.ShouldBeFalse();
    }
}
