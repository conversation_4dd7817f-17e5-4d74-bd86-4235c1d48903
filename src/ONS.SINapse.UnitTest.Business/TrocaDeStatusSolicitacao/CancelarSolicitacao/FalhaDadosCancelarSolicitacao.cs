using System.Linq.Expressions;
using Microsoft.Extensions.DependencyInjection;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Repository.IRepository;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using ONS.SINapse.Solicitacao.Dtos.Integracao;
using ONS.SINapse.Solicitacao.EventHandlers.Events;
using ONS.SINapse.UnitTest.Shared.Fixtures;


namespace ONS.SINapse.UnitTest.Business.TrocaDeStatusSolicitacao.CancelarSolicitacao;

public partial class CancelarSolicitacaoTest
{
    [Fact(DisplayName = "[Cancelar] - Deve retornar erro quando solicitação não existir")]
    [Trait("Business", "Troca De Status Solicitacao")]
    public async Task CancelarEmLote_DeveRetornarErro_SolicitacaoNaoExistente()
    {
        // Arrange
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoPendente();
        var origem = solicitacao.Origem.Codigo;
        var usuario = UsuarioFixture.GerarUsuarioDtoValido();
        
        ConfigurarMockUserContext(usuario, origem);

        _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoRepository>()
            .Setup(repository => repository.GetAsync(It.IsAny<Expression<Func<Entities.Entities.Solicitacao, bool>>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        var command = new CancelarEmLoteCommand([new StatusDeSolicitacaoIntegracaoRecebimentoDto(
            solicitacao.Id,
            solicitacao.Status,
            null,
            new ObjetoDeManobraDto(origem, origem),
            usuario,
            "SINapse"
        )]);

        // Act
        var response = await mediator.EnviarComandoAsync<CancelarEmLoteCommand, StatusDeSolicitacaoIntegracaoEnvioEmLoteDto>(command, CancellationToken.None);

        // Assert
        await ValidarDadosCancelarSolicitacao(response,
            $"Não foi possível alterar o status da solicitação {solicitacao.Id} para Cancelada. Solicitação não existe.");
    }

    [Fact(DisplayName = "[Cancelar] - Deve retornar erro quando status da solicitação for inválido")]
    [Trait("Business", "Troca De Status Solicitacao")]
    public async Task CancelarEmLote_DeveRetornarErro_StatusInvalido()
    {
        // Arrange
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoCancelada();
        var origem = solicitacao.Origem.Codigo;
        var usuario = UsuarioFixture.GerarUsuarioDtoValido();

        ConfigurarMockUserContext(usuario, origem);

        _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoRepository>()
            .Setup(repository => repository.GetAsync(It.IsAny<Expression<Func<Entities.Entities.Solicitacao, bool>>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([solicitacao]);

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        var command = new CancelarEmLoteCommand([new StatusDeSolicitacaoIntegracaoRecebimentoDto(
            solicitacao.Id,
            solicitacao.Status,
            null,
            new ObjetoDeManobraDto(origem, origem),
            usuario,
            "SINapse"
        )]);

        // Act
        
        var response = 
            await mediator.EnviarComandoAsync<CancelarEmLoteCommand, StatusDeSolicitacaoIntegracaoEnvioEmLoteDto>(command, CancellationToken.None);
        
        // Assert
        await ValidarDadosCancelarSolicitacao(response,
            $"Não foi possível alterar o status da solicitação {solicitacao.Id} para Cancelada. Solicitação se encontra com status {solicitacao.Status.GetDescription()}.");
    }

    [Fact(DisplayName = "[Cancelar] - Deve retornar erro quando usuário não for solicitante da própria solicitação")]
    [Trait("Business", "Troca De Status Solicitacao")]
    public async Task CancelarEmLote_DeveRetornarErro_SolicitanteInvalido()
    {
        // Arrange
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoPendente();
        var destino = solicitacao.Destino.Codigo;

        var usuario = UsuarioFixture.GerarUsuarioDtoValido();

        ConfigurarMockUserContext(usuario, destino);
        
        _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoRepository>()
            .Setup(repository => repository.GetAsync(It.IsAny<Expression<Func<Entities.Entities.Solicitacao, bool>>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([solicitacao]);

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        var command = new CancelarEmLoteCommand([new StatusDeSolicitacaoIntegracaoRecebimentoDto(
            solicitacao.Id,
            solicitacao.Status,
            null,
            new ObjetoDeManobraDto(destino, destino),
            usuario,
            "SINapse"
        )]);

        // Act
        var response = await mediator.EnviarComandoAsync<CancelarEmLoteCommand, StatusDeSolicitacaoIntegracaoEnvioEmLoteDto>(command, CancellationToken.None);

        // Assert
        await ValidarDadosCancelarSolicitacao(response,
            $"Não foi possível alterar o status da solicitação {solicitacao.Id} para Cancelada. Operação não permitida, usuário não é o solicitante.");
    }

    private async Task ValidarDadosCancelarSolicitacao(StatusDeSolicitacaoIntegracaoEnvioEmLoteDto response, string message)
    {
        response.ShouldNotBeNull();

        response.Solicitacoes
            .Where(x => x.Status == StatusDeSolicitacao.Erro)
            .ToList().ShouldNotBeEmpty();

        response.Solicitacoes.First().Erros.First()
            .ShouldBe(message);

        response.Solicitacoes
            .All(x => x.Status == StatusDeSolicitacao.Cancelada)
            .ShouldBeFalse();

        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoRepository>().Verify(
            repository => repository.GetAsync(It.IsAny<Expression<Func<Entities.Entities.Solicitacao, bool>>>(), It.IsAny<CancellationToken>()),
            Times.Once);

        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoRepository>().Verify(
            repository => repository.BulkUpdateAsync(It.IsAny<List<Entities.Entities.Solicitacao>>(), It.IsAny<CancellationToken>()),
            Times.Never);
        
        var solicitacaoCanceladaComErroEventPublished =
            await _dependencyInjectorFactory.Harness.Published.Any<StatusDeSolicitacaoIntegracaoRecebidaEvent>();

        var solicitacaoCanceladaEventPublished =
            await _dependencyInjectorFactory.Harness.Published.Any<SolicitacoesCanceladasEmLoteEvent>();
        
        solicitacaoCanceladaComErroEventPublished.ShouldBeTrue();
        solicitacaoCanceladaEventPublished.ShouldBeFalse();
    }
}
