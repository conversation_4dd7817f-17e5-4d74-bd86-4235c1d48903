using Microsoft.Extensions.DependencyInjection;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Repository.IRepository.Firebase;

using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands.Firebase;
using ONS.SINapse.Solicitacao.Dtos.Integracao;
using ONS.SINapse.Solicitacao.EventHandlers.Events;
using ONS.SINapse.UnitTest.Shared.Fixtures;

namespace ONS.SINapse.UnitTest.Business.TrocaDeStatusSolicitacao.CancelarSolicitacao;

public partial class CancelarSolicitacaoTest
{
    [Fact(DisplayName = "[Cancelar] - Deve cancelar solicitacoes em lote")]
    [Trait("Business", "Troca De Status Solicitacao")]
    public async Task DeveCancelarSolicitacoesEmLote()
    {
        // Arrange
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoPendente();
        var origem = solicitacao.Origem.Codigo;
        var usuario = UsuarioFixture.GerarUsuarioDtoValido();
        
        ConfigurarMockUserContext(usuario, origem);

        _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoFirebaseRepository>()
            .Setup(repository => repository.GetByIdAsync(It.IsAny<string[]>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([solicitacao]);

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        var command = new CancelarEmLoteCommand([new StatusDeSolicitacaoIntegracaoRecebimentoDto(
            solicitacao.Id,
            solicitacao.Status,
            null,
            new ObjetoDeManobraDto(origem, origem),
            usuario,
            "SINapse"
        )]);

        // Act
        var response = await mediator.EnviarComandoAsync<CancelarEmLoteCommand, StatusDeSolicitacaoIntegracaoEnvioEmLoteDto>(command, CancellationToken.None);

        // Assert
        Assert.NotNull(response);
            
        response.Solicitacoes
            .Select(x => x.Erros.Length == 0)
            .ToList()
            .ForEach(item => item.ShouldBeTrue());

        response.Solicitacoes
            .Where(x => x.Status == StatusDeSolicitacao.Erro)
            .ToList()
            .ShouldBeEmpty();

        response.Solicitacoes
            .All(x => x.Status == StatusDeSolicitacao.Cancelada)
            .ShouldBeTrue();

        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoFirebaseRepository>().Verify(
            repository => repository.GetByIdAsync(It.IsAny<string[]>(), It.IsAny<CancellationToken>()),
            Times.Between(1, 2, Moq.Range.Inclusive));

        var canceladaPublished =
            _dependencyInjectorFactory.Harness.Published.Any<SolicitacaoConcluidaEvent>();
            
        var canceladaKafkaPublished = 
            _dependencyInjectorFactory.Harness.Published.Any<StatusDeSolicitacaoIntegracaoRecebidaEvent>();

        var commandPublicado = 
            _dependencyInjectorFactory.Harness.Published.Any<TrocarDeStatusNoFirebaseCommand>();

        await Task.WhenAll(canceladaPublished, canceladaKafkaPublished, commandPublicado);
            
        canceladaPublished.Result.ShouldBeTrue();
        canceladaKafkaPublished.Result.ShouldBeTrue();
        commandPublicado.Result.ShouldBeTrue();
    }
}