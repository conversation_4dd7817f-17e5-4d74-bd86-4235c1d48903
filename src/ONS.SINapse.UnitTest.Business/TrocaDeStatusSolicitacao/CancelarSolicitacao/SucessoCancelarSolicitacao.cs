using System.Linq.Expressions;
using Microsoft.Extensions.DependencyInjection;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Repository.IRepository;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using ONS.SINapse.Solicitacao.EventHandlers.Events;

namespace ONS.SINapse.UnitTest.Business.TrocaDeStatusSolicitacao.CancelarSolicitacao
{
    public partial class CancelarSolicitacaoTest
    {
        [Fact(DisplayName = "[Cancelar] - Deve cancelar solicitacoes em lote")]
        [Trait("Business", "Troca De Status Solicitacao")]
        public async Task DeveCancelarSolicitacoesEmLote()
        {
            // Arrange
            var solicitacao = _solicitacaoFixture.GerarSolicitacaoPendente();
            var origem = solicitacao.Origem.Codigo;
            
            ConfigurarMockUserContext(origem);
            
            _dependencyInjectorFactory.Mocker
                .GetMock<ISolicitacaoRepository>()
                .Setup(repository => repository.GetAsync(It.IsAny<Expression<Func<Entities.Entities.Solicitacao, bool>>>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync([solicitacao]);

            var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

            var command = new CancelarEmLoteCommand([solicitacao.Id]);

            // Act
            var response = await mediator.EnviarComandoAsync<CancelarEmLoteCommand, ResultadoCancelamentoEmLoteDto>(command, CancellationToken.None);

            // Assert
            Assert.NotNull(response);
            response.ValidationResult.IsValid.ShouldBeTrue(response.ValidationResult.ToString());
            response.Resultado.Select(x => x.Erros.Count == 0).ToList().ForEach(item => item.ShouldBeTrue());

            response.Resultado
                .Where(x => x.StatusId == (short)StatusDeSolicitacao.Erro)
                .ToList().ShouldBeEmpty();

            response.Resultado
                .All(x => x.StatusId == (short)StatusDeSolicitacao.Cancelada)
                .ShouldBeTrue();

            _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoRepository>().Verify(
                repository => repository.GetAsync(It.IsAny<Expression<Func<Entities.Entities.Solicitacao, bool>>>(), It.IsAny<CancellationToken>()),
                Times.Once);

            _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoRepository>().Verify(
                repository => repository.BulkUpdateAsync(It.IsAny<List<Entities.Entities.Solicitacao>>(), It.IsAny<CancellationToken>()),
                Times.Once);

            (await _dependencyInjectorFactory.Harness.Published.Any<SolicitacoesCanceladasComErroEmLoteEvent>()).ShouldBeFalse();
            (await _dependencyInjectorFactory.Harness.Published.Any<SolicitacoesCanceladasEmLoteEvent>()).ShouldBeTrue();
        }
    }

}
