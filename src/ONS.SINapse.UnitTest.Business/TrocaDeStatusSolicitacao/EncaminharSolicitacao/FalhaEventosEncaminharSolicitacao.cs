using FluentValidation.Results;
using ONS.SINapse.Repository.IRepository.Firebase;
using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Solicitacao.Dtos.Integracao;
using ONS.SINapse.UnitTest.Shared.Fixtures;

using ONS.SINapse.Solicitacao.CommandHandlers.Commands.Firebase;

namespace ONS.SINapse.UnitTest.Business.TrocaDeStatusSolicitacao.EncaminharSolicitacao;

public partial class EncaminharSolicitacaoTest
{
    [Fact(DisplayName = "[EncaminharSolicitacao] - Deve retornar um erro em cadastrar solicitação no firebase")]
    [Trait("Business", "Encaminhar Solicitacao")]
    public async Task Encaminhar_DeveRetornarUmErro_FirebaseCommand()
    {
        // Aguardar isolamento e resetar mock
        await Task.Delay(50);
        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoFirebaseRepository>().Reset();

        // Arrange
        var objetoDeManobra = _objetoManobraFixture.GerarObjetoDeManobra("CN", "CNOS");
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoPendente(objetoDeManobra);
        var origem = solicitacao.Origem.Codigo;
        var usuario = UsuarioFixture.GerarUsuarioDtoValido();

        ConfigurarMockUserContext(usuario, origem);

        _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoFirebaseRepository>()
            .Setup(repository => repository.GetByIdAsync(It.IsAny<string[]>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([solicitacao]);

        var validationResult = new ValidationResult();
        validationResult.AdicionarErro("Erro ao enviar command para o firebase");

        var result = new StatusDeSolicitacaoIntegracaoEnvioEmLoteDto([
            new StatusDeSolicitacaoIntegracaoEnvioDto(
                solicitacao.Id,
                StatusDeSolicitacao.Erro,
                usuario,
                solicitacao.CreatedAt,
                null,
                solicitacao.SistemaDeOrigem,
                validationResult.Errors.Select(x => x.ErrorMessage).ToArray()
            )
        ]);

        result.ValidationResult.AdicionarErro("Erro ao enviar command para o firebase");

        var mediatorMock = _dependencyInjectorFactory.Mocker.GetMock<IMediatorHandler>();

        mediatorMock
            .Setup(x => x.EnviarComandoAsync<EncaminharSolicitacaoFirebaseCommand, StatusDeSolicitacaoIntegracaoEnvioEmLoteDto>(
                It.IsAny<EncaminharSolicitacaoFirebaseCommand>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(result);

        var command = new EncaminharSolicitacaoCommand
        {
            SolicitacoesId = [solicitacao.Id]
        };

        // Act
        var response =
            await mediatorMock.Object
                .EnviarComandoAsync(command, CancellationToken.None);

        // Assert
        Assert.NotNull(response);

        response.IsValid.ShouldBeFalse();
        response.Errors.ShouldNotBeEmpty();

        var encaminharSolicitacaoFirebaseCommand =
            await _dependencyInjectorFactory.Harness.Published.Any<EncaminharSolicitacaoFirebaseCommand>();

        var encaminharSolicitacaoNoFirebaseCommand =
            await _dependencyInjectorFactory.Harness.Published.Any<EncaminharSolicitacaoNoFirebaseCommand>();

        encaminharSolicitacaoFirebaseCommand.ShouldBeFalse();
        encaminharSolicitacaoNoFirebaseCommand.ShouldBeFalse();

        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoFirebaseRepository>().Verify(
            repository => repository.GetByIdAsync(It.IsAny<string[]>(), It.IsAny<CancellationToken>()),
            Times.Once);

        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoFirebaseRepository>().Verify(
            repository => repository.AddUpdateAsync(It.IsAny<Dictionary<string, object?>>(), It.IsAny<CancellationToken>()),
            Times.Never);
    }
}
