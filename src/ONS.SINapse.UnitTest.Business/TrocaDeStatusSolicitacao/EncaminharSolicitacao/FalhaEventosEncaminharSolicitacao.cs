using FluentValidation.Results;
using ONS.SINapse.Repository.IRepository;
using ONS.SINapse.Repository.IRepository.Firebase;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using ONS.SINapse.Solicitacao.EventHandlers.Events;
using System.Linq.Expressions;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Solicitacao.Dtos.Integracao;
using ONS.SINapse.UnitTest.Shared.Fixtures;

namespace ONS.SINapse.UnitTest.Business.TrocaDeStatusSolicitacao.EncaminharSolicitacao;

public partial class EncaminharSolicitacaoTest
{
    [Fact(DisplayName = "[EncaminharSolicitacao] - Deve retornar um erro em cadastrar solicitação no firebase")]
    [Trait("Business", "Encaminhar Solicitacao")]
    public async Task Encaminhar_DeveRetornarUmErro_FirebaseCommand()
    {
        // Arrange
        var objetoDeManobra = _objetoManobraFixture.GerarObjetoDeManobra("CN", "CNOS");
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoPendente(objetoDeManobra);
        var origem = solicitacao.Origem.Codigo;
        var usuario = UsuarioFixture.GerarUsuarioDtoValido();
        
        ConfigurarMockUserContext(usuario, origem);

        _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoRepository>()
            .Setup(repository => repository.GetAsync(It.IsAny<Expression<Func<Entities.Entities.Solicitacao, bool>>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([solicitacao]);

        var validationResult = new ValidationResult();
        validationResult.AdicionarErro("Erro ao enviar command para o firebase");

        var result = new StatusDeSolicitacaoIntegracaoEnvioEmLoteDto([
            new StatusDeSolicitacaoIntegracaoEnvioDto(
                solicitacao.Id,
                StatusDeSolicitacao.Erro,
                usuario,
                solicitacao.CreatedAt,
                null,
                solicitacao.SistemaDeOrigem,
                validationResult.Errors.Select(x => x.ErrorMessage).ToArray()
            )
        ]);

        var mediatorMock = _dependencyInjectorFactory.Mocker.GetMock<IMediatorHandler>();

        mediatorMock
            .Setup(x => x.EnviarComandoAsync<CadastrarSolicitacaoEmLoteCommand, StatusDeSolicitacaoIntegracaoEnvioEmLoteDto>(
                It.IsAny<CadastrarSolicitacaoEmLoteCommand>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(result);

        var command = new EncaminharSolicitacaoCommand
        {
            SolicitacoesId = [solicitacao.Id]
        };

        // Act
        var response =
            await mediatorMock.Object
                .EnviarComandoAsync(command, CancellationToken.None);

        // Assert
        Assert.NotNull(response);

        response.IsValid.ShouldBeFalse();
        response.Errors.ShouldNotBeEmpty();
        
        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoRepository>().Verify(
            repository => repository.GetAsync(It.IsAny<Expression<Func<Entities.Entities.Solicitacao, bool>>>(), It.IsAny<CancellationToken>()),
            Times.Once);

        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoRepository>().Verify(
            repository => repository.BulkUpdateAsync(It.IsAny<IEnumerable<Entities.Entities.Solicitacao>>(), It.IsAny<CancellationToken>()),
            Times.Never);

        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoFirebaseRepository>().Verify(
            repository => repository.PatchFlattenAsync(It.IsAny<List<EncaminharSolicitacaoFirebaseDto>>(), It.IsAny<CancellationToken>()),
            Times.Never);

        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoRepository>().Verify(
            repository => repository.AdicionarAsync(It.IsAny<List<Entities.Entities.Solicitacao>>(), It.IsAny<CancellationToken>()),
            Times.Never);

        var cadastrarSolicitacaoEmLoteCommandPublished =
            await _dependencyInjectorFactory.Harness.Published.Any<CadastrarSolicitacaoEmLoteCommand>();

        var cadastrarSolicitacaoEmLoteNoFirebaseCommandPublished =
            await _dependencyInjectorFactory.Harness.Published.Any<CadastrarSolicitacaoEmLoteNoFirebaseCommand>();

        var solicitacoesCadastradasEmLoteEventPublished =
            await _dependencyInjectorFactory.Harness.Published.Any<SolicitacoesCadastradasEmLoteEvent>();

        cadastrarSolicitacaoEmLoteCommandPublished.ShouldBeFalse();
        cadastrarSolicitacaoEmLoteNoFirebaseCommandPublished.ShouldBeFalse();
        solicitacoesCadastradasEmLoteEventPublished.ShouldBeFalse();
    }
}
