using Microsoft.Extensions.DependencyInjection;
using ONS.SINapse.Repository.IRepository.Firebase;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands.Firebase;
using ONS.SINapse.UnitTest.Shared.Fixtures;


namespace ONS.SINapse.UnitTest.Business.TrocaDeStatusSolicitacao.EncaminharSolicitacao;

public partial class EncaminharSolicitacaoTest
{
    [Fact(DisplayName = "[EncaminharSolicitacao] - Deve encaminhar solicitacao")]
    [Trait("Business", "Encaminhar Solicitacao")]
    public async Task DeveEncaminharSolicitacao()
    {
        // Isolamento mais agressivo para evitar falhas esporádicas em CI/CD
        await Task.Delay(100);

        // Reset completo de todos os mocks relacionados
        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoFirebaseRepository>().Reset();

        // Arrange
        var objetoDeManobraOrigem = _objetoManobraFixture.GerarObjetoDeManobra("CN", "CNOS");
        var objetoDeManobraDestino = _objetoManobraFixture.GerarObjetoDeManobra("NE", "COSR-NE");
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoPendente(objetoDeManobraOrigem, objetoDeManobraDestino);
        var solicitacaoResult = _solicitacaoFixture.GerarSolicitacaoPendente(objetoDeManobraDestino, solicitacao.EncaminharPara);

        var usuario = UsuarioFixture.GerarUsuarioDtoValido();

        ConfigurarMockUserContext(usuario, objetoDeManobraDestino.Codigo);

        _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoFirebaseRepository>()
            .Setup(repository => repository.GetByIdAsync(It.IsAny<string[]>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([solicitacao]);

        _dependencyInjectorFactory.Mocker
           .GetMock<ISolicitacaoFirebaseRepository>()
           .Setup(repository => repository.AddUpdateAsync(It.IsAny<List<Entities.Entities.Solicitacao>>(), It.IsAny<CancellationToken>()));

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        var command = new EncaminharSolicitacaoCommand
        {
            SolicitacoesId = [solicitacao.Id]
        };

        // Act
        var response = await mediator.EnviarComandoAsync(command, CancellationToken.None);

        // Assert
        Assert.NotNull(response);
        response.IsValid.ShouldBeTrue(response.ToString());

        response.Errors.ShouldBeEmpty();

        var encaminharSolicitacaoFirebaseCommand =
            await _dependencyInjectorFactory.Harness.Published.Any<EncaminharSolicitacaoFirebaseCommand>();

        var encaminharSolicitacaoNoFirebaseCommand =
            await _dependencyInjectorFactory.Harness.Published.Any<EncaminharSolicitacaoNoFirebaseCommand>();

        encaminharSolicitacaoFirebaseCommand.ShouldBeTrue();
        encaminharSolicitacaoNoFirebaseCommand.ShouldBeTrue();

        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoFirebaseRepository>().Verify(
            repository => repository.GetByIdAsync(It.IsAny<string[]>(), It.IsAny<CancellationToken>()),
            Times.Exactly(2));

        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoFirebaseRepository>().Verify(
            repository => repository.AddUpdateAsync(It.IsAny<Dictionary<string, object?>>(), It.IsAny<CancellationToken>()),
            Times.Once);
    }
}
