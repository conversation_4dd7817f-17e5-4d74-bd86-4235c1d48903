using Microsoft.Extensions.DependencyInjection;
using ONS.SINapse.Repository.IRepository.Firebase;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using System.Linq.Expressions;
using ONS.SINapse.UnitTest.Shared.Fixtures;
using ONS.SINapse.Repository.IRepository.InMemory;

namespace ONS.SINapse.UnitTest.Business.TrocaDeStatusSolicitacao.EncaminharSolicitacao;

public partial class EncaminharSolicitacaoTest
{
    [Fact(DisplayName = "[EncaminharSolicitacao] - Deve encaminhar solicitacao")]
    [Trait("Business", "Encaminhar Solicitacao")]
    public async Task DeveEncaminharSolicitacao()
    {
        // Arrange
        var objetoDeManobraOrigem = _objetoManobraFixture.GerarObjetoDeManobra("CN", "CNOS");
        var objetoDeManobraDestino = _objetoManobraFixture.GerarObjetoDeManobra("NE", "COSR-NE");
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoPendente(objetoDeManobraOrigem, objetoDeManobraDestino);
        var solicitacaoResult = _solicitacaoFixture.GerarSolicitacaoPendente(objetoDeManobraDestino, solicitacao.EncaminharPara);
        
        var usuario = UsuarioFixture.GerarUsuarioDtoValido();

        ConfigurarMockUserContext(usuario, objetoDeManobraDestino.Codigo);

   _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoFirebaseRepository>()
            .Setup(repository => repository.GetByIdAsync(It.IsAny<string[]>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([solicitacao]);

        _dependencyInjectorFactory.Mocker
           .GetMock<ISolicitacaoFirebaseRepository>()
           .Setup(repository => repository.AddUpdateAsync(It.IsAny<List<Entities.Entities.Solicitacao>>(), It.IsAny<CancellationToken>()));

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        var command = new EncaminharSolicitacaoCommand
        {
            SolicitacoesId = [solicitacao.Id]
        };

        // Act
        var response = await mediator.EnviarComandoAsync(command, CancellationToken.None);

        // Assert
        Assert.NotNull(response);
        response.IsValid.ShouldBeTrue(response.ToString());

        response.Errors.ShouldBeEmpty();

        var cadastrarSolicitacaoEmLoteCommandPublished =
            await _dependencyInjectorFactory.Harness.Published.Any<CadastrarSolicitacaoEmLoteCommand>();
        
        cadastrarSolicitacaoEmLoteCommandPublished.ShouldBeTrue();

        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoMemoryRepository>().Verify(
            repository => repository.GetList(It.IsAny<Expression<Func<Entities.Entities.Solicitacao, bool>>>()),
            Times.Once);

        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoFirebaseRepository>().Verify(
            repository => repository.AddUpdateAsync(It.IsAny<List<Entities.Entities.Solicitacao>>(), It.IsAny<CancellationToken>()),
            Times.Once);

        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoFirebaseRepository>().Verify(
            repository => repository.PatchFlattenAsync(It.IsAny<List<EncaminharSolicitacaoFirebaseDto>>(), It.IsAny<CancellationToken>()),
            Times.Once);
    }
}
