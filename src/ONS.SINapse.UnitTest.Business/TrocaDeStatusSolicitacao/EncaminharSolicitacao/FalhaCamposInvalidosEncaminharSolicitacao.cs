using FluentValidation.Results;
using FluentValidation.TestHelper;
using Microsoft.Extensions.DependencyInjection;
using ONS.SINapse.Repository.IRepository.Firebase;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using ONS.SINapse.Solicitacao.EventHandlers.Events;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands.Firebase;

namespace ONS.SINapse.UnitTest.Business.TrocaDeStatusSolicitacao.EncaminharSolicitacao;

public partial class EncaminharSolicitacaoTest
{
    [Fact(DisplayName = "[EncaminharSolicitacao] - Deve retornar o erro 'solicitações não podevem estar vazias'")]
    [Trait("Business", "Troca De Status Solicitacao")]
    public async Task FinalizarSolicitacao_DeveRetornarUmErro_SolicitacoesNaoPodemEstarVazias()
    {
        // Arrange
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoConfirmada();
        var origem = solicitacao.Origem.Codigo;

        ConfigurarMockUserContext(origem);

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        var command = new EncaminharSolicitacaoCommand
        {
            SolicitacoesId = []
        };

        // Act
        var response = await mediator.EnviarComandoAsync<EncaminharSolicitacaoCommand>(command, CancellationToken.None);

        // Assert
        await ValidarEncaminharSolicitacao(response, "SolicitacoesId");
    }

    [Fact(DisplayName = "[EncaminharSolicitacao] - Deve retornar o erro 'usuário deve ser preenchido'")]
    [Trait("Business", "Troca De Status Solicitacao")]
    public async Task FinalizarSolicitacao_DeveRetornarUmErro_UsuarioDeveSerPreenchido()
    {
        // Arrange
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoConfirmada();

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        var command = new EncaminharSolicitacaoCommand
        {
            SolicitacoesId = [solicitacao.Id]
        };

        // Act
        var response = await mediator.EnviarComandoAsync(command, CancellationToken.None);

        // Assert
        await ValidarEncaminharSolicitacao(response, "Usuario");
    }

    private async Task ValidarEncaminharSolicitacao(ValidationResult response, string campo)
    {
        Assert.NotNull(response);

        response.Errors.ShouldNotBeEmpty();
        response.IsValid.ShouldBe(false);

        var testValidationResult = new TestValidationResult<EncaminharSolicitacaoCommand>(response);
        testValidationResult.ShouldHaveValidationErrorFor(campo);

        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoFirebaseRepository>().Verify(
            repository => repository.GetByIdAsync(It.IsAny<string[]>(), It.IsAny<CancellationToken>())),
            Times.Never);

        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoFirebaseRepository>().Verify(
            repository => repository.PatchFlattenAsync(It.IsAny<List<EncaminharSolicitacaoFirebaseDto>>(), It.IsAny<CancellationToken>()),
            Times.Never);

        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoFirebaseRepository>().Verify(
            repository => repository.AddUpdateAsync(It.IsAny<List<Entities.Entities.Solicitacao>>(), It.IsAny<CancellationToken>()),
            Times.Never);

        var cadastrarSolicitacaoEmLoteCommandPublished =
            await _dependencyInjectorFactory.Harness.Published.Any<CadastrarSolicitacaoEmLoteCommand>();

        var CriarSolicitacaoNoFirebaseCommandPublished =
            await _dependencyInjectorFactory.Harness.Published.Any<CriarSolicitacaoNoFirebaseCommand>();

        var solicitacoesCadastradasEmLoteEventPublished =
            await _dependencyInjectorFactory.Harness.Published.Any<SolicitacoesCadastradasEmLoteEvent>();

        cadastrarSolicitacaoEmLoteCommandPublished.ShouldBeFalse();
        CriarSolicitacaoNoFirebaseCommandPublished.ShouldBeFalse();
        solicitacoesCadastradasEmLoteEventPublished.ShouldBeFalse();
    }
}
