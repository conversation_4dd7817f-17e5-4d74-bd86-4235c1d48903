using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Identity;
using ONS.SINapse.UnitTest.Business.Fixtures.Collections;
using ONS.SINapse.UnitTest.Shared;
using ONS.SINapse.UnitTest.Shared.Fixtures;
using Xunit.Abstractions;

namespace ONS.SINapse.UnitTest.Business.TrocaDeStatusSolicitacao.EncaminharSolicitacao;

[Collection(nameof(EncaminharSolicitacaoCollection))]
public partial class EncaminharSolicitacaoTest
{
    private readonly SolicitacaoFixture _solicitacaoFixture;
    private readonly ObjetoManobraFixture _objetoManobraFixture;
    private readonly ITestOutputHelper _testOutputHelper;
    private readonly MockDependencyInjectorFactory _dependencyInjectorFactory;

    public EncaminharSolicitacaoTest(SolicitacaoFixture solicitacaoFixture, ObjetoManobraFixture objetoManobraFixture, ITestOutputHelper testOutputHelper)
    {
        _solicitacaoFixture = solicitacaoFixture;
        _objetoManobraFixture = objetoManobraFixture;
        _testOutputHelper = testOutputHelper;
        _dependencyInjectorFactory = new MockDependencyInjectorFactory();
        _dependencyInjectorFactory.RegisterMocks();
    }

    private void ConfigurarMockUserContext(string destino)
    {
        var perfil = new Perfil([new Scope("CENTROS", destino, destino)], [], destino, destino);
        _dependencyInjectorFactory.Mocker
            .GetMock<IUserContext>()
            .SetupGet(x => x.Perfil)
            .Returns(perfil);
    }
    
    private void ConfigurarMockUserContext(UsuarioDto usuario, string destino)
    {
        ConfigurarMockUserContext(destino);

        var userContextMock = _dependencyInjectorFactory.Mocker.GetMock<IUserContext>();
        
        userContextMock.SetupGet(x => x.Sid)
            .Returns(usuario.Sid);
        
        userContextMock.SetupGet(x => x.Nome)
            .Returns(usuario.Nome);
        
        userContextMock.SetupGet(x => x.Login)
            .Returns(usuario.Login);

        userContextMock.Setup(x => x.UsuarioAutenticado()).Returns(true);
    }
}