using FluentValidation.Results;
using Microsoft.Extensions.DependencyInjection;
using ONS.SINapse.Repository.IRepository.Firebase;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using System.Linq.Expressions;
using ONS.SINapse.UnitTest.Shared.Fixtures;
using ONS.SINapse.Repository.IRepository.InMemory;

namespace ONS.SINapse.UnitTest.Business.TrocaDeStatusSolicitacao.EncaminharSolicitacao;

public partial class EncaminharSolicitacaoTest
{
    [Fact(DisplayName = "[EncaminharSolicitacao] - Deve retornar erro quando solicitação já estiver encaminhada")]
    [Trait("Business", "Encaminhar Solicitacao")]
    public async Task EncaminharSolicitacao_DeveRetornarErro_SolicitacaoJaEncaminhada()
    {
        // Arrange
        var objetoDeManobra = _objetoManobraFixture.GerarObjetoDeManobra("CN", "CNOS");
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoEncaminhada(objetoDeManobra);
        var origem = solicitacao.Origem.Codigo;
        var usuario = UsuarioFixture.GerarUsuarioDtoValido();

        ConfigurarMockUserContext(usuario, origem);

        _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoMemoryRepository>()
            .Setup(repository => repository.GetList(It.IsAny<Expression<Func<Entities.Entities.Solicitacao, bool>>>()))
            .Returns([solicitacao]);

        _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoFirebaseRepository>()
            .Setup(repository => repository.AddUpdateAsync(It.IsAny<List<Entities.Entities.Solicitacao>>(), It.IsAny<CancellationToken>()));

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        var command = new EncaminharSolicitacaoCommand
        {
            SolicitacoesId = [solicitacao.Id]
        };

        // Act
        var response = await mediator.EnviarComandoAsync(command, CancellationToken.None);

        // Assert
        await ValidarDadosEncaminharSolicitacao(response, $"Solicitacao {solicitacao.Id} já foi encaminhada.");
    }

    [Fact(DisplayName = "[EncaminharSolicitacao] - Deve retornar erro quando solicitação estiver com status inválido")]
    [Trait("Business", "Encaminhar Solicitacao")]
    public async Task EncaminharSolicitacao_DeveRetornarErro_SolicitacaoComStatusInvalido()
    {
        // Arrange
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoCancelada();
        var destino = solicitacao.Destino.Codigo;
        var usuario = UsuarioFixture.GerarUsuarioDtoValido();

        ConfigurarMockUserContext(usuario, destino);

        _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoMemoryRepository>()
            .Setup(repository => repository.GetList(It.IsAny<Expression<Func<Entities.Entities.Solicitacao, bool>>>()))
            .Returns([solicitacao]);

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        var command = new EncaminharSolicitacaoCommand
        {
            SolicitacoesId = [solicitacao.Id]
        };

        // Act
        var response = await mediator.EnviarComandoAsync(command, CancellationToken.None);

        // Assert
        await ValidarDadosEncaminharSolicitacao(response, $"Solicitação {solicitacao.Id} não pode ser encaminhada pois está {solicitacao.Status.GetDescription()}.");
    }

    [Fact(DisplayName = "[EncaminharSolicitacao] - Deve retornar erro quando solicitação estiver com origem inválida")]
    [Trait("Business", "Encaminhar Solicitacao")]
    public async Task EncaminharSolicitacao_DeveRetornarErro_SolicitacaoComOrigemIncorreta()
    {
        // Arrange
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoPendente();
        var usuario = UsuarioFixture.GerarUsuarioDtoValido();

        ConfigurarMockUserContext(usuario, "CTP");

        _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoMemoryRepository>()
            .Setup(repository => repository.GetList(It.IsAny<Expression<Func<Entities.Entities.Solicitacao, bool>>>()))
            .Returns([solicitacao]);

        _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoFirebaseRepository>()
            .Setup(repository => repository.AddUpdateAsync(It.IsAny<List<Entities.Entities.Solicitacao>>(), It.IsAny<CancellationToken>()));

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        var command = new EncaminharSolicitacaoCommand
        {
            SolicitacoesId = [solicitacao.Id]
        };

        // Act
        var response = await mediator.EnviarComandoAsync(command, CancellationToken.None);

        // Assert
        await ValidarDadosEncaminharSolicitacao(response, $"Solicitação {solicitacao.Id} não pode ser encaminhada pois não é do CNOS.");
    }

    private async Task ValidarDadosEncaminharSolicitacao(ValidationResult response, string message)
    {
        response.ShouldNotBeNull();
        response.IsValid.ShouldBeFalse(response.ToString());
        response.Errors.ShouldNotBeEmpty();

        response.Errors.Any(x => x.ErrorMessage == message).ShouldBeTrue();
        response.Errors.Any(x => x.ErrorMessage == "Não há solicitações válidas para encaminhar.").ShouldBeTrue();

        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoMemoryRepository>().Verify(
            repository => repository.GetList(It.IsAny<Expression<Func<Entities.Entities.Solicitacao, bool>>>()),
            Times.Once);

        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoFirebaseRepository>().Verify(
            repository => repository.PatchFlattenAsync(It.IsAny<List<EncaminharSolicitacaoFirebaseDto>>(), It.IsAny<CancellationToken>()),
            Times.Never);

        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoFirebaseRepository>().Verify(
            repository => repository.AddUpdateAsync(It.IsAny<List<Entities.Entities.Solicitacao>>(), It.IsAny<CancellationToken>()),
            Times.Never);

        var cadastrarSolicitacaoEmLoteCommandPublished =
            await _dependencyInjectorFactory.Harness.Published.Any<CadastrarSolicitacaoEmLoteCommand>();

        cadastrarSolicitacaoEmLoteCommandPublished.ShouldBeFalse();
    }
}
