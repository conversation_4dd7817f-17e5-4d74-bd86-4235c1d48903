using MassTransit;
using Microsoft.Extensions.Logging;
using ONS.SINapse.Shared.Bus;
using ONS.SINapse.Solicitacao.Dtos.Integracao;
using ONS.SINapse.Solicitacao.EventHandlers.Events;
using ONS.SINapse.Solicitacao.EventHandlers.Handlers;
using ONS.SINapse.UnitTest.Shared.Fixtures;

namespace ONS.SINapse.UnitTest.Business.TrocaDeStatusSolicitacao.FinalizarSolicitacaoAutomaticamente;

public class FinalizacaoAutoPublisherHandlerTest
{
    private readonly SolicitacaoFixture _solicitacaoFixture = new();

    [Fact(DisplayName = "Deve publicar evento de finalização automática quando houver solicitações elegíveis")]
    public async Task DevePublicarEventoQuandoSolicitacoesElegiveis()
    {
        // Arrange
        var busMock = new Mock<ISqsBus>();
        var loggerMock = new Mock<ILogger<FinalizacaoAutoPublisherHandler>>();
        var handler = new FinalizacaoAutoPublisherHandler(busMock.Object, loggerMock.Object);

        var solicitacaoConfirmada = _solicitacaoFixture.GerarSolicitacaoConfirmada();
        var solicitacaoCiencia = _solicitacaoFixture.GerarSolicitacaoCienciaInformada();

        var dtos = new List<StatusDeSolicitacaoIntegracaoEnvioDto>
        {
            new(
                solicitacaoConfirmada.Id,
                solicitacaoConfirmada.Status,
                UsuarioFixture.GerarUsuarioDtoValido(),
                DateTime.UtcNow,
                null,
                solicitacaoConfirmada.SistemaDeOrigem,
                []
            ),
            new(
                solicitacaoCiencia.Id,
                solicitacaoCiencia.Status,
                UsuarioFixture.GerarUsuarioDtoValido(),
                DateTime.UtcNow,
                null,
                solicitacaoCiencia.SistemaDeOrigem,
                []
            )
        };
        var evento = new StatusDeSolicitacaoIntegracaoRecebidaEvent(dtos);
        var consumeContextMock = new Mock<ConsumeContext<StatusDeSolicitacaoIntegracaoRecebidaEvent>>();
        consumeContextMock.Setup(x => x.Message).Returns(evento);
        consumeContextMock.Setup(x => x.CancellationToken).Returns(CancellationToken.None);

        // Act
        await handler.Consume(consumeContextMock.Object);

        // Assert
        busMock.Verify(x => x.Publish(It.Is<SolicitacaoFinalizacaoAutoEvent>(e =>
            e.Solicitacoes.Count == 2 &&
            e.Solicitacoes.Any(s => s.Id == solicitacaoConfirmada.Id && s.Status == solicitacaoConfirmada.Status) &&
            e.Solicitacoes.Any(s => s.Id == solicitacaoCiencia.Id && s.Status == solicitacaoCiencia.Status)
        ), CancellationToken.None), Times.Once);
    }

    [Fact(DisplayName = "Não deve publicar evento quando não houver solicitações elegíveis")]
    public async Task NaoDevePublicarEventoQuandoNaoHaElegiveis()
    {
        // Arrange
        var busMock = new Mock<ISqsBus>();
        var loggerMock = new Mock<ILogger<FinalizacaoAutoPublisherHandler>>();
        var handler = new FinalizacaoAutoPublisherHandler(busMock.Object, loggerMock.Object);

        var solicitacaoCancelada = _solicitacaoFixture.GerarSolicitacaoCancelada();
        var solicitacaoImpedida = _solicitacaoFixture.GerarSolicitacaoImpedida("Impedida para teste");
        var solicitacaoCiencia = _solicitacaoFixture.GerarSolicitacaoCienciaInformada();
        var dtos = new List<StatusDeSolicitacaoIntegracaoEnvioDto>
        {
            new(
                solicitacaoCancelada.Id,
                solicitacaoCancelada.Status,
                UsuarioFixture.GerarUsuarioDtoValido(),
                DateTime.UtcNow,
                null,
                solicitacaoCancelada.SistemaDeOrigem,
                []
            ),
            new(
                solicitacaoImpedida.Id,
                solicitacaoImpedida.Status,
                UsuarioFixture.GerarUsuarioDtoValido(),
                DateTime.UtcNow,
                null,
                solicitacaoImpedida.SistemaDeOrigem,
                []
            ),
            new(
                solicitacaoCiencia.Id,
                solicitacaoCiencia.Status,
                UsuarioFixture.GerarUsuarioDtoValido(),
                DateTime.UtcNow,
                null,
                solicitacaoCiencia.SistemaDeOrigem,
                ["Erro para teste"]
            )
        };
        var evento = new StatusDeSolicitacaoIntegracaoRecebidaEvent(dtos);
        var consumeContextMock = new Mock<ConsumeContext<StatusDeSolicitacaoIntegracaoRecebidaEvent>>();
        consumeContextMock.Setup(x => x.Message).Returns(evento);
        consumeContextMock.Setup(x => x.CancellationToken).Returns(CancellationToken.None);

        // Act
        await handler.Consume(consumeContextMock.Object);

        // Assert
        busMock.Verify(x => x.Publish(It.IsAny<SolicitacaoFinalizacaoAutoEvent>(), It.IsAny<CancellationToken>()), Times.Never);
    }
}
