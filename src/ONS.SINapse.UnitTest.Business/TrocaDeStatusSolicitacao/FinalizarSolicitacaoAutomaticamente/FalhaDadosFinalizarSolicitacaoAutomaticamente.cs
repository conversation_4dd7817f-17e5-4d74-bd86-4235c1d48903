using Microsoft.Extensions.DependencyInjection;
using ONS.SINapse.Entities.Entities;
using ONS.SINapse.Integracao.Shared.Enums;

using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using ONS.SINapse.Solicitacao.Dtos.Integracao;
using ONS.SINapse.Solicitacao.EventHandlers.Events;
using ONS.SINapse.UnitTest.Shared.Fixtures;
using ONS.SINapse.Repository.IRepository.Firebase;

namespace ONS.SINapse.UnitTest.Business.TrocaDeStatusSolicitacao.FinalizarSolicitacaoAutomaticamente;

public partial class FinalizarSolicitacaoAutomaticamenteTest
{
    [Fact(DisplayName = "[Finalizar Automaticamente] - Deve lidar com lista de solicitações vazia")]
    [Trait("Business", "Troca De Status Solicitacao")]
    public async Task FinalizarAutomaticamente_SolicitacaoNaoExistente()
    {
        // Arrange
        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoFirebaseRepository>()
            .Setup(repository => repository.GetSolicitacoesParaFinalizarAsync(It.IsAny<DateTime>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        var command = new FinalizarAutomaticamenteCommand(true);

        // Act
        var response = 
            await mediator.EnviarComandoAsync<FinalizarAutomaticamenteCommand>(command, CancellationToken.None);

        // Assert
        Assert.NotNull(response);
        response.IsValid.ShouldBeTrue(response.ToString());

        response.Errors.ShouldBeEmpty();

        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoFirebaseRepository>().Verify(
           repository => repository.GetSolicitacoesParaFinalizarAsync(It.IsAny<DateTime>(), It.IsAny<CancellationToken>()),
           Times.AtLeastOnce());

        var comandoPublicado = _dependencyInjectorFactory.Harness.Published.Any<FinalizarEmLoteCommand>();
        var comandoConsumido = _dependencyInjectorFactory.Harness.Consumed.Any<FinalizarEmLoteCommand>();

        var eventoPublicado = _dependencyInjectorFactory.Harness.Published.Any<SolicitacaoFinalizadaAutomaticamenteEvent>();
        var eventoConsumido = _dependencyInjectorFactory.Harness.Consumed.Any<SolicitacaoFinalizadaAutomaticamenteEvent>();

        await Task.WhenAll(comandoPublicado, comandoConsumido, eventoPublicado, eventoConsumido);

        comandoPublicado.Result.ShouldBeFalse();
        comandoConsumido.Result.ShouldBeFalse();
        eventoPublicado.Result.ShouldBeTrue();
        eventoConsumido.Result.ShouldBeTrue();
    }

    [Fact(DisplayName = "[Finalizar Automaticamente] - Deve lidar com solicitacao invalida")]
    [Trait("Business", "Troca De Status Solicitacao")]
    public async Task FinalizarAutomaticamente_DeveRetornarErro_SolicitacaoInvalida()
    {
        // Arrange
        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        var solicitacao = _solicitacaoFixture.GerarSolicitacaoConfirmada(destino: new ObjetoDeManobra("NE", "COSR-NE"));

        var usuario = UsuarioFixture.GerarUsuarioDtoValido();
        var destino = solicitacao.Destino.Codigo;

        _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoFirebaseRepository>()
            .Setup(repository => repository.GetSolicitacoesParaFinalizarAsync(It.IsAny<DateTime>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([solicitacao]);

        var validationResult = new FluentValidation.Results.ValidationResult();
        validationResult.AdicionarErro(
            $"Não foi possível alterar o status da solicitação {solicitacao.Id} para {StatusDeSolicitacao.Finalizada.GetDescription()}. " +
            $"Solicitação se encontra com status {solicitacao.Status.GetDescription()}.");

        var result = new StatusDeSolicitacaoIntegracaoEnvioEmLoteDto([
            new StatusDeSolicitacaoIntegracaoEnvioDto(
                solicitacao.Id,
                StatusDeSolicitacao.Erro,
                usuario,
                solicitacao.CreatedAt,
                null,
                solicitacao.SistemaDeOrigem,
                validationResult.Errors.Select(x => x.ErrorMessage).ToArray()
            )
        ]);

        var mediatorMock = _dependencyInjectorFactory.Mocker.GetMock<IMediatorHandler>();

        mediatorMock
            .Setup(x => 
                x.EnviarComandoAsync<FinalizarEmLoteCommand, StatusDeSolicitacaoIntegracaoEnvioEmLoteDto>
                    (It.IsAny<FinalizarEmLoteCommand>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(result);

        ConfigurarMockUserContext(usuario, destino);

        var command = new FinalizarAutomaticamenteCommand(false);

        // Act
        var response =
            await mediator.EnviarComandoAsync<FinalizarAutomaticamenteCommand>(command, CancellationToken.None);

        // Assert
        Assert.NotNull(response);
        response.IsValid.ShouldBeFalse(response.ToString());

        response.Errors.ShouldNotBeEmpty();

        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoFirebaseRepository>().Verify(
           repository => repository.GetSolicitacoesParaFinalizarAsync(It.IsAny<DateTime>(), It.IsAny<CancellationToken>()),
           Times.AtLeastOnce());

        var eventoPublicado = _dependencyInjectorFactory.Harness.Published.Any<SolicitacaoFinalizadaAutomaticamenteEvent>();
        var eventoConsumido = _dependencyInjectorFactory.Harness.Consumed.Any<SolicitacaoFinalizadaAutomaticamenteEvent>();

        await Task.WhenAll(eventoPublicado, eventoConsumido);

        eventoPublicado.Result.ShouldBeTrue();
        eventoConsumido.Result.ShouldBeTrue();
    }

    [Fact(DisplayName = "[Finalizar Automaticamente] - Deve retornar erro ao buscar solicitações")]
    [Trait("Business", "Troca De Status Solicitacao")]
    public async Task FinalizarAutomaticamente_DeveRetornarErro_ErroAoBuscarDadosSolicitacao()
    {
        // Arrange
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoConfirmada(destino: new ObjetoDeManobra("NE", "COSR-NE"));

        var usuario = UsuarioFixture.GerarUsuarioDtoValido();
        var destino = solicitacao.Destino.Codigo;

        _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoFirebaseRepository>()
            .Setup(repository => repository.GetSolicitacoesParaFinalizarAsync(It.IsAny<DateTime>(), It.IsAny<CancellationToken>()))
            .Throws(new Exception("Erro"));

        ConfigurarMockUserContext(usuario, destino);

        var command = new FinalizarAutomaticamenteCommand(false);

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        // Act
        var response = await mediator.EnviarComandoAsync<FinalizarAutomaticamenteCommand>(
                command, CancellationToken.None);

        // Assert
        Assert.NotNull(response);
        response.IsValid.ShouldBeFalse(response.ToString());
        response.Errors.ShouldNotBeEmpty();

        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoFirebaseRepository>().Verify(
           repository => repository.GetSolicitacoesParaFinalizarAsync(It.IsAny<DateTime>(), It.IsAny<CancellationToken>()),
           Times.AtLeastOnce());

        var eventoPublicado = _dependencyInjectorFactory.Harness.Published.Any<SolicitacaoFinalizadaAutomaticamenteEvent>();
        var eventoConsumido = _dependencyInjectorFactory.Harness.Consumed.Any<SolicitacaoFinalizadaAutomaticamenteEvent>();

        await Task.WhenAll(eventoPublicado, eventoConsumido);

        eventoPublicado.Result.ShouldBeTrue();
        eventoConsumido.Result.ShouldBeTrue();
    }
}