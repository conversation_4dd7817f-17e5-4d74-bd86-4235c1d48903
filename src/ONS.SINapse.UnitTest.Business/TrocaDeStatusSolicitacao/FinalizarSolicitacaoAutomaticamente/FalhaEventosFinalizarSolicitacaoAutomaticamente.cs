using MassTransit;
using ONS.SINapse.Entities.Entities;
using ONS.SINapse.Repository.IRepository.InMemory;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using ONS.SINapse.Solicitacao.EventHandlers.Events;
using ONS.SINapse.UnitTest.Shared.Fixtures;
using System.Linq.Expressions;

namespace ONS.SINapse.UnitTest.Business.TrocaDeStatusSolicitacao.FinalizarSolicitacaoAutomaticamente;

public partial class FinalizarSolicitacaoAutomaticamenteTest
{
    [Fact(DisplayName = "[Finalizar Automaticamente] - Deve retornar um erro no evento de solicitação finalizada")]
    [Trait("Business", "Troca De Status Solicitacao")]
    public async Task FinalizarAutomaticamente_DeveRetornarUmErro_SolicitacaoConcluidaEvent()
    {
        // Arrange
        
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoConfirmada(destino: new ObjetoDeManobra("NE", "COSR-NE"));
        
        var usuario = UsuarioFixture.GerarUsuarioDtoValido();
        var destino = solicitacao.Destino.Codigo;

        _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoMemoryRepository>()
            .Setup(repository => repository.GetList(It.IsAny<Expression<Func<Entities.Entities.Solicitacao, bool>>>()))
            .Returns([solicitacao]);

        ConfigurarMockUserContext(usuario, destino);
    
        var command = new FinalizarAutomaticamenteCommand(false);
    
        var mediatorMock = _dependencyInjectorFactory.Mocker.GetMock<IMediatorHandler>();
        mediatorMock
            .Setup(mediator => mediator.PublicarEventoAsync(It.IsAny<SolicitacaoFinalizadaAutomaticamenteEvent>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new RequestFaultException());
    
        // Act & Assert

        await mediatorMock.Object
            .EnviarComandoAsync<FinalizarAutomaticamenteCommand>(
                command, CancellationToken.None)
            .ShouldThrowAsync<RequestFaultException>();

        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoMemoryRepository>().Verify(
            repository => repository.GetList(It.IsAny<Expression<Func<Entities.Entities.Solicitacao, bool>>>()),
            Times.AtLeastOnce());
        
        var eventoPublicado = _dependencyInjectorFactory.Harness.Published.Any<SolicitacaoFinalizadaAutomaticamenteEvent>();
        var eventoConsumido = _dependencyInjectorFactory.Harness.Consumed.Any<SolicitacaoFinalizadaAutomaticamenteEvent>();

        await Task.WhenAll(eventoPublicado, eventoConsumido);

        eventoPublicado.Result.ShouldBeFalse();
        eventoConsumido.Result.ShouldBeFalse();
    }
}