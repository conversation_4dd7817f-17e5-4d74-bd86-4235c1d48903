using Microsoft.Extensions.DependencyInjection;
using ONS.SINapse.Entities.Entities;

using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using ONS.SINapse.Solicitacao.EventHandlers.Events;
using ONS.SINapse.UnitTest.Shared.Fixtures;
using ONS.SINapse.Repository.IRepository.Firebase;

namespace ONS.SINapse.UnitTest.Business.TrocaDeStatusSolicitacao.FinalizarSolicitacaoAutomaticamente;

public partial class FinalizarSolicitacaoAutomaticamenteTest
{
    [Fact(DisplayName = "[Finalizar Automaticamente] - Deve finalizar solicitacoes em lote automaticamente")]
    [Trait("Business", "Troca De Status Solicitacao")]
    public async Task DeveFinalizarSolicitacoesAutomaticamente()
    {
        // Arrange
        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();
        
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoConfirmada(destino: new ObjetoDeManobra("NE", "COSR-NE"));
        
        var usuario = UsuarioFixture.GerarUsuarioDtoValido();
        var destino = solicitacao.Destino.Codigo;

        _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoFirebaseRepository>()
            .Setup(repository => repository.GetByIdAsync(It.IsAny<string[]>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([solicitacao]);

        _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoFirebaseRepository>()
            .Setup(repository => repository.GetSolicitacoesParaFinalizarAsync(It.IsAny<DateTime>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([solicitacao]);

        ConfigurarMockUserContext(usuario, destino);
    
        var command = new FinalizarAutomaticamenteCommand(false);
    
        // Act
        var response = 
            await mediator.EnviarComandoAsync<FinalizarAutomaticamenteCommand>(command, CancellationToken.None);
    
        // Assert
        Assert.NotNull(response);
        response.IsValid.ShouldBeTrue(response.ToString());

        response.Errors.ShouldBeEmpty();

        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoFirebaseRepository>().Verify(
           repository => repository.GetByIdAsync(It.IsAny<string[]>(), It.IsAny<CancellationToken>()),
           Times.AtLeastOnce());

        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoFirebaseRepository>().Verify(
           repository => repository.GetSolicitacoesParaFinalizarAsync(It.IsAny<DateTime>(), It.IsAny<CancellationToken>()),
           Times.AtLeastOnce());

        var comandoPublicado = _dependencyInjectorFactory.Harness.Published.Any<FinalizarEmLoteCommand>();
        var comandoConsumido = _dependencyInjectorFactory.Harness.Consumed.Any<FinalizarEmLoteCommand>();

        var eventoPublicado = _dependencyInjectorFactory.Harness.Published.Any<SolicitacaoFinalizadaAutomaticamenteEvent>();
        var eventoConsumido = _dependencyInjectorFactory.Harness.Consumed.Any<SolicitacaoFinalizadaAutomaticamenteEvent>();

        await Task.WhenAll(comandoPublicado, comandoConsumido, eventoPublicado, eventoConsumido);
        
        comandoPublicado.Result.ShouldBeTrue();
        comandoConsumido.Result.ShouldBeTrue();
        eventoPublicado.Result.ShouldBeTrue();
        eventoConsumido.Result.ShouldBeTrue();
    }

    [Fact(DisplayName = "[Finalizar Automaticamente] - Deve finalizar solicitacoes em lote automaticamente ignorando tempo")]
    [Trait("Business", "Troca De Status Solicitacao")]
    public async Task DeveFinalizarSolicitacoesAutomaticamenteIgnorandoTempo()
    {
        // Arrange
        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        var solicitacao = _solicitacaoFixture.GerarSolicitacaoConfirmada(destino: new ObjetoDeManobra("NE", "COSR-NE"));

        var usuario = UsuarioFixture.GerarUsuarioDtoValido();
        var destino = solicitacao.Destino.Codigo;

        _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoFirebaseRepository>()
            .Setup(repository => repository.GetByIdAsync(It.IsAny<string[]>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([solicitacao]);

        _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoFirebaseRepository>()
            .Setup(repository => repository.GetSolicitacoesParaFinalizarAsync(It.IsAny<DateTime>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([solicitacao]);

        ConfigurarMockUserContext(usuario, destino);

        var command = new FinalizarAutomaticamenteCommand(true);

        // Act
        var response =
            await mediator.EnviarComandoAsync<FinalizarAutomaticamenteCommand>(command, CancellationToken.None);

        // Assert
        Assert.NotNull(response);
        response.IsValid.ShouldBeTrue(response.ToString());

        response.Errors.ShouldBeEmpty();

        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoFirebaseRepository>().Verify(
           repository => repository.GetByIdAsync(It.IsAny<string[]>(), It.IsAny<CancellationToken>()),
           Times.AtLeastOnce());

        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoFirebaseRepository>().Verify(
           repository => repository.GetSolicitacoesParaFinalizarAsync(It.IsAny<DateTime>(), It.IsAny<CancellationToken>()),
           Times.AtLeastOnce());

        var comandoPublicado = _dependencyInjectorFactory.Harness.Published.Any<FinalizarEmLoteCommand>();
        var comandoConsumido = _dependencyInjectorFactory.Harness.Consumed.Any<FinalizarEmLoteCommand>();

        var eventoPublicado = _dependencyInjectorFactory.Harness.Published.Any<SolicitacaoFinalizadaAutomaticamenteEvent>();
        var eventoConsumido = _dependencyInjectorFactory.Harness.Consumed.Any<SolicitacaoFinalizadaAutomaticamenteEvent>();

        await Task.WhenAll(comandoPublicado, comandoConsumido, eventoPublicado, eventoConsumido);

        comandoPublicado.Result.ShouldBeTrue();
        comandoConsumido.Result.ShouldBeTrue();
        eventoPublicado.Result.ShouldBeTrue();
        eventoConsumido.Result.ShouldBeTrue();
    }
}