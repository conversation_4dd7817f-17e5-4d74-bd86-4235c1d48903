using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Identity;
using ONS.SINapse.UnitTest.Business.Fixtures.Collections;
using ONS.SINapse.UnitTest.Shared;
using ONS.SINapse.UnitTest.Shared.Fixtures;

namespace ONS.SINapse.UnitTest.Business.TrocaDeStatusSolicitacao.FinalizarSolicitacaoAutomaticamente;

[Collection(nameof(FinalizarSolicitacaoAutomaticamenteCollection))]
public partial class FinalizarSolicitacaoAutomaticamenteTest
{
    private readonly SolicitacaoFixture _solicitacaoFixture;
    private readonly MockDependencyInjectorFactory _dependencyInjectorFactory;

    public FinalizarSolicitacaoAutomaticamenteTest(SolicitacaoFixture solicitacaoFixture)
    {
        _solicitacaoFixture = solicitacaoFixture;
        _dependencyInjectorFactory = new MockDependencyInjectorFactory();
        _dependencyInjectorFactory.RegisterMocks();
    }
    
    private void ConfigurarMockUserContext(string destino)
    {
        var perfil = new Perfil([new Scope("CENTROS", destino, destino)], [], destino, destino);
        _dependencyInjectorFactory.Mocker
            .GetMock<IUserContext>()
            .SetupGet(x => x.Perfil)
            .Returns(perfil);
    }

    private void ConfigurarMockUserContext(UsuarioDto usuario, string destino)
    {
        ConfigurarMockUserContext(destino);

        var userContextMock = _dependencyInjectorFactory.Mocker.GetMock<IUserContext>();
        
        userContextMock.SetupGet(x => x.Sid)
            .Returns(usuario.Sid);
        
        userContextMock.SetupGet(x => x.Nome)
            .Returns(usuario.Nome);
        
        userContextMock.SetupGet(x => x.Login)
            .Returns(usuario.Login);
    }
}