using System.Linq.Expressions;
using AutoMapper;
using FluentValidation.Results;
using FluentValidation.TestHelper;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using ONS.SINapse.Entities.Entities;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Repository.IRepository;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Shared.Identity;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using ONS.SINapse.Solicitacao.Dtos.Integracao;
using ONS.SINapse.Solicitacao.EventHandlers.Events;
using ONS.SINapse.UnitTest.Business.Fixtures.Collections;
using ONS.SINapse.UnitTest.Shared;
using ONS.SINapse.UnitTest.Shared.Fixtures;
using ONS.SINapse.UnitTest.Shared.JsonConverters;

namespace ONS.SINapse.UnitTest.Business.TrocaDeStatusSolicitacao;

[Collection(nameof(ConfirmarSolicitacaoCollection))]
public class ConfirmarSolicitacaoTest
{
    private readonly SolicitacaoFixture _solicitacaoFixture;
    private readonly MockDependencyInjectorFactory _dependencyInjectorFactory;
    
    public ConfirmarSolicitacaoTest(SolicitacaoFixture solicitacaoFixture)
    {
        _solicitacaoFixture = solicitacaoFixture;
        _dependencyInjectorFactory = new MockDependencyInjectorFactory();
        _dependencyInjectorFactory.RegisterMocks();
    }
    
    [Fact(DisplayName = "[Confirmar] - Deve retornar sucesso ao confirmar solicitação")]
    [Trait("Business", "Troca De Status Solicitacao")]
    public async Task ConfirmarSolicitacao_DeveRetornarSucesso_ConfirmarSolicitacao()
    {
        // Arrange
        
        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();
        
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoPendente(destino: new ObjetoDeManobra("NE", "COSR-NE"));
        
        var usuario = UsuarioFixture.GerarUsuarioDtoValido();
        var destino = solicitacao.Destino.Codigo;
        
        _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoRepository>()
            .Setup(x => x.GetAsync(It.IsAny<Expression<Func<Entities.Entities.Solicitacao, bool>>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([solicitacao]);
        
        ConfigurarMockUserContext(usuario, destino);

        var command = new ConfirmarCommand([
            new StatusDeSolicitacaoIntegracaoRecebimentoDto(
                solicitacao.Id,
                StatusDeSolicitacao.Confirmada,
                null,
                new ObjetoDeManobraDto(destino, destino),
                usuario,
                "SINapse"
            )
        ]);
        
        // Act

        var response = await mediator
            .EnviarComandoAsync<ConfirmarCommand, StatusDeSolicitacaoIntegracaoEnvioEmLoteDto>(command, CancellationToken.None);

        // Assert
        
        response.Solicitacoes.All(x => x.Id == solicitacao.Id).ShouldBeTrue();
        
        ConfirmarSolicitacao_DeveRetornarSucesso_ValidarCommandResponse(response);
        ConfirmarSolicitacao_DeveRetornarSucesso_ValidarStatusSolicitacao(solicitacao);
        await ConfirmarSolicitacao_DeveRetornarSucesso_ValidarEventosERepository(_dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoRepository>());
        
    }

    [Fact(DisplayName = "[Confirmar] - Deve retornar um erro ao confirmar e enviar ao firebase")]
    [Trait("Business", "Troca De Status Solicitacao")]
    public async Task ConfirmarSolicitacao_DeveRetornarUmErro_EnviarAoFirebase()
    {
        var mediatorMock = _dependencyInjectorFactory.Mocker.GetMock<IMediatorHandler>();
        
        const string mensagemDeErro = "Erro ao enviar ao firebase";
        var result = new ValidationResult();
        result.AdicionarErro(mensagemDeErro);
        
        mediatorMock.CallBase = true;
        
        mediatorMock
            .Setup(x => x.EnviarComandoAsync(It.IsAny<EnviarTrocaDeStatusAoFirebaseCommand>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((EnviarTrocaDeStatusAoFirebaseCommand cmd, CancellationToken token) =>
            {
                Task.Run(async () => await mediatorMock.Object.EnviarComandoAsync(cmd, token), token);
                return result;
            });
        
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoPendente(destino: new ObjetoDeManobra("NE", "COSR-NE"));
        
        var usuario = UsuarioFixture.GerarUsuarioDtoValido();
        var destino = solicitacao.Destino.Codigo;
        
        _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoRepository>()
            .Setup(x => x.GetAsync(It.IsAny<Expression<Func<Entities.Entities.Solicitacao, bool>>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([solicitacao]);
        
        ConfigurarMockUserContext(usuario, destino);

        var command = new ConfirmarCommand([
            new StatusDeSolicitacaoIntegracaoRecebimentoDto(
                solicitacao.Id,
                StatusDeSolicitacao.Confirmada,
                null,
                new ObjetoDeManobraDto(destino, destino),
                usuario,
                "SINapse"
            )
        ]);
        
        // Act

        var response = await mediatorMock.Object
            .EnviarComandoAsync<ConfirmarCommand, StatusDeSolicitacaoIntegracaoEnvioEmLoteDto>(command, CancellationToken.None);
        
        // Assert
        
        Assert.NotNull(response);
        
        response.Solicitacoes.All(x => x.Id == solicitacao.Id).ShouldBeTrue();
        
        response.Solicitacoes.All(x => x.Status == StatusDeSolicitacao.Erro).ShouldBeTrue();

        var erros = response.Solicitacoes.SelectMany(x => x.Erros)
            .ToList();
        
        erros.ShouldNotBeEmpty();
        erros.Any(x => x == mensagemDeErro).ShouldBeTrue();
        
        var solicitacaoConfirmadaEventDisparado = await _dependencyInjectorFactory.Harness.Published
            .Any<StatusDeSolicitacaoIntegracaoRecebidaEvent>();
        
        solicitacaoConfirmadaEventDisparado.ShouldBeTrue();
    }
    
    
    [Fact(DisplayName = "[Confirmar] - Deve retornar um erro ao confirmar Codigo Invalido")]
    [Trait("Business", "Troca De Status Solicitacao")]
    public async Task ConfirmarSolicitacao_DeveRetornarUmErro_CodigoNaoPreenchido()
    {
        // Arrange

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();
        
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoPendente(destino: new ObjetoDeManobra("NE", "COSR-NE"));
        
        var usuario = UsuarioFixture.GerarUsuarioDtoValido();
        var destino = solicitacao.Destino.Codigo;
        
        _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoRepository>()
            .Setup(x => x.GetAsync(It.IsAny<Expression<Func<Entities.Entities.Solicitacao, bool>>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);
        
        ConfigurarMockUserContext(usuario, destino);

        var command = new ConfirmarCommand([
            new StatusDeSolicitacaoIntegracaoRecebimentoDto(
                null,
                StatusDeSolicitacao.Confirmada,
                null,
                new ObjetoDeManobraDto(destino, destino),
                usuario,
                "SINapse"
            )
        ]);
        
        // Act

        var response = await mediator
            .EnviarComandoAsync<ConfirmarCommand, StatusDeSolicitacaoIntegracaoEnvioEmLoteDto>(command, CancellationToken.None);
        
        // Assert
        
        await ValidarCamposConfirmarSolicitacaoCommand(response, nameof(StatusDeSolicitacaoIntegracaoRecebimentoDto.Id));
    }

    [Fact(DisplayName = "[Confirmar] - Deve retornar um erro ao confirmar Usuario Invalido")]
    [Trait("Business", "Troca De Status Solicitacao")]
    public async Task ConfirmarSolicitacao_DeveRetornarUmErro_UsuarioNaoPreenchido()
    {
        // Arrange
        
        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();
        
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoPendente(destino: new ObjetoDeManobra("NE", "COSR-NE"));
        
        var usuario = UsuarioFixture.GerarUsuarioDtoValido();
        var destino = solicitacao.Destino.Codigo;
        
        _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoRepository>()
            .Setup(x => x.GetAsync(It.IsAny<Expression<Func<Entities.Entities.Solicitacao, bool>>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([solicitacao]);
        
        ConfigurarMockUserContext(usuario, destino);

        var command = new ConfirmarCommand([
            new StatusDeSolicitacaoIntegracaoRecebimentoDto(
                solicitacao.Id,
                StatusDeSolicitacao.Confirmada,
                null,
                new ObjetoDeManobraDto(destino, destino),
                null,
                "SINapse"
            )
        ]);
        
        // Act

        var response = await mediator
            .EnviarComandoAsync<ConfirmarCommand, StatusDeSolicitacaoIntegracaoEnvioEmLoteDto>(command, CancellationToken.None);
        
        // Assert

        await ValidarCamposConfirmarSolicitacaoCommand(response, nameof(StatusDeSolicitacaoIntegracaoRecebimentoDto.Usuario));
    }

    [Fact(DisplayName = "[Confirmar] - Deve retornar um erro ao confirmar Solicitação não existe")]
    [Trait("Business", "Troca De Status Solicitacao")]
    public async Task ConfirmarSolicitacao_DeveRetornarUmErro_SolicitacaoNaoExiste()
    {
        // Arrange

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();
        
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoPendente(destino: new ObjetoDeManobra("NE", "COSR-NE"));
        
        var usuario = UsuarioFixture.GerarUsuarioDtoValido();
        var destino = solicitacao.Destino.Codigo;
        
        _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoRepository>()
            .Setup(x => x.GetAsync(It.IsAny<Expression<Func<Entities.Entities.Solicitacao, bool>>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);
        
        ConfigurarMockUserContext(usuario, destino);

        var command = new ConfirmarCommand([
            new StatusDeSolicitacaoIntegracaoRecebimentoDto(
                solicitacao.Id,
                StatusDeSolicitacao.Confirmada,
                null,
                new ObjetoDeManobraDto(destino, destino),
                usuario,
                "SINapse"
            )
        ]);
        
        // Act

        var response = await mediator
            .EnviarComandoAsync<ConfirmarCommand, StatusDeSolicitacaoIntegracaoEnvioEmLoteDto>(command, CancellationToken.None);


        // Assert
        
        Assert.NotNull(response);
        
        response.Solicitacoes.All(x => x.Id == solicitacao.Id).ShouldBeTrue();
        
        response.Solicitacoes.All(x => x.Status == StatusDeSolicitacao.Erro).ShouldBeTrue();

        var erros = response.Solicitacoes.SelectMany(x => x.Erros)
            .ToList();
        
        erros.ShouldNotBeEmpty();
        
        erros.Any(e => e.Contains("não existe", StringComparison.InvariantCultureIgnoreCase))
            .ShouldBeTrue();
        
        var solicitacaoEnviadaAoFirebase = _dependencyInjectorFactory.Harness.Published
            .Any<EnviarTrocaDeStatusAoFirebaseCommand>();

        var trocaStatusEventDisparado = _dependencyInjectorFactory.Harness.Published
            .Any<StatusDeSolicitacaoIntegracaoRecebidaEvent>();

        await Task.WhenAll(solicitacaoEnviadaAoFirebase, trocaStatusEventDisparado);
        
        solicitacaoEnviadaAoFirebase.Result.ShouldBeFalse();
        trocaStatusEventDisparado.Result.ShouldBeTrue();  
        
        _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoRepository>()
            .Verify(x =>
                    x.BulkUpdateAsync(It.IsAny<List<Entities.Entities.Solicitacao>>(), It.IsAny<CancellationToken>()),
                Times.Never);
    }

    [Fact(DisplayName = "[Confirmar] - Deve retornar um erro ao confirmar Solicitação status diferente de pendente")]
    [Trait("Business", "Troca De Status Solicitacao")]
    public async Task ConfirmarSolicitacao_DeveRetornarUmErro_StatusDiferenteDePendente()
    {
        // Arrange
        
        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();
        
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoCancelada();
        
        var usuario = UsuarioFixture.GerarUsuarioDtoValido();
        var destino = solicitacao.Destino.Codigo;
        
        _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoRepository>()
            .Setup(x => x.GetAsync(It.IsAny<Expression<Func<Entities.Entities.Solicitacao, bool>>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([solicitacao]);
        
        ConfigurarMockUserContext(usuario, destino);

        var command = new ConfirmarCommand([
            new StatusDeSolicitacaoIntegracaoRecebimentoDto(
                solicitacao.Id,
                StatusDeSolicitacao.Confirmada,
                null,
                new ObjetoDeManobraDto(destino, destino),
                usuario,
                "SINapse"
            )
        ]);
        
        // Act

        var response = await mediator
            .EnviarComandoAsync<ConfirmarCommand, StatusDeSolicitacaoIntegracaoEnvioEmLoteDto>(command, CancellationToken.None);
        
        // Assert
        
        Assert.NotNull(response);
        
        response.Solicitacoes.All(x => x.Id == solicitacao.Id).ShouldBeTrue();
        
        response.Solicitacoes.All(x => x.Status == StatusDeSolicitacao.Erro).ShouldBeTrue();

        var erros = response.Solicitacoes.SelectMany(x => x.Erros)
            .ToList();
        
        erros.ShouldNotBeEmpty();

        erros.Any(x =>
                x ==
                $"Não foi possível alterar o status da solicitação {solicitacao.Id} para {StatusDeSolicitacao.Confirmada.GetDescription()}. Solicitação se encontra com status {solicitacao.Status.GetDescription()}.")
            .ShouldBeTrue();
        
        var solicitacaoEnviadaAoFirebase = _dependencyInjectorFactory.Harness.Published
            .Any<EnviarTrocaDeStatusAoFirebaseCommand>();

        var trocaStatusEventDisparado = _dependencyInjectorFactory.Harness.Published
            .Any<StatusDeSolicitacaoIntegracaoRecebidaEvent>();

        await Task.WhenAll(solicitacaoEnviadaAoFirebase, trocaStatusEventDisparado);
        
        solicitacaoEnviadaAoFirebase.Result.ShouldBeFalse();
        trocaStatusEventDisparado.Result.ShouldBeTrue();
        
        _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoRepository>()
            .Verify(x =>
                    x.BulkUpdateAsync(It.IsAny<List<Entities.Entities.Solicitacao>>(), It.IsAny<CancellationToken>()),
                Times.Never);
    }

    [Fact(DisplayName = "[Confirmar] - Deve retornar um erro ao confirmar Solicitação usuário não é o destinatário")]
    [Trait("Business", "Troca De Status Solicitacao")]
    public async Task ConfirmarSolicitacao_DeveRetornarUmErro_UsuarioNaoEhDestinatario()
    {
        // Arrange
        
        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();
        
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoPendente(destino: new ObjetoDeManobra("NE", "COSR-NE"));
        
        var usuario = UsuarioFixture.GerarUsuarioDtoValido();
        const string destino = "SE";
        
        _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoRepository>()
            .Setup(x => x.GetAsync(It.IsAny<Expression<Func<Entities.Entities.Solicitacao, bool>>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([solicitacao]);
        
        ConfigurarMockUserContext(usuario, destino);

        var command = new ConfirmarCommand([
            new StatusDeSolicitacaoIntegracaoRecebimentoDto(
                solicitacao.Id,
                StatusDeSolicitacao.Confirmada,
                null,
                new ObjetoDeManobraDto(destino, destino),
                usuario,
                "SINapse"
            )
        ]);
        
        // Act

        var response = await mediator
            .EnviarComandoAsync<ConfirmarCommand, StatusDeSolicitacaoIntegracaoEnvioEmLoteDto>(command, CancellationToken.None);
        
        // Assert
        
        Assert.NotNull(response);
        
        response.Solicitacoes.All(x => x.Id == solicitacao.Id).ShouldBeTrue();
        
        response.Solicitacoes.All(x => x.Status == StatusDeSolicitacao.Erro).ShouldBeTrue();

        var erros = response.Solicitacoes.SelectMany(x => x.Erros)
            .ToList();
        
        erros.ShouldNotBeEmpty();

        erros.Any(x =>
                x ==
                $"Não foi possível alterar o status da solicitação {solicitacao.Id} para {StatusDeSolicitacao.Confirmada.GetDescription()}. Operação não permitida, usuário é o solicitante.")
            .ShouldBeTrue();
        
        var solicitacaoEnviadaAoFirebase = _dependencyInjectorFactory.Harness.Published
            .Any<EnviarTrocaDeStatusAoFirebaseCommand>();

        var trocaStatusEventDisparado = _dependencyInjectorFactory.Harness.Published
            .Any<StatusDeSolicitacaoIntegracaoRecebidaEvent>();

        await Task.WhenAll(solicitacaoEnviadaAoFirebase, trocaStatusEventDisparado);
        
        solicitacaoEnviadaAoFirebase.Result.ShouldBeFalse();
        trocaStatusEventDisparado.Result.ShouldBeTrue();
        
        _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoRepository>()
            .Verify(x =>
                    x.BulkUpdateAsync(It.IsAny<List<Entities.Entities.Solicitacao>>(), It.IsAny<CancellationToken>()),
                Times.Never);
    }
    
    private static void ConfirmarSolicitacao_DeveRetornarSucesso_ValidarStatusSolicitacao(
        Entities.Entities.Solicitacao solicitacao)
    {
        solicitacao.Status.ShouldBe(StatusDeSolicitacao.Confirmada);
        solicitacao.Chat.Any(x => x.Status == StatusDeSolicitacao.Confirmada).ShouldBeTrue();
        solicitacao.HistoricosDeStatus.Any(x => x.Status == StatusDeSolicitacao.Confirmada).ShouldBeTrue();
    }
    
    private static void ConfirmarSolicitacao_DeveRetornarSucesso_ValidarCommandResponse(StatusDeSolicitacaoIntegracaoEnvioEmLoteDto response)
    {
        Assert.NotNull(response);
        
        response.Solicitacoes.All(x => x.Status == StatusDeSolicitacao.Confirmada).ShouldBeTrue();

        response.Solicitacoes.SelectMany(x => x.Erros)
            .ShouldBeEmpty();
    }
    
    private async Task ConfirmarSolicitacao_DeveRetornarSucesso_ValidarEventosERepository(Mock<ISolicitacaoRepository> repositoryMock)
    {
        var solicitacaoEnviadaAoFirebase = _dependencyInjectorFactory.Harness.Published
            .Any<EnviarTrocaDeStatusAoFirebaseCommand>();

        var trocaStatusEventDisparado = _dependencyInjectorFactory.Harness.Published
            .Any<StatusDeSolicitacaoIntegracaoRecebidaEvent>();
        
        var solicitacaoEnviadaAoFirebaseConsumida = _dependencyInjectorFactory.Harness.Consumed
            .Any<EnviarTrocaDeStatusAoFirebaseCommand>();
        
        var trocaStatusEventDisparadoConsumida = _dependencyInjectorFactory.Harness.Consumed
            .Any<StatusDeSolicitacaoIntegracaoRecebidaEvent>();

        await Task.WhenAll(
            solicitacaoEnviadaAoFirebase, 
            trocaStatusEventDisparado,
            solicitacaoEnviadaAoFirebaseConsumida,
            trocaStatusEventDisparadoConsumida);
        
        solicitacaoEnviadaAoFirebase.Result.ShouldBeTrue();
        trocaStatusEventDisparado.Result.ShouldBeTrue();   
        solicitacaoEnviadaAoFirebaseConsumida.Result.ShouldBeTrue();   
        trocaStatusEventDisparadoConsumida.Result.ShouldBeTrue();

        repositoryMock.Verify(x
                => x.BulkUpdateAsync(It.IsAny<List<Entities.Entities.Solicitacao>>(), It.IsAny<CancellationToken>()),
            Times.Once);
    }
    
    private async Task ValidarCamposConfirmarSolicitacaoCommand(StatusDeSolicitacaoIntegracaoEnvioEmLoteDto response, string propertyName)
    {
        Assert.NotNull(response);
        
        response.Solicitacoes.All(x => x.Status == StatusDeSolicitacao.Erro).ShouldBeTrue();

        var erros = response.Solicitacoes.SelectMany(x => x.Erros)
            .ToList();
        
        erros.ShouldNotBeEmpty();

        erros.Any(x => x == $"Campo {propertyName} precisa ser preenchido.").ShouldBeTrue();
        
        var solicitacaoEnviadaAoFirebase = _dependencyInjectorFactory.Harness.Published
            .Any<EnviarTrocaDeStatusAoFirebaseCommand>();

        var trocaStatusEventDisparado = _dependencyInjectorFactory.Harness.Published
            .Any<StatusDeSolicitacaoIntegracaoRecebidaEvent>();

        await Task.WhenAll(solicitacaoEnviadaAoFirebase, trocaStatusEventDisparado);
        
        solicitacaoEnviadaAoFirebase.Result.ShouldBeFalse();
        trocaStatusEventDisparado.Result.ShouldBeTrue();
        
        _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoRepository>()
            .Verify(x =>
                    x.BulkUpdateAsync(It.IsAny<List<Entities.Entities.Solicitacao>>(), It.IsAny<CancellationToken>()),
                Times.Never);
    }
    
    private void ConfigurarMockUserContext(string destino)
    {
        var perfil = new Perfil([new Scope("CENTROS", destino, destino)], [], destino, destino);
        _dependencyInjectorFactory.Mocker
            .GetMock<IUserContext>()
            .SetupGet(x => x.Perfil)
            .Returns(perfil);
    }
    
    private void ConfigurarMockUserContext(UsuarioDto usuario, string destino)
    {
        ConfigurarMockUserContext(destino);

        var userContextMock = _dependencyInjectorFactory.Mocker.GetMock<IUserContext>();
        
        userContextMock.SetupGet(x => x.Sid)
            .Returns(usuario.Sid);
        
        userContextMock.SetupGet(x => x.Nome)
            .Returns(usuario.Nome);
        
        userContextMock.SetupGet(x => x.Login)
            .Returns(usuario.Login);
    }
}