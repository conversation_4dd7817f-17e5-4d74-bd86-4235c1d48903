using System.Linq.Expressions;
using FluentValidation.Results;
using FluentValidation.TestHelper;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using ONS.SINapse.Entities.Entities;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Repository.IRepository;
using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Shared.Identity;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using ONS.SINapse.Solicitacao.EventHandlers.Events;
using ONS.SINapse.UnitTest.Business.Fixtures.Collections;
using ONS.SINapse.UnitTest.Business.Fixtures.Solicitacao;
using ONS.SINapse.UnitTest.Shared;
using ONS.SINapse.UnitTest.Shared.Fixtures;

namespace ONS.SINapse.UnitTest.Business.TrocaDeStatusSolicitacao;

[Collection(nameof(ConfirmarSolicitacaoCollection))]
public class ConfirmarSolicitacaoTest
{
    private readonly SolicitacaoFixture _solicitacaoFixture;
    private readonly MockDependencyInjectorFactory _dependencyInjectorFactory;
    
    public ConfirmarSolicitacaoTest(SolicitacaoFixture solicitacaoFixture)
    {
        _solicitacaoFixture = solicitacaoFixture;
        _dependencyInjectorFactory = new MockDependencyInjectorFactory();
        _dependencyInjectorFactory.RegisterMocks();
    }
    
    [Fact(DisplayName = "[Confirmar] - Deve retornar sucesso ao confirmar solicitação")]
    [Trait("Business", "Troca De Status Solicitacao")]
    public async Task ConfirmarSolicitacao_DeveRetornarSucesso_ConfirmarSolicitacao()
    {
        // Arrange
        
        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();
        
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoPendente(destino: new ObjetoDeManobra("NE", "COSR-NE"));
        
        ConfirmarSolicitacao_DeveRetornarSucesso_MockSetup(solicitacao);
        
        var command = new ConfirmarCommand(solicitacao.Id);
        
        // Act

        var response = await mediator
            .EnviarComandoAsync<ConfirmarCommand, SolicitacaoConfirmadaResultDto>(command, CancellationToken.None);

        // Assert
        
        solicitacao.Id.ShouldBe(response.SolicitacaoId);
        
        ConfirmarSolicitacao_DeveRetornarSucesso_ValidarCommandResponse(response);
        ConfirmarSolicitacao_DeveRetornarSucesso_ValidarStatusSolicitacao(solicitacao);
        await ConfirmarSolicitacao_DeveRetornarSucesso_ValidarEventosERepository(_dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoRepository>());

    }

    [Fact(DisplayName = "[Confirmar] - Deve retornar um erro ao confirmar e enviar ao firebase")]
    [Trait("Business", "Troca De Status Solicitacao")]
    public async Task ConfirmarSolicitacao_DeveRetornarUmErro_EnviarAoFirebase()
    {
        // Arrange
        
        var mediatorMock = _dependencyInjectorFactory.Mocker.GetMock<IMediatorHandler>();

        const string mensagemDeErro = "Erro ao enviar ao firebase";
        var result = new ValidationResult();
        result.AdicionarErro(mensagemDeErro);
        
        mediatorMock
            .Setup(x => x.EnviarComandoAsync(It.IsAny<ConfirmarSolicitacaoNoFirebaseCommand>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(result);
        
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoPendente(destino: new ObjetoDeManobra("NE", "COSR-NE"));
        
        ConfirmarSolicitacao_DeveRetornarSucesso_MockSetup(solicitacao);
        
        var command = new ConfirmarCommand(solicitacao.Id);
        
        // Act
        
        var response = await mediatorMock.Object
            .EnviarComandoAsync<ConfirmarCommand, SolicitacaoConfirmadaResultDto>(command, CancellationToken.None);
        
        // Assert
        
        Assert.NotNull(response);
        
        solicitacao.Id.ShouldBe(response.SolicitacaoId);
        
        response.Erros.ShouldNotBeEmpty();
        response.Erros.Any(x => x == mensagemDeErro).ShouldBeTrue();
        response.ValidationResult.IsValid.ShouldBeFalse();
        response.ValidationResult.Errors.Any(x => x.ErrorMessage == mensagemDeErro).ShouldBeTrue();
        
        var solicitacaoConfirmadaEventDisparado = await _dependencyInjectorFactory.Harness.Published
            .Any<SolicitacaoConfirmadaEvent>();
        
        solicitacaoConfirmadaEventDisparado.ShouldBeFalse();
    }
    
    [Fact(DisplayName = "[Confirmar] - Deve retornar um erro ao confirmar Codigo Invalido")]
    [Trait("Business", "Troca De Status Solicitacao")]
    public async Task ConfirmarSolicitacao_DeveRetornarUmErro_CodigoNaoPreenchido()
    {
        // Arrange

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();
        
        var command = new ConfirmarCommand(string.Empty);
        
        // Act
        
        var response = await mediator
            .EnviarComandoAsync<ConfirmarCommand, SolicitacaoConfirmadaResultDto>(command, CancellationToken.None);
        
        // Assert
        
        await ValidarCamposConfirmarSolicitacaoCommand(response, x => x.Codigo);
    }

    [Fact(DisplayName = "[Confirmar] - Deve retornar um erro ao confirmar Usuario Invalido")]
    [Trait("Business", "Troca De Status Solicitacao")]
    public async Task ConfirmarSolicitacao_DeveRetornarUmErro_UsuarioNaoPreenchido()
    {
        // Arrange
        
        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();
        _dependencyInjectorFactory.Mocker.GetMock<IHttpContextAccessor>()
            .SetupGet(x => x.HttpContext).Returns(() => null);
        
        var command = new ConfirmarCommand("confirmar-solicitacao-erro-sem-usuario");
        
        // Act
        
        var response = await mediator
            .EnviarComandoAsync<ConfirmarCommand, SolicitacaoConfirmadaResultDto>(command, CancellationToken.None);
        
        // Assert

        await ValidarCamposConfirmarSolicitacaoCommand(response, x => x.Usuario);
    }

    [Fact(DisplayName = "[Confirmar] - Deve retornar um erro ao confirmar Solicitação não existe")]
    [Trait("Business", "Troca De Status Solicitacao")]
    public async Task ConfirmarSolicitacao_DeveRetornarUmErro_SolicitacaoNaoExiste()
    {
        // Arrange

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        var repositoryMock = _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoRepository>();
        
        repositoryMock.Setup(x => x.GetOneAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(null as Entities.Entities.Solicitacao);
        
        var command = new ConfirmarCommand("confirmar-solicitacao-nao-existe");
        
        // Act
        
        var response = await mediator.EnviarComandoAsync<ConfirmarCommand, SolicitacaoConfirmadaResultDto>(command, CancellationToken.None);

        // Assert
        
        Assert.NotNull(response);
        response.Erros.ShouldNotBeEmpty();

        response.ValidationResult.IsValid.ShouldBeFalse();
        
        response.Erros.Any(e => e.Contains("não existe", StringComparison.InvariantCultureIgnoreCase)).ShouldBeTrue();
        
        repositoryMock.Verify(x => x.GetOneAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()), Times.Once);
        
        var solicitacaoEnviadaAoFirebase = await _dependencyInjectorFactory.Harness.Published
            .Any<ConfirmarSolicitacaoNoFirebaseCommand>();

        var solicitacaoConfirmadaEventDisparado = await _dependencyInjectorFactory.Harness.Published
            .Any<SolicitacaoConfirmadaEvent>();
        
        solicitacaoEnviadaAoFirebase.ShouldBeFalse();
        solicitacaoConfirmadaEventDisparado.ShouldBeFalse();     
    }

    [Fact(DisplayName = "[Confirmar] - Deve retornar um erro ao confirmar Solicitação status diferente de pendente")]
    [Trait("Business", "Troca De Status Solicitacao")]
    public async Task ConfirmarSolicitacao_DeveRetornarUmErro_StatusDiferenteDePendente()
    {
        // Arrange
        
        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();
        
        var repositoryMock = _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoRepository>();
        
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoConfirmada();
        
        repositoryMock.Setup(x => x.GetOneAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(solicitacao);
        
        var command = new ConfirmarCommand("confirmar-solicitacao-status-diferente");
        
        // Act
        
        var response = await mediator
            .EnviarComandoAsync<ConfirmarCommand, SolicitacaoConfirmadaResultDto>(command, CancellationToken.None);
        
        // Assert
        
        Assert.NotNull(response);
        
        response.Erros.ShouldNotBeEmpty();
        
        response.ValidationResult.IsValid.ShouldBeFalse();
        
        var solicitacaoEnviadaAoFirebase = await _dependencyInjectorFactory.Harness.Published
            .Any<ConfirmarSolicitacaoNoFirebaseCommand>();

        var solicitacaoConfirmadaEventDisparado = await _dependencyInjectorFactory.Harness.Published
            .Any<SolicitacaoConfirmadaEvent>();
        
        solicitacaoEnviadaAoFirebase.ShouldBeFalse();
        solicitacaoConfirmadaEventDisparado.ShouldBeFalse();
    }

    [Fact(DisplayName = "[Confirmar] - Deve retornar um erro ao confirmar Solicitação usuário não é o destinatário")]
    [Trait("Business", "Troca De Status Solicitacao")]
    public async Task ConfirmarSolicitacao_DeveRetornarUmErro_UsuarioNaoEhDestinatario()
    {
        // Arrange
        
        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();
        
        var command = new ConfirmarCommand("confirmar-solicitacao-usuario-nao-eh-destinatario");
        
        var repositoryMock = _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoRepository>();
        
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoPendente(destino: new ObjetoDeManobra("NE", "CORS-NE"));
        
        repositoryMock.Setup(x => x.GetOneAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(solicitacao);
        
        // Act
        
        // Middleware de usuário UserDefinitionMiddleware por padrão seta um usuário anônimo.
        var response = await mediator
            .EnviarComandoAsync<ConfirmarCommand, SolicitacaoConfirmadaResultDto>(command, CancellationToken.None);

        // Assert
        
        Assert.NotNull(response);
        
        response.Erros.ShouldNotBeEmpty();
        
        response.ValidationResult.IsValid.ShouldBeFalse();
        
        response.Erros.Any(e => e.Contains("não é destinatário", StringComparison.InvariantCultureIgnoreCase)).ShouldBeTrue();
        
        var solicitacaoEnviadaAoFirebase = await _dependencyInjectorFactory.Harness.Published
            .Any<ConfirmarSolicitacaoNoFirebaseCommand>();

        var solicitacaoConfirmadaEventDisparado = await _dependencyInjectorFactory.Harness.Published
            .Any<SolicitacaoConfirmadaEvent>();
        
        solicitacaoEnviadaAoFirebase.ShouldBeFalse();
        solicitacaoConfirmadaEventDisparado.ShouldBeFalse();
    }

    private void ConfirmarSolicitacao_DeveRetornarSucesso_MockSetup(Entities.Entities.Solicitacao solicitacao)
    {
        _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoRepository>()
            .Setup(x => x.GetOneAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(solicitacao);
        
        var perfil = new Perfil([new Scope("CENTROS", "NE", "COSR-NE")], [], "COSR-NE", "NE");
        
        _dependencyInjectorFactory.Mocker.GetMock<IUserContext>()
            .SetupGet(x => x.Perfil).Returns(perfil);
    }
    
    private static void ConfirmarSolicitacao_DeveRetornarSucesso_ValidarStatusSolicitacao(
        Entities.Entities.Solicitacao solicitacao)
    {
        solicitacao.Status.ShouldBe(StatusDeSolicitacao.Confirmada);
        solicitacao.Chat.Any(x => x.Status == StatusDeSolicitacao.Confirmada).ShouldBeTrue();
        solicitacao.HistoricosDeStatus.Any(x => x.Status == StatusDeSolicitacao.Confirmada).ShouldBeTrue();
    }
    
    private static void ConfirmarSolicitacao_DeveRetornarSucesso_ValidarCommandResponse(SolicitacaoConfirmadaResultDto response)
    {
        Assert.NotNull(response);
        
        response.Erros.ShouldBeEmpty();
        response.ValidationResult.IsValid.ShouldBeTrue();
        response.Status.ShouldBe(StatusDeSolicitacao.Confirmada.GetDescription());
        response.StatusId.ShouldBe((short)StatusDeSolicitacao.Confirmada);
    }
    
    private async Task ConfirmarSolicitacao_DeveRetornarSucesso_ValidarEventosERepository(Mock<ISolicitacaoRepository> repositoryMock)
    {
        var solicitacaoEnviadaAoFirebase = await _dependencyInjectorFactory.Harness.Published
            .Any<ConfirmarSolicitacaoNoFirebaseCommand>();

        var solicitacaoConfirmadaEventDisparado = await _dependencyInjectorFactory.Harness.Published
            .Any<SolicitacaoConfirmadaEvent>();
        
        solicitacaoEnviadaAoFirebase.ShouldBeTrue();
        solicitacaoConfirmadaEventDisparado.ShouldBeTrue();
        
        solicitacaoEnviadaAoFirebase = await _dependencyInjectorFactory.Harness.Consumed
            .Any<ConfirmarSolicitacaoNoFirebaseCommand>();

        solicitacaoConfirmadaEventDisparado = await _dependencyInjectorFactory.Harness.Consumed
            .Any<SolicitacaoConfirmadaEvent>();
        
        solicitacaoEnviadaAoFirebase.ShouldBeTrue();
        solicitacaoConfirmadaEventDisparado.ShouldBeTrue();
        
        repositoryMock.Verify(x => x.AtualizarAsync(It.IsAny<Entities.Entities.Solicitacao>(), It.IsAny<CancellationToken>()), Times.Once);
    }
    
    private async Task ValidarCamposConfirmarSolicitacaoCommand<TProperty>(SolicitacaoConfirmadaResultDto response, Expression<Func<ConfirmarCommand, TProperty>> campo)
    {
        Assert.NotNull(response);
        
        response.Erros.ShouldNotBeEmpty();
        
        var testValidationResult = new TestValidationResult<ConfirmarCommand>(response.ValidationResult);
        testValidationResult.ShouldHaveValidationErrorFor(campo);
        
        var solicitacaoEnviadaAoFirebase = await _dependencyInjectorFactory.Harness.Published
            .Any<ConfirmarSolicitacaoNoFirebaseCommand>();

        var solicitacaoConfirmadaEventDisparado = await _dependencyInjectorFactory.Harness.Published
            .Any<SolicitacaoConfirmadaEvent>();
        
        solicitacaoEnviadaAoFirebase.ShouldBeFalse();
        solicitacaoConfirmadaEventDisparado.ShouldBeFalse();      
    }
}