using FluentValidation.Results;
using Microsoft.Extensions.DependencyInjection;
using ONS.SINapse.Entities.Entities;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Repository.IRepository;
using ONS.SINapse.Repository.IRepository.Firebase;

using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Shared.Identity;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Shared.Messages;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands.Firebase;
using ONS.SINapse.Solicitacao.Dtos.Integracao;
using ONS.SINapse.Solicitacao.EventHandlers.Events;
using ONS.SINapse.UnitTest.Business.Fixtures.Collections;
using ONS.SINapse.UnitTest.Shared;
using ONS.SINapse.UnitTest.Shared.Fixtures;

namespace ONS.SINapse.UnitTest.Business.TrocaDeStatusSolicitacao;

[Collection(nameof(ImpedirSolicitacaoCollection))]
public class ImpedirSolicitacaoTest
{
    private readonly SolicitacaoFixture _solicitacaoFixture;
    private readonly MockDependencyInjectorFactory _dependencyInjectorFactory;

    public ImpedirSolicitacaoTest(SolicitacaoFixture solicitacaoFixture)
    {
        _solicitacaoFixture = solicitacaoFixture;
        _dependencyInjectorFactory = new MockDependencyInjectorFactory();
        _dependencyInjectorFactory.RegisterMocks();
    }
    
    [Fact(DisplayName = "[Impedir] - Deve retornar sucesso ao impedir solicitação com status pendente")]
    [Trait("Business", "Troca De Status Solicitacao")]
    public async Task ImpedirSolicitacao_DeveRetornarSucesso_ImpedirSolicitacaoPendente()
    {
        // Arrange
        
        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();
        
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoPendente(destino: new ObjetoDeManobra("NE", "COSR-NE"));
        
        var usuario = UsuarioFixture.GerarUsuarioDtoValido();
        var destino = solicitacao.Destino.Codigo;

        _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoFirebaseRepository>()
            .Setup(repository => repository.GetByIdAsync(It.IsAny<string[]>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([solicitacao]);

        ConfigurarMockUserContext(usuario, destino);

        var command = new ImpedirCommand([
            new StatusDeSolicitacaoIntegracaoRecebimentoDto(
                solicitacao.Id,
                StatusDeSolicitacao.Impedida,
                "Teste de unidade - Impedir solicitacao pendente com sucesso",
                new ObjetoDeManobraDto(destino, destino),
                usuario,
                "SINapse"
            )
        ]);
        
        // Act

        var response = await mediator
            .EnviarComandoAsync<ImpedirCommand, StatusDeSolicitacaoIntegracaoEnvioEmLoteDto>(command, CancellationToken.None);

        // Assert
        
        response.Solicitacoes.All(x => x.Id == solicitacao.Id).ShouldBeTrue();
        
        ImpedirSolicitacao_DeveRetornarSucesso_ValidarCommandResponse(response);
        ImpedirSolicitacao_DeveRetornarSucesso_ValidarStatusSolicitacao(solicitacao);
        await ImpedirSolicitacao_DeveRetornarSucesso_ValidarEventosERepository(_dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoRepository>());
    }
    
    [Fact(DisplayName = "[Impedir] - Deve retornar sucesso ao impedir solicitação com status confirmada")]
    [Trait("Business", "Troca De Status Solicitacao")]
    public async Task ImpedirSolicitacao_DeveRetornarSucesso_ImpedirSolicitacaoConfirmada()
    {
        // Arrange
        
        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();
        
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoConfirmada(destino: new ObjetoDeManobra("NE", "COSR-NE"));
        
        var usuario = UsuarioFixture.GerarUsuarioDtoValido();
        var destino = solicitacao.Destino.Codigo;

        _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoFirebaseRepository>()
            .Setup(repository => repository.GetByIdAsync(It.IsAny<string[]>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([solicitacao]);

        ConfigurarMockUserContext(usuario, destino);

        var command = new ImpedirCommand([
            new StatusDeSolicitacaoIntegracaoRecebimentoDto(
                solicitacao.Id,
                StatusDeSolicitacao.Impedida,
                "Teste de unidade - Impedir solicitacao pendente com sucesso",
                new ObjetoDeManobraDto(destino, destino),
                usuario,
                "SINapse"
            )
        ]);
        
        // Act

        var response = await mediator
            .EnviarComandoAsync<ImpedirCommand, StatusDeSolicitacaoIntegracaoEnvioEmLoteDto>(command, CancellationToken.None);
        
        // Assert
        
        response.Solicitacoes.All(x => x.Id == solicitacao.Id).ShouldBeTrue();
        
        ImpedirSolicitacao_DeveRetornarSucesso_ValidarCommandResponse(response);
        ImpedirSolicitacao_DeveRetornarSucesso_ValidarStatusSolicitacao(solicitacao);
        await ImpedirSolicitacao_DeveRetornarSucesso_ValidarEventosERepository(_dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoRepository>());

    }
    
    [Fact(DisplayName = "[Impedir] - Deve retornar um erro ao impedir e enviar ao firebase")]
    [Trait("Business", "Troca De Status Solicitacao")]
    public async Task ImpedirSolicitacao_DeveRetornarUmErro_EnviarAoFirebase()
    {
        // Arrange
        
        var mediatorMock = _dependencyInjectorFactory.Mocker.GetMock<IMediatorHandler>();
        
        const string mensagemDeErro = "Erro ao enviar ao firebase";
        var result = new ValidationResult();
        result.AdicionarErro(mensagemDeErro);
        
        mediatorMock
            .Setup(x => x.EnviarComandoAsync(
                It.Is<Command>(cmd => cmd is TrocarDeStatusNoFirebaseCommand), It.IsAny<CancellationToken>()))
            .ReturnsAsync(result);
        
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoConfirmada(destino: new ObjetoDeManobra("NE", "COSR-NE"));
        
        var usuario = UsuarioFixture.GerarUsuarioDtoValido();
        var destino = solicitacao.Destino.Codigo;

        _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoFirebaseRepository>()
            .Setup(repository => repository.GetByIdAsync(It.IsAny<string[]>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([solicitacao]);

        ConfigurarMockUserContext(usuario, destino);

        var command = new ImpedirCommand([
            new StatusDeSolicitacaoIntegracaoRecebimentoDto(
                solicitacao.Id,
                StatusDeSolicitacao.Impedida,
                "Teste de unidade - Impedir solicitacao pendente com sucesso",
                new ObjetoDeManobraDto(destino, destino),
                usuario,
                "SINapse"
            )
        ]);
        
        // Act
        
        var response = await mediatorMock.Object
            .EnviarComandoAsync<ImpedirCommand, StatusDeSolicitacaoIntegracaoEnvioEmLoteDto>(command, CancellationToken.None);
        
        // Assert
        
        Assert.NotNull(response);
        
        response.Solicitacoes.All(x => x.Id == solicitacao.Id).ShouldBeTrue();
        
        response.Solicitacoes.All(x => x.Status == StatusDeSolicitacao.Erro).ShouldBeTrue();

        var erros = response.Solicitacoes.SelectMany(x => x.Erros)
            .ToList();
        
        erros.ShouldNotBeEmpty();
        erros.Any(x => x == mensagemDeErro).ShouldBeTrue();
        
        var solicitacaoImpedidaEventDisparado = _dependencyInjectorFactory.Harness.Published
            .Any<StatusDeSolicitacaoIntegracaoRecebidaEvent>();
        
        var trocaDeStatusEventDisparado = _dependencyInjectorFactory.Harness.Published
            .Any<SolicitacaoImpedidaEvent>();

        await Task.WhenAll(solicitacaoImpedidaEventDisparado, trocaDeStatusEventDisparado);
        
        solicitacaoImpedidaEventDisparado.Result.ShouldBeTrue();
        trocaDeStatusEventDisparado.Result.ShouldBeFalse();
    }
    
    [Fact(DisplayName = "[Impedir] - Deve retornar um erro ao impedir solicitação status incorreto")]
    [Trait("Business", "Troca De Status Solicitacao")]
    public async Task ImpedirSolicitacao_DeveRetornarUmErro_StatusIncorreto()
    {
        // Arrange
        
        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();
        
        var usuario = UsuarioFixture.GerarUsuarioDtoValido();
        
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoCancelada(usuario);
        
        var destino = solicitacao.Destino.Codigo;

        _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoFirebaseRepository>()
            .Setup(repository => repository.GetByIdAsync(It.IsAny<string[]>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([solicitacao]);

        ConfigurarMockUserContext(usuario, destino);

        var command = new ImpedirCommand([
            new StatusDeSolicitacaoIntegracaoRecebimentoDto(
                solicitacao.Id,
                StatusDeSolicitacao.Impedida,
                "Teste de unidade - Impedir solicitacao pendente com sucesso",
                new ObjetoDeManobraDto(destino, destino),
                usuario,
                "SINapse"
            )
        ]);
        
        // Act

        var response = await mediator
            .EnviarComandoAsync<ImpedirCommand, StatusDeSolicitacaoIntegracaoEnvioEmLoteDto>(command, CancellationToken.None);
        
        // Assert
        
        Assert.NotNull(response);
        
        response.Solicitacoes.All(x => x.Id == solicitacao.Id).ShouldBeTrue();
        
        response.Solicitacoes.All(x => x.Status == StatusDeSolicitacao.Erro).ShouldBeTrue();

        var erros = response.Solicitacoes.SelectMany(x => x.Erros)
            .ToList();
        
        erros.ShouldNotBeEmpty();

        erros.Any(x =>
                x ==
                $"Não foi possível alterar o status da solicitação {solicitacao.Id} para {StatusDeSolicitacao.Impedida.GetDescription()}. Solicitação se encontra com status {solicitacao.Status.GetDescription()}.")
            .ShouldBeTrue();
        
        var solicitacaoEnviadaAoFirebase = _dependencyInjectorFactory.Harness.Published
            .Any<TrocarDeStatusNoFirebaseCommand>();

        var solicitacaoImpedidaEventDisparado = _dependencyInjectorFactory.Harness.Published
            .Any<SolicitacaoImpedidaEvent>();

        var trocaStatusEventDisparado = _dependencyInjectorFactory.Harness.Published
            .Any<StatusDeSolicitacaoIntegracaoRecebidaEvent>();

        await Task.WhenAll(solicitacaoEnviadaAoFirebase, solicitacaoImpedidaEventDisparado, trocaStatusEventDisparado);
        
        solicitacaoEnviadaAoFirebase.Result.ShouldBeFalse();
        solicitacaoImpedidaEventDisparado.Result.ShouldBeFalse();
        trocaStatusEventDisparado.Result.ShouldBeTrue();
    }
    
    [Fact(DisplayName = "[Impedir] - Deve retornar um erro ao impedir solicitação usuário não é o destinatário")]
    [Trait("Business", "Troca De Status Solicitacao")]
    public async Task ImpedirSolicitacao_DeveRetornarUmErro_UsuarioNaoEhDestinatario()
    {
        // Arrange
        
        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();
        
        var usuario = UsuarioFixture.GerarUsuarioDtoValido();
        
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoPendente(destino: new ObjetoDeManobra("SE", "COSR-SE"));
        
        const string destino = "NE";

   _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoFirebaseRepository>()
            .Setup(repository => repository.GetByIdAsync(It.IsAny<string[]>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([solicitacao]);

        ConfigurarMockUserContext(usuario, destino);

        var command = new ImpedirCommand([
            new StatusDeSolicitacaoIntegracaoRecebimentoDto(
                solicitacao.Id,
                StatusDeSolicitacao.Impedida,
                "Teste de unidade - Impedir solicitacao pendente com sucesso",
                new ObjetoDeManobraDto(destino, destino),
                usuario,
                "SINapse"
            )
        ]);
        
        // Act
        
        var response = await mediator
            .EnviarComandoAsync<ImpedirCommand, StatusDeSolicitacaoIntegracaoEnvioEmLoteDto>(command, CancellationToken.None);

        // Assert
        
        Assert.NotNull(response);
        
        response.Solicitacoes.All(x => x.Id == solicitacao.Id).ShouldBeTrue();
        
        response.Solicitacoes.All(x => x.Status == StatusDeSolicitacao.Erro).ShouldBeTrue();

        var erros = response.Solicitacoes.SelectMany(x => x.Erros)
            .ToList();
        
        erros.ShouldNotBeEmpty();
        
        erros.Any(e => e.Contains("Operação não permitida, usuário é o solicitante.", StringComparison.InvariantCultureIgnoreCase))
            .ShouldBeTrue();
        
        var solicitacaoEnviadaAoFirebase = _dependencyInjectorFactory.Harness.Published
            .Any<TrocarDeStatusNoFirebaseCommand>();

        var solicitacaoImpedidaEventDisparado = _dependencyInjectorFactory.Harness.Published
            .Any<SolicitacaoImpedidaEvent>();

        var trocaStatusEventDisparado = _dependencyInjectorFactory.Harness.Published
            .Any<StatusDeSolicitacaoIntegracaoRecebidaEvent>();

        await Task.WhenAll(solicitacaoEnviadaAoFirebase, solicitacaoImpedidaEventDisparado, trocaStatusEventDisparado);
        
        solicitacaoEnviadaAoFirebase.Result.ShouldBeFalse();
        solicitacaoImpedidaEventDisparado.Result.ShouldBeFalse();
        trocaStatusEventDisparado.Result.ShouldBeTrue();
    }
    
    [Fact(DisplayName = "[Impedir] - Deve retornar um erro ao impedir Codigo Invalido")]
    [Trait("Business", "Troca De Status Solicitacao")]
    public async Task ImpedirSolicitacao_DeveRetornarUmErro_CodigoNaoPreenchido()
    {
        // Arrange

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoFirebaseRepository>()
            .Setup(repository => repository.GetByIdAsync(It.IsAny<string[]>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        var usuario = UsuarioFixture.GerarUsuarioDtoValido();
        
        var command = new ImpedirCommand([
            new StatusDeSolicitacaoIntegracaoRecebimentoDto(
                null,
                StatusDeSolicitacao.Impedida,
                "Teste de unidade - Impedir solicitacao pendente com sucesso",
                new ObjetoDeManobraDto("NE", "COSR-NE"),
                usuario,
                "SINapse"
            )
        ]);
        
        // Act
        
        var response = await mediator
            .EnviarComandoAsync<ImpedirCommand, StatusDeSolicitacaoIntegracaoEnvioEmLoteDto>(command, CancellationToken.None);
        
        // Assert
        
        await ValidarCamposImpedirSolicitacaoCommand(response, nameof(StatusDeSolicitacaoIntegracaoRecebimentoDto.Id));
    }

    [Fact(DisplayName = "[Impedir] - Deve retornar um erro ao impedir Usuario Invalido")]
    [Trait("Business", "Troca De Status Solicitacao")]
    public async Task ImpedirSolicitacao_DeveRetornarUmErro_UsuarioNaoPreenchido()
    {
        // Arrange
        
        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoFirebaseRepository>()
            .Setup(repository => repository.GetByIdAsync(It.IsAny<string[]>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        var command = new ImpedirCommand([
            new StatusDeSolicitacaoIntegracaoRecebimentoDto(
                "solicitacao",
                StatusDeSolicitacao.Impedida,
                "Teste de unidade - Impedir solicitacao pendente com sucesso",
                new ObjetoDeManobraDto("NE", "COSR-NE"),
                null,
                "SINapse"
            )
        ]);
        
        // Act
        
        var response = await mediator
            .EnviarComandoAsync<ImpedirCommand, StatusDeSolicitacaoIntegracaoEnvioEmLoteDto>(command, CancellationToken.None);
        
        // Assert

        await ValidarCamposImpedirSolicitacaoCommand(response, nameof(StatusDeSolicitacaoIntegracaoRecebimentoDto.Usuario));
    }

    [Fact(DisplayName = "[Impedir] - Deve retornar um erro ao impedir Motivo Invalido")]
    [Trait("Business", "Troca De Status Solicitacao")]
    public async Task ImpedirSolicitacao_DeveRetornarUmErro_MotivoNaoPreenchido()
    {
        // Arrange
        
        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        var solicitacao = _solicitacaoFixture.GerarSolicitacaoPendente();

        var destino = solicitacao.Destino.Codigo;

   _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoFirebaseRepository>()
            .Setup(repository => repository.GetByIdAsync(It.IsAny<string[]>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([solicitacao]);

        var usuario = UsuarioFixture.GerarUsuarioDtoValido();
        
        ConfigurarMockUserContext(usuario, destino);
        
        var command = new ImpedirCommand([
            new StatusDeSolicitacaoIntegracaoRecebimentoDto(
                solicitacao.Id,
                StatusDeSolicitacao.Impedida,
                null,
                new ObjetoDeManobraDto(destino, destino),
                usuario,
                "SINapse"
            )
        ]);
        
        // Act
        
        var response = await mediator
            .EnviarComandoAsync<ImpedirCommand, StatusDeSolicitacaoIntegracaoEnvioEmLoteDto>(command, CancellationToken.None);
        
        // Assert

        await ValidarCamposImpedirSolicitacaoCommand(response, nameof(StatusDeSolicitacaoIntegracaoRecebimentoDto.Motivo));
    }
    
    [Fact(DisplayName = "[Impedir] - Deve retornar um erro ao impedir Solicitação não existe")]
    [Trait("Business", "Troca De Status Solicitacao")]
    public async Task ImpedirSolicitacao_DeveRetornarUmErro_SolicitacaoNaoExiste()
    {
        // Arrange

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoFirebaseRepository>()
            .Setup(repository => repository.GetByIdAsync(It.IsAny<string[]>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        var solicitacao = _solicitacaoFixture.GerarSolicitacaoPendente();

        var destino = solicitacao.Destino.Codigo;
        
        var usuario = UsuarioFixture.GerarUsuarioDtoValido();
        
        ConfigurarMockUserContext(usuario, destino);
        
        var command = new ImpedirCommand([
            new StatusDeSolicitacaoIntegracaoRecebimentoDto(
                solicitacao.Id,
                StatusDeSolicitacao.Impedida,
                null,
                new ObjetoDeManobraDto(destino, destino),
                usuario,
                "SINapse"
            )
        ]);
        
        // Act
        
        var response = 
            await mediator.EnviarComandoAsync<ImpedirCommand, StatusDeSolicitacaoIntegracaoEnvioEmLoteDto>(command, CancellationToken.None);

        // Assert
        
        Assert.NotNull(response);
        
        response.Solicitacoes.All(x => x.Id == solicitacao.Id).ShouldBeTrue();
        
        response.Solicitacoes.All(x => x.Status == StatusDeSolicitacao.Erro).ShouldBeTrue();

        var erros = response.Solicitacoes.SelectMany(x => x.Erros)
            .ToList();
        
        erros.ShouldNotBeEmpty();
        
        erros.Any(e => e.Contains("não existe", StringComparison.InvariantCultureIgnoreCase))
            .ShouldBeTrue();
        
        var solicitacaoEnviadaAoFirebase = _dependencyInjectorFactory.Harness.Published
            .Any<TrocarDeStatusNoFirebaseCommand>();

        var solicitacaoImpedidaEventDisparado = _dependencyInjectorFactory.Harness.Published
            .Any<SolicitacaoImpedidaEvent>();

        var trocaStatusEventDisparado = _dependencyInjectorFactory.Harness.Published
            .Any<StatusDeSolicitacaoIntegracaoRecebidaEvent>();

        await Task.WhenAll(solicitacaoEnviadaAoFirebase, solicitacaoImpedidaEventDisparado, trocaStatusEventDisparado);
        
        solicitacaoEnviadaAoFirebase.Result.ShouldBeFalse();
        solicitacaoImpedidaEventDisparado.Result.ShouldBeFalse();
        trocaStatusEventDisparado.Result.ShouldBeTrue();
    }
    
    private async Task ValidarCamposImpedirSolicitacaoCommand(StatusDeSolicitacaoIntegracaoEnvioEmLoteDto response, string propertyName)
    {
        Assert.NotNull(response);
        
        response.Solicitacoes.All(x => x.Status == StatusDeSolicitacao.Erro).ShouldBeTrue();

        var erros = response.Solicitacoes.SelectMany(x => x.Erros)
            .ToList();
        
        erros.ShouldNotBeEmpty();

        erros.Any(x => x == $"Campo {propertyName} precisa ser preenchido.").ShouldBeTrue();
        
        var solicitacaoEnviadaAoFirebase = _dependencyInjectorFactory.Harness.Published
            .Any<TrocarDeStatusNoFirebaseCommand>();

        var solicitacaoImpedidaEventDisparado = _dependencyInjectorFactory.Harness.Published
            .Any<SolicitacaoImpedidaEvent>();

        var trocaStatusEventDisparado = _dependencyInjectorFactory.Harness.Published
            .Any<StatusDeSolicitacaoIntegracaoRecebidaEvent>();

        await Task.WhenAll(solicitacaoEnviadaAoFirebase, solicitacaoImpedidaEventDisparado, trocaStatusEventDisparado);
        
        solicitacaoEnviadaAoFirebase.Result.ShouldBeFalse();
        solicitacaoImpedidaEventDisparado.Result.ShouldBeFalse();
        trocaStatusEventDisparado.Result.ShouldBeTrue();

        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoFirebaseRepository>().Verify(
            repository => repository.AddUpdateAsync(It.IsAny<Dictionary<string, object?>>(), It.IsAny<CancellationToken>()),
            Times.Never);
    }
    
    private static void ImpedirSolicitacao_DeveRetornarSucesso_ValidarStatusSolicitacao(
        Entities.Entities.Solicitacao solicitacao)
    {
        solicitacao.Status.ShouldBe(StatusDeSolicitacao.Impedida);
        solicitacao.Chat.Any(x => x.Status == StatusDeSolicitacao.Impedida).ShouldBeTrue();
        solicitacao.HistoricosDeStatus.Any(x => x.Status == StatusDeSolicitacao.Impedida).ShouldBeTrue();
    }
    
    private static void ImpedirSolicitacao_DeveRetornarSucesso_ValidarCommandResponse(StatusDeSolicitacaoIntegracaoEnvioEmLoteDto response)
    {
        Assert.NotNull(response);
        
        response.Solicitacoes.All(x => x.Status == StatusDeSolicitacao.Impedida).ShouldBeTrue();

        response.Solicitacoes.SelectMany(x => x.Erros)
            .ShouldBeEmpty();
    }
    
    private async Task ImpedirSolicitacao_DeveRetornarSucesso_ValidarEventosERepository(Mock<ISolicitacaoRepository> repositoryMock)
    {
        var solicitacaoEnviadaAoFirebase = _dependencyInjectorFactory.Harness.Published
            .Any<TrocarDeStatusNoFirebaseCommand>();

        var solicitacaoImpedidaEventDisparado = _dependencyInjectorFactory.Harness.Published
            .Any<SolicitacaoImpedidaEvent>();

        var trocaStatusEventDisparado = _dependencyInjectorFactory.Harness.Published
            .Any<StatusDeSolicitacaoIntegracaoRecebidaEvent>();

        var solicitacaoConfirmadaEventDisparadoConsumida = _dependencyInjectorFactory.Harness.Consumed
            .Any<SolicitacaoImpedidaEvent>();
        
        var trocaStatusEventDisparadoConsumida = _dependencyInjectorFactory.Harness.Consumed
            .Any<StatusDeSolicitacaoIntegracaoRecebidaEvent>();

        await Task.WhenAll(
            solicitacaoEnviadaAoFirebase, 
            solicitacaoImpedidaEventDisparado, 
            trocaStatusEventDisparado,
            solicitacaoConfirmadaEventDisparadoConsumida,
            trocaStatusEventDisparadoConsumida);
        
        solicitacaoEnviadaAoFirebase.Result.ShouldBeTrue();
        solicitacaoImpedidaEventDisparado.Result.ShouldBeTrue();
        trocaStatusEventDisparado.Result.ShouldBeTrue(); 
        solicitacaoConfirmadaEventDisparadoConsumida.Result.ShouldBeTrue();   
        trocaStatusEventDisparadoConsumida.Result.ShouldBeTrue();

        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoFirebaseRepository>().Verify(
            repository => repository.AddUpdateAsync(It.IsAny<Dictionary<string, object?>>(), It.IsAny<CancellationToken>()),
            Times.Once);
    }
    
    private void ConfigurarMockUserContext(string destino)
    {
        var perfil = new Perfil([new Scope("CENTROS", destino, destino)], [], destino, destino);
        _dependencyInjectorFactory.Mocker
            .GetMock<IUserContext>()
            .SetupGet(x => x.Perfil)
            .Returns(perfil);
    }
    
    private void ConfigurarMockUserContext(UsuarioDto usuario, string destino)
    {
        ConfigurarMockUserContext(destino);

        var userContextMock = _dependencyInjectorFactory.Mocker.GetMock<IUserContext>();
        
        userContextMock.SetupGet(x => x.Sid)
            .Returns(usuario.Sid);
        
        userContextMock.SetupGet(x => x.Nome)
            .Returns(usuario.Nome);
        
        userContextMock.SetupGet(x => x.Login)
            .Returns(usuario.Login);
    }
}