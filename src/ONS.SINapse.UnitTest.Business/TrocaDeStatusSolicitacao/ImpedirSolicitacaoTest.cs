using System.Linq.Expressions;
using FluentValidation.Results;
using FluentValidation.TestHelper;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using ONS.SINapse.Entities.Entities;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Repository.IRepository;
using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Shared.Identity;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using ONS.SINapse.Solicitacao.EventHandlers.Events;
using ONS.SINapse.UnitTest.Business.Fixtures.Collections;
using ONS.SINapse.UnitTest.Business.Fixtures.Solicitacao;
using ONS.SINapse.UnitTest.Shared;
using ONS.SINapse.UnitTest.Shared.Fixtures;

namespace ONS.SINapse.UnitTest.Business.TrocaDeStatusSolicitacao;

[Collection(nameof(ImpedirSolicitacaoCollection))]
public class ImpedirSolicitacaoTest
{
    private readonly SolicitacaoFixture _solicitacaoFixture;
    private readonly MockDependencyInjectorFactory _dependencyInjectorFactory;

    public ImpedirSolicitacaoTest(SolicitacaoFixture solicitacaoFixture)
    {
        _solicitacaoFixture = solicitacaoFixture;
        _dependencyInjectorFactory = new MockDependencyInjectorFactory();
        _dependencyInjectorFactory.RegisterMocks();
    }
    
    [Fact(DisplayName = "[Impedir] - Deve retornar sucesso ao impedir solicitação com status pendente")]
    [Trait("Business", "Troca De Status Solicitacao")]
    public async Task ImpedirSolicitacao_DeveRetornarSucesso_ImpedirSolicitacaoPendente()
    {
        // Arrange
        
        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();
        
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoPendente(destino: new ObjetoDeManobra("NE", "COSR-NE"));
        
        ImpedirSolicitacao_DeveRetornarSucesso_MockSetup(solicitacao);
        
        var command = new ImpedirCommand(solicitacao.Id, "Teste de unidade - Impedir solicitacao pendente com sucesso");
        
        // Act

        var response = await mediator
            .EnviarComandoAsync<ImpedirCommand, SolicitacaoImpedidaResultDto>(command, CancellationToken.None);

        // Assert
        
        solicitacao.Id.ShouldBe(response.SolicitacaoId);
        
        ImpedirSolicitacao_DeveRetornarSucesso_ValidarCommandResponse(response);
        ImpedirSolicitacao_DeveRetornarSucesso_ValidarStatusSolicitacao(solicitacao);
        await ImpedirSolicitacao_DeveRetornarSucesso_ValidarEventosERepository(_dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoRepository>());

    }
    
    [Fact(DisplayName = "[Impedir] - Deve retornar sucesso ao impedir solicitação com status confirmada")]
    [Trait("Business", "Troca De Status Solicitacao")]
    public async Task ImpedirSolicitacao_DeveRetornarSucesso_ImpedirSolicitacaoConfirmada()
    {
        // Arrange
        
        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();
        
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoConfirmada(destino: new ObjetoDeManobra("NE", "COSR-NE"));
        
        ImpedirSolicitacao_DeveRetornarSucesso_MockSetup(solicitacao);
        
        var command = new ImpedirCommand(solicitacao.Id, "Teste de unidade - Impedir solicitacao pendente com sucesso");
        
        // Act

        var response = await mediator
            .EnviarComandoAsync<ImpedirCommand, SolicitacaoImpedidaResultDto>(command, CancellationToken.None);

        // Assert
        
        solicitacao.Id.ShouldBe(response.SolicitacaoId);
        
        ImpedirSolicitacao_DeveRetornarSucesso_ValidarCommandResponse(response);
        ImpedirSolicitacao_DeveRetornarSucesso_ValidarStatusSolicitacao(solicitacao);
        await ImpedirSolicitacao_DeveRetornarSucesso_ValidarEventosERepository(_dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoRepository>());

    }
    
    [Fact(DisplayName = "[Impedir] - Deve retornar um erro ao impedir e enviar ao firebase")]
    [Trait("Business", "Troca De Status Solicitacao")]
    public async Task ImpedirSolicitacao_DeveRetornarUmErro_EnviarAoFirebase()
    {
        // Arrange
        
        var mediatorMock = _dependencyInjectorFactory.Mocker.GetMock<IMediatorHandler>();

        const string mensagemDeErro = "Erro ao enviar ao firebase";
        var result = new ValidationResult();
        result.AdicionarErro(mensagemDeErro);
        
        mediatorMock
            .Setup(x => x.EnviarComandoAsync(It.IsAny<ImpedirSolicitacaoNoFirebaseCommand>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(result);
        
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoPendente(destino: new ObjetoDeManobra("NE", "COSR-NE"));
        
        ImpedirSolicitacao_DeveRetornarSucesso_MockSetup(solicitacao);
        
        var command = new ImpedirCommand(solicitacao.Id, "Teste de unidade - Erro no firebase");
        
        // Act
        
        var response = await mediatorMock.Object
            .EnviarComandoAsync<ImpedirCommand, SolicitacaoImpedidaResultDto>(command, CancellationToken.None);
        
        // Assert
        
        Assert.NotNull(response);
        
        solicitacao.Id.ShouldBe(response.SolicitacaoId);
        
        response.StatusId.ShouldBe((short)StatusDeSolicitacao.Erro);
        
        response.Erros.ShouldNotBeEmpty();
        response.Erros.Any(x => x == mensagemDeErro).ShouldBeTrue();
        response.ValidationResult.IsValid.ShouldBeFalse();
        response.ValidationResult.Errors.Any(x => x.ErrorMessage == mensagemDeErro).ShouldBeTrue();
        
        var solicitacaoImpedidaEventDisparado = await _dependencyInjectorFactory.Harness.Published
            .Any<SolicitacaoImpedidaEvent>();
        
        solicitacaoImpedidaEventDisparado.ShouldBeFalse();
    }
    
    [Fact(DisplayName = "[Impedir] - Deve retornar um erro ao impedir solicitação status incorreto")]
    [Trait("Business", "Troca De Status Solicitacao")]
    public async Task ImpedirSolicitacao_DeveRetornarUmErro_StatusIncorreto()
    {
        // Arrange
        
        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();
        
        var repositoryMock = _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoRepository>();
        
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoCancelada();
        
        repositoryMock.Setup(x => x.GetOneAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(solicitacao);
        
        var command = new ImpedirCommand(solicitacao.Id, "Teste de unidade - status incorreto");
        
        // Act
        
        var response = await mediator
            .EnviarComandoAsync<ImpedirCommand, SolicitacaoImpedidaResultDto>(command, CancellationToken.None);
        
        // Assert
        
        Assert.NotNull(response);
        
        response.SolicitacaoId.ShouldBe(solicitacao.Id);
        response.StatusId.ShouldBe((short)StatusDeSolicitacao.Erro);
        
        response.Erros.ShouldNotBeEmpty();
        
        response.ValidationResult.IsValid.ShouldBeFalse();
        
        var solicitacaoEnviadaAoFirebase = await _dependencyInjectorFactory.Harness.Published
            .Any<ImpedirSolicitacaoNoFirebaseCommand>();

        var solicitacaoImpedidaEventDisparado = await _dependencyInjectorFactory.Harness.Published
            .Any<SolicitacaoImpedidaEvent>();
        
        solicitacaoEnviadaAoFirebase.ShouldBeFalse();
        solicitacaoImpedidaEventDisparado.ShouldBeFalse();
    }
    
    [Fact(DisplayName = "[Impedir] - Deve retornar um erro ao impedir solicitação usuário não é o destinatário")]
    [Trait("Business", "Troca De Status Solicitacao")]
    public async Task ImpedirSolicitacao_DeveRetornarUmErro_UsuarioNaoEhDestinatario()
    {
        // Arrange
        
        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();
        
        var repositoryMock = _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoRepository>();
        
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoPendente(destino: new ObjetoDeManobra("NE", "CORS-NE"));
        
        repositoryMock.Setup(x => x.GetOneAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(solicitacao);
        
        var command = new ImpedirCommand(solicitacao.Id, "Teste de unidade - Usuário não é destinatário");
        
        // Act
        
        // Middleware de usuário UserDefinitionMiddleware por padrão seta um usuário anônimo.
        var response = await mediator
            .EnviarComandoAsync<ImpedirCommand, SolicitacaoImpedidaResultDto>(command, CancellationToken.None);

        // Assert
        
        Assert.NotNull(response);
        
        response.SolicitacaoId.ShouldBe(solicitacao.Id);
        response.StatusId.ShouldBe((short)StatusDeSolicitacao.Erro);
        
        response.Erros.ShouldNotBeEmpty();
        
        response.ValidationResult.IsValid.ShouldBeFalse();
        
        response.Erros.Any(e => e.Contains("não é destinatário", StringComparison.InvariantCultureIgnoreCase)).ShouldBeTrue();
        
        var solicitacaoEnviadaAoFirebase = await _dependencyInjectorFactory.Harness.Published
            .Any<ImpedirSolicitacaoNoFirebaseCommand>();

        var solicitacaoImpedidaEventDisparado = await _dependencyInjectorFactory.Harness.Published
            .Any<SolicitacaoImpedidaEvent>();
        
        solicitacaoEnviadaAoFirebase.ShouldBeFalse();
        solicitacaoImpedidaEventDisparado.ShouldBeFalse();
    }
    
    [Fact(DisplayName = "[Impedir] - Deve retornar um erro ao impedir Codigo Invalido")]
    [Trait("Business", "Troca De Status Solicitacao")]
    public async Task ImpedirSolicitacao_DeveRetornarUmErro_CodigoNaoPreenchido()
    {
        // Arrange

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();
        
        var command = new ImpedirCommand(string.Empty, "Teste de unidade - Codigo Invalido");
        
        // Act
        
        var response = await mediator
            .EnviarComandoAsync<ImpedirCommand, SolicitacaoImpedidaResultDto>(command, CancellationToken.None);
        
        // Assert
        
        await ValidarCamposImpedirSolicitacaoCommand(response, x => x.Codigo);
    }

    [Fact(DisplayName = "[Impedir] - Deve retornar um erro ao impedir Usuario Invalido")]
    [Trait("Business", "Troca De Status Solicitacao")]
    public async Task ImpedirSolicitacao_DeveRetornarUmErro_UsuarioNaoPreenchido()
    {
        // Arrange
        
        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();
        _dependencyInjectorFactory.Mocker.GetMock<IHttpContextAccessor>()
            .SetupGet(x => x.HttpContext).Returns(() => null);
        
        var command = new ImpedirCommand("confirmar-solicitacao-erro-sem-usuario", "Teste de unidade - Usuario Invalido");
        
        // Act
        
        var response = await mediator
            .EnviarComandoAsync<ImpedirCommand, SolicitacaoImpedidaResultDto>(command, CancellationToken.None);
        
        // Assert

        await ValidarCamposImpedirSolicitacaoCommand(response, x => x.Usuario);
    }

    [Fact(DisplayName = "[Impedir] - Deve retornar um erro ao impedir Motivo Invalido")]
    [Trait("Business", "Troca De Status Solicitacao")]
    public async Task ImpedirSolicitacao_DeveRetornarUmErro_MotivoNaoPreenchido()
    {
        // Arrange
        
        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();
        _dependencyInjectorFactory.Mocker.GetMock<IHttpContextAccessor>()
            .SetupGet(x => x.HttpContext).Returns(() => null);
        
        var command = new ImpedirCommand("confirmar-solicitacao-erro-sem-usuario", string.Empty);
        
        // Act
        
        var response = await mediator
            .EnviarComandoAsync<ImpedirCommand, SolicitacaoImpedidaResultDto>(command, CancellationToken.None);
        
        // Assert

        await ValidarCamposImpedirSolicitacaoCommand(response, x => x.Motivo);
    }
    
    [Fact(DisplayName = "[Impedir] - Deve retornar um erro ao impedir Solicitação não existe")]
    [Trait("Business", "Troca De Status Solicitacao")]
    public async Task ImpedirSolicitacao_DeveRetornarUmErro_SolicitacaoNaoExiste()
    {
        // Arrange

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        var repositoryMock = _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoRepository>();
        
        repositoryMock.Setup(x => x.GetOneAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(null as Entities.Entities.Solicitacao);
        
        var command = new ImpedirCommand("confirmar-solicitacao-nao-existe", "Teste de unidade - Solicitação não existe");
        
        // Act
        
        var response = await mediator.EnviarComandoAsync<ImpedirCommand, SolicitacaoImpedidaResultDto>(command, CancellationToken.None);

        // Assert
        
        Assert.NotNull(response);
        
        response.StatusId.ShouldBe((short)StatusDeSolicitacao.Erro);
        
        response.Erros.ShouldNotBeEmpty();

        response.ValidationResult.IsValid.ShouldBeFalse();
        
        response.Erros.Any(e => e.Contains("não existe", StringComparison.InvariantCultureIgnoreCase)).ShouldBeTrue();
        
        repositoryMock.Verify(x => x.GetOneAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()), Times.Once);
        
        var solicitacaoEnviadaAoFirebase = await _dependencyInjectorFactory.Harness.Published
            .Any<ImpedirSolicitacaoNoFirebaseCommand>();

        var solicitacaoImpedidaEventDisparado = await _dependencyInjectorFactory.Harness.Published
            .Any<SolicitacaoImpedidaEvent>();
        
        solicitacaoEnviadaAoFirebase.ShouldBeFalse();
        solicitacaoImpedidaEventDisparado.ShouldBeFalse();     
    }
    
    private async Task ValidarCamposImpedirSolicitacaoCommand<TProperty>(SolicitacaoImpedidaResultDto response, Expression<Func<ImpedirCommand, TProperty>> campo)
    {
        Assert.NotNull(response);
        
        response.StatusId.ShouldBe((short)StatusDeSolicitacao.Erro);
        
        response.Erros.ShouldNotBeEmpty();
        
        var testValidationResult = new TestValidationResult<ImpedirCommand>(response.ValidationResult);
        testValidationResult.ShouldHaveValidationErrorFor(campo);
        
        var solicitacaoEnviadaAoFirebase = await _dependencyInjectorFactory.Harness.Published
            .Any<ImpedirSolicitacaoNoFirebaseCommand>();

        var solicitacaoConfirmadaEventDisparado = await _dependencyInjectorFactory.Harness.Published
            .Any<SolicitacaoImpedidaEvent>();
        
        solicitacaoEnviadaAoFirebase.ShouldBeFalse();
        solicitacaoConfirmadaEventDisparado.ShouldBeFalse();      
    }
    
    private void ImpedirSolicitacao_DeveRetornarSucesso_MockSetup(Entities.Entities.Solicitacao solicitacao)
    {
        _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoRepository>()
            .Setup(x => x.GetOneAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(solicitacao);
        
        var perfil = new Perfil([new Scope("CENTROS", "NE", "COSR-NE")], [], "COSR-NE", "NE");
        
        _dependencyInjectorFactory.Mocker.GetMock<IUserContext>()
            .SetupGet(x => x.Perfil).Returns(perfil);
    }
    
    private static void ImpedirSolicitacao_DeveRetornarSucesso_ValidarStatusSolicitacao(
        Entities.Entities.Solicitacao solicitacao)
    {
        solicitacao.Status.ShouldBe(StatusDeSolicitacao.Impedida);
        solicitacao.Chat.Any(x => x.Status == StatusDeSolicitacao.Impedida).ShouldBeTrue();
        solicitacao.HistoricosDeStatus.Any(x => x.Status == StatusDeSolicitacao.Impedida).ShouldBeTrue();
    }
    
    private static void ImpedirSolicitacao_DeveRetornarSucesso_ValidarCommandResponse(SolicitacaoImpedidaResultDto response)
    {
        Assert.NotNull(response);
        
        response.Erros.ShouldBeEmpty();
        response.ValidationResult.IsValid.ShouldBeTrue();
        response.Status.ShouldBe(StatusDeSolicitacao.Impedida.GetDescription());
        response.StatusId.ShouldBe((short)StatusDeSolicitacao.Impedida);
    }
    
    private async Task ImpedirSolicitacao_DeveRetornarSucesso_ValidarEventosERepository(Mock<ISolicitacaoRepository> repositoryMock)
    {
        var solicitacaoEnviadaAoFirebase = await _dependencyInjectorFactory.Harness.Published
            .Any<ImpedirSolicitacaoNoFirebaseCommand>();

        var solicitacaoConfirmadaEventDisparado = await _dependencyInjectorFactory.Harness.Published
            .Any<SolicitacaoImpedidaEvent>();
        
        solicitacaoEnviadaAoFirebase.ShouldBeTrue();
        solicitacaoConfirmadaEventDisparado.ShouldBeTrue();
        
        solicitacaoEnviadaAoFirebase = await _dependencyInjectorFactory.Harness.Consumed
            .Any<ImpedirSolicitacaoNoFirebaseCommand>();

        solicitacaoConfirmadaEventDisparado = await _dependencyInjectorFactory.Harness.Consumed
            .Any<SolicitacaoImpedidaEvent>();
        
        solicitacaoEnviadaAoFirebase.ShouldBeTrue();
        solicitacaoConfirmadaEventDisparado.ShouldBeTrue();
        
        repositoryMock.Verify(x => x.AtualizarAsync(It.IsAny<Entities.Entities.Solicitacao>(), It.IsAny<CancellationToken>()), Times.Once);
    }
    
}