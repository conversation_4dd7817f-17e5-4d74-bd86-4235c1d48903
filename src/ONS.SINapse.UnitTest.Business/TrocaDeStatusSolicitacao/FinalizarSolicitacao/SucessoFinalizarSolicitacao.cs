using Microsoft.Extensions.DependencyInjection;
using ONS.SINapse.Entities.Entities;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using ONS.SINapse.Solicitacao.Dtos.Integracao;
using ONS.SINapse.Solicitacao.EventHandlers.Events;
using ONS.SINapse.UnitTest.Shared.Fixtures;
using ONS.SINapse.Repository.IRepository.Firebase;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands.Firebase;

namespace ONS.SINapse.UnitTest.Business.TrocaDeStatusSolicitacao.FinalizarSolicitacao;

public partial class FinalizarSolicitacaoTest
{
    [Fact(DisplayName = "[Finalizar] - Deve finalizar solicitacoes em lote")]
    [Trait("Business", "Troca De Status Solicitacao")]
    public async Task DeveFinalizarSolicitacoesEmLote()
    {
        // Arrange
        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();
        
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoConfirmada(destino: new ObjetoDeManobra("NE", "COSR-NE"));
        
        var usuario = UsuarioFixture.GerarUsuarioDtoValido();
        var destino = solicitacao.Destino.Codigo;

        _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoFirebaseRepository>()
            .Setup(repository => repository.GetByIdAsync(It.IsAny<string[]>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([solicitacao]);

        ConfigurarMockUserContext(usuario, destino);
    
        var command = new FinalizarEmLoteCommand([
            new StatusDeSolicitacaoIntegracaoRecebimentoDto(
                solicitacao.Id,
                StatusDeSolicitacao.Finalizada,
                null,
                new ObjetoDeManobraDto(destino, destino),
                usuario,
                "SINapse"
            )
        ]);
    
        // Act
        var response = 
            await mediator.EnviarComandoAsync<FinalizarEmLoteCommand, StatusDeSolicitacaoIntegracaoEnvioEmLoteDto>(command, CancellationToken.None);
    
        // Assert
        Assert.NotNull(response);

        response.Solicitacoes.ShouldNotBeEmpty();
    
        var solicitacoesComErro = response.Solicitacoes
            .Where(x => x.Status == StatusDeSolicitacao.Erro)
            .ToList();
        
        solicitacoesComErro.ShouldBeEmpty();
    
        solicitacao.FinalizadaAutomaticamente.ShouldBeFalse();

        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoFirebaseRepository>().Verify(
           repository => repository.GetByIdAsync(It.IsAny<string[]>(), It.IsAny<CancellationToken>()),
           Times.Between(1, 2, Moq.Range.Inclusive));

        var commandPublicado = _dependencyInjectorFactory.Harness.Published.Any<TrocarDeStatusNoFirebaseCommand>();

        var eventoPublicado = _dependencyInjectorFactory.Harness.Published.Any<SolicitacaoConcluidaEvent>();
        var eventoConsumido = _dependencyInjectorFactory.Harness.Consumed.Any<SolicitacaoConcluidaEvent>();
        
        var kafkaPublicado = _dependencyInjectorFactory.Harness.Published.Any<StatusDeSolicitacaoIntegracaoRecebidaEvent>();
        var kafkaConsumido = _dependencyInjectorFactory.Harness.Consumed.Any<StatusDeSolicitacaoIntegracaoRecebidaEvent>();

        await Task.WhenAll(commandPublicado, eventoPublicado, eventoConsumido, kafkaPublicado, kafkaConsumido);

        commandPublicado.Result.ShouldBeTrue();
        eventoPublicado.Result.ShouldBeTrue();
        eventoConsumido.Result.ShouldBeTrue();
        kafkaPublicado.Result.ShouldBeTrue();
        kafkaConsumido.Result.ShouldBeTrue();
    }
    
    [Fact(DisplayName = "[Finalizar] - Deve finalizar solicitacoes em lote automaticamente")]
    [Trait("Business", "Troca De Status Solicitacao")]
    public async Task DeveFinalizarSolicitacoesEmLoteAutomaticamente()
    {
        // Arrange
        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();
        
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoConfirmada(destino: new ObjetoDeManobra("NE", "COSR-NE"));
        
        var usuario = UsuarioFixture.GerarUsuarioDtoValido();
        var destino = solicitacao.Destino.Codigo;

        _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoFirebaseRepository>()
            .Setup(repository => repository.GetByIdAsync(It.IsAny<string[]>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([solicitacao]);

        ConfigurarMockUserContext(usuario, destino);
    
        var command = new FinalizarEmLoteCommand([
            new StatusDeSolicitacaoIntegracaoRecebimentoDto(
                solicitacao.Id,
                StatusDeSolicitacao.Finalizada,
                null,
                new ObjetoDeManobraDto(destino, destino),
                usuario,
                "SINapse"
            )
        ]);
        command.FinalizadoProcessoAutomatico();
    
        // Act
        var response = 
            await mediator.EnviarComandoAsync<FinalizarEmLoteCommand, StatusDeSolicitacaoIntegracaoEnvioEmLoteDto>(command, CancellationToken.None);
    
        // Assert
        Assert.NotNull(response);

        response.Solicitacoes.ShouldNotBeEmpty();
    
        var solicitacoesComErro = response.Solicitacoes
            .Where(x => x.Status == StatusDeSolicitacao.Erro)
            .ToList();
        
        solicitacoesComErro.ShouldBeEmpty();
    
        solicitacao.FinalizadaAutomaticamente.ShouldBeTrue();

        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoFirebaseRepository>().Verify(
           repository => repository.GetByIdAsync(It.IsAny<string[]>(), It.IsAny<CancellationToken>()),
           Times.Between(1, 2, Moq.Range.Inclusive));

        var commandPublicado = _dependencyInjectorFactory.Harness.Published.Any<TrocarDeStatusNoFirebaseCommand>();

        var eventoPublicado = _dependencyInjectorFactory.Harness.Published.Any<SolicitacaoConcluidaEvent>();
        var eventoConsumido = _dependencyInjectorFactory.Harness.Consumed.Any<SolicitacaoConcluidaEvent>();

        var kafkaPublicado = _dependencyInjectorFactory.Harness.Published.Any<StatusDeSolicitacaoIntegracaoRecebidaEvent>();
        var kafkaConsumido = _dependencyInjectorFactory.Harness.Consumed.Any<StatusDeSolicitacaoIntegracaoRecebidaEvent>();

        await Task.WhenAll(commandPublicado, eventoPublicado, eventoConsumido, kafkaPublicado, kafkaConsumido);

        commandPublicado.Result.ShouldBeTrue();
        eventoPublicado.Result.ShouldBeTrue();
        eventoConsumido.Result.ShouldBeTrue();
        kafkaPublicado.Result.ShouldBeTrue();
        kafkaConsumido.Result.ShouldBeTrue();
    }
}