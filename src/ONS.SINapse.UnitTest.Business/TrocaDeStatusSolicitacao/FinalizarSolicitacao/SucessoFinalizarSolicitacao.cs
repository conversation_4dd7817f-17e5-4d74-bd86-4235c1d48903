using System.Linq.Expressions;
using Microsoft.Extensions.DependencyInjection;
using ONS.SINapse.Entities.Entities;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Repository.IRepository;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using ONS.SINapse.Solicitacao.Dtos.Integracao;
using ONS.SINapse.Solicitacao.EventHandlers.Events;
using ONS.SINapse.UnitTest.Shared.Fixtures;

namespace ONS.SINapse.UnitTest.Business.TrocaDeStatusSolicitacao.FinalizarSolicitacao;

public partial class FinalizarSolicitacaoTest
{
    [Fact(DisplayName = "[Finalizar] - Deve finalizar solicitacoes em lote")]
    [Trait("Business", "Troca De Status Solicitacao")]
    public async Task DeveFinalizarSolicitacoesEmLote()
    {
        // Arrange
        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();
        
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoConfirmada(destino: new ObjetoDeManobra("NE", "COSR-NE"));
        
        var usuario = UsuarioFixture.GerarUsuarioDtoValido();
        var destino = solicitacao.Destino.Codigo;
        
        _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoRepository>()
            .Setup(x => x.GetAsync(It.IsAny<Expression<Func<Entities.Entities.Solicitacao, bool>>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([solicitacao]);
        
        ConfigurarMockUserContext(usuario, destino);
    
        var command = new FinalizarEmLoteCommand([
            new StatusDeSolicitacaoIntegracaoRecebimentoDto(
                solicitacao.Id,
                StatusDeSolicitacao.Finalizada,
                null,
                new ObjetoDeManobraDto(destino, destino),
                usuario,
                "SINapse"
            )
        ]);
    
        // Act
        var response = 
            await mediator.EnviarComandoAsync<FinalizarEmLoteCommand, StatusDeSolicitacaoIntegracaoEnvioEmLoteDto>(command, CancellationToken.None);
    
        // Assert
        Assert.NotNull(response);

        response.Solicitacoes.ShouldNotBeEmpty();
    
        var solicitacoesComErro = response.Solicitacoes
            .Where(x => x.Status == StatusDeSolicitacao.Erro)
            .ToList();
        
        solicitacoesComErro.ShouldBeEmpty();
    
        solicitacao.FinalizadaAutomaticamente.ShouldBeFalse();
        
        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoRepository>().Verify(
            repository => repository.GetAsync(It.IsAny<Expression<Func<Entities.Entities.Solicitacao, bool>>>(), It.IsAny<CancellationToken>()),
            Times.Once);
    
        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoRepository>().Verify(
            repository => repository.BulkUpdateAsync(It.IsAny<List<Entities.Entities.Solicitacao>>(), It.IsAny<CancellationToken>()),
            Times.Once);
            
        var eventoPublicado = _dependencyInjectorFactory.Harness.Published.Any<SolicitacaoFinalizadaEmLoteEvent>();
        var eventoConsumido = _dependencyInjectorFactory.Harness.Consumed.Any<SolicitacaoFinalizadaEmLoteEvent>();
        
        var kafkaPublicado = _dependencyInjectorFactory.Harness.Published.Any<StatusDeSolicitacaoIntegracaoRecebidaEvent>();
        var kafkaConsumido = _dependencyInjectorFactory.Harness.Consumed.Any<StatusDeSolicitacaoIntegracaoRecebidaEvent>();

        await Task.WhenAll(eventoPublicado, eventoConsumido, kafkaPublicado, kafkaConsumido);
        
        eventoPublicado.Result.ShouldBeTrue();
        eventoConsumido.Result.ShouldBeTrue();
        kafkaPublicado.Result.ShouldBeTrue();
        kafkaConsumido.Result.ShouldBeTrue();
    }
    
    [Fact(DisplayName = "[Finalizar] - Deve finalizar solicitacoes em lote automaticamente")]
    [Trait("Business", "Troca De Status Solicitacao")]
    public async Task DeveFinalizarSolicitacoesEmLoteAutomaticamente()
    {
        // Arrange
        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();
        
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoConfirmada(destino: new ObjetoDeManobra("NE", "COSR-NE"));
        
        var usuario = UsuarioFixture.GerarUsuarioDtoValido();
        var destino = solicitacao.Destino.Codigo;
        
        _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoRepository>()
            .Setup(x => x.GetAsync(It.IsAny<Expression<Func<Entities.Entities.Solicitacao, bool>>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([solicitacao]);
        
        ConfigurarMockUserContext(usuario, destino);
    
        var command = new FinalizarEmLoteCommand([
            new StatusDeSolicitacaoIntegracaoRecebimentoDto(
                solicitacao.Id,
                StatusDeSolicitacao.Finalizada,
                null,
                new ObjetoDeManobraDto(destino, destino),
                usuario,
                "SINapse"
            )
        ]);
        command.FinalizadoProcessoAutomatico();
    
        // Act
        var response = 
            await mediator.EnviarComandoAsync<FinalizarEmLoteCommand, StatusDeSolicitacaoIntegracaoEnvioEmLoteDto>(command, CancellationToken.None);
    
        // Assert
        Assert.NotNull(response);

        response.Solicitacoes.ShouldNotBeEmpty();
    
        var solicitacoesComErro = response.Solicitacoes
            .Where(x => x.Status == StatusDeSolicitacao.Erro)
            .ToList();
        
        solicitacoesComErro.ShouldBeEmpty();
    
        solicitacao.FinalizadaAutomaticamente.ShouldBeTrue();
        
        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoRepository>().Verify(
            repository => repository.GetAsync(It.IsAny<Expression<Func<Entities.Entities.Solicitacao, bool>>>(), It.IsAny<CancellationToken>()),
            Times.Once);
    
        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoRepository>().Verify(
            repository => repository.BulkUpdateAsync(It.IsAny<List<Entities.Entities.Solicitacao>>(), It.IsAny<CancellationToken>()),
            Times.Once);
            
        var eventoPublicado = _dependencyInjectorFactory.Harness.Published.Any<SolicitacaoFinalizadaEmLoteEvent>();
        var eventoConsumido = _dependencyInjectorFactory.Harness.Consumed.Any<SolicitacaoFinalizadaEmLoteEvent>();
        
        var kafkaPublicado = _dependencyInjectorFactory.Harness.Published.Any<StatusDeSolicitacaoIntegracaoRecebidaEvent>();
        var kafkaConsumido = _dependencyInjectorFactory.Harness.Consumed.Any<StatusDeSolicitacaoIntegracaoRecebidaEvent>();

        await Task.WhenAll(eventoPublicado, eventoConsumido, kafkaPublicado, kafkaConsumido);
        
        eventoPublicado.Result.ShouldBeTrue();
        eventoConsumido.Result.ShouldBeTrue();
        kafkaPublicado.Result.ShouldBeTrue();
        kafkaConsumido.Result.ShouldBeTrue();
    }
}