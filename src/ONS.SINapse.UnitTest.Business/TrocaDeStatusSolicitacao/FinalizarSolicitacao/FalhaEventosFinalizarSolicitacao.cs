using System.Linq.Expressions;
using MassTransit;
using ONS.SINapse.Entities.Entities;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Repository.IRepository.InMemory;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using ONS.SINapse.Solicitacao.Dtos.Integracao;
using ONS.SINapse.Solicitacao.EventHandlers.Events;
using ONS.SINapse.UnitTest.Shared.Fixtures;

namespace ONS.SINapse.UnitTest.Business.TrocaDeStatusSolicitacao.FinalizarSolicitacao;

public partial class FinalizarSolicitacaoTest
{
    [Fact(DisplayName = "[Finalizar] - Deve retornar um erro no evento de solicitação finalizada")]
    [Trait("Business", "Troca De Status Solicitacao")]
    public async Task FinalizarSolicitacao_DeveRetornarUmErro_SolicitacaoFinalizadaEmLoteEvent()
    {
        // Arrange
        
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoConfirmada(destino: new ObjetoDeManobra("NE", "COSR-NE"));
        
        var usuario = UsuarioFixture.GerarUsuarioDtoValido();
        var destino = solicitacao.Destino.Codigo;

        _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoMemoryRepository>()
            .Setup(repository => repository.GetList(It.IsAny<Expression<Func<Entities.Entities.Solicitacao, bool>>>()))
            .Returns([solicitacao]);

        ConfigurarMockUserContext(usuario, destino);
    
        var command = new FinalizarEmLoteCommand([
            new StatusDeSolicitacaoIntegracaoRecebimentoDto(
                solicitacao.Id,
                StatusDeSolicitacao.Finalizada,
                null,
                new ObjetoDeManobraDto(destino, destino),
                usuario,
                "SINapse"
            )
        ]);
    
        var mediatorMock = _dependencyInjectorFactory.Mocker.GetMock<IMediatorHandler>();
        mediatorMock
            .Setup(mediator => mediator.PublicarEventoAsync(It.IsAny<StatusDeSolicitacaoIntegracaoRecebidaEvent>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new RequestFaultException());
    
        // Act & Assert

        await mediatorMock.Object
            .EnviarComandoAsync<FinalizarEmLoteCommand, StatusDeSolicitacaoIntegracaoEnvioEmLoteDto>(
                command, CancellationToken.None)
            .ShouldThrowAsync<RequestFaultException>();

        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoMemoryRepository>().Verify(
            repository => repository.GetList(It.IsAny<Expression<Func<Entities.Entities.Solicitacao, bool>>>()),
            Times.Exactly(2));
    
        var eventoPublicado = _dependencyInjectorFactory.Harness.Published.Any<SolicitacaoConcluidaEvent>();
        var eventoConsumido = _dependencyInjectorFactory.Harness.Consumed.Any<SolicitacaoConcluidaEvent>();
        
        var kafkaPublicado = _dependencyInjectorFactory.Harness.Published.Any<StatusDeSolicitacaoIntegracaoRecebidaEvent>();
        var kafkaConsumido = _dependencyInjectorFactory.Harness.Consumed.Any<StatusDeSolicitacaoIntegracaoRecebidaEvent>();

        var commandPublicado = _dependencyInjectorFactory.Harness.Published.Any<EnviarTrocaDeStatusAoFirebaseCommand>();
        var commandConsumido = _dependencyInjectorFactory.Harness.Consumed.Any<EnviarTrocaDeStatusAoFirebaseCommand>();

        await Task.WhenAll(eventoPublicado, eventoConsumido, kafkaPublicado, kafkaConsumido, commandPublicado, commandConsumido);
        
        eventoPublicado.Result.ShouldBeTrue();
        eventoConsumido.Result.ShouldBeTrue();
        kafkaPublicado.Result.ShouldBeFalse();
        kafkaConsumido.Result.ShouldBeFalse();
        commandPublicado.Result.ShouldBeTrue();
        commandConsumido.Result.ShouldBeTrue();
    }
}