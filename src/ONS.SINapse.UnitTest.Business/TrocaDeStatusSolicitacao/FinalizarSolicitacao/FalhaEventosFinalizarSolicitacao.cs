using System.Linq.Expressions;
using MassTransit;
using ONS.SINapse.Entities.Entities;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Repository.IRepository;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using ONS.SINapse.Solicitacao.Dtos.Integracao;
using ONS.SINapse.Solicitacao.EventHandlers.Events;
using ONS.SINapse.UnitTest.Shared.Fixtures;

namespace ONS.SINapse.UnitTest.Business.TrocaDeStatusSolicitacao.FinalizarSolicitacao;

public partial class FinalizarSolicitacaoTest
{
    [Fact(DisplayName = "[Finalizar] - Deve retornar um erro no evento de solicitação finalizada")]
    [Trait("Business", "Troca De Status Solicitacao")]
    public async Task FinalizarSolicitacao_DeveRetornarUmErro_SolicitacaoFinalizadaEmLoteEvent()
    {
        // Arrange
        
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoConfirmada(destino: new ObjetoDeManobra("NE", "COSR-NE"));
        
        var usuario = UsuarioFixture.GerarUsuarioDtoValido();
        var destino = solicitacao.Destino.Codigo;
        
        _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoRepository>()
            .Setup(x => x.GetAsync(It.IsAny<Expression<Func<Entities.Entities.Solicitacao, bool>>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([solicitacao]);
        
        ConfigurarMockUserContext(usuario, destino);
    
        var command = new FinalizarEmLoteCommand([
            new StatusDeSolicitacaoIntegracaoRecebimentoDto(
                solicitacao.Id,
                StatusDeSolicitacao.Finalizada,
                null,
                new ObjetoDeManobraDto(destino, destino),
                usuario,
                "SINapse"
            )
        ]);
    
        var mediatorMock = _dependencyInjectorFactory.Mocker.GetMock<IMediatorHandler>();
        mediatorMock
            .Setup(mediator => mediator.PublicarEventoAsync(It.IsAny<SolicitacaoFinalizadaEmLoteEvent>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new RequestFaultException());
    
        // Act & Assert

        await mediatorMock.Object
            .EnviarComandoAsync<FinalizarEmLoteCommand, StatusDeSolicitacaoIntegracaoEnvioEmLoteDto>(
                command, CancellationToken.None)
            .ShouldThrowAsync<RequestFaultException>();
    
        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoRepository>().Verify(
            repository => repository.GetAsync(It.IsAny<Expression<Func<Entities.Entities.Solicitacao, bool>>>(), It.IsAny<CancellationToken>()),
            Times.Once);
    
        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoRepository>().Verify(
            repository => repository.BulkUpdateAsync(It.IsAny<List<Entities.Entities.Solicitacao>>(), It.IsAny<CancellationToken>()),
            Times.Once);
    
        var eventoPublicado = _dependencyInjectorFactory.Harness.Published.Any<SolicitacaoFinalizadaEmLoteEvent>();
        var eventoConsumido = _dependencyInjectorFactory.Harness.Consumed.Any<SolicitacaoFinalizadaEmLoteEvent>();
        
        var kafkaPublicado = _dependencyInjectorFactory.Harness.Published.Any<StatusDeSolicitacaoIntegracaoRecebidaEvent>();
        var kafkaConsumido = _dependencyInjectorFactory.Harness.Consumed.Any<StatusDeSolicitacaoIntegracaoRecebidaEvent>();

        await Task.WhenAll(eventoPublicado, eventoConsumido, kafkaPublicado, kafkaConsumido);
        
        eventoPublicado.Result.ShouldBeFalse();
        eventoConsumido.Result.ShouldBeFalse();
        kafkaPublicado.Result.ShouldBeTrue();
        kafkaConsumido.Result.ShouldBeTrue();
    }
}