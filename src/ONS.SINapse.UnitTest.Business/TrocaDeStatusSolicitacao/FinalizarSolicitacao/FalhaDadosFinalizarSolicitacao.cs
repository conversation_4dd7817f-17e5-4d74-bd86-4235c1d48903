using System.Linq.Expressions;
using MassTransit;
using Microsoft.Extensions.DependencyInjection;
using ONS.SINapse.Entities.Entities;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Repository.IRepository;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using ONS.SINapse.Solicitacao.Dtos.Integracao;
using ONS.SINapse.Solicitacao.EventHandlers.Events;
using ONS.SINapse.UnitTest.Shared.Fixtures;

namespace ONS.SINapse.UnitTest.Business.TrocaDeStatusSolicitacao.FinalizarSolicitacao;

public partial class FinalizarSolicitacaoTest
{
    [Fact(DisplayName = "[Finalizar] - Deve retornar erro quando solicitação não existir")]
    [Trait("Business", "Troca De Status Solicitacao")]
    public async Task FinalizarEmLote_DeveRetornarErro_SolicitacaoNaoExistente()
    {
        // Arrange
        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();
        
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoConfirmada(destino: new ObjetoDeManobra("NE", "COSR-NE"));
        
        var usuario = UsuarioFixture.GerarUsuarioDtoValido();
        var destino = solicitacao.Destino.Codigo;
        
        _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoRepository>()
            .Setup(x => x.GetAsync(It.IsAny<Expression<Func<Entities.Entities.Solicitacao, bool>>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);
        
        ConfigurarMockUserContext(usuario, destino);
    
        var command = new FinalizarEmLoteCommand([
            new StatusDeSolicitacaoIntegracaoRecebimentoDto(
                solicitacao.Id,
                StatusDeSolicitacao.Finalizada,
                null,
                new ObjetoDeManobraDto(destino, destino),
                usuario,
                "SINapse"
            )
        ]);
    
        // Act
        var response = 
            await mediator.EnviarComandoAsync<FinalizarEmLoteCommand, StatusDeSolicitacaoIntegracaoEnvioEmLoteDto>(command, CancellationToken.None);
    
        // Assert
        await ValidarFinalizarSolicitacao(response, 
            $"Não foi possível alterar o status da solicitação {solicitacao.Id} para {StatusDeSolicitacao.Finalizada.GetDescription()}. Solicitação não existe.");
    }
    
    [Fact(DisplayName = "[Finalizar] - Deve retornar erro quando status da solicitação for inválido")]
    [Trait("Business", "Troca De Status Solicitacao")]
    public async Task FinalizarEmLote_DeveRetornarErro_StatusInvalido()
    {
        // Arrange
        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();
        
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoPendente(destino: new ObjetoDeManobra("NE", "COSR-NE"));
        
        var usuario = UsuarioFixture.GerarUsuarioDtoValido();
        var destino = solicitacao.Destino.Codigo;
        
        _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoRepository>()
            .Setup(x => x.GetAsync(It.IsAny<Expression<Func<Entities.Entities.Solicitacao, bool>>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([solicitacao]);
        
        ConfigurarMockUserContext(usuario, destino);

    
        var command = new FinalizarEmLoteCommand([
            new StatusDeSolicitacaoIntegracaoRecebimentoDto(
                solicitacao.Id,
                StatusDeSolicitacao.Finalizada,
                null,
                new ObjetoDeManobraDto(destino, destino),
                usuario,
                "SINapse"
            )
        ]);
    
        // Act
        var response = 
            await mediator.EnviarComandoAsync<FinalizarEmLoteCommand, StatusDeSolicitacaoIntegracaoEnvioEmLoteDto>(command, CancellationToken.None);
    
        // Assert
        
        await ValidarFinalizarSolicitacao(response,
            $"Não foi possível alterar o status da solicitação {solicitacao.Id} para {StatusDeSolicitacao.Finalizada.GetDescription()}. Solicitação se encontra com status {solicitacao.Status.GetDescription()}.");
    }
    
    [Fact(DisplayName = "[Finalizar] - Deve retornar erro quando usuário for solicitante da própria solicitação")]
    [Trait("Business", "Troca De Status Solicitacao")]
    public async Task FinalizarEmLote_DeveRetornarErro_SolicitanteInvalido()
    {
        // Arrange
        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();
        
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoPendente();
        
        var usuario = UsuarioFixture.GerarUsuarioDtoValido();
        var origem = solicitacao.Origem.Codigo;
        
        _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoRepository>()
            .Setup(x => x.GetAsync(It.IsAny<Expression<Func<Entities.Entities.Solicitacao, bool>>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([solicitacao]);
        
        ConfigurarMockUserContext(usuario, origem);
    
        var command = new FinalizarEmLoteCommand([
            new StatusDeSolicitacaoIntegracaoRecebimentoDto(
                solicitacao.Id,
                StatusDeSolicitacao.Finalizada,
                null,
                new ObjetoDeManobraDto(origem, origem),
                usuario,
                "SINapse"
            )
        ]);
    
        // Act
        var response = 
            await mediator.EnviarComandoAsync<FinalizarEmLoteCommand, StatusDeSolicitacaoIntegracaoEnvioEmLoteDto>(command, CancellationToken.None);
    
        // Assert
        await ValidarFinalizarSolicitacao(response,
            $"Não foi possível alterar o status da solicitação {solicitacao.Id} para {StatusDeSolicitacao.Finalizada.GetDescription()}. Operação não permitida, usuário é o solicitante.");
    }
    
    [Fact(DisplayName = "[Finalizar] - Deve retornar erro quando usuário for atualizar os dados das solicitações")]
    [Trait("Business", "Troca De Status Solicitacao")]
    public async Task FinalizarEmLote_DeveRetornarErro_DadosInvalidosParaUpdate()
    {
        // Arrange
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoConfirmada();
        var destino = solicitacao.Destino.Codigo;

        var usuario = UsuarioFixture.GerarUsuarioDtoValido();
        
        ConfigurarMockUserContext(usuario, destino);
    
        _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoRepository>()
            .Setup(repository => repository.GetAsync(It.IsAny<Expression<Func<Entities.Entities.Solicitacao, bool>>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([solicitacao]);
    
        _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoRepository>()
            .Setup(repository => repository.BulkUpdateAsync(It.IsAny<List<Entities.Entities.Solicitacao>>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new RequestFaultException());
    
        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();
    
        var command = new FinalizarEmLoteCommand([
            new StatusDeSolicitacaoIntegracaoRecebimentoDto(
                solicitacao.Id,
                StatusDeSolicitacao.Finalizada,
                null,
                new ObjetoDeManobraDto(destino, destino),
                usuario,
                "SINapse"
            )
        ]);
        
        // Act & Assert

        await mediator.EnviarComandoAsync<FinalizarEmLoteCommand, StatusDeSolicitacaoIntegracaoEnvioEmLoteDto>(command,
                CancellationToken.None)
            .ShouldThrowAsync<RequestFaultException>();
        
        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoRepository>().Verify(
            repository => repository.GetAsync(It.IsAny<Expression<Func<Entities.Entities.Solicitacao, bool>>>(), It.IsAny<CancellationToken>()),
            Times.Once);
    
        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoRepository>().Verify(
            repository => repository.BulkUpdateAsync(It.IsAny<List<Entities.Entities.Solicitacao>>(), It.IsAny<CancellationToken>()),
            Times.Once);

        var eventoPublicado = _dependencyInjectorFactory.Harness.Published.Any<SolicitacaoFinalizadaEmLoteEvent>();
        var eventoConsumido = _dependencyInjectorFactory.Harness.Consumed.Any<SolicitacaoFinalizadaEmLoteEvent>();
        
        var kafkaPublicado = _dependencyInjectorFactory.Harness.Published.Any<StatusDeSolicitacaoIntegracaoRecebidaEvent>();
        var kafkaConsumido = _dependencyInjectorFactory.Harness.Consumed.Any<StatusDeSolicitacaoIntegracaoRecebidaEvent>();

        await Task.WhenAll(eventoPublicado, eventoConsumido, kafkaPublicado, kafkaConsumido);
        
        eventoPublicado.Result.ShouldBeFalse();
        eventoConsumido.Result.ShouldBeFalse();
        kafkaPublicado.Result.ShouldBeFalse();
        kafkaConsumido.Result.ShouldBeFalse();
    }
    
    private async Task ValidarFinalizarSolicitacao(StatusDeSolicitacaoIntegracaoEnvioEmLoteDto response, string message) 
    {
        Assert.NotNull(response);

        response.Solicitacoes
            .SelectMany(x => x.Erros)
            .Any(x => x == message)
            .ShouldBeTrue();

        response.Solicitacoes
            .All(x => x.Erros.Length > 0)
            .ShouldBeTrue();

        response.Solicitacoes
            .All(x => x.Status == StatusDeSolicitacao.Erro)
            .ShouldBeTrue();

        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoRepository>().Verify(
            repository => repository.BulkUpdateAsync(It.IsAny<List<Entities.Entities.Solicitacao>>(), It.IsAny<CancellationToken>()),
            Times.Never);
            
        var solicitacaoFinalizadaEventPublished =
            _dependencyInjectorFactory.Harness.Published.Any<SolicitacaoFinalizadaEmLoteEvent>();

        var finalizadaEventKafkaPublished =
            _dependencyInjectorFactory.Harness.Published.Any<StatusDeSolicitacaoIntegracaoRecebidaEvent>();

        await Task.WhenAll(solicitacaoFinalizadaEventPublished, finalizadaEventKafkaPublished);

        solicitacaoFinalizadaEventPublished.Result.ShouldBeFalse();
        finalizadaEventKafkaPublished.Result.ShouldBeTrue();
    }
}