using System.Linq.Expressions;
using MassTransit;
using Microsoft.Extensions.DependencyInjection;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Repository.IRepository;
using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using ONS.SINapse.Solicitacao.EventHandlers.Events;

namespace ONS.SINapse.UnitTest.Business.TrocaDeStatusSolicitacao.FinalizarSolicitacao
{
    public partial class FinalizarSolicitacaoTest
    {
        [Fact(DisplayName = "[Finalizar] - Deve retornar erro quando solicitação não existir")]
        [Trait("Business", "Troca De Status Solicitacao")]
        public async Task FinalizarEmLote_DeveRetornarErro_SolicitacaoNaoExistente()
        {
            // Arrange
            var solicitacao = _solicitacaoFixture.GerarSolicitacaoCancelada();
            var destino = solicitacao.Destino.Codigo;

            ConfigurarMockUserContext(destino);

            _dependencyInjectorFactory.Mocker
                .GetMock<ISolicitacaoRepository>()
                .Setup(repository => repository.GetAsync(It.IsAny<Expression<Func<Entities.Entities.Solicitacao, bool>>>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync([]);

            var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

            var command = new FinalizarEmLoteCommand([solicitacao.Id]);

            // Act
            var response = await mediator.EnviarComandoAsync<FinalizarEmLoteCommand, SolicitacaoFinalizadaEmLoteResultDto>(command, CancellationToken.None);

            // Assert
            await ValidarFinalizarSolicitacao(response, 
                $"Não foi possível alterar o status da solicitação {solicitacao.Id} para Cancelada. Solicitação não existe.");
        }

        [Fact(DisplayName = "[Finalizar] - Deve retornar erro quando status da solicitação for inválido")]
        [Trait("Business", "Troca De Status Solicitacao")]
        public async Task FinalizarEmLote_DeveRetornarErro_StatusInvalido()
        {
            // Arrange
            var solicitacao = _solicitacaoFixture.GerarSolicitacaoCancelada();
            var destino = solicitacao.Destino.Codigo;

            ConfigurarMockUserContext(destino);

            _dependencyInjectorFactory.Mocker
                .GetMock<ISolicitacaoRepository>()
                .Setup(repository => repository.GetAsync(It.IsAny<Expression<Func<Entities.Entities.Solicitacao, bool>>>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync([solicitacao]);

            var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

            var command = new FinalizarEmLoteCommand([solicitacao.Id]);

            // Act
            var response = await mediator.EnviarComandoAsync<FinalizarEmLoteCommand, SolicitacaoFinalizadaEmLoteResultDto>(command, CancellationToken.None);

            // Assert
            await ValidarFinalizarSolicitacao(response,
                $"Não foi possível alterar o status da solicitação {solicitacao.Id} para finalizada. Solicitação se encontra com status '{DescriptionExtensions.GetDescription<StatusDeSolicitacao>(solicitacao.Status)}'.");
        }

        [Fact(DisplayName = "[Finalizar] - Deve retornar erro quando usuário for solicitante da própria solicitação")]
        [Trait("Business", "Troca De Status Solicitacao")]
        public async Task FinalizarEmLote_DeveRetornarErro_SolicitanteInvalido()
        {
            // Arrange
            var solicitacao = _solicitacaoFixture.GerarSolicitacaoConfirmada();
            var destino = solicitacao.Origem.Codigo;

            ConfigurarMockUserContext(destino);

            _dependencyInjectorFactory.Mocker
                .GetMock<ISolicitacaoRepository>()
                .Setup(repository => repository.GetAsync(It.IsAny<Expression<Func<Entities.Entities.Solicitacao, bool>>>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync([solicitacao]);

            var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

            var command = new FinalizarEmLoteCommand([solicitacao.Id]);

            // Act
            var response = await mediator.EnviarComandoAsync<FinalizarEmLoteCommand, SolicitacaoFinalizadaEmLoteResultDto>(command, CancellationToken.None);

            // Assert
            await ValidarFinalizarSolicitacao(response,
                $"Não foi possível alterar o status da solicitação {solicitacao.Id} para finalizada. Operação não permitida, usuário é o solicitante.");
        }

        [Fact(DisplayName = "[Finalizar] - Deve retornar erro quando usuário for atualizar os dados das solicitações")]
        [Trait("Business", "Troca De Status Solicitacao")]
        public async Task FinalizarEmLote_DeveRetornarErro_DadosInvalidosParaUpdate()
        {
            // Arrange
            var solicitacao = _solicitacaoFixture.GerarSolicitacaoConfirmada();
            var destino = solicitacao.Destino.Codigo;

            ConfigurarMockUserContext(destino);

            _dependencyInjectorFactory.Mocker
                .GetMock<ISolicitacaoRepository>()
                .Setup(repository => repository.GetAsync(It.IsAny<Expression<Func<Entities.Entities.Solicitacao, bool>>>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync([solicitacao]);

            _dependencyInjectorFactory.Mocker
                .GetMock<ISolicitacaoRepository>()
                .Setup(repository => repository.BulkUpdateAsync(It.IsAny<List<Entities.Entities.Solicitacao>>(), It.IsAny<CancellationToken>()))
                .ThrowsAsync(new RequestFaultException());

            var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();
            var command = new FinalizarEmLoteCommand([solicitacao.Id]);

            // Act & Assert
            await Assert.ThrowsAsync<RequestFaultException>(async () =>
                await mediator.EnviarComandoAsync<FinalizarEmLoteCommand, SolicitacaoFinalizadaEmLoteResultDto>(command, CancellationToken.None)
            );

            _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoRepository>().Verify(
                repository => repository.GetAsync(It.IsAny<Expression<Func<Entities.Entities.Solicitacao, bool>>>(), It.IsAny<CancellationToken>()),
                Times.Once);

            _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoRepository>().Verify(
                repository => repository.BulkUpdateAsync(It.IsAny<List<Entities.Entities.Solicitacao>>(), It.IsAny<CancellationToken>()),
                Times.Once);

            ShouldBeBooleanExtensions.ShouldBeFalse((await _dependencyInjectorFactory.Harness.Published.Any<SolicitacaoFinalizadaEmLoteEvent>()));
            ShouldBeBooleanExtensions.ShouldBeFalse((await _dependencyInjectorFactory.Harness.Consumed.Any<SolicitacaoFinalizadaEmLoteEvent>()));
        }

        private async Task ValidarFinalizarSolicitacao(SolicitacaoFinalizadaEmLoteResultDto response, string message) 
        {
            response.ShouldNotBeNull();

            response.Solicitacoes
                .Where(x => x.StatusId == (short)StatusDeSolicitacao.Erro)
                .ToList().ShouldNotBeEmpty();

            response.Solicitacoes.First().Erros.First()
                .ShouldBe(message);

            _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoRepository>().Verify(
                repository => repository.GetAsync(It.IsAny<Expression<Func<Entities.Entities.Solicitacao, bool>>>(), It.IsAny<CancellationToken>()),
                Times.Once);

            _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoRepository>().Verify(
                repository => repository.BulkUpdateAsync(It.IsAny<List<Entities.Entities.Solicitacao>>(), It.IsAny<CancellationToken>()),
                Times.Never);


            var solicitacaoFinalizadaEmLoteEventPublished = 
            await _dependencyInjectorFactory.Harness.Published.Any<SolicitacaoFinalizadaEmLoteEvent>();
            
            solicitacaoFinalizadaEmLoteEventPublished.ShouldBeFalse();
        }
    }
}
