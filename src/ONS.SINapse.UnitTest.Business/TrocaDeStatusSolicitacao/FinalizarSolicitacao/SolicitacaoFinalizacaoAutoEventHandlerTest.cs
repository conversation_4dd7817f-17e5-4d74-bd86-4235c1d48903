using MassTransit;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Repository.IRepository.Firebase;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Shared.Settings;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using ONS.SINapse.Solicitacao.Dtos.Integracao;
using ONS.SINapse.Solicitacao.EventHandlers.Events;
using ONS.SINapse.Solicitacao.EventHandlers.Handlers;

namespace ONS.SINapse.UnitTest.Business.TrocaDeStatusSolicitacao.FinalizarSolicitacao;

public class SolicitacaoFinalizacaoAutoEventHandlerTest
{
    private readonly Mock<IMediatorHandler> _mediatorHandler;
    private readonly Mock<ISolicitacaoFirebaseRepository> _solicitacaoFirebaseRepository;
    private readonly Mock<ILogger<SolicitacaoFinalizacaoAutoEventHandler>> _logger;
    private readonly SolicitacaoFinalizacaoAutoEventHandler _handler;

    public SolicitacaoFinalizacaoAutoEventHandlerTest()
    {
        var usuarioDoSistemaSettings = new Mock<IOptions<UsuarioDoSistemaSettings>>();
        _mediatorHandler = new Mock<IMediatorHandler>();
        _solicitacaoFirebaseRepository = new Mock<ISolicitacaoFirebaseRepository>();
        _logger = new Mock<ILogger<SolicitacaoFinalizacaoAutoEventHandler>>();

        usuarioDoSistemaSettings.Setup(x => x.Value).Returns(new UsuarioDoSistemaSettings
            { Login = "sys", Nome = "Sistema", Sid = "123" });

        _handler = new SolicitacaoFinalizacaoAutoEventHandler(
            usuarioDoSistemaSettings.Object,
            _mediatorHandler.Object,
            _solicitacaoFirebaseRepository.Object,
            _logger.Object
        );
    }

    [Fact(DisplayName = "Deve logar warning se payload estiver vazio")]
    public async Task Consume_PayloadVazio_DeveLogarWarning()
    {
        // Arrange
        var evt = new SolicitacaoFinalizacaoAutoEvent { Solicitacoes = new List<SolicitacaoFinalizacaoAutoDto>() };
        var context = new Mock<ConsumeContext<SolicitacaoFinalizacaoAutoEvent>>();
        context.Setup(x => x.Message).Returns(evt);

        // Act
        await _handler.Consume(context.Object);

        // Assert
        _logger.Verify(l => l.Log(
            LogLevel.Warning,
            It.IsAny<EventId>(),
            It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Payload vazio")),
            null,
            It.IsAny<Func<It.IsAnyType, Exception, string>>()!), Times.Once);
    }

    [Fact(DisplayName = "Deve logar warning se nenhum status for adequado para finalização")]
    public async Task Consume_StatusInadequado_DeveLogarWarning()
    {
        // Arrange
        var solicitacoes = new List<SolicitacaoFinalizacaoAutoDto>
        {
            new("id1", StatusDeSolicitacao.Pendente)
        };
        var evt = new SolicitacaoFinalizacaoAutoEvent { Solicitacoes = solicitacoes };
        var context = new Mock<ConsumeContext<SolicitacaoFinalizacaoAutoEvent>>();
        context.Setup(x => x.Message).Returns(evt);
        _solicitacaoFirebaseRepository.Setup(r => r.StatusAsync("id1", It.IsAny<CancellationToken>()))
            .ReturnsAsync(StatusDeSolicitacao.Pendente);

        // Act
        await _handler.Consume(context.Object);

        // Assert
        _logger.Verify(l => l.Log(
            LogLevel.Warning,
            It.IsAny<EventId>(),
            It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Nenhuma solicitação com status adequado")),
            null,
            It.IsAny<Func<It.IsAnyType, Exception, string>>()!), Times.Once);
    }

    [Fact(DisplayName = "Deve enviar comando para finalizar solicitações automaticamente")]
    public async Task Consume_StatusAdequado_DeveEnviarComandoFinalizar()
    {
        // Arrange
        var solicitacoes = new List<SolicitacaoFinalizacaoAutoDto>
        {
            new("id1", StatusDeSolicitacao.Confirmada)
        };
        var evt = new SolicitacaoFinalizacaoAutoEvent { Solicitacoes = solicitacoes };
        var context = new Mock<ConsumeContext<SolicitacaoFinalizacaoAutoEvent>>();
        context.Setup(x => x.Message).Returns(evt);
        _solicitacaoFirebaseRepository.Setup(r => r.StatusAsync("id1", It.IsAny<CancellationToken>()))
            .ReturnsAsync(StatusDeSolicitacao.Confirmada);
        _mediatorHandler.Setup(m =>
                m.EnviarComandoAsync<FinalizarEmLoteCommand, StatusDeSolicitacaoIntegracaoEnvioEmLoteDto>(
                    It.IsAny<FinalizarEmLoteCommand>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(MockStatusDeSolicitacaoIntegracaoEnvioEmLoteDto());

        // Act
        await _handler.Consume(context.Object);

        // Assert
        _mediatorHandler.Verify(
            m => m.EnviarComandoAsync<FinalizarEmLoteCommand, StatusDeSolicitacaoIntegracaoEnvioEmLoteDto>(
                It.IsAny<FinalizarEmLoteCommand>(), It.IsAny<CancellationToken>()), Times.Once);
        _logger.Verify(l => l.Log(
            LogLevel.Error,
            It.IsAny<EventId>(),
            It.IsAny<It.IsAnyType>(),
            It.IsAny<Exception>(),
            It.IsAny<Func<It.IsAnyType, Exception, string>>()!), Times.Never);
    }

    [Fact(DisplayName = "Deve logar erro se houver falha ao finalizar solicitações")]
    public async Task Consume_FalhaAoFinalizar_DeveLogarErro()
    {
        // Arrange
        var solicitacoes = new List<SolicitacaoFinalizacaoAutoDto>
        {
            new SolicitacaoFinalizacaoAutoDto("id1", StatusDeSolicitacao.Confirmada)
        };
        var evt = new SolicitacaoFinalizacaoAutoEvent { Solicitacoes = solicitacoes };
        var context = new Mock<ConsumeContext<SolicitacaoFinalizacaoAutoEvent>>();
        context.Setup(x => x.Message).Returns(evt);
        _solicitacaoFirebaseRepository.Setup(r => r.StatusAsync("id1", It.IsAny<CancellationToken>()))
            .ReturnsAsync(StatusDeSolicitacao.Confirmada);
        _mediatorHandler.Setup(m =>
                m.EnviarComandoAsync<FinalizarEmLoteCommand, StatusDeSolicitacaoIntegracaoEnvioEmLoteDto>(
                    It.IsAny<FinalizarEmLoteCommand>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(MockStatusDeSolicitacaoIntegracaoEnvioEmLoteDto(["Falha ao finalizar"]));

        // Act
        await _handler.Consume(context.Object);

        // Assert
        _logger.Verify(l => l.Log(
            LogLevel.Error,
            It.IsAny<EventId>(),
            It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Erro ao finalizar automaticamente")),
            null,
            It.IsAny<Func<It.IsAnyType, Exception, string>>()!), Times.Once);
    }

    private StatusDeSolicitacaoIntegracaoEnvioEmLoteDto MockStatusDeSolicitacaoIntegracaoEnvioEmLoteDto(
        List<string>? erros = null)
    {
        var envioDto = new StatusDeSolicitacaoIntegracaoEnvioDto(
            "id1",
            StatusDeSolicitacao.Finalizada,
            new ONS.SINapse.Shared.DTO.UsuarioDto { Login = "sys", Nome = "Sistema", Sid = "123" },
            DateTime.UtcNow,
            null,
            string.Empty,
            erros?.ToArray() ?? []
        );
        return new StatusDeSolicitacaoIntegracaoEnvioEmLoteDto([envioDto]);
    }
}


