using FluentValidation.Results;
using MassTransit;
using Microsoft.Extensions.DependencyInjection;
using ONS.SINapse.Repository.IRepository;
using ONS.SINapse.Shared.Constants;
using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Shared.Services;
using ONS.SINapse.Solicitacao.EventHandlers.Events;
using ONS.SINapse.UnitTest.Business.Fixtures.Solicitacao;
using ONS.SINapse.UnitTest.Shared;
using ValidationResult = FluentValidation.Results.ValidationResult;

namespace ONS.SINapse.UnitTest.Business.TrocaDeStatusSolicitacao.FinalizarSolicitacao;

public class SolicitacaoFinalizadaAutomaticamenteEventHandlerTest
{
    private readonly MockDependencyInjectorFactory _dependencyInjectorFactory;

    public SolicitacaoFinalizadaAutomaticamenteEventHandlerTest()
    {
        _dependencyInjectorFactory = new MockDependencyInjectorFactory();
        _dependencyInjectorFactory.RegisterMocks();
    }

    [Fact(DisplayName = "[Finalizar Automaticamente] - Deve cadastrar no storage com sucesso ao finalizar solicitacao")]
    [Trait("Business", "Finalizar Automaticamente")]
    public async Task
        Publicar_ValidationResultValido_DeveRegistrarCorretamenteFinalizacaoAutomatica()
    {
        // Arrange

        var evento = SolicitacaoEventsFixture.CriarSolicitacaoFinalizadaAutomaticamenteEvent(new ValidationResult());

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        // Act
        await mediator.PublicarEventoAsync(evento, CancellationToken.None);

        // Assert
        
        var eventoPublicado = 
            _dependencyInjectorFactory.Harness.Published.Any<SolicitacaoFinalizadaAutomaticamenteEvent>();
    
        var eventoConsumido = 
            _dependencyInjectorFactory.Harness.Consumed.Any<SolicitacaoFinalizadaAutomaticamenteEvent>();
    
        await Task.WhenAll(eventoPublicado, eventoConsumido);
    
        eventoPublicado.Result.ShouldBeTrue();
        eventoConsumido.Result.ShouldBeTrue();
        
        _dependencyInjectorFactory.Mocker
            .GetMock<IStatusServiceRepository>()
            .Verify(s => s.UpdateStatusAsync(
            JobService.FinalizarSolicitacaoAutomaticamente,
            true,
            null,
            null,
            It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact(DisplayName = "[Finalizar Automaticamente] - Deve cadastrar no storage com falha quando a validação é inválida")]
    [Trait("Business", "Finalizar Automaticamente")]
    public async Task Publicar_ValidationResultInvalido_DeveAddStatusErro()
    {
        // Arrange
        var validationResult = new ValidationResult();
        validationResult.Errors.Add(new ValidationFailure("TestProperty", "Test error message"));
        var exceptionFailure = new ValidationExptionFailure(new Exception("Test exception"),"TestProperty", "Test exception message");
        validationResult.Errors.Add(exceptionFailure);

        var evento = SolicitacaoEventsFixture.CriarSolicitacaoFinalizadaAutomaticamenteEvent(validationResult);

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        // Act
        await mediator.PublicarEventoAsync(evento, CancellationToken.None);

        // Assert
        var eventoPublicado = 
            _dependencyInjectorFactory.Harness.Published.Any<SolicitacaoFinalizadaAutomaticamenteEvent>();

        var eventoConsumido = 
            _dependencyInjectorFactory.Harness.Consumed.Any<SolicitacaoFinalizadaAutomaticamenteEvent>();

        await Task.WhenAll(eventoPublicado, eventoConsumido);

        eventoPublicado.Result.ShouldBeTrue();
        eventoConsumido.Result.ShouldBeTrue();

        _dependencyInjectorFactory.Mocker
            .GetMock<IStatusServiceRepository>()
            .Verify(s => s.UpdateStatusAsync(
            JobService.FinalizarSolicitacaoAutomaticamente,
            false,
            It.IsAny<Exception>(),
            It.Is<string>(erros => erros.Contains("Test error message") && erros.Contains("Test exception message")),
            It.IsAny<CancellationToken>()), 
                Times.Once);
    }
    

    [Fact(DisplayName = "[Finalizar Automaticamente] - Deve tratar exceção lançada pelo método AddStatus")]
    [Trait("Business", "Finalizar Automaticamente")]
    public async Task Publicar_AddStatusThrowsException_DeveRetornarException()
    {
        // Arrange
        var evento = SolicitacaoEventsFixture.CriarSolicitacaoFinalizadaAutomaticamenteEvent(new ValidationResult());

        var statusRepositoryMock = _dependencyInjectorFactory.Mocker
            .GetMock<IStatusServiceRepository>();
        statusRepositoryMock.Setup(s => s.UpdateStatusAsync(
                It.IsAny<string>(), 
                It.IsAny<bool>(), 
                It.IsAny<Exception>(), 
                It.IsAny<string>(),
                It.IsAny<CancellationToken>()
                )
            )
            .Throws(new Exception("Test exception"));

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        // Act & Assert
        await mediator.PublicarEventoAsync(evento, CancellationToken.None);

        var eventoPublicado = 
            _dependencyInjectorFactory.Harness.Published.Any<SolicitacaoFinalizadaAutomaticamenteEvent>();

        var eventoConsumido = 
            _dependencyInjectorFactory.Harness.Consumed.Any<SolicitacaoFinalizadaAutomaticamenteEvent>();

        var eventoErroPublicado = _dependencyInjectorFactory.Harness.Published
            .Any<Fault<SolicitacaoFinalizadaAutomaticamenteEvent>>();
        
        await Task.WhenAll(eventoPublicado, eventoConsumido, eventoErroPublicado);

        eventoPublicado.Result.ShouldBeTrue();
        eventoConsumido.Result.ShouldBeTrue();
        eventoErroPublicado.Result.ShouldBeTrue();

        // Verify that the exception was handled and didn't cause the test to fail
       
        statusRepositoryMock
            .Verify(s => s.UpdateStatusAsync(
            JobService.FinalizarSolicitacaoAutomaticamente,
            true,
            null,
            null, It.IsAny<CancellationToken>())
                , Times.Once);
    }
    
}