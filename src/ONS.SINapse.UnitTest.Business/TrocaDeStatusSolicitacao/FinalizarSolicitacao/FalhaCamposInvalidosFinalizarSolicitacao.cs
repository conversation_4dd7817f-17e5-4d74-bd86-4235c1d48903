using Microsoft.Extensions.DependencyInjection;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Repository.IRepository.Firebase;
using ONS.SINapse.Repository.IRepository.InMemory;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using ONS.SINapse.Solicitacao.Dtos.Integracao;
using ONS.SINapse.Solicitacao.EventHandlers.Events;
using ONS.SINapse.UnitTest.Shared.Fixtures;
using System.Linq.Expressions;

namespace ONS.SINapse.UnitTest.Business.TrocaDeStatusSolicitacao.FinalizarSolicitacao;

public partial class FinalizarSolicitacaoTest
{
    [Theory(DisplayName = "[Finalizar] - Deve retornar um erro quando há campos não preenchidos")]
    [Trait("Business", "Troca De Status Solicitacao")]
    [ClassData(typeof(StatusDeSolicitacaoIntegracaoCamposTest))]
    public async Task FinalizarSolicitacao_DeveRetornarUmErro_UsuarioDeveSerPreenchido(ValidacaoDeCamposStatusDeSolicitacaoAction action)
    {
        // Arrange

        var solicitacao = _solicitacaoFixture.GerarSolicitacaoConfirmada();

        var origem = solicitacao.Origem.Codigo;

        var usuario = UsuarioFixture.GerarUsuarioDtoValido();
            
        ConfigurarMockUserContext(usuario, origem);
        
        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        var trocaDeStatus = new StatusDeSolicitacaoIntegracaoRecebimentoDto(
            solicitacao.Id,
            StatusDeSolicitacao.Finalizada,
            null,
            new ObjetoDeManobraDto(origem, origem),
            usuario,
            "SINapse"
        );

        action.Acao(trocaDeStatus);
        
        var command = new FinalizarEmLoteCommand([
            trocaDeStatus
        ]);

        // Quando id null não deve retornar nada do mongo
        var solicitacoesRetornadas =
            trocaDeStatus.Id is null ? new List<Entities.Entities.Solicitacao>() : [solicitacao];

        _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoMemoryRepository>()
            .Setup(repository => repository.GetList(It.IsAny<Expression<Func<Entities.Entities.Solicitacao, bool>>>()))
            .Returns(solicitacoesRetornadas);

        // Act

        var response = 
            await mediator.EnviarComandoAsync<FinalizarEmLoteCommand, StatusDeSolicitacaoIntegracaoEnvioEmLoteDto>(command, CancellationToken.None);

        // Assert

        await ValidarCamposFinalizarSolicitacao(response, action.Campo);
    }
    
    private async Task ValidarCamposFinalizarSolicitacao(StatusDeSolicitacaoIntegracaoEnvioEmLoteDto response, string campo)
    {   
        Assert.NotNull(response);

        response.Solicitacoes
            .SelectMany(x => x.Erros)
            .Any(x => x == $"Campo {campo} precisa ser preenchido.")
            .ShouldBeTrue();

        response.Solicitacoes
            .All(x => x.Erros.Length > 0)
            .ShouldBeTrue();

        response.Solicitacoes
            .All(x => x.Status == StatusDeSolicitacao.Erro)
            .ShouldBeTrue();

        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoFirebaseRepository>().Verify(
            repository => repository.PatchFlattenAsync(It.IsAny<List<EncaminharSolicitacaoFirebaseDto>>(), It.IsAny<CancellationToken>()),
            Times.Never);

        var solicitacaoFinalizadaEventPublished =
            _dependencyInjectorFactory.Harness.Published.Any<SolicitacaoConcluidaEvent>();

        var finalizadaEventKafkaPublished =
            _dependencyInjectorFactory.Harness.Published.Any<StatusDeSolicitacaoIntegracaoRecebidaEvent>();

        await Task.WhenAll(solicitacaoFinalizadaEventPublished, finalizadaEventKafkaPublished);

        solicitacaoFinalizadaEventPublished.Result.ShouldBeFalse();
        finalizadaEventKafkaPublished.Result.ShouldBeTrue();
    }
}



