using System.Collections;
using Confluent.Kafka;
using MassTransit;
using MassTransit.Testing;
using Microsoft.Extensions.DependencyInjection;
using ONS.SINapse.Integracao.Shared.Dtos;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Integracao.Shared.Helpers;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using ONS.SINapse.Solicitacao.EventHandlers.Events;
using ONS.SINapse.Solicitacao.EventHandlers.IntegrationEvents;
using ONS.SINapse.UnitTest.Business.Fixtures.Collections;
using ONS.SINapse.UnitTest.Business.Fixtures.Solicitacao;
using ONS.SINapse.UnitTest.Shared;
using ONS.SINapse.UnitTest.Shared.Fixtures;

namespace ONS.SINapse.UnitTest.Business.TrocaDeStatusSolicitacao;

[Collection(nameof(TrocaDeStatusExternaIntegrationEventHandlerCollection))]
public class TrocaDeStatusExternaIntegrationEventHandlerTest
{
    private readonly MockDependencyInjectorFactory _dependencyInjectorFactory;

    public TrocaDeStatusExternaIntegrationEventHandlerTest()
    {
        _dependencyInjectorFactory = new MockDependencyInjectorFactory();
        _dependencyInjectorFactory.RegisterMocks();
    }
    
    [Theory(DisplayName = "[Troca de Status Externa] - Deve enviar command de troca de status com sucesso")]
    [Trait("Business", "Troca de Status Externa")]
    [InlineData((int)StatusDeSolicitacao.Cancelada, typeof(CancelarEmLoteCommand))]
    [InlineData((int)StatusDeSolicitacao.CienciaInformada, typeof(InformarCienciaCommand))]
    public async Task TrocaDeStatusExterna_StatusValido_DeveRetornarSucesso(int? status, Type commandType)
    {
        // Arrange
        
        var evento =
            SolicitacaoEventsFixture.CriarTrocaDeStatusExternaIntegrationEvent("TEST123", "NE", status);
        
        evento.DefinirHeaders([new Header("AuthorizationUser", "dummyToken"u8.ToArray())]);

        var usuarioDto = UsuarioFixture.GerarUsuarioDtoValido();

        _dependencyInjectorFactory.Mocker
            .GetMock<IUsuarioJwtHelper>()
            .Setup(x => x.GetUsuario(It.IsAny<string>()))
            .Returns(new UsuarioJwtDto(usuarioDto.Sid, usuarioDto.Login, usuarioDto.Nome));

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();
        
        // Act && Assert

        await mediator.PublicarEventoAsync(evento, CancellationToken.None);
        
        var eventoPublicado = _dependencyInjectorFactory.Harness.Published.Any<TrocaDeStatusExternaIntegrationEvent>();
        var eventoConsumido = _dependencyInjectorFactory.Harness.Consumed.Any<TrocaDeStatusExternaIntegrationEvent>();

        var commandDisparado = _dependencyInjectorFactory.Harness.Published
            .Any(x => x.MessageType == commandType);
        
        await Task.WhenAll(eventoPublicado, eventoConsumido, commandDisparado);
        
        eventoPublicado.Result.ShouldBeTrue();
        eventoConsumido.Result.ShouldBeTrue();
        commandDisparado.Result.ShouldBeTrue();
    }
    
    [Theory(DisplayName = "[Troca de Status Externa] - Deve lançar exception ao receber status inválido")]
    [Trait("Business", "Troca de Status Externa")]
    [InlineData((int)StatusDeSolicitacao.Pendente)]
    [InlineData((int)StatusDeSolicitacao.Impedida)]
    [InlineData((int)StatusDeSolicitacao.Confirmada)]
    [InlineData((int)StatusDeSolicitacao.Erro)]
    [InlineData(null)]
    public async Task TrocaDeStatusExterna_StatusInvalido_DeveRetornarUmaException(int? status)
    {
        // Arrange
        
        var evento =
            SolicitacaoEventsFixture.CriarTrocaDeStatusExternaIntegrationEvent("TEST123", "NE");
        evento.Status = status;
        
        evento.DefinirHeaders([new Header("AuthorizationUser", "dummyToken"u8.ToArray())]);

        var usuarioDto = UsuarioFixture.GerarUsuarioDtoValido();

        _dependencyInjectorFactory.Mocker
            .GetMock<IUsuarioJwtHelper>()
            .Setup(x => x.GetUsuario(It.IsAny<string>()))
            .Returns(new UsuarioJwtDto(usuarioDto.Sid, usuarioDto.Login, usuarioDto.Nome));

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();
        
        // Act && Assert

        await mediator.PublicarEventoAsync(evento, CancellationToken.None);
        
        var eventoPublicado = _dependencyInjectorFactory.Harness.Published.Any<TrocaDeStatusExternaIntegrationEvent>();
        var eventoConsumido = _dependencyInjectorFactory.Harness.Consumed.Any<TrocaDeStatusExternaIntegrationEvent>();
        var eventoErroPublicado = _dependencyInjectorFactory.Harness.Published.Any<Fault<TrocaDeStatusExternaIntegrationEvent>>();

        
        var fault = await _dependencyInjectorFactory.Harness.Published
            .SelectAsync<Fault<TrocaDeStatusExternaIntegrationEvent>>()
            .FirstOrDefault();
        
        var exceptionInfo = fault.Context.Message.Exceptions.FirstOrDefault();
        
        exceptionInfo.ShouldNotBeNull();
        exceptionInfo.ExceptionType.ShouldBe(typeof(ArgumentOutOfRangeException).FullName);
        
        await Task.WhenAll(eventoPublicado, eventoConsumido, eventoErroPublicado);
        
        eventoPublicado.Result.ShouldBeTrue();
        eventoConsumido.Result.ShouldBeTrue();
        eventoErroPublicado.Result.ShouldBeTrue();
    }

    [Fact(DisplayName = "[Troca de Status Externa] - Deve lançar exception ao receber status finalizado")]
    [Trait("Business", "Troca de Status Externa")]
    public async Task TrocaDeStatusExterna_StatusFinalizado_DeveRetornarUmaException()
    {
        // Arrange
        
        var evento =
            SolicitacaoEventsFixture.CriarTrocaDeStatusExternaIntegrationEvent("TEST123", "NE", (int)StatusDeSolicitacao.Finalizada);
        
        evento.DefinirHeaders([new Header("AuthorizationUser", "dummyToken"u8.ToArray())]);

        var usuarioDto = UsuarioFixture.GerarUsuarioDtoValido();

        _dependencyInjectorFactory.Mocker
            .GetMock<IUsuarioJwtHelper>()
            .Setup(x => x.GetUsuario(It.IsAny<string>()))
            .Returns(new UsuarioJwtDto(usuarioDto.Sid, usuarioDto.Login, usuarioDto.Nome));

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();
        
        // Act && Assert

        await mediator.PublicarEventoAsync(evento, CancellationToken.None);
        
        var eventoPublicado = _dependencyInjectorFactory.Harness.Published.Any<TrocaDeStatusExternaIntegrationEvent>();
        var eventoConsumido = _dependencyInjectorFactory.Harness.Consumed.Any<TrocaDeStatusExternaIntegrationEvent>();
        var eventoErroPublicado = _dependencyInjectorFactory.Harness.Published.Any<Fault<TrocaDeStatusExternaIntegrationEvent>>();

        
        var fault = await _dependencyInjectorFactory.Harness.Published
            .SelectAsync<Fault<TrocaDeStatusExternaIntegrationEvent>>()
            .FirstOrDefault();
        
        var exceptionInfo = fault.Context.Message.Exceptions.FirstOrDefault();
        
        exceptionInfo.ShouldNotBeNull();
        exceptionInfo.ExceptionType.ShouldBe(typeof(NotImplementedException).FullName);
        
        await Task.WhenAll(eventoPublicado, eventoConsumido, eventoErroPublicado);
        
        eventoPublicado.Result.ShouldBeTrue();
        eventoConsumido.Result.ShouldBeTrue();
        eventoErroPublicado.Result.ShouldBeTrue();
    }

    #region Validação de campos inválidos

    [Fact(DisplayName = "[Troca de Status Externa] - Deve retornar erro quando o header 'AuthorizationUser' está ausente")]
    [Trait("Business", "Troca de Status Externa")]
    public async Task TrocaDeStatusExterna_SemAuthorizationUserHeader_DeveRetornarErro()
    {
        // Arrange
        var evento =
            SolicitacaoEventsFixture.CriarTrocaDeStatusExternaIntegrationEvent("TEST123", "NE");

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();
    
        // Act
        await mediator.PublicarEventoAsync(evento, CancellationToken.None);
    
        // Assert
        var eventoPublicado = _dependencyInjectorFactory.Harness.Published.Any<TrocaDeStatusExternaIntegrationEvent>();
        var eventoConsumido = _dependencyInjectorFactory.Harness.Consumed.Any<TrocaDeStatusExternaIntegrationEvent>();
        var eventoErroPublicado = await _dependencyInjectorFactory.Harness.Published.SelectAsync(x => 
            x.MessageType == typeof(SolicitacoesCanceladasComErroEmLoteEvent) ||
            x.MessageType == typeof(CienciaInformadaComErroEvent))
            .FirstOrDefault();

        eventoErroPublicado.ShouldNotBeNull();
        
        if(eventoErroPublicado.Context.TryGetPayload(out SolicitacoesCanceladasComErroEmLoteEvent? solicitacaoCancelada))
        {
            solicitacaoCancelada.Solicitacoes.ShouldNotBeEmpty();
            solicitacaoCancelada.Solicitacoes.Any(x => x.Erros.Any(y => y.Contains("Não foi possível definir o usuário"))).ShouldBeTrue();
        }

        if (eventoErroPublicado.Context.TryGetPayload(out CienciaInformadaComErroEvent? solicitacaoCienciaInformada))
        {
            solicitacaoCienciaInformada.ShouldNotBeNull();
            solicitacaoCienciaInformada.Solicitacao.Erros.ShouldNotBeEmpty();
            solicitacaoCienciaInformada.Solicitacao.Erros.Any(y => y.Contains("Não foi possível definir o usuário")).ShouldBeTrue();
        }
        
        await Task.WhenAll(eventoPublicado, eventoConsumido);
    
        eventoPublicado.Result.ShouldBeTrue();
        eventoConsumido.Result.ShouldBeTrue();
    }
    
    [Theory(DisplayName = "[Troca de Status Externa] - Deve retornar erro quando há campos ausente")]
    [Trait("Business", "Troca de Status Externa")]
    [ClassData(typeof(ValidarCamposTrocaDeStatusExterna))]
    public async Task CadastroDeSolicitacaoExterna_CamposInvalidos_DeveRetornarErro(ValidarCamposTrocaDeStatusExternaAction validarCampo, Type eventErrorType)
    {
        // Arrange
        
        var evento =
            SolicitacaoEventsFixture.CriarTrocaDeStatusExternaIntegrationEvent();

        validarCampo.Acao(evento);
        
        evento.DefinirHeaders([new Header("AuthorizationUser", "dummyToken"u8.ToArray())]);
    
        var usuarioDto = UsuarioFixture.GerarUsuarioDtoValido();
    
        _dependencyInjectorFactory.Mocker
            .GetMock<IUsuarioJwtHelper>()
            .Setup(x => x.GetUsuario(It.IsAny<string>()))
            .Returns(new UsuarioJwtDto(usuarioDto.Sid, usuarioDto.Login, usuarioDto.Nome));
    
        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>(); 
        
        // Act && Assert
    
        await mediator.PublicarEventoAsync(evento, CancellationToken.None);
        
        var eventoPublicado = _dependencyInjectorFactory.Harness.Published.Any<TrocaDeStatusExternaIntegrationEvent>();
        var eventoConsumido = _dependencyInjectorFactory.Harness.Consumed.Any<TrocaDeStatusExternaIntegrationEvent>();
        var eventoErroPublicado =  _dependencyInjectorFactory.Harness.Published.Any(x => x.MessageType == eventErrorType);
        
        await Task.WhenAll(eventoPublicado, eventoConsumido, eventoErroPublicado);
    
        eventoPublicado.Result.ShouldBeTrue();
        eventoConsumido.Result.ShouldBeTrue();
        eventoErroPublicado.Result.ShouldBeTrue();
    }
    
    public class ValidarCamposTrocaDeStatusExterna : IEnumerable<object[]>
    {
        public IEnumerator<object[]> GetEnumerator()
        {
            yield return [new ValidarCamposTrocaDeStatusExternaAction("Status", x => x.Status = null), typeof(Fault<TrocaDeStatusExternaIntegrationEvent>)];

            // Cancelar
            yield return [new ValidarCamposTrocaDeStatusExternaAction("Codigo", x =>
            {
                x.Codigo = null;
                x.Status = (int)StatusDeSolicitacao.Cancelada;
            }), typeof(SolicitacoesCanceladasComErroEmLoteEvent)];
            yield return [new ValidarCamposTrocaDeStatusExternaAction("Centro de Operacao", x =>
            {
                x.CentroDeOperacao = null;
                x.Status = (int)StatusDeSolicitacao.Cancelada;
            }), typeof(SolicitacoesCanceladasComErroEmLoteEvent)];
            
            // Informar Ciencia
            yield return [new ValidarCamposTrocaDeStatusExternaAction("Codigo", x =>
            {
                x.Codigo = null;
                x.Status = (int)StatusDeSolicitacao.CienciaInformada;
            }), typeof(CienciaInformadaComErroEvent)];
            yield return [new ValidarCamposTrocaDeStatusExternaAction("Centro de Operacao", x =>
            {
                x.CentroDeOperacao = null;
                x.Status = (int)StatusDeSolicitacao.CienciaInformada;
            }), typeof(CienciaInformadaComErroEvent)];
        }
        
        IEnumerator IEnumerable.GetEnumerator() => GetEnumerator();
    }
    
    public class ValidarCamposTrocaDeStatusExternaAction
    {
        private string Nome { get; }
        public Action<TrocaDeStatusExternaIntegrationEvent> Acao { get; }

        public ValidarCamposTrocaDeStatusExternaAction(string nome, Action<TrocaDeStatusExternaIntegrationEvent> acao)
        {
            Nome = nome;
            Acao = acao;
        }

        public override string ToString() => Nome;
    }

    #endregion
}