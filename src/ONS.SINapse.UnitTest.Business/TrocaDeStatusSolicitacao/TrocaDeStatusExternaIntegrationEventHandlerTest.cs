using Microsoft.Extensions.DependencyInjection;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using ONS.SINapse.Solicitacao.Dtos.Integracao;
using ONS.SINapse.Solicitacao.EventHandlers.IntegrationEvents;
using ONS.SINapse.UnitTest.Business.Fixtures.Collections;
using ONS.SINapse.UnitTest.Shared;
using Xunit.Abstractions;

namespace ONS.SINapse.UnitTest.Business.TrocaDeStatusSolicitacao;

[Collection(nameof(TrocaDeStatusExternaIntegrationEventHandlerCollection))]
public class TrocaDeStatusExternaIntegrationEventHandlerTest
{
    private readonly ITestOutputHelper _testOutputHelper;
    private readonly MockDependencyInjectorFactory _dependencyInjectorFactory;

    public TrocaDeStatusExternaIntegrationEventHandlerTest(ITestOutputHelper testOutputHelper)
    {
        _testOutputHelper = testOutputHelper;
        _dependencyInjectorFactory = new MockDependencyInjectorFactory();
        _dependencyInjectorFactory.RegisterMocks();
    }

    [Theory(DisplayName = "[Troca de Status Externa] - Deve enviar command de troca de status com sucesso")]
    [Trait("Business", "Troca de Status Externa")]
    [InlineData(StatusDeSolicitacao.Confirmada, typeof(ConfirmarCommand))]
    [InlineData(StatusDeSolicitacao.Cancelada, typeof(CancelarEmLoteCommand))]
    [InlineData(StatusDeSolicitacao.Finalizada, typeof(FinalizarEmLoteCommand))]
    [InlineData(StatusDeSolicitacao.Impedida, typeof(ImpedirCommand))]
    [InlineData(StatusDeSolicitacao.CienciaInformada, typeof(InformarCienciaCommand))]
    public async Task TrocaStatusExterna_DeveRetornarSucesso(StatusDeSolicitacao status, Type commandType)
    {
        // Arrange
        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();
        
        var evento = new TrocaDeStatusExternaIntegrationEvent([
            new StatusDeSolicitacaoIntegracaoRecebimentoDto(
                Guid.NewGuid().ToString(),
                status, 
                null,
                new ObjetoDeManobraDto("NE", "NE"), 
                new UsuarioDto(),
                "Externo")
        ]);

        // Act
        
        await mediator.PublicarEventoAsync(evento, CancellationToken.None);

        // Assert
        
        var publicou = await _dependencyInjectorFactory
            .Harness
            .Published
            .Any(x => x.MessageObject.GetType() == commandType);
        
        publicou.ShouldBeTrue();
    }
    
    [Theory(DisplayName = "[Troca de Status Externa] - Não deve enviar nenhum command para troca de status")]
    [Trait("Business", "Troca de Status Externa")]
    [InlineData(StatusDeSolicitacao.Pendente)]
    [InlineData(StatusDeSolicitacao.Erro)]
    public async Task TrocaStatusExterna_DeveRetornarErro(StatusDeSolicitacao status)
    {
        // Arrange
        
        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();
        
        var evento = new TrocaDeStatusExternaIntegrationEvent([
            new StatusDeSolicitacaoIntegracaoRecebimentoDto(
                Guid.NewGuid().ToString(),
                status, 
                null,
                new ObjetoDeManobraDto("NE", "NE"), 
                new UsuarioDto(),
                "Externo")
        ]);
        
        // Act
        
        await mediator.PublicarEventoAsync(evento, CancellationToken.None);

        // Assert
        
        var publicou = _dependencyInjectorFactory
            .Harness
            .Published
            .Any<TrocaDeStatusExternaIntegrationEvent>();

        var consumiu = _dependencyInjectorFactory
            .Harness
            .Consumed
            .Any<TrocaDeStatusExternaIntegrationEvent>();

        Type[] commands =
        [
            typeof(ConfirmarCommand),
            typeof(CancelarEmLoteCommand),
            typeof(FinalizarEmLoteCommand),
            typeof(ImpedirCommand),
            typeof(InformarCienciaCommand)
        ];

        var commandsDisparados = _dependencyInjectorFactory
            .Harness
            .Published
            .Any(x => commands.Contains(x.MessageObject.GetType()));
        
        await Task.WhenAll(publicou, consumiu, commandsDisparados);
        
        publicou.Result.ShouldBeTrue();
        consumiu.Result.ShouldBeTrue();
        commandsDisparados.Result.ShouldBeFalse();
    }
}