using System.Linq.Expressions;
using Microsoft.Extensions.DependencyInjection;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Repository.IRepository;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using ONS.SINapse.Solicitacao.Dtos.Integracao;
using ONS.SINapse.Solicitacao.EventHandlers.Events;
using ONS.SINapse.UnitTest.Shared.Fixtures;

namespace ONS.SINapse.UnitTest.Business.TrocaDeStatusSolicitacao.InformarCiencia;

public partial class InformarCienciaTest
{
    [Fact(DisplayName = "[Informar Ciência] - Deve retornar erro quando solicitação não existir")]
    [Trait("Business", "Troca De Status Solicitacao")]
    public async Task InformarCiencia_DeveRetornarErro_SolicitacaoNaoExistente()
    {
        // Arrange
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoImpedida("Motivo para informar Ciência");
        var destino = solicitacao.Destino.Codigo;
        var usuario = UsuarioFixture.GerarUsuarioDtoValido();

        ConfigurarMockUserContext(usuario, destino);
        
        _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoRepository>()
            .Setup(repository => repository.GetAsync(It.IsAny<Expression<Func<Entities.Entities.Solicitacao, bool>>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);
        
        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        var command = new InformarCienciaCommand([
            new StatusDeSolicitacaoIntegracaoRecebimentoDto(
                solicitacao.Id,
                StatusDeSolicitacao.CienciaInformada,
                null,
                new ObjetoDeManobraDto(destino, destino),
                usuario,
                "SINapse"
            )
        ]);

        // Act
        var response = 
            await mediator.EnviarComandoAsync<InformarCienciaCommand, StatusDeSolicitacaoIntegracaoEnvioEmLoteDto>(command, CancellationToken.None);

        // Assert
        await ValidarDadosInformarCienciaSolicitacao(response, 
            $"Não foi possível alterar o status da solicitação {solicitacao.Id} para {StatusDeSolicitacao.CienciaInformada.GetDescription()}. Solicitação não existe.");
    }

    [Fact(DisplayName = "[Informar Ciência] - Deve retornar erro quando status da solicitação for inválido")]
    [Trait("Business", "Troca De Status Solicitacao")]
    public async Task InformarCiencia_DeveRetornarErro_StatusInvalido()
    {
        // Arrange
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoCienciaInformada();
        var origem = solicitacao.Origem.Codigo;
        var usuario = UsuarioFixture.GerarUsuarioDtoValido();

        ConfigurarMockUserContext(usuario, origem);

        _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoRepository>()
            .Setup(repository => repository.GetAsync(It.IsAny<Expression<Func<Entities.Entities.Solicitacao, bool>>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([solicitacao]);

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        var command = new InformarCienciaCommand([
            new StatusDeSolicitacaoIntegracaoRecebimentoDto(
                solicitacao.Id,
                StatusDeSolicitacao.CienciaInformada,
                null,
                new ObjetoDeManobraDto(origem, origem),
                usuario,
                "SINapse"
            )
        ]);

        // Act
        var response = await mediator.EnviarComandoAsync<InformarCienciaCommand, StatusDeSolicitacaoIntegracaoEnvioEmLoteDto>(command, CancellationToken.None);

        // Assert
        await ValidarDadosInformarCienciaSolicitacao(response, 
            $"Não foi possível alterar o status da solicitação {solicitacao.Id} para {StatusDeSolicitacao.CienciaInformada.GetDescription()}. Solicitação se encontra com status {solicitacao.Status.GetDescription()}.");
    }

    [Fact(DisplayName = "[Informar Ciência] - Deve retornar erro quando usuário for solicitante da própria solicitação")]
    [Trait("Business", "Troca De Status Solicitacao")]
    public async Task InformarCiencia_DeveRetornarErro_SolicitanteInvalido()
    {
        // Arrange
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoImpedida("Motivo para informar Ciência");
        var destino = solicitacao.Destino.Codigo;
        var usuario = UsuarioFixture.GerarUsuarioDtoValido();
        
        ConfigurarMockUserContext(usuario, destino);

        _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoRepository>()
            .Setup(repository => repository.GetAsync(It.IsAny<Expression<Func<Entities.Entities.Solicitacao, bool>>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([solicitacao]);

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        var command = new InformarCienciaCommand([
            new StatusDeSolicitacaoIntegracaoRecebimentoDto(
                solicitacao.Id,
                StatusDeSolicitacao.CienciaInformada,
                null,
                new ObjetoDeManobraDto(destino, destino),
                usuario,
                "SINapse"
            )
        ]);

        // Act
        var response = 
            await mediator.EnviarComandoAsync<InformarCienciaCommand, StatusDeSolicitacaoIntegracaoEnvioEmLoteDto>(command, CancellationToken.None);

        // Assert
        await ValidarDadosInformarCienciaSolicitacao(response, 
            $"Não foi possível alterar o status da solicitação {solicitacao.Id} para {StatusDeSolicitacao.CienciaInformada.GetDescription()}. Operação não permitida, usuário é o solicitante.");
    }

    private async Task ValidarDadosInformarCienciaSolicitacao(StatusDeSolicitacaoIntegracaoEnvioEmLoteDto response, string message)
    {
        response.ShouldNotBeNull();

        response.Solicitacoes.Any(x => x.Status == StatusDeSolicitacao.CienciaInformada).ShouldBeFalse();
        response.Solicitacoes.All(x => x.Status == StatusDeSolicitacao.Erro).ShouldBeTrue();
        response.Solicitacoes.SelectMany(x => x.Erros).ShouldNotBeEmpty();

        response.Solicitacoes.First().Erros.First()
            .ShouldBe(message);

        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoRepository>().Verify(
            repository => repository.GetAsync(It.IsAny<Expression<Func<Entities.Entities.Solicitacao, bool>>>(), It.IsAny<CancellationToken>()),
            Times.Once);

        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoRepository>().Verify(
            repository => repository.BulkUpdateAsync(It.IsAny<List<Entities.Entities.Solicitacao>>(), It.IsAny<CancellationToken>()),
            Times.Never);
        
        var informarCienciaSolicitacaoNoFirebaseCommandPublished =
            await _dependencyInjectorFactory.Harness.Published.Any<EnviarTrocaDeStatusAoFirebaseCommand>();
        
        var cienciaInformadaEventPublished =
            await _dependencyInjectorFactory.Harness.Published.Any<StatusDeSolicitacaoIntegracaoRecebidaEvent>();
        
        informarCienciaSolicitacaoNoFirebaseCommandPublished.ShouldBeFalse();
        cienciaInformadaEventPublished.ShouldBeTrue();
    }
}
