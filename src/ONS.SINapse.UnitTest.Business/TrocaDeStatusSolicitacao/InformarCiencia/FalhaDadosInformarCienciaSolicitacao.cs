using Microsoft.Extensions.DependencyInjection;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Repository.IRepository;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using ONS.SINapse.Solicitacao.EventHandlers.Events;

namespace ONS.SINapse.UnitTest.Business.TrocaDeStatusSolicitacao.InformarCiencia;

public partial class InformarCienciaTest
{
    [Fact(DisplayName = "[Informar Ciência] - Deve retornar erro quando solicitação não existir")]
    [Trait("Business", "Troca De Status Solicitacao")]
    public async Task InformarCiencia_DeveRetornarErro_SolicitacaoNaoExistente()
    {
        // Arrange
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoImpedida("Motivo para informar Ciência");
        var destino = solicitacao.Destino.Codigo;

        ConfigurarMockUserContext(destino);
        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        var command = new InformarCienciaCommand(solicitacao.Id);

        // Act
        var response = await mediator.EnviarComandoAsync<InformarCienciaCommand, CienciaInformadaResultDto>(command, CancellationToken.None);

        // Assert
        await ValidarDadosInformarCienciaSolicitacao(response, $"Solicitação não existe.");
    }

    [Fact(DisplayName = "[Informar Ciência] - Deve retornar erro quando status da solicitação for inválido")]
    [Trait("Business", "Troca De Status Solicitacao")]
    public async Task InformarCiencia_DeveRetornarErro_StatusInvalido()
    {
        // Arrange
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoPendente();
        var origem = solicitacao.Origem.Codigo;

        ConfigurarMockUserContext(origem);

        _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoRepository>()
            .Setup(repository => repository.GetOneAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(solicitacao);

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        var command = new InformarCienciaCommand(solicitacao.Id);

        // Act
        var response = await mediator.EnviarComandoAsync<InformarCienciaCommand, CienciaInformadaResultDto>(command, CancellationToken.None);

        // Assert
        await ValidarDadosInformarCienciaSolicitacao(response, $"Solicitação não está impedida.");
    }

    [Fact(DisplayName = "[Informar Ciência] - Deve retornar erro quando usuário for solicitante da própria solicitação")]
    [Trait("Business", "Troca De Status Solicitacao")]
    public async Task InformarCiencia_DeveRetornarErro_SolicitanteInvalido()
    {
        // Arrange
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoImpedida("Motivo para informar Ciência");
        var destino = solicitacao.Destino.Codigo;
        
        ConfigurarMockUserContext(destino);

        _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoRepository>()
            .Setup(repository => repository.GetOneAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(solicitacao);

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        var command = new InformarCienciaCommand(solicitacao.Id);
        command.DefinirCentros([destino]);

        // Act
        var response = await mediator.EnviarComandoAsync<InformarCienciaCommand, CienciaInformadaResultDto>(command, CancellationToken.None);

        // Assert
        await ValidarDadosInformarCienciaSolicitacao(response, $"Perfil não é solicitante, operação não permitida.");
    }

    private async Task ValidarDadosInformarCienciaSolicitacao(CienciaInformadaResultDto response, string message)
    {
        response.ShouldNotBeNull();

        response.StatusId.ShouldNotBe((short)StatusDeSolicitacao.CienciaInformada);
        response.StatusId.ShouldBe((short)StatusDeSolicitacao.Erro);
        response.Erros.ShouldNotBeEmpty();

        response.Erros.First().ShouldBe(message);

        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoRepository>().Verify(
            repository => repository.GetOneAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()),
            Times.Once);

        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoRepository>().Verify(
            repository => repository.AtualizarAsync(It.IsAny<Entities.Entities.Solicitacao>(), It.IsAny<CancellationToken>()),
            Times.Never);
        
        var cienciaInformadaComErroEventPublished =
            await _dependencyInjectorFactory.Harness.Published.Any<CienciaInformadaComErroEvent>();
        
        var informarCienciaSolicitacaoNoFirebaseCommandPublished =
            await _dependencyInjectorFactory.Harness.Published.Any<InformarCienciaSolicitacaoNoFirebaseCommand>();
        
        var cienciaInformadaEventPublished =
            await _dependencyInjectorFactory.Harness.Published.Any<CienciaInformadaEvent>();
        
        cienciaInformadaComErroEventPublished.ShouldBeTrue();
        informarCienciaSolicitacaoNoFirebaseCommandPublished.ShouldBeFalse();
        cienciaInformadaEventPublished.ShouldBeFalse();
    }
}
