using System.Linq.Expressions;
using Microsoft.Extensions.DependencyInjection;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Repository.IRepository.InMemory;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using ONS.SINapse.Solicitacao.Dtos.Integracao;
using ONS.SINapse.Solicitacao.EventHandlers.Events;
using ONS.SINapse.UnitTest.Shared.Fixtures;


namespace ONS.SINapse.UnitTest.Business.TrocaDeStatusSolicitacao.InformarCiencia
{
    public partial class InformarCienciaTest
    {
        [Fact(DisplayName = "[Informar Ciência] - Deve informar ciencia")]
        [Trait("Business", "Troca De Status Solicitacao")]
        public async Task DeveInformarCienciaSolicitacao()
        {
            // Arrange
            var solicitacao = _solicitacaoFixture.GerarSolicitacaoImpedida("Motivo para informar Ciência");
            var origem = solicitacao.Origem.Codigo;
            var usuario = UsuarioFixture.GerarUsuarioDtoValido();
            
            ConfigurarMockUserContext(usuario, origem);

            _dependencyInjectorFactory.Mocker
                .GetMock<ISolicitacaoMemoryRepository>()
                .Setup(repository => repository.GetList(It.IsAny<Expression<Func<Entities.Entities.Solicitacao, bool>>>()))
                .Returns([solicitacao]);

            var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

            var command = new InformarCienciaCommand([
                new StatusDeSolicitacaoIntegracaoRecebimentoDto(
                    solicitacao.Id,
                    StatusDeSolicitacao.CienciaInformada,
                    null,
                    new ObjetoDeManobraDto(origem, origem),
                    usuario,
                    "SINapse"
                )
            ]);

            // Act
            var response = 
                await mediator.EnviarComandoAsync<InformarCienciaCommand, StatusDeSolicitacaoIntegracaoEnvioEmLoteDto>(command, CancellationToken.None);

            // Assert
            Assert.NotNull(response);
            response.ValidationResult.IsValid.ShouldBeTrue(response.ValidationResult.ToString());

            response.Solicitacoes
                .SelectMany(x => x.Erros)
                .ShouldBeEmpty();
            
            response.Solicitacoes
                .All(x => x.Status == StatusDeSolicitacao.CienciaInformada)
                .ShouldBeTrue();

            var canceladaKafkaPublished =
                await _dependencyInjectorFactory.Harness.Published.Any<StatusDeSolicitacaoIntegracaoRecebidaEvent>();
            
            var informarCienciaSolicitacaoNoFirebaseCommandPublished =
                await _dependencyInjectorFactory.Harness.Published.Any<EnviarTrocaDeStatusAoFirebaseCommand>();
            
            informarCienciaSolicitacaoNoFirebaseCommandPublished.ShouldBeTrue();
            canceladaKafkaPublished.ShouldBeTrue();
        }
    }
}
