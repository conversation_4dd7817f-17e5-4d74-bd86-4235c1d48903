using System.Linq.Expressions;
using FluentValidation.Results;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Repository.IRepository;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using ONS.SINapse.Solicitacao.Dtos.Integracao;
using ONS.SINapse.UnitTest.Shared.Fixtures;

namespace ONS.SINapse.UnitTest.Business.TrocaDeStatusSolicitacao.InformarCiencia;

public partial class InformarCienciaTest
{
    [Fact(DisplayName = "[Informar Ciência] - Deve retornar um erro em informar ciencia no firebase")]
    [Trait("Business", "Troca De Status Solicitacao")]
    public async Task InformarCiencia_DeveRetornarUmErro_FirebaseCommand()
    {
        // Arrange
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoImpedida("Motivo para informar Ci�ncia");
        var origem = solicitacao.Origem.Codigo;
        var usuario = UsuarioFixture.GerarUsuarioDtoValido();

        ConfigurarMockUserContext(usuario, origem);

        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoRepository>()
            .Setup(x => x.GetAsync(It.IsAny<Expression<Func<Entities.Entities.Solicitacao, bool>>>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync([solicitacao]);

        var result = new ValidationResult();
        result.AdicionarErro("Erro ao enviar command para o firebase");

        var mediatorMock = _dependencyInjectorFactory.Mocker.GetMock<IMediatorHandler>();

        mediatorMock
            .Setup(x => x.EnviarComandoAsync(It.IsAny<EnviarTrocaDeStatusAoFirebaseCommand>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(result);

        var command = new InformarCienciaCommand([
            new StatusDeSolicitacaoIntegracaoRecebimentoDto(
                solicitacao.Id,
                StatusDeSolicitacao.CienciaInformada,
                null,
                new ObjetoDeManobraDto(origem, origem),
                usuario,
                "SINapse"
            )
        ]);

        // Act
        var response =
            await mediatorMock.Object
                .EnviarComandoAsync<InformarCienciaCommand, StatusDeSolicitacaoIntegracaoEnvioEmLoteDto>(
                    command, CancellationToken.None);

        // Assert
        Assert.NotNull(response);

        response.Solicitacoes
            .All(x => x.Erros.Length > 0)
            .ShouldBeTrue();

        response.Solicitacoes
            .All(x => x.Status == StatusDeSolicitacao.Erro)
            .ShouldBeTrue();

        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoRepository>().Verify(
            repository => repository.GetAsync(It.IsAny<Expression<Func<Entities.Entities.Solicitacao, bool>>>(), It.IsAny<CancellationToken>()),
            Times.Once);

        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoRepository>().Verify(
            repository => repository.BulkUpdateAsync(It.IsAny<List<Entities.Entities.Solicitacao>>(), It.IsAny<CancellationToken>()),
            Times.Once);
        
        var informarCienciaSolicitacaoNoFirebaseCommandPublished =
            await _dependencyInjectorFactory.Harness.Published.Any<EnviarTrocaDeStatusAoFirebaseCommand>();
        
        informarCienciaSolicitacaoNoFirebaseCommandPublished.ShouldBeFalse();
    }
}