using Microsoft.Extensions.DependencyInjection;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Repository.IRepository;
using ONS.SINapse.Repository.IRepository.Firebase;

using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands.Firebase;
using ONS.SINapse.Solicitacao.Dtos.Integracao;
using ONS.SINapse.Solicitacao.EventHandlers.Events;
using ONS.SINapse.UnitTest.Shared.Fixtures;

namespace ONS.SINapse.UnitTest.Business.TrocaDeStatusSolicitacao.InformarCiencia;

public partial class InformarCienciaTest
{
    [Fact(DisplayName = "[Informar Ciência] - Deve retornar o erro 'usuário deve ser preenchido'")]
    [Trait("Business", "Troca De Status Solicitacao")]
    public async Task InformarCiencia_DeveRetornarUmErro_UsuarioDeveSerPreenchido()
    {
        // Arrange
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoConfirmada();
        var origem = solicitacao.Origem.Codigo;
        var usuario = UsuarioFixture.GerarUsuarioDtoValido();

   _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoFirebaseRepository>()
            .Setup(repository => repository.GetByIdAsync(It.IsAny<string[]>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([solicitacao]);

        ConfigurarMockUserContext(usuario, origem);

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        var command = new InformarCienciaCommand([
        new StatusDeSolicitacaoIntegracaoRecebimentoDto(
            solicitacao.Id,
            StatusDeSolicitacao.CienciaInformada,
            null,
            new ObjetoDeManobraDto(origem, origem),
            null,
            "SINapse"
            )
        ]);

        // Act
        var response = 
            await mediator.EnviarComandoAsync<InformarCienciaCommand, StatusDeSolicitacaoIntegracaoEnvioEmLoteDto>(command, CancellationToken.None);

        // Assert
        await ValidarInformarCienciaSolicitacao(response, "Usuario");
    }

    [Fact(DisplayName = "[Informar Ciência] - Deve retornar o erro 'nenhum centro encontrado'")]
    [Trait("Business", "Troca De Status Solicitacao")]
    public async Task InformarCiencia_DeveRetornarUmErro_NenhumCentroEncontrado()
    {
        // Arrange
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoConfirmada();
        var origem = solicitacao.Origem.Codigo;
        var usuario = UsuarioFixture.GerarUsuarioDtoValido();

        ConfigurarMockUserContext(usuario, origem);

   _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoFirebaseRepository>()
            .Setup(repository => repository.GetByIdAsync(It.IsAny<string[]>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([solicitacao]);

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        var command = new InformarCienciaCommand([
            new StatusDeSolicitacaoIntegracaoRecebimentoDto(
                solicitacao.Id,
                StatusDeSolicitacao.CienciaInformada,
                null,
                null,
                usuario,
                "SINapse"
            )
        ]);

        // Act
        var response = 
            await mediator.EnviarComandoAsync<InformarCienciaCommand, StatusDeSolicitacaoIntegracaoEnvioEmLoteDto>(command, CancellationToken.None);

        // Assert
        await ValidarInformarCienciaSolicitacao(response, "Centro");
    }

    private async Task ValidarInformarCienciaSolicitacao(StatusDeSolicitacaoIntegracaoEnvioEmLoteDto response, string campo)
    {   
        Assert.NotNull(response);

        response.Solicitacoes
            .SelectMany(x => x.Erros)
            .Any(x => x == $"Campo {campo} precisa ser preenchido.")
            .ShouldBeTrue();

        response.Solicitacoes
            .All(x => x.Erros.Length > 0)
            .ShouldBeTrue();

        response.Solicitacoes
            .All(x => x.Status == StatusDeSolicitacao.Erro)
            .ShouldBeTrue();

        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoRepository>().Verify(
            repository => repository.BulkUpdateAsync(It.IsAny<List<Entities.Entities.Solicitacao>>(), It.IsAny<bool>(), It.IsAny<CancellationToken>()),
            Times.Never);
            
        var cienciaInformadaComErroEventPublished =
            await _dependencyInjectorFactory.Harness.Published.Any<StatusDeSolicitacaoIntegracaoRecebidaEvent>();
            
        cienciaInformadaComErroEventPublished.ShouldBeTrue();

        var informarCienciaSolicitacaoNoFirebaseCommandPublished =
            await _dependencyInjectorFactory.Harness.Published.Any<TrocarDeStatusNoFirebaseCommand>();
            
        informarCienciaSolicitacaoNoFirebaseCommandPublished.ShouldBeFalse();
    }
    
}