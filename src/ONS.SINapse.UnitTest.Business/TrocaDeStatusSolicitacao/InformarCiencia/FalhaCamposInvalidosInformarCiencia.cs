using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Repository.IRepository;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using ONS.SINapse.Solicitacao.EventHandlers.Events;

namespace ONS.SINapse.UnitTest.Business.TrocaDeStatusSolicitacao.InformarCiencia
{
    public partial class InformarCienciaTest
    {
        [Fact(DisplayName = "[Informar Ciência] - Deve retornar o erro 'usuário deve ser preenchido'")]
        [Trait("Business", "Troca De Status Solicitacao")]
        public async Task FinalizarSolicitacao_DeveRetornarUmErro_UsuarioDeveSerPreenchido()
        {
            // Arrange
            var solicitacao = _solicitacaoFixture.GerarSolicitacaoConfirmada();
            var origem = solicitacao.Origem.Codigo;

            _dependencyInjectorFactory.Mocker.GetMock<IHttpContextAccessor>()
                .SetupGet(x => x.HttpContext)
                .Returns(() => null);

            var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

            var command = new InformarCienciaCommand(solicitacao.Id);
            command.DefinirCentros([origem]);
            command.DefinirViaApiIntegracao();

            // Act
            var response = await mediator.EnviarComandoAsync<InformarCienciaCommand, CienciaInformadaResultDto>(command, CancellationToken.None);

            // Assert
            await ValidarCancelarSolicitacao(response, "Usuario");
        }

        [Fact(DisplayName = "[Informar Ciência] - Deve retornar o erro 'nenhum centro encontrado'")]
        [Trait("Business", "Troca De Status Solicitacao")]
        public async Task FinalizarSolicitacao_DeveRetornarUmErro_NenhumCentroEncontrado()
        {
            // Arrange
            var solicitacao = _solicitacaoFixture.GerarSolicitacaoConfirmada();
            var origem = solicitacao.Origem.Codigo;

            _dependencyInjectorFactory.Mocker.GetMock<IHttpContextAccessor>()
                .SetupGet(x => x.HttpContext)
                .Returns(() => null);

            var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

            var command = new InformarCienciaCommand(solicitacao.Id);
            command.DefinirViaApiIntegracao();

            // Act
            var response = await mediator.EnviarComandoAsync<InformarCienciaCommand, CienciaInformadaResultDto>(command, CancellationToken.None);

            // Assert
            await ValidarCancelarSolicitacao(response, "Centros");
        }

        private async Task ValidarCancelarSolicitacao(CienciaInformadaResultDto response, string campo)
        {   
            Assert.NotNull(response);

            response.Erros.Any(x => x == $"Campo {campo} precisa ser preenchido.").ShouldBeTrue();

            response.Erros.ShouldNotBeEmpty();
            response.StatusId.ShouldBe((short)StatusDeSolicitacao.Erro);

            _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoRepository>().Verify(
                repository => repository.GetOneAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()),
                Times.Never);

            _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoRepository>().Verify(
                repository => repository.AtualizarAsync(It.IsAny<Entities.Entities.Solicitacao>(), It.IsAny<CancellationToken>()),
                Times.Never);
            
            var cienciaInformadaComErroEventPublished =
                await _dependencyInjectorFactory.Harness.Published.Any<CienciaInformadaComErroEvent>();
            
            cienciaInformadaComErroEventPublished.ShouldBeTrue();

            var informarCienciaSolicitacaoNoFirebaseCommandPublished =
                await _dependencyInjectorFactory.Harness.Published.Any<InformarCienciaSolicitacaoNoFirebaseCommand>();
            
            informarCienciaSolicitacaoNoFirebaseCommandPublished.ShouldBeFalse();

            var cienciaInformadaEventPublished =
                await _dependencyInjectorFactory.Harness.Published.Any<CienciaInformadaEvent>();
            
            cienciaInformadaEventPublished.ShouldBeFalse();
        }
    }
}
