using System.Collections;
using ONS.SINapse.Solicitacao.Dtos.Integracao;

namespace ONS.SINapse.UnitTest.Business.TrocaDeStatusSolicitacao;


public class StatusDeSolicitacaoIntegracaoCamposTest : IEnumerable<object[]>
{
    public IEnumerator<object[]> GetEnumerator()
    {
        yield return [new ValidacaoDeCamposStatusDeSolicitacaoAction(nameof(StatusDeSolicitacaoIntegracaoRecebimentoDto.Id))];
        yield return [new ValidacaoDeCamposStatusDeSolicitacaoAction(nameof(StatusDeSolicitacaoIntegracaoRecebimentoDto.Status))];
        yield return [new ValidacaoDeCamposStatusDeSolicitacaoAction(nameof(StatusDeSolicitacaoIntegracaoRecebimentoDto.Centro))];
        yield return [new ValidacaoDeCamposStatusDeSolicitacaoAction(nameof(StatusDeSolicitacaoIntegracaoRecebimentoDto.Usuario))];
    }
    
    IEnumerator IEnumerable.GetEnumerator() => GetEnumerator();

    private static void DefinirNullValue(StatusDeSolicitacaoIntegracaoRecebimentoDto source, string campo)
    {
        source.GetType()
            .GetProperty(campo)
            ?.SetValue(source, null);
    }
}

public class ValidacaoDeCamposStatusDeSolicitacaoAction
{
    public Action<StatusDeSolicitacaoIntegracaoRecebimentoDto> Acao { get; }
    public string Campo { get; }
    
    public ValidacaoDeCamposStatusDeSolicitacaoAction(string campo)
    {
        Acao = x => DefinirNullValue(x, campo);
        Campo = campo;
    }
    
    public override string ToString() => $"Campo {Campo} não preenchido";
    
    private static void DefinirNullValue(StatusDeSolicitacaoIntegracaoRecebimentoDto source, string campo)
    {
        source.GetType()
            .GetProperty(campo)
            ?.SetValue(source, null);
    }
}