using Microsoft.Extensions.DependencyInjection;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Repository.IRepository;
using ONS.SINapse.Repository.IRepository.Firebase;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using ONS.SINapse.Solicitacao.Dtos.Integracao;
using ONS.SINapse.Solicitacao.EventHandlers.Events;
using ONS.SINapse.UnitTest.Shared.Fixtures;

namespace ONS.SINapse.UnitTest.Business.TrocaDeStatusSolicitacao.ConfirmarEnvio;

public partial class ConfirmarEnvioSolicitacaoTest
{
    [Fact(DisplayName = "[ConfirmarEnvio] - Deve retornar o erro 'nenhuma solicitação encontrada'")]
    [Trait("Business", "Confirmar Envio")]
    public async Task CancelarSolicitacao_DeveRetornarUmErro_NenhumaSolicitacaoEncontrada()
    {
        // Arrange

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        var command = new ConfirmarEnvioEmLoteCommand([]);

        // Act
        var response =
            await mediator.EnviarComandoAsync<ConfirmarEnvioEmLoteCommand, StatusDeSolicitacaoIntegracaoEnvioEmLoteDto>(
                command, CancellationToken.None);

        // Assert

        response.ShouldNotBeNull();
        response.Solicitacoes.ShouldBeEmpty();

        var repositoryMock = _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoRepository>();

        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoFirebaseRepository>().Verify(
            repository => repository.GetByIdAsync(It.IsAny<string[]>(), It.IsAny<CancellationToken>()),
            Times.Never);

        var confirmarEnvioSolicitacaoFirebaseCommand =
            await _dependencyInjectorFactory.Harness.Published.Any<ConfirmarEnvioSolicitacaoFirebaseCommand>();

        var statusDeSolicitacaoIntegracaoRecebidaEvent =
            await _dependencyInjectorFactory.Harness.Published.Any<StatusDeSolicitacaoIntegracaoRecebidaEvent>();

        var solicitacaoConcluidaEvent =
            await _dependencyInjectorFactory.Harness.Published.Any<SolicitacaoConcluidaEvent>();

        confirmarEnvioSolicitacaoFirebaseCommand.ShouldBeFalse();
        statusDeSolicitacaoIntegracaoRecebidaEvent.ShouldBeFalse();
        solicitacaoConcluidaEvent.ShouldBeFalse();
    }

    [Fact(DisplayName = "[ConfirmarEnvio] - Deve retornar o erro 'usuário deve ser preenchido'")]
    [Trait("Business", "Confirmar Envio")]
    public async Task ConfirmarEnvio_DeveRetornarUmErro_UsuarioDeveSerPreenchido()
    {
        // Arrange
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoPendente();
        var origem = solicitacao.Origem.Codigo;

        _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoFirebaseRepository>()
            .Setup(repository => repository.GetByIdAsync(It.IsAny<string[]>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([solicitacao]);

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        var command = new ConfirmarEnvioEmLoteCommand([
            new StatusDeSolicitacaoIntegracaoRecebimentoDto(
                solicitacao.Id,
                solicitacao.Status,
                null,
                new ObjetoDeManobraDto(origem, origem),
                null,
                "SINapse"
            )
        ]);

        // Act
        var response =
            await mediator.EnviarComandoAsync<ConfirmarEnvioEmLoteCommand, StatusDeSolicitacaoIntegracaoEnvioEmLoteDto>(
                command, CancellationToken.None);

        // Assert
        await ValidarConfirmarEnvioSolicitacao(response, "Usuario");
    }

    [Fact(DisplayName = "[ConfirmarEnvio] - Deve retornar o erro 'nenhum centro encontrado'")]
    [Trait("Business", "Confirmar Envio")]
    public async Task ConfirmarEnvio_DeveRetornarUmErro_NenhumCentroEncontrado()
    {
        // Arrange

        var solicitacao = _solicitacaoFixture.GerarSolicitacaoPendente();

        var usuario = UsuarioFixture.GerarUsuarioDtoValido();

        _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoFirebaseRepository>()
            .Setup(repository => repository.GetByIdAsync(It.IsAny<string[]>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([solicitacao]);

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        var command = new ConfirmarEnvioEmLoteCommand([
            new StatusDeSolicitacaoIntegracaoRecebimentoDto(
                solicitacao.Id,
                solicitacao.Status,
                null,
                null,
                usuario,
                "SINapse"
            )
        ]);

        // Act

        var response =
            await mediator.EnviarComandoAsync<ConfirmarEnvioEmLoteCommand, StatusDeSolicitacaoIntegracaoEnvioEmLoteDto>(
                command, CancellationToken.None);

        // Assert

        await ValidarConfirmarEnvioSolicitacao(response, "Centro");
    }

    private async Task ValidarConfirmarEnvioSolicitacao(StatusDeSolicitacaoIntegracaoEnvioEmLoteDto response,
        string campo)
    {
        Assert.NotNull(response);

        response.Solicitacoes
            .SelectMany(x => x.Erros)
            .Any(x => x == $"Campo {campo} precisa ser preenchido.")
            .ShouldBeTrue();

        response.Solicitacoes
            .All(x => x.Erros.Length > 0)
            .ShouldBeTrue();

        response.Solicitacoes
            .All(x => x.Status == StatusDeSolicitacao.Erro)
            .ShouldBeTrue();

        var confirmarEnvioSolicitacaoFirebaseCommand =
            await _dependencyInjectorFactory.Harness.Published.Any<ConfirmarEnvioSolicitacaoFirebaseCommand>();

        var statusDeSolicitacaoIntegracaoRecebidaEvent =
            await _dependencyInjectorFactory.Harness.Published.Any<StatusDeSolicitacaoIntegracaoRecebidaEvent>();

        var solicitacaoConcluidaEvent =
            await _dependencyInjectorFactory.Harness.Published.Any<SolicitacaoConcluidaEvent>();

        confirmarEnvioSolicitacaoFirebaseCommand.ShouldBeFalse();
        statusDeSolicitacaoIntegracaoRecebidaEvent.ShouldBeTrue();
        solicitacaoConcluidaEvent.ShouldBeFalse();
    }
}
