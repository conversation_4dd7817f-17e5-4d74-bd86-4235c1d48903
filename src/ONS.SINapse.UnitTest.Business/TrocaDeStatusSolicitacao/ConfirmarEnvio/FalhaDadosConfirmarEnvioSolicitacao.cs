using Microsoft.Extensions.DependencyInjection;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Repository.IRepository.Firebase;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using ONS.SINapse.Solicitacao.Dtos.Integracao;
using ONS.SINapse.Solicitacao.EventHandlers.Events;
using ONS.SINapse.UnitTest.Shared.Fixtures;

namespace ONS.SINapse.UnitTest.Business.TrocaDeStatusSolicitacao.ConfirmarEnvio;

public partial class ConfirmarEnvioSolicitacaoTest
{
    [Fact(DisplayName = "[ConfirmarEnvio] - Deve retornar erro quando solicitação não existir")]
    [Trait("Business", "ConfirmarEnvio")]
    public async Task ConfirmarEnvio_DeveRetornarErro_SolicitacaoNaoExiste()
    {
        // Arrange
        var objetoDeManobra = _objetoManobraFixture.GerarObjetoDeManobra("CN", "CNOS");
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoAguardandoEnvio(objetoDeManobra);
        var origem = solicitacao.Origem.Codigo;
        var usuario = UsuarioFixture.GerarUsuarioDtoValido();

        ConfigurarMockUserContext(usuario, origem);

        _dependencyInjectorFactory.Mocker
                 .GetMock<ISolicitacaoFirebaseRepository>()
                 .Setup(repository => repository.GetByIdAsync(It.IsAny<string[]>(), It.IsAny<CancellationToken>()))
                 .ReturnsAsync([]);

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        var command = new ConfirmarEnvioEmLoteCommand([
           new StatusDeSolicitacaoIntegracaoRecebimentoDto(
                solicitacao.Id,
                StatusDeSolicitacao.Enviada,
                null,
                new ObjetoDeManobraDto(objetoDeManobra.Codigo, objetoDeManobra.Nome),
                usuario,
                "Teste"
           )
        ]);

        // Act
        var response = await mediator.EnviarComandoAsync<ConfirmarEnvioEmLoteCommand, StatusDeSolicitacaoIntegracaoEnvioEmLoteDto>(command, CancellationToken.None);

        // Assert
        await ValidarDadosConfirmarEnvio(response, 
            $"Não foi possível alterar o status da solicitação {solicitacao.Id} para {StatusDeSolicitacao.Enviada.GetDescription()}. Solicitação não existe.");
    }

    [Fact(DisplayName = "[ConfirmarEnvio] - Deve retornar erro quando solicitante for inválido")]
    [Trait("Business", "ConfirmarEnvio")]
    public async Task ConfirmarEnvio_DeveRetornarErro_SolicitanteInvaido()
    {
        // Arrange
        var objetoDeManobra = _objetoManobraFixture.GerarObjetoDeManobra("CN", "CNOS");
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoAguardandoEnvio(objetoDeManobra);
        var origem = solicitacao.Origem.Codigo;
        var usuario = UsuarioFixture.GerarUsuarioDtoValido();

        ConfigurarMockUserContext(usuario, origem);

        _dependencyInjectorFactory.Mocker
                 .GetMock<ISolicitacaoFirebaseRepository>()
                 .Setup(repository => repository.GetByIdAsync(It.IsAny<string[]>(), It.IsAny<CancellationToken>()))
                 .ReturnsAsync([solicitacao]);

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        var command = new ConfirmarEnvioEmLoteCommand([
           new StatusDeSolicitacaoIntegracaoRecebimentoDto(
                solicitacao.Id,
                StatusDeSolicitacao.Enviada,
                null,
                new ObjetoDeManobraDto("NE", "CORS-NE"),
                usuario,
                "Teste"
           )
        ]);

        // Act
        var response = await mediator.EnviarComandoAsync<ConfirmarEnvioEmLoteCommand, StatusDeSolicitacaoIntegracaoEnvioEmLoteDto>(command, CancellationToken.None);

        // Assert
        await ValidarDadosConfirmarEnvio(response,
            $"Não foi possível alterar o status da solicitação {solicitacao.Id} para {StatusDeSolicitacao.Enviada.GetDescription()}. Operação não permitida, usuário não é o solicitante.");
    }

    [Fact(DisplayName = "[ConfirmarEnvio] - Deve retornar erro quando status for inválido")]
    [Trait("Business", "ConfirmarEnvio")]
    public async Task ConfirmarEnvio_DeveRetornarErro_StatusInvalido()
    {
        // Arrange
        var objetoDeManobra = _objetoManobraFixture.GerarObjetoDeManobra("CN", "CNOS");
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoPendente(objetoDeManobra);
        var origem = solicitacao.Origem.Codigo;
        var usuario = UsuarioFixture.GerarUsuarioDtoValido();

        ConfigurarMockUserContext(usuario, origem);

        _dependencyInjectorFactory.Mocker
                 .GetMock<ISolicitacaoFirebaseRepository>()
                 .Setup(repository => repository.GetByIdAsync(It.IsAny<string[]>(), It.IsAny<CancellationToken>()))
                 .ReturnsAsync([solicitacao]);

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        var command = new ConfirmarEnvioEmLoteCommand([
           new StatusDeSolicitacaoIntegracaoRecebimentoDto(
                solicitacao.Id,
                StatusDeSolicitacao.Enviada,
                null,
                new ObjetoDeManobraDto("NE", "CORS-NE"),
                usuario,
                "Teste"
           )
        ]);

        // Act
        var response = await mediator.EnviarComandoAsync<ConfirmarEnvioEmLoteCommand, StatusDeSolicitacaoIntegracaoEnvioEmLoteDto>(command, CancellationToken.None);

        // Assert
        await ValidarDadosConfirmarEnvio(response,
            $"Não foi possível alterar o status da solicitação {solicitacao.Id} para {StatusDeSolicitacao.Enviada.GetDescription()}. Solicitação se encontra com status {solicitacao.Status.GetDescription()}.");
    }

    private async Task ValidarDadosConfirmarEnvio(StatusDeSolicitacaoIntegracaoEnvioEmLoteDto response, string message)
    {
        response.ShouldNotBeNull();

        var erros = response.Solicitacoes
            .SelectMany(x => x.Erros)
            .ToList();

        erros.Any(x => x.Contains(message)).ShouldBeTrue();

        erros.ShouldNotBeEmpty();

        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoFirebaseRepository>().Verify(
            repository => repository.GetByIdAsync(It.IsAny<string[]>(), It.IsAny<CancellationToken>()),
            Times.Once);

        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoFirebaseRepository>().Verify(
            repository => repository.AddUpdateAsync(It.IsAny<Dictionary<string, object?>>(), It.IsAny<CancellationToken>()),
            Times.Never);

        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoFirebaseRepository>().Verify(
            repository => repository.AddUpdateAsync(It.IsAny<List<Entities.Entities.Solicitacao>>(), It.IsAny<CancellationToken>()),
            Times.Never);

        var StatusDeSolicitacaoIntegracaoRecebidaEventPublished =
            await _dependencyInjectorFactory.Harness.Published.Any<StatusDeSolicitacaoIntegracaoRecebidaEvent>();

        var confirmarEnvioSolicitacaoFirebaseCommandPublished =
            await _dependencyInjectorFactory.Harness.Published.Any<ConfirmarEnvioSolicitacaoFirebaseCommand>();

        StatusDeSolicitacaoIntegracaoRecebidaEventPublished.ShouldBeTrue();
        confirmarEnvioSolicitacaoFirebaseCommandPublished.ShouldBeFalse();
    }
}
