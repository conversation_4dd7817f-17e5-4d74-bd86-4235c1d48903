using Microsoft.Extensions.DependencyInjection;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Repository.IRepository.Firebase;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using ONS.SINapse.Solicitacao.Dtos.Integracao;
using ONS.SINapse.Solicitacao.EventHandlers.Events;
using ONS.SINapse.UnitTest.Shared.Fixtures;

namespace ONS.SINapse.UnitTest.Business.TrocaDeStatusSolicitacao.ConfirmarEnvio;

public partial class ConfirmarEnvioSolicitacaoTest
{
    [Fact(DisplayName = "[ConfirmarEnvio] - Deve confirmar envio de solicitacao")]
    [Trait("Business", "Confirmar Envio")]
    public async Task DeveConfirmarEnvioSolicitacao()
    {
        // Arrange
        var objetoDeManobraOrigem = _objetoManobraFixture.GerarObjetoDeManobra("CN", "CNOS");
        var objetoDeManobraDestino = _objetoManobraFixture.GerarObjetoDeManobra("NE", "COSR-NE");
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoAguardandoEnvio(objetoDeManobraOrigem, objetoDeManobraDestino);
        
        var usuario = UsuarioFixture.GerarUsuarioDtoValido();

        ConfigurarMockUserContext(usuario, objetoDeManobraDestino.Codigo);

        _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoFirebaseRepository>()
            .Setup(repository => repository.GetByIdAsync(It.IsAny<string[]>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([solicitacao]);

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        var command = new ConfirmarEnvioEmLoteCommand([
           new StatusDeSolicitacaoIntegracaoRecebimentoDto(
                solicitacao.Id,
                StatusDeSolicitacao.Enviada,
                null,
                new ObjetoDeManobraDto(objetoDeManobraOrigem.Codigo, objetoDeManobraOrigem.Nome),
                usuario,
                "Teste"
           )
       ]);

        // Act
        var response = await mediator.EnviarComandoAsync<ConfirmarEnvioEmLoteCommand, StatusDeSolicitacaoIntegracaoEnvioEmLoteDto>(command, CancellationToken.None);

        // Assert
        Assert.NotNull(response);
        response.ValidationResult.IsValid.ShouldBeTrue(response.ToString());

        response.ValidationResult.Errors.ShouldBeEmpty();

        var confirmarEnvioSolicitacaoFirebaseCommand =
            await _dependencyInjectorFactory.Harness.Published.Any<ConfirmarEnvioSolicitacaoFirebaseCommand>();

        var statusDeSolicitacaoIntegracaoRecebidaEvent =
            await _dependencyInjectorFactory.Harness.Published.Any<StatusDeSolicitacaoIntegracaoRecebidaEvent>();

        var solicitacaoConcluidaEvent =
            await _dependencyInjectorFactory.Harness.Published.Any<SolicitacaoConcluidaEvent>();

        confirmarEnvioSolicitacaoFirebaseCommand.ShouldBeTrue();
        statusDeSolicitacaoIntegracaoRecebidaEvent.ShouldBeTrue();
        solicitacaoConcluidaEvent.ShouldBeTrue();

        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoFirebaseRepository>().Verify(
            repository => repository.GetByIdAsync(It.IsAny<string[]>(), It.IsAny<CancellationToken>()),
            Times.AtLeastOnce());
    }
}
