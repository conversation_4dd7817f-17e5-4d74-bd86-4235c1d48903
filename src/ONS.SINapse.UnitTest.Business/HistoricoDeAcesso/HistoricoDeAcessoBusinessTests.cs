using System.Linq.Expressions;
using ONS.SINapse.Business.Imp.Business;
using ONS.SINapse.Entities.Entities;
using ONS.SINapse.Repository.IRepository;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Notifications;

namespace ONS.SINapse.UnitTest.Business.HistoricoDeAcesso;

public partial class HistoricoDeAcessoBusinessTests
{
    [Fact(DisplayName = "Deve retornar histórico de acesso")]
    [Trait("Business", "Histórico de Acesso")]
    public async Task DeveRetornarHistoricoDeAcesso()
    {
        // Arrange
        var filtro = new FiltroHistoricoDeAcesso
        {
            Inicio = DateTime.Today.AddDays(-30),
            Fim = DateTime.Today,
            CentroAgente = "NE"
        };

        var historicoList = new List<HistoricoAcesso>
        {
            new("id1", new ObjetoDeManobra("NE", "CORS-NE"), DateTime.Today.AddDays(-20), DateTime.Today.AddDays(-10)),
            new("id2", new ObjetoDeManobra("NE", "CORS-NE"), DateTime.Today.AddDays(-5), null)
        };

        _dependencyInjectorFactory.Mocker.GetMock<IHistoricoDeAcessoRepository>().Setup(r => r.GetAsync(
                It.IsAny<Expression<Func<HistoricoAcesso, bool>>>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(historicoList);

        // Act
        var resultado = await _dependencyInjectorFactory.Mocker.CreateInstance<HistoricoDeAcessoBusiness>().ObterHistoricoDeAcessoAsync(filtro, CancellationToken.None);

        // Assert
        resultado.ShouldNotBeNull();
        resultado.Count().ShouldBe(2);
        _dependencyInjectorFactory.Mocker.GetMock<IHistoricoDeAcessoRepository>().Verify(
            r => r.GetAsync(It.IsAny<Expression<Func<HistoricoAcesso, bool>>>(), It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact(DisplayName = "Deve retornar lista vazia quando não existirem registros")]
    [Trait("Business", "Histórico de Acesso")]
    public async Task DeveRetornarListaVaziaQuandoNaoExistiremRegistros()
    {
        // Arrange
        var filtro = new FiltroHistoricoDeAcesso
        {
            Inicio = DateTime.Today.AddDays(-30),
            Fim = DateTime.Today,
            CentroAgente = "NE"
        };

        _dependencyInjectorFactory.Mocker.GetMock<IHistoricoDeAcessoRepository>().Setup(r => r.GetAsync(
                It.IsAny<Expression<Func<HistoricoAcesso, bool>>>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<HistoricoAcesso>());

        // Act
        var resultado = await _dependencyInjectorFactory.Mocker.CreateInstance<HistoricoDeAcessoBusiness>().ObterHistoricoDeAcessoAsync(filtro, CancellationToken.None);

        // Assert
        resultado.ShouldNotBeNull();
        resultado.ShouldBeEmpty();
        _dependencyInjectorFactory.Mocker.GetMock<IHistoricoDeAcessoRepository>().Verify(
            r => r.GetAsync(It.IsAny<Expression<Func<HistoricoAcesso, bool>>>(), It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact(DisplayName = "Deve retornar lista vazia quando filtro tem data inicio maior que data fim")]
    [Trait("Business", "Histórico de Acesso")]
    public async Task DeveRetornarListaVaziaQuandoFiltroTemDataInicioMaiorQueDataFim()
    {
        // Arrange
        var filtro = new FiltroHistoricoDeAcesso
        {
            Inicio = DateTime.Today,
            Fim = DateTime.Today.AddDays(-30),
            CentroAgente = "NE"
        };

        // Act
        var resultado = await _dependencyInjectorFactory.Mocker.CreateInstance<HistoricoDeAcessoBusiness>().ObterHistoricoDeAcessoAsync(filtro, CancellationToken.None);

        // Assert
        resultado.ShouldNotBeNull();
        resultado.ShouldBeEmpty();
        _dependencyInjectorFactory.Mocker.Get<NotificationContext>().HasNotifications.ShouldBeTrue();
        _dependencyInjectorFactory.Mocker.Get<NotificationContext>().Notifications.First().Message.ShouldBe("Data de inicio não pode ser maior que a data fim.");
        _dependencyInjectorFactory.Mocker.GetMock<IHistoricoDeAcessoRepository>().Verify(
            r => r.GetAsync(It.IsAny<Expression<Func<HistoricoAcesso, bool>>>(), It.IsAny<CancellationToken>()),
            Times.Never);
    }

    [Fact(DisplayName = "Deve criar filtro padrão quando filtro for nulo")]
    [Trait("Business", "Histórico de Acesso")]
    public async Task DeveCriarFiltroPadraoQuandoFiltroForNulo()
    {
        // Arrange
        FiltroHistoricoDeAcesso? filtro = null;

        _dependencyInjectorFactory.Mocker.GetMock<IHistoricoDeAcessoRepository>().Setup(r => r.GetAsync(
                It.IsAny<Expression<Func<HistoricoAcesso, bool>>>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<HistoricoAcesso>());

        // Act
        var resultado = await _dependencyInjectorFactory.Mocker.CreateInstance<HistoricoDeAcessoBusiness>().ObterHistoricoDeAcessoAsync(filtro, CancellationToken.None);

        // Assert
        resultado.ShouldNotBeNull();
        resultado.ShouldBeEmpty();
        _dependencyInjectorFactory.Mocker.GetMock<IHistoricoDeAcessoRepository>().Verify(
            r => r.GetAsync(It.IsAny<Expression<Func<HistoricoAcesso, bool>>>(), It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact(DisplayName = "Deve filtrar apenas por centro agente quando datas forem nulas")]
    [Trait("Business", "Histórico de Acesso")]
    public async Task DeveFiltrarApenasPorCentroAgenteQuandoDatasForemNulas()
    {
        // Arrange
        var filtro = new FiltroHistoricoDeAcesso
        {
            Inicio = null,
            Fim = null,
            CentroAgente = "NE"
        };

        var historicoList = new List<HistoricoAcesso>
        {
            new("id1", new ObjetoDeManobra("NE", "CORS-NE"), DateTime.Today.AddDays(-20), DateTime.Today.AddDays(-10))
        };

        _dependencyInjectorFactory.Mocker.GetMock<IHistoricoDeAcessoRepository>().Setup(r => r.GetAsync(
                It.IsAny<Expression<Func<HistoricoAcesso, bool>>>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(historicoList);

        // Act
        var resultado = await _dependencyInjectorFactory.Mocker.CreateInstance<HistoricoDeAcessoBusiness>().ObterHistoricoDeAcessoAsync(filtro, CancellationToken.None);

        // Assert
        resultado.ShouldNotBeNull();
        resultado.Count().ShouldBe(1);
        _dependencyInjectorFactory.Mocker.GetMock<IHistoricoDeAcessoRepository>().Verify(
            r => r.GetAsync(It.IsAny<Expression<Func<HistoricoAcesso, bool>>>(), It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact(DisplayName = "Deve calcular duração para históricos com fim definido")]
    [Trait("Business", "Histórico de Acesso")]
    public async Task DeveCalcularDuracaoParaHistoricosComFimDefinido()
    {
        // Arrange
        var filtro = new FiltroHistoricoDeAcesso
        {
            Inicio = DateTime.Today.AddDays(-30),
            Fim = DateTime.Today
        };

        var dataInicio = DateTime.Today.AddDays(-20);
        var dataFim = DateTime.Today.AddDays(-10);
        var duracaoEsperada = dataFim - dataInicio;

        var historicoList = new List<HistoricoAcesso>
        {
            new("id1", new ObjetoDeManobra("NE", "CORS-NE"), dataInicio, dataFim)
        };

        _dependencyInjectorFactory.Mocker.GetMock<IHistoricoDeAcessoRepository>().Setup(r => r.GetAsync(
                It.IsAny<Expression<Func<HistoricoAcesso, bool>>>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(historicoList);

        // Act
        var resultado = await _dependencyInjectorFactory.Mocker.CreateInstance<HistoricoDeAcessoBusiness>().ObterHistoricoDeAcessoAsync(filtro, CancellationToken.None);

        // Assert
        resultado.ShouldNotBeNull();
        resultado.Count().ShouldBe(1);
        var historicoResultado = resultado.First();
        historicoResultado.Duracao.ShouldBe(duracaoEsperada);
    }

    [Fact(DisplayName = "Deve calcular duração até o momento atual para históricos sem fim definido")]
    [Trait("Business", "Histórico de Acesso")]
    public async Task DeveCalcularDuracaoAteOMomentoAtualParaHistoricosSemFimDefinido()
    {
        // Arrange
        var filtro = new FiltroHistoricoDeAcesso
        {
            Inicio = DateTime.Today.AddDays(-30),
            Fim = DateTime.Today
        };

        var dataInicio = DateTime.Today.AddDays(-5);

        var historicoList = new List<HistoricoAcesso>
        {
            new("id1", new ObjetoDeManobra("NE", "CORS-NE"), dataInicio, null)
        };

        _dependencyInjectorFactory.Mocker.GetMock<IHistoricoDeAcessoRepository>().Setup(r => r.GetAsync(
                It.IsAny<Expression<Func<HistoricoAcesso, bool>>>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(historicoList);

        // Act
        var resultado = await _dependencyInjectorFactory.Mocker.CreateInstance<HistoricoDeAcessoBusiness>().ObterHistoricoDeAcessoAsync(filtro, CancellationToken.None);

        // Assert
        resultado.ShouldNotBeNull();
        resultado.Count().ShouldBe(1);
        var historicoResultado = resultado.First();
        historicoResultado.Duracao.ShouldNotBeNull();
        historicoResultado.Duracao.Value.TotalDays.ShouldBeGreaterThanOrEqualTo(4);
    }
}