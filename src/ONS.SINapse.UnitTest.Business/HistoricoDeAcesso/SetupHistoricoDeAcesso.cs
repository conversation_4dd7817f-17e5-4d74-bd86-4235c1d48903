using ONS.SINapse.UnitTest.Business.Fixtures.Collections;
using ONS.SINapse.UnitTest.Shared;

namespace ONS.SINapse.UnitTest.Business.HistoricoDeAcesso;

[Collection(nameof(HistoricoDeAcessoCollection))]
public partial class HistoricoDeAcessoBusinessTests
{
    private readonly MockDependencyInjectorFactory _dependencyInjectorFactory;

    public HistoricoDeAcessoBusinessTests()
    {
        _dependencyInjectorFactory = new MockDependencyInjectorFactory();
        _dependencyInjectorFactory.RegisterMocks();
    }
}