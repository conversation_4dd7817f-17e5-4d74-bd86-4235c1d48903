using ONS.SINapse.Business.Imp.Business;
using ONS.SINapse.UnitTest.Business.Fixtures.Collections;
using ONS.SINapse.UnitTest.Shared;
using ONS.SINapse.UnitTest.Shared.Fixtures;
using System.Reflection;

namespace ONS.SINapse.UnitTest.Business.AgenteOnline;

[Collection(nameof(AgenteOnlineCollection))]
public partial class AgenteOnlineTest
{
    private readonly MockDependencyInjectorFactory _dependencyInjectorFactory;
    private readonly RespostaDaChamadaFixture _respostaDaChamadaFixture;

    public AgenteOnlineTest(RespostaDaChamadaFixture respostaDaChamadaFixture)
    {
        _respostaDaChamadaFixture = respostaDaChamadaFixture;
        _dependencyInjectorFactory = new MockDependencyInjectorFactory();
        _dependencyInjectorFactory.RegisterMocks();
    }

    private static void SetDisposeFieldToTrue(AgenteOnlineBusiness instance)
    {
        var type = instance.GetType();
        var fieldInfo = type.GetField("_disposed", BindingFlags.NonPublic | BindingFlags.Instance);

        fieldInfo!.SetValue(instance, true);
    }
}