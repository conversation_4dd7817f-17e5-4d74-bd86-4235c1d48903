using ONS.SINapse.Business.IBusiness;
using ONS.SINapse.Business.Imp.Business;

namespace ONS.SINapse.UnitTest.Business.AgenteOnline;

public partial class AgenteOnlineTest
{
    [Fact(DisplayName = "[Agente Online] - Deve obter agentes online")]
    [Trait("Business", "Agente Online")]
    public async Task DeveObterAgentesOnline()
    {
        // Arrange

        var respostaDasChamadas = new List<Entities.Entities.RespostaDaChamada>()
        {
            _respostaDaChamadaFixture.GeraRespostaDaChamada()
        };

        _dependencyInjectorFactory.Mocker
            .GetMock<IRespostaDaChamadaBusiness>()
            .Setup(repository => repository.ObterAgentesOnline(It.IsAny<CancellationToken>()))
            .ReturnsAsync(respostaDasChamadas);

        // Act
        var resposta = await _dependencyInjectorFactory.Mocker.CreateInstance<AgenteOnlineBusiness>().ObterAgentesOnlineAsync(CancellationToken.None);

        // Assert

        resposta.ShouldNotBeEmpty();
        resposta.ShouldContain(item =>
            item.Codigo == respostaDasChamadas.First().CentroAgente.Codigo &&
            item.Locais == respostaDasChamadas.First().Equipamentos);

        _dependencyInjectorFactory.Mocker.GetMock<IRespostaDaChamadaBusiness>().Verify(
            repository => repository.ObterAgentesOnline(It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact(DisplayName = "[Agente Online] - Deve retornar lista vazia ao obter agentes online")]
    [Trait("Business", "Agente Online")]
    public async Task DeveRetornarListaVaziaAoObterAgentesOnline()
    {
        // Arrange

        var respostaDasChamadas = new List<Entities.Entities.RespostaDaChamada>();

        _dependencyInjectorFactory.Mocker
            .GetMock<IRespostaDaChamadaBusiness>()
            .Setup(repository => repository.ObterAgentesOnline(It.IsAny<CancellationToken>()))
            .ReturnsAsync(respostaDasChamadas);

        // Act
        var resposta = await _dependencyInjectorFactory.Mocker.CreateInstance<AgenteOnlineBusiness>().ObterAgentesOnlineAsync(CancellationToken.None);

        // Assert

        resposta.ShouldBeEmpty();

        _dependencyInjectorFactory.Mocker.GetMock<IRespostaDaChamadaBusiness>().Verify(
            repository => repository.ObterAgentesOnline(It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact(DisplayName = "[Agente Online] - Deve obter códigos dos agentes online")]
    [Trait("Business", "Agente Online")]
    public async Task DeveObterCodigoAgentesOnline()
    {
        // Arrange

        _dependencyInjectorFactory.Mocker
            .GetMock<IRespostaDaChamadaBusiness>()
            .Setup(repository => repository.ObterCodigoAgentesOnlineAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(["AA"]);

        // Act
        var resposta = await _dependencyInjectorFactory.Mocker.CreateInstance<AgenteOnlineBusiness>().ObterCodigoAgentesOnlineAsync(CancellationToken.None);

        // Assert

        resposta.ShouldNotBeEmpty();

        _dependencyInjectorFactory.Mocker.GetMock<IRespostaDaChamadaBusiness>().Verify(
            repository => repository.ObterCodigoAgentesOnlineAsync(It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact(DisplayName = "[Agente Online] -  Deve retornar lista vazia ao obter códigos dos agentes online")]
    [Trait("Business", "Agente Online")]
    public async Task DeveRetornarListaVaziaAoObterCodigoAgentesOnline()
    {
        // Arrange

        _dependencyInjectorFactory.Mocker
            .GetMock<IRespostaDaChamadaBusiness>()
            .Setup(repository => repository.ObterCodigoAgentesOnlineAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        // Act
        var resposta = await _dependencyInjectorFactory.Mocker.CreateInstance<AgenteOnlineBusiness>().ObterCodigoAgentesOnlineAsync(CancellationToken.None);

        // Assert

        resposta.ShouldBeEmpty();

        _dependencyInjectorFactory.Mocker.GetMock<IRespostaDaChamadaBusiness>().Verify(
            repository => repository.ObterCodigoAgentesOnlineAsync(It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact(DisplayName = "[Agente Online] - Deve buscar agentes inativos")]
    [Trait("Business", "Agente Online")]
    public async Task DeveBuscarAgentesInativos()
    {
        // Arrange

        _dependencyInjectorFactory.Mocker
            .GetMock<IRespostaDaChamadaBusiness>()
            .Setup(repository => repository.ObterCentroAgentesInativos(It.IsAny<string[]>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(["AA"]);

        // Act
        var resposta = await _dependencyInjectorFactory.Mocker.CreateInstance<AgenteOnlineBusiness>().BuscarAgentesInativosAsync(["AA"], CancellationToken.None);

        // Assert

        resposta.ShouldNotBeEmpty();

        _dependencyInjectorFactory.Mocker.GetMock<IRespostaDaChamadaBusiness>().Verify(
            repository => repository.ObterCentroAgentesInativos(It.IsAny<string[]>(), It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact(DisplayName = "[Agente Online] - Deve retornar lista vazia ao buscar agentes inativos")]
    [Trait("Business", "Agente Online")]
    public async Task DeveRetornarListaVaziaAoBuscarAgentesInativos()
    {
        // Arrange

        _dependencyInjectorFactory.Mocker
            .GetMock<IRespostaDaChamadaBusiness>()
            .Setup(repository => repository.ObterCentroAgentesInativos(It.IsAny<string[]>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        // Act
        var resposta = await _dependencyInjectorFactory.Mocker.CreateInstance<AgenteOnlineBusiness>().BuscarAgentesInativosAsync(["AA"], CancellationToken.None);

        // Assert

        resposta.ShouldBeEmpty();

        _dependencyInjectorFactory.Mocker.GetMock<IRespostaDaChamadaBusiness>().Verify(
            repository => repository.ObterCentroAgentesInativos(It.IsAny<string[]>(), It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact(DisplayName = "[Agente Online] - Deve executar o dispose")]
    [Trait("Business", "Agente Online")]
    public async Task DeveExecutarDispose()
    {
        // Arrange e Act
        var exception = Record.Exception(() => _dependencyInjectorFactory.Mocker.CreateInstance<AgenteOnlineBusiness>().Dispose());

        // Assert

        exception.ShouldBeNull();
    }

    [Fact(DisplayName = "[Agente Online] - Não deve executar o dispose")]
    [Trait("Business", "Agente Online")]
    public async Task NaoDeveExecutarDispose()
    {
        // Arrange

        var instance = _dependencyInjectorFactory.Mocker.CreateInstance<AgenteOnlineBusiness>();
        SetDisposeFieldToTrue(instance);

        // Act
        var exception = Record.Exception(() => instance.Dispose());

        // Assert

        exception.ShouldBeNull();
    }
}