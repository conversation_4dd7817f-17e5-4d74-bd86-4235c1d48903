using ONS.SINapse.UnitTest.Business.Fixtures.Collections;
using ONS.SINapse.UnitTest.Shared;
using ONS.SINapse.UnitTest.Shared.Fixtures;

namespace ONS.SINapse.UnitTest.Business.AgenteIntegracao;

[Collection(nameof(AgenteIntegracaoCollection))]
public partial class AgenteIntegracaoTest
{
    private readonly MockDependencyInjectorFactory _dependencyInjectorFactory;
    private readonly AgenteFixture _agenteFixture;

    public AgenteIntegracaoTest(AgenteFixture agenteFixture)
    {
        _agenteFixture = agenteFixture;
        _dependencyInjectorFactory = new MockDependencyInjectorFactory();
        _dependencyInjectorFactory.RegisterMocks();
    }
}