using ONS.SINapse.Business.IBusiness;
using ONS.SINapse.Business.Imp.Business.Integracao;
using ONS.SINapse.Entities.Entities.DadosCadastrais;
using ONS.SINapse.Integracao.Shared.Dtos;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Repository.IRepository.DadosCadastrais;
using ONS.SINapse.Shared.DTO;
using System.Linq.Expressions;

namespace ONS.SINapse.UnitTest.Business.AgenteIntegracao;

public partial class AgenteIntegracaoTest
{
    [Fact(DisplayName = "[Agente Integração] - Deve listar agentes online com o status ativo")]
    [Trait("Business", "Agente Integração")]
    public async Task DeveListarAgentesOnlineStatusAtivo()
    {
        // Arrange

        var agenteOnlineIntegracaoDto = new AgentesOnlineIntegracaoDto(new[] { "CO" });
        var agentes = new List<Agente>()
        {
            _agenteFixture.GerarAgente()
        };

        _dependencyInjectorFactory.Mocker
            .GetMock<IAgenteRepository>()
            .Setup(repository => repository.GetAsync(It.IsAny<Expression<Func<Agente, bool>>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(agentes);

        _dependencyInjectorFactory.Mocker
            .GetMock<IAgenteOnlineBusiness>()
            .Setup(business => business.BuscarAgentesInativosAsync(It.IsAny<string[]>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        _dependencyInjectorFactory.Mocker
            .GetMock<IAgenteOnlineBusiness>()
            .Setup(business => business.ObterAgentesOnlineAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<AgenteOnlineDto>()
            {
                new ("CO", ["LO"])
            });

        // Act
        var resposta = await _dependencyInjectorFactory.Mocker.CreateInstance<AgenteIntegracaoBusiness>().GetAgentesOnlineAsync(agenteOnlineIntegracaoDto, CancellationToken.None);

        // Assert

        resposta.Agentes.ShouldNotBeEmpty();

        _dependencyInjectorFactory.Mocker.GetMock<IAgenteRepository>().Verify(
            repository => repository.GetAsync(It.IsAny<Expression<Func<Agente, bool>>>(), It.IsAny<CancellationToken>()),
            Times.Once);

        _dependencyInjectorFactory.Mocker.GetMock<IAgenteOnlineBusiness>().Verify(
            repository => repository.BuscarAgentesInativosAsync(It.IsAny<string[]>(), It.IsAny<CancellationToken>()),
            Times.Once);

        _dependencyInjectorFactory.Mocker.GetMock<IAgenteOnlineBusiness>().Verify(
            repository => repository.ObterAgentesOnlineAsync(It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact(DisplayName = "[Agente Integração] - Deve listar agentes online com o status inativo")]
    [Trait("Business", "Agente Integração")]
    public async Task DeveListarAgentesOnlineStatusInativo()
    {
        // Arrange

        var agenteOnlineIntegracaoDto = new AgentesOnlineIntegracaoDto([]);
        var agentes = new List<Agente>()
        {
            _agenteFixture.GerarAgente()
        };

        _dependencyInjectorFactory.Mocker
            .GetMock<IAgenteRepository>()
            .Setup(repository => repository.GetAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(agentes);

        _dependencyInjectorFactory.Mocker
            .GetMock<IAgenteOnlineBusiness>()
            .Setup(business => business.BuscarAgentesInativosAsync(It.IsAny<string[]>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(["CO"]);

        _dependencyInjectorFactory.Mocker
            .GetMock<IAgenteOnlineBusiness>()
            .Setup(business => business.ObterAgentesOnlineAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        // Act
        var resposta = await _dependencyInjectorFactory.Mocker.CreateInstance<AgenteIntegracaoBusiness>().GetAgentesOnlineAsync(agenteOnlineIntegracaoDto, CancellationToken.None);

        // Assert

        resposta.Agentes.ShouldNotBeEmpty();

        _dependencyInjectorFactory.Mocker.GetMock<IAgenteRepository>().Verify(
            repository => repository.GetAsync(It.IsAny<CancellationToken>()),
            Times.Once);

        _dependencyInjectorFactory.Mocker.GetMock<IAgenteOnlineBusiness>().Verify(
            repository => repository.BuscarAgentesInativosAsync(It.IsAny<string[]>(), It.IsAny<CancellationToken>()),
            Times.Once);

        _dependencyInjectorFactory.Mocker.GetMock<IAgenteOnlineBusiness>().Verify(
            repository => repository.ObterAgentesOnlineAsync(It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact(DisplayName = "[Agente Integração] - Deve listar agentes online com o status offline")]
    [Trait("Business", "Agente Integração")]
    public async Task DeveListarAgentesOnlineStatusOffline()
    {
        // Arrange

        var agenteOnlineIntegracaoDto = new AgentesOnlineIntegracaoDto([]);
        var agentes = new List<Agente>()
        {
            _agenteFixture.GerarAgente()
        };

        _dependencyInjectorFactory.Mocker
            .GetMock<IAgenteRepository>()
            .Setup(repository => repository.GetAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(agentes);

        _dependencyInjectorFactory.Mocker
            .GetMock<IAgenteOnlineBusiness>()
            .Setup(business => business.BuscarAgentesInativosAsync(It.IsAny<string[]>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        _dependencyInjectorFactory.Mocker
            .GetMock<IAgenteOnlineBusiness>()
            .Setup(business => business.ObterAgentesOnlineAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        // Act
        var resposta = await _dependencyInjectorFactory.Mocker.CreateInstance<AgenteIntegracaoBusiness>().GetAgentesOnlineAsync(agenteOnlineIntegracaoDto, CancellationToken.None);

        // Assert

        resposta.Agentes.ShouldNotBeEmpty();

        _dependencyInjectorFactory.Mocker.GetMock<IAgenteRepository>().Verify(
            repository => repository.GetAsync(It.IsAny<CancellationToken>()),
            Times.Once);

        _dependencyInjectorFactory.Mocker.GetMock<IAgenteOnlineBusiness>().Verify(
            repository => repository.BuscarAgentesInativosAsync(It.IsAny<string[]>(), It.IsAny<CancellationToken>()),
            Times.Once);

        _dependencyInjectorFactory.Mocker.GetMock<IAgenteOnlineBusiness>().Verify(
            repository => repository.ObterAgentesOnlineAsync(It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact(DisplayName = "[Agente Integração] - Não deve listar agentes online")]
    [Trait("Business", "Agente Integração")]
    public async Task NaoDeveListarAgentesOnline()
    {
        // Arrange

        var agenteOnlineIntegracaoDto = new AgentesOnlineIntegracaoDto(new[] { "CO" });

        _dependencyInjectorFactory.Mocker
            .GetMock<IAgenteRepository>()
            .Setup(repository => repository.GetAsync(It.IsAny<Expression<Func<Agente, bool>>>(), It.IsAny<CancellationToken>()));

        _dependencyInjectorFactory.Mocker
            .GetMock<IAgenteOnlineBusiness>()
            .Setup(business => business.BuscarAgentesInativosAsync(It.IsAny<string[]>(), It.IsAny<CancellationToken>()));

        _dependencyInjectorFactory.Mocker
            .GetMock<IAgenteOnlineBusiness>()
            .Setup(business => business.ObterAgentesOnlineAsync(It.IsAny<CancellationToken>()));

        // Act
        var resposta = await _dependencyInjectorFactory.Mocker.CreateInstance<AgenteIntegracaoBusiness>().GetAgentesOnlineAsync(agenteOnlineIntegracaoDto, CancellationToken.None);

        // Assert

        resposta.Agentes.ShouldBeEmpty();

        _dependencyInjectorFactory.Mocker.GetMock<IAgenteRepository>().Verify(
            repository => repository.GetAsync(It.IsAny<Expression<Func<Agente, bool>>>(), It.IsAny<CancellationToken>()),
            Times.Once);

        _dependencyInjectorFactory.Mocker.GetMock<IAgenteOnlineBusiness>().Verify(
            repository => repository.BuscarAgentesInativosAsync(It.IsAny<string[]>(), It.IsAny<CancellationToken>()),
            Times.Once);

        _dependencyInjectorFactory.Mocker.GetMock<IAgenteOnlineBusiness>().Verify(
            repository => repository.ObterAgentesOnlineAsync(It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact(DisplayName = "[Agente Integração] - Deve transformar agentes corretamente com status online")]
    [Trait("Business", "Agente Integração")]
    public async Task DeveTransformarAgentesCorretamenteComStatusOnline()
    {
        // Arrange
        var agente = _agenteFixture.GerarAgente(codigo: "CO");

        _dependencyInjectorFactory.Mocker
            .GetMock<IAgenteRepository>()
            .Setup(repo => repo.GetAsync(It.IsAny<Expression<Func<Agente, bool>>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<Agente> { agente });

        _dependencyInjectorFactory.Mocker
            .GetMock<IAgenteOnlineBusiness>()
            .Setup(b => b.ObterAgentesOnlineAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<AgenteOnlineDto>
            {
                new AgenteOnlineDto("CO", new[] { "LO" })
            });

        _dependencyInjectorFactory.Mocker
            .GetMock<IAgenteOnlineBusiness>()
            .Setup(b => b.BuscarAgentesInativosAsync(It.IsAny<string[]>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        var dto = new AgentesOnlineIntegracaoDto(new[] { "LO" });

        // Act
        var resposta = await _dependencyInjectorFactory.Mocker
            .CreateInstance<AgenteIntegracaoBusiness>()
            .GetAgentesOnlineAsync(dto, CancellationToken.None);

        // Assert
        resposta.Agentes.ShouldHaveSingleItem();

        var agenteRetornado = resposta.Agentes.First();
        agenteRetornado.Status.ShouldBe((short)StatusDeAgente.Online);
    }

    [Fact(DisplayName = "[Agente Integração] - Deve transformar agentes corretamente com status inativo")]
    [Trait("Business", "Agente Integração")]
    public async Task DeveTransformarAgentesCorretamenteComStatusInativo()
    {
        // Arrange
        var agente = _agenteFixture.GerarAgente(codigo: "CO");

        _dependencyInjectorFactory.Mocker
            .GetMock<IAgenteRepository>()
            .Setup(repo => repo.GetAsync(It.IsAny<Expression<Func<Agente, bool>>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<Agente> { agente });

        _dependencyInjectorFactory.Mocker
            .GetMock<IAgenteOnlineBusiness>()
            .Setup(business => business.BuscarAgentesInativosAsync(It.IsAny<string[]>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(["CO"]);

        _dependencyInjectorFactory.Mocker
            .GetMock<IAgenteOnlineBusiness>()
            .Setup(business => business.ObterAgentesOnlineAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        var dto = new AgentesOnlineIntegracaoDto(new[] { "LO" });

        // Act
        var resposta = await _dependencyInjectorFactory.Mocker
            .CreateInstance<AgenteIntegracaoBusiness>()
            .GetAgentesOnlineAsync(dto, CancellationToken.None);

        // Assert
        resposta.Agentes.ShouldHaveSingleItem();

        var agenteRetornado = resposta.Agentes.First();
        agenteRetornado.Status.ShouldBe((short)StatusDeAgente.Inativo);
    }

    [Fact(DisplayName = "[Agente Integração] - Deve transformar agentes corretamente com status offline")]
    [Trait("Business", "Agente Integração")]
    public async Task DeveTransformarAgentesCorretamenteComStatusOffline()
    {
        // Arrange
        var agente = _agenteFixture.GerarAgente(codigo: "CO");

        _dependencyInjectorFactory.Mocker
            .GetMock<IAgenteRepository>()
            .Setup(repo => repo.GetAsync(It.IsAny<Expression<Func<Agente, bool>>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<Agente> { agente });

        _dependencyInjectorFactory.Mocker
            .GetMock<IAgenteOnlineBusiness>()
            .Setup(business => business.BuscarAgentesInativosAsync(It.IsAny<string[]>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        _dependencyInjectorFactory.Mocker
            .GetMock<IAgenteOnlineBusiness>()
            .Setup(business => business.ObterAgentesOnlineAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        var dto = new AgentesOnlineIntegracaoDto(new[] { "LO" });

        // Act
        var resposta = await _dependencyInjectorFactory.Mocker
            .CreateInstance<AgenteIntegracaoBusiness>()
            .GetAgentesOnlineAsync(dto, CancellationToken.None);

        // Assert
        resposta.Agentes.ShouldHaveSingleItem();

        var agenteRetornado = resposta.Agentes.First();
        agenteRetornado.Status.ShouldBe((short)StatusDeAgente.Offline);
    }
}