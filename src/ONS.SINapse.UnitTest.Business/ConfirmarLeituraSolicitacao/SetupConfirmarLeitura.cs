using ONS.SINapse.Shared.Identity;
using ONS.SINapse.UnitTest.Business.Fixtures.Collections;
using ONS.SINapse.UnitTest.Shared;
using ONS.SINapse.UnitTest.Shared.Fixtures;

namespace ONS.SINapse.UnitTest.Business.ConfirmarLeituraSolicitacao;

[Collection(nameof(ConfirmarLeituraCollection))]
public partial class ConfirmarLeituraTest
{
    private readonly SolicitacaoFixture _solicitacaoFixture;
    private readonly UsuarioFixture _usuarioFixture;
    private readonly PerfilFixture _perfilFixture;
    private readonly MockDependencyInjectorFactory _dependencyInjectorFactory;

    public ConfirmarLeituraTest(SolicitacaoFixture solicitacaoFixture, UsuarioFixture usuarioFixture, PerfilFixture perfilFixture)
    {
        _solicitacaoFixture = solicitacaoFixture;
        _dependencyInjectorFactory = new MockDependencyInjectorFactory();
        _dependencyInjectorFactory.RegisterMocks();
        _usuarioFixture = usuarioFixture;
        _perfilFixture = perfilFixture;
    }

    private void ConfigurarMockUserContext(string destino)
    {
        var perfil = new Perfil([new Scope("CENTROS", destino, destino)], [], destino, destino);
        _dependencyInjectorFactory.Mocker
            .GetMock<IUserContext>()
            .SetupGet(x => x.Perfil)
            .Returns(perfil);
    }
}
