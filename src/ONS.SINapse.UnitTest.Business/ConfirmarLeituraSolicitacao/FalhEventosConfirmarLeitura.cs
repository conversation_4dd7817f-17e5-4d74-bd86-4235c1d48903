using FluentValidation.Results;
using ONS.SINapse.Repository.IRepository;
using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using System.Linq.Expressions;

namespace ONS.SINapse.UnitTest.Business.ConfirmarLeituraSolicitacao
{
    public partial class ConfirmarLeituraTest
    {
        [Fact(DisplayName = "[Confirmar Leitura] - Deve retornar um erro em confirmar leitura chat solicitação no firebase")]
        [Trait("Business", "Confirmar Leitura")]
        public async Task ConfirmarLeitura_DeveRetornarUmErro_FirebaseCommand()
        {
            // Arrange
            var solicitacao = _solicitacaoFixture.GerarSolicitacaoPendente();

            var command = new ConfirmarLeituraCommand(solicitacao.Id);

            _dependencyInjectorFactory.Mocker
                .GetMock<ISolicitacaoRepository>()
                .Setup(repository => repository.GetAsync(It.IsAny<Expression<Func<Entities.Entities.Solicitacao, bool>>>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync([solicitacao]);

            var result = new ValidationResult();
            result.AdicionarErro("Erro ao enviar command para o firebase");

            var mediatorMock = _dependencyInjectorFactory.Mocker.GetMock<IMediatorHandler>();

            mediatorMock
                .Setup(x => x.EnviarComandoAsync(It.IsAny<ConfirmarLeituraSolicitacaoNoFirebaseCommand>(),
                    It.IsAny<CancellationToken>()))
                .ReturnsAsync(result);

            // Act
            var response =
                await mediatorMock.Object
                    .EnviarComandoAsync<ConfirmarLeituraCommand>(
                        command, CancellationToken.None);

            // Assert
            Assert.NotNull(response);
            response.IsValid.ShouldBeTrue(response.ToString());
            response.Errors.ShouldBeEmpty();

            _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoRepository>().Verify(
                repository => repository.GetAsync(It.IsAny<Expression<Func<Entities.Entities.Solicitacao, bool>>>(), It.IsAny<CancellationToken>()),
                Times.Once);

            _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoRepository>().Verify(
                repository => repository.BulkUpdateChatAsync(It.IsAny<IEnumerable<Entities.Entities.Solicitacao>>(), It.IsAny<CancellationToken>()),
                Times.Once);

            var confirmarLeituraSolicitacaoNoFirebaseCommandPublished =
                await _dependencyInjectorFactory.Harness.Published.Any<ConfirmarLeituraSolicitacaoNoFirebaseCommand>();

            confirmarLeituraSolicitacaoNoFirebaseCommandPublished.ShouldBeFalse();
        }
    }
}