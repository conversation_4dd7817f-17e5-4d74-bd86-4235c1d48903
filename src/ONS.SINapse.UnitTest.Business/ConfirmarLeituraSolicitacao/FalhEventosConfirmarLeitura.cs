using FluentValidation.Results;
using ONS.SINapse.Repository.IRepository.Firebase;
using ONS.SINapse.Repository.IRepository.InMemory;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using System.Linq.Expressions;

namespace ONS.SINapse.UnitTest.Business.ConfirmarLeituraSolicitacao
{
    public partial class ConfirmarLeituraTest
    {
        [Fact(DisplayName = "[Confirmar Leitura] - Deve retornar um erro em confirmar leitura chat solicitação no firebase")]
        [Trait("Business", "Confirmar Leitura")]
        public async Task ConfirmarLeitura_DeveRetornarUmErro_FirebaseCommand()
        {
            // Arrange
            var solicitacao = _solicitacaoFixture.GerarSolicitacaoPendente();

            var command = new ConfirmarLeituraCommand(solicitacao.Id);

            _dependencyInjectorFactory.Mocker
                .GetMock<ISolicitacaoMemoryRepository>()
                .Setup(repository => repository.GetList(It.IsAny<Expression<Func<Entities.Entities.Solicitacao, bool>>>()))
                .Returns([solicitacao]);

            var result = new ValidationResult();
            result.AdicionarErro("Erro ao enviar command para o firebase");

            var mediatorMock = _dependencyInjectorFactory.Mocker.GetMock<IMediatorHandler>();

            mediatorMock
                .Setup(x => x.EnviarComandoAsync(It.IsAny<ConfirmarLeituraSolicitacaoNoFirebaseCommand>(),
                    It.IsAny<CancellationToken>()))
                .ReturnsAsync(result);

            // Act
            var response =
                await mediatorMock.Object
                    .EnviarComandoAsync<ConfirmarLeituraCommand>(
                        command, CancellationToken.None);

            // Assert
            Assert.NotNull(response);
            response.IsValid.ShouldBeTrue(response.ToString());
            response.Errors.ShouldBeEmpty();

            _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoMemoryRepository>().Verify(
                repository => repository.GetList(It.IsAny<Expression<Func<Entities.Entities.Solicitacao, bool>>>()),
                Times.Once);

            _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoFirebaseRepository>().Verify(
                repository => repository.PatchFlattenAsync(It.IsAny<ChatDeSolicitacaoFirebaseDto>(), It.IsAny<CancellationToken>()),
                Times.Never);

            var confirmarLeituraSolicitacaoNoFirebaseCommandPublished =
                await _dependencyInjectorFactory.Harness.Published.Any<ConfirmarLeituraSolicitacaoNoFirebaseCommand>();

            confirmarLeituraSolicitacaoNoFirebaseCommandPublished.ShouldBeFalse();
        }
    }
}