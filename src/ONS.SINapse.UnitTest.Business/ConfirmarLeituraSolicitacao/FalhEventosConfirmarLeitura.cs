using FluentValidation.Results;
using ONS.SINapse.Repository.IRepository.Firebase;
using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Shared.Messages;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands.Firebase;

namespace ONS.SINapse.UnitTest.Business.ConfirmarLeituraSolicitacao
{
    public partial class ConfirmarLeituraTest
    {
        [Fact(DisplayName = "[Confirmar Leitura] - Deve retornar um erro em confirmar leitura chat solicitação no firebase")]
        [Trait("Business", "Confirmar Leitura")]
        public async Task ConfirmarLeitura_DeveRetornarUmErro_FirebaseCommand()
        {
            // Arrange
            var solicitacao = _solicitacaoFixture.GerarSolicitacaoPendente();

            var command = new ConfirmarLeituraCommand(solicitacao.Id);

            _dependencyInjectorFactory.Mocker
                .GetMock<ISolicitacaoFirebaseRepository>()
                .Setup(repository => repository.GetByIdAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(solicitacao);

            var result = new ValidationResult();
            result.AdicionarErro("Erro ao enviar command para o firebase");

            var mediatorMock = _dependencyInjectorFactory.Mocker.GetMock<IMediatorHandler>();

            mediatorMock
                .Setup(x => x.EnviarComandoAsync(
                    It.Is<Command>(cmd => cmd is LeituraDeSolicitacaoNoFirebaseCommand),
                    It.IsAny<CancellationToken>()))
                .ReturnsAsync(result);

            // Act
            var response =
                await mediatorMock.Object
                    .EnviarComandoAsync<ConfirmarLeituraCommand>(
                        command, CancellationToken.None);

            // Assert
            Assert.NotNull(response);
            response.IsValid.ShouldBeFalse(response.ToString());
            response.Errors.ShouldNotBeEmpty();

            _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoFirebaseRepository>().Verify(
                repository => repository.GetByIdAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()),
                Times.Once);

            _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoFirebaseRepository>().Verify(
                repository => repository.AddUpdateAsync(It.IsAny<Dictionary<string, object?>>(), It.IsAny<CancellationToken>()),
                Times.Never);

            var leituraDeSolicitacaoNoFirebaseCommandPublished =
                await _dependencyInjectorFactory.Harness.Published.Any<LeituraDeSolicitacaoNoFirebaseCommand>();

            leituraDeSolicitacaoNoFirebaseCommandPublished.ShouldBeFalse();
        }
    }
}