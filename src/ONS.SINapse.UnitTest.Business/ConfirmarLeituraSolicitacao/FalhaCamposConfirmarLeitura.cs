using Microsoft.Extensions.DependencyInjection;
using ONS.SINapse.Repository.IRepository;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using System.Linq.Expressions;

namespace ONS.SINapse.UnitTest.Business.ConfirmarLeituraSolicitacao;

public partial class ConfirmarLeituraTest
{
    [Fact(DisplayName = "[Confirmar Leitura] - Deve retornar erro quando solicitacao não for informada")]
    [Trait("Business", "Confirmar Leitura")]
    public async Task ConfirmarLeitura_DeveRetornarErro_SolicitacaoNaoInformada()
    {
        // Arrange
        _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoRepository>()
            .Setup(repository => repository.GetAsync(It.IsAny<Expression<Func<Entities.Entities.Solicitacao, bool>>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        var command = new ConfirmarLeituraCommand("");

        // Act
        var response = await mediator.EnviarComandoAsync<ConfirmarLeituraCommand>(command, CancellationToken.None);

        // Assert
        Assert.NotNull(response);
        response.IsValid.ShouldBeFalse(response.ToString());
        response.Errors.Any(x => x.ErrorMessage == "Solicitação não encontrada.").ShouldBeTrue();

        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoRepository>().Verify(
            repository => repository.GetAsync(It.IsAny<Expression<Func<Entities.Entities.Solicitacao, bool>>>(), It.IsAny<CancellationToken>()),
            Times.Once);

        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoRepository>().Verify(
            repository => repository.BulkUpdateChatAsync(It.IsAny<IEnumerable<Entities.Entities.Solicitacao>>(), It.IsAny<CancellationToken>()),
            Times.Never);

        var confirmarLeituraSolicitacaoNoFirebaseCommandPublished =
            await _dependencyInjectorFactory.Harness.Published.Any<ConfirmarLeituraSolicitacaoNoFirebaseCommand>();

        confirmarLeituraSolicitacaoNoFirebaseCommandPublished.ShouldBeFalse();
    }
}
