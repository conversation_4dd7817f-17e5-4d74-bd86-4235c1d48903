using Microsoft.Extensions.DependencyInjection;
using ONS.SINapse.Repository.IRepository.Firebase;
using ONS.SINapse.Repository.IRepository.InMemory;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using System.Linq.Expressions;

namespace ONS.SINapse.UnitTest.Business.ConfirmarLeituraSolicitacao;

public partial class ConfirmarLeituraTest
{
    [Fact(DisplayName = "[Confirmar Leitura] - Deve retornar erro quando solicitacao não for informada")]
    [Trait("Business", "Confirmar Leitura")]
    public async Task ConfirmarLeitura_DeveRetornarErro_SolicitacaoNaoInformada()
    {
        // Arrange
        _dependencyInjectorFactory.Mocker
                .GetMock<ISolicitacaoMemoryRepository>()
                .Setup(repository => repository.GetList(It.IsAny<Expression<Func<Entities.Entities.Solicitacao, bool>>>()))
                .Returns([]);

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        var command = new ConfirmarLeituraCommand("");

        // Act
        var response = await mediator.EnviarComandoAsync<ConfirmarLeituraCommand>(command, CancellationToken.None);

        // Assert
        Assert.NotNull(response);
        response.IsValid.ShouldBeFalse(response.ToString());
        response.Errors.Any(x => x.ErrorMessage == "Solicitação não encontrada.").ShouldBeTrue();

        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoMemoryRepository>().Verify(
            repository => repository.GetList(It.IsAny<Expression<Func<Entities.Entities.Solicitacao, bool>>>()),
            Times.Once);

        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoFirebaseRepository>().Verify(
            repository => repository.PatchFlattenAsync(It.IsAny<ChatDeSolicitacaoFirebaseDto>(), It.IsAny<CancellationToken>()),
            Times.Never);

        var confirmarLeituraSolicitacaoNoFirebaseCommandPublished =
            await _dependencyInjectorFactory.Harness.Published.Any<ConfirmarLeituraSolicitacaoNoFirebaseCommand>();

        confirmarLeituraSolicitacaoNoFirebaseCommandPublished.ShouldBeFalse();
    }
}
