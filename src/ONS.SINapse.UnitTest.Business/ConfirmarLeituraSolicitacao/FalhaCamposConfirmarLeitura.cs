using Microsoft.Extensions.DependencyInjection;
using ONS.SINapse.Repository.IRepository.Firebase;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands.Firebase;

namespace ONS.SINapse.UnitTest.Business.ConfirmarLeituraSolicitacao;

public partial class ConfirmarLeituraTest
{
    [Fact(DisplayName = "[Confirmar Leitura] - Deve retornar erro quando solicitacao não for informada")]
    [Trait("Business", "Confirmar Leitura")]
    public async Task ConfirmarLeitura_DeveRetornarErro_SolicitacaoNaoInformada()
    {
        // Arrange
        _dependencyInjectorFactory.Mocker
                .GetMock<ISolicitacaoFirebaseRepository>()
                .Setup(repository => repository.GetByIdAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()));

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        var command = new ConfirmarLeituraCommand("");

        // Act
        var response = await mediator.EnviarComandoAsync(command, CancellationToken.None);

        // Assert
        Assert.NotNull(response);
        response.IsValid.ShouldBeFalse(response.ToString());
        response.Errors.Any(x => x.ErrorMessage == "Solicitação não encontrada.").ShouldBeTrue();

        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoFirebaseRepository>().Verify(
            repository => repository.GetByIdAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()),
            Times.Once);

        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoFirebaseRepository>().Verify(
            repository => repository.AddUpdateAsync(It.IsAny<Dictionary<string, object?>>(), It.IsAny<CancellationToken>()),
            Times.Never);

        var leituraDeSolicitacaoNoFirebaseCommandPublished =
            await _dependencyInjectorFactory.Harness.Published.Any<LeituraDeSolicitacaoNoFirebaseCommand>();

        leituraDeSolicitacaoNoFirebaseCommandPublished.ShouldBeFalse();
    }
}
