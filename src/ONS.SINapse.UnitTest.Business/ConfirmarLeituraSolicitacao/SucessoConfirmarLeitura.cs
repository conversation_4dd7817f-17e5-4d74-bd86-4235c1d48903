using Microsoft.Extensions.DependencyInjection;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using System.Linq.Expressions;
using ONS.SINapse.UnitTest.Shared.Fixtures;
using ONS.SINapse.Repository.IRepository.InMemory;
using ONS.SINapse.Repository.IRepository.Firebase;
using ONS.SINapse.Shared.DTO;

namespace ONS.SINapse.UnitTest.Business.ConfirmarLeituraSolicitacao
{
    public partial class ConfirmarLeituraTest
    {
        [Fact(DisplayName = "[Confirmar Leitura] - Deve confirmar leitura")]
        [Trait("Business", "Confirmar Leitura")]
        public async Task DeveConfirmarLeitura()
        {
            // Arrange
            var solicitacao = _solicitacaoFixture.GerarSolicitacaoPendente();
            solicitacao.AdicionarMensagemNoChat("Teste", UsuarioFixture.GerarUsuarioValido(), _perfilFixture.GerarPerfilValido());

            var origem = solicitacao.Origem.Codigo;

            ConfigurarMockUserContext(origem);

            _dependencyInjectorFactory.Mocker
                .GetMock<ISolicitacaoMemoryRepository>()
                .Setup(repository => repository.GetList(It.IsAny<Expression<Func<Entities.Entities.Solicitacao, bool>>>()))
                .Returns([solicitacao]);

            var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

            var command = new ConfirmarLeituraCommand(solicitacao.Id);

            // Act
            var response = await mediator.EnviarComandoAsync<ConfirmarLeituraCommand>(command, CancellationToken.None);

            // Assert
            Assert.NotNull(response);
            response.IsValid.ShouldBeTrue(response.ToString());

            response.Errors.ShouldBeEmpty();

            _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoMemoryRepository>().Verify(
                repository => repository.GetList(It.IsAny<Expression<Func<Entities.Entities.Solicitacao, bool>>>()),
                Times.Once);

            _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoFirebaseRepository>().Verify(
                repository => repository.PatchFlattenAsync(It.IsAny<ChatDeSolicitacaoFirebaseDto>(), It.IsAny<CancellationToken>()),
                Times.Once);

            var confirmarLeituraSolicitacaoNoFirebaseCommandPublished =
                await _dependencyInjectorFactory.Harness.Published.Any<ConfirmarLeituraSolicitacaoNoFirebaseCommand>();

            confirmarLeituraSolicitacaoNoFirebaseCommandPublished.ShouldBeTrue();
        }

        [Fact(DisplayName = "[Confirmar Leitura] - Deve retornar Sucesso sem mensagens para confirmar leitura")]
        [Trait("Business", "Confirmar Leitura")]
        public async Task DeveRetornarSucessoSemMensagensParaLeitura()
        {
            // Arrange
            var solicitacao = _solicitacaoFixture.GerarSolicitacaoPendente();
            var origem = solicitacao.Origem.Codigo;

            ConfigurarMockUserContext(origem);

            _dependencyInjectorFactory.Mocker
                .GetMock<ISolicitacaoMemoryRepository>()
                .Setup(repository => repository.GetList(It.IsAny<Expression<Func<Entities.Entities.Solicitacao, bool>>>()))
                .Returns([solicitacao]);

            var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

            var command = new ConfirmarLeituraCommand(solicitacao.Id);

            // Act
            var response = await mediator.EnviarComandoAsync<ConfirmarLeituraCommand>(command, CancellationToken.None);

            // Assert
            Assert.NotNull(response);
            response.IsValid.ShouldBeTrue(response.ToString());

            response.Errors.ShouldBeEmpty();

            _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoMemoryRepository>().Verify(
                repository => repository.GetList(It.IsAny<Expression<Func<Entities.Entities.Solicitacao, bool>>>()),
                Times.Once);

            _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoFirebaseRepository>().Verify(
                repository => repository.PatchFlattenAsync(It.IsAny<ChatDeSolicitacaoFirebaseDto>(), It.IsAny<CancellationToken>()),
                Times.Never);

            var confirmarLeituraSolicitacaoNoFirebaseCommandPublished =
                await _dependencyInjectorFactory.Harness.Published.Any<ConfirmarLeituraSolicitacaoNoFirebaseCommand>();

            confirmarLeituraSolicitacaoNoFirebaseCommandPublished.ShouldBeFalse();
        }
    }
}
