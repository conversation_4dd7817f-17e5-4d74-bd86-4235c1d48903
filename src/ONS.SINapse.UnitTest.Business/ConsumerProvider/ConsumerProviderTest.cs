using Confluent.Kafka;
using Microsoft.Extensions.Options;
using ONS.SINapse.Shared.Kafka;
using ONS.SINapse.Shared.Kafka.Providers;

namespace ONS.SINapse.UnitTest.Business.ConsumerProvider;

public partial class ConsumerProviderTests
{
    [Fact(DisplayName = "Deve consumir mensagem com sucesso")]
    [Trait("Kafka", "ConsumerProvider")]
    public async Task SubscribeAsync_DeveConsumirMensagemComSucesso()
    {
        // Arrange
        var evento = new TestIntegrationKafkaEvent { Conteudo = "Teste" };
        var headers = new Headers();
        var consumerMock = _dependencyInjectorFactory.Mocker.GetMock<IConsumer<Ignore, TestIntegrationKafkaEvent>>();

        var consumeResult = new ConsumeResult<Ignore, TestIntegrationKafkaEvent>
        {
            Message = new Message<Ignore, TestIntegrationKafkaEvent>
            {
                Value = evento,
                Headers = headers
            }
        };

        consumerMock.Setup(c => c.Consume(It.IsAny<CancellationToken>())).Returns(consumeResult);
        MockConsumerProvider(consumerMock);

        var messageHandlerMock = new Mock<Func<TestIntegrationKafkaEvent, Task>>();

        // Act
        await _consumerProvider.SubscribeAsync(messageHandlerMock.Object, CancellationToken.None);

        // Assert
        messageHandlerMock.Verify(m => m.Invoke(evento), Times.Once);
        consumerMock.Verify(c => c.Commit(It.IsAny<ConsumeResult<Ignore, TestIntegrationKafkaEvent>>()), Times.Once);
    }

    [Fact(DisplayName = "Não deve consumir mensagem quando não estiver conectado")]
    [Trait("Kafka", "ConsumerProvider")]
    public async Task SubscribeAsync_NaoDeveConsumir_QuandoNaoConectado()
    {
        // Arrange
        var messageHandlerMock = new Mock<Func<TestIntegrationKafkaEvent, Task>>();

        _dependencyInjectorFactory.Mocker.Use(Options.Create(_producerProviderFixture.ObterKafkaSettingsInativo()));
        _consumerProvider = _dependencyInjectorFactory.Mocker.CreateInstance<ConsumerProvider<TestIntegrationKafkaEvent>>();

        // Act
        await _consumerProvider.SubscribeAsync(messageHandlerMock.Object, CancellationToken.None);

        // Assert
        messageHandlerMock.Verify(m => m.Invoke(It.IsAny<TestIntegrationKafkaEvent>()), Times.Never);
    }

    [Fact(DisplayName = "Deve chamar Dispose corretamente")]
    [Trait("Kafka", "ConsumerProvider")]
    public void Dispose_DeveChamarDisposeCorretamente()
    {
        // Arrange
        var consumerMock = new Mock<IConsumer<Ignore, TestIntegrationKafkaEvent>>();
        MockConsumerProvider(consumerMock);

        // Act
        _consumerProvider.Dispose();

        // Assert
        consumerMock.Verify(c => c.Close(), Times.Once);
        consumerMock.Verify(c => c.Dispose(), Times.Once);
    }
}

public class TestIntegrationKafkaEvent : IntegrationKafkaEvent
{
    public string? Conteudo { get; set; }
}

public class TestTopicoIntegrationKafka : TopicoIntegrationKafka<TestIntegrationKafkaEvent>
{
    public TestTopicoIntegrationKafka(string topico) : base(topico) { }
}