using Confluent.Kafka;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using ONS.SINapse.Shared.Kafka;
using ONS.SINapse.Shared.Kafka.Providers;
using ONS.SINapse.UnitTest.Business.Fixtures.Collections;
using ONS.SINapse.UnitTest.Shared;

namespace ONS.SINapse.UnitTest.Business.ConsumerProvider;

[Collection(nameof(ConsumerProviderCollection))]
public partial class ConsumerProviderTests
{
    private ConsumerProvider<TestIntegrationKafkaEvent> _consumerProvider;

    private readonly ProducerProviderFixture _producerProviderFixture;
    private readonly MockDependencyInjectorFactory _dependencyInjectorFactory;
    private readonly Mock<ILogger<ConsumerProvider<TestIntegrationKafkaEvent>>> _loggerMock;

    public ConsumerProviderTests(ProducerProviderFixture producerProviderFixture)
    {
        _producerProviderFixture = producerProviderFixture;
        _dependencyInjectorFactory = new MockDependencyInjectorFactory();
        _dependencyInjectorFactory.RegisterMocks();

        _dependencyInjectorFactory.Mocker.Use(Options.Create(_producerProviderFixture.ObterKafkaSettingsValido()));

        var topico = new TestTopicoIntegrationKafka("test-topic");
        _dependencyInjectorFactory.Mocker.Use<TopicoIntegrationKafka<TestIntegrationKafkaEvent>>(topico);

        _consumerProvider = _dependencyInjectorFactory.Mocker.CreateInstance<ConsumerProvider<TestIntegrationKafkaEvent>>();

        _loggerMock = new Mock<ILogger<ConsumerProvider<TestIntegrationKafkaEvent>>>();
    }

    private void MockConsumerProvider(Mock<IConsumer<Ignore, TestIntegrationKafkaEvent>> consumerMock)
    {
        var consumerField = typeof(ConsumerProvider<TestIntegrationKafkaEvent>)
                            .GetField("_consumer", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

        consumerField!.SetValue(_consumerProvider, consumerMock.Object);
    }
}