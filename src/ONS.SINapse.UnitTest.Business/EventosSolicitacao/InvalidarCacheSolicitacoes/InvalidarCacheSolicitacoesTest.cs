using MassTransit;
using Microsoft.Extensions.DependencyInjection;
using ONS.SINapse.Shared.Constants;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Shared.Services.Caching;
using ONS.SINapse.Solicitacao.EventHandlers.Events;
using ONS.SINapse.UnitTest.Business.Fixtures.Collections;

namespace ONS.SINapse.UnitTest.Business.EventosSolicitacao.InvalidarCacheSolicitacoes;

[Collection(nameof(InvalidarCacheSolicitacoesEventHandlerCollection))]
public partial class InvalidarCacheSolicitacoesTests
{
    [Fact(DisplayName = "[Invalidar Cache Solicitacoes] - Deve invalidar cache de solicitações canceladas")]
    [Trait("Business", "Invalidar Cache Solicitacoes")]
    public async Task Publicar_SolicitacoesCanceladasEmLoteEvent_DeveInvalidarCache_Canceladas()
    {
        // Arrange
        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();
        var mockRepository = _dependencyInjectorFactory.Mocker
            .GetMock<ICacheService>();

        var solicitacao = _solicitacaoFixture.GerarSolicitacaoCancelada();
        var evento = new SolicitacoesCanceladasEmLoteEvent([solicitacao.Id]);

        // Act
        await mediator.PublicarEventoAsync(evento, CancellationToken.None);

        // Assert
        var eventoPublicado =
            _dependencyInjectorFactory.Harness.Published.Any<SolicitacoesCanceladasEmLoteEvent>();

        var eventoConsumido =
            _dependencyInjectorFactory.Harness.Consumed.Any<SolicitacoesCanceladasEmLoteEvent>();

        await Task.WhenAll(eventoPublicado, eventoConsumido);

        eventoPublicado.Result.ShouldBeTrue();
        eventoConsumido.Result.ShouldBeTrue();

        mockRepository.Verify(r => r.RemoveByPrefixAsync(
            CacheRedisKey.KeyISolicitacaoPaged,
            It.IsAny<CancellationToken>()), Times.Once);

        mockRepository.Verify(r => r.RemoveByPrefixAsync(
            CacheRedisKey.KeyISolicitacaoPagedCount,
            It.IsAny<CancellationToken>()), Times.Once);

        mockRepository.Verify(r => r.RemoveByPrefixAsync(
            CacheRedisKey.KeyISolicitacaoRepository,
            It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact(DisplayName = "[Invalidar Cache Solicitacoes] - Deve retornar erro ao invalidar cache de solicitações canceladas")]
    [Trait("Business", "Invalidar Cache Solicitacoes")]
    public async Task Publicar_SolicitacoesCanceladasEmLoteEvent_DeveRetornarErroInvalidarCache_Canceladas()
    {
        // Arrange
        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();
        var mockRepository = _dependencyInjectorFactory.Mocker
            .GetMock<ICacheService>();

        mockRepository.Setup(r =>
                r.RemoveByPrefixAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new Exception("Teste de unidade remover cache solicitação"));

        var solicitacao = _solicitacaoFixture.GerarSolicitacaoCancelada();
        var evento = new SolicitacoesCanceladasEmLoteEvent([solicitacao.Id]);

        // Act
        await mediator.PublicarEventoAsync(evento, CancellationToken.None);

        // Assert
        var eventoPublicado =
            _dependencyInjectorFactory.Harness.Published.Any<SolicitacoesCanceladasEmLoteEvent>();

        var eventoConsumido =
            _dependencyInjectorFactory.Harness.Consumed.Any<SolicitacoesCanceladasEmLoteEvent>();

        var falhaDisparada =
            _dependencyInjectorFactory.Harness.Published.Any<Fault<SolicitacoesCanceladasEmLoteEvent>>();

        await Task.WhenAll(eventoPublicado, eventoConsumido, falhaDisparada);

        eventoPublicado.Result.ShouldBeTrue();
        eventoConsumido.Result.ShouldBeTrue();
        falhaDisparada.Result.ShouldBeTrue();

        mockRepository.Verify(r => r.RemoveByPrefixAsync(
            CacheRedisKey.KeyISolicitacaoPaged,
            It.IsAny<CancellationToken>()), Times.Once);

        mockRepository.Verify(r => r.RemoveByPrefixAsync(
            CacheRedisKey.KeyISolicitacaoPagedCount,
            It.IsAny<CancellationToken>()), Times.Once);

        mockRepository.Verify(r => r.RemoveByPrefixAsync(
            CacheRedisKey.KeyISolicitacaoRepository,
            It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact(DisplayName = "[Invalidar Cache Solicitacoes] - Deve invalidar cache de solicitações finalizadas")]
    [Trait("Business", "Invalidar Cache Solicitacoes")]
    public async Task Publicar_SolicitacaoFinalizadaEmLoteEvent_DeveInvalidarCache_Finalizar()
    {
        // Arrange
        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();
        var mockRepository = _dependencyInjectorFactory.Mocker
            .GetMock<ICacheService>();

        var solicitacao = _solicitacaoFixture.GerarSolicitacaoCancelada();
        var evento = new SolicitacaoFinalizadaEmLoteEvent([solicitacao.Id]);

        // Act
        await mediator.PublicarEventoAsync(evento, CancellationToken.None);

        // Assert
        var eventoPublicado =
            _dependencyInjectorFactory.Harness.Published.Any<SolicitacaoFinalizadaEmLoteEvent>();

        var eventoConsumido =
            _dependencyInjectorFactory.Harness.Consumed.Any<SolicitacaoFinalizadaEmLoteEvent>();

        await Task.WhenAll(eventoPublicado, eventoConsumido);

        eventoPublicado.Result.ShouldBeTrue();
        eventoConsumido.Result.ShouldBeTrue();

        mockRepository.Verify(r => r.RemoveByPrefixAsync(
            CacheRedisKey.KeyISolicitacaoPaged,
            It.IsAny<CancellationToken>()), Times.Once);

        mockRepository.Verify(r => r.RemoveByPrefixAsync(
            CacheRedisKey.KeyISolicitacaoPagedCount,
            It.IsAny<CancellationToken>()), Times.Once);

        mockRepository.Verify(r => r.RemoveByPrefixAsync(
            CacheRedisKey.KeyISolicitacaoRepository,
            It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact(DisplayName = "[Invalidar Cache Solicitacoes] - Deve retornar erro ao invalidar cache de solicitações finalizadas")]
    [Trait("Business", "Invalidar Cache Solicitacoes")]
    public async Task Publicar_SolicitacaoFinalizadaEmLoteEvent_DeveRetornarErroInvalidarCache_Finalizar()
    {
        // Arrange
        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();
        var mockRepository = _dependencyInjectorFactory.Mocker
            .GetMock<ICacheService>();

        mockRepository.Setup(r =>
                r.RemoveByPrefixAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new Exception("Teste de unidade remover cache solicitação"));

        var solicitacao = _solicitacaoFixture.GerarSolicitacaoCancelada();
        var evento = new SolicitacaoFinalizadaEmLoteEvent([solicitacao.Id]);

        // Act
        await mediator.PublicarEventoAsync(evento, CancellationToken.None);

        // Assert
        var eventoPublicado =
            _dependencyInjectorFactory.Harness.Published.Any<SolicitacaoFinalizadaEmLoteEvent>();

        var eventoConsumido =
            _dependencyInjectorFactory.Harness.Consumed.Any<SolicitacaoFinalizadaEmLoteEvent>();

        var falhaDisparada =
            _dependencyInjectorFactory.Harness.Published.Any<Fault<SolicitacaoFinalizadaEmLoteEvent>>();

        await Task.WhenAll(eventoPublicado, eventoConsumido, falhaDisparada);

        eventoPublicado.Result.ShouldBeTrue();
        eventoConsumido.Result.ShouldBeTrue();
        falhaDisparada.Result.ShouldBeTrue();

        await Task.Delay(500);

        mockRepository.Verify(r => r.RemoveByPrefixAsync(
            CacheRedisKey.KeyISolicitacaoPaged,
            It.IsAny<CancellationToken>()), Times.Once);

        mockRepository.Verify(r => r.RemoveByPrefixAsync(
            CacheRedisKey.KeyISolicitacaoPagedCount,
            It.IsAny<CancellationToken>()), Times.Once);

        mockRepository.Verify(r => r.RemoveByPrefixAsync(
            CacheRedisKey.KeyISolicitacaoRepository,
            It.IsAny<CancellationToken>()), Times.Once);
    }
}
