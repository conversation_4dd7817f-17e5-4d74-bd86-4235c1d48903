using System.IO.Compression;
using System.Text;
using ONS.SINapse.Business.Imp.Business.TemplatesDeSolicitacao;
using ONS.SINapse.CacheSync.Services;
using ONS.SINapse.Shared.DTO;

namespace ONS.SINapse.UnitTest.Business.ExportarSqlDatasetViews;

public class ExportacaoDatasetViewsBusinessTest
{
    [Fact]
    public async Task ExportarAsync_DeveGerarZipComArquivosSqlCorretamente()
    {
        // Arrange
        var mockService = new Mock<ISinapseDadosDatasetService>();
        var viewsMock = new List<DatasetViewsDot>
        {
            new("view1", "CREATE VIEW view1 AS SELECT * FROM table1"),
            new("view2", "CREATE VIEW view2 AS SELECT * FROM table2")
        };

        mockService.Setup(s => s.GetDatasetViews(It.IsAny<CancellationToken>()))
            .ReturnsAsync(viewsMock);

        var business = new ExportacaoDatasetViewsBusiness(mockService.Object);

        // Act
        await using var resultStream = await business.ExportarAsync(CancellationToken.None);

        using var zip = new ZipArchive(resultStream, ZipArchiveMode.Read, leaveOpen: false);
        var entries = zip.Entries;

        // Assert
        entries.Count.ShouldBe(2);

        var entry1 = entries.FirstOrDefault(e => e.FullName == "views/view1.sql");
        var entry2 = entries.FirstOrDefault(e => e.FullName == "views/view2.sql");

        entry1.ShouldNotBeNull();
        entry2.ShouldNotBeNull();

        string ReadEntryContent(ZipArchiveEntry entry)
        {
            using var reader = new StreamReader(entry.Open(), Encoding.UTF8);
            return reader.ReadToEnd();
        }

        ReadEntryContent(entry1).ShouldBe("CREATE VIEW view1 AS SELECT * FROM table1");
        ReadEntryContent(entry2).ShouldBe("CREATE VIEW view2 AS SELECT * FROM table2");
    }
}