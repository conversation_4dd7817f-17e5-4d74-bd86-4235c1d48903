using ONS.SINapse.UnitTest.Business.Fixtures.Collections;
using ONS.SINapse.UnitTest.Shared;

namespace ONS.SINapse.UnitTest.Business.ExtracaoDeSolicitacao;

[Collection(nameof(ExtracaoDeSolicitacaoEventHandlerCollection))]
public partial class ExtracaoDeSolicitacaoEventHandlerTest
{
    private readonly MockDependencyInjectorFactory _dependencyInjectorFactory;

    public ExtracaoDeSolicitacaoEventHandlerTest()
    {
        _dependencyInjectorFactory = new MockDependencyInjectorFactory();
        _dependencyInjectorFactory.RegisterMocks();
    }
}