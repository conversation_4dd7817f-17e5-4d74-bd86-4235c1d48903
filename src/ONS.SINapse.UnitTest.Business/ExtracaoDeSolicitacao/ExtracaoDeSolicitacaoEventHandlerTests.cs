using MassTransit;
using Microsoft.Extensions.DependencyInjection;
using ONS.SINapse.Business.IBusiness;
using ONS.SINapse.Business.Imp.Business.EventHandlers.Events;

namespace ONS.SINapse.UnitTest.Business.ExtracaoDeSolicitacao;

public partial class ExtracaoDeSolicitacaoEventHandlerTest
{
    [Fact(DisplayName = "[Evento Extrair Solicitacao] - Deve retornar extrair solicitação")]
    [Trait("Business", "Evento Extrair Solicitacao")]
    public async Task Publicar_ExtracaoDeSolicitacaoEvent_DeveExtrairSolicitacao()
    {
        // Isolamento robusto para CI/CD - delay necessário para estabilidade
        await Task.Delay(100);

        // Aguardar um pouco mais para garantir que eventos anteriores foram processados
        await Task.Delay(50);

        // Arrange
        var services = new ServiceCollection();

        var mockBusiness = _dependencyInjectorFactory.Mocker.GetMock<IExtracaoDeSolicitacaoS3Business>();

        mockBusiness
            .Setup(x => x.ExtrairDadosAsync(It.IsAny<CancellationToken>()));

        var evento = new ExtracaoDeSolicitacaoEvent
        {
            Message = "Teste de unidade extração de solicitação"
        };

        // Act
        await _dependencyInjectorFactory.PublishEndpoint.Publish(evento);

        // Assert
        var eventoPublicado = _dependencyInjectorFactory.Harness.Published.Any<ExtracaoDeSolicitacaoEvent>();
        var eventoConsumido = _dependencyInjectorFactory.Harness.Consumed.Any<ExtracaoDeSolicitacaoEvent>();

        await Task.WhenAll(eventoPublicado, eventoConsumido);

        eventoPublicado.Result.ShouldBeTrue();
        eventoConsumido.Result.ShouldBeTrue();

        mockBusiness.Verify(x => x.ExtrairDadosAsync(It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact(DisplayName = "[Evento Extrair Solicitacao] - Deve retornar erro extrair solicitações")]
    [Trait("Business", "Evento Extrair Solicitacao")]
    public async Task Publicar_ExtracaoDeSolicitacaoEvent_DeveRetornarErroTeste()
    {
        // Arrange
        var services = new ServiceCollection();

        var mockBusiness = _dependencyInjectorFactory.Mocker.GetMock<IExtracaoDeSolicitacaoS3Business>();

        mockBusiness
            .Setup(x => x.ExtrairDadosAsync(It.IsAny<CancellationToken>()))
            .ThrowsAsync(new Exception("Erro de teste"));

        var evento = new ExtracaoDeSolicitacaoEvent
        {
            Message = "Teste de unidade extração de solicitação"
        };

        // Act
        await _dependencyInjectorFactory.PublishEndpoint.Publish(evento);

        // Assert
        var eventoPublicado = _dependencyInjectorFactory.Harness.Published.Any<ExtracaoDeSolicitacaoEvent>();
        var eventoConsumido = _dependencyInjectorFactory.Harness.Consumed.Any<ExtracaoDeSolicitacaoEvent>();
        var falhaDisparada = _dependencyInjectorFactory.Harness.Published.Any<Fault<ExtracaoDeSolicitacaoEvent>>();

        await Task.WhenAll(eventoPublicado, eventoConsumido, falhaDisparada);

        eventoPublicado.Result.ShouldBeTrue();
        eventoConsumido.Result.ShouldBeTrue();
        falhaDisparada.Result.ShouldBeTrue();

        mockBusiness.Verify(x => x.ExtrairDadosAsync(It.IsAny<CancellationToken>()), Times.Once);
    }
}
