using MassTransit;
using Microsoft.Extensions.DependencyInjection;
using ONS.SINapse.Repository.IRepository;
using ONS.SINapse.Repository.IRepository.Firebase;

using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Solicitacao.EventHandlers.Events;
using ONS.SINapse.UnitTest.Business.Fixtures.Collections;
using ONS.SINapse.UnitTest.Business.Fixtures.Solicitacao;
using ONS.SINapse.UnitTest.Shared;
using ONS.SINapse.UnitTest.Shared.Fixtures;

namespace ONS.SINapse.UnitTest.Business.EnviarSolicitacaoAoMongo;

[Collection(nameof(RemoverSolicitacoesFirebaseEventHandlerCollection))]
public class EnviarSolicitacaoAoMongoEventHandlerTest
{
    private readonly SolicitacaoFixture _solicitacaoFixture;
    private readonly MockDependencyInjectorFactory _dependencyInjectorFactory;

    public EnviarSolicitacaoAoMongoEventHandlerTest(SolicitacaoFixture solicitacaoFixture)
    {
        _solicitacaoFixture = solicitacaoFixture;
        _dependencyInjectorFactory = new MockDependencyInjectorFactory();
        _dependencyInjectorFactory.RegisterMocks();
    }
        

    [Fact(DisplayName = "[Enviar solicitação ao mongo] - Deve retornar erro ao enviar solicitações ao mongo")]
    [Trait("Business", "Finalizar solicitação firebase")]
    public async Task Publicar_SolicitacaoFinalizadaEmLoteEvent_DeveRetornarErro()
    {
        // Arrange

        var solicitacao = _solicitacaoFixture.GerarSolicitacaoConfirmada();
        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();
        var mockRepository = _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoRepository>();

   _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoFirebaseRepository>()
            .Setup(repository => repository.GetByIdAsync(It.IsAny<string[]>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([solicitacao]);

        mockRepository.Setup(r =>
                r.BulkUpdateAsync(It.IsAny<List<Entities.Entities.Solicitacao>>(), It.IsAny<bool>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new Exception("Teste de unidade remover solicitação firebase"));
        
        var evento = 
            SolicitacaoEventsFixture.CriarSolicitacaoConcluidaEvent([solicitacao]);
    
        // Act
        
        await mediator.PublicarEventoAsync(evento, CancellationToken.None);
        
        // Assert
        
        var eventoPublicado = 
            _dependencyInjectorFactory.Harness.Published.Any<SolicitacaoConcluidaEvent>();
        
        var eventoConsumido = 
            _dependencyInjectorFactory.Harness.Consumed.Any<SolicitacaoConcluidaEvent>();

        var eventoSolicitacaoEnvidaaoMongoPublicado =
           _dependencyInjectorFactory.Harness.Published.Any<SolicitacaoEnviadoParaMongoEvent>();

        var eventoSolicitacaoEnvidaaoMongoConsumido =
            _dependencyInjectorFactory.Harness.Consumed.Any<SolicitacaoEnviadoParaMongoEvent>();

        var falhaDisparada =
            _dependencyInjectorFactory.Harness.Published.Any<Fault<SolicitacaoConcluidaEvent>>();
        
        await Task.WhenAll(eventoPublicado, eventoConsumido, falhaDisparada, eventoSolicitacaoEnvidaaoMongoPublicado, eventoSolicitacaoEnvidaaoMongoConsumido);
        
        eventoPublicado.Result.ShouldBeTrue();
        eventoConsumido.Result.ShouldBeTrue();
        falhaDisparada.Result.ShouldBeTrue();
        eventoSolicitacaoEnvidaaoMongoPublicado.Result.ShouldBeFalse();
        eventoSolicitacaoEnvidaaoMongoConsumido.Result.ShouldBeFalse();

        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoRepository>().Verify(
            repository => repository.BulkUpdateAsync(It.IsAny<List<Entities.Entities.Solicitacao>>(), It.IsAny<bool>(), It.IsAny<CancellationToken>()),
            Times.Once);

        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoFirebaseRepository>().Verify(
            repository => repository.RemoveAsync(It.IsAny<List<string>>(), It.IsAny<CancellationToken>()),
            Times.Never);
    }

    [Fact(DisplayName = "[Enviar solicitação ao mongo] - Deve enviar solicitações ao mongo")]
    [Trait("Business", "Finalizar solicitação firebase")]
    public async Task Publicar_SolicitacaoFinalizadaEmLoteEvent_DeveEnviarSolicitacoesAoMongo()
    {
        // Arrange

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        var solicitacao = _solicitacaoFixture.GerarSolicitacaoConfirmada();
        var solicitacaoInexistente = _solicitacaoFixture.GerarSolicitacaoConfirmada();

   _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoFirebaseRepository>()
            .Setup(repository => repository.GetByIdAsync(It.IsAny<string[]>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([solicitacao]);

        var evento = 
            SolicitacaoEventsFixture.CriarSolicitacaoConcluidaEvent([solicitacao, solicitacaoInexistente]);
    
        // Act
        
        await mediator.PublicarEventoAsync(evento, CancellationToken.None);

        // Assert

        var eventoPublicado =
            _dependencyInjectorFactory.Harness.Published.Any<SolicitacaoConcluidaEvent>();

        var eventoConsumido =
            _dependencyInjectorFactory.Harness.Consumed.Any<SolicitacaoConcluidaEvent>();

        var eventoSolicitacaoEnvidaaoMongoPublicado =
           _dependencyInjectorFactory.Harness.Published.Any<SolicitacaoEnviadoParaMongoEvent>();

        var eventoSolicitacaoEnvidaaoMongoConsumido =
            _dependencyInjectorFactory.Harness.Consumed.Any<SolicitacaoEnviadoParaMongoEvent>();

        await Task.WhenAll(eventoPublicado, eventoConsumido, eventoSolicitacaoEnvidaaoMongoPublicado, eventoSolicitacaoEnvidaaoMongoConsumido);

        eventoPublicado.Result.ShouldBeTrue();
        eventoConsumido.Result.ShouldBeTrue();
        eventoSolicitacaoEnvidaaoMongoPublicado.Result.ShouldBeTrue();
        eventoSolicitacaoEnvidaaoMongoConsumido.Result.ShouldBeTrue();

        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoRepository>().Verify(
            repository => repository.BulkUpdateAsync(It.IsAny<List<Entities.Entities.Solicitacao>>(), It.IsAny<bool>(), It.IsAny<CancellationToken>()),
            Times.Once);

        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoFirebaseRepository>().Verify(
            repository => repository.RemoveAsync(It.IsAny<List<string>>(), It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact(DisplayName = "[Enviar solicitação ao mongo] - Deve lidar com lista vazia de solicitações")]
    [Trait("Business", "Enviar solicitação ao mongo")]
    public async Task Publicar_SolicitacaoFinalizadaEmLoteEvent_DeveProcessarListaVazia()
    {
        // Arrange
        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoFirebaseRepository>()
            .Setup(repository => repository.GetByIdAsync(It.IsAny<string[]>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        var evento = SolicitacaoEventsFixture.CriarSolicitacaoConcluidaEvent([]);

        // Act
        await mediator.PublicarEventoAsync(evento, CancellationToken.None);

        // Assert
        var eventoPublicado =
            _dependencyInjectorFactory.Harness.Published.Any<SolicitacaoConcluidaEvent>();

        var eventoConsumido =
            _dependencyInjectorFactory.Harness.Consumed.Any<SolicitacaoConcluidaEvent>();

        var eventoSolicitacaoEnvidaaoMongoPublicado =
           _dependencyInjectorFactory.Harness.Published.Any<SolicitacaoEnviadoParaMongoEvent>();

        var eventoSolicitacaoEnvidaaoMongoConsumido =
            _dependencyInjectorFactory.Harness.Consumed.Any<SolicitacaoEnviadoParaMongoEvent>();

        await Task.WhenAll(eventoPublicado, eventoConsumido, eventoSolicitacaoEnvidaaoMongoPublicado, eventoSolicitacaoEnvidaaoMongoConsumido);

        eventoPublicado.Result.ShouldBeTrue();
        eventoConsumido.Result.ShouldBeTrue();
        eventoSolicitacaoEnvidaaoMongoPublicado.Result.ShouldBeFalse();
        eventoSolicitacaoEnvidaaoMongoConsumido.Result.ShouldBeFalse();

        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoRepository>().Verify(
            repository => repository.BulkUpdateAsync(It.IsAny<List<Entities.Entities.Solicitacao>>(), It.IsAny<bool>(), It.IsAny<CancellationToken>()),
            Times.Never);

        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoFirebaseRepository>().Verify(
            repository => repository.RemoveAsync(It.IsAny<List<string>>(), It.IsAny<CancellationToken>()),
            Times.Never);
    }
}