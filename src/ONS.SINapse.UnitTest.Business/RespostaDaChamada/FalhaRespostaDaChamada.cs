using ONS.SINapse.Business.Imp.Business;
using ONS.SINapse.Business.Imp.Workers.Messages;
using ONS.SINapse.Entities.Entities;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Repository.IRepository;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Kafka.Providers;
using System.Linq.Expressions;

namespace ONS.SINapse.UnitTest.Business.RespostaDaChamada;

public partial class RespostaDaChamadaTest
{
    [Fact(DisplayName = "[Resposta Chamada] - Deve falhar ao responder a chamada")]
    [Trait("Business", "Resposta da Chamada")]
    public async Task DeveFalharAoResponderChamada()
    {
        // Arrange

        var request = new ResponderChamadaDto(new List<RespondertemDto>());

        var controleDeChamada = _controleDeChamadaFixture.GerarControleDeChamadaComStatusLiberadoParaChamada();

        _dependencyInjectorFactory.Mocker
            .GetMock<IControleDeChamadaRepository>()
            .Setup(repository => repository.GetOneAsync(It.IsAny<Expression<Func<Entities.Entities.ControleDeChamada, bool>>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(controleDeChamada);

        // Act
        await _dependencyInjectorFactory.Mocker.CreateInstance<RespostaDaChamadaBusiness>().ResponderChamada(request, CancellationToken.None);

        // Assert

        _dependencyInjectorFactory.Mocker.GetMock<IRespostaDaChamadaRepository>().Verify(
            repository => repository.BulkUpdateAsync(It.IsAny<ICollection<Entities.Entities.RespostaDaChamada>>(), It.IsAny<CancellationToken>()),
            Times.Never);

        _dependencyInjectorFactory.Mocker.GetMock<IControleDeChamadaRepository>().Verify(
            repository => repository.GetOneAsync(It.IsAny<Expression<Func<Entities.Entities.ControleDeChamada, bool>>>(), It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact(DisplayName = "[Resposta Chamada] - Não deve processar a chamada")]
    [Trait("Business", "Resposta da Chamada")]
    public async Task NaoDeveProcessarChamada()
    {
        // Arrange

        var controleDeChamada = _controleDeChamadaFixture.GerarControleDeChamadaComStatusLiberadoParaChamada();

        // Act

        var resposta = await _dependencyInjectorFactory.Mocker.CreateInstance<RespostaDaChamadaBusiness>().ProcessarChamada(controleDeChamada, CancellationToken.None);

        // Assert

        resposta.ShouldBeEmpty();

        _dependencyInjectorFactory.Mocker.GetMock<IRespostaDaChamadaRepository>().Verify(
            repository => repository.BulkUpdateAsync(It.IsAny<ICollection<Entities.Entities.RespostaDaChamada>>(), It.IsAny<CancellationToken>()),
            Times.Never);
    }

    [Fact(DisplayName = "[Resposta Chamada] - Não deve publicar a lista de chamada com chamada com status invalido")]
    [Trait("Business", "Resposta da Chamada")]
    public async Task DevePublicarListaDeChamadaComStatusDeChamadaInvalido()
    {
        // Arrange

        var controleDeChamada = _controleDeChamadaFixture.GerarControleDeChamadaComStatusLiberadoParaChamada();

        // Act

        await _dependencyInjectorFactory.Mocker.CreateInstance<RespostaDaChamadaBusiness>()
            .PublicarListaDeChamada(controleDeChamada, CancellationToken.None);

        // Arrange

        _dependencyInjectorFactory.Mocker.GetMock<IProducerProvider<StatusAgenteMessage>>().Verify(
            producer => producer.PublishAsync(It.IsAny<StatusAgenteMessage>(), It.IsAny<Dictionary<string, string>>(), It.IsAny<CancellationToken>()),
            Times.Never);

        _dependencyInjectorFactory.Mocker.GetMock<IHistoricoDeAcessoRepository>().Verify(
            repository => repository.BulkCreateAsync(It.IsAny<IEnumerable<HistoricoAcesso>>(), It.IsAny<CancellationToken>()),
            Times.Never);
    }

    [Fact(DisplayName = "[Resposta Chamada] - Não deve publicar a lista de chamada sem centros")]
    [Trait("Business", "Resposta da Chamada")]
    public async Task DevePublicarListaDeChamadaSemCentros()
    {
        // Arrange

        var controleDeChamada = _controleDeChamadaFixture.GerarControleDeChamadaComStatusProcessamentoFinalizado();

        _dependencyInjectorFactory.Mocker
            .GetMock<IRespostaDaChamadaRepository>()
            .Setup(repository => repository.BuscarPendentesDeNotificacaoAsync(It.IsAny<CancellationToken>()));
        // Act

        await _dependencyInjectorFactory.Mocker.CreateInstance<RespostaDaChamadaBusiness>()
            .PublicarListaDeChamada(controleDeChamada, CancellationToken.None);

        // Arrange

        _dependencyInjectorFactory.Mocker.GetMock<IProducerProvider<StatusAgenteMessage>>().Verify(
            producer => producer.PublishAsync(It.IsAny<StatusAgenteMessage>(), It.IsAny<Dictionary<string, string>>(), It.IsAny<CancellationToken>()),
            Times.Never);

        _dependencyInjectorFactory.Mocker.GetMock<IHistoricoDeAcessoRepository>().Verify(
            repository => repository.BulkCreateAsync(It.IsAny<IEnumerable<HistoricoAcesso>>(), It.IsAny<CancellationToken>()),
            Times.Never);
    }

    [Fact(DisplayName = "[Resposta Chamada] - Deve disparar exceção ao publicar lista de chamada")]
    [Trait("Business", "Resposta da Chamada")]
    public async Task DeveDispararExcecaoAoPublicarListaDeChamada()
    {
        // Arrange

        var respostaDasChamadas = new List<Entities.Entities.RespostaDaChamada>
        {
            new(_objetoDeManobraFixture.GerarObjetoDeManobra("CO", "NO"), 0)
            {
                Status = StatusDeCentroAgente.Online
            }
        };

        var controleDeChamada = _controleDeChamadaFixture.GerarControleDeChamadaComStatusProcessamentoFinalizado();

        _dependencyInjectorFactory.Mocker
            .GetMock<IRespostaDaChamadaRepository>()
            .Setup(repository => repository.BuscarPendentesDeNotificacaoAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(respostaDasChamadas);

        _dependencyInjectorFactory.Mocker.GetMock<IProducerProvider<StatusAgenteMessage>>()
            .Setup(s => s.EstaConectado())
            .Returns(true);

        _dependencyInjectorFactory.Mocker.GetMock<IProducerProvider<StatusAgenteMessage>>()
            .Setup(s => s.PublishAsync(It.IsAny<StatusAgenteMessage>(), It.IsAny<Dictionary<string, string>>(), It.IsAny<CancellationToken>()))
            .Throws<Exception>();

        // Act

        await _dependencyInjectorFactory.Mocker.CreateInstance<RespostaDaChamadaBusiness>()
            .PublicarListaDeChamada(controleDeChamada, CancellationToken.None);

        // Arrange

        _dependencyInjectorFactory.Mocker.GetMock<IProducerProvider<StatusAgenteMessage>>().Verify(
            producer => producer.PublishAsync(It.IsAny<StatusAgenteMessage>(), It.IsAny<Dictionary<string, string>>(), It.IsAny<CancellationToken>()),
            Times.AtLeastOnce);
    }

    [Fact(DisplayName = "[Resposta Chamada] - Não deve obter os agentes online")]
    [Trait("Business", "Resposta da Chamada")]
    public async Task NaoDeveObterAgentesOnline()
    {
        // Arrange

        var controleDeChamada = _controleDeChamadaFixture.GerarControleDeChamadaComStatusProcessamentoFinalizado();

        _dependencyInjectorFactory.Mocker
            .GetMock<IControleDeChamadaRepository>()
            .Setup(repository => repository.GetOneAsync(It.IsAny<Expression<Func<Entities.Entities.ControleDeChamada, bool>>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(controleDeChamada);

        _dependencyInjectorFactory.Mocker
            .GetMock<IRespostaDaChamadaRepository>()
            .Setup(repository => repository.GetAsync(It.IsAny<Expression<Func<Entities.Entities.RespostaDaChamada, bool>>>(), It.IsAny<CancellationToken>()));

        // Act

        var resposta = await _dependencyInjectorFactory.Mocker.CreateInstance<RespostaDaChamadaBusiness>()
            .ObterAgentesOnline(CancellationToken.None);

        // Assert

        resposta.ShouldBeNull();

        _dependencyInjectorFactory.Mocker.GetMock<IControleDeChamadaRepository>().Verify(
            repository => repository.GetOneAsync(It.IsAny<Expression<Func<Entities.Entities.ControleDeChamada, bool>>>(), It.IsAny<CancellationToken>()),
            Times.Once);

        _dependencyInjectorFactory.Mocker.GetMock<IRespostaDaChamadaRepository>().Verify(
            repository => repository.GetAsync(It.IsAny<Expression<Func<Entities.Entities.RespostaDaChamada, bool>>>(), It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact(DisplayName = "[Resposta Chamada] - Não deve obter os agentes online por não obter a chamada")]
    [Trait("Business", "Resposta da Chamada")]
    public async Task NaoDeveObterAgentesOnlinePorNaoObterChamada()
    {
        // Arrange

        _dependencyInjectorFactory.Mocker
            .GetMock<IControleDeChamadaRepository>()
            .Setup(repository => repository.GetOneAsync(It.IsAny<Expression<Func<Entities.Entities.ControleDeChamada, bool>>>(), It.IsAny<CancellationToken>()));
        
        // Act

        var resposta = await _dependencyInjectorFactory.Mocker.CreateInstance<RespostaDaChamadaBusiness>()
            .ObterAgentesOnline(CancellationToken.None);

        // Assert

        resposta.ShouldBeEmpty();

        _dependencyInjectorFactory.Mocker.GetMock<IControleDeChamadaRepository>().Verify(
            repository => repository.GetOneAsync(It.IsAny<Expression<Func<Entities.Entities.ControleDeChamada, bool>>>(), It.IsAny<CancellationToken>()),
            Times.Once);

        _dependencyInjectorFactory.Mocker.GetMock<IRespostaDaChamadaRepository>().Verify(
            repository => repository.GetAsync(It.IsAny<Expression<Func<Entities.Entities.RespostaDaChamada, bool>>>(), It.IsAny<CancellationToken>()),
            Times.Never);
    }

    [Fact(DisplayName = "[Resposta Chamada] - Não deve obter os códigos dos agentes online")]
    [Trait("Business", "Resposta da Chamada")]
    public async Task NaoDeveObterCodigosAgentesOnline()
    {
        // Arrange

        var controleDeChamada = _controleDeChamadaFixture.GerarControleDeChamadaComStatusProcessamentoFinalizado();

        _dependencyInjectorFactory.Mocker
            .GetMock<IControleDeChamadaRepository>()
            .Setup(repository => repository.GetOneAsync(It.IsAny<Expression<Func<Entities.Entities.ControleDeChamada, bool>>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(controleDeChamada);

        _dependencyInjectorFactory.Mocker
            .GetMock<IRespostaDaChamadaRepository>()
            .Setup(repository => repository.ObterCodigoAgentesOnlineAsync(It.IsAny<Expression<Func<Entities.Entities.RespostaDaChamada, bool>>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        // Act

        var resposta = await _dependencyInjectorFactory.Mocker.CreateInstance<RespostaDaChamadaBusiness>()
            .ObterCodigoAgentesOnlineAsync(CancellationToken.None);

        // Assert

        resposta.ShouldBeEmpty();

        _dependencyInjectorFactory.Mocker.GetMock<IControleDeChamadaRepository>().Verify(
            repository => repository.GetOneAsync(It.IsAny<Expression<Func<Entities.Entities.ControleDeChamada, bool>>>(), It.IsAny<CancellationToken>()),
            Times.Once);

        _dependencyInjectorFactory.Mocker.GetMock<IRespostaDaChamadaRepository>().Verify(
            repository => repository.ObterCodigoAgentesOnlineAsync(It.IsAny<Expression<Func<Entities.Entities.RespostaDaChamada, bool>>>(), It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact(DisplayName = "[Resposta Chamada] - Não deve obter os códigos dos agentes online por não obter a chamada")]
    [Trait("Business", "Resposta da Chamada")]
    public async Task NaoDeveObterCodigosAgentesOnlinePorNaoObterChamada()
    {
        // Arrange

        _dependencyInjectorFactory.Mocker
            .GetMock<IControleDeChamadaRepository>()
            .Setup(repository => repository.GetOneAsync(It.IsAny<Expression<Func<Entities.Entities.ControleDeChamada, bool>>>(), It.IsAny<CancellationToken>()));

        // Act

        var resposta = await _dependencyInjectorFactory.Mocker.CreateInstance<RespostaDaChamadaBusiness>()
            .ObterCodigoAgentesOnlineAsync(CancellationToken.None);

        // Assert

        resposta.ShouldBeEmpty();

        _dependencyInjectorFactory.Mocker.GetMock<IControleDeChamadaRepository>().Verify(
            repository => repository.GetOneAsync(It.IsAny<Expression<Func<Entities.Entities.ControleDeChamada, bool>>>(), It.IsAny<CancellationToken>()),
            Times.Once);

        _dependencyInjectorFactory.Mocker.GetMock<IRespostaDaChamadaRepository>().Verify(
            repository => repository.ObterCodigoAgentesOnlineAsync(It.IsAny<Expression<Func<Entities.Entities.RespostaDaChamada, bool>>>(), It.IsAny<CancellationToken>()),
            Times.Never);
    }

    [Fact(DisplayName = "[Resposta Chamada] - Não deve obter os códigos dos centro-agentes inativos")]
    [Trait("Business", "Resposta da Chamada")]
    public async Task NaoDeveObterObterCentroAgentesInativos()
    {
        // Arrange

        var respostaDasChamadas = new List<Entities.Entities.RespostaDaChamada>
        {
            new(_objetoDeManobraFixture.GerarObjetoDeManobra("CO", "NO"), 0)
            {
                Status = StatusDeCentroAgente.Online
            }
        };

        _dependencyInjectorFactory.Mocker
            .GetMock<IRespostaDaChamadaRepository>()
            .Setup(repository => repository.GetAsync(It.IsAny<Expression<Func<Entities.Entities.RespostaDaChamada, bool>>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(respostaDasChamadas);

        // Act

        var resposta = await _dependencyInjectorFactory.Mocker.CreateInstance<RespostaDaChamadaBusiness>()
            .ObterCentroAgentesInativos(["CO"], CancellationToken.None);

        // Assert

        resposta.ShouldBeEmpty();

        _dependencyInjectorFactory.Mocker.GetMock<IRespostaDaChamadaRepository>().Verify(
            repository => repository.GetAsync(It.IsAny<Expression<Func<Entities.Entities.RespostaDaChamada, bool>>>(), It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact(DisplayName = "[Resposta Chamada] - Deve retornar vazio ao buscar por centro")]
    [Trait("Business", "Resposta da Chamada")]
    public async Task DeveRetornarVazioAoBuscarPorCentro()
    {
        // Arrange

        _dependencyInjectorFactory.Mocker
            .GetMock<IRespostaDaChamadaRepository>()
            .Setup(repository => repository.GetAsync(It.IsAny<Expression<Func<Entities.Entities.RespostaDaChamada, bool>>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        // Act

        var resposta = await _dependencyInjectorFactory.Mocker.CreateInstance<RespostaDaChamadaBusiness>()
            .BuscarPorCentro(["CO"], CancellationToken.None);

        // Assert

        resposta.ShouldBeEmpty();

        _dependencyInjectorFactory.Mocker.GetMock<IRespostaDaChamadaRepository>().Verify(
            repository => repository.GetAsync(It.IsAny<Expression<Func<Entities.Entities.RespostaDaChamada, bool>>>(), It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact(DisplayName = "[Resposta Chamada] - Deve retornar nulo ao buscar por centro")]
    [Trait("Business", "Resposta da Chamada")]
    public async Task DeveRetornarNuloAoBuscarPorCentro()
    {
        // Arrange

        _dependencyInjectorFactory.Mocker
            .GetMock<IRespostaDaChamadaRepository>()
            .Setup(repository => repository.GetAsync(It.IsAny<Expression<Func<Entities.Entities.RespostaDaChamada, bool>>>(), It.IsAny<CancellationToken>()));

        // Act

        var resposta = await _dependencyInjectorFactory.Mocker.CreateInstance<RespostaDaChamadaBusiness>()
            .BuscarPorCentro(["CO"], CancellationToken.None);

        // Assert

        resposta.ShouldBeNull();

        _dependencyInjectorFactory.Mocker.GetMock<IRespostaDaChamadaRepository>().Verify(
            repository => repository.GetAsync(It.IsAny<Expression<Func<Entities.Entities.RespostaDaChamada, bool>>>(), It.IsAny<CancellationToken>()),
            Times.Once);
    }
}