using ONS.SINapse.Business.Imp.Business;
using ONS.SINapse.Business.Imp.Workers.Messages;
using ONS.SINapse.Entities.Entities;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Repository.IRepository;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Kafka.Providers;
using System.Linq.Expressions;

namespace ONS.SINapse.UnitTest.Business.RespostaDaChamada;

public partial class RespostaDaChamadaTest
{
    [Fact(DisplayName = "[Resposta Chamada] - Deve responder a chamada")]
    [Trait("Business", "Resposta da Chamada")]
    public async Task DeveResponderChamada()
    {
        // Arrange

        var respondertemDtos = new List<RespondertemDto>()
        {
            new ()
            {
                CentroAgente = new ObjetoDeManobraDto("CO", "NO"),
                Equipamentos = new[] { "ABC" }
            }
        };

        var request = new ResponderChamadaDto(respondertemDtos);

        var respostaDasChamadas = request.Resposta
            .Select(r => (ObjetoDeManobra)r.CentroAgente)
            .Distinct()
            .Select(x => new Entities.Entities.RespostaDaChamada(x, 0))
            .ToList();

        var controleDeChamada = _controleDeChamadaFixture.GerarControleDeChamadaComStatusChamadaEmAndamento();

        _dependencyInjectorFactory.Mocker
            .GetMock<IControleDeChamadaRepository>()
            .Setup(repository => repository.GetOneAsync(It.IsAny<Expression<Func<Entities.Entities.ControleDeChamada, bool>>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(controleDeChamada);

        _dependencyInjectorFactory.Mocker
            .GetMock<IRespostaDaChamadaRepository>()
            .Setup(repository => repository.FindOrCreate(It.IsAny<ObjetoDeManobra[]>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(respostaDasChamadas);

        // Act
        await _dependencyInjectorFactory.Mocker.CreateInstance<RespostaDaChamadaBusiness>().ResponderChamada(request, CancellationToken.None);

        // Assert

        _dependencyInjectorFactory.Mocker.GetMock<IRespostaDaChamadaRepository>().Verify(
            repository => repository.BulkUpdateAsync(It.IsAny<ICollection<Entities.Entities.RespostaDaChamada>>(), It.IsAny<CancellationToken>()),
            Times.Once);

        _dependencyInjectorFactory.Mocker.GetMock<IControleDeChamadaRepository>().Verify(
            repository => repository.GetOneAsync(It.IsAny<Expression<Func<Entities.Entities.ControleDeChamada, bool>>>(), It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact(DisplayName = "[Resposta Chamada] - Deve processar a chamada")]
    [Trait("Business", "Resposta da Chamada")]
    public async Task DeveProcessarChamada()
    {
        // Arrange

        var respondertemDtos = new List<RespondertemDto>()
        {
            new ()
            {
                CentroAgente = new ObjetoDeManobraDto("CO", "NO"),
                Equipamentos = new[] { "ABC" }
            }
        };

        var request = new ResponderChamadaDto(respondertemDtos);

        var respostaDasChamadas = request.Resposta
            .Select(r => (ObjetoDeManobra)r.CentroAgente)
            .Distinct()
            .Select(x => new Entities.Entities.RespostaDaChamada(x, 0))
            .ToList();

        var controleDeChamada = _controleDeChamadaFixture.GerarControleDeChamadaComStatusProcessando();

        _dependencyInjectorFactory.Mocker
            .GetMock<IRespostaDaChamadaRepository>()
            .Setup(repository => repository.GetAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(respostaDasChamadas);

        // Act

        var resposta = await _dependencyInjectorFactory.Mocker.CreateInstance<RespostaDaChamadaBusiness>()
            .ProcessarChamada(controleDeChamada, CancellationToken.None);

        // Assert

        resposta.ShouldNotBeEmpty();

        _dependencyInjectorFactory.Mocker.GetMock<IRespostaDaChamadaRepository>().Verify(
            repository => repository.BulkUpdateAsync(It.IsAny<ICollection<Entities.Entities.RespostaDaChamada>>(), It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact(DisplayName = "[Resposta Chamada] - Deve publicar a lista de chamada com status de agente online")]
    [Trait("Business", "Resposta da Chamada")]
    public async Task DevePublicarListaDeChamadaComStatusDeAgenteOnline()
    {
        // Arrange

        var respostaDasChamadas = new List<Entities.Entities.RespostaDaChamada>
        {
            new(_objetoDeManobraFixture.GerarObjetoDeManobra("CO", "NO"), 0)
            {
                Status = StatusDeCentroAgente.Online
            }
        };

        var controleDeChamada = _controleDeChamadaFixture.GerarControleDeChamadaComStatusProcessamentoFinalizado();

        _dependencyInjectorFactory.Mocker
            .GetMock<IRespostaDaChamadaRepository>()
            .Setup(repository => repository.BuscarPendentesDeNotificacaoAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(respostaDasChamadas);

        _dependencyInjectorFactory.Mocker.GetMock<IProducerProvider<StatusAgenteMessage>>()
            .Setup(s => s.EstaConectado())
            .Returns(true);

        _dependencyInjectorFactory.Mocker.GetMock<IProducerProvider<StatusAgenteMessage>>()
            .Setup(s => s.PublishAsync(It.IsAny<StatusAgenteMessage>(), It.IsAny<Dictionary<string, string>>(), It.IsAny<CancellationToken>()));

        _dependencyInjectorFactory.Mocker.GetMock<IHistoricoDeAcessoRepository>()
            .Setup(s => s.BulkCreateAsync(It.IsAny<IEnumerable<HistoricoAcesso>>(), It.IsAny<CancellationToken>()));

        // Act

        await _dependencyInjectorFactory.Mocker.CreateInstance<RespostaDaChamadaBusiness>()
            .PublicarListaDeChamada(controleDeChamada, CancellationToken.None);

        // Arrange

        _dependencyInjectorFactory.Mocker.GetMock<IProducerProvider<StatusAgenteMessage>>().Verify(
            producer => producer.PublishAsync(It.IsAny<StatusAgenteMessage>(), It.IsAny<Dictionary<string, string>>(), It.IsAny<CancellationToken>()),
            Times.Once);

        _dependencyInjectorFactory.Mocker.GetMock<IHistoricoDeAcessoRepository>().Verify(
            repository => repository.BulkCreateAsync(It.IsAny<IEnumerable<HistoricoAcesso>>(), It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact(DisplayName = "[Resposta Chamada] - Deve publicar a lista de chamada com status de agente offline")]
    [Trait("Business", "Resposta da Chamada")]
    public async Task DevePublicarListaDeChamadaComStatusDeAgenteOffline()
    {
        // Arrange

        var objetoDeManobra = _objetoDeManobraFixture.GerarObjetoDeManobra("CO", "NO");

        var respostaDasChamadas = new List<Entities.Entities.RespostaDaChamada>
        {
            new(objetoDeManobra, 0)
            {
                Status = StatusDeCentroAgente.Offline
            }
        };

        var historicosDeAcesso = new List<HistoricoAcesso>()
        {
            new(string.Empty, objetoDeManobra, DateTime.Now, DateTime.UtcNow)
        };

        var controleDeChamada = _controleDeChamadaFixture.GerarControleDeChamadaComStatusProcessamentoFinalizado();

        _dependencyInjectorFactory.Mocker
            .GetMock<IRespostaDaChamadaRepository>()
            .Setup(repository => repository.BuscarPendentesDeNotificacaoAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(respostaDasChamadas);

        _dependencyInjectorFactory.Mocker.GetMock<IProducerProvider<StatusAgenteMessage>>()
            .Setup(s => s.EstaConectado())
            .Returns(true);

        _dependencyInjectorFactory.Mocker.GetMock<IProducerProvider<StatusAgenteMessage>>()
            .Setup(s => s.PublishAsync(It.IsAny<StatusAgenteMessage>(), It.IsAny<Dictionary<string, string>>(), It.IsAny<CancellationToken>()));

        _dependencyInjectorFactory.Mocker.GetMock<IHistoricoDeAcessoRepository>()
            .Setup(s => s.BulkCreateAsync(It.IsAny<IEnumerable<HistoricoAcesso>>(), It.IsAny<CancellationToken>()));

        _dependencyInjectorFactory.Mocker.GetMock<IHistoricoDeAcessoRepository>()
            .Setup(s => s.GetAsync(It.IsAny<Expression<Func<HistoricoAcesso, bool>>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(historicosDeAcesso);

        // Act

        await _dependencyInjectorFactory.Mocker.CreateInstance<RespostaDaChamadaBusiness>()
            .PublicarListaDeChamada(controleDeChamada, CancellationToken.None);

        // Arrange

        _dependencyInjectorFactory.Mocker.GetMock<IProducerProvider<StatusAgenteMessage>>().Verify(
            producer => producer.PublishAsync(It.IsAny<StatusAgenteMessage>(), It.IsAny<Dictionary<string, string>>(), It.IsAny<CancellationToken>()),
            Times.Once);

        _dependencyInjectorFactory.Mocker.GetMock<IHistoricoDeAcessoRepository>().Verify(
            repository => repository.GetAsync(It.IsAny<Expression<Func<HistoricoAcesso, bool>>>(), It.IsAny<CancellationToken>()),
            Times.Once);

        _dependencyInjectorFactory.Mocker.GetMock<IHistoricoDeAcessoRepository>().Verify(
            repository => repository.BulkUpdateAsync(It.IsAny<IEnumerable<HistoricoAcesso>>(), It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact(DisplayName = "[Resposta Chamada] - Deve obter os agentes online")]
    [Trait("Business", "Resposta da Chamada")]
    public async Task DeveObterAgentesOnline()
    {
        // Arrange

        var respostaDasChamadas = new List<Entities.Entities.RespostaDaChamada>
        {
            new(_objetoDeManobraFixture.GerarObjetoDeManobra("CO", "NO"), 0)
            {
                Status = StatusDeCentroAgente.Online
            }
        };

        var controleDeChamada = _controleDeChamadaFixture.GerarControleDeChamadaComStatusProcessamentoFinalizado();

        _dependencyInjectorFactory.Mocker
            .GetMock<IControleDeChamadaRepository>()
            .Setup(repository => repository.GetOneAsync(It.IsAny<Expression<Func<Entities.Entities.ControleDeChamada, bool>>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(controleDeChamada);

        _dependencyInjectorFactory.Mocker
            .GetMock<IRespostaDaChamadaRepository>()
            .Setup(repository => repository.GetAsync(It.IsAny<Expression<Func<Entities.Entities.RespostaDaChamada, bool>>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(respostaDasChamadas);

        // Act

        var resposta = await _dependencyInjectorFactory.Mocker.CreateInstance<RespostaDaChamadaBusiness>()
            .ObterAgentesOnline(CancellationToken.None);

        // Assert

        resposta.ShouldNotBeEmpty();

        _dependencyInjectorFactory.Mocker.GetMock<IControleDeChamadaRepository>().Verify(
            repository => repository.GetOneAsync(It.IsAny<Expression<Func<Entities.Entities.ControleDeChamada, bool>>>(), It.IsAny<CancellationToken>()),
            Times.Once);

        _dependencyInjectorFactory.Mocker.GetMock<IRespostaDaChamadaRepository>().Verify(
            repository => repository.GetAsync(It.IsAny<Expression<Func<Entities.Entities.RespostaDaChamada, bool>>>(), It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact(DisplayName = "[Resposta Chamada] - Deve obter os códigos dos agentes online")]
    [Trait("Business", "Resposta da Chamada")]
    public async Task DeveObterCodigosAgentesOnline()
    {
        // Arrange

        var controleDeChamada = _controleDeChamadaFixture.GerarControleDeChamadaComStatusProcessamentoFinalizado();

        _dependencyInjectorFactory.Mocker
            .GetMock<IControleDeChamadaRepository>()
            .Setup(repository => repository.GetOneAsync(It.IsAny<Expression<Func<Entities.Entities.ControleDeChamada, bool>>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(controleDeChamada);

        _dependencyInjectorFactory.Mocker
            .GetMock<IRespostaDaChamadaRepository>()
            .Setup(repository => repository.ObterCodigoAgentesOnlineAsync(It.IsAny<Expression<Func<Entities.Entities.RespostaDaChamada, bool>>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(["AA", "BB", "CC"]);

        // Act

        var resposta = await _dependencyInjectorFactory.Mocker.CreateInstance<RespostaDaChamadaBusiness>()
            .ObterCodigoAgentesOnlineAsync(CancellationToken.None);

        // Assert

        resposta.ShouldNotBeEmpty();

        _dependencyInjectorFactory.Mocker.GetMock<IControleDeChamadaRepository>().Verify(
            repository => repository.GetOneAsync(It.IsAny<Expression<Func<Entities.Entities.ControleDeChamada, bool>>>(), It.IsAny<CancellationToken>()),
            Times.Once);

        _dependencyInjectorFactory.Mocker.GetMock<IRespostaDaChamadaRepository>().Verify(
            repository => repository.ObterCodigoAgentesOnlineAsync(It.IsAny<Expression<Func<Entities.Entities.RespostaDaChamada, bool>>>(), It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact(DisplayName = "[Resposta Chamada] - Deve obter os códigos dos centro-agentes inativos")]
    [Trait("Business", "Resposta da Chamada")]
    public async Task DeveObterObterCentroAgentesInativos()
    {
        // Arrange

        var respostaDasChamadas = new List<Entities.Entities.RespostaDaChamada>
        {
            new(_objetoDeManobraFixture.GerarObjetoDeManobra("CO", "NO"), 0)
            {
                Status = StatusDeCentroAgente.Online
            }
        };

        _dependencyInjectorFactory.Mocker
            .GetMock<IRespostaDaChamadaRepository>()
            .Setup(repository => repository.GetAsync(It.IsAny<Expression<Func<Entities.Entities.RespostaDaChamada, bool>>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(respostaDasChamadas);

        // Act

        var resposta = await _dependencyInjectorFactory.Mocker.CreateInstance<RespostaDaChamadaBusiness>()
            .ObterCentroAgentesInativos(["CO", "FOO"], CancellationToken.None);

        // Assert

        resposta.ShouldNotBeEmpty();
        resposta.ShouldContain("FOO");

        _dependencyInjectorFactory.Mocker.GetMock<IRespostaDaChamadaRepository>().Verify(
            repository => repository.GetAsync(It.IsAny<Expression<Func<Entities.Entities.RespostaDaChamada, bool>>>(), It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact(DisplayName = "[Resposta Chamada] - Deve buscar por centro")]
    [Trait("Business", "Resposta da Chamada")]
    public async Task DeveBuscarPorCentro()
    {
        // Arrange

        var respostaDasChamadas = new List<Entities.Entities.RespostaDaChamada>
        {
            new(_objetoDeManobraFixture.GerarObjetoDeManobra("CO", "NO"), 0)
            {
                Status = StatusDeCentroAgente.Online
            }
        };

        _dependencyInjectorFactory.Mocker
            .GetMock<IRespostaDaChamadaRepository>()
            .Setup(repository => repository.GetAsync(It.IsAny<Expression<Func<Entities.Entities.RespostaDaChamada, bool>>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(respostaDasChamadas);

        // Act

        var resposta = await _dependencyInjectorFactory.Mocker.CreateInstance<RespostaDaChamadaBusiness>()
            .BuscarPorCentro(["CO"], CancellationToken.None);

        // Assert

        resposta.ShouldNotBeEmpty();

        _dependencyInjectorFactory.Mocker.GetMock<IRespostaDaChamadaRepository>().Verify(
            repository => repository.GetAsync(It.IsAny<Expression<Func<Entities.Entities.RespostaDaChamada, bool>>>(), It.IsAny<CancellationToken>()),
            Times.Once);
    }
}