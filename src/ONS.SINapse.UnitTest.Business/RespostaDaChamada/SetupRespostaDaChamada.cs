using Microsoft.Extensions.Options;
using ONS.SINapse.UnitTest.Business.Fixtures.Collections;
using ONS.SINapse.UnitTest.Shared;
using ONS.SINapse.UnitTest.Shared.Fixtures;

namespace ONS.SINapse.UnitTest.Business.RespostaDaChamada;

[Collection(nameof(RespostaDaChamadaCollection))]

public partial class RespostaDaChamadaTest
{
    private readonly MockDependencyInjectorFactory _dependencyInjectorFactory;
    private readonly ControleDeChamadaFixture _controleDeChamadaFixture;
    private readonly ProducerProviderFixture _producerProviderFixture;
    private readonly ObjetoManobraFixture _objetoDeManobraFixture;

    public RespostaDaChamadaTest(ControleDeChamadaFixture controleDeChamadaFixture, ProducerProviderFixture producerProviderFixture, ObjetoManobraFixture objetoDeManobraFixture)
    {
        _controleDeChamadaFixture = controleDeChamadaFixture;
        _producerProviderFixture = producerProviderFixture;
        _objetoDeManobraFixture = objetoDeManobraFixture;
        _dependencyInjectorFactory = new MockDependencyInjectorFactory();
        _dependencyInjectorFactory.RegisterMocks();

        _dependencyInjectorFactory.Mocker.Use(Options.Create(_producerProviderFixture.ObterKafkaSettingsValido()));
    }
}