using ONS.SINapse.Business.Imp.Business.TemplatesDeSolicitacao;
using ONS.SINapse.UnitTest.Business.Fixtures.ExportarTemplates;

namespace ONS.SINapse.UnitTest.Business.ExportarTemplates;

[Collection(nameof(ExportarTemplatesDeSolicitacaoCollection))]
public class ExportacaoTemplatesDeSolicitacaoTests
{
    private readonly ExportarTemplatesDeSolicitacaoFixture _exportarTemplatesDeSolicitacaoFixture;

    public ExportacaoTemplatesDeSolicitacaoTests(ExportarTemplatesDeSolicitacaoFixture exportarTemplatesDeSolicitacaoFixture)
    {
        _exportarTemplatesDeSolicitacaoFixture = exportarTemplatesDeSolicitacaoFixture;
        _exportarTemplatesDeSolicitacaoFixture.ResetAutoMocker();
    }

    [Fact(DisplayName = "Deve exportar arquivos dos templates")]
    [Trait("Business", "Exportar Templates")]
    public async Task ExportarAsync_DeveRetornarUmStreamZip_QuandoHouverTemplatesNoDiretorio()
    {
        // Arrange
        
        var settings = _exportarTemplatesDeSolicitacaoFixture.ObterTemplatesSettings("ExportarTemplates\\templates");
        
        // Mock o comportamento do sistema de arquivos

        var business = new ExportacaoTemplatesDeSolicitacaoBusiness(settings);
        
        // Act
        var result = await business.ExportarAsync(CancellationToken.None);

        // Assert
        Assert.NotNull(result);
        result.CanRead.ShouldBeTrue();
        result.Position.ShouldBe(0);
        result.Length.ShouldBeGreaterThan(0);
    }

    [Fact(DisplayName = "Deve lançar uma exception de que o diretorios dos templates não existe")]
    [Trait("Business", "Exportar Templates")]
    public async Task ExportarAsync_DeveLacarException_QuandoDiretorioTemplateNaoExiste()
    {
        // Arrange
        var settings = _exportarTemplatesDeSolicitacaoFixture.ObterTemplatesSettings("templates-invalidos");

        var business = new ExportacaoTemplatesDeSolicitacaoBusiness(settings);

        // Act & Assert
        var exception = await Assert.ThrowsAsync<DirectoryNotFoundException>(async () =>
            await business.ExportarAsync(CancellationToken.None));
        
        exception.Message.ShouldContain(ExportacaoTemplatesDeSolicitacaoBusiness.MensagemArquivoNaoExiste);
    }
}