using Microsoft.Extensions.Options;
using ONS.SINapse.Business.IBusiness;
using ONS.SINapse.Entities.Entities;
using ONS.SINapse.Repository.IRepository;
using ONS.SINapse.Shared.Constants;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Enums;
using ONS.SINapse.Shared.Identity;
using ONS.SINapse.Shared.Notifications;
using ONS.SINapse.Shared.Settings;
using ONS.SINapse.UnitTest.Shared;
using ONS.SINapse.UnitTest.Shared.Fixtures;
using ONS.SINapse.UnitTest.Shared.Identity;
using System.Linq.Expressions;

namespace ONS.SINapse.UnitTest.Business.ComunicadoBusiness;

public class ComunicadoBusinessDestinatariosTest : IClassFixture<ComunicadoFixture>
{
    protected readonly MockDependencyInjectorFactory DependencyInjectorFactory;

    public ComunicadoBusinessDestinatariosTest()
    {
        DependencyInjectorFactory = new MockDependencyInjectorFactory();
        DependencyInjectorFactory.RegisterMocks();
        ConfigurarMocksBasicos();
    }

    [Fact(DisplayName = "[ComunicadoBusiness] - Deve adicionar comunicado para centros regionais com sucesso")]
    [Trait("Business", "ComunicadoBusiness")]
    public async Task AddAsync_CentrosRegionais_DeveAdicionarComSucesso()
    {
        // Arrange
        var comunicadoDto = ComunicadoFixture.GerarComunicadoDtoValido();
        comunicadoDto.TipoDeComunicado = TipoDeComunicado.CentrosRegionais;
        comunicadoDto.Destinatarios = new List<string> { "SE", "NE" };

        ConfigurarUsuarioComPermissoes(Operacoes.CriarBroadcast);

        var cancellationToken = CancellationToken.None;

        // Act
        var result = await DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.ComunicadoBusiness>()
            .AddAsync(comunicadoDto, cancellationToken);

        // Assert
        result.ShouldNotBeNull();
        result.Id.ShouldNotBeNullOrEmpty();
        
        DependencyInjectorFactory.Mocker.GetMock<IComunicadoRepository>()
            .Verify(repo => repo.AddAsync(It.IsAny<Comunicado>(), cancellationToken), Times.Once);
    }

    [Fact(DisplayName = "[ComunicadoBusiness] - Deve falhar ao adicionar comunicado para centros regionais inexistentes")]
    [Trait("Business", "ComunicadoBusiness")]
    public async Task AddAsync_CentrosRegionaisInexistentes_DeveFalhar()
    {
        // Arrange
        var comunicadoDto = ComunicadoFixture.GerarComunicadoDtoValido();
        comunicadoDto.TipoDeComunicado = TipoDeComunicado.CentrosRegionais;
        comunicadoDto.Destinatarios = new List<string> { "CENTRO_INEXISTENTE", "OUTRO_INEXISTENTE" };

        ConfigurarUsuarioComPermissoes(Operacoes.CriarBroadcast);

        var cancellationToken = CancellationToken.None;

        // Act
        var result = await DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.ComunicadoBusiness>()
            .AddAsync(comunicadoDto, cancellationToken);

        // Assert
        result.ShouldNotBeNull();
        result.Id.ShouldBeNullOrEmpty();
        
        var notificationContext = DependencyInjectorFactory.Mocker.Get<NotificationContext>();
        notificationContext.HasNotifications.ShouldBeTrue();
        notificationContext.Notifications.ShouldContain(n => n.Message.Contains("não localizados"));
        
        DependencyInjectorFactory.Mocker.GetMock<IComunicadoRepository>()
            .Verify(repo => repo.AddAsync(It.IsAny<Comunicado>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact(DisplayName = "[ComunicadoBusiness] - Deve falhar ao adicionar comunicado para áreas elétricas sem agentes")]
    [Trait("Business", "ComunicadoBusiness")]
    public async Task AddAsync_AreasEletricasSemAgentes_DeveFalhar()
    {
        // Arrange
        var comunicadoDto = ComunicadoFixture.GerarComunicadoDtoValido();
        comunicadoDto.TipoDeComunicado = TipoDeComunicado.AreasEletricas;
        comunicadoDto.Destinatarios = new List<string> { "AGENTE1", "AGENTE2" };

        ConfigurarUsuarioComPermissoes(Operacoes.CriarMulticast);
        ConfigurarSinapseDadosVazio(); // Simula não encontrar agentes

        var cancellationToken = CancellationToken.None;

        // Act
        var result = await DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.ComunicadoBusiness>()
            .AddAsync(comunicadoDto, cancellationToken);

        // Assert
        result.ShouldNotBeNull();
        result.Id.ShouldBeNullOrEmpty();
        
        var notificationContext = DependencyInjectorFactory.Mocker.Get<NotificationContext>();
        notificationContext.HasNotifications.ShouldBeTrue();
        notificationContext.Notifications.ShouldContain(n => n.Message.Contains("Nenhum registro localizado"));
        
        DependencyInjectorFactory.Mocker.GetMock<IComunicadoRepository>()
            .Verify(repo => repo.AddAsync(It.IsAny<Comunicado>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact(DisplayName = "[ComunicadoBusiness] - Deve falhar ao adicionar comunicado para agentes não encontrados")]
    [Trait("Business", "ComunicadoBusiness")]
    public async Task AddAsync_AgentesNaoEncontrados_DeveFalhar()
    {
        // Arrange
        var comunicadoDto = ComunicadoFixture.GerarComunicadoDtoValido();
        comunicadoDto.TipoDeComunicado = TipoDeComunicado.AreasEletricas;
        comunicadoDto.Destinatarios = new List<string> { "AGENTE_INEXISTENTE" };

        ConfigurarUsuarioComPermissoes(Operacoes.CriarMulticast);
        ConfigurarSinapseDadosComAgentesValidos(); // Agentes válidos mas diferentes dos solicitados

        var cancellationToken = CancellationToken.None;

        // Act
        var result = await DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.ComunicadoBusiness>()
            .AddAsync(comunicadoDto, cancellationToken);

        // Assert
        result.ShouldNotBeNull();
        result.Id.ShouldBeNullOrEmpty();
        
        var notificationContext = DependencyInjectorFactory.Mocker.Get<NotificationContext>();
        notificationContext.HasNotifications.ShouldBeTrue();
        notificationContext.Notifications.ShouldContain(n => n.Message.Contains("Nenhum agente localizado"));
        
        DependencyInjectorFactory.Mocker.GetMock<IComunicadoRepository>()
            .Verify(repo => repo.AddAsync(It.IsAny<Comunicado>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact(DisplayName = "[ComunicadoBusiness] - Deve falhar ao adicionar comunicado para alguns agentes não encontrados")]
    [Trait("Business", "ComunicadoBusiness")]
    public async Task AddAsync_AlgunsAgentesNaoEncontrados_DeveFalhar()
    {
        // Arrange
        var comunicadoDto = ComunicadoFixture.GerarComunicadoDtoValido();
        comunicadoDto.TipoDeComunicado = TipoDeComunicado.AreasEletricas;
        comunicadoDto.Destinatarios = new List<string> { "AGENTE1", "AGENTE_INEXISTENTE" };

        ConfigurarUsuarioComPermissoes(Operacoes.CriarMulticast);
        ConfigurarSinapseDadosComAgentesValidos(); // Só tem AGENTE1

        var cancellationToken = CancellationToken.None;

        // Act
        var result = await DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.ComunicadoBusiness>()
            .AddAsync(comunicadoDto, cancellationToken);

        // Assert
        result.ShouldNotBeNull();
        result.Id.ShouldBeNullOrEmpty();
        
        var notificationContext = DependencyInjectorFactory.Mocker.Get<NotificationContext>();
        notificationContext.HasNotifications.ShouldBeTrue();
        notificationContext.Notifications.ShouldContain(n => n.Message.Contains("Agentes não localizados"));
        
        DependencyInjectorFactory.Mocker.GetMock<IComunicadoRepository>()
            .Verify(repo => repo.AddAsync(It.IsAny<Comunicado>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact(DisplayName = "[ComunicadoBusiness] - Deve adicionar comunicado para áreas elétricas com agentes válidos")]
    [Trait("Business", "ComunicadoBusiness")]
    public async Task AddAsync_AreasEletricasComAgentesValidos_DeveAdicionarComSucesso()
    {
        // Arrange
        var comunicadoDto = ComunicadoFixture.GerarComunicadoDtoValido();
        comunicadoDto.TipoDeComunicado = TipoDeComunicado.AreasEletricas;
        comunicadoDto.Destinatarios = new List<string> { "AGENTE1" };

        ConfigurarUsuarioComPermissoes(Operacoes.CriarMulticast);
        ConfigurarSinapseDadosComAgentesValidos();

        var cancellationToken = CancellationToken.None;

        // Act
        var result = await DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.ComunicadoBusiness>()
            .AddAsync(comunicadoDto, cancellationToken);

        // Assert
        result.ShouldNotBeNull();
        result.Id.ShouldNotBeNullOrEmpty();
        
        DependencyInjectorFactory.Mocker.GetMock<IComunicadoRepository>()
            .Verify(repo => repo.AddAsync(It.IsAny<Comunicado>(), cancellationToken), Times.Once);
    }

    [Fact(DisplayName = "[ComunicadoBusiness] - Deve filtrar destinatários vazios ou nulos")]
    [Trait("Business", "ComunicadoBusiness")]
    public async Task AddAsync_DestinatariosComValoresVazios_DeveFiltrarEProcessar()
    {
        // Arrange
        var comunicadoDto = ComunicadoFixture.GerarComunicadoDtoValido();
        comunicadoDto.TipoDeComunicado = TipoDeComunicado.AreasEletricas;
        comunicadoDto.Destinatarios = new List<string> { "AGENTE1", "", "AGENTE1" }; // Duplicados e vazios

        ConfigurarUsuarioComPermissoes(Operacoes.CriarMulticast);
        ConfigurarSinapseDadosComAgentesValidos();

        var cancellationToken = CancellationToken.None;

        // Act
        var result = await DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.ComunicadoBusiness>()
            .AddAsync(comunicadoDto, cancellationToken);

        // Assert
        result.ShouldNotBeNull();
        result.Id.ShouldNotBeNullOrEmpty();
        
        DependencyInjectorFactory.Mocker.GetMock<IComunicadoRepository>()
            .Verify(repo => repo.AddAsync(It.IsAny<Comunicado>(), cancellationToken), Times.Once);
    }

    [Fact(DisplayName = "[ComunicadoBusiness] - Deve retornar zero para total de comunicados não lidos quando não há comunicados")]
    [Trait("Business", "ComunicadoBusiness")]
    public async Task ObterTotalDeComunicadosNaoLidosPeloUsuario_SemComunicados_DeveRetornarZero()
    {
        // Arrange
        ConfigurarUsuarioComCentros(new List<string> { "SE", "NE" });

        DependencyInjectorFactory.Mocker.GetMock<IComunicadoRepository>()
            .Setup(repo => repo.CountAsync(It.IsAny<Expression<Func<Comunicado, bool>>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(0L);

        var cancellationToken = CancellationToken.None;

        // Act
        var result = await DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.ComunicadoBusiness>()
            .ObterTotalDeComunicadosNaoLidosPeloUsuario(cancellationToken);

        // Assert
        result.ShouldBe(0L);
        
        DependencyInjectorFactory.Mocker.GetMock<IComunicadoRepository>()
            .Verify(repo => repo.CountAsync(It.IsAny<Expression<Func<Comunicado, bool>>>(), cancellationToken), Times.Once);
    }

    private void ConfigurarMocksBasicos()
    {
        // Configurar ComunicadoSettings
        var comunicadoSettings = new ComunicadoSettings
        {
            TempoDeVidaDeComunicadoEmDias = 30
        };
        
        var optionsMock = Options.Create(comunicadoSettings);
        DependencyInjectorFactory.Mocker.Use<IOptions<ComunicadoSettings>>(optionsMock);

        // Configurar mocks básicos dos repositórios e serviços
        DependencyInjectorFactory.Mocker.GetMock<IComunicadoRepository>();
        DependencyInjectorFactory.Mocker.GetMock<INotificacaoDeComunicadoBusiness>();
    }

    private void ConfigurarUsuarioComPermissoes(string operacao)
    {
        var scopes = new List<Scope> { new("CENTROS", "SE", "CORS-SE") };
        var operacoes = new List<string> { operacao };
        var perfil = new Perfil(scopes, operacoes, "Operador", "operador");
        
        DependencyInjectorFactory.Mocker.Get<IUserContextTest>().DefinirPerfil(perfil);
    }

    private void ConfigurarUsuarioComCentros(List<string> centros)
    {
        var scopes = centros.Select(c => new Scope("CENTROS", c, "CORS-SE")).ToList();
        var operacoes = new List<string> { Operacoes.CriarMulticast };
        var perfil = new Perfil(scopes, operacoes, "Operador", "operador");
        
        DependencyInjectorFactory.Mocker.Get<IUserContextTest>().DefinirPerfil(perfil);
    }

    private void ConfigurarSinapseDadosVazio()
    {
        var sinapseDadosService = DependencyInjectorFactory.Mocker.GetMock<CacheSync.Services.ISinapseDadosDatasetQueryService>();
        sinapseDadosService.Setup(service => service.GetDatasetAsync<ICollection<AreaEletricaComunicadoDto>>(
                It.IsAny<Expression<Func<CacheSync.Services.ISinapseDadosDatasetService, Task<ICollection<AreaEletricaComunicadoDto>>>>>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<AreaEletricaComunicadoDto>());
    }

    private void ConfigurarSinapseDadosComAgentesValidos()
    {
        var areasEletricas = new List<AreaEletricaComunicadoDto>
        {
            new("AREA1", "Área Elétrica 1", "SE", new List<AgenteDto>
            {
                new("AGENTE1", "Agente 1")
            })
        };

        var sinapseDadosService = DependencyInjectorFactory.Mocker.GetMock<CacheSync.Services.ISinapseDadosDatasetQueryService>();
        sinapseDadosService.Setup(service => service.GetDatasetAsync<ICollection<AreaEletricaComunicadoDto>>(
                It.IsAny<Expression<Func<CacheSync.Services.ISinapseDadosDatasetService, Task<ICollection<AreaEletricaComunicadoDto>>>>>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(areasEletricas);
    }
}
