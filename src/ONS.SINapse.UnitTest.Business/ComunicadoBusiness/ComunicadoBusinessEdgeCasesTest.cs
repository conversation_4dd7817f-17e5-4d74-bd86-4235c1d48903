using Microsoft.Extensions.Options;
using ONS.SINapse.Business.IBusiness;
using ONS.SINapse.Entities.Entities;
using ONS.SINapse.Repository.IRepository;
using ONS.SINapse.Shared.Constants;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Enums;
using ONS.SINapse.Shared.Identity;
using ONS.SINapse.Shared.Notifications;
using ONS.SINapse.Shared.Settings;
using ONS.SINapse.UnitTest.Shared;
using ONS.SINapse.UnitTest.Shared.Fixtures;
using ONS.SINapse.UnitTest.Shared.Identity;

namespace ONS.SINapse.UnitTest.Business.ComunicadoBusiness;

public class ComunicadoBusinessEdgeCasesTest : IClassFixture<ComunicadoFixture>
{
    private readonly ComunicadoFixture _comunicadoFixture;
    protected readonly MockDependencyInjectorFactory DependencyInjectorFactory;

    public ComunicadoBusinessEdgeCasesTest(ComunicadoFixture comunicadoFixture)
    {
        _comunicadoFixture = comunicadoFixture;
        DependencyInjectorFactory = new MockDependencyInjectorFactory();
        DependencyInjectorFactory.RegisterMocks();
        ConfigurarMocksBasicos();
    }

    [Fact(DisplayName = "[ComunicadoBusiness] - Deve falhar ao registrar leitura para comunicado inexistente")]
    [Trait("Business", "ComunicadoBusiness")]
    public async Task RegistrarLeituraAsync_ComunicadoInexistente_DeveFalhar()
    {
        // Arrange
        var id = "comunicado-inexistente";

        DependencyInjectorFactory.Mocker.GetMock<IComunicadoRepository>()
            .Setup(repo => repo.GetOneAsync(id, It.IsAny<CancellationToken>()))
            .ReturnsAsync((Comunicado)null);

        var cancellationToken = CancellationToken.None;

        // Act
        var result = await DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.ComunicadoBusiness>()
            .RegistrarLeituraAsync(id, cancellationToken);

        // Assert
        result.ShouldNotBeNull();
        result.Id.ShouldBeNullOrEmpty();
        
        var notificationContext = DependencyInjectorFactory.Mocker.Get<NotificationContext>();
        notificationContext.HasNotifications.ShouldBeTrue();
        notificationContext.Notifications.ShouldContain(n => n.Message.Contains("não encontrado"));
        
        DependencyInjectorFactory.Mocker.GetMock<IComunicadoRepository>()
            .Verify(repo => repo.UpdateAsync(It.IsAny<string>(), It.IsAny<Comunicado>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact(DisplayName = "[ComunicadoBusiness] - Deve tratar exceção ao registrar leitura")]
    [Trait("Business", "ComunicadoBusiness")]
    public async Task RegistrarLeituraAsync_ExcecaoNoRepositorio_DeveTratarExcecao()
    {
        // Arrange
        var id = ComunicadoFixture.IdComunicadoPadrao;

        DependencyInjectorFactory.Mocker.GetMock<IComunicadoRepository>()
            .Setup(repo => repo.GetOneAsync(id, It.IsAny<CancellationToken>()))
            .ThrowsAsync(new Exception("Erro no banco de dados"));

        var cancellationToken = CancellationToken.None;

        // Act
        var result = await DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.ComunicadoBusiness>()
            .RegistrarLeituraAsync(id, cancellationToken);

        // Assert
        result.ShouldNotBeNull();
        result.Id.ShouldBeNullOrEmpty();
        
        var notificationContext = DependencyInjectorFactory.Mocker.Get<NotificationContext>();
        notificationContext.HasNotifications.ShouldBeTrue();
        notificationContext.Notifications.ShouldContain(n => n.Message.Contains("não existe"));
    }

    [Fact(DisplayName = "[ComunicadoBusiness] - Deve falhar ao obter comunicado por ID inexistente")]
    [Trait("Business", "ComunicadoBusiness")]
    public async Task GetByIdAsync_ComunicadoInexistente_DeveFalhar()
    {
        // Arrange
        var id = "comunicado-inexistente";

        DependencyInjectorFactory.Mocker.GetMock<IComunicadoRepository>()
            .Setup(repo => repo.GetOneAsync(id, It.IsAny<CancellationToken>()))
            .ReturnsAsync((Comunicado)null);

        var cancellationToken = CancellationToken.None;

        // Act
        var result = await DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.ComunicadoBusiness>()
            .GetByIdAsync(id, cancellationToken);

        // Assert
        result.ShouldNotBeNull();
        result.Id.ShouldBeNullOrEmpty();
        
        var notificationContext = DependencyInjectorFactory.Mocker.Get<NotificationContext>();
        notificationContext.HasNotifications.ShouldBeTrue();
        notificationContext.Notifications.ShouldContain(n => n.Message.Contains("não existe"));
    }

    [Theory(DisplayName = "[ComunicadoBusiness] - Deve validar tipos de comunicado corretamente")]
    [Trait("Business", "ComunicadoBusiness")]
    [InlineData(TipoDeComunicado.Todos, Operacoes.CriarBroadcast, true)]
    [InlineData(TipoDeComunicado.AreasEletricas, Operacoes.CriarMulticast, true)]
    [InlineData(TipoDeComunicado.Todos, Operacoes.CriarMulticast, false)]
    [InlineData(TipoDeComunicado.AreasEletricas, Operacoes.CriarBroadcast, false)]
    public async Task AddAsync_ValidarPermissoesPorTipoComunicado(TipoDeComunicado tipo, string operacao, bool devePassar)
    {
        // Arrange
        var comunicadoDto = ComunicadoFixture.GerarComunicadoDtoValido();
        comunicadoDto.TipoDeComunicado = tipo;

        if (tipo == TipoDeComunicado.Todos)
        {
            comunicadoDto.Destinatarios = new List<string>(); // Todos não deve ter destinatários
        }

        ConfigurarUsuarioComPermissoes(operacao);
        ConfigurarAreasEletricas();

        var cancellationToken = CancellationToken.None;

        // Act
        var result = await DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.ComunicadoBusiness>()
            .AddAsync(comunicadoDto, cancellationToken);

        // Assert
        result.ShouldNotBeNull();

        if (devePassar)
        {
            result.Id.ShouldNotBeNullOrEmpty();
        }
        else
        {
            result.Id.ShouldBeNullOrEmpty();
            var notificationContext = DependencyInjectorFactory.Mocker.Get<NotificationContext>();
            notificationContext.HasNotifications.ShouldBeTrue();
        }
    }

    [Fact(DisplayName = "[ComunicadoBusiness] - Deve adicionar comunicado para centros regionais com sucesso")]
    [Trait("Business", "ComunicadoBusiness")]
    public async Task AddAsync_CentrosRegionais_DeveAdicionarComSucesso()
    {
        // Arrange
        var comunicadoDto = ComunicadoFixture.GerarComunicadoDtoValido();
        comunicadoDto.TipoDeComunicado = TipoDeComunicado.CentrosRegionais;
        comunicadoDto.Destinatarios = new List<string> { "SE", "NE" };

        ConfigurarUsuarioComPermissoes(Operacoes.CriarBroadcast);

        var cancellationToken = CancellationToken.None;

        // Act
        var result = await DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.ComunicadoBusiness>()
            .AddAsync(comunicadoDto, cancellationToken);

        // Assert
        result.ShouldNotBeNull();
        result.Id.ShouldNotBeNullOrEmpty();

        DependencyInjectorFactory.Mocker.GetMock<IComunicadoRepository>()
            .Verify(repo => repo.AddAsync(It.IsAny<Comunicado>(), cancellationToken), Times.Once);
    }

    [Fact(DisplayName = "[ComunicadoBusiness] - Deve falhar ao adicionar comunicado com destinatários para tipo Todos")]
    [Trait("Business", "ComunicadoBusiness")]
    public async Task AddAsync_TipoTodosComDestinatarios_DeveFalhar()
    {
        // Arrange
        var comunicadoDto = ComunicadoFixture.GerarComunicadoDtoValido();
        comunicadoDto.TipoDeComunicado = TipoDeComunicado.Todos;
        comunicadoDto.Destinatarios = new List<string> { "AGENTE1", "AGENTE2" };

        ConfigurarUsuarioComPermissoes(Operacoes.CriarBroadcast);

        var cancellationToken = CancellationToken.None;

        // Act
        var result = await DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.ComunicadoBusiness>()
            .AddAsync(comunicadoDto, cancellationToken);

        // Assert
        result.ShouldNotBeNull();
        result.Id.ShouldBeNullOrEmpty();
        
        var notificationContext = DependencyInjectorFactory.Mocker.Get<NotificationContext>();
        notificationContext.HasNotifications.ShouldBeTrue();
        notificationContext.Notifications.ShouldContain(n => n.Message.Contains("não permite envio de lista"));
    }

    [Fact(DisplayName = "[ComunicadoBusiness] - Deve falhar ao adicionar comunicado sem destinatários para tipos que exigem")]
    [Trait("Business", "ComunicadoBusiness")]
    public async Task AddAsync_SemDestinatarios_DeveFalhar()
    {
        // Arrange
        var comunicadoDto = ComunicadoFixture.GerarComunicadoDtoValido();
        comunicadoDto.TipoDeComunicado = TipoDeComunicado.AreasEletricas;
        comunicadoDto.Destinatarios = new List<string>();

        ConfigurarUsuarioComPermissoes(Operacoes.CriarMulticast);

        var cancellationToken = CancellationToken.None;

        // Act
        var result = await DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.ComunicadoBusiness>()
            .AddAsync(comunicadoDto, cancellationToken);

        // Assert
        result.ShouldNotBeNull();
        result.Id.ShouldBeNullOrEmpty();
        
        var notificationContext = DependencyInjectorFactory.Mocker.Get<NotificationContext>();
        notificationContext.HasNotifications.ShouldBeTrue();
        notificationContext.Notifications.ShouldContain(n => n.Message.Contains("um ou mais destinatários"));
    }

    [Fact(DisplayName = "[ComunicadoBusiness] - Deve tratar exceção ao adicionar complemento")]
    [Trait("Business", "ComunicadoBusiness")]
    public async Task AdicionarComplentoAsync_ExcecaoNoRepositorio_DeveTratarExcecao()
    {
        // Arrange
        var comunicado = _comunicadoFixture.GerarComunicadoValido();
        var complemento = ComunicadoFixture.GerarComplementoDeComunicadoDtoValido();
        var id = ComunicadoFixture.IdComunicadoPadrao;

        ConfigurarUsuarioComPermissaoParaComplemento("SE");

        DependencyInjectorFactory.Mocker.GetMock<IComunicadoRepository>()
            .Setup(repo => repo.GetOneAsync(id, It.IsAny<CancellationToken>()))
            .ReturnsAsync(comunicado);

        DependencyInjectorFactory.Mocker.GetMock<IComunicadoRepository>()
            .Setup(repo => repo.UpdateAsync(id, It.IsAny<Comunicado>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new Exception("Erro no banco de dados"));

        var cancellationToken = CancellationToken.None;

        // Act
        var result = await DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.ComunicadoBusiness>()
            .AdicionarComplentoAsync(id, complemento, cancellationToken);

        // Assert
        result.ShouldNotBeNull();
        
        var notificationContext = DependencyInjectorFactory.Mocker.Get<NotificationContext>();
        notificationContext.HasNotifications.ShouldBeTrue();
        notificationContext.Notifications.ShouldContain(n => n.Message.Contains("Erro ao adicionar complemento"));
    }

    private void ConfigurarMocksBasicos()
    {
        // Configurar ComunicadoSettings
        var comunicadoSettings = new ComunicadoSettings
        {
            TempoDeVidaDeComunicadoEmDias = 30
        };
        
        var optionsMock = Options.Create(comunicadoSettings);
        DependencyInjectorFactory.Mocker.Use<IOptions<ComunicadoSettings>>(optionsMock);

        // Configurar mocks básicos dos repositórios e serviços
        DependencyInjectorFactory.Mocker.GetMock<IComunicadoRepository>();
        DependencyInjectorFactory.Mocker.GetMock<INotificacaoDeComunicadoBusiness>();
        
        // Configurar ISinapseDadosDatasetQueryService
        var sinapseDadosService = DependencyInjectorFactory.Mocker.GetMock<ONS.SINapse.CacheSync.Services.ISinapseDadosDatasetQueryService>();
        sinapseDadosService.Setup(service => service.GetDatasetAsync<ICollection<AreaEletricaComunicadoDto>>(
                It.IsAny<System.Linq.Expressions.Expression<Func<ONS.SINapse.CacheSync.Services.ISinapseDadosDatasetService, Task<ICollection<AreaEletricaComunicadoDto>>>>>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(ComunicadoFixture.GerarAreasEletricasValidas());
    }

    private void ConfigurarUsuarioComPermissoes(string operacao)
    {
        var scopes = new List<Scope> { new("CENTROS", "SE", "CORS-SE") };
        var operacoes = new List<string> { operacao };
        var perfil = new Perfil(scopes, operacoes, "Operador", "operador");
        
        DependencyInjectorFactory.Mocker.Get<IUserContextTest>().DefinirPerfil(perfil);
    }

    private void ConfigurarUsuarioComPermissaoParaComplemento(string codigoCentro)
    {
        var scopes = new List<Scope> { new("CENTROS", codigoCentro, "CORS-SE") };
        var operacoes = new List<string> { Operacoes.CriarMulticast };
        var perfil = new Perfil(scopes, operacoes, "Operador", "operador");
        
        DependencyInjectorFactory.Mocker.Get<IUserContextTest>().DefinirPerfil(perfil);
    }

    private void ConfigurarAreasEletricas()
    {
        // Mock já configurado no ConfigurarMocksBasicos
    }
}
