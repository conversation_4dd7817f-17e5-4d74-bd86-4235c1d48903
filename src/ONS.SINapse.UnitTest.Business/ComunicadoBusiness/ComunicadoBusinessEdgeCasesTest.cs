using ONS.SINapse.Business.IBusiness;
using ONS.SINapse.CacheSync.Services;
using ONS.SINapse.Entities.Entities;
using ONS.SINapse.Repository.IRepository;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Identity;
using ONS.SINapse.Shared.Notifications;
using ONS.SINapse.UnitTest.Shared.Fixtures;
using ONS.SINapse.UnitTest.Shared.Identity;
using Shouldly;
using Xunit;

namespace ONS.SINapse.UnitTest.Business.ComunicadoBusiness;

public partial class ComunicadoBusinessEdgeCasesTest
{
    [Fact(DisplayName = "[ComunicadoBusiness] - Deve lidar com título muito longo")]
    [Trait("Business", "ComunicadoBusiness")]
    public async Task AddAsync_TituloMuitoLongo_DeveValidarCorretamente()
    {
        // Arrange
        var comunicadoDto = ComunicadoFixture.GerarComunicadoDtoComTituloLongo();
        
        ConfigurarUsuarioComPermissoes("CRIAR_MULTICAST");
        ConfigurarAreasEletricas(ComunicadoFixture.GerarAreasEletricasValidas());

        var cancellationToken = CancellationToken.None;

        // Act
        var result = await DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.ComunicadoBusiness>()
            .AddAsync(comunicadoDto, cancellationToken);

        // Assert
        var notificationContext = DependencyInjectorFactory.Mocker.Get<NotificationContext>();
        
        // Deve ter notificação de validação para título muito longo
        if (notificationContext.HasNotifications)
        {
            result.Id.ShouldBeNullOrEmpty();
        }
        else
        {
            result.Id.ShouldNotBeNullOrEmpty();
        }
    }

    [Fact(DisplayName = "[ComunicadoBusiness] - Deve lidar com mensagem muito longa")]
    [Trait("Business", "ComunicadoBusiness")]
    public async Task AddAsync_MensagemMuitoLonga_DeveValidarCorretamente()
    {
        // Arrange
        var comunicadoDto = ComunicadoFixture.GerarComunicadoDtoComMensagemLonga();
        
        ConfigurarUsuarioComPermissoes("CRIAR_MULTICAST");
        ConfigurarAreasEletricas(ComunicadoFixture.GerarAreasEletricasValidas());

        var cancellationToken = CancellationToken.None;

        // Act
        var result = await DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.ComunicadoBusiness>()
            .AddAsync(comunicadoDto, cancellationToken);

        // Assert
        var notificationContext = DependencyInjectorFactory.Mocker.Get<NotificationContext>();
        
        // Deve ter notificação de validação para mensagem muito longa
        if (notificationContext.HasNotifications)
        {
            result.Id.ShouldBeNullOrEmpty();
        }
        else
        {
            result.Id.ShouldNotBeNullOrEmpty();
        }
    }

    [Fact(DisplayName = "[ComunicadoBusiness] - Deve lidar com exceção no repositório durante adição")]
    [Trait("Business", "ComunicadoBusiness")]
    public async Task AddAsync_ExcecaoNoRepositorio_DeveLancarExcecao()
    {
        // Arrange
        var comunicadoDto = ComunicadoFixture.GerarComunicadoDtoValido();
        
        ConfigurarUsuarioComPermissoes("CRIAR_MULTICAST");
        ConfigurarAreasEletricas(ComunicadoFixture.GerarAreasEletricasValidas());

        DependencyInjectorFactory.Mocker.GetMock<IComunicadoRepository>()
            .Setup(repo => repo.AddAsync(It.IsAny<Comunicado>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new Exception("Erro no banco de dados"));

        var cancellationToken = CancellationToken.None;

        // Act & Assert
        await Should.ThrowAsync<Exception>(async () =>
            await DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.ComunicadoBusiness>()
                .AddAsync(comunicadoDto, cancellationToken));
    }

    [Fact(DisplayName = "[ComunicadoBusiness] - Deve lidar com exceção durante registro de leitura")]
    [Trait("Business", "ComunicadoBusiness")]
    public async Task RegistrarLeituraAsync_ExcecaoNoRepositorio_DeveRetornarDtoVazio()
    {
        // Arrange
        var id = ComunicadoFixture.IdComunicadoPadrao;

        DependencyInjectorFactory.Mocker.GetMock<IComunicadoRepository>()
            .Setup(repo => repo.GetOneAsync(id, It.IsAny<CancellationToken>()))
            .ThrowsAsync(new Exception("Erro no banco de dados"));

        var cancellationToken = CancellationToken.None;

        // Act
        var result = await DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.ComunicadoBusiness>()
            .RegistrarLeituraAsync(id, cancellationToken);

        // Assert
        result.ShouldNotBeNull();
        result.Id.ShouldBeNullOrEmpty();
        
        var notificationContext = DependencyInjectorFactory.Mocker.Get<NotificationContext>();
        notificationContext.HasNotifications.ShouldBeTrue();
    }

    [Fact(DisplayName = "[ComunicadoBusiness] - Deve lidar com exceção durante adição de complemento")]
    [Trait("Business", "ComunicadoBusiness")]
    public async Task AdicionarComplentoAsync_ExcecaoNoRepositorio_DeveAdicionarNotificacao()
    {
        // Arrange
        var comunicado = ComunicadoFixture.GerarComunicadoValido();
        var complemento = ComunicadoFixture.GerarComplementoDeComunicadoDtoValido();
        var id = ComunicadoFixture.IdComunicadoPadrao;

        ConfigurarUsuarioComPermissaoParaComplemento("SE");

        DependencyInjectorFactory.Mocker.GetMock<IComunicadoRepository>()
            .Setup(repo => repo.GetOneAsync(id, It.IsAny<CancellationToken>()))
            .ReturnsAsync(comunicado);

        DependencyInjectorFactory.Mocker.GetMock<IComunicadoRepository>()
            .Setup(repo => repo.UpdateAsync(id, It.IsAny<Comunicado>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new Exception("Erro no banco de dados"));

        var cancellationToken = CancellationToken.None;

        // Act
        var result = await DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.ComunicadoBusiness>()
            .AdicionarComplentoAsync(id, complemento, cancellationToken);

        // Assert
        result.ShouldNotBeNull();
        
        var notificationContext = DependencyInjectorFactory.Mocker.Get<NotificationContext>();
        notificationContext.HasNotifications.ShouldBeTrue();
    }

    [Fact(DisplayName = "[ComunicadoBusiness] - Deve lidar com destinatários duplicados")]
    [Trait("Business", "ComunicadoBusiness")]
    public async Task AddAsync_DestinatariosDuplicados_DeveRemoverDuplicatas()
    {
        // Arrange
        var comunicadoDto = ComunicadoFixture.GerarComunicadoDtoValido();
        comunicadoDto.Destinatarios = new List<string> { "AGENTE001", "AGENTE001", "AGENTE002", "AGENTE002" };
        
        ConfigurarUsuarioComPermissoes("CRIAR_MULTICAST");
        ConfigurarAreasEletricas(ComunicadoFixture.GerarAreasEletricasValidas());

        var cancellationToken = CancellationToken.None;

        // Act
        var result = await DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.ComunicadoBusiness>()
            .AddAsync(comunicadoDto, cancellationToken);

        // Assert
        result.ShouldNotBeNull();
        result.Id.ShouldNotBeNullOrEmpty();
        
        DependencyInjectorFactory.Mocker.GetMock<IComunicadoRepository>()
            .Verify(repo => repo.AddAsync(It.IsAny<Comunicado>(), cancellationToken), Times.Once);
    }

    [Fact(DisplayName = "[ComunicadoBusiness] - Deve lidar com destinatários vazios ou nulos")]
    [Trait("Business", "ComunicadoBusiness")]
    public async Task AddAsync_DestinatariosVaziosOuNulos_DeveRemoverVazios()
    {
        // Arrange
        var comunicadoDto = ComunicadoFixture.GerarComunicadoDtoValido();
        comunicadoDto.Destinatarios = new List<string> { "AGENTE001", "", null, "AGENTE002", "   " };
        
        ConfigurarUsuarioComPermissoes("CRIAR_MULTICAST");
        ConfigurarAreasEletricas(ComunicadoFixture.GerarAreasEletricasValidas());

        var cancellationToken = CancellationToken.None;

        // Act
        var result = await DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.ComunicadoBusiness>()
            .AddAsync(comunicadoDto, cancellationToken);

        // Assert
        result.ShouldNotBeNull();
        result.Id.ShouldNotBeNullOrEmpty();
        
        DependencyInjectorFactory.Mocker.GetMock<IComunicadoRepository>()
            .Verify(repo => repo.AddAsync(It.IsAny<Comunicado>(), cancellationToken), Times.Once);
    }

    [Fact(DisplayName = "[ComunicadoBusiness] - Deve lidar com CancellationToken cancelado")]
    [Trait("Business", "ComunicadoBusiness")]
    public async Task GetAsync_CancellationTokenCancelado_DeveLancarOperationCanceledException()
    {
        // Arrange
        var cancellationTokenSource = new CancellationTokenSource();
        cancellationTokenSource.Cancel();
        var cancellationToken = cancellationTokenSource.Token;

        DependencyInjectorFactory.Mocker.GetMock<IComunicadoRepository>()
            .Setup(repo => repo.GetAsync(It.IsAny<System.Linq.Expressions.Expression<Func<Comunicado, bool>>>(), cancellationToken))
            .ThrowsAsync(new OperationCanceledException());

        // Act & Assert
        await Should.ThrowAsync<OperationCanceledException>(async () =>
            await DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.ComunicadoBusiness>()
                .GetAsync(cancellationToken));
    }

    [Fact(DisplayName = "[ComunicadoBusiness] - Deve lidar com serviço de dados indisponível")]
    [Trait("Business", "ComunicadoBusiness")]
    public async Task GetAreasEletricas_ServicoIndisponivel_DeveRetornarListaVazia()
    {
        // Arrange
        ConfigurarUsuarioComCentros(new List<string> { "SE" });
        
        DependencyInjectorFactory.Mocker.GetMock<ISinapseDadosDatasetQueryService>()
            .Setup(service => service.GetDatasetAsync(
                It.IsAny<Func<ISinapseDadosDatasetQueryService, Task<IEnumerable<AreaEletricaComunicadoDto>>>>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync((IEnumerable<AreaEletricaComunicadoDto>?)null);

        var cancellationToken = CancellationToken.None;

        // Act
        var result = await DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.ComunicadoBusiness>()
            .GetAreasEletricas(cancellationToken);

        // Assert
        result.ShouldNotBeNull();
        result.Count().ShouldBe(0);
    }

    [Fact(DisplayName = "[ComunicadoBusiness] - Deve lidar com configurações de tempo de vida zeradas")]
    [Trait("Business", "ComunicadoBusiness")]
    public async Task ExcluirComunicadosAntigos_TempoDeVidaZero_DeveExcluirTodos()
    {
        // Arrange
        var comunicados = new List<Comunicado>
        {
            ComunicadoFixture.GerarComunicadoValido()
        };

        DependencyInjectorFactory.Mocker.GetMock<IComunicadoRepository>()
            .Setup(repo => repo.GetAsync(It.IsAny<System.Linq.Expressions.Expression<Func<Comunicado, bool>>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(comunicados);

        var cancellationToken = CancellationToken.None;

        // Act
        var result = await DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.ComunicadoBusiness>()
            .ExcluirComunicadosAntigos(cancellationToken);

        // Assert
        result.ShouldNotBeNull();
        
        DependencyInjectorFactory.Mocker.GetMock<IComunicadoRepository>()
            .Verify(repo => repo.DeleteManyAsync(It.IsAny<System.Linq.Expressions.Expression<Func<Comunicado, bool>>>(), cancellationToken), Times.Once);
    }

    private static void ConfigurarUsuarioComPermissoes(string operacao)
    {
        var scopes = new List<Scope> { new("CENTROS", "SE", "CORS-SE") };
        var operacoes = new List<string> { operacao };
        var perfil = new Perfil(scopes, operacoes, "Operador", "operador");
        
        DependencyInjectorFactory.Mocker.Get<IUserContextTest>().DefinirPerfil(perfil);
    }

    private static void ConfigurarUsuarioComCentros(List<string> centros)
    {
        var scopes = centros.Select(c => new Scope("CENTROS", c, $"CORS-{c}")).ToList();
        var operacoes = new List<string> { "CONSULTAR_COMUNICADO" };
        var perfil = new Perfil(scopes, operacoes, "Operador", "operador");
        
        DependencyInjectorFactory.Mocker.Get<IUserContextTest>().DefinirPerfil(perfil);
    }

    private static void ConfigurarUsuarioComPermissaoParaComplemento(string codigoCentro)
    {
        var scopes = new List<Scope> { new("CENTROS", codigoCentro, $"CORS-{codigoCentro}") };
        var operacoes = new List<string> { "CRIAR_MULTICAST" };
        var perfil = new Perfil(scopes, operacoes, "Operador", "operador");
        
        DependencyInjectorFactory.Mocker.Get<IUserContextTest>().DefinirPerfil(perfil);
    }

    private static void ConfigurarAreasEletricas(List<AreaEletricaComunicadoDto> areasEletricas)
    {
        DependencyInjectorFactory.Mocker.GetMock<ISinapseDadosDatasetQueryService>()
            .Setup(service => service.GetDatasetAsync(
                It.IsAny<Func<ISinapseDadosDatasetQueryService, Task<IEnumerable<AreaEletricaComunicadoDto>>>>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(areasEletricas);
    }
}
