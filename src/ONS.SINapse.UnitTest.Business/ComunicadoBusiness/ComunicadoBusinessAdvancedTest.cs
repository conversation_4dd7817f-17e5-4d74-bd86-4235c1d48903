using ONS.SINapse.Business.IBusiness;
using ONS.SINapse.CacheSync.Services;
using ONS.SINapse.Entities.Entities;
using ONS.SINapse.Repository.IRepository;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Enums;
using ONS.SINapse.Shared.Identity;
using ONS.SINapse.Shared.Notifications;
using ONS.SINapse.UnitTest.Shared.Fixtures;
using ONS.SINapse.UnitTest.Shared.Identity;
using Shouldly;
using System.Linq.Expressions;
using Xunit;

namespace ONS.SINapse.UnitTest.Business.ComunicadoBusiness;

public partial class ComunicadoBusinessAdvancedTest
{
    [Fact(DisplayName = "[ComunicadoBusiness] - Deve obter total de comunicados não lidos")]
    [Trait("Business", "ComunicadoBusiness")]
    public async Task ObterTotalDeComunicadosNaoLidosPeloUsuario_DeveRetornarTotal()
    {
        // Arrange
        var totalEsperado = 5L;
        
        DependencyInjectorFactory.Mocker.GetMock<IComunicadoRepository>()
            .Setup(repo => repo.CountAsync(It.IsAny<Expression<Func<Comunicado, bool>>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(totalEsperado);

        var cancellationToken = CancellationToken.None;

        // Act
        var result = await DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.ComunicadoBusiness>()
            .ObterTotalDeComunicadosNaoLidosPeloUsuario(cancellationToken);

        // Assert
        result.ShouldBe(totalEsperado);
        
        DependencyInjectorFactory.Mocker.GetMock<IComunicadoRepository>()
            .Verify(repo => repo.CountAsync(It.IsAny<Expression<Func<Comunicado, bool>>>(), cancellationToken), Times.Once);
    }

    [Fact(DisplayName = "[ComunicadoBusiness] - Deve excluir comunicados antigos")]
    [Trait("Business", "ComunicadoBusiness")]
    public async Task ExcluirComunicadosAntigos_DeveExcluirComunicadosAntigos()
    {
        // Arrange
        var comunicadosAntigos = new List<Comunicado>
        {
            ComunicadoFixture.GerarComunicadoValido(),
            ComunicadoFixture.GerarComunicadoValido()
        };

        var idsEsperados = comunicadosAntigos.Select(c => c.Id).ToList();

        DependencyInjectorFactory.Mocker.GetMock<IComunicadoRepository>()
            .Setup(repo => repo.GetAsync(It.IsAny<Expression<Func<Comunicado, bool>>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(comunicadosAntigos);

        var cancellationToken = CancellationToken.None;

        // Act
        var result = await DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.ComunicadoBusiness>()
            .ExcluirComunicadosAntigos(cancellationToken);

        // Assert
        result.ShouldNotBeNull();
        result.Count.ShouldBe(2);
        
        DependencyInjectorFactory.Mocker.GetMock<IComunicadoRepository>()
            .Verify(repo => repo.DeleteManyAsync(It.IsAny<Expression<Func<Comunicado, bool>>>(), cancellationToken), Times.Once);
    }

    [Fact(DisplayName = "[ComunicadoBusiness] - Deve obter áreas elétricas")]
    [Trait("Business", "ComunicadoBusiness")]
    public async Task GetAreasEletricas_DeveRetornarAreasEletricas()
    {
        // Arrange
        var areasEletricas = ComunicadoFixture.GerarAreasEletricasValidas();
        
        ConfigurarUsuarioComCentros(new List<string> { "SE", "NE" });
        
        DependencyInjectorFactory.Mocker.GetMock<ISinapseDadosDatasetQueryService>()
            .Setup(service => service.GetDatasetAsync(
                It.IsAny<Func<ISinapseDadosDatasetQueryService, Task<IEnumerable<AreaEletricaComunicadoDto>>>>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(areasEletricas);

        var cancellationToken = CancellationToken.None;

        // Act
        var result = await DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.ComunicadoBusiness>()
            .GetAreasEletricas(cancellationToken);

        // Assert
        result.ShouldNotBeNull();
        result.Count().ShouldBe(2);
        
        DependencyInjectorFactory.Mocker.GetMock<ISinapseDadosDatasetQueryService>()
            .Verify(service => service.GetDatasetAsync(
                It.IsAny<Func<ISinapseDadosDatasetQueryService, Task<IEnumerable<AreaEletricaComunicadoDto>>>>(),
                cancellationToken), Times.Once);
    }

    [Fact(DisplayName = "[ComunicadoBusiness] - Deve falhar ao adicionar comunicado com destinatários inexistentes")]
    [Trait("Business", "ComunicadoBusiness")]
    public async Task AddAsync_DestinatariosInexistentes_DeveFalhar()
    {
        // Arrange
        var comunicadoDto = ComunicadoFixture.GerarComunicadoDtoValido();
        comunicadoDto.Destinatarios = ComunicadoFixture.GerarDestinatariosInvalidos();
        
        ConfigurarUsuarioComPermissoes("CRIAR_MULTICAST");
        ConfigurarAreasEletricasVazias();

        var cancellationToken = CancellationToken.None;

        // Act
        var result = await DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.ComunicadoBusiness>()
            .AddAsync(comunicadoDto, cancellationToken);

        // Assert
        result.ShouldNotBeNull();
        result.Id.ShouldBeNullOrEmpty();
        
        var notificationContext = DependencyInjectorFactory.Mocker.Get<NotificationContext>();
        notificationContext.HasNotifications.ShouldBeTrue();
        
        DependencyInjectorFactory.Mocker.GetMock<IComunicadoRepository>()
            .Verify(repo => repo.AddAsync(It.IsAny<Comunicado>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact(DisplayName = "[ComunicadoBusiness] - Deve falhar ao adicionar comunicado tipo Todos com destinatários")]
    [Trait("Business", "ComunicadoBusiness")]
    public async Task AddAsync_TipoTodosComDestinatarios_DeveFalhar()
    {
        // Arrange
        var comunicadoDto = ComunicadoFixture.GerarComunicadoDtoTodos();
        comunicadoDto.Destinatarios = new List<string> { "AGENTE001" }; // Não deve ter destinatários para tipo Todos
        
        ConfigurarUsuarioComPermissoes("CRIAR_BROADCAST");

        var cancellationToken = CancellationToken.None;

        // Act
        var result = await DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.ComunicadoBusiness>()
            .AddAsync(comunicadoDto, cancellationToken);

        // Assert
        result.ShouldNotBeNull();
        result.Id.ShouldBeNullOrEmpty();
        
        var notificationContext = DependencyInjectorFactory.Mocker.Get<NotificationContext>();
        notificationContext.HasNotifications.ShouldBeTrue();
        
        DependencyInjectorFactory.Mocker.GetMock<IComunicadoRepository>()
            .Verify(repo => repo.AddAsync(It.IsAny<Comunicado>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact(DisplayName = "[ComunicadoBusiness] - Deve falhar ao adicionar comunicado AreasEletricas sem destinatários")]
    [Trait("Business", "ComunicadoBusiness")]
    public async Task AddAsync_AreasEletricasSemDestinatarios_DeveFalhar()
    {
        // Arrange
        var comunicadoDto = ComunicadoFixture.GerarComunicadoDtoValido();
        comunicadoDto.Destinatarios = new List<string>(); // Deve ter destinatários para AreasEletricas
        
        ConfigurarUsuarioComPermissoes("CRIAR_MULTICAST");

        var cancellationToken = CancellationToken.None;

        // Act
        var result = await DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.ComunicadoBusiness>()
            .AddAsync(comunicadoDto, cancellationToken);

        // Assert
        result.ShouldNotBeNull();
        result.Id.ShouldBeNullOrEmpty();
        
        var notificationContext = DependencyInjectorFactory.Mocker.Get<NotificationContext>();
        notificationContext.HasNotifications.ShouldBeTrue();
        
        DependencyInjectorFactory.Mocker.GetMock<IComunicadoRepository>()
            .Verify(repo => repo.AddAsync(It.IsAny<Comunicado>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact(DisplayName = "[ComunicadoBusiness] - Deve falhar ao adicionar complemento sem permissão")]
    [Trait("Business", "ComunicadoBusiness")]
    public async Task AdicionarComplentoAsync_SemPermissao_DeveFalhar()
    {
        // Arrange
        var comunicado = ComunicadoFixture.GerarComunicadoValido();
        var complemento = ComunicadoFixture.GerarComplementoDeComunicadoDtoValido();
        var id = ComunicadoFixture.IdComunicadoPadrao;

        ConfigurarUsuarioSemPermissaoParaComplemento();

        DependencyInjectorFactory.Mocker.GetMock<IComunicadoRepository>()
            .Setup(repo => repo.GetOneAsync(id, It.IsAny<CancellationToken>()))
            .ReturnsAsync(comunicado);

        var cancellationToken = CancellationToken.None;

        // Act
        var result = await DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.ComunicadoBusiness>()
            .AdicionarComplentoAsync(id, complemento, cancellationToken);

        // Assert
        result.ShouldNotBeNull();
        
        var notificationContext = DependencyInjectorFactory.Mocker.Get<NotificationContext>();
        notificationContext.HasNotifications.ShouldBeTrue();
        
        DependencyInjectorFactory.Mocker.GetMock<IComunicadoRepository>()
            .Verify(repo => repo.UpdateAsync(It.IsAny<string>(), It.IsAny<Comunicado>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Theory(DisplayName = "[ComunicadoBusiness] - Deve processar diferentes tipos de comunicado")]
    [Trait("Business", "ComunicadoBusiness")]
    [InlineData(TipoDeComunicado.Todos)]
    [InlineData(TipoDeComunicado.CentrosRegionais)]
    [InlineData(TipoDeComunicado.AreasEletricas)]
    public async Task AddAsync_DiferentesTiposDeComunicado_DeveProcessarCorretamente(TipoDeComunicado tipoDeComunicado)
    {
        // Arrange
        var comunicadoDto = tipoDeComunicado switch
        {
            TipoDeComunicado.Todos => ComunicadoFixture.GerarComunicadoDtoTodos(),
            TipoDeComunicado.CentrosRegionais => ComunicadoFixture.GerarComunicadoDtoCentrosRegionais(),
            TipoDeComunicado.AreasEletricas => ComunicadoFixture.GerarComunicadoDtoValido(),
            _ => throw new ArgumentOutOfRangeException(nameof(tipoDeComunicado))
        };

        var operacao = tipoDeComunicado == TipoDeComunicado.AreasEletricas ? "CRIAR_MULTICAST" : "CRIAR_BROADCAST";
        ConfigurarUsuarioComPermissoes(operacao);

        if (tipoDeComunicado == TipoDeComunicado.AreasEletricas)
        {
            ConfigurarAreasEletricas(ComunicadoFixture.GerarAreasEletricasValidas());
        }

        var cancellationToken = CancellationToken.None;

        // Act
        var result = await DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.ComunicadoBusiness>()
            .AddAsync(comunicadoDto, cancellationToken);

        // Assert
        result.ShouldNotBeNull();
        result.Id.ShouldNotBeNullOrEmpty();
        
        DependencyInjectorFactory.Mocker.GetMock<IComunicadoRepository>()
            .Verify(repo => repo.AddAsync(It.IsAny<Comunicado>(), cancellationToken), Times.Once);
    }

    private static void ConfigurarUsuarioComPermissoes(string operacao)
    {
        var scopes = new List<Scope> { new("CENTROS", "SE", "CORS-SE") };
        var operacoes = new List<string> { operacao };
        var perfil = new Perfil(scopes, operacoes, "Operador", "operador");
        
        DependencyInjectorFactory.Mocker.Get<IUserContextTest>().DefinirPerfil(perfil);
    }

    private static void ConfigurarUsuarioComCentros(List<string> centros)
    {
        var scopes = centros.Select(c => new Scope("CENTROS", c, $"CORS-{c}")).ToList();
        var operacoes = new List<string> { "CONSULTAR_COMUNICADO" };
        var perfil = new Perfil(scopes, operacoes, "Operador", "operador");
        
        DependencyInjectorFactory.Mocker.Get<IUserContextTest>().DefinirPerfil(perfil);
    }

    private static void ConfigurarUsuarioSemPermissaoParaComplemento()
    {
        var scopes = new List<Scope> { new("CENTROS", "NE", "CORS-NE") }; // Centro diferente do comunicado
        var operacoes = new List<string> { "CRIAR_MULTICAST" };
        var perfil = new Perfil(scopes, operacoes, "Operador", "operador");
        
        DependencyInjectorFactory.Mocker.Get<IUserContextTest>().DefinirPerfil(perfil);
    }

    private static void ConfigurarAreasEletricas(List<AreaEletricaComunicadoDto> areasEletricas)
    {
        DependencyInjectorFactory.Mocker.GetMock<ISinapseDadosDatasetQueryService>()
            .Setup(service => service.GetDatasetAsync(
                It.IsAny<Func<ISinapseDadosDatasetQueryService, Task<IEnumerable<AreaEletricaComunicadoDto>>>>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(areasEletricas);
    }

    private static void ConfigurarAreasEletricasVazias()
    {
        DependencyInjectorFactory.Mocker.GetMock<ISinapseDadosDatasetQueryService>()
            .Setup(service => service.GetDatasetAsync(
                It.IsAny<Func<ISinapseDadosDatasetQueryService, Task<IEnumerable<AreaEletricaComunicadoDto>>>>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<AreaEletricaComunicadoDto>());
    }
}
