using Microsoft.Extensions.Options;
using ONS.SINapse.Business.IBusiness;
using ONS.SINapse.Entities.Entities;
using ONS.SINapse.Repository.IRepository;
using ONS.SINapse.Shared.Constants;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Identity;
using ONS.SINapse.Shared.Notifications;
using ONS.SINapse.Shared.Settings;
using ONS.SINapse.UnitTest.Shared;
using ONS.SINapse.UnitTest.Shared.Fixtures;
using ONS.SINapse.UnitTest.Shared.Identity;
using System.Linq.Expressions;

namespace ONS.SINapse.UnitTest.Business.ComunicadoBusiness;

public class ComunicadoBusinessAdvancedTest : IClassFixture<ComunicadoFixture>
{
    private readonly ComunicadoFixture _comunicadoFixture;
    protected readonly MockDependencyInjectorFactory DependencyInjectorFactory;

    public ComunicadoBusinessAdvancedTest(ComunicadoFixture comunicadoFixture)
    {
        _comunicadoFixture = comunicadoFixture;
        DependencyInjectorFactory = new MockDependencyInjectorFactory();
        DependencyInjectorFactory.RegisterMocks();
        ConfigurarMocksBasicos();
    }

    [Fact(DisplayName = "[ComunicadoBusiness] - Deve falhar ao adicionar complemento sem permissão")]
    [Trait("Business", "ComunicadoBusiness")]
    public async Task AdicionarComplentoAsync_SemPermissao_DeveFalhar()
    {
        // Arrange
        var comunicado = _comunicadoFixture.GerarComunicadoValido();
        var complemento = ComunicadoFixture.GerarComplementoDeComunicadoDtoValido();
        var id = ComunicadoFixture.IdComunicadoPadrao;

        ConfigurarUsuarioSemPermissaoParaComplemento();

        DependencyInjectorFactory.Mocker.GetMock<IComunicadoRepository>()
            .Setup(repo => repo.GetOneAsync(id, It.IsAny<CancellationToken>()))
            .ReturnsAsync(comunicado);

        var cancellationToken = CancellationToken.None;

        // Act
        var result = await DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.ComunicadoBusiness>()
            .AdicionarComplentoAsync(id, complemento, cancellationToken);

        // Assert
        result.ShouldNotBeNull();
        
        var notificationContext = DependencyInjectorFactory.Mocker.Get<NotificationContext>();
        notificationContext.HasNotifications.ShouldBeTrue();
        notificationContext.Notifications.ShouldContain(n => n.Message.Contains("sem permissão"));
        
        DependencyInjectorFactory.Mocker.GetMock<IComunicadoRepository>()
            .Verify(repo => repo.UpdateAsync(It.IsAny<string>(), It.IsAny<Comunicado>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact(DisplayName = "[ComunicadoBusiness] - Deve falhar ao adicionar complemento com mensagem vazia")]
    [Trait("Business", "ComunicadoBusiness")]
    public async Task AdicionarComplentoAsync_MensagemVazia_DeveFalhar()
    {
        // Arrange
        var comunicado = _comunicadoFixture.GerarComunicadoValido();
        var complemento = new ComplementoDeComunicadoDto("", new ObjetoDeManobraDto("SE", "CORS-SE"));
        var id = ComunicadoFixture.IdComunicadoPadrao;

        ConfigurarUsuarioComPermissaoParaComplemento("SE");

        DependencyInjectorFactory.Mocker.GetMock<IComunicadoRepository>()
            .Setup(repo => repo.GetOneAsync(id, It.IsAny<CancellationToken>()))
            .ReturnsAsync(comunicado);

        var cancellationToken = CancellationToken.None;

        // Act
        var result = await DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.ComunicadoBusiness>()
            .AdicionarComplentoAsync(id, complemento, cancellationToken);

        // Assert
        result.ShouldNotBeNull();
        
        var notificationContext = DependencyInjectorFactory.Mocker.Get<NotificationContext>();
        notificationContext.HasNotifications.ShouldBeTrue();
        notificationContext.Notifications.ShouldContain(n => n.Message.Contains("Mensagem obrigatória"));
        
        DependencyInjectorFactory.Mocker.GetMock<IComunicadoRepository>()
            .Verify(repo => repo.UpdateAsync(It.IsAny<string>(), It.IsAny<Comunicado>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact(DisplayName = "[ComunicadoBusiness] - Deve falhar ao adicionar complemento para comunicado inexistente")]
    [Trait("Business", "ComunicadoBusiness")]
    public async Task AdicionarComplentoAsync_ComunicadoInexistente_DeveFalhar()
    {
        // Arrange
        var complemento = ComunicadoFixture.GerarComplementoDeComunicadoDtoValido();
        var id = "comunicado-inexistente";

        DependencyInjectorFactory.Mocker.GetMock<IComunicadoRepository>()
            .Setup(repo => repo.GetOneAsync(id, It.IsAny<CancellationToken>()))
            .ReturnsAsync((Comunicado)null);

        var cancellationToken = CancellationToken.None;

        // Act
        var result = await DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.ComunicadoBusiness>()
            .AdicionarComplentoAsync(id, complemento, cancellationToken);

        // Assert
        result.ShouldNotBeNull();
        result.Id.ShouldBeNullOrEmpty();
        
        var notificationContext = DependencyInjectorFactory.Mocker.Get<NotificationContext>();
        notificationContext.HasNotifications.ShouldBeTrue();
        notificationContext.Notifications.ShouldContain(n => n.Message.Contains("não encontrado"));
    }

    [Fact(DisplayName = "[ComunicadoBusiness] - Deve excluir comunicados antigos com sucesso")]
    [Trait("Business", "ComunicadoBusiness")]
    public async Task ExcluirComunicadosAntigos_DeveExcluirComSucesso()
    {
        // Arrange
        var comunicadosAntigos = new List<Comunicado>
        {
            _comunicadoFixture.GerarComunicadoValido(),
            _comunicadoFixture.GerarComunicadoValido()
        };

        var idsEsperados = comunicadosAntigos.Select(c => c.Id).ToList();

        DependencyInjectorFactory.Mocker.GetMock<IComunicadoRepository>()
            .Setup(repo => repo.GetAsync(It.IsAny<Expression<Func<Comunicado, bool>>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(comunicadosAntigos);

        var cancellationToken = CancellationToken.None;

        // Act
        var result = await DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.ComunicadoBusiness>()
            .ExcluirComunicadosAntigos(cancellationToken);

        // Assert
        result.ShouldNotBeNull();
        result.Count.ShouldBe(2);
        result.ShouldContain(idsEsperados[0]);
        result.ShouldContain(idsEsperados[1]);
        
        DependencyInjectorFactory.Mocker.GetMock<IComunicadoRepository>()
            .Verify(repo => repo.DeleteManyAsync(It.IsAny<Expression<Func<Comunicado, bool>>>(), cancellationToken), Times.Once);
    }

    [Fact(DisplayName = "[ComunicadoBusiness] - Deve obter áreas elétricas com sucesso")]
    [Trait("Business", "ComunicadoBusiness")]
    public async Task GetAreasEletricas_DeveObterAreasComSucesso()
    {
        // Arrange
        var areasEletricas = ComunicadoFixture.GerarAreasEletricasValidas();
        ConfigurarUsuarioComCentros(new List<string> { "SE", "NE" });

        var cancellationToken = CancellationToken.None;

        // Act
        var result = await DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.ComunicadoBusiness>()
            .GetAreasEletricas(cancellationToken);

        // Assert
        result.ShouldNotBeNull();
        result.Count().ShouldBe(areasEletricas.Count);
        
        var sinapseDadosService = DependencyInjectorFactory.Mocker.GetMock<ONS.SINapse.CacheSync.Services.ISinapseDadosDatasetQueryService>();
        sinapseDadosService.Verify(service => service.GetDatasetAsync<ICollection<AreaEletricaComunicadoDto>>(
            It.IsAny<System.Linq.Expressions.Expression<Func<ONS.SINapse.CacheSync.Services.ISinapseDadosDatasetService, Task<ICollection<AreaEletricaComunicadoDto>>>>>(),
            cancellationToken), Times.Once);
    }

    [Fact(DisplayName = "[ComunicadoBusiness] - Deve obter total de comunicados não lidos")]
    [Trait("Business", "ComunicadoBusiness")]
    public async Task ObterTotalDeComunicadosNaoLidosPeloUsuario_DeveRetornarTotal()
    {
        // Arrange
        var totalEsperado = 5L;
        ConfigurarUsuarioComCentros(new List<string> { "SE", "NE" });

        DependencyInjectorFactory.Mocker.GetMock<IComunicadoRepository>()
            .Setup(repo => repo.CountAsync(It.IsAny<Expression<Func<Comunicado, bool>>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(totalEsperado);

        var cancellationToken = CancellationToken.None;

        // Act
        var result = await DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.ComunicadoBusiness>()
            .ObterTotalDeComunicadosNaoLidosPeloUsuario(cancellationToken);

        // Assert
        result.ShouldBe(totalEsperado);
        
        DependencyInjectorFactory.Mocker.GetMock<IComunicadoRepository>()
            .Verify(repo => repo.CountAsync(It.IsAny<Expression<Func<Comunicado, bool>>>(), cancellationToken), Times.Once);
    }

    private void ConfigurarMocksBasicos()
    {
        // Configurar ComunicadoSettings
        var comunicadoSettings = new ComunicadoSettings
        {
            TempoDeVidaDeComunicadoEmDias = 30
        };
        
        var optionsMock = Options.Create(comunicadoSettings);
        DependencyInjectorFactory.Mocker.Use<IOptions<ComunicadoSettings>>(optionsMock);

        // Configurar mocks básicos dos repositórios e serviços
        DependencyInjectorFactory.Mocker.GetMock<IComunicadoRepository>();
        DependencyInjectorFactory.Mocker.GetMock<INotificacaoDeComunicadoBusiness>();
        
        // Configurar ISinapseDadosDatasetQueryService
        var sinapseDadosService = DependencyInjectorFactory.Mocker.GetMock<ONS.SINapse.CacheSync.Services.ISinapseDadosDatasetQueryService>();
        sinapseDadosService.Setup(service => service.GetDatasetAsync<ICollection<AreaEletricaComunicadoDto>>(
                It.IsAny<System.Linq.Expressions.Expression<Func<ONS.SINapse.CacheSync.Services.ISinapseDadosDatasetService, Task<ICollection<AreaEletricaComunicadoDto>>>>>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(ComunicadoFixture.GerarAreasEletricasValidas());
    }

    private void ConfigurarUsuarioComPermissaoParaComplemento(string codigoCentro)
    {
        var scopes = new List<Scope> { new("CENTROS", codigoCentro, "CORS-SE") };
        var operacoes = new List<string> { Operacoes.CriarMulticast };
        var perfil = new Perfil(scopes, operacoes, "Operador", "operador");
        
        DependencyInjectorFactory.Mocker.Get<IUserContextTest>().DefinirPerfil(perfil);
    }

    private void ConfigurarUsuarioSemPermissaoParaComplemento()
    {
        var scopes = new List<Scope> { new("CENTROS", "OUTRO", "CORS-SE") };
        var operacoes = new List<string> { Operacoes.CriarMulticast };
        var perfil = new Perfil(scopes, operacoes, "Operador", "operador");
        
        DependencyInjectorFactory.Mocker.Get<IUserContextTest>().DefinirPerfil(perfil);
    }

    private void ConfigurarUsuarioComCentros(List<string> centros)
    {
        var scopes = centros.Select(c => new Scope("CENTROS", c, "CORS-SE")).ToList();
        var operacoes = new List<string> { Operacoes.CriarMulticast };
        var perfil = new Perfil(scopes, operacoes, "Operador", "operador");
        
        DependencyInjectorFactory.Mocker.Get<IUserContextTest>().DefinirPerfil(perfil);
    }
}
