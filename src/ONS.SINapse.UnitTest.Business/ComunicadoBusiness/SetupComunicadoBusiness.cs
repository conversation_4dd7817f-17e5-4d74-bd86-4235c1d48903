using Microsoft.Extensions.Options;
using ONS.SINapse.Business.IBusiness;
using ONS.SINapse.CacheSync.Services;
using ONS.SINapse.Repository.IRepository;
using ONS.SINapse.Shared.Settings;
using ONS.SINapse.UnitTest.Shared.Fixtures;

namespace ONS.SINapse.UnitTest.Business.ComunicadoBusiness;

public partial class ComunicadoBusinessTest : IClassFixture<ComunicadoFixture>
{
    public ComunicadoBusinessTest()
    {
        DependencyInjectorFactory.Mocker.RestartMock();
        ConfigurarMocksBasicos();
    }

    private static void ConfigurarMocksBasicos()
    {
        // Configurar ComunicadoSettings
        var comunicadoSettings = new ComunicadoSettings
        {
            TempoDeVidaDeComunicadoEmDias = 30
        };
        
        var optionsMock = Options.Create(comunicadoSettings);
        DependencyInjectorFactory.Mocker.Use<IOptions<ComunicadoSettings>>(optionsMock);

        // Configurar mocks básicos dos repositórios e serviços
        DependencyInjectorFactory.Mocker.GetMock<IComunicadoRepository>();
        DependencyInjectorFactory.Mocker.GetMock<INotificacaoDeComunicadoBusiness>();
        DependencyInjectorFactory.Mocker.GetMock<ISinapseDadosDatasetQueryService>();
    }
}

public partial class ComunicadoBusinessAdvancedTest : IClassFixture<ComunicadoFixture>
{
    public ComunicadoBusinessAdvancedTest()
    {
        DependencyInjectorFactory.Mocker.RestartMock();
        ConfigurarMocksBasicos();
    }

    private static void ConfigurarMocksBasicos()
    {
        // Configurar ComunicadoSettings
        var comunicadoSettings = new ComunicadoSettings
        {
            TempoDeVidaDeComunicadoEmDias = 30
        };
        
        var optionsMock = Options.Create(comunicadoSettings);
        DependencyInjectorFactory.Mocker.Use<IOptions<ComunicadoSettings>>(optionsMock);

        // Configurar mocks básicos dos repositórios e serviços
        DependencyInjectorFactory.Mocker.GetMock<IComunicadoRepository>();
        DependencyInjectorFactory.Mocker.GetMock<INotificacaoDeComunicadoBusiness>();
        DependencyInjectorFactory.Mocker.GetMock<ISinapseDadosDatasetQueryService>();
    }
}

public partial class ComunicadoBusinessEdgeCasesTest : IClassFixture<ComunicadoFixture>
{
    public ComunicadoBusinessEdgeCasesTest()
    {
        DependencyInjectorFactory.Mocker.RestartMock();
        ConfigurarMocksBasicos();
    }

    private static void ConfigurarMocksBasicos()
    {
        // Configurar ComunicadoSettings
        var comunicadoSettings = new ComunicadoSettings
        {
            TempoDeVidaDeComunicadoEmDias = 30
        };
        
        var optionsMock = Options.Create(comunicadoSettings);
        DependencyInjectorFactory.Mocker.Use<IOptions<ComunicadoSettings>>(optionsMock);

        // Configurar mocks básicos dos repositórios e serviços
        DependencyInjectorFactory.Mocker.GetMock<IComunicadoRepository>();
        DependencyInjectorFactory.Mocker.GetMock<INotificacaoDeComunicadoBusiness>();
        DependencyInjectorFactory.Mocker.GetMock<ISinapseDadosDatasetQueryService>();
    }
}
