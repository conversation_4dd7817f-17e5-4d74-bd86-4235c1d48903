using Microsoft.Extensions.Options;
using ONS.SINapse.Business.IBusiness;
using ONS.SINapse.Entities.Entities;
using ONS.SINapse.Repository.IRepository;
using ONS.SINapse.Shared.Constants;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Enums;
using ONS.SINapse.Shared.Identity;
using ONS.SINapse.Shared.Notifications;
using ONS.SINapse.Shared.Settings;
using ONS.SINapse.UnitTest.Shared;
using ONS.SINapse.UnitTest.Shared.Fixtures;
using ONS.SINapse.UnitTest.Shared.Identity;

namespace ONS.SINapse.UnitTest.Business.ComunicadoBusiness;

public class ComunicadoBusinessValidationTest : IClassFixture<ComunicadoFixture>
{
    private readonly ComunicadoFixture _comunicadoFixture;
    protected readonly MockDependencyInjectorFactory DependencyInjectorFactory;

    public ComunicadoBusinessValidationTest(ComunicadoFixture comunicadoFixture)
    {
        _comunicadoFixture = comunicadoFixture;
        DependencyInjectorFactory = new MockDependencyInjectorFactory();
        DependencyInjectorFactory.RegisterMocks();
        ConfigurarMocksBasicos();
    }

    [Fact(DisplayName = "[ComunicadoBusiness] - Deve falhar ao adicionar comunicado com título muito longo")]
    [Trait("Business", "ComunicadoBusiness")]
    public async Task AddAsync_TituloMuitoLongo_DeveFalhar()
    {
        // Arrange
        var comunicadoDto = ComunicadoFixture.GerarComunicadoDtoComTituloLongo();
        
        ConfigurarUsuarioComPermissoes(Operacoes.CriarMulticast);

        var cancellationToken = CancellationToken.None;

        // Act
        var result = await DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.ComunicadoBusiness>()
            .AddAsync(comunicadoDto, cancellationToken);

        // Assert
        result.ShouldNotBeNull();
        result.Id.ShouldBeNullOrEmpty();
        
        var notificationContext = DependencyInjectorFactory.Mocker.Get<NotificationContext>();
        notificationContext.HasNotifications.ShouldBeTrue();
        
        DependencyInjectorFactory.Mocker.GetMock<IComunicadoRepository>()
            .Verify(repo => repo.AddAsync(It.IsAny<Comunicado>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact(DisplayName = "[ComunicadoBusiness] - Deve falhar ao adicionar comunicado com mensagem muito longa")]
    [Trait("Business", "ComunicadoBusiness")]
    public async Task AddAsync_MensagemMuitoLonga_DeveFalhar()
    {
        // Arrange
        var comunicadoDto = ComunicadoFixture.GerarComunicadoDtoComMensagemLonga();
        
        ConfigurarUsuarioComPermissoes(Operacoes.CriarMulticast);

        var cancellationToken = CancellationToken.None;

        // Act
        var result = await DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.ComunicadoBusiness>()
            .AddAsync(comunicadoDto, cancellationToken);

        // Assert
        result.ShouldNotBeNull();
        result.Id.ShouldBeNullOrEmpty();
        
        var notificationContext = DependencyInjectorFactory.Mocker.Get<NotificationContext>();
        notificationContext.HasNotifications.ShouldBeTrue();
        
        DependencyInjectorFactory.Mocker.GetMock<IComunicadoRepository>()
            .Verify(repo => repo.AddAsync(It.IsAny<Comunicado>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact(DisplayName = "[ComunicadoBusiness] - Deve falhar ao adicionar complemento inválido")]
    [Trait("Business", "ComunicadoBusiness")]
    public async Task AdicionarComplentoAsync_ComplementoInvalido_DeveFalhar()
    {
        // Arrange
        var comunicado = _comunicadoFixture.GerarComunicadoValido();
        var complemento = ComunicadoFixture.GerarComplementoDeComunicadoDtoInvalido();
        var id = ComunicadoFixture.IdComunicadoPadrao;

        ConfigurarUsuarioComPermissaoParaComplemento("SE");

        DependencyInjectorFactory.Mocker.GetMock<IComunicadoRepository>()
            .Setup(repo => repo.GetOneAsync(id, It.IsAny<CancellationToken>()))
            .ReturnsAsync(comunicado);

        var cancellationToken = CancellationToken.None;

        // Act
        var result = await DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.ComunicadoBusiness>()
            .AdicionarComplentoAsync(id, complemento, cancellationToken);

        // Assert
        result.ShouldNotBeNull();
        
        var notificationContext = DependencyInjectorFactory.Mocker.Get<NotificationContext>();
        notificationContext.HasNotifications.ShouldBeTrue();
        notificationContext.Notifications.ShouldContain(n => n.Message.Contains("Mensagem obrigatória"));
        
        DependencyInjectorFactory.Mocker.GetMock<IComunicadoRepository>()
            .Verify(repo => repo.UpdateAsync(It.IsAny<string>(), It.IsAny<Comunicado>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact(DisplayName = "[ComunicadoBusiness] - Deve falhar ao adicionar comunicado com destinatários inválidos")]
    [Trait("Business", "ComunicadoBusiness")]
    public async Task AddAsync_DestinatariosInvalidos_DeveFalhar()
    {
        // Arrange
        var comunicadoDto = ComunicadoFixture.GerarComunicadoDtoValido();
        comunicadoDto.TipoDeComunicado = TipoDeComunicado.AreasEletricas;
        comunicadoDto.Destinatarios = ComunicadoFixture.GerarDestinatariosInvalidos();

        ConfigurarUsuarioComPermissoes(Operacoes.CriarMulticast);
        ConfigurarSinapseDadosComAgentesValidos();

        var cancellationToken = CancellationToken.None;

        // Act
        var result = await DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.ComunicadoBusiness>()
            .AddAsync(comunicadoDto, cancellationToken);

        // Assert
        result.ShouldNotBeNull();
        result.Id.ShouldBeNullOrEmpty();
        
        var notificationContext = DependencyInjectorFactory.Mocker.Get<NotificationContext>();
        notificationContext.HasNotifications.ShouldBeTrue();
        notificationContext.Notifications.ShouldContain(n => n.Message.Contains("Nenhum agente localizado"));
        
        DependencyInjectorFactory.Mocker.GetMock<IComunicadoRepository>()
            .Verify(repo => repo.AddAsync(It.IsAny<Comunicado>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact(DisplayName = "[ComunicadoBusiness] - Deve falhar ao adicionar comunicado com centros regionais inválidos")]
    [Trait("Business", "ComunicadoBusiness")]
    public async Task AddAsync_CentrosRegionaisInvalidos_DeveFalhar()
    {
        // Arrange
        var comunicadoDto = ComunicadoFixture.GerarComunicadoDtoValido();
        comunicadoDto.TipoDeComunicado = TipoDeComunicado.CentrosRegionais;
        comunicadoDto.Destinatarios = ComunicadoFixture.GerarCentrosRegionaisInvalidos();

        ConfigurarUsuarioComPermissoes(Operacoes.CriarBroadcast);

        var cancellationToken = CancellationToken.None;

        // Act
        var result = await DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.ComunicadoBusiness>()
            .AddAsync(comunicadoDto, cancellationToken);

        // Assert
        result.ShouldNotBeNull();
        result.Id.ShouldBeNullOrEmpty();
        
        var notificationContext = DependencyInjectorFactory.Mocker.Get<NotificationContext>();
        notificationContext.HasNotifications.ShouldBeTrue();
        notificationContext.Notifications.ShouldContain(n => n.Message.Contains("não localizados"));
        
        DependencyInjectorFactory.Mocker.GetMock<IComunicadoRepository>()
            .Verify(repo => repo.AddAsync(It.IsAny<Comunicado>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact(DisplayName = "[ComunicadoBusiness] - Deve adicionar comunicado com destinatários válidos")]
    [Trait("Business", "ComunicadoBusiness")]
    public async Task AddAsync_DestinatariosValidos_DeveAdicionarComSucesso()
    {
        // Arrange
        var comunicadoDto = ComunicadoFixture.GerarComunicadoDtoValido();
        comunicadoDto.TipoDeComunicado = TipoDeComunicado.AreasEletricas;
        comunicadoDto.Destinatarios = ComunicadoFixture.GerarDestinatariosValidos();

        ConfigurarUsuarioComPermissoes(Operacoes.CriarMulticast);
        ConfigurarSinapseDadosComTodosAgentesValidos();

        var cancellationToken = CancellationToken.None;

        // Act
        var result = await DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.ComunicadoBusiness>()
            .AddAsync(comunicadoDto, cancellationToken);

        // Assert
        result.ShouldNotBeNull();
        result.Id.ShouldNotBeNullOrEmpty();
        
        DependencyInjectorFactory.Mocker.GetMock<IComunicadoRepository>()
            .Verify(repo => repo.AddAsync(It.IsAny<Comunicado>(), cancellationToken), Times.Once);
    }

    [Fact(DisplayName = "[ComunicadoBusiness] - Deve adicionar comunicado com centros regionais válidos")]
    [Trait("Business", "ComunicadoBusiness")]
    public async Task AddAsync_CentrosRegionaisValidos_DeveAdicionarComSucesso()
    {
        // Arrange
        var comunicadoDto = ComunicadoFixture.GerarComunicadoDtoValido();
        comunicadoDto.TipoDeComunicado = TipoDeComunicado.CentrosRegionais;
        comunicadoDto.Destinatarios = ComunicadoFixture.GerarCentrosRegionaisValidos();

        ConfigurarUsuarioComPermissoes(Operacoes.CriarBroadcast);

        var cancellationToken = CancellationToken.None;

        // Act
        var result = await DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.ComunicadoBusiness>()
            .AddAsync(comunicadoDto, cancellationToken);

        // Assert
        result.ShouldNotBeNull();
        result.Id.ShouldNotBeNullOrEmpty();
        
        DependencyInjectorFactory.Mocker.GetMock<IComunicadoRepository>()
            .Verify(repo => repo.AddAsync(It.IsAny<Comunicado>(), cancellationToken), Times.Once);
    }

    private void ConfigurarMocksBasicos()
    {
        // Configurar ComunicadoSettings
        var comunicadoSettings = new ComunicadoSettings
        {
            TempoDeVidaDeComunicadoEmDias = 30
        };
        
        var optionsMock = Options.Create(comunicadoSettings);
        DependencyInjectorFactory.Mocker.Use(optionsMock);

        // Configurar mocks básicos dos repositórios e serviços
        DependencyInjectorFactory.Mocker.GetMock<IComunicadoRepository>();
        DependencyInjectorFactory.Mocker.GetMock<INotificacaoDeComunicadoBusiness>();
    }

    private void ConfigurarUsuarioComPermissoes(string operacao)
    {
        var scopes = new List<Scope> { new("CENTROS", "SE", "CORS-SE") };
        var operacoes = new List<string> { operacao };
        var perfil = new Perfil(scopes, operacoes, "Operador", "operador");
        
        DependencyInjectorFactory.Mocker.Get<IUserContextTest>().DefinirPerfil(perfil);
    }

    private void ConfigurarUsuarioComPermissaoParaComplemento(string codigoCentro)
    {
        var scopes = new List<Scope> { new("CENTROS", codigoCentro, "CORS-SE") };
        var operacoes = new List<string> { Operacoes.CriarMulticast };
        var perfil = new Perfil(scopes, operacoes, "Operador", "operador");
        
        DependencyInjectorFactory.Mocker.Get<IUserContextTest>().DefinirPerfil(perfil);
    }

    private void ConfigurarSinapseDadosComAgentesValidos()
    {
        var areasEletricas = new List<AreaEletricaComunicadoDto>
        {
            new("AREA1", "Área Elétrica 1", "SE", new List<AgenteDto>
            {
                new("AGENTE1", "Agente 1")
            })
        };

        var sinapseDadosService = DependencyInjectorFactory.Mocker.GetMock<CacheSync.Services.ISinapseDadosDatasetQueryService>();
        sinapseDadosService.Setup(service => service.GetDatasetAsync(
                It.IsAny<System.Linq.Expressions.Expression<Func<CacheSync.Services.ISinapseDadosDatasetService, Task<ICollection<AreaEletricaComunicadoDto>>>>>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(areasEletricas);
    }

    private void ConfigurarSinapseDadosComTodosAgentesValidos()
    {
        var areasEletricas = new List<AreaEletricaComunicadoDto>
        {
            new("AREA1", "Área Elétrica 1", "SE", new List<AgenteDto>
            {
                new("AGENTE001", "Agente 001"),
                new("AGENTE002", "Agente 002"),
                new("AGENTE003", "Agente 003")
            })
        };

        var sinapseDadosService = DependencyInjectorFactory.Mocker.GetMock<CacheSync.Services.ISinapseDadosDatasetQueryService>();
        sinapseDadosService.Setup(service => service.GetDatasetAsync(
                It.IsAny<System.Linq.Expressions.Expression<Func<CacheSync.Services.ISinapseDadosDatasetService, Task<ICollection<AreaEletricaComunicadoDto>>>>>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(areasEletricas);
    }
}
