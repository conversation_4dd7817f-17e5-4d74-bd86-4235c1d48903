using Microsoft.Extensions.Options;
using ONS.SINapse.Business.IBusiness;
using ONS.SINapse.Entities.Entities;
using ONS.SINapse.Repository.IRepository;
using ONS.SINapse.Shared.Constants;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Identity;
using ONS.SINapse.Shared.Notifications;
using ONS.SINapse.Shared.Settings;
using ONS.SINapse.UnitTest.Shared;
using ONS.SINapse.UnitTest.Shared.Fixtures;
using ONS.SINapse.UnitTest.Shared.Identity;
using System.Linq.Expressions;

namespace ONS.SINapse.UnitTest.Business.ComunicadoBusiness;

public class ComunicadoBusinessTest : IClassFixture<ComunicadoFixture>
{
    private readonly ComunicadoFixture _comunicadoFixture;
    protected readonly MockDependencyInjectorFactory DependencyInjectorFactory;

    public ComunicadoBusinessTest(ComunicadoFixture comunicadoFixture)
    {
        _comunicadoFixture = comunicadoFixture;
        DependencyInjectorFactory = new MockDependencyInjectorFactory();
        DependencyInjectorFactory.RegisterMocks();
        ConfigurarMocksBasicos();
    }

    [Fact(DisplayName = "[ComunicadoBusiness] - Deve obter comunicados com sucesso")]
    [Trait("Business", "ComunicadoBusiness")]
    public async Task GetAsync_DeveObterComunicadosComSucesso()
    {
        // Arrange
        var comunicados = new List<Comunicado>
        {
            _comunicadoFixture.GerarComunicadoValido(),
            _comunicadoFixture.GerarComunicadoValido()
        };

        DependencyInjectorFactory.Mocker.GetMock<IComunicadoRepository>()
            .Setup(repo => repo.GetAsync(It.IsAny<Expression<Func<Comunicado, bool>>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(comunicados);

        var cancellationToken = CancellationToken.None;

        // Act
        var result = await DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.ComunicadoBusiness>()
            .GetAsync(cancellationToken);

        // Assert
        result.ShouldNotBeNull();
        result.Count().ShouldBe(2);

        DependencyInjectorFactory.Mocker.GetMock<IComunicadoRepository>()
            .Verify(repo => repo.GetAsync(It.IsAny<Expression<Func<Comunicado, bool>>>(), cancellationToken), Times.Once);
    }

    [Fact(DisplayName = "[ComunicadoBusiness] - Deve adicionar comunicado com sucesso")]
    [Trait("Business", "ComunicadoBusiness")]
    public async Task AddAsync_ComunicadoValido_DeveAdicionarComSucesso()
    {
        // Arrange
        var comunicadoDto = ComunicadoFixture.GerarComunicadoDtoValido();
        
        ConfigurarUsuarioComPermissoes(Operacoes.CriarMulticast);

        var cancellationToken = CancellationToken.None;

        // Act
        var result = await DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.ComunicadoBusiness>()
            .AddAsync(comunicadoDto, cancellationToken);

        // Assert
        result.ShouldNotBeNull();
        result.Id.ShouldNotBeNullOrEmpty();

        DependencyInjectorFactory.Mocker.GetMock<IComunicadoRepository>()
            .Verify(repo => repo.AddAsync(It.IsAny<Comunicado>(), cancellationToken), Times.Once);

        DependencyInjectorFactory.Mocker.GetMock<INotificacaoDeComunicadoBusiness>()
            .Verify(business => business.NotificarComunicadoAsync(It.IsAny<Comunicado>(), It.IsAny<string>(), cancellationToken), Times.Once);
    }

    [Fact(DisplayName = "[ComunicadoBusiness] - Deve falhar ao adicionar comunicado inválido")]
    [Trait("Business", "ComunicadoBusiness")]
    public async Task AddAsync_ComunicadoInvalido_DeveFalhar()
    {
        // Arrange
        var comunicadoDto = ComunicadoFixture.GerarComunicadoDtoInvalido();
        var cancellationToken = CancellationToken.None;

        // Act
        var result = await DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.ComunicadoBusiness>()
            .AddAsync(comunicadoDto, cancellationToken);

        // Assert
        result.ShouldNotBeNull();
        result.Id.ShouldBeNullOrEmpty();

        var notificationContext = DependencyInjectorFactory.Mocker.Get<NotificationContext>();
        notificationContext.HasNotifications.ShouldBeTrue();

        DependencyInjectorFactory.Mocker.GetMock<IComunicadoRepository>()
            .Verify(repo => repo.AddAsync(It.IsAny<Comunicado>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact(DisplayName = "[ComunicadoBusiness] - Deve adicionar comunicado tipo Todos com sucesso")]
    [Trait("Business", "ComunicadoBusiness")]
    public async Task AddAsync_ComunicadoTodos_DeveAdicionarComSucesso()
    {
        // Arrange
        var comunicadoDto = ComunicadoFixture.GerarComunicadoDtoTodos();

        ConfigurarUsuarioComPermissoes(Operacoes.CriarBroadcast);

        var cancellationToken = CancellationToken.None;

        // Act
        var result = await DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.ComunicadoBusiness>()
            .AddAsync(comunicadoDto, cancellationToken);

        // Assert
        result.ShouldNotBeNull();
        result.Id.ShouldNotBeNullOrEmpty();

        DependencyInjectorFactory.Mocker.GetMock<IComunicadoRepository>()
            .Verify(repo => repo.AddAsync(It.IsAny<Comunicado>(), cancellationToken), Times.Once);
    }

    [Fact(DisplayName = "[ComunicadoBusiness] - Deve adicionar comunicado para centros regionais usando fixture")]
    [Trait("Business", "ComunicadoBusiness")]
    public async Task AddAsync_CentrosRegionaisFixture_DeveAdicionarComSucesso()
    {
        // Arrange
        var comunicadoDto = ComunicadoFixture.GerarComunicadoDtoCentrosRegionais();

        ConfigurarUsuarioComPermissoes(Operacoes.CriarBroadcast);

        var cancellationToken = CancellationToken.None;

        // Act
        var result = await DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.ComunicadoBusiness>()
            .AddAsync(comunicadoDto, cancellationToken);

        // Assert
        result.ShouldNotBeNull();
        result.Id.ShouldNotBeNullOrEmpty();

        DependencyInjectorFactory.Mocker.GetMock<IComunicadoRepository>()
            .Verify(repo => repo.AddAsync(It.IsAny<Comunicado>(), cancellationToken), Times.Once);
    }

    [Fact(DisplayName = "[ComunicadoBusiness] - Deve obter comunicado por ID com sucesso")]
    [Trait("Business", "ComunicadoBusiness")]
    public async Task GetByIdAsync_ComunicadoExistente_DeveRetornarComunicado()
    {
        // Arrange
        var comunicado = _comunicadoFixture.GerarComunicadoValido();
        var id = ComunicadoFixture.IdComunicadoPadrao;

        DependencyInjectorFactory.Mocker.GetMock<IComunicadoRepository>()
            .Setup(repo => repo.GetOneAsync(id, It.IsAny<CancellationToken>()))
            .ReturnsAsync(comunicado);

        var cancellationToken = CancellationToken.None;

        // Act
        var result = await DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.ComunicadoBusiness>()
            .GetByIdAsync(id, cancellationToken);

        // Assert
        result.ShouldNotBeNull();
        result.Id.ShouldNotBeNullOrEmpty();

        DependencyInjectorFactory.Mocker.GetMock<IComunicadoRepository>()
            .Verify(repo => repo.GetOneAsync(id, cancellationToken), Times.Once);
    }

    [Fact(DisplayName = "[ComunicadoBusiness] - Deve registrar leitura com sucesso")]
    [Trait("Business", "ComunicadoBusiness")]
    public async Task RegistrarLeituraAsync_ComunicadoExistente_DeveRegistrarLeitura()
    {
        // Arrange
        var comunicado = _comunicadoFixture.GerarComunicadoValido();
        var id = ComunicadoFixture.IdComunicadoPadrao;

        DependencyInjectorFactory.Mocker.GetMock<IComunicadoRepository>()
            .Setup(repo => repo.GetOneAsync(id, It.IsAny<CancellationToken>()))
            .ReturnsAsync(comunicado);

        var cancellationToken = CancellationToken.None;

        // Act
        var result = await DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.ComunicadoBusiness>()
            .RegistrarLeituraAsync(id, cancellationToken);

        // Assert
        result.ShouldNotBeNull();
        result.Id.ShouldNotBeNullOrEmpty();

        DependencyInjectorFactory.Mocker.GetMock<IComunicadoRepository>()
            .Verify(repo => repo.UpdateAsync(id, It.IsAny<Comunicado>(), cancellationToken), Times.Once);
    }

    [Fact(DisplayName = "[ComunicadoBusiness] - Deve propagar CancellationToken corretamente")]
    [Trait("Business", "ComunicadoBusiness")]
    public async Task GetAsync_DevePropagarCancellationToken()
    {
        // Arrange
        var comunicados = new List<Comunicado>();
        var cancellationTokenSource = new CancellationTokenSource();
        var cancellationToken = cancellationTokenSource.Token;

        DependencyInjectorFactory.Mocker.GetMock<IComunicadoRepository>()
            .Setup(repo => repo.GetAsync(It.IsAny<Expression<Func<Comunicado, bool>>>(), cancellationToken))
            .ReturnsAsync(comunicados);

        // Act
        await DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.ComunicadoBusiness>()
            .GetAsync(cancellationToken);

        // Assert
        DependencyInjectorFactory.Mocker.GetMock<IComunicadoRepository>()
            .Verify(repo => repo.GetAsync(It.IsAny<Expression<Func<Comunicado, bool>>>(), cancellationToken), Times.Once);
    }

    [Fact(DisplayName = "[ComunicadoBusiness] - Deve adicionar complemento com sucesso")]
    [Trait("Business", "ComunicadoBusiness")]
    public async Task AdicionarComplentoAsync_ComplementoValido_DeveAdicionarComSucesso()
    {
        // Arrange
        var comunicado = _comunicadoFixture.GerarComunicadoValido();
        var complemento = ComunicadoFixture.GerarComplementoDeComunicadoDtoValido();
        var id = ComunicadoFixture.IdComunicadoPadrao;

        ConfigurarUsuarioComPermissaoParaComplemento("SE");

        DependencyInjectorFactory.Mocker.GetMock<IComunicadoRepository>()
            .Setup(repo => repo.GetOneAsync(id, It.IsAny<CancellationToken>()))
            .ReturnsAsync(comunicado);

        var cancellationToken = CancellationToken.None;

        // Act
        var result = await DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.ComunicadoBusiness>()
            .AdicionarComplentoAsync(id, complemento, cancellationToken);

        // Assert
        result.ShouldNotBeNull();
        result.Id.ShouldNotBeNullOrEmpty();

        DependencyInjectorFactory.Mocker.GetMock<IComunicadoRepository>()
            .Verify(repo => repo.UpdateAsync(id, It.IsAny<Comunicado>(), cancellationToken), Times.Once);

        DependencyInjectorFactory.Mocker.GetMock<INotificacaoDeComunicadoBusiness>()
            .Verify(business => business.NotificarComunicadoAsync(It.IsAny<Comunicado>(), It.IsAny<string>(), cancellationToken), Times.Once);
    }

    private void ConfigurarMocksBasicos()
    {
        // Configurar ComunicadoSettings
        var comunicadoSettings = new ComunicadoSettings
        {
            TempoDeVidaDeComunicadoEmDias = 30
        };

        var optionsMock = Options.Create(comunicadoSettings);
        DependencyInjectorFactory.Mocker.Use(optionsMock);

        // Configurar mocks básicos dos repositórios e serviços
        DependencyInjectorFactory.Mocker.GetMock<IComunicadoRepository>();
        DependencyInjectorFactory.Mocker.GetMock<INotificacaoDeComunicadoBusiness>();

        // Configurar ISinapseDadosDatasetQueryService
        var sinapseDadosService = DependencyInjectorFactory.Mocker.GetMock<CacheSync.Services.ISinapseDadosDatasetQueryService>();
        sinapseDadosService.Setup(service => service.GetDatasetAsync<ICollection<AreaEletricaComunicadoDto>>(
                It.IsAny<Expression<Func<CacheSync.Services.ISinapseDadosDatasetService, Task<ICollection<AreaEletricaComunicadoDto>>>>>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(ComunicadoFixture.GerarAreasEletricasValidas());
    }

    private void ConfigurarUsuarioComPermissoes(string operacao)
    {
        var scopes = new List<Scope> { new("CENTROS", "SE", "CORS-SE") };
        var operacoes = new List<string> { operacao };
        var perfil = new Perfil(scopes, operacoes, "Operador", "operador");

        DependencyInjectorFactory.Mocker.Get<IUserContextTest>().DefinirPerfil(perfil);
    }

    private void ConfigurarUsuarioComPermissaoParaComplemento(string codigoCentro)
    {
        var scopes = new List<Scope> { new("CENTROS", codigoCentro, "CORS-SE") };
        var operacoes = new List<string> { Operacoes.CriarMulticast };
        var perfil = new Perfil(scopes, operacoes, "Operador", "operador");

        DependencyInjectorFactory.Mocker.Get<IUserContextTest>().DefinirPerfil(perfil);
    }
}
