using Microsoft.Extensions.DependencyInjection;
using ONS.SINapse.Repository.IRepository.Firebase;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands.Firebase;

namespace ONS.SINapse.UnitTest.Business.ChatDeSolicitacao
{
    public partial class ChatDeSolicitacaoTest
    {
        [Fact(DisplayName = "[Chat Solicitação] - Deve adicionar chat")]
        [Trait("Business", "Chat de Solicitacao")]
        public async Task DeveAdicionarChatSolicitacao()
        {
            // Arrange
            var solicitacao = _solicitacaoFixture.GerarSolicitacaoPendente();
            var origem = solicitacao.Origem.Codigo;

            ConfigurarMockUserContext(origem);

            _dependencyInjectorFactory.Mocker
                .GetMock<ISolicitacaoFirebaseRepository>()
                .Setup(repository => repository.GetByIdAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(solicitacao);

            var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

            var command = new ChatDeSolicitacaoCommand(solicitacao.Id, "Teste chat Solicitação");

            // Act
            var response = await mediator.EnviarComandoAsync<ChatDeSolicitacaoCommand>(command, CancellationToken.None);

            // Assert
            Assert.NotNull(response);
            response.IsValid.ShouldBeTrue(response.ToString());

            response.Errors.ShouldBeEmpty();

            _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoFirebaseRepository>().Verify(
                repository => repository.GetByIdAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()),
                Times.Once);

            _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoFirebaseRepository>().Verify(
                repository => repository.AddUpdateAsync(It.IsAny<Dictionary<string, object?>>(), It.IsAny<CancellationToken>()),
                Times.Once);

            var criarMensagemNoChatDeSolicitacaoFirebaseCommandPublished =
                await _dependencyInjectorFactory.Harness.Published.Any<CriarMensagemNoChatDeSolicitacaoFirebaseCommand>();

            criarMensagemNoChatDeSolicitacaoFirebaseCommandPublished.ShouldBeTrue();
        }
    }
}
