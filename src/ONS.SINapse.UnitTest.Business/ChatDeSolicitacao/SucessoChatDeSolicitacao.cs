using Microsoft.Extensions.DependencyInjection;
using ONS.SINapse.Repository.IRepository;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using System.Linq.Expressions;

namespace ONS.SINapse.UnitTest.Business.ChatDeSolicitacao
{
    public partial class ChatDeSolicitacaoTest
    {
        [Fact(DisplayName = "[Chat Solicitação] - Deve adicionar chat")]
        [Trait("Business", "Chat de Solicitacao")]
        public async Task DeveAdicionarChatSolicitacao()
        {
            // Arrange
            var solicitacao = _solicitacaoFixture.GerarSolicitacaoPendente();
            var origem = solicitacao.Origem.Codigo;

            ConfigurarMockUserContext(origem);

            _dependencyInjectorFactory.Mocker
                .GetMock<ISolicitacaoRepository>()
                .Setup(repository => repository.GetAsync(It.IsAny<Expression<Func<Entities.Entities.Solicitacao, bool>>>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync([solicitacao]);

            var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

            var command = new ChatDeSolicitacaoCommand(solicitacao.Id, "Teste chat Solicitação");

            // Act
            var response = await mediator.EnviarComandoAsync<ChatDeSolicitacaoCommand>(command, CancellationToken.None);

            // Assert
            Assert.NotNull(response);
            response.IsValid.ShouldBeTrue(response.ToString());

            response.Errors.ShouldBeEmpty();

            _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoRepository>().Verify(
                repository => repository.GetAsync(It.IsAny<Expression<Func<Entities.Entities.Solicitacao, bool>>>(), It.IsAny<CancellationToken>()),
                Times.Once);

            _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoRepository>().Verify(
                repository => repository.AtualizarAsync(It.IsAny<Entities.Entities.Solicitacao>(), It.IsAny<CancellationToken>()),
                Times.Once);

            var enviarMensagemChatSolicitacaoNoFirebaseCommandPublished =
                await _dependencyInjectorFactory.Harness.Published.Any<EnviarMensagemChatSolicitacaoNoFirebaseCommand>();

            enviarMensagemChatSolicitacaoNoFirebaseCommandPublished.ShouldBeTrue();
        }
    }
}
