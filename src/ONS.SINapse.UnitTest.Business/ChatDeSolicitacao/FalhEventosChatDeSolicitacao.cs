using FluentValidation.Results;
using ONS.SINapse.Repository.IRepository.Firebase;
using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Shared.Messages;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands.Firebase;

namespace ONS.SINapse.UnitTest.Business.ChatDeSolicitacao
{
    public partial class ChatDeSolicitacaoTest
    {
        [Fact(DisplayName = "[Chat Solicitação] - Deve retornar um erro em enviar mensagem chat solicitação no firebase")]
        [Trait("Business", "Chat de Solicitacao")]
        public async Task ChatSolicitacaoo_DeveRetornarUmErro_FirebaseCommand()
        {
            // Arrange
            var solicitacao = _solicitacaoFixture.GerarSolicitacaoPendente();

            var command = new ChatDeSolicitacaoCommand(solicitacao.Id, "Teste chat Solicitação");

            _dependencyInjectorFactory.Mocker
                .GetMock<ISolicitacaoFirebaseRepository>()
                .Setup(repository => repository.GetByIdAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(solicitacao);

            var result = new ValidationResult();
            result.AdicionarErro("Erro ao enviar command para o firebase");

            var mediatorMock = _dependencyInjectorFactory.Mocker.GetMock<IMediatorHandler>();

            mediatorMock
                .Setup(x => x.EnviarComandoAsync(
                    It.Is<Command>(cmd => cmd is CriarMensagemNoChatDeSolicitacaoFirebaseCommand),
                    It.IsAny<CancellationToken>()))
                .ReturnsAsync(result);

            // Act
            var response =
                await mediatorMock.Object
                    .EnviarComandoAsync<ChatDeSolicitacaoCommand>(
                        command, CancellationToken.None);

            // Assert
            Assert.NotNull(response);
            response.IsValid.ShouldBeFalse(response.ToString());

            response.Errors.ShouldNotBeEmpty();

            _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoFirebaseRepository>().Verify(
                repository => repository.GetByIdAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()),
                Times.Once);

            _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoFirebaseRepository>().Verify(
                repository => repository.AddUpdateAsync(It.IsAny<Dictionary<string, object?>>(), It.IsAny<CancellationToken>()),
                Times.Never);

            var criarMensagemNoChatDeSolicitacaoFirebaseCommandPublished =
                await _dependencyInjectorFactory.Harness.Published.Any<CriarMensagemNoChatDeSolicitacaoFirebaseCommand>();

            criarMensagemNoChatDeSolicitacaoFirebaseCommandPublished.ShouldBeFalse();
        }
    }
}