using FluentValidation.Results;
using ONS.SINapse.Repository.IRepository;
using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using System.Linq.Expressions;

namespace ONS.SINapse.UnitTest.Business.ChatDeSolicitacao
{
    public partial class ChatDeSolicitacaoTest
    {
        [Fact(DisplayName = "[Chat Solicitação] - Deve retornar um erro em enviar mensagem chat solicitação no firebase")]
        [Trait("Business", "Chat de Solicitacao")]
        public async Task ChatSolicitacaoo_DeveRetornarUmErro_FirebaseCommand()
        {
            // Arrange
            var solicitacao = _solicitacaoFixture.GerarSolicitacaoPendente();

            var command = new ChatDeSolicitacaoCommand(solicitacao.Id, "Teste chat Solicitação");

            _dependencyInjectorFactory.Mocker
                .GetMock<ISolicitacaoRepository>()
                .Setup(repository => repository.GetAsync(It.IsAny<Expression<Func<Entities.Entities.Solicitacao, bool>>>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync([solicitacao]);

            var result = new ValidationResult();
            result.AdicionarErro("Erro ao enviar command para o firebase");

            var mediatorMock = _dependencyInjectorFactory.Mocker.GetMock<IMediatorHandler>();

            mediatorMock
                .Setup(x => x.EnviarComandoAsync(It.IsAny<EnviarMensagemChatSolicitacaoNoFirebaseCommand>(),
                    It.IsAny<CancellationToken>()))
                .ReturnsAsync(result);

            // Act
            var response =
                await mediatorMock.Object
                    .EnviarComandoAsync<ChatDeSolicitacaoCommand>(
                        command, CancellationToken.None);

            // Assert
            Assert.NotNull(response);
            response.IsValid.ShouldBeTrue(response.ToString());

            response.Errors.ShouldBeEmpty();

            _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoRepository>().Verify(
                repository => repository.GetAsync(It.IsAny<Expression<Func<Entities.Entities.Solicitacao, bool>>>(), It.IsAny<CancellationToken>()),
                Times.Once);

            _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoRepository>().Verify(
                repository => repository.AtualizarAsync(It.IsAny<Entities.Entities.Solicitacao>(), It.IsAny<CancellationToken>()),
                Times.Once);

            var enviarMensagemChatSolicitacaoNoFirebaseCommandPublished =
                await _dependencyInjectorFactory.Harness.Published.Any<EnviarMensagemChatSolicitacaoNoFirebaseCommand>();

            enviarMensagemChatSolicitacaoNoFirebaseCommandPublished.ShouldBeFalse();
        }
    }
}