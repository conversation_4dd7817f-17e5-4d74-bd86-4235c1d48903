using Microsoft.Extensions.DependencyInjection;
using ONS.SINapse.Repository.IRepository.Firebase;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands.Firebase;

namespace ONS.SINapse.UnitTest.Business.ChatDeSolicitacao
{
    public partial class ChatDeSolicitacaoTest
    {
        [Fact(DisplayName = "[Chat Solicitação] - Deve retornar erro quando solicitação não existir")]
        [Trait("Business", "Chat de Solicitacao")]
        public async Task ChatSolicitacao_DeveRetornarErro_SolicitacaoNaoEncontrada()
        {
            // Arrange
            var solicitacao = _solicitacaoFixture.GerarSolicitacaoPendente();
            var origem = solicitacao.Origem.Codigo;

            ConfigurarMockUserContext(origem);

            _dependencyInjectorFactory.Mocker
                .GetMock<ISolicitacaoFirebaseRepository>()
                .Setup(repository => repository.GetByIdAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()));

            var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

            var command = new ChatDeSolicitacaoCommand(solicitacao.Id, "Teste chat Solicitação");

            // Act
            var response = await mediator.EnviarComandoAsync<ChatDeSolicitacaoCommand>(command, CancellationToken.None);

            // Assert
            response.ShouldNotBeNull();
            response.IsValid.ShouldBeFalse(response.ToString());
            response.Errors.ShouldNotBeEmpty();

            response.Errors.Any(x => x.ErrorMessage == "Solicitação não encontrada.").ShouldBeTrue();

            _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoFirebaseRepository>().Verify(
                repository => repository.GetByIdAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()),
                Times.Once);

            _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoFirebaseRepository>().Verify(
                repository => repository.AddUpdateAsync(It.IsAny<Dictionary<string, object?>>(), It.IsAny<CancellationToken>()),
                Times.Never);

            var criarMensagemNoChatDeSolicitacaoFirebaseCommandPublished =
                await _dependencyInjectorFactory.Harness.Published.Any<CriarMensagemNoChatDeSolicitacaoFirebaseCommand>();

            criarMensagemNoChatDeSolicitacaoFirebaseCommandPublished.ShouldBeFalse();
        }
    }
}
