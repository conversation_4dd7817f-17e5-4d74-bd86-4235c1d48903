using Microsoft.Extensions.DependencyInjection;
using ONS.SINapse.Repository.IRepository;
using ONS.SINapse.Repository.IRepository.Firebase;
using ONS.SINapse.Repository.IRepository.InMemory;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using System.Linq.Expressions;

namespace ONS.SINapse.UnitTest.Business.ChatDeSolicitacao
{
    public partial class ChatDeSolicitacaoTest
    {
        [Fact(DisplayName = "[Chat Solicitação] - Deve retornar erro quando solicitação não existir")]
        [Trait("Business", "Chat de Solicitacao")]
        public async Task ChatSolicitacao_DeveRetornarErro_SolicitacaoNaoEncontrada()
        {
            // Arrange
            var solicitacao = _solicitacaoFixture.GerarSolicitacaoPendente();
            var origem = solicitacao.Origem.Codigo;

            ConfigurarMockUserContext(origem);

            _dependencyInjectorFactory.Mocker
                .GetMock<ISolicitacaoMemoryRepository>()
                .Setup(repository => repository.GetList(It.IsAny<Expression<Func<Entities.Entities.Solicitacao, bool>>>()))
                .Returns([]);

            var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

            var command = new ChatDeSolicitacaoCommand(solicitacao.Id, "Teste chat Solicitação");

            // Act
            var response = await mediator.EnviarComandoAsync<ChatDeSolicitacaoCommand>(command, CancellationToken.None);

            // Assert
            response.ShouldNotBeNull();
            response.IsValid.ShouldBeFalse(response.ToString());
            response.Errors.ShouldNotBeEmpty();

            response.Errors.Any(x => x.ErrorMessage == "Solicitação não encontrada.").ShouldBeTrue();

            _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoMemoryRepository>().Verify(
                repository => repository.GetList(It.IsAny<Expression<Func<Entities.Entities.Solicitacao, bool>>>()),
                Times.Once);

            _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoFirebaseRepository>().Verify(
                repository => repository.PatchFlattenAsync(It.IsAny<ChatDeSolicitacaoFirebaseDto>(), It.IsAny<CancellationToken>()),
                Times.Never);

            var enviarMensagemChatSolicitacaoNoFirebaseCommandPublished =
                await _dependencyInjectorFactory.Harness.Published.Any<EnviarMensagemChatSolicitacaoNoFirebaseCommand>();

            enviarMensagemChatSolicitacaoNoFirebaseCommandPublished.ShouldBeFalse();
        }
    }
}
