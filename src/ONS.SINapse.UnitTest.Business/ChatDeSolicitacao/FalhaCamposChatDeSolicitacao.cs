using Microsoft.Extensions.DependencyInjection;
using ONS.SINapse.Repository.IRepository.Firebase;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands.Firebase;

namespace ONS.SINapse.UnitTest.Business.ChatDeSolicitacao;

public partial class ChatDeSolicitacaoTest
{
    [Fact(DisplayName = "[Chat Solicitação] - Deve retornar erro quando mensagem não for informada")]
    [Trait("Business", "Chat de Solicitacao")]
    public async Task ChatSolicitacao_DeveRetornarErro_MensagemNaoInformada()
    {
        // Arrange
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoPendente();
        var origem = solicitacao.Origem.Codigo;

        ConfigurarMockUserContext(origem);

   _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoFirebaseRepository>()
            .Setup(repository => repository.GetByIdAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(solicitacao);

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        var command = new ChatDeSolicitacaoCommand(solicitacao.Id, "Teste chat Solicitação")
        {
            Mensagem = string.Empty
        };

        // Act
        var response = await mediator.EnviarComandoAsync(command, CancellationToken.None);

        // Assert
        response.ShouldNotBeNull();
        response.IsValid.ShouldBeFalse(response.ToString());
        response.Errors.ShouldNotBeEmpty();

        response.Errors.Any(x => x.ErrorMessage == "Mensagem não informada.").ShouldBeTrue();

        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoFirebaseRepository>().Verify(
            repository => repository.GetByIdAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()),
            Times.Once);

        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoFirebaseRepository>().Verify(
            repository => repository.AddUpdateAsync(It.IsAny<Dictionary<string, object?>>(), It.IsAny<CancellationToken>()),
            Times.Never);

        var criarMensagemNoChatDeSolicitacaoFirebaseCommandPublished =
            await _dependencyInjectorFactory.Harness.Published.Any<CriarMensagemNoChatDeSolicitacaoFirebaseCommand>();

        criarMensagemNoChatDeSolicitacaoFirebaseCommandPublished.ShouldBeFalse();
    }
}
