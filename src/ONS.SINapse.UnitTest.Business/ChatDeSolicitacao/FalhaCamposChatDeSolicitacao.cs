using Microsoft.Extensions.DependencyInjection;
using ONS.SINapse.Repository.IRepository;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using System.Linq.Expressions;

namespace ONS.SINapse.UnitTest.Business.ChatDeSolicitacao;

public partial class ChatDeSolicitacaoTest
{
    [Fact(DisplayName = "[Chat Solicitação] - Deve retornar erro quando mensagem não for informada")]
    [Trait("Business", "Chat de Solicitacao")]
    public async Task ChatSolicitacao_DeveRetornarErro_MensagemNaoInformada()
    {
        // Arrange
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoPendente();
        var origem = solicitacao.Origem.Codigo;

        ConfigurarMockUserContext(origem);

        _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoRepository>()
            .Setup(repository => repository.GetAsync(It.IsAny<Expression<Func<Entities.Entities.Solicitacao, bool>>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([solicitacao]);

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        var command = new ChatDeSolicitacaoCommand(solicitacao.Id, "Teste chat Solicitação")
        {
            Mensagem = string.Empty
        };

        // Act
        var response = await mediator.EnviarComandoAsync<ChatDeSolicitacaoCommand>(command, CancellationToken.None);

        // Assert
        response.ShouldNotBeNull();
        response.IsValid.ShouldBeFalse(response.ToString());
        response.Errors.ShouldNotBeEmpty();

        response.Errors.Any(x => x.ErrorMessage == "Mensagem não informada.").ShouldBeTrue();

        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoRepository>().Verify(
            repository => repository.GetAsync(It.IsAny<Expression<Func<Entities.Entities.Solicitacao, bool>>>(), It.IsAny<CancellationToken>()),
            Times.Once);

        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoRepository>().Verify(
            repository => repository.AtualizarAsync(It.IsAny<Entities.Entities.Solicitacao>(), It.IsAny<CancellationToken>()),
            Times.Never);

        var enviarMensagemChatSolicitacaoNoFirebaseCommandPublished =
            await _dependencyInjectorFactory.Harness.Published.Any<EnviarMensagemChatSolicitacaoNoFirebaseCommand>();

        enviarMensagemChatSolicitacaoNoFirebaseCommandPublished.ShouldBeFalse();
    }
}
