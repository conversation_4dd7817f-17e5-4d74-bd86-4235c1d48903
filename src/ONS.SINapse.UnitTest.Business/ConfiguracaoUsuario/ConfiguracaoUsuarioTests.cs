using ONS.SINapse.Business.Imp.Business.Firebase;
using ONS.SINapse.Entities.Entities;
using ONS.SINapse.Repository.IRepository;
using ONS.SINapse.Repository.IRepository.Firebase;
using ONS.SINapse.Shared.Notifications;
using ONS.SINapse.UnitTest.Shared.Fixtures;

namespace ONS.SINapse.UnitTest.Business.ConfiguracaoUsuarioTest;

public partial class ConfiguracaoUsuarioTests
{
    [Fact(DisplayName = "[Adicionar Configuracao] - Deve adicionar configuração quando válida")]
    [Trait("Business", "Configuracao de Usuario")]
    public async Task DeveAdicionarConfiguracaoQuandoValida()
    {
        // Arrange
        
        var configuracao = NotificacaoConfiguracaoFixture.GerarNotificacaoConfiguracaoValida();

        // Act

        await _dependencyInjectorFactory.Mocker.CreateInstance<ConfiguracaoUsuarioBusiness>().AdicionarConfiguracaoNotificacaoAsync(configuracao, CancellationToken.None);

        // Assert

        _dependencyInjectorFactory.Mocker.GetMock<IConfiguracaoUsuarioRepository>().Verify(
            repository => repository.AddUpdateAsync(It.IsAny<ConfiguracaoUsuario>(), It.IsAny<string>(), It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact(DisplayName = "[Adicionar Configuracao] - Não deve adicionar configuração lista vazia")]
    [Trait("Business", "Configuracao de Usuario")]
    public async Task NaoDeveAdicionarConfiguracaoQuandoListaVazia()
    {
        // Arrange

        var configuracao = NotificacaoConfiguracaoFixture.GerarNotificacaoConfiguracaoComListaVazia();

        // Act

        await _dependencyInjectorFactory.Mocker.CreateInstance<ConfiguracaoUsuarioBusiness>().AdicionarConfiguracaoNotificacaoAsync(configuracao, CancellationToken.None);

        // Assert

        _dependencyInjectorFactory.Mocker.GetMock<IConfiguracaoUsuarioRepository>().Verify(
            repository => repository.AddUpdateAsync(It.IsAny<ConfiguracaoUsuario>(), It.IsAny<string>(), It.IsAny<CancellationToken>()),
            Times.Never);
    }

    [Fact(DisplayName = "[Adicionar Configuracao] - Não deve adicionar configuração quando inválida")]
    [Trait("Business", "Configuracao de Usuario")]
    public async Task NaoDeveAdicionarConfiguracaoQuandoInvalida()
    {
        // Arrange

        var configuracao = NotificacaoConfiguracaoFixture.GerarNotificacaoConfiguracaoInvalida();

        // Act

        await _dependencyInjectorFactory.Mocker.CreateInstance<ConfiguracaoUsuarioBusiness>().AdicionarConfiguracaoNotificacaoAsync(configuracao, CancellationToken.None);

        // Assert

        _dependencyInjectorFactory.Mocker.GetMock<IConfiguracaoUsuarioRepository>().Verify(
            repository => repository.AddUpdateAsync(It.IsAny<ConfiguracaoUsuario>(), It.IsAny<string>(), It.IsAny<CancellationToken>()),
            Times.Never);
    }
}
