using ONS.SINapse.UnitTest.Business.Fixtures.Collections;
using ONS.SINapse.UnitTest.Shared;
using ONS.SINapse.UnitTest.Shared.Fixtures;

namespace ONS.SINapse.UnitTest.Business.ConfiguracaoUsuarioTest;

[Collection(nameof(ConfiguracaoUsuarioCollection))]
public partial class ConfiguracaoUsuarioTests
{
    private readonly MockDependencyInjectorFactory _dependencyInjectorFactory;
    private readonly NotificacaoConfiguracaoFixture _notificacaoConfiguracaoFixture;

    public ConfiguracaoUsuarioTests(NotificacaoConfiguracaoFixture notificacaoConfiguracaoFixture)
    {
        _dependencyInjectorFactory = new MockDependencyInjectorFactory();
        _dependencyInjectorFactory.RegisterMocks();
        _notificacaoConfiguracaoFixture = notificacaoConfiguracaoFixture;
    }
}
