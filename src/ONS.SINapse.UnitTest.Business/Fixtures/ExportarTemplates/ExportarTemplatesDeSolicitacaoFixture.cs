using Microsoft.Extensions.Options;
using Moq.AutoMock;
using ONS.SINapse.Shared.Settings;

namespace ONS.SINapse.UnitTest.Business.Fixtures.ExportarTemplates;

[CollectionDefinition(nameof(ExportarTemplatesDeSolicitacaoCollection))]
public class ExportarTemplatesDeSolicitacaoCollection : ICollectionFixture<ExportarTemplatesDeSolicitacaoFixture>;


[Collection(nameof(ExportarTemplatesDeSolicitacaoCollection))]
public class ExportarTemplatesDeSolicitacaoFixture
{
    public AutoMocker Mocker;

    public ExportarTemplatesDeSolicitacaoFixture()
    {
        Mocker = new AutoMocker();
    }

    public void ResetAutoMocker() => Mocker = new AutoMocker();

    public IOptions<TemplatesSettings> ObterTemplatesSettings(string pastaTemplates)
    {
        var settings = new TemplatesSettings
        {
            DiretorioDosTemplates = pastaTemplates,
            NomeDoArquivoDeDicionario = string.Empty
        };

        var options = Options.Create(settings);
        
        Mocker.Use(options);
        
        return Mocker.Get<IOptions<TemplatesSettings>>();
    }
}

