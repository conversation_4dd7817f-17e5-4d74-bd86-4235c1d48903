using FluentValidation.Results;
using ONS.SINapse.Entities.Entities;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Solicitacao.EventHandlers.Events;
using ONS.SINapse.Solicitacao.EventHandlers.IntegrationEvents;

namespace ONS.SINapse.UnitTest.Business.Fixtures.Solicitacao;

public class SolicitacaoEventsFixture
{
    public static SolicitacoesCadastradasEmLoteEvent CriarSolicitacaoCadastradaEvent(Entities.Entities.Solicitacao solicitacao) =>
        new([
            new SolicitacaoCadastradasEmLoteDto(
                solicitacao.Id,
                solicitacao.Origem.Codigo,
                solicitacao.Mensagem,
                Convert(solicitacao.UsuarioDeCriacao),
                solicitacao.Destino.Codigo,
                Convert(solicitacao.Status)
            )
            {
                Agente = solicitacao.EncaminharPara?.Codigo,
                Local = solicitacao.Local?.Codigo,
                CreatedAt = solicitacao.CreatedAt,
                UpdatedAt = solicitacao.UpdatedAt,
                SistemaDeOrigem = solicitacao.SistemaDeOrigem
            }
        ]);

    public static SolicitacaoImpedidaEvent CriarSolicitacaoImpedidaEvent(Entities.Entities.Solicitacao solicitacao)
    {
        var usuario = solicitacao.GetUsuarioDoImpedimento();
        var solicitacaoImpedidaDto = new SolicitacaoImpedidaDto(
            solicitacao.Origem.Codigo,
            solicitacao.Id,
            solicitacao.Local?.Codigo,
            solicitacao.CreatedAt,
            solicitacao.UpdatedAt,
            solicitacao.Mensagem,
            new UsuarioDto(usuario!.Sid, usuario.Nome, usuario.Login),
            solicitacao.Status
        );
        
        return new SolicitacaoImpedidaEvent([solicitacaoImpedidaDto]);
    }

    public static SolicitacaoFinalizadaEmLoteEvent CriarSolicitacaoFinalizadaEmLoteEvent(
        List<Entities.Entities.Solicitacao> solicitacoes)
    {
        var solicitacoesFinalizadasDto = solicitacoes
            .Select(solicitacao => solicitacao.Id)
            .ToList();
        
        return new SolicitacaoFinalizadaEmLoteEvent(solicitacoesFinalizadasDto);
    }

    public static SolicitacoesCanceladasEmLoteEvent CriarSolicitacaoCanceladaEvent(
        List<Entities.Entities.Solicitacao> solicitacoes)
    {
        var solicitacoesCanceladasDto = solicitacoes
            .Select(solicitacao => solicitacao.Id);
        
        return new SolicitacoesCanceladasEmLoteEvent(solicitacoesCanceladasDto.ToList());
    }

    public static SolicitacaoFinalizadaAutomaticamenteEvent CriarSolicitacaoFinalizadaAutomaticamenteEvent(
        ValidationResult validationResult) => new(validationResult);
    

    public static CadastroDeSolicitacaoExternaRecebidaIntegrationEvent CriarCadastroDeSolicitacaoExternaRecebidaIntegrationEvent(
        ObjetoDeManobraDto? origem = null,
        ObjetoDeManobraDto? destino = null,
        ObjetoDeManobraDto? local = null,
        ObjetoDeManobraDto? encaminharPara = null,
        string? informacaoAdicional = null,
        string? mensagem = null,
        string? sistemaDeOrigem = null,
        string? codigoExterno = null,
        string? motivo = null,
        List<string>? tags = null
        )
    {
        return new CadastroDeSolicitacaoExternaRecebidaIntegrationEvent
        {
            Solicitacoes =
                [
                    new CadastroDeSolicitacaoExternaDto
                    {
                        Origem = origem ?? new ObjetoDeManobraDto("NE", "COSR-NE"),
                        Destino = destino ?? new ObjetoDeManobraDto("GSU", "ENGIE"),
                        Local = local ?? new ObjetoDeManobraDto("TEST", "Local Teste"),
                        EncaminharPara = encaminharPara ?? new ObjetoDeManobraDto("GSU", "ENGIE"),
                        InformacaoAdicional = informacaoAdicional,
                        Mensagem = mensagem ?? "Teste de unidade cadastro de solicitação externa",
                        SistemaDeOrigem = sistemaDeOrigem ?? "EXTERNO",
                        Id = codigoExterno ?? Guid.NewGuid().ToString(),
                        Motivo = motivo ?? "Teste de unidade cadastro de solicitação externa",
                        Tags = tags ?? [],
                        Usuario = new UsuarioDto("S-SID-USUARIO-TESTE", "<EMAIL>", "Usuario Teste")
                    }
                ]
        };
    }
    
    
    #region private Converters Methods 

    private static UsuarioDto Convert(Usuario usuario) => new(usuario.Sid, usuario.Nome, usuario.Login);

    private static StatusDeSolicitacaoDto Convert(StatusDeSolicitacao status) => new((short)status, status.GetDescription());

    #endregion
    
}