using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Factories;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using ONS.SINapse.UnitTest.Shared.Fixtures;

namespace ONS.SINapse.UnitTest.Business.Fixtures.Solicitacao.CadastroSolicitacao;

[CollectionDefinition(nameof(CadastrarSolicitacaoCollection))]
public class CadastrarSolicitacaoCollection
    : ICollectionFixture<CadastrarSolicitacaoFixture>,
        ICollectionFixture<SolicitacaoFixture>;


[Collection(nameof(CadastrarSolicitacaoCollection))]
public class CadastrarSolicitacaoFixture
{
    public CadastroSolicitacaoDto ObterCadastroSolicitacaoValido(
            bool withoutOrigem = false,
            bool withoutDestino = false,
            bool withoutMensagem = false
        )
    {
        var origem = new ObjetoDeManobraDto("NE", "CORS-NE");
        var destino = new ObjetoDeManobraDto("GSU", "ENGIE");

        return new CadastroSolicitacaoDto(
            IdSolicitacaoFactory.GerarNovoId(origem.Codigo, destino.Codigo),
            origem,
            destino,
            "Corte de teste de unidade",
            "BARIRI | Limitar geração em 15kv",
            new UsuarioDto("sid-usuario-teste", "Usuário Teste", "<EMAIL>"),
            string.Empty
        )
        {
            Motivo = "Teste",
            Local = new ObjetoDeManobraDto("SPBAR", "BARIRI"),
            EncaminharPara = new ObjetoDeManobraDto("GSU", "ENGIE"),
            Origem = withoutOrigem ? null : new ObjetoDeManobraDto("NE", "CORS-NE"),
            Destino = withoutDestino ? null : new ObjetoDeManobraDto("GSU", "ENGIE"),
            Mensagem = withoutMensagem ? null : "Corte de teste de unidade",
            SistemaDeOrigem = "SINAPSE"
        };
    }

    public CadastroSolicitacaoDto ObterCadastroSolicitacaoUsuarioInvalido()
    {
        var cadastro = ObterCadastroSolicitacaoValido();
        
        return cadastro;
    }

    public CadastroSolicitacaoDto ObterCadastroSolicitacaoMensagemInvalida()
    {
        var cadastro = ObterCadastroSolicitacaoValido(withoutMensagem: true);
        return cadastro;
    }

    public CadastroSolicitacaoDto ObterCadastroSolicitacaoOrigemInvalida()
    {
        var cadastro = ObterCadastroSolicitacaoValido(withoutOrigem: true);
        return cadastro;
    }

    public CadastroSolicitacaoDto ObterCadastroSolicitacaoDestinoInvalido()
    {
        var cadastro = ObterCadastroSolicitacaoValido(withoutDestino: true);
        return cadastro;
    }

    public CadastroSolicitacaoDto ObterCadastroSolicitacaoSistemaDeOrigemInvalido()
    {
        var cadastro = ObterCadastroSolicitacaoValido();
        cadastro.SistemaDeOrigem = null;
        return cadastro;
    }
}