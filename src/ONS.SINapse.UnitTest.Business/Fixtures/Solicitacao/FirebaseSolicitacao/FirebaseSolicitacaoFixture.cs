using ONS.SINapse.Entities.Entities;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Shared.Factories;
using ONS.SINapse.UnitTest.Shared.Fixtures;

namespace ONS.SINapse.UnitTest.Business.Fixtures.Solicitacao.FirebaseSolicitacao;

using Solicitacao = Entities.Entities.Solicitacao;

[CollectionDefinition(nameof(FirebaseSolicitacaoCollection))]
public class FirebaseSolicitacaoCollection
    : ICollectionFixture<FirebaseSolicitacaoFixture>;


[Collection(nameof(FirebaseSolicitacaoCollection))]
public class FirebaseSolicitacaoFixture
{
    private static string DescricaoStatusPendente => StatusDeSolicitacao.Pendente.GetDescription();
    private static StatusDeSolicitacao StatusIdPendente => StatusDeSolicitacao.Pendente;
    private static DateTime DataAtual => DateTime.UtcNow;

    public Solicitacao ObterSolicitacaoCadastroValido()
    {
        var usuario = UsuarioFixture.GerarUsuarioValido();
        var origem = new ObjetoDeManobra("NE", "CORS-NE");
        var destino = new ObjetoDeManobra("CHF", "CHESF");
        var local = new ObjetoDeManobra("PIUBE", "Boa Esperança");
        var encaminharPara = new ObjetoDeManobra("CHF", "CHESF");
        string? informacaoAdicional = null;
        const string mensagem = "Boa Esperança |  Sincronizar 1 UG(s).";
        var loteId = Guid.NewGuid();
        string? motivo = null;
        string? solicitacaoOrigem = null;
        string[] tags = ["Geração", "Elevação"];
        const string sistemaDeOrigem = Solicitacao.SistemaDeOrigemInterno;

        return new Solicitacao(
            IdSolicitacaoFactory.GerarNovoId(origem.Codigo, destino.Codigo),
            usuario,
            origem,
            destino,
            mensagem,
            tags,
            sistemaDeOrigem
        )
        {
            Local = local,
            EncaminharPara = encaminharPara,
            InformacaoAdicional = informacaoAdicional,
            LoteId = loteId.ToString(),
            Motivo = motivo,
            SolicitacaoDeOrigemId = solicitacaoOrigem
        };
    }

public StatusDeSolicitacaoFirebaseDto ObterStatusDeSolicitacaoValido() =>
        new(string.Empty, StatusIdPendente, DataAtual, [], []);

    public StatusDeSolicitacaoFirebaseDto ObterImpedirSolicitacaoValido()
    {
        var dto = new StatusDeSolicitacaoFirebaseDto(string.Empty, StatusIdPendente, DataAtual, [], []);
        dto.AdicionarImpedimento("Teste Unitário");
        return dto;
    }

    public ChatDeSolicitacaoFirebaseDto ObterChatSolicitacaoValido() =>
        new(string.Empty, []);

    private static ObjetoDeManobraDto CriarObjetoDeManobra(string id, string nome) =>
        new(id, nome);
}
