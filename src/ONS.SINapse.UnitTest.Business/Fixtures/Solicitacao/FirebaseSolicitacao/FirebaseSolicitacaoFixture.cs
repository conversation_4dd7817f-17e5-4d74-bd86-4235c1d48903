using ONS.SINapse.Entities.Entities;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Shared.Factories;
using ONS.SINapse.Shared.Identity;
using ONS.SINapse.UnitTest.Shared.Fixtures;

namespace ONS.SINapse.UnitTest.Business.Fixtures.Solicitacao.FirebaseSolicitacao;

using Solicitacao = Entities.Entities.Solicitacao;

[CollectionDefinition(nameof(FirebaseSolicitacaoCollection))]
public class FirebaseSolicitacaoCollection
    : ICollectionFixture<FirebaseSolicitacaoFixture>;


[Collection(nameof(FirebaseSolicitacaoCollection))]
public class FirebaseSolicitacaoFixture
{
    private static string DescricaoStatusPendente => StatusDeSolicitacao.Pendente.GetDescription();
    private static StatusDeSolicitacao StatusIdPendente => StatusDeSolicitacao.Pendente;
    private static DateTime DataAtual => DateTime.UtcNow;

    public Solicitacao ObterSolicitacaoCadastroValido()
    {
        var usuario = UsuarioFixture.GerarUsuarioValido();
        var origem = new ObjetoDeManobra("NE", "CORS-NE");
        var destino = new ObjetoDeManobra("CHF", "CHESF");
        var local = new ObjetoDeManobra("PIUBE", "Boa Esperança");
        var encaminharPara = new ObjetoDeManobra("CHF", "CHESF");
        string? informacaoAdicional = null;
        const string mensagem = "Boa Esperança |  Sincronizar 1 UG(s).";
        var loteId = Guid.NewGuid();
        string? motivo = null;
        string? solicitacaoOrigem = null;
        string[] tags = ["Geração", "Elevação"];
        const string sistemaDeOrigem = Solicitacao.SistemaDeOrigemInterno;

        var solicitacao = new Solicitacao(
            IdSolicitacaoFactory.GerarNovoId(origem.Codigo, destino.Codigo),
            usuario,
            origem,
            destino,
            mensagem,
            tags,
            sistemaDeOrigem
        )
        {
            Local = local,
            EncaminharPara = encaminharPara,
            InformacaoAdicional = informacaoAdicional,
            LoteId = loteId.ToString(),
            Motivo = motivo,
            SolicitacaoDeOrigemId = solicitacaoOrigem
        };
        solicitacao.SetStatusInicial(usuario);
        return solicitacao;
    }
    
    public Solicitacao ObterSolicitacaoConfirmada()
    {
        var solicitacao = ObterSolicitacaoCadastroValido();
        solicitacao.Confirmar(UsuarioFixture.GerarUsuarioValido());
        return solicitacao;
    }
    
    public Solicitacao ObterSolicitacaoImpedida(string motivo = "Motivo para impedir")
    {
        var solicitacao = ObterSolicitacaoCadastroValido();
        solicitacao.Impedir(motivo, UsuarioFixture.GerarUsuarioValido());
        return solicitacao;
    }
    
    public Solicitacao ObterSolicitacaoCienciaInformada()
    {
        var solicitacao = ObterSolicitacaoImpedida();
        solicitacao.InformarCiencia(UsuarioFixture.GerarUsuarioValido());
        return solicitacao;
    }
    
    public Solicitacao ObterSolicitacaoFinalizada()
    {
        var solicitacao = ObterSolicitacaoCienciaInformada();
        solicitacao.Finalizar(UsuarioFixture.GerarUsuarioValido());
        return solicitacao;
    }
    
    public Solicitacao ObterSolicitacaoFinalizadaAutomaticamente()
    {
        var solicitacao = ObterSolicitacaoCienciaInformada();
        solicitacao.FinalizarAutomaticamente(UsuarioFixture.GerarUsuarioValido());
        return solicitacao;
    }
    
    public Solicitacao ObterSolicitacaoCancelada()
    {
        var solicitacao = ObterSolicitacaoCadastroValido();
        solicitacao.Cancelar(UsuarioFixture.GerarUsuarioValido());
        return solicitacao;
    }
    
    public Solicitacao ObterSolicitacaoEntregue()
    {
        var solicitacao = ObterSolicitacaoCadastroValido();
        solicitacao.ConfirmarEntrega(UsuarioFixture.GerarUsuarioValido(), ObterPerfilDestino(solicitacao));
        return solicitacao;
    }
    
    public Solicitacao ObterSolicitacaoLida()
    {
        var solicitacao = ObterSolicitacaoEntregue();
        solicitacao.ConfirmarLeitura(UsuarioFixture.GerarUsuarioValido(), ObterPerfilDestino(solicitacao));
        return solicitacao;
    }
    
    public Solicitacao ObterSolicitacaoComMensagemNoChat(string mensagem = "Mensagem de Teste")
    {
        var solicitacao = ObterSolicitacaoCadastroValido();
        solicitacao.AdicionarMensagemNoChat(mensagem, UsuarioFixture.GerarUsuarioValido(), ObterPerfilDestino(solicitacao));
        return solicitacao;
    }
    
    public Perfil ObterPerfilDestino(Solicitacao solicitacao, string[]? operacao = null)
    {
        var tipo = solicitacao.Origem.Codigo == "CN" ? "CENTROS" : "AGENTES";
        
        var scope = new Scope(tipo, solicitacao.Destino.Codigo, solicitacao.Destino.Codigo);
        return new Perfil([scope], operacao is null ? [] : operacao.ToList(), solicitacao.Destino.Codigo, solicitacao.Destino.Codigo);
    }
    
    public Perfil ObterPerfilOrigem(Solicitacao solicitacao, string[]? operacao = null)
    {
        var scope = new Scope("CENTROS", solicitacao.Origem.Codigo, solicitacao.Origem.Codigo);
        return new Perfil([scope], operacao is null ? [] : operacao.ToList(), solicitacao.Origem.Codigo, solicitacao.Origem.Codigo);
    }
}
