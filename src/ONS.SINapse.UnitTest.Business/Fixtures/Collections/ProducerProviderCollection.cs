using ONS.SINapse.Shared.Settings;

namespace ONS.SINapse.UnitTest.Business.Fixtures.Collections;

[CollectionDefinition(nameof(ProducerProviderCollection))]
public class ProducerProviderCollection : ICollectionFixture<ProducerProviderFixture>;

[Collection(nameof(ProducerProviderCollection))]
public class ProducerProviderFixture
{
    public KafkaSettings ObterKafkaSettingsValido() => new KafkaSettings
    {
        Ativo = true,
        BootstrapServers = "localhost:9092",
        QuantidadeDeWorkersPorInstancia = 1,
        RequireAuth = false,
        SecurityProtocol = "PLAINTEXT",
        SslCaPem = string.Empty,
        SslKeystorePassword = string.Empty,
        SaslMechanism = string.Empty,
        SaslUsername = string.Empty,
        SaslPassword = string.Empty,
        ConsumerName = "TestConsumer"
    };

    public KafkaSettings ObterKafkaSettingsInativo() => new KafkaSettings
    {
        Ativo = false,
        BootstrapServers = "localhost:9092",
        QuantidadeDeWorkersPorInstancia = 1,
        RequireAuth = false,
        SecurityProtocol = "PLAINTEXT",
        SslCaPem = string.Empty,
        SslKeystorePassword = string.Empty,
        SaslMechanism = string.Empty,
        SaslUsername = string.Empty,
        SaslPassword = string.Empty,
        ConsumerName = "TestConsumer"
    };
}
