<Project Sdk="Microsoft.NET.Sdk">

    <!-- <PERSON><PERSON><PERSON>ades específicas do projeto de teste -->
    <PropertyGroup>
        <IsPackable>false</IsPackable>
        <IsTestProject>true</IsTestProject>
    </PropertyGroup>

    <!-- Pacotes de teste -->
    <ItemGroup>
        <PackageReference Include="coverlet.collector" />
        <PackageReference Include="Microsoft.NET.Test.Sdk" />
        <PackageReference Include="MongoDB.Driver" />
        <PackageReference Include="xunit" />
        <PackageReference Include="xunit.runner.visualstudio" />
    </ItemGroup>

    <ItemGroup>
        <Using Include="Xunit" />
        <Using Include="Moq" />
        <Using Include="Shouldly" />
    </ItemGroup>
    <ItemGroup>
        <Content Include="ExportarTemplates\templates\**\*">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </Content>
    </ItemGroup>
    <ItemGroup>
      <ProjectReference Include="..\ONS.SINapse.Repository\ONS.SINapse.Repository.csproj" />
      <ProjectReference Include="..\ONS.SINapse.Solicitacao\ONS.SINapse.Solicitacao.csproj" />
      <ProjectReference Include="..\ONS.SINapse.UnitTest.Domain\ONS.SINapse.UnitTest.Domain.csproj" />
    </ItemGroup>
</Project>
