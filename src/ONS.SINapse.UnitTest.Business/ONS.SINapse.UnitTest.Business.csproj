<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>

        <IsPackable>false</IsPackable>
        <IsTestProject>true</IsTestProject>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="coverlet.collector" Version="6.0.0" />
        <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.8.0" />
        <PackageReference Include="xunit" Version="2.9.3" />
        <PackageReference Include="xunit.runner.visualstudio" Version="2.9.3" />
    </ItemGroup>

    <ItemGroup>
        <Using Include="Xunit" />
        <Using Include="Moq" />
        <Using Include="Shouldly" />
    </ItemGroup>
    <ItemGroup>
        <Content Include="ExportarTemplates\templates\**\*">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </Content>
    </ItemGroup>
    <ItemGroup>
      <ProjectReference Include="..\ONS.SINapse.Repository\ONS.SINapse.Repository.csproj" />
      <ProjectReference Include="..\ONS.SINapse.Solicitacao\ONS.SINapse.Solicitacao.csproj" />
      <ProjectReference Include="..\ONS.SINapse.UnitTest.Domain\ONS.SINapse.UnitTest.Domain.csproj" />
    </ItemGroup>
</Project>
