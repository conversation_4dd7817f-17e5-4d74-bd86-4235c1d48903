using ONS.SINapse.Entities.Entities.DadosCadastrais;
using ONS.SINapse.Repository.IRepository.DadosCadastrais;
using ONS.SINapse.Shared.DTO.Firebase;
using ONS.SINapse.Shared.Identity;
using ONS.SINapse.Shared.Services.Firebase;
using ONS.SINapse.UnitTest.Business.Fixtures.Collections;
using ONS.SINapse.UnitTest.Shared;
using ONS.SINapse.UnitTest.Shared.Fixtures;
using System.Linq.Expressions;

namespace ONS.SINapse.UnitTest.Business.VisaoDeUsuario;

[Collection(nameof(VisaoDeUsuarioCollection))]
public partial class VisaoDeUsuarioBusinessTest
{
    private readonly MockDependencyInjectorFactory _dependencyInjectorFactory;
    private readonly VisaoDeUsuarioFixture _visaoDeUsuarioFixture;
    private readonly AgenteFixture _agenteFixture;
    private readonly PerfilFixture _perfilFixture;

    public VisaoDeUsuarioBusinessTest(VisaoDeUsuarioFixture visaoDeUsuarioFixture, AgenteFixture agenteFixture, PerfilFixture perfilFixture)
    {
        _dependencyInjectorFactory = new MockDependencyInjectorFactory();
        _dependencyInjectorFactory.RegisterMocks();

        _visaoDeUsuarioFixture = visaoDeUsuarioFixture;
        _agenteFixture = agenteFixture;
        _perfilFixture = perfilFixture;
    }

    private void ConfigurarMocksVisao(bool visoesEmUso = false, bool centroNoPerfil = true)
    {
        _dependencyInjectorFactory.Mocker.GetMock<IVisaoUsuarioFirebaseDatabaseService>()
            .Setup(s => s.VisoesEmUsoAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(visoesEmUso
                ? new Dictionary<string, VisaoUsuarioRealtimeDto> { { "id2", CriarVisaoUsuarioRealtimeDtoMock() } }
                : new Dictionary<string, VisaoUsuarioRealtimeDto>());

        _dependencyInjectorFactory.Mocker.GetMock<IUserContext>()
            .SetupGet(u => u.Perfil)
            .Returns(_perfilFixture.GerarPerfilValido(
                centroNoPerfil ? [new Scope("NE", "NE", "NE"), new Scope("SE", "SE", "SE")] : [],
                [],
                "NE",
                "NE"
            ));

        _dependencyInjectorFactory.Mocker.GetMock<IUserContext>()
            .SetupGet(u => u.Centros)
            .Returns(centroNoPerfil ? ["NE", "SE"] : []);

        _dependencyInjectorFactory.Mocker.GetMock<IAgenteRepository>()
            .Setup(r => r.GetOneAsync(It.IsAny<Expression<Func<Agente, bool>>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_agenteFixture.GerarAgente("NE"));
    }

    private static VisaoUsuarioRealtimeDto CriarVisaoUsuarioRealtimeDtoMock()
    {
        return new VisaoUsuarioRealtimeDto(
            sid: "TestSid",
            id: "id1",
            nome: "VisaoTeste",
            codigoCentroAgente: "NE",
            nomeCentroAgente: "Centro NE",
            equipamentosDeManobra: new List<EquipamentoVisaoUsuarioRealtimeDto>
            {
            new EquipamentoVisaoUsuarioRealtimeDto("EQ1", "Equipamento 1", new TipoDeEquipamentoVisaoUsuarioRealtimeDto("TIPO1", "Tipo 1"))
            },
            tags: new List<EquipamentoVisaoUsuarioRealtimeDto>
            {
            new EquipamentoVisaoUsuarioRealtimeDto("TAG1", "Tag 1", new TipoDeEquipamentoVisaoUsuarioRealtimeDto("TIPO2", "Tipo 2"))
            }
        );
    }

}
