using ONS.SINapse.Business.Imp.Business;
using ONS.SINapse.Shared.Notifications;
using ONS.SINapse.Shared.Services.Firebase;
using ONS.SINapse.UnitTest.Shared.Fixtures;

namespace ONS.SINapse.UnitTest.Business.VisaoDeUsuario;

public partial class VisaoDeUsuarioBusinessTest
{
    [Fact(DisplayName = "[VisaoDeUsuarioBusiness] - Deve adicionar visão válida")]
    [Trait("Business", "VisaoDeUsuarioBusiness")]
    public async Task AddAsync_DeveAdicionarVisaoValida()
    {
        var visao = VisaoDeUsuarioFixture.GerarVisaoValida();
        ConfigurarMocksVisao(visoesEmUso: false, centroNoPerfil: true);

        var sut = _dependencyInjectorFactory.Mocker.CreateInstance<VisaoDeUsuarioBusiness>();

        var result = await sut.AddAsync(visao, CancellationToken.None);

        Assert.Equal(visao, result);
        Assert.Empty(_dependencyInjectorFactory.Mocker.Get<NotificationContext>().Notifications);
    }

    [Fact(DisplayName = "[VisaoDeUsuarioBusiness] - Não deve adicionar visão com nome em uso")]
    [Trait("Business", "VisaoDeUsuarioBusiness")]
    public async Task AddAsync_DeveNotificarSeNomeEmUso()
    {
        var visao = VisaoDeUsuarioFixture.GerarVisaoComNomeEmUso();
        ConfigurarMocksVisao(visoesEmUso: true, centroNoPerfil: true);

        var sut = _dependencyInjectorFactory.Mocker.CreateInstance<VisaoDeUsuarioBusiness>();

        var result = await sut.AddAsync(visao, CancellationToken.None);

        Assert.Equal(visao, result);
        Assert.Contains(_dependencyInjectorFactory.Mocker.Get<NotificationContext>().Notifications,
            n => n.Message.Contains("Nome da visão já está em uso"));
    }

    [Fact(DisplayName = "[VisaoDeUsuarioBusiness] - Não deve adicionar visão se centro não pertence ao perfil")]
    [Trait("Business", "VisaoDeUsuarioBusiness")]
    public async Task AddAsync_DeveNotificarSeCentroNaoPertence()
    {
        var visao = VisaoDeUsuarioFixture.GerarVisaoValida();
        ConfigurarMocksVisao(visoesEmUso: false, centroNoPerfil: false);

        var sut = _dependencyInjectorFactory.Mocker.CreateInstance<VisaoDeUsuarioBusiness>();

        var result = await sut.AddAsync(visao, CancellationToken.None);

        Assert.Equal(visao, result);
        Assert.Contains(_dependencyInjectorFactory.Mocker.Get<NotificationContext>().Notifications,
            n => n.Message.Contains("Centro agente da visão não pertence ao perfil"));
    }

    [Fact(DisplayName = "[VisaoDeUsuarioBusiness] - Não deve adicionar visão inválida")]
    [Trait("Business", "VisaoDeUsuarioBusiness")]
    public async Task AddAsync_DeveNotificarSeVisaoInvalida()
    {
        var visao = VisaoDeUsuarioFixture.GerarVisaoSemEquipamentosETags();
        ConfigurarMocksVisao();

        var sut = _dependencyInjectorFactory.Mocker.CreateInstance<VisaoDeUsuarioBusiness>();

        var result = await sut.AddAsync(visao, CancellationToken.None);

        Assert.Equal(visao, result);
        Assert.Contains(_dependencyInjectorFactory.Mocker.Get<NotificationContext>().Notifications,
            n => n.Message.Contains("Informe pelo menos um, tag ou equipamento de manobra"));
    }

    [Fact(DisplayName = "[VisaoDeUsuarioBusiness] - Deve atualizar visão válida")]
    [Trait("Business", "VisaoDeUsuarioBusiness")]
    public async Task UpdateAsync_DeveAtualizarVisaoValida()
    {
        var visao = VisaoDeUsuarioFixture.GerarVisaoValida();
        ConfigurarMocksVisao(visoesEmUso: false, centroNoPerfil: true);

        var sut = _dependencyInjectorFactory.Mocker.CreateInstance<VisaoDeUsuarioBusiness>();

        var result = await sut.UpdateAsync("id1", "NE", visao, CancellationToken.None);

        Assert.Equal(visao, result);
        Assert.Empty(_dependencyInjectorFactory.Mocker.Get<NotificationContext>().Notifications);
    }

    [Fact(DisplayName = "[VisaoDeUsuarioBusiness] - Deve atualizar e deletar visão antiga se centro mudou")]
    [Trait("Business", "VisaoDeUsuarioBusiness")]
    public async Task UpdateAsync_DeveDeletarSeCentroDiferente()
    {
        var visao = VisaoDeUsuarioFixture.GerarVisaoValida("SE");
        ConfigurarMocksVisao(visoesEmUso: false, centroNoPerfil: true);

        var sut = _dependencyInjectorFactory.Mocker.CreateInstance<VisaoDeUsuarioBusiness>();

        var result = await sut.UpdateAsync("id1", "NE", visao, CancellationToken.None);

        Assert.Equal(visao, result);
        _dependencyInjectorFactory.Mocker.GetMock<IVisaoUsuarioFirebaseDatabaseService>()
            .Verify(s => s.DeleteAsync("id1", "NE", It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact(DisplayName = "[VisaoDeUsuarioBusiness] - Não deve atualizar se nome em uso")]
    [Trait("Business", "VisaoDeUsuarioBusiness")]
    public async Task UpdateAsync_DeveNotificarSeNomeEmUso()
    {
        var visao = VisaoDeUsuarioFixture.GerarVisaoComNomeEmUso();
        ConfigurarMocksVisao(visoesEmUso: true, centroNoPerfil: true);

        var sut = _dependencyInjectorFactory.Mocker.CreateInstance<VisaoDeUsuarioBusiness>();

        var result = await sut.UpdateAsync("id1", "NE", visao, CancellationToken.None);

        Assert.Equal(visao, result);
        Assert.Contains(_dependencyInjectorFactory.Mocker.Get<NotificationContext>().Notifications,
            n => n.Message.Contains("Nome da visão já está em uso"));
    }

    [Fact(DisplayName = "[VisaoDeUsuarioBusiness] - Não deve atualizar se centro não pertence ao perfil")]
    [Trait("Business", "VisaoDeUsuarioBusiness")]
    public async Task UpdateAsync_DeveNotificarSeCentroNaoPertence()
    {
        var visao = VisaoDeUsuarioFixture.GerarVisaoValida();
        ConfigurarMocksVisao(visoesEmUso: false, centroNoPerfil: false);

        var sut = _dependencyInjectorFactory.Mocker.CreateInstance<VisaoDeUsuarioBusiness>();

        var result = await sut.UpdateAsync("id1", "NE", visao, CancellationToken.None);

        Assert.Equal(visao, result);
        Assert.Contains(_dependencyInjectorFactory.Mocker.Get<NotificationContext>().Notifications,
            n => n.Message.Contains("Centro agente da visão não pertence ao perfil"));
    }

    [Fact(DisplayName = "[VisaoDeUsuarioBusiness] - Não deve atualizar visão inválida")]
    [Trait("Business", "VisaoDeUsuarioBusiness")]
    public async Task UpdateAsync_DeveNotificarSeVisaoInvalida()
    {
        var visao = VisaoDeUsuarioFixture.GerarVisaoSemEquipamentosETags();
        ConfigurarMocksVisao();

        var sut = _dependencyInjectorFactory.Mocker.CreateInstance<VisaoDeUsuarioBusiness>();

        var result = await sut.UpdateAsync("id1", "NE", visao, CancellationToken.None);

        Assert.Equal(visao, result);
        Assert.Contains(_dependencyInjectorFactory.Mocker.Get<NotificationContext>().Notifications,
            n => n.Message.Contains("Informe pelo menos um, tag ou equipamento de manobra"));
    }

    [Fact(DisplayName = "[VisaoDeUsuarioBusiness] - Deve deletar visão quando centro pertence")]
    [Trait("Business", "VisaoDeUsuarioBusiness")]
    public async Task DeleteAsync_DeveDeletarComSucesso()
    {
        ConfigurarMocksVisao(centroNoPerfil: true);

        var sut = _dependencyInjectorFactory.Mocker.CreateInstance<VisaoDeUsuarioBusiness>();

        await sut.DeleteAsync("id1", "NE", CancellationToken.None);

        _dependencyInjectorFactory.Mocker.GetMock<IVisaoUsuarioFirebaseDatabaseService>()
            .Verify(s => s.DeleteAsync("id1", "NE", It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact(DisplayName = "[VisaoDeUsuarioBusiness] - Não deve deletar se centro não pertence ao perfil")]
    [Trait("Business", "VisaoDeUsuarioBusiness")]
    public async Task DeleteAsync_DeveNotificarSeCentroNaoPertence()
    {
        ConfigurarMocksVisao(centroNoPerfil: false);

        var sut = _dependencyInjectorFactory.Mocker.CreateInstance<VisaoDeUsuarioBusiness>();

        await sut.DeleteAsync("id1", "NE", CancellationToken.None);

        Assert.Contains(_dependencyInjectorFactory.Mocker.Get<NotificationContext>().Notifications,
            n => n.Message.Contains("Centro agente da visão não pertence ao perfil"));
    }
}
