using ONS.SINapse.UnitTest.Business.Fixtures.Collections;
using ONS.SINapse.UnitTest.Shared;

namespace ONS.SINapse.UnitTest.Business.DistributedCache;

[Collection(nameof(DistributedCacheCollection))]
public partial class DistributedCacheServiceTest
{
    private readonly MockDependencyInjectorFactory _dependencyInjectorFactory;

    public DistributedCacheServiceTest()
    {
        _dependencyInjectorFactory = new MockDependencyInjectorFactory();
        _dependencyInjectorFactory.RegisterMocks();
    }
}
