using Microsoft.Extensions.Caching.Distributed;
using Newtonsoft.Json;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Services.Caching;
using System.Text;

namespace ONS.SINapse.UnitTest.Business.DistributedCache;

public partial class DistributedCacheServiceTest
{
    [Fact(DisplayName = "[Cache Service] - Deve retornar valor quando chave existe no cache")]
    [Trait("Business", "Cache Service")]
    public async Task GetAsync_ChaveExiste_DeveRetornarValor()
    {
        // Arrange
        var key = "test-key";
        var expected = new ObjetoDeManobraDto("NE", "CORS-NE");
        var serialized = JsonConvert.SerializeObject(expected);

        _dependencyInjectorFactory.Mocker
            .GetMock<IDistributedCache>()
            .Setup(x => x.GetAsync(key, It.IsAny<CancellationToken>()))
            .ReturnsAsync(Encoding.UTF8.GetBytes(serialized));

        var cacheService = new DistributedCacheService(_dependencyInjectorFactory.Mocker.GetMock<IDistributedCache>().Object);

        // Act
        var result = await cacheService.GetAsync<ObjetoDeManobraDto>(key);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(expected.Codigo, result!.Codigo);
        Assert.Equal(expected.Nome, result.Nome);
    }

    [Fact(DisplayName = "[Cache Service] - Deve retornar null quando chave não existe no cache")]
    [Trait("Business", "Cache Service")]
    public async Task GetAsync_ChaveNaoExiste_DeveRetornarNull()
    {
        // Arrange
        var key = "chave-inexistente";

        _dependencyInjectorFactory.Mocker
            .GetMock<IDistributedCache>()
            .Setup(x => x.GetAsync(key, It.IsAny<CancellationToken>()))
            .ReturnsAsync((byte[]?)null);

        var cacheService = new DistributedCacheService(_dependencyInjectorFactory.Mocker.GetMock<IDistributedCache>().Object);

        // Act
        var result = await cacheService.GetAsync<ObjetoDeManobraDto>(key);

        // Assert
        Assert.Null(result);
    }

    [Fact(DisplayName = "[Cache Service] - Deve armazenar valor no cache com sobrecarga padrão")]
    [Trait("Business", "Cache Service")]
    public async Task SetAsync_ComSobrecargaPadrao_DeveSalvarComSucesso()
    {
        // Arrange
        var key = "cache-padrao";
        var value = new ObjetoDeManobraDto("NE", "CORS-NE");
        var serialized = JsonConvert.SerializeObject(value);

        _dependencyInjectorFactory.Mocker
            .GetMock<IDistributedCache>()
            .Setup(x => x.SetAsync(
                key,
                It.Is<byte[]>(b => Encoding.UTF8.GetString(b) == serialized),
                It.IsAny<DistributedCacheEntryOptions>(),
                It.IsAny<CancellationToken>()
            ))
            .Returns(Task.CompletedTask)
            .Verifiable();

        var cacheService = new DistributedCacheService(_dependencyInjectorFactory.Mocker.GetMock<IDistributedCache>().Object);

        // Act
        await cacheService.SetAsync(key, value);

        // Assert
        _dependencyInjectorFactory.Mocker.GetMock<IDistributedCache>().Verify();
    }

    [Fact(DisplayName = "[Cache Service] - Deve armazenar valor com expiração por TimeSpan")]
    [Trait("Business", "Cache Service")]
    public async Task SetAsync_ComTimeSpan_DeveSalvarComExpiracao()
    {
        // Arrange
        var key = "cache-timespan";
        var value = new ObjetoDeManobraDto("NE", "CORS-NE");
        var expiration = TimeSpan.FromMinutes(30);
        var serialized = JsonConvert.SerializeObject(value);

        _dependencyInjectorFactory.Mocker
            .GetMock<IDistributedCache>()
            .Setup(x => x.SetAsync(
                key,
                It.Is<byte[]>(b => Encoding.UTF8.GetString(b) == serialized),
                It.Is<DistributedCacheEntryOptions>(opt => opt.AbsoluteExpirationRelativeToNow == expiration),
                It.IsAny<CancellationToken>()
            ))
            .Returns(Task.CompletedTask)
            .Verifiable();

        var cacheService = new DistributedCacheService(_dependencyInjectorFactory.Mocker.GetMock<IDistributedCache>().Object);

        // Act
        await cacheService.SetAsync(key, value, expiration);

        // Assert
        _dependencyInjectorFactory.Mocker.GetMock<IDistributedCache>().Verify();
    }

    [Fact(DisplayName = "[Cache Service] - Deve armazenar valor com expiração por DateTimeOffset")]
    [Trait("Business", "Cache Service")]
    public async Task SetAsync_ComDateTimeOffset_DeveSalvarComExpiracao()
    {
        // Arrange
        var key = "cache-datetimeoffset";
        var value = new ObjetoDeManobraDto("NE", "CORS-NE");
        var expiration = DateTimeOffset.UtcNow.AddMinutes(10);
        var serialized = JsonConvert.SerializeObject(value);

        _dependencyInjectorFactory.Mocker
            .GetMock<IDistributedCache>()
            .Setup(x => x.SetAsync(
                key,
                It.Is<byte[]>(b => Encoding.UTF8.GetString(b) == serialized),
                It.Is<DistributedCacheEntryOptions>(opt =>
                    opt.AbsoluteExpiration.HasValue &&
                    opt.AbsoluteExpiration.Value.ToUnixTimeSeconds() == expiration.ToUnixTimeSeconds()),
                It.IsAny<CancellationToken>()
            ))
            .Returns(Task.CompletedTask)
            .Verifiable();

        var cacheService = new DistributedCacheService(_dependencyInjectorFactory.Mocker.GetMock<IDistributedCache>().Object);

        // Act
        await cacheService.SetAsync(key, value, expiration);

        // Assert
        _dependencyInjectorFactory.Mocker.GetMock<IDistributedCache>().Verify();
    }

    [Fact(DisplayName = "[Cache Service] - Deve armazenar valor com expiração por segundos")]
    [Trait("Business", "Cache Service")]
    public async Task SetAsync_ComSegundos_DeveSalvarComExpiracao()
    {
        // Arrange
        var key = "cache-segundos";
        var value = new ObjetoDeManobraDto("NE", "CORS-NE");
        var seconds = 120;
        var expiration = TimeSpan.FromSeconds(seconds);
        var serialized = JsonConvert.SerializeObject(value);

        _dependencyInjectorFactory.Mocker
            .GetMock<IDistributedCache>()
            .Setup(x => x.SetAsync(
                key,
                It.Is<byte[]>(b => Encoding.UTF8.GetString(b) == serialized),
                It.Is<DistributedCacheEntryOptions>(opt => opt.AbsoluteExpirationRelativeToNow == expiration),
                It.IsAny<CancellationToken>()
            ))
            .Returns(Task.CompletedTask)
            .Verifiable();

        var cacheService = new DistributedCacheService(_dependencyInjectorFactory.Mocker.GetMock<IDistributedCache>().Object);

        // Act
        await cacheService.SetAsync(key, value, seconds);

        // Assert
        _dependencyInjectorFactory.Mocker.GetMock<IDistributedCache>().Verify();
    }

    [Fact(DisplayName = "[Cache Service] - Deve remover chaves com prefixo específico")]
    [Trait("Business", "Cache Service")]
    public async Task RemoveByPrefixAsync_DeveRemoverCorretamente()
    {
        // Arrange
        var cacheService = new DistributedCacheService(_dependencyInjectorFactory.Mocker.GetMock<IDistributedCache>().Object);

        await cacheService.SetAsync("obj:1", new ObjetoDeManobraDto("NE", "CORS-NE"));
        await cacheService.SetAsync("obj:2", new ObjetoDeManobraDto("SE", "CORS-SE"));
        await cacheService.SetAsync("obj:3", new ObjetoDeManobraDto("S", "CORS-S"));

        _dependencyInjectorFactory.Mocker.GetMock<IDistributedCache>()
            .Setup(x => x.RemoveAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask)
            .Verifiable();

        // Act
        await cacheService.RemoveByPrefixAsync("obj:");

        // Assert
        _dependencyInjectorFactory.Mocker.GetMock<IDistributedCache>().Verify(x => x.RemoveAsync("obj:1", It.IsAny<CancellationToken>()), Times.Once);
        _dependencyInjectorFactory.Mocker.GetMock<IDistributedCache>().Verify(x => x.RemoveAsync("obj:2", It.IsAny<CancellationToken>()), Times.Once);
        _dependencyInjectorFactory.Mocker.GetMock<IDistributedCache>().Verify(x => x.RemoveAsync("outro:1", It.IsAny<CancellationToken>()), Times.Never);
    }

}
