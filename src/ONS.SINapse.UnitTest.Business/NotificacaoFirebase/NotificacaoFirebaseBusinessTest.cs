using Microsoft.Extensions.Options;
using ONS.SINapse.Shared.DTO.Firebase;
using ONS.SINapse.Shared.Services.Firebase;
using ONS.SINapse.Shared.Settings;
using ONS.SINapse.Business.Imp.Business.Firebase;

namespace ONS.SINapse.UnitTest.Business.NotificacaoFirebase;

public class NotificacaoFirebaseBusinessTest
{
    [Fact(DisplayName = "Deve enviar notificações para o Firebase agrupadas por destino e com expiração definida")]
    public async Task DeveEnviarNotificacoesParaFirebase()
    {
        // Arrange
        var notificacaoServiceMock = new Mock<INotificacaoFirebaseDatabaseService>();
        var config = new ConfiguracaoDeNotificacaoSettings { TempoExpiracaoEmSegundos = 3600, Prefixo = "[ONS]", Sufixo = "-TESTE" };
        var optionsMock = new Mock<IOptions<ConfiguracaoDeNotificacaoSettings>>();
        optionsMock.Setup(x => x.Value).Returns(config);
        var business = new NotificacaoFirebaseBusiness(notificacaoServiceMock.Object, optionsMock.Object);

        var notificacoes = new List<NotificacaoRealtimeDto>
        {
            new("DestinoA", "Título 1", "Corpo 1", "TipoA") { ExpirationDate = 0 },
            new("DestinoA", "Título 2", "Corpo 2", "TipoB") { ExpirationDate = 0 },
            new("DestinoB", "Título 3", "Corpo 3", "TipoA") { ExpirationDate = 0 }
        };

        // Act
        await business.SendAsync(notificacoes, CancellationToken.None);

        // Assert
        notificacaoServiceMock.Verify(x => x.SetAsync(It.Is<Dictionary<string, Dictionary<string, NotificacaoRealtimeDto>>>(collection =>
            collection.Count == 2 &&
            collection.ContainsKey("DestinoA") &&
            collection.ContainsKey("DestinoB") &&
            collection["DestinoA"].Count == 2 &&
            collection["DestinoB"].Count == 1 &&
            collection["DestinoA"].All(pair => pair.Value.ExpirationDate > 0 && pair.Value.Title.StartsWith("[ONS]")) &&
            collection["DestinoA"].All(pair => pair.Value.Title.EndsWith("-TESTE"))
        ), CancellationToken.None), Times.Once);
    }

    [Fact(DisplayName = "Deve deletar todas as notificações do Firebase")]
    public async Task DeveDeletarTodasAsNotificacoes()
    {
        // Arrange
        var notificacaoServiceMock = new Mock<INotificacaoFirebaseDatabaseService>();
        var config = new ConfiguracaoDeNotificacaoSettings { TempoExpiracaoEmSegundos = 3600 };
        var optionsMock = new Mock<IOptions<ConfiguracaoDeNotificacaoSettings>>();
        optionsMock.Setup(x => x.Value).Returns(config);
        var business = new NotificacaoFirebaseBusiness(notificacaoServiceMock.Object, optionsMock.Object);

        // Act
        await business.DeleteAllAsync(CancellationToken.None);

        // Assert
        notificacaoServiceMock.Verify(x => x.DeleteAllAsync(CancellationToken.None), Times.Once);
    }
}
