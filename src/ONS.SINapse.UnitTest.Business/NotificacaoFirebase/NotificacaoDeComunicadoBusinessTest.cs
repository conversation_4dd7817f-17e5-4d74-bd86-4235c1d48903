using ONS.SINapse.Business.IBusiness.Firebase;
using ONS.SINapse.Business.Imp.Business;
using ONS.SINapse.Entities.Entities;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.DTO.Firebase;
using ONS.SINapse.Shared.Enums;

namespace ONS.SINapse.UnitTest.Business.NotificacaoFirebase;

public class NotificacaoDeComunicadoBusinessTest
{
    [Fact(DisplayName = "Deve notificar comunicado para todos quando TipoDeComunicado for Todos")]
    public async Task DeveNotificarComunicadoParaTodos()
    {
        // Arrange
        var firebaseBusinessMock = new Mock<INotificacaoFirebaseBusiness>();
        var business = new NotificacaoDeComunicadoBusiness(firebaseBusinessMock.Object);
        var comunicado = new Comunicado(new Usuario("sid", "login", "nome"))
        {
            Titulo = "Título do Comunicado",
            Origem = new ObjetoDeManobra("COD1", "Origem"),
            TipoDeComunicado = TipoDeComunicado.Todos,
            Mensagem = "Mensagem"
        };

        // Act
        await business.NotificarComunicadoAsync(comunicado, "sid", CancellationToken.None);

        // Assert
        firebaseBusinessMock.Verify(x => x.SendAsync(
            It.Is<IEnumerable<NotificacaoRealtimeDto>>(notificacoes =>
                notificacoes.Count() == 1 &&
                notificacoes.First().Destination == NotificacaoRealtimeDto.TodosDevemSerNotificados &&
                notificacoes.First().Title == "Novo comunicado" &&
                notificacoes.First().Body == comunicado.Titulo &&
                notificacoes.First().Tipo == nameof(Comunicado)
            ),
            CancellationToken.None), Times.Once);
    }

    [Fact(DisplayName = "Deve notificar comunicado para origem e destinatários quando TipoDeComunicado não for Todos")]
    public async Task DeveNotificarComunicadoParaOrigemEDestinatarios()
    {
        // Arrange
        var firebaseBusinessMock = new Mock<INotificacaoFirebaseBusiness>();
        var business = new NotificacaoDeComunicadoBusiness(firebaseBusinessMock.Object);
        var comunicado = new Comunicado(new Usuario("sid", "login", "nome"))
        {
            Titulo = "Título do Comunicado",
            Origem = new ObjetoDeManobra("CN", "ONS"),
            TipoDeComunicado = TipoDeComunicado.CentrosRegionais,
            Mensagem = "Mensagem",
            Destinatarios = new List<Destinatario>
            {
                new("COD2", "Nome2", "Centro2"),
                new("COD3", "Nome3", "Centro3")
            }
        };

        // Act
        await business.NotificarComunicadoAsync(comunicado, "sid", CancellationToken.None);

        // Assert
        firebaseBusinessMock.Verify(x => x.SendAsync(
            It.Is<IEnumerable<NotificacaoRealtimeDto>>(notificacoes =>
                notificacoes.Count() == 3 &&
                notificacoes.Any(n => n.Destination == "CN") &&
                notificacoes.Any(n => n.Destination == "COD2") &&
                notificacoes.Any(n => n.Destination == "COD3") &&
                notificacoes.All(n => n.Title == "Novo comunicado") &&
                notificacoes.All(n => n.Body == comunicado.Titulo) &&
                notificacoes.All(n => n.Tipo == nameof(Comunicado))
            ),
            CancellationToken.None), Times.Once);
    }

    [Fact(DisplayName = "Deve notificar comunicado atualizado quando HasComplemento for true")]
    public async Task DeveNotificarComunicadoAtualizadoQuandoHasComplementoTrue()
    {
        // Arrange
        var firebaseBusinessMock = new Mock<INotificacaoFirebaseBusiness>();
        var business = new NotificacaoDeComunicadoBusiness(firebaseBusinessMock.Object);
        var comunicado = new Comunicado(new Usuario("sid", "login", "nome"))
        {
            Titulo = "Título do Comunicado",
            Origem = new ObjetoDeManobra("CN", "CNOS"),
            TipoDeComunicado = TipoDeComunicado.Todos,
            Mensagem = "Mensagem"
        };
        comunicado.AdicionarComplemento(new ObjetoDeManobraDto("CN", "CNOS"), "Mensagem");

        // Act
        await business.NotificarComunicadoAsync(comunicado, "sid", CancellationToken.None);

        // Assert
        firebaseBusinessMock.Verify(x => x.SendAsync(
            It.Is<IEnumerable<NotificacaoRealtimeDto>>(notificacoes =>
                notificacoes.Count() == 1 &&
                notificacoes.First().Title == "Comunicado atualizado"
            ),
            CancellationToken.None), Times.Once);
    }
}
