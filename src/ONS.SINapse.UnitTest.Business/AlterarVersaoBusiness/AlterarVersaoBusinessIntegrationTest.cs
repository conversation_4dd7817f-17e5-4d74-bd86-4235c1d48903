using AutoMapper;
using ONS.SINapse.Repository.IRepository;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.DTO.Firebase;
using ONS.SINapse.UnitTest.Shared.Fixtures;
using System.Diagnostics;

namespace ONS.SINapse.UnitTest.Business.AlterarVersaoBusiness;

public partial class AlterarVersaoBusinessTest
{
    [Fact(DisplayName = "[AlterarVersaoBusiness] - Deve ter performance adequada com múltiplas operações")]
    [Trait("Business", "AlterarVersaoBusiness")]
    public async Task Performance_MultiplasOperacoes_DeveExecutarRapidamente()
    {
        // Arrange
        var listaAdicionar = AlterarVersaoFixture.GerarListaAdicionarVersao(50);
        var listaEditar = AlterarVersaoFixture.GerarListaEditarVersao(50);
        var cancellationToken = CancellationToken.None;

        foreach (var dto in listaAdicionar)
        {
            var versaoFirebase = AlterarVersaoFixture.GerarVersaoFirebaseDto(dto.Versao, dto.Descricao);
            _dependencyInjectorFactory.Mocker.GetMock<IMapper>()
                .Setup(mapper => mapper.Map<VersaoFirebaseDto>(dto))
                .Returns(versaoFirebase);
        }

        var business = _dependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.AlterarVersaoBusiness>();
        var stopwatch = Stopwatch.StartNew();

        // Act
        var tasks = new List<Task>();
        
        foreach (var dto in listaAdicionar)
        {
            tasks.Add(business.Adicionar(dto, cancellationToken));
        }
        
        foreach (var dto in listaEditar)
        {
            tasks.Add(business.Editar(dto, cancellationToken));
        }
        
        await Task.WhenAll(tasks);
        stopwatch.Stop();

        // Assert
        stopwatch.ElapsedMilliseconds.ShouldBeLessThan(2000); // Deve executar em menos de 2 segundos
        
        _dependencyInjectorFactory.Mocker.GetMock<IVersaoFirebaseRepository>().Verify(
            repo => repo.AddAsync(It.IsAny<VersaoFirebaseDto>(), cancellationToken),
            Times.Exactly(50));

        _dependencyInjectorFactory.Mocker.GetMock<IVersaoFirebaseRepository>().Verify(
            repo => repo.UpdateAsync(It.IsAny<EditarVersaoDto>(), cancellationToken),
            Times.Exactly(50));
    }

    [Fact(DisplayName = "[AlterarVersaoBusiness] - Deve manter consistência durante operações concorrentes")]
    [Trait("Business", "AlterarVersaoBusiness")]
    public async Task OperacoesConcorrentes_DeveManter Consistencia()
    {
        // Arrange
        var adicionarVersaoDto = AlterarVersaoFixture.GerarAdicionarVersaoValido();
        var editarVersaoDto = AlterarVersaoFixture.GerarEditarVersaoValido();
        var versaoFirebaseDto = AlterarVersaoFixture.GerarVersaoFirebaseDto();
        var cancellationToken = CancellationToken.None;

        var chamadasAdicionar = new List<AdicionarVersaoDto>();
        var chamadasEditar = new List<EditarVersaoDto>();

        _dependencyInjectorFactory.Mocker.GetMock<IMapper>()
            .Setup(mapper => mapper.Map<VersaoFirebaseDto>(It.IsAny<AdicionarVersaoDto>()))
            .Callback<AdicionarVersaoDto>(dto => chamadasAdicionar.Add(dto))
            .Returns(versaoFirebaseDto);

        _dependencyInjectorFactory.Mocker.GetMock<IVersaoFirebaseRepository>()
            .Setup(repo => repo.UpdateAsync(It.IsAny<EditarVersaoDto>(), It.IsAny<CancellationToken>()))
            .Callback<EditarVersaoDto, CancellationToken>((dto, token) => chamadasEditar.Add(dto))
            .Returns(Task.CompletedTask);

        var business = _dependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.AlterarVersaoBusiness>();

        // Act
        var tasks = new List<Task>
        {
            business.Adicionar(adicionarVersaoDto, cancellationToken),
            business.Editar(editarVersaoDto, cancellationToken),
            business.Adicionar(adicionarVersaoDto, cancellationToken),
            business.Editar(editarVersaoDto, cancellationToken)
        };

        await Task.WhenAll(tasks);

        // Assert
        chamadasAdicionar.Count.ShouldBe(2);
        chamadasEditar.Count.ShouldBe(2);
        
        chamadasAdicionar.All(dto => dto.Versao == adicionarVersaoDto.Versao).ShouldBeTrue();
        chamadasEditar.All(dto => dto.Descricao == editarVersaoDto.Descricao).ShouldBeTrue();
    }

    [Fact(DisplayName = "[AlterarVersaoBusiness] - Deve validar integridade dos dados no mapeamento")]
    [Trait("Business", "AlterarVersaoBusiness")]
    public async Task ValidarIntegridade_DadosMapeamento_DeveEstarCorretos()
    {
        // Arrange
        var adicionarVersaoDto = AlterarVersaoFixture.GerarAdicionarVersaoValido();
        var cancellationToken = CancellationToken.None;
        VersaoFirebaseDto? dadosCapturados = null;

        _dependencyInjectorFactory.Mocker.GetMock<IMapper>()
            .Setup(mapper => mapper.Map<VersaoFirebaseDto>(adicionarVersaoDto))
            .Returns(() => AlterarVersaoFixture.GerarVersaoFirebaseDto(adicionarVersaoDto.Versao, adicionarVersaoDto.Descricao));

        _dependencyInjectorFactory.Mocker.GetMock<IVersaoFirebaseRepository>()
            .Setup(repo => repo.AddAsync(It.IsAny<VersaoFirebaseDto>(), It.IsAny<CancellationToken>()))
            .Callback<VersaoFirebaseDto, CancellationToken>((dto, token) => 
            {
                dadosCapturados = dto;
            })
            .Returns(Task.CompletedTask);

        // Act
        await _dependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.AlterarVersaoBusiness>()
            .Adicionar(adicionarVersaoDto, cancellationToken);

        // Assert
        dadosCapturados.ShouldNotBeNull();
        dadosCapturados.Versao.ShouldBe(adicionarVersaoDto.Versao);
        dadosCapturados.Descricao.ShouldBe(adicionarVersaoDto.Descricao);
        dadosCapturados.DataCadastro.ShouldBeInRange(DateTime.UtcNow.AddMinutes(-1), DateTime.UtcNow.AddMinutes(1));
    }

    [Fact(DisplayName = "[AlterarVersaoBusiness] - Deve lidar com timeout do repository")]
    [Trait("Business", "AlterarVersaoBusiness")]
    public async Task Timeout_Repository_DeveLidarCorretamente()
    {
        // Arrange
        var adicionarVersaoDto = AlterarVersaoFixture.GerarAdicionarVersaoValido();
        var versaoFirebaseDto = AlterarVersaoFixture.GerarVersaoFirebaseDto();
        var cancellationToken = CancellationToken.None;

        _dependencyInjectorFactory.Mocker.GetMock<IMapper>()
            .Setup(mapper => mapper.Map<VersaoFirebaseDto>(adicionarVersaoDto))
            .Returns(versaoFirebaseDto);

        _dependencyInjectorFactory.Mocker.GetMock<IVersaoFirebaseRepository>()
            .Setup(repo => repo.AddAsync(versaoFirebaseDto, cancellationToken))
            .ThrowsAsync(new TimeoutException("Timeout no Firebase"));

        // Act & Assert
        var exception = await Assert.ThrowsAsync<TimeoutException>(
            () => _dependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.AlterarVersaoBusiness>()
                .Adicionar(adicionarVersaoDto, cancellationToken));
        
        exception.Message.ShouldBe("Timeout no Firebase");
    }

    [Fact(DisplayName = "[AlterarVersaoBusiness] - Deve processar versões em lote")]
    [Trait("Business", "AlterarVersaoBusiness")]
    public async Task ProcessarVersoes_EmLote_DeveProcessarTodas()
    {
        // Arrange
        var versoes = AlterarVersaoFixture.GerarListaAdicionarVersao(10);
        var cancellationToken = CancellationToken.None;

        foreach (var versao in versoes)
        {
            var versaoFirebase = AlterarVersaoFixture.GerarVersaoFirebaseDto(versao.Versao, versao.Descricao);
            _dependencyInjectorFactory.Mocker.GetMock<IMapper>()
                .Setup(mapper => mapper.Map<VersaoFirebaseDto>(versao))
                .Returns(versaoFirebase);
        }

        var business = _dependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.AlterarVersaoBusiness>();

        // Act
        var tasks = versoes.Select(versao => business.Adicionar(versao, cancellationToken));
        await Task.WhenAll(tasks);

        // Assert
        _dependencyInjectorFactory.Mocker.GetMock<IMapper>().Verify(
            mapper => mapper.Map<VersaoFirebaseDto>(It.IsAny<AdicionarVersaoDto>()),
            Times.Exactly(10));

        _dependencyInjectorFactory.Mocker.GetMock<IVersaoFirebaseRepository>().Verify(
            repo => repo.AddAsync(It.IsAny<VersaoFirebaseDto>(), cancellationToken),
            Times.Exactly(10));
    }

    [Fact(DisplayName = "[AlterarVersaoBusiness] - Deve processar edições em lote")]
    [Trait("Business", "AlterarVersaoBusiness")]
    public async Task ProcessarEdicoes_EmLote_DeveProcessarTodas()
    {
        // Arrange
        var edicoes = AlterarVersaoFixture.GerarListaEditarVersao(15);
        var cancellationToken = CancellationToken.None;

        var business = _dependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.AlterarVersaoBusiness>();

        // Act
        var tasks = edicoes.Select(edicao => business.Editar(edicao, cancellationToken));
        await Task.WhenAll(tasks);

        // Assert
        _dependencyInjectorFactory.Mocker.GetMock<IVersaoFirebaseRepository>().Verify(
            repo => repo.UpdateAsync(It.IsAny<EditarVersaoDto>(), cancellationToken),
            Times.Exactly(15));
    }

    [Fact(DisplayName = "[AlterarVersaoBusiness] - Deve manter ordem de execução em operações sequenciais")]
    [Trait("Business", "AlterarVersaoBusiness")]
    public async Task OperacoesSequenciais_DeveManterOrdem()
    {
        // Arrange
        var ordemExecucao = new List<string>();
        var adicionarVersaoDto = AlterarVersaoFixture.GerarAdicionarVersaoValido();
        var editarVersaoDto = AlterarVersaoFixture.GerarEditarVersaoValido();
        var versaoFirebaseDto = AlterarVersaoFixture.GerarVersaoFirebaseDto();
        var cancellationToken = CancellationToken.None;

        _dependencyInjectorFactory.Mocker.GetMock<IMapper>()
            .Setup(mapper => mapper.Map<VersaoFirebaseDto>(adicionarVersaoDto))
            .Returns(versaoFirebaseDto);

        _dependencyInjectorFactory.Mocker.GetMock<IVersaoFirebaseRepository>()
            .Setup(repo => repo.AddAsync(It.IsAny<VersaoFirebaseDto>(), It.IsAny<CancellationToken>()))
            .Callback(() => ordemExecucao.Add("Adicionar"))
            .Returns(Task.CompletedTask);

        _dependencyInjectorFactory.Mocker.GetMock<IVersaoFirebaseRepository>()
            .Setup(repo => repo.UpdateAsync(It.IsAny<EditarVersaoDto>(), It.IsAny<CancellationToken>()))
            .Callback(() => ordemExecucao.Add("Editar"))
            .Returns(Task.CompletedTask);

        var business = _dependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.AlterarVersaoBusiness>();

        // Act
        await business.Adicionar(adicionarVersaoDto, cancellationToken);
        await business.Editar(editarVersaoDto, cancellationToken);
        await business.Adicionar(adicionarVersaoDto, cancellationToken);

        // Assert
        ordemExecucao.Count.ShouldBe(3);
        ordemExecucao[0].ShouldBe("Adicionar");
        ordemExecucao[1].ShouldBe("Editar");
        ordemExecucao[2].ShouldBe("Adicionar");
    }

    [Fact(DisplayName = "[AlterarVersaoBusiness] - Deve funcionar com diferentes configurações de mapper")]
    [Trait("Business", "AlterarVersaoBusiness")]
    public async Task DiferentesConfiguracoes_Mapper_DeveFuncionar()
    {
        // Arrange
        var adicionarVersaoDto = AlterarVersaoFixture.GerarAdicionarVersaoValido();
        var versaoFirebaseCustomizada = AlterarVersaoFixture.GerarVersaoFirebaseDto(
            "2.0.0", 
            "Versão customizada", 
            "custom-sid", 
            "Custom User");
        var cancellationToken = CancellationToken.None;

        _dependencyInjectorFactory.Mocker.GetMock<IMapper>()
            .Setup(mapper => mapper.Map<VersaoFirebaseDto>(adicionarVersaoDto))
            .Returns(versaoFirebaseCustomizada);

        // Act
        await _dependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.AlterarVersaoBusiness>()
            .Adicionar(adicionarVersaoDto, cancellationToken);

        // Assert
        _dependencyInjectorFactory.Mocker.GetMock<IVersaoFirebaseRepository>().Verify(
            repo => repo.AddAsync(It.Is<VersaoFirebaseDto>(dto => 
                dto.Versao == "2.0.0" && 
                dto.Descricao == "Versão customizada" &&
                dto.Sid == "custom-sid" &&
                dto.Usuario == "Custom User"), cancellationToken),
            Times.Once);
    }
}
