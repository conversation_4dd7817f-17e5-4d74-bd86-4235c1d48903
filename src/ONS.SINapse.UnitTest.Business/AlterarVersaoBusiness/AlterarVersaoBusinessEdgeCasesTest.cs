using AutoMapper;
using ONS.SINapse.Repository.IRepository;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.DTO.Firebase;
using ONS.SINapse.UnitTest.Shared.Fixtures;

namespace ONS.SINapse.UnitTest.Business.AlterarVersaoBusiness;

public partial class AlterarVersaoBusinessTest
{
    [Fact(DisplayName = "[AlterarVersaoBusiness] - Deve propagar exceção do mapper na adição")]
    [Trait("Business", "AlterarVersaoBusiness")]
    public async Task Adicionar_ExcecaoMapper_DevePropagarExcecao()
    {
        // Arrange
        var adicionarVersaoDto = AlterarVersaoFixture.GerarAdicionarVersaoValido();
        var cancellationToken = CancellationToken.None;

        DependencyInjectorFactory.Mocker.GetMock<IMapper>()
            .Setup(mapper => mapper.Map<VersaoFirebaseDto>(adicionarVersaoDto))
            .Throws(new InvalidOperationException("Erro no mapeamento"));

        // Act & Assert
        var exception = await Assert.ThrowsAsync<InvalidOperationException>(
            () => DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.AlterarVersaoBusiness>()
                .Adicionar(adicionarVersaoDto, cancellationToken));
        
        exception.Message.ShouldBe("Erro no mapeamento");
    }

    [Fact(DisplayName = "[AlterarVersaoBusiness] - Deve propagar exceção do repository na adição")]
    [Trait("Business", "AlterarVersaoBusiness")]
    public async Task Adicionar_ExcecaoRepository_DevePropagarExcecao()
    {
        // Arrange
        var adicionarVersaoDto = AlterarVersaoFixture.GerarAdicionarVersaoValido();
        var versaoFirebaseDto = AlterarVersaoFixture.GerarVersaoFirebaseDto();
        var cancellationToken = CancellationToken.None;

        DependencyInjectorFactory.Mocker.GetMock<IMapper>()
            .Setup(mapper => mapper.Map<VersaoFirebaseDto>(adicionarVersaoDto))
            .Returns(versaoFirebaseDto);

        DependencyInjectorFactory.Mocker.GetMock<IVersaoFirebaseRepository>()
            .Setup(repo => repo.AddAsync(versaoFirebaseDto, cancellationToken))
            .ThrowsAsync(new InvalidOperationException("Erro no Firebase"));

        // Act & Assert
        var exception = await Assert.ThrowsAsync<InvalidOperationException>(
            () => DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.AlterarVersaoBusiness>()
                .Adicionar(adicionarVersaoDto, cancellationToken));
        
        exception.Message.ShouldBe("Erro no Firebase");
    }

    [Fact(DisplayName = "[AlterarVersaoBusiness] - Deve propagar exceção do repository na edição")]
    [Trait("Business", "AlterarVersaoBusiness")]
    public async Task Editar_ExcecaoRepository_DevePropagarExcecao()
    {
        // Arrange
        var editarVersaoDto = AlterarVersaoFixture.GerarEditarVersaoValido();
        var cancellationToken = CancellationToken.None;

        DependencyInjectorFactory.Mocker.GetMock<IVersaoFirebaseRepository>()
            .Setup(repo => repo.UpdateAsync(editarVersaoDto, cancellationToken))
            .ThrowsAsync(new TimeoutException("Timeout no Firebase"));

        // Act & Assert
        var exception = await Assert.ThrowsAsync<TimeoutException>(
            () => DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.AlterarVersaoBusiness>()
                .Editar(editarVersaoDto, cancellationToken));
        
        exception.Message.ShouldBe("Timeout no Firebase");
    }

    [Fact(DisplayName = "[AlterarVersaoBusiness] - Deve lidar com CancellationToken cancelado na adição")]
    [Trait("Business", "AlterarVersaoBusiness")]
    public async Task Adicionar_CancellationTokenCancelado_DevePropagarOperationCanceledException()
    {
        // Arrange
        var adicionarVersaoDto = AlterarVersaoFixture.GerarAdicionarVersaoValido();
        var versaoFirebaseDto = AlterarVersaoFixture.GerarVersaoFirebaseDto();

        var cancellationTokenSource = new CancellationTokenSource();
        cancellationTokenSource.Cancel();

        DependencyInjectorFactory.Mocker.GetMock<IMapper>()
            .Setup(mapper => mapper.Map<VersaoFirebaseDto>(adicionarVersaoDto))
            .Returns(versaoFirebaseDto);

        DependencyInjectorFactory.Mocker.GetMock<IVersaoFirebaseRepository>()
            .Setup(repo => repo.AddAsync(versaoFirebaseDto, It.IsAny<CancellationToken>()))
            .ThrowsAsync(new OperationCanceledException());

        // Act & Assert
        await Assert.ThrowsAsync<OperationCanceledException>(
            () => DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.AlterarVersaoBusiness>()
                .Adicionar(adicionarVersaoDto, cancellationTokenSource.Token));
    }

    [Fact(DisplayName = "[AlterarVersaoBusiness] - Deve lidar com CancellationToken cancelado na edição")]
    [Trait("Business", "AlterarVersaoBusiness")]
    public async Task Editar_CancellationTokenCancelado_DevePropagarOperationCanceledException()
    {
        // Arrange
        var editarVersaoDto = AlterarVersaoFixture.GerarEditarVersaoValido();

        var cancellationTokenSource = new CancellationTokenSource();
        cancellationTokenSource.Cancel();

        DependencyInjectorFactory.Mocker.GetMock<IVersaoFirebaseRepository>()
            .Setup(repo => repo.UpdateAsync(editarVersaoDto, It.IsAny<CancellationToken>()))
            .ThrowsAsync(new OperationCanceledException());

        // Act & Assert
        await Assert.ThrowsAsync<OperationCanceledException>(
            () => DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.AlterarVersaoBusiness>()
                .Editar(editarVersaoDto, cancellationTokenSource.Token));
    }

    [Fact(DisplayName = "[AlterarVersaoBusiness] - Deve processar múltiplas operações sequenciais")]
    [Trait("Business", "AlterarVersaoBusiness")]
    public async Task OperacoesSequenciais_DeveProcessarCorretamente()
    {
        // Arrange
        var adicionarVersaoDto = AlterarVersaoFixture.GerarAdicionarVersaoValido();
        var editarVersaoDto = AlterarVersaoFixture.GerarEditarVersaoValido();
        var versaoFirebaseDto = AlterarVersaoFixture.GerarVersaoFirebaseDto();
        var cancellationToken = CancellationToken.None;

        DependencyInjectorFactory.Mocker.GetMock<IMapper>()
            .Setup(mapper => mapper.Map<VersaoFirebaseDto>(adicionarVersaoDto))
            .Returns(versaoFirebaseDto);

        var business = DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.AlterarVersaoBusiness>();

        // Act
        await business.Adicionar(adicionarVersaoDto, cancellationToken);
        await business.Editar(editarVersaoDto, cancellationToken);

        // Assert
        DependencyInjectorFactory.Mocker.GetMock<IVersaoFirebaseRepository>().Verify(
            repo => repo.AddAsync(versaoFirebaseDto, cancellationToken),
            Times.Once);

        DependencyInjectorFactory.Mocker.GetMock<IVersaoFirebaseRepository>().Verify(
            repo => repo.UpdateAsync(editarVersaoDto, cancellationToken),
            Times.Once);
    }

    [Fact(DisplayName = "[AlterarVersaoBusiness] - Deve processar operações paralelas")]
    [Trait("Business", "AlterarVersaoBusiness")]
    public async Task OperacoesParalelas_DeveProcessarCorretamente()
    {
        // Arrange
        var adicionarVersaoDto = AlterarVersaoFixture.GerarAdicionarVersaoValido();
        var editarVersaoDto = AlterarVersaoFixture.GerarEditarVersaoValido();
        var versaoFirebaseDto = AlterarVersaoFixture.GerarVersaoFirebaseDto();
        var cancellationToken = CancellationToken.None;

        DependencyInjectorFactory.Mocker.GetMock<IMapper>()
            .Setup(mapper => mapper.Map<VersaoFirebaseDto>(adicionarVersaoDto))
            .Returns(versaoFirebaseDto);

        var business1 = DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.AlterarVersaoBusiness>();
        var business2 = DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.AlterarVersaoBusiness>();

        // Act
        var task1 = business1.Adicionar(adicionarVersaoDto, cancellationToken);
        var task2 = business2.Editar(editarVersaoDto, cancellationToken);
        
        await Task.WhenAll(task1, task2);

        // Assert
        DependencyInjectorFactory.Mocker.GetMock<IVersaoFirebaseRepository>().Verify(
            repo => repo.AddAsync(versaoFirebaseDto, cancellationToken),
            Times.Once);

        DependencyInjectorFactory.Mocker.GetMock<IVersaoFirebaseRepository>().Verify(
            repo => repo.UpdateAsync(editarVersaoDto, cancellationToken),
            Times.Once);
    }

    [Fact(DisplayName = "[AlterarVersaoBusiness] - Deve processar versão com caracteres especiais")]
    [Trait("Business", "AlterarVersaoBusiness")]
    public async Task Adicionar_VersaoComCaracteresEspeciais_DeveProcessar()
    {
        // Arrange
        var versaoEspecial = "1.0.0-beta+build.123";
        var descricaoEspecial = "Versão com caracteres especiais: áéíóú ç ñ @#$%";
        var adicionarVersaoDto = AlterarVersaoFixture.GerarAdicionarVersaoComVersaoCustomizada(versaoEspecial);
        adicionarVersaoDto.Descricao = descricaoEspecial;
        
        var versaoFirebaseDto = AlterarVersaoFixture.GerarVersaoFirebaseDto(versaoEspecial, descricaoEspecial);
        var cancellationToken = CancellationToken.None;

        DependencyInjectorFactory.Mocker.GetMock<IMapper>()
            .Setup(mapper => mapper.Map<VersaoFirebaseDto>(adicionarVersaoDto))
            .Returns(versaoFirebaseDto);

        // Act
        await DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.AlterarVersaoBusiness>()
            .Adicionar(adicionarVersaoDto, cancellationToken);

        // Assert
        DependencyInjectorFactory.Mocker.GetMock<IMapper>().Verify(
            mapper => mapper.Map<VersaoFirebaseDto>(It.Is<AdicionarVersaoDto>(dto => 
                dto.Versao == versaoEspecial && dto.Descricao == descricaoEspecial)),
            Times.Once);
    }

    [Fact(DisplayName = "[AlterarVersaoBusiness] - Deve processar descrição com caracteres especiais na edição")]
    [Trait("Business", "AlterarVersaoBusiness")]
    public async Task Editar_DescricaoComCaracteresEspeciais_DeveProcessar()
    {
        // Arrange
        var descricaoEspecial = "Correção crítica: bug #123 - melhoria de 50% na performance ⚡";
        var editarVersaoDto = AlterarVersaoFixture.GerarEditarVersaoComDescricaoCustomizada(descricaoEspecial);
        var cancellationToken = CancellationToken.None;

        // Act
        await DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.AlterarVersaoBusiness>()
            .Editar(editarVersaoDto, cancellationToken);

        // Assert
        DependencyInjectorFactory.Mocker.GetMock<IVersaoFirebaseRepository>().Verify(
            repo => repo.UpdateAsync(It.Is<EditarVersaoDto>(dto => dto.Descricao == descricaoEspecial), cancellationToken),
            Times.Once);
    }
}
