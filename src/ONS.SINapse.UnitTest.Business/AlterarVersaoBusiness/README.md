# Testes Unitários - AlterarVersaoBusiness

Este diretório contém os testes unitários para a classe `AlterarVersaoBusiness`, que é responsável por gerenciar a adição e edição de versões do sistema no Firebase.

## 📁 Estrutura dos Arquivos

- **AlterarVersaoBusinessTest.cs**: Testes principais da funcionalidade básica
- **SetupAlterarVersaoBusiness.cs**: Configuração base para os testes

## 🎯 Funcionalidade Testada

A classe `AlterarVersaoBusiness` implementa a interface `IAlterarVersaoBusiness` e possui dois métodos principais:

- **Adicionar**: Mapeia `AdicionarVersaoDto` para `VersaoFirebaseDto` usando AutoMapper e persiste no Firebase
- **Editar**: Atualiza uma versão existente usando `EditarVersaoDto` diretamente no Firebase

### Comportamento Principal

1. **Adicionar**: Usa AutoMapper para converter DTO → Firebase DTO → Repository
2. **Editar**: Chama diretamente o repository com o DTO de edição
3. **Dependências**: `IMapper` e `IVersaoFirebaseRepository`

## 🧪 Cenários de Teste Cobertos

### 1. AlterarVersaoBusinessTest.cs (10 testes)

#### ✅ **Cenários Principais**
- **Adicionar versão**: Valida mapeamento e persistência correta
- **Editar versão**: Valida atualização direta no repository
- **Diferentes versões**: Testa com múltiplas versões válidas (Theory)
- **Diferentes descrições**: Testa com várias descrições válidas (Theory)

#### 🔄 **Cenários de Propagação**
- **CancellationToken**: Verifica propagação correta do token
- **Dados vazios**: Testa comportamento com strings vazias
- **Dados longos**: Testa com versões e descrições extensas

### 2. AlterarVersaoBusinessEdgeCasesTest.cs (10 testes)

#### ⚠️ **Cenários de Erro**
- **Exceção do Mapper**: Verifica propagação de erros de mapeamento
- **Exceção do Repository**: Testa propagação de erros do Firebase
- **CancellationToken cancelado**: Valida comportamento com token cancelado
- **Timeout**: Testa comportamento com timeout do Firebase

#### 🔤 **Cenários Especiais**
- **Caracteres especiais**: Testa versões e descrições com caracteres especiais
- **Operações sequenciais**: Valida múltiplas operações em sequência
- **Operações paralelas**: Testa operações simultâneas

### 3. AlterarVersaoBusinessIntegrationTest.cs (10 testes)

#### ⚡ **Cenários de Performance**
- **Múltiplas operações**: Testa performance com 100 operações (< 2s)
- **Operações concorrentes**: Valida consistência em operações paralelas
- **Processamento em lote**: Testa adição/edição de múltiplas versões

#### 🔍 **Cenários de Integridade**
- **Integridade de dados**: Valida que dados mapeados estão corretos
- **Ordem de execução**: Verifica ordem em operações sequenciais
- **Configurações customizadas**: Testa com diferentes configurações de mapper

## 🛡️ Cenários de Borda Específicos

### 📊 **Dados de Entrada**
- **Strings vazias**: Aceita e processa versões/descrições vazias
- **Dados longos**: Suporta versões e descrições extensas
- **Caracteres especiais**: Processa caracteres Unicode, símbolos e acentos
- **Versões semânticas**: Suporta formatos como "1.0.0-beta+build.123"

### 🔗 **Integração com Dependências**
- **AutoMapper**: Mocks configurados para diferentes cenários de mapeamento
- **Repository**: Verificações detalhadas de chamadas ao Firebase
- **CancellationToken**: Propagação correta em todos os cenários
- **Exceções**: Propagação adequada de erros das dependências

### ⚡ **Performance e Concorrência**
- **Múltiplas operações**: Performance adequada com 50+ operações
- **Operações paralelas**: Suporte a operações simultâneas
- **Consistência**: Manutenção de estado em operações concorrentes

## 🚀 Características Técnicas

### 🔧 **Configuração de Testes**
- **Mocks**: Uso de `Mock<IMapper>` e `Mock<IVersaoFirebaseRepository>`
- **Fixtures**: `AlterarVersaoFixture` para geração de dados de teste
- **Dependency Injection**: `MockDependencyInjectorFactory` para isolamento

### 📈 **Cobertura de Código**
- **100% dos métodos públicos**: `Adicionar` e `Editar`
- **Cenários de sucesso e falha**: Cobertura completa de fluxos
- **Casos extremos**: Validação de comportamento em situações limite

### 🎯 **Padrões de Verificação**
- **Verificação de chamadas**: `Times.Once`, `Times.Exactly(n)`
- **Verificação de dados**: Validação detalhada de propriedades dos DTOs
- **Verificação de comportamento**: Confirmação de lógica de negócio

## 🔧 Execução dos Testes

```bash
# Executar apenas os testes deste diretório
dotnet test --filter "FullyQualifiedName~AlterarVersaoBusiness"

# Executar com verbosidade detalhada
dotnet test --filter "FullyQualifiedName~AlterarVersaoBusiness" --verbosity detailed

# Executar apenas testes principais
dotnet test --filter "FullyQualifiedName~AlterarVersaoBusinessTest"

# Executar apenas testes de edge cases
dotnet test --filter "FullyQualifiedName~AlterarVersaoBusinessEdgeCasesTest"

# Executar apenas testes de integração
dotnet test --filter "FullyQualifiedName~AlterarVersaoBusinessIntegrationTest"
```

## 📋 Resultados Esperados

- **Total de testes**: 30 (10 + 10 + 10)
- **Taxa de sucesso**: 100%
- **Tempo de execução**: ~3-5 segundos
- **Cobertura**: Cenários principais, bordas, erros e integração

## 📚 Dependências Testadas

### Interfaces Mockadas
- `IMapper`: AutoMapper para conversão de DTOs
- `IVersaoFirebaseRepository`: Repository para persistência no Firebase

### DTOs Utilizados
- `AdicionarVersaoDto`: DTO para adição de versões
- `EditarVersaoDto`: DTO para edição de versões
- `VersaoFirebaseDto`: DTO para persistência no Firebase

### Cenários de Negócio
- **Adição**: Mapeamento + persistência via repository
- **Edição**: Atualização direta via repository
- **Mapeamento**: Conversão automática via AutoMapper
- **Propagação**: CancellationToken e exceções

## 🎯 Validações Principais

1. **Lógica de Negócio**: Verifica se o fluxo de adição/edição está correto
2. **Integração AutoMapper**: Confirma que mapeamento é executado corretamente
3. **Integração Repository**: Valida que dados são persistidos no Firebase
4. **Gestão de Erros**: Verifica propagação adequada de exceções
5. **Performance**: Confirma que a performance é adequada mesmo com muitas operações

## 📁 Arquivos de Suporte

### Fixtures
- **AlterarVersaoFixture.cs**: Métodos de geração de dados de teste
- **AlterarVersaoBusinessCollection.cs**: Collection para isolamento

### Dados de Teste
- **Versões válidas**: "1.0.0", "2.1.3", "10.15.99", "0.0.1"
- **Descrições válidas**: Textos variados para diferentes cenários
- **Dados especiais**: Caracteres Unicode, símbolos, textos longos
- **Dados de borda**: Strings vazias, dados muito longos

## 🎯 Descobertas Importantes

1. **Simplicidade**: A classe é simples mas crítica para versionamento
2. **Dependência do AutoMapper**: Mapeamento é essencial para adição
3. **Edição direta**: Edição não usa mapeamento, vai direto ao repository
4. **Performance**: Excelente performance mesmo com muitas operações
5. **Robustez**: Lida bem com dados vazios e caracteres especiais

**Os testes cobrem completamente a funcionalidade de gerenciamento de versões!** 🎉
