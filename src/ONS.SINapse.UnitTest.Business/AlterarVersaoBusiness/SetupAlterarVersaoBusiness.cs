using AutoMapper;
using ONS.SINapse.Repository.IRepository;
using ONS.SINapse.UnitTest.Business.Fixtures.Collections;
using ONS.SINapse.UnitTest.Shared;
using ONS.SINapse.UnitTest.Shared.Fixtures;

namespace ONS.SINapse.UnitTest.Business.AlterarVersaoBusiness;

[Collection(nameof(AlterarVersaoBusinessCollection))]
public partial class AlterarVersaoBusinessTest
{
    protected readonly MockDependencyInjectorFactory DependencyInjectorFactory;

    public AlterarVersaoBusinessTest(AlterarVersaoFixture alterarVersaoFixture)
    {
        DependencyInjectorFactory = new MockDependencyInjectorFactory();
        DependencyInjectorFactory.RegisterMocks();

        // Configurar mocks específicos para AlterarVersaoBusiness
        ConfigurarMocks();
    }

    private void ConfigurarMocks()
    {
        // Configurar mock padrão para IVersaoFirebaseRepository
        DependencyInjectorFactory.Mocker.GetMock<IVersaoFirebaseRepository>()
            .Setup(repo => repo.AddAsync(It.IsAny<ONS.SINapse.Shared.DTO.Firebase.VersaoFirebaseDto>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        DependencyInjectorFactory.Mocker.GetMock<IVersaoFirebaseRepository>()
            .Setup(repo => repo.UpdateAsync(It.IsAny<ONS.SINapse.Shared.DTO.EditarVersaoDto>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);
    }
}
