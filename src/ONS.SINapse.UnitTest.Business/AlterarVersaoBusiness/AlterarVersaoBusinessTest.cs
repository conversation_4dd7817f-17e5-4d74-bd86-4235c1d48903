using ONS.SINapse.Repository.IRepository;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.DTO.Firebase;
using ONS.SINapse.UnitTest.Shared.Fixtures;

namespace ONS.SINapse.UnitTest.Business.AlterarVersaoBusiness;

public partial class AlterarVersaoBusinessTest
{
    [Fact(DisplayName = "[AlterarVersaoBusiness] - Deve criar instância com sucesso")]
    [Trait("Business", "AlterarVersaoBusiness")]
    public void CriarInstancia_DeveSerCriadaComSucesso()
    {
        // Act
        var business = DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.AlterarVersaoBusiness>();

        // Assert
        business.ShouldNotBeNull();
    }

    [Fact(DisplayName = "[AlterarVersaoBusiness] - Deve verificar UserContextAccessor")]
    [Trait("Business", "AlterarVersaoBusiness")]
    public void VerificarUserContextAccessor_DeveRetornarUserContext()
    {
        // Act
        var userContextAccessor = _dependencyInjectorFactory.Mocker.Get<IUserContextAccessor>();
        var userContext = userContextAccessor.UserContext;

        // Assert
        userContext.ShouldNotBeNull();
        userContext.Sid.ShouldNotBeNullOrEmpty();
        userContext.Nome.ShouldNotBeNullOrEmpty();
    }

    [Fact(DisplayName = "[AlterarVersaoBusiness] - Deve adicionar versão com sucesso")]
    [Trait("Business", "AlterarVersaoBusiness")]
    public async Task Adicionar_VersaoValida_DeveAdicionarComSucesso()
    {
        // Arrange
        var adicionarVersaoDto = AlterarVersaoFixture.GerarAdicionarVersaoValido();
        var cancellationToken = CancellationToken.None;

        // Act
        await DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.AlterarVersaoBusiness>()
            .Adicionar(adicionarVersaoDto, cancellationToken);

        // Assert
        DependencyInjectorFactory.Mocker.GetMock<IVersaoFirebaseRepository>().Verify(
            repo => repo.AddAsync(It.IsAny<VersaoFirebaseDto>(), cancellationToken),
            Times.Once);
    }

    [Fact(DisplayName = "[AlterarVersaoBusiness] - Deve editar versão com sucesso")]
    [Trait("Business", "AlterarVersaoBusiness")]
    public async Task Editar_VersaoValida_DeveEditarComSucesso()
    {
        // Arrange
        var editarVersaoDto = AlterarVersaoFixture.GerarEditarVersaoValido();
        var cancellationToken = CancellationToken.None;

        // Act
        await DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.AlterarVersaoBusiness>()
            .Editar(editarVersaoDto, cancellationToken);

        // Assert
        DependencyInjectorFactory.Mocker.GetMock<IVersaoFirebaseRepository>().Verify(
            repo => repo.UpdateAsync(editarVersaoDto, cancellationToken),
            Times.Once);
    }

    [Theory(DisplayName = "[AlterarVersaoBusiness] - Deve adicionar diferentes versões")]
    [Trait("Business", "AlterarVersaoBusiness")]
    [MemberData(nameof(AlterarVersaoFixture.DadosVersaoValida), MemberType = typeof(AlterarVersaoFixture))]
    public async Task Adicionar_DiferentesVersoes_DeveAdicionarCorretamente(string versao, string descricao)
    {
        // Arrange
        var adicionarVersaoDto = AlterarVersaoFixture.GerarAdicionarVersaoComVersaoCustomizada(versao);
        adicionarVersaoDto.Descricao = descricao;
        var cancellationToken = CancellationToken.None;

        // Act
        await DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.AlterarVersaoBusiness>()
            .Adicionar(adicionarVersaoDto, cancellationToken);

        // Assert
        DependencyInjectorFactory.Mocker.GetMock<IVersaoFirebaseRepository>().Verify(
            repo => repo.AddAsync(It.IsAny<VersaoFirebaseDto>(), cancellationToken),
            Times.Once);
    }

    [Theory(DisplayName = "[AlterarVersaoBusiness] - Deve editar com diferentes descrições")]
    [Trait("Business", "AlterarVersaoBusiness")]
    [MemberData(nameof(AlterarVersaoFixture.DadosDescricaoValida), MemberType = typeof(AlterarVersaoFixture))]
    public async Task Editar_DiferentesDescricoes_DeveEditarCorretamente(string descricao)
    {
        // Arrange
        var editarVersaoDto = AlterarVersaoFixture.GerarEditarVersaoComDescricaoCustomizada(descricao);
        var cancellationToken = CancellationToken.None;

        // Act
        await DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.AlterarVersaoBusiness>()
            .Editar(editarVersaoDto, cancellationToken);

        // Assert
        DependencyInjectorFactory.Mocker.GetMock<IVersaoFirebaseRepository>().Verify(
            repo => repo.UpdateAsync(It.Is<EditarVersaoDto>(dto => dto.Descricao == descricao), cancellationToken),
            Times.Once);
    }

    [Fact(DisplayName = "[AlterarVersaoBusiness] - Deve propagar CancellationToken na adição")]
    [Trait("Business", "AlterarVersaoBusiness")]
    public async Task Adicionar_DevePropagarCancellationToken()
    {
        // Arrange
        var adicionarVersaoDto = AlterarVersaoFixture.GerarAdicionarVersaoValido();

        var cancellationTokenSource = new CancellationTokenSource();
        var cancellationToken = cancellationTokenSource.Token;

        // Act
        await DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.AlterarVersaoBusiness>()
            .Adicionar(adicionarVersaoDto, cancellationToken);

        // Assert
        DependencyInjectorFactory.Mocker.GetMock<IVersaoFirebaseRepository>().Verify(
            repo => repo.AddAsync(It.IsAny<VersaoFirebaseDto>(), cancellationToken),
            Times.Once);
    }

    [Fact(DisplayName = "[AlterarVersaoBusiness] - Deve propagar CancellationToken na edição")]
    [Trait("Business", "AlterarVersaoBusiness")]
    public async Task Editar_DevePropagarCancellationToken()
    {
        // Arrange
        var editarVersaoDto = AlterarVersaoFixture.GerarEditarVersaoValido();
        
        var cancellationTokenSource = new CancellationTokenSource();
        var cancellationToken = cancellationTokenSource.Token;

        // Act
        await DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.AlterarVersaoBusiness>()
            .Editar(editarVersaoDto, cancellationToken);

        // Assert
        DependencyInjectorFactory.Mocker.GetMock<IVersaoFirebaseRepository>().Verify(
            repo => repo.UpdateAsync(editarVersaoDto, cancellationToken),
            Times.Once);
    }

    [Fact(DisplayName = "[AlterarVersaoBusiness] - Deve processar versão com dados vazios")]
    [Trait("Business", "AlterarVersaoBusiness")]
    public async Task Adicionar_DadosVazios_DeveProcessar()
    {
        // Arrange
        var adicionarVersaoDto = AlterarVersaoFixture.GerarAdicionarVersaoVazio();
        var cancellationToken = CancellationToken.None;

        // Act
        await DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.AlterarVersaoBusiness>()
            .Adicionar(adicionarVersaoDto, cancellationToken);

        // Assert
        DependencyInjectorFactory.Mocker.GetMock<IVersaoFirebaseRepository>().Verify(
            repo => repo.AddAsync(It.IsAny<VersaoFirebaseDto>(), cancellationToken),
            Times.Once);
    }

    [Fact(DisplayName = "[AlterarVersaoBusiness] - Deve processar edição com descrição vazia")]
    [Trait("Business", "AlterarVersaoBusiness")]
    public async Task Editar_DescricaoVazia_DeveProcessar()
    {
        // Arrange
        var editarVersaoDto = AlterarVersaoFixture.GerarEditarVersaoVazio();
        var cancellationToken = CancellationToken.None;

        // Act
        await DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.AlterarVersaoBusiness>()
            .Editar(editarVersaoDto, cancellationToken);

        // Assert
        DependencyInjectorFactory.Mocker.GetMock<IVersaoFirebaseRepository>().Verify(
            repo => repo.UpdateAsync(It.Is<EditarVersaoDto>(dto => dto.Descricao == string.Empty), cancellationToken),
            Times.Once);
    }

    [Fact(DisplayName = "[AlterarVersaoBusiness] - Deve processar dados com tamanho longo")]
    [Trait("Business", "AlterarVersaoBusiness")]
    public async Task Adicionar_DadosLongos_DeveProcessar()
    {
        // Arrange
        var adicionarVersaoDto = AlterarVersaoFixture.GerarAdicionarVersaoComDadosLongos();
        var cancellationToken = CancellationToken.None;

        // Act
        await DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.AlterarVersaoBusiness>()
            .Adicionar(adicionarVersaoDto, cancellationToken);

        // Assert
        DependencyInjectorFactory.Mocker.GetMock<IVersaoFirebaseRepository>().Verify(
            repo => repo.AddAsync(It.IsAny<VersaoFirebaseDto>(), cancellationToken),
            Times.Once);
    }

    [Fact(DisplayName = "[AlterarVersaoBusiness] - Deve propagar exceção do repository")]
    [Trait("Business", "AlterarVersaoBusiness")]
    public async Task Adicionar_ExcecaoRepository_DevePropagarExcecao()
    {
        // Arrange
        var adicionarVersaoDto = AlterarVersaoFixture.GerarAdicionarVersaoValido();
        var cancellationToken = CancellationToken.None;

        DependencyInjectorFactory.Mocker.GetMock<IVersaoFirebaseRepository>()
            .Setup(repo => repo.AddAsync(It.IsAny<VersaoFirebaseDto>(), cancellationToken))
            .ThrowsAsync(new InvalidOperationException("Erro no Firebase"));

        // Act & Assert
        var exception = await Assert.ThrowsAsync<InvalidOperationException>(
            () => DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.AlterarVersaoBusiness>()
                .Adicionar(adicionarVersaoDto, cancellationToken));

        exception.Message.ShouldBe("Erro no Firebase");
    }
}
