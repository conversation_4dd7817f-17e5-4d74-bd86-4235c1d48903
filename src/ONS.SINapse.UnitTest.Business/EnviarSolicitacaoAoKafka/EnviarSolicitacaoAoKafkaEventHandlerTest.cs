using MassTransit;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Integracao.Shared.Settings;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Kafka;
using ONS.SINapse.Shared.Kafka;
using ONS.SINapse.Shared.Kafka.Providers;

using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Solicitacao.Dtos.Integracao;
using ONS.SINapse.Solicitacao.EventHandlers.Events;
using ONS.SINapse.Solicitacao.Workers.Messages;
using ONS.SINapse.UnitTest.Business.Fixtures.Collections;
using ONS.SINapse.UnitTest.Shared;
using ONS.SINapse.UnitTest.Shared.Fixtures;

namespace ONS.SINapse.UnitTest.Business.EnviarSolicitacaoAoKafka;

[Collection(nameof(EnviarSolicitacaoAoKafkaEventHandlerCollection))]
public class EnviarSolicitacaoAoKafkaEventHandlerTest
{
    private readonly SolicitacaoFixture _solicitacaoFixture;
    private readonly MockDependencyInjectorFactory _dependencyInjectorFactory;

    public EnviarSolicitacaoAoKafkaEventHandlerTest(SolicitacaoFixture solicitacaoFixture)
    {
        _solicitacaoFixture = solicitacaoFixture;
        _dependencyInjectorFactory = new MockDependencyInjectorFactory();
        _dependencyInjectorFactory.RegisterMocks();

        ConfigurarKafkaSettings();
    }

    private void ConfigurarKafkaSettings()
    {
        // Configurar KafkaTopicsSettings para os testes
        var kafkaSettings = new KafkaTopicsSettings
        {
            Sistemas = new List<KafkaSistemaTopicSettings>
            {
                new() { Nome = "GERDIN", Topic = "ONS.GERDIN.SISTEMA", Notificar = true },
                new() { Nome = "EXTERNO", Topic = "ONS.EXTERNO.SISTEMA", Notificar = true },
                new() { Nome = "DESABILITADO", Topic = "ONS.DESABILITADO.SISTEMA", Notificar = false },
                new() { Nome = "SEM_TOPICO", Topic = "", Notificar = true }
            }
        };

        _dependencyInjectorFactory.Mocker.Use(Options.Create(kafkaSettings));
    }


    [Fact(DisplayName = "[Enviar solicitação Kafka] - Deve enviar solicitação ao Kafka")]
    [Trait("Business", "Enviar solicitação Kafka Handler")]
    public async Task CadastrarNotificacaoFirebase_CadastroSolicitacao_DeveRetornarSucesso()
    {
        // Arrange
        var statusDeSolicitacao =
            new StatusDeSolicitacaoIntegracaoEnvioDto(
                "id",
                StatusDeSolicitacao.Confirmada,
                new UsuarioDto(),
                DateTime.Now,
                null,
                "GERDIN",
                [""]
            );

        var evento = new StatusDeSolicitacaoIntegracaoRecebidaEvent([statusDeSolicitacao]);

        _dependencyInjectorFactory.Mocker
            .GetMock<IProducerProvider<AtualizacaoStatusMessage>>()
            .Setup(producerProvider => producerProvider.EstaConectado())
            .Returns(true);

        var handler = _dependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Solicitacao.EventHandlers.Handlers.EnviarSolicitacaoAoKafkaEventHandler>();
        var context = new Mock<ConsumeContext<StatusDeSolicitacaoIntegracaoRecebidaEvent>>();
        context.Setup(c => c.Message).Returns(evento);
        context.Setup(c => c.CancellationToken).Returns(CancellationToken.None);

        // Act
        await handler.Consume(context.Object);

        // Assert
        _dependencyInjectorFactory.Mocker.GetMock<IProducerProvider<AtualizacaoStatusMessage>>()
            .Verify(producerProvider => producerProvider.EstaConectado(),
            Times.Once);

        _dependencyInjectorFactory.Mocker.GetMock<IProducerProvider<AtualizacaoStatusMessage>>()
            .Verify(producerProvider => producerProvider.AtualizarTopico(It.IsAny<Action<TopicoIntegrationKafka<AtualizacaoStatusMessage>>>()),
            Times.Once);

        _dependencyInjectorFactory.Mocker.GetMock<IProducerProvider<AtualizacaoStatusMessage>>()
            .Verify(producerProvider =>
                producerProvider.PublishAsync(
                    It.IsAny<AtualizacaoStatusMessage>(),
                    It.IsAny<IDictionary<string, string>>(),
                    It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact(DisplayName = "[Enviar solicitação Kafka] - Não deve enviar solicitação ao Kafka quando notificação desabilitada")]
    [Trait("Business", "Enviar solicitação Kafka Handler")]
    public async Task CadastrarNotificacaoFirebase_CadastroSolicitacao_NaoDeveEnviarAoKafka()
    {
        // Arrange
        var statusDeSolicitacao =
            new StatusDeSolicitacaoIntegracaoEnvioDto(
                "id",
                StatusDeSolicitacao.Confirmada,
                new UsuarioDto(),
                DateTime.Now,
                null,
                "DESABILITADO",
                [""]
            );

        var evento = new StatusDeSolicitacaoIntegracaoRecebidaEvent([statusDeSolicitacao]);

        _dependencyInjectorFactory.Mocker
            .GetMock<IProducerProvider<AtualizacaoStatusMessage>>()
            .Setup(producerProvider => producerProvider.EstaConectado())
            .Returns(true);

        var handler = _dependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Solicitacao.EventHandlers.Handlers.EnviarSolicitacaoAoKafkaEventHandler>();
        var context = new Mock<ConsumeContext<StatusDeSolicitacaoIntegracaoRecebidaEvent>>();
        context.Setup(c => c.Message).Returns(evento);
        context.Setup(c => c.CancellationToken).Returns(CancellationToken.None);

        // Act
        await handler.Consume(context.Object);

        // Assert
        _dependencyInjectorFactory.Mocker.GetMock<IProducerProvider<AtualizacaoStatusMessage>>()
            .Verify(producerProvider => producerProvider.EstaConectado(),
            Times.Once);

        _dependencyInjectorFactory.Mocker.GetMock<IProducerProvider<AtualizacaoStatusMessage>>()
            .Verify(producerProvider => producerProvider.AtualizarTopico(It.IsAny<Action<TopicoIntegrationKafka<AtualizacaoStatusMessage>>>()),
            Times.Never);

        _dependencyInjectorFactory.Mocker.GetMock<IProducerProvider<AtualizacaoStatusMessage>>()
            .Verify(producerProvider =>
                producerProvider.PublishAsync(
                    It.IsAny<AtualizacaoStatusMessage>(),
                    It.IsAny<IDictionary<string, string>>(),
                    It.IsAny<CancellationToken>()),
            Times.Never);
    }
}
