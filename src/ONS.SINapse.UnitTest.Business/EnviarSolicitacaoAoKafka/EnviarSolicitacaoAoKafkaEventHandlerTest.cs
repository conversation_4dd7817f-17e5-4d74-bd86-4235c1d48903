using MassTransit;
using Microsoft.Extensions.Options;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Integracao.Shared.Settings;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Kafka;
using ONS.SINapse.Shared.Kafka.Providers;
using ONS.SINapse.Solicitacao.Dtos.Integracao;
using ONS.SINapse.Solicitacao.EventHandlers.Events;
using ONS.SINapse.Solicitacao.Workers.Messages;
using ONS.SINapse.UnitTest.Business.Fixtures.Collections;
using ONS.SINapse.UnitTest.Shared;
using ONS.SINapse.UnitTest.Shared.Fixtures;

namespace ONS.SINapse.UnitTest.Business.EnviarSolicitacaoAoKafka;

[Collection(nameof(EnviarSolicitacaoAoKafkaEventHandlerCollection))]
public class EnviarSolicitacaoAoKafkaEventHandlerTest
{
    private readonly SolicitacaoFixture _solicitacaoFixture;
    private readonly MockDependencyInjectorFactory _dependencyInjectorFactory;

    public EnviarSolicitacaoAoKafkaEventHandlerTest(SolicitacaoFixture solicitacaoFixture)
    {
        _solicitacaoFixture = solicitacaoFixture;
        _dependencyInjectorFactory = new MockDependencyInjectorFactory();
        _dependencyInjectorFactory.RegisterMocks();

        ConfigurarKafkaSettings();
    }

    private void ConfigurarKafkaSettings()
    {
        // Configurar KafkaTopicsSettings para os testes
        var kafkaSettings = new KafkaTopicsSettings
        {
            Sistemas =
            [
                new() { Nome = "GERDIN", Topic = "ONS.GERDIN.SISTEMA", Notificar = true },
                new() { Nome = "EXTERNO", Topic = "ONS.EXTERNO.SISTEMA", Notificar = true },
                new() { Nome = "DESABILITADO", Topic = "ONS.DESABILITADO.SISTEMA", Notificar = false },
                new() { Nome = "SEM_TOPICO", Topic = "", Notificar = true }
            ]
        };

        _dependencyInjectorFactory.Mocker.Use(Options.Create(kafkaSettings));
    }


    [Fact(DisplayName = "[Enviar solicitação Kafka] - Deve enviar solicitação ao Kafka")]
    [Trait("Business", "Enviar solicitação Kafka Handler")]
    public async Task CadastrarNotificacaoFirebase_CadastroSolicitacao_DeveRetornarSucesso()
    {
        // Arrange
        var statusDeSolicitacao =
            new StatusDeSolicitacaoIntegracaoEnvioDto(
                "id",
                StatusDeSolicitacao.Confirmada,
                new UsuarioDto(),
                DateTime.Now,
                null,
                "GERDIN",
                [""]
            );

        var evento = new StatusDeSolicitacaoIntegracaoRecebidaEvent([statusDeSolicitacao]);

        _dependencyInjectorFactory.Mocker
            .GetMock<IProducerProvider<AtualizacaoStatusMessage>>()
            .Setup(producerProvider => producerProvider.EstaConectado())
            .Returns(true);

        var handler = _dependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Solicitacao.EventHandlers.Handlers.EnviarSolicitacaoAoKafkaEventHandler>();
        var context = new Mock<ConsumeContext<StatusDeSolicitacaoIntegracaoRecebidaEvent>>();
        context.Setup(c => c.Message).Returns(evento);
        context.Setup(c => c.CancellationToken).Returns(CancellationToken.None);

        // Act
        await handler.Consume(context.Object);

        // Assert
        _dependencyInjectorFactory.Mocker.GetMock<IProducerProvider<AtualizacaoStatusMessage>>()
            .Verify(producerProvider => producerProvider.EstaConectado(),
            Times.Once);

        _dependencyInjectorFactory.Mocker.GetMock<IProducerProvider<AtualizacaoStatusMessage>>()
            .Verify(producerProvider => producerProvider.AtualizarTopico(It.IsAny<Action<TopicoIntegrationKafka<AtualizacaoStatusMessage>>>()),
            Times.Once);

        _dependencyInjectorFactory.Mocker.GetMock<IProducerProvider<AtualizacaoStatusMessage>>()
            .Verify(producerProvider =>
                producerProvider.PublishAsync(
                    It.IsAny<AtualizacaoStatusMessage>(),
                    It.IsAny<IDictionary<string, string>>(),
                    It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact(DisplayName = "[Enviar solicitação Kafka] - Não deve enviar solicitação ao Kafka quando notificação desabilitada")]
    [Trait("Business", "Enviar solicitação Kafka Handler")]
    public async Task CadastrarNotificacaoFirebase_CadastroSolicitacao_NaoDeveEnviarAoKafka()
    {
        // Arrange
        var statusDeSolicitacao =
            new StatusDeSolicitacaoIntegracaoEnvioDto(
                "id",
                StatusDeSolicitacao.Confirmada,
                new UsuarioDto(),
                DateTime.Now,
                null,
                "DESABILITADO",
                [""]
            );

        var evento = new StatusDeSolicitacaoIntegracaoRecebidaEvent([statusDeSolicitacao]);

        _dependencyInjectorFactory.Mocker
            .GetMock<IProducerProvider<AtualizacaoStatusMessage>>()
            .Setup(producerProvider => producerProvider.EstaConectado())
            .Returns(true);

        var handler = _dependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Solicitacao.EventHandlers.Handlers.EnviarSolicitacaoAoKafkaEventHandler>();
        var context = new Mock<ConsumeContext<StatusDeSolicitacaoIntegracaoRecebidaEvent>>();
        context.Setup(c => c.Message).Returns(evento);
        context.Setup(c => c.CancellationToken).Returns(CancellationToken.None);

        // Act
        await handler.Consume(context.Object);

        // Assert
        _dependencyInjectorFactory.Mocker.GetMock<IProducerProvider<AtualizacaoStatusMessage>>()
            .Verify(producerProvider => producerProvider.EstaConectado(),
            Times.Once);

        _dependencyInjectorFactory.Mocker.GetMock<IProducerProvider<AtualizacaoStatusMessage>>()
            .Verify(producerProvider => producerProvider.AtualizarTopico(It.IsAny<Action<TopicoIntegrationKafka<AtualizacaoStatusMessage>>>()),
            Times.Never);

        _dependencyInjectorFactory.Mocker.GetMock<IProducerProvider<AtualizacaoStatusMessage>>()
            .Verify(producerProvider =>
                producerProvider.PublishAsync(
                    It.IsAny<AtualizacaoStatusMessage>(),
                    It.IsAny<IDictionary<string, string>>(),
                    It.IsAny<CancellationToken>()),
            Times.Never);
    }

    [Fact(DisplayName = "[Enviar solicitação Kafka] - Não deve publicar quando sistema não está configurado")]
    [Trait("Business", "Enviar solicitação Kafka Handler")]
    public async Task DeveIgnorarSistemaNaoConfigurado()
    {
        // Arrange
        var statusDeSolicitacao = new StatusDeSolicitacaoIntegracaoEnvioDto(
            "id",
            StatusDeSolicitacao.Confirmada,
            new UsuarioDto(),
            DateTime.Now,
            null,
            "SISTEMA_INEXISTENTE",
            [""]
        );

        var evento = new StatusDeSolicitacaoIntegracaoRecebidaEvent([statusDeSolicitacao]);

        _dependencyInjectorFactory.Mocker
            .GetMock<IProducerProvider<AtualizacaoStatusMessage>>()
            .Setup(producerProvider => producerProvider.EstaConectado())
            .Returns(true);

        var handler = _dependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Solicitacao.EventHandlers.Handlers.EnviarSolicitacaoAoKafkaEventHandler>();
        var context = new Mock<ConsumeContext<StatusDeSolicitacaoIntegracaoRecebidaEvent>>();
        context.Setup(c => c.Message).Returns(evento);
        context.Setup(c => c.CancellationToken).Returns(CancellationToken.None);

        // Act
        await handler.Consume(context.Object);

        // Assert
        _dependencyInjectorFactory.Mocker.GetMock<IProducerProvider<AtualizacaoStatusMessage>>()
            .Verify(producerProvider => producerProvider.EstaConectado(), Times.Once);

        _dependencyInjectorFactory.Mocker.GetMock<IProducerProvider<AtualizacaoStatusMessage>>()
            .Verify(producerProvider => producerProvider.PublishAsync(
                It.IsAny<AtualizacaoStatusMessage>(),
                It.IsAny<IDictionary<string, string>>(),
                It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact(DisplayName = "[Enviar solicitação Kafka] - Não deve publicar quando producer não está conectado")]
    [Trait("Business", "Enviar solicitação Kafka Handler")]
    public async Task NaoDevePublicarQuandoProducerNaoEstaConectado()
    {
        // Arrange
        var statusDeSolicitacao = new StatusDeSolicitacaoIntegracaoEnvioDto(
            "id",
            StatusDeSolicitacao.Confirmada,
            new UsuarioDto(),
            DateTime.Now,
            null,
            "GERDIN",
            [""]
        );

        var evento = new StatusDeSolicitacaoIntegracaoRecebidaEvent([statusDeSolicitacao]);

        _dependencyInjectorFactory.Mocker
            .GetMock<IProducerProvider<AtualizacaoStatusMessage>>()
            .Setup(producerProvider => producerProvider.EstaConectado())
            .Returns(false);

        var handler = _dependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Solicitacao.EventHandlers.Handlers.EnviarSolicitacaoAoKafkaEventHandler>();
        var context = new Mock<ConsumeContext<StatusDeSolicitacaoIntegracaoRecebidaEvent>>();
        context.Setup(c => c.Message).Returns(evento);
        context.Setup(c => c.CancellationToken).Returns(CancellationToken.None);

        // Act
        await handler.Consume(context.Object);

        // Assert
        _dependencyInjectorFactory.Mocker.GetMock<IProducerProvider<AtualizacaoStatusMessage>>()
            .Verify(producerProvider => producerProvider.EstaConectado(), Times.Once);

        _dependencyInjectorFactory.Mocker.GetMock<IProducerProvider<AtualizacaoStatusMessage>>()
            .Verify(producerProvider => producerProvider.AtualizarTopico(It.IsAny<Action<TopicoIntegrationKafka<AtualizacaoStatusMessage>>>()), Times.Never);

        _dependencyInjectorFactory.Mocker.GetMock<IProducerProvider<AtualizacaoStatusMessage>>()
            .Verify(producerProvider => producerProvider.PublishAsync(
                It.IsAny<AtualizacaoStatusMessage>(),
                It.IsAny<IDictionary<string, string>>(),
                It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact(DisplayName = "[Enviar solicitação Kafka] - Não deve publicar quando tópico está vazio")]
    [Trait("Business", "Enviar solicitação Kafka Handler")]
    public async Task NaoDevePublicarQuandoTopicoEstaVazio()
    {
        // Arrange
        var statusDeSolicitacao = new StatusDeSolicitacaoIntegracaoEnvioDto(
            "id",
            StatusDeSolicitacao.Confirmada,
            new UsuarioDto(),
            DateTime.Now,
            null,
            "SEM_TOPICO",
            [""]
        );

        var evento = new StatusDeSolicitacaoIntegracaoRecebidaEvent([statusDeSolicitacao]);

        _dependencyInjectorFactory.Mocker
            .GetMock<IProducerProvider<AtualizacaoStatusMessage>>()
            .Setup(producerProvider => producerProvider.EstaConectado())
            .Returns(true);

        var handler = _dependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Solicitacao.EventHandlers.Handlers.EnviarSolicitacaoAoKafkaEventHandler>();
        var context = new Mock<ConsumeContext<StatusDeSolicitacaoIntegracaoRecebidaEvent>>();
        context.Setup(c => c.Message).Returns(evento);
        context.Setup(c => c.CancellationToken).Returns(CancellationToken.None);

        // Act
        await handler.Consume(context.Object);

        // Assert
        _dependencyInjectorFactory.Mocker.GetMock<IProducerProvider<AtualizacaoStatusMessage>>()
            .Verify(producerProvider => producerProvider.EstaConectado(), Times.Once);

        _dependencyInjectorFactory.Mocker.GetMock<IProducerProvider<AtualizacaoStatusMessage>>()
            .Verify(producerProvider => producerProvider.PublishAsync(
                It.IsAny<AtualizacaoStatusMessage>(),
                It.IsAny<IDictionary<string, string>>(),
                It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact(DisplayName = "[Enviar solicitação Kafka] - Deve processar múltiplas solicitações")]
    [Trait("Business", "Enviar solicitação Kafka Handler")]
    public async Task DeveProcessarMultiplasSolicitacoes()
    {
        // Arrange
        var statusDeSolicitacao1 = new StatusDeSolicitacaoIntegracaoEnvioDto(
            "id1",
            StatusDeSolicitacao.Confirmada,
            new UsuarioDto(),
            DateTime.Now,
            null,
            "GERDIN",
            [""]
        );

        var statusDeSolicitacao2 = new StatusDeSolicitacaoIntegracaoEnvioDto(
            "id2",
            StatusDeSolicitacao.Cancelada,
            new UsuarioDto(),
            DateTime.Now,
            null,
            "EXTERNO",
            [""]
        );

        var statusDeSolicitacao3 = new StatusDeSolicitacaoIntegracaoEnvioDto(
            "id3",
            StatusDeSolicitacao.Finalizada,
            new UsuarioDto(),
            DateTime.Now,
            null,
            "DESABILITADO",
            [""]
        );

        var evento = new StatusDeSolicitacaoIntegracaoRecebidaEvent([statusDeSolicitacao1, statusDeSolicitacao2, statusDeSolicitacao3]);

        _dependencyInjectorFactory.Mocker
            .GetMock<IProducerProvider<AtualizacaoStatusMessage>>()
            .Setup(producerProvider => producerProvider.EstaConectado())
            .Returns(true);

        var handler = _dependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Solicitacao.EventHandlers.Handlers.EnviarSolicitacaoAoKafkaEventHandler>();
        var context = new Mock<ConsumeContext<StatusDeSolicitacaoIntegracaoRecebidaEvent>>();
        context.Setup(c => c.Message).Returns(evento);
        context.Setup(c => c.CancellationToken).Returns(CancellationToken.None);

        // Act
        await handler.Consume(context.Object);

        // Assert
        _dependencyInjectorFactory.Mocker.GetMock<IProducerProvider<AtualizacaoStatusMessage>>()
            .Verify(producerProvider => producerProvider.EstaConectado(), Times.Once);

        // Agora o handler processa apenas sistemas habilitados (GERDIN e EXTERNO), ignora DESABILITADO
        _dependencyInjectorFactory.Mocker.GetMock<IProducerProvider<AtualizacaoStatusMessage>>()
            .Verify(producerProvider => producerProvider.AtualizarTopico(It.IsAny<Action<TopicoIntegrationKafka<AtualizacaoStatusMessage>>>()), Times.Exactly(2));

        // E publica apenas para os 2 sistemas habilitados (GERDIN e EXTERNO)
        _dependencyInjectorFactory.Mocker.GetMock<IProducerProvider<AtualizacaoStatusMessage>>()
            .Verify(producerProvider => producerProvider.PublishAsync(
                It.IsAny<AtualizacaoStatusMessage>(),
                It.IsAny<IDictionary<string, string>>(),
                It.IsAny<CancellationToken>()), Times.Exactly(2));
    }

    [Theory(DisplayName = "[Enviar solicitação Kafka] - Deve processar diferentes status de solicitação")]
    [Trait("Business", "Enviar solicitação Kafka Handler")]
    [InlineData(StatusDeSolicitacao.Pendente)]
    [InlineData(StatusDeSolicitacao.Confirmada)]
    [InlineData(StatusDeSolicitacao.Cancelada)]
    [InlineData(StatusDeSolicitacao.Finalizada)]
    [InlineData(StatusDeSolicitacao.Impedida)]
    public async Task DeveProcessarDiferentesStatusDeSolicitacao(StatusDeSolicitacao status)
    {
        // Arrange
        var statusDeSolicitacao = new StatusDeSolicitacaoIntegracaoEnvioDto(
            "id",
            status,
            new UsuarioDto(),
            DateTime.Now,
            null,
            "GERDIN",
            [""]
        );

        var evento = new StatusDeSolicitacaoIntegracaoRecebidaEvent([statusDeSolicitacao]);

        _dependencyInjectorFactory.Mocker
            .GetMock<IProducerProvider<AtualizacaoStatusMessage>>()
            .Setup(producerProvider => producerProvider.EstaConectado())
            .Returns(true);

        var handler = _dependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Solicitacao.EventHandlers.Handlers.EnviarSolicitacaoAoKafkaEventHandler>();
        var context = new Mock<ConsumeContext<StatusDeSolicitacaoIntegracaoRecebidaEvent>>();
        context.Setup(c => c.Message).Returns(evento);
        context.Setup(c => c.CancellationToken).Returns(CancellationToken.None);

        // Act
        await handler.Consume(context.Object);

        // Assert
        _dependencyInjectorFactory.Mocker.GetMock<IProducerProvider<AtualizacaoStatusMessage>>()
            .Verify(producerProvider => producerProvider.EstaConectado(), Times.Once);

        _dependencyInjectorFactory.Mocker.GetMock<IProducerProvider<AtualizacaoStatusMessage>>()
            .Verify(producerProvider => producerProvider.AtualizarTopico(It.IsAny<Action<TopicoIntegrationKafka<AtualizacaoStatusMessage>>>()), Times.Once);

        _dependencyInjectorFactory.Mocker.GetMock<IProducerProvider<AtualizacaoStatusMessage>>()
            .Verify(producerProvider => producerProvider.PublishAsync(
                It.IsAny<AtualizacaoStatusMessage>(),
                It.IsAny<IDictionary<string, string>>(),
                It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact(DisplayName = "[Enviar solicitação Kafka] - Deve tratar exceção durante publicação")]
    [Trait("Business", "Enviar solicitação Kafka Handler")]
    public async Task DeveTratarExcecaoDurantePublicacao()
    {
        // Arrange
        var statusDeSolicitacao = new StatusDeSolicitacaoIntegracaoEnvioDto(
            "id",
            StatusDeSolicitacao.Confirmada,
            new UsuarioDto(),
            DateTime.Now,
            null,
            "GERDIN",
            [""]
        );

        var evento = new StatusDeSolicitacaoIntegracaoRecebidaEvent([statusDeSolicitacao]);

        _dependencyInjectorFactory.Mocker
            .GetMock<IProducerProvider<AtualizacaoStatusMessage>>()
            .Setup(producerProvider => producerProvider.EstaConectado())
            .Returns(true);

        _dependencyInjectorFactory.Mocker
            .GetMock<IProducerProvider<AtualizacaoStatusMessage>>()
            .Setup(producerProvider => producerProvider.PublishAsync(
                It.IsAny<AtualizacaoStatusMessage>(),
                It.IsAny<IDictionary<string, string>>(),
                It.IsAny<CancellationToken>()))
            .ThrowsAsync(new Exception("Erro de conexão com Kafka"));

        var handler = _dependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Solicitacao.EventHandlers.Handlers.EnviarSolicitacaoAoKafkaEventHandler>();
        var context = new Mock<ConsumeContext<StatusDeSolicitacaoIntegracaoRecebidaEvent>>();
        context.Setup(c => c.Message).Returns(evento);
        context.Setup(c => c.CancellationToken).Returns(CancellationToken.None);

        // Act & Assert
        var exception = await Should.ThrowAsync<Exception>(async () => await handler.Consume(context.Object));
        exception.Message.ShouldBe("Erro de conexão com Kafka");

        _dependencyInjectorFactory.Mocker.GetMock<IProducerProvider<AtualizacaoStatusMessage>>()
            .Verify(producerProvider => producerProvider.EstaConectado(), Times.Once);

        _dependencyInjectorFactory.Mocker.GetMock<IProducerProvider<AtualizacaoStatusMessage>>()
            .Verify(producerProvider => producerProvider.AtualizarTopico(It.IsAny<Action<TopicoIntegrationKafka<AtualizacaoStatusMessage>>>()), Times.Once);

        _dependencyInjectorFactory.Mocker.GetMock<IProducerProvider<AtualizacaoStatusMessage>>()
            .Verify(producerProvider => producerProvider.PublishAsync(
                It.IsAny<AtualizacaoStatusMessage>(),
                It.IsAny<IDictionary<string, string>>(),
                It.IsAny<CancellationToken>()), Times.Once);
    }
}
