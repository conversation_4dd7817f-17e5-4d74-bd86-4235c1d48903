using Microsoft.Extensions.DependencyInjection;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Kafka;
using ONS.SINapse.Shared.Kafka.Providers;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Solicitacao.Dtos.Integracao;
using ONS.SINapse.Solicitacao.EventHandlers.Events;
using ONS.SINapse.Solicitacao.Workers.Messages;
using ONS.SINapse.UnitTest.Business.Fixtures.Collections;
using ONS.SINapse.UnitTest.Shared;
using ONS.SINapse.UnitTest.Shared.Fixtures;

namespace ONS.SINapse.UnitTest.Business.EnviarSolicitacaoAoKafka;

[Collection(nameof(EnviarSolicitacaoAoKafkaEventHandlerCollection))]
public class EnviarSolicitacaoAoKafkaEventHandlerTest
{
    private readonly SolicitacaoFixture _solicitacaoFixture;
    private readonly MockDependencyInjectorFactory _dependencyInjectorFactory;

    public EnviarSolicitacaoAoKafkaEventHandlerTest(SolicitacaoFixture solicitacaoFixture)
    {
        _solicitacaoFixture = solicitacaoFixture;
        _dependencyInjectorFactory = new MockDependencyInjectorFactory();
        _dependencyInjectorFactory.RegisterMocks();
    }


    [Fact(DisplayName = "[Enviar solicitação Kafka] - Deve enviar solicitação ao Kafka")]
    [Trait("Business", "Enviar solicitação Kafka Handler")]
    public async Task CadastrarNotificacaoFirebase_CadastroSolicitacao_DeveRetornarSucesso()
    {
        // Arrange
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoPendente();
        var statusDeSolicitacao = 
            new StatusDeSolicitacaoIntegracaoEnvioDto(
                "id",
                StatusDeSolicitacao.Confirmada,
                new UsuarioDto(),
                DateTime.Now,
                null,
                "Externo",
                [""]
            );

        var evento = new StatusDeSolicitacaoIntegracaoRecebidaEvent([statusDeSolicitacao]);

        _dependencyInjectorFactory.Mocker
            .GetMock<IProducerProvider<AtualizacaoStatusMessage>>()
            .Setup(producerProvider => producerProvider.EstaConectado())
            .Returns(true);

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        // Act

        await mediator.PublicarEventoAsync(evento, CancellationToken.None);

        // Assert

        var eventoPublicado = _dependencyInjectorFactory.Harness.Published.Any<StatusDeSolicitacaoIntegracaoRecebidaEvent>();
        var eventoConsumido = _dependencyInjectorFactory.Harness.Consumed.Any<StatusDeSolicitacaoIntegracaoRecebidaEvent>();
        await Task.WhenAll(eventoPublicado, eventoConsumido);

        eventoPublicado.Result.ShouldBeTrue();
        eventoConsumido.Result.ShouldBeTrue();

        _dependencyInjectorFactory.Mocker.GetMock<IProducerProvider<AtualizacaoStatusMessage>>()
            .Verify(producerProvider => producerProvider.AtualizarTopico(It.IsAny<Action<TopicoIntegrationKafka<AtualizacaoStatusMessage>>>()),
            Times.Once);

        _dependencyInjectorFactory.Mocker.GetMock<IProducerProvider<AtualizacaoStatusMessage>>()
            .Verify(producerProvider =>
                producerProvider.PublishAsync(
                    It.IsAny<AtualizacaoStatusMessage>(),
                    It.IsAny<IDictionary<string, string>>(),
                    It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact(DisplayName = "[Enviar solicitação Kafka] - Não deve enviar solicitação ao Kafka")]
    [Trait("Business", "Enviar solicitação Kafka Handler")]
    public async Task CadastrarNotificacaoFirebase_CadastroSolicitacao_NaoDeveEnviarAoKafka()
    {
        // Arrange
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoPendente();
        var statusDeSolicitacao =
            new StatusDeSolicitacaoIntegracaoEnvioDto(
                "id",
                StatusDeSolicitacao.Confirmada,
                new UsuarioDto(),
                DateTime.Now,
                null,
                "Externo",
                [""]
            );

        var evento = new StatusDeSolicitacaoIntegracaoRecebidaEvent([statusDeSolicitacao]);

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        // Act

        await mediator.PublicarEventoAsync(evento, CancellationToken.None);

        // Assert

        var eventoPublicado = _dependencyInjectorFactory.Harness.Published.Any<StatusDeSolicitacaoIntegracaoRecebidaEvent>();
        var eventoConsumido = _dependencyInjectorFactory.Harness.Consumed.Any<StatusDeSolicitacaoIntegracaoRecebidaEvent>();
        await Task.WhenAll(eventoPublicado, eventoConsumido);

        eventoPublicado.Result.ShouldBeTrue();
        eventoConsumido.Result.ShouldBeTrue();

        _dependencyInjectorFactory.Mocker.GetMock<IProducerProvider<AtualizacaoStatusMessage>>()
            .Verify(producerProvider => producerProvider.EstaConectado(),
            Times.Once);

        _dependencyInjectorFactory.Mocker.GetMock<IProducerProvider<AtualizacaoStatusMessage>>()
            .Verify(producerProvider => producerProvider.AtualizarTopico(It.IsAny<Action<TopicoIntegrationKafka<AtualizacaoStatusMessage>>>()),
            Times.Never);

        _dependencyInjectorFactory.Mocker.GetMock<IProducerProvider<AtualizacaoStatusMessage>>()
            .Verify(producerProvider =>
                producerProvider.PublishAsync(
                    It.IsAny<AtualizacaoStatusMessage>(),
                    It.IsAny<IDictionary<string, string>>(),
                    It.IsAny<CancellationToken>()),
            Times.Never);
    }
}
