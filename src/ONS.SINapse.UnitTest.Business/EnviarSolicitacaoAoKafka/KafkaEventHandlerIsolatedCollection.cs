using ONS.SINapse.UnitTest.Shared.Fixtures;

namespace ONS.SINapse.UnitTest.Business.EnviarSolicitacaoAoKafka;

[CollectionDefinition("KafkaEventHandlerIsolated", DisableParallelization = true)]
public class KafkaEventHandlerIsolatedCollection : ICollectionFixture<SolicitacaoFixture>
{
    // Esta classe não possui código, apenas serve para definir a collection
    // e especificar que ela usa a SolicitacaoFixture
    // DisableParallelization = true garante que este teste não execute em paralelo com outros
}
