using ONS.SINapse.Business.IBusiness;
using ONS.SINapse.Shared.DTO.Firebase;
using ONS.SINapse.Shared.Identity;
using ONS.SINapse.Shared.Services.Firebase;
using ONS.SINapse.UnitTest.Shared.Identity;

namespace ONS.SINapse.UnitTest.Business.AlertaSonoroBusiness;

public class AlertaSonoroBusinessTest
{
    private readonly Mock<IAlertaSonoroFirebaseDatabaseService> _mockFirebaseService;
    private readonly IUserContextTest _userContext;
    private readonly IAlertaSonoroBusiness _alertaSonoroBusiness;

    public AlertaSonoroBusinessTest()
    {
        _mockFirebaseService = new Mock<IAlertaSonoroFirebaseDatabaseService>();
        var perfil = new Perfil(new List<Scope>(), new List<string>(), "Operador", "operador");
        _userContext = new UserContextTest("token", "user-123", "<PERSON>", "joao.silva", perfil);
        _alertaSonoroBusiness = new ONS.SINapse.Business.Imp.Business.AlertaSonoroBusiness(_userContext, _mockFirebaseService.Object);
    }

    [Fact(DisplayName = "[AlertaSonoroBusiness] - Deve ativar alerta sonoro para usuário com scopes")]
    [Trait("Business", "AlertaSonoroBusiness")]
    public async Task AtivarAsync_UsuarioComScopes_DeveAtivarAlertaSonoro()
    {
        // Arrange
        var scopes = new List<Scope>
        {
            new("CENTROS", "NE", "Nordeste"),
            new("CENTROS", "SE", "Sudeste")
        };
        
        var perfil = new Perfil(scopes, new List<string>(), "Operador", "operador");
        _userContext.DefinirPerfil(perfil);

        var cancellationToken = CancellationToken.None;

        // Act
        await _alertaSonoroBusiness.AtivarAsync(cancellationToken);

        // Assert
        _mockFirebaseService.Verify(
            service => service.SetAsync(
                It.Is<IDictionary<string, AlertaSonoroRealtimeDto>>(dict =>
                    dict.Count == 2 &&
                    dict.ContainsKey("ne") &&
                    dict.ContainsKey("se") &&
                    dict["ne"].Sid == "user-123" &&
                    dict["ne"].Usuario == "João Silva" &&
                    dict["ne"].Ativo == true &&
                    dict["se"].Sid == "user-123" &&
                    dict["se"].Usuario == "João Silva" &&
                    dict["se"].Ativo == true),
                cancellationToken),
            Times.Once);
    }

    [Fact(DisplayName = "[AlertaSonoroBusiness] - Deve desativar alerta sonoro para usuário com scopes")]
    [Trait("Business", "AlertaSonoroBusiness")]
    public async Task DesativarAsync_UsuarioComScopes_DeveDesativarAlertaSonoro()
    {
        // Arrange
        var scopes = new List<Scope>
        {
            new("CENTROS", "SUL", "Sul"),
            new("CENTROS", "NORTE", "Norte")
        };
        
        var perfil = new Perfil(scopes, new List<string>(), "Operador", "operador");
        _userContext.DefinirPerfil(perfil);

        var cancellationToken = CancellationToken.None;

        // Act
        await _alertaSonoroBusiness.DesativarAsync(cancellationToken);

        // Assert
        _mockFirebaseService.Verify(
            service => service.SetAsync(
                It.Is<IDictionary<string, AlertaSonoroRealtimeDto>>(dict =>
                    dict.Count == 2 &&
                    dict.ContainsKey("sul") &&
                    dict.ContainsKey("norte") &&
                    dict["sul"].Sid == "user-123" &&
                    dict["sul"].Usuario == "João Silva" &&
                    dict["sul"].Ativo == false &&
                    dict["norte"].Sid == "user-123" &&
                    dict["norte"].Usuario == "João Silva" &&
                    dict["norte"].Ativo == false),
                cancellationToken),
            Times.Once);
    }

    [Fact(DisplayName = "[AlertaSonoroBusiness] - Não deve processar quando usuário não possui scopes")]
    [Trait("Business", "AlertaSonoroBusiness")]
    public async Task AtivarAsync_UsuarioSemScopes_NaoDeveProcessar()
    {
        // Arrange
        var perfil = new Perfil(new List<Scope>(), new List<string>(), "Operador", "operador");
        _userContext.DefinirPerfil(perfil);

        var cancellationToken = CancellationToken.None;

        // Act
        await _alertaSonoroBusiness.AtivarAsync(cancellationToken);

        // Assert
        _mockFirebaseService.Verify(
            service => service.SetAsync(It.IsAny<IDictionary<string, AlertaSonoroRealtimeDto>>(), It.IsAny<CancellationToken>()),
            Times.Never);
    }

    [Fact(DisplayName = "[AlertaSonoroBusiness] - Não deve processar quando usuário não possui scopes na desativação")]
    [Trait("Business", "AlertaSonoroBusiness")]
    public async Task DesativarAsync_UsuarioSemScopes_NaoDeveProcessar()
    {
        // Arrange
        var perfil = new Perfil(new List<Scope>(), new List<string>(), "Operador", "operador");
        _userContext.DefinirPerfil(perfil);

        var cancellationToken = CancellationToken.None;

        // Act
        await _alertaSonoroBusiness.DesativarAsync(cancellationToken);

        // Assert
        _mockFirebaseService.Verify(
            service => service.SetAsync(It.IsAny<IDictionary<string, AlertaSonoroRealtimeDto>>(), It.IsAny<CancellationToken>()),
            Times.Never);
    }

    [Fact(DisplayName = "[AlertaSonoroBusiness] - Deve processar scope único corretamente")]
    [Trait("Business", "AlertaSonoroBusiness")]
    public async Task AtivarAsync_UsuarioComScopeUnico_DeveProcessarCorretamente()
    {
        // Arrange
        var scopes = new List<Scope>
        {
            new("CENTROS", "CENTRO-OESTE", "Centro-Oeste")
        };

        var perfil = new Perfil(scopes, new List<string>(), "Supervisor", "supervisor");
        _userContext.DefinirPerfil(perfil);

        var cancellationToken = CancellationToken.None;

        // Act
        await _alertaSonoroBusiness.AtivarAsync(cancellationToken);

        // Assert
        _mockFirebaseService.Verify(
            service => service.SetAsync(
                It.Is<IDictionary<string, AlertaSonoroRealtimeDto>>(dict =>
                    dict.Count == 1 &&
                    dict.ContainsKey("centro-oeste") &&
                    dict["centro-oeste"].Sid == "user-123" &&
                    dict["centro-oeste"].Usuario == "João Silva" &&
                    dict["centro-oeste"].Ativo == true),
                cancellationToken),
            Times.Once);
    }

    [Fact(DisplayName = "[AlertaSonoroBusiness] - Deve converter códigos de scope para lowercase")]
    [Trait("Business", "AlertaSonoroBusiness")]
    public async Task AtivarAsync_ScopesComMaiusculas_DeveConverterParaLowercase()
    {
        // Arrange
        var scopes = new List<Scope>
        {
            new("CENTROS", "NORDESTE", "Nordeste"),
            new("CENTROS", "SUDESTE", "Sudeste")
        };

        var perfil = new Perfil(scopes, new List<string>(), "Operador", "operador");
        _userContext.DefinirPerfil(perfil);

        var cancellationToken = CancellationToken.None;

        // Act
        await _alertaSonoroBusiness.AtivarAsync(cancellationToken);

        // Assert
        _mockFirebaseService.Verify(
            service => service.SetAsync(
                It.Is<IDictionary<string, AlertaSonoroRealtimeDto>>(dict =>
                    dict.ContainsKey("nordeste") &&
                    dict.ContainsKey("sudeste") &&
                    !dict.ContainsKey("NORDESTE") &&
                    !dict.ContainsKey("SUDESTE")),
                cancellationToken),
            Times.Once);
    }

    [Theory(DisplayName = "[AlertaSonoroBusiness] - Deve processar diferentes quantidades de scopes")]
    [Trait("Business", "AlertaSonoroBusiness")]
    [InlineData(1)]
    [InlineData(3)]
    [InlineData(5)]
    public async Task AtivarAsync_DiferentesQuantidadesDeScopes_DeveProcessarCorretamente(int quantidadeScopes)
    {
        // Arrange
        var scopes = new List<Scope>();
        for (int i = 1; i <= quantidadeScopes; i++)
        {
            scopes.Add(new Scope("CENTROS", $"SCOPE-{i}", $"Scope {i}"));
        }

        var perfil = new Perfil(scopes, new List<string>(), "Operador", "operador");
        _userContext.DefinirPerfil(perfil);

        var cancellationToken = CancellationToken.None;

        // Act
        await _alertaSonoroBusiness.AtivarAsync(cancellationToken);

        // Assert
        _mockFirebaseService.Verify(
            service => service.SetAsync(
                It.Is<IDictionary<string, AlertaSonoroRealtimeDto>>(dict =>
                    dict.Count == quantidadeScopes &&
                    dict.All(kvp => kvp.Value.Sid == "user-123") &&
                    dict.All(kvp => kvp.Value.Usuario == "João Silva") &&
                    dict.All(kvp => kvp.Value.Ativo == true)),
                cancellationToken),
            Times.Once);
    }

    [Fact(DisplayName = "[AlertaSonoroBusiness] - Deve definir DataDeAtualizacao próxima ao momento atual")]
    [Trait("Business", "AlertaSonoroBusiness")]
    public async Task AtivarAsync_DeveDefinirDataDeAtualizacaoCorreta()
    {
        // Arrange
        var scopes = new List<Scope>
        {
            new("CENTROS", "TEST", "Teste")
        };

        var perfil = new Perfil(scopes, new List<string>(), "Operador", "operador");
        _userContext.DefinirPerfil(perfil);

        var dataAntes = DateTime.UtcNow;
        var cancellationToken = CancellationToken.None;

        // Act
        await _alertaSonoroBusiness.AtivarAsync(cancellationToken);
        var dataDepois = DateTime.UtcNow;

        // Assert
        _mockFirebaseService.Verify(
            service => service.SetAsync(
                It.Is<IDictionary<string, AlertaSonoroRealtimeDto>>(dict =>
                    dict["test"].DataDeAtualizacao >= dataAntes &&
                    dict["test"].DataDeAtualizacao <= dataDepois),
                cancellationToken),
            Times.Once);
    }

    [Fact(DisplayName = "[AlertaSonoroBusiness] - Deve propagar CancellationToken corretamente")]
    [Trait("Business", "AlertaSonoroBusiness")]
    public async Task AtivarAsync_DevePropagarCancellationToken()
    {
        // Arrange
        var scopes = new List<Scope>
        {
            new("CENTROS", "TOKEN-TEST", "Token Test")
        };

        var perfil = new Perfil(scopes, new List<string>(), "Operador", "operador");
        _userContext.DefinirPerfil(perfil);

        var cancellationTokenSource = new CancellationTokenSource();
        var cancellationToken = cancellationTokenSource.Token;

        // Act
        await _alertaSonoroBusiness.AtivarAsync(cancellationToken);

        // Assert
        _mockFirebaseService.Verify(
            service => service.SetAsync(
                It.IsAny<IDictionary<string, AlertaSonoroRealtimeDto>>(),
                cancellationToken),
            Times.Once);
    }
}
