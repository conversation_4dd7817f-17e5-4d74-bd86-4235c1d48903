using ONS.SINapse.Business.IBusiness;
using ONS.SINapse.Shared.DTO.Firebase;
using ONS.SINapse.Shared.Identity;
using ONS.SINapse.Shared.Services.Firebase;
using ONS.SINapse.UnitTest.Shared.Fixtures;
using ONS.SINapse.UnitTest.Shared.Identity;

namespace ONS.SINapse.UnitTest.Business.AlertaSonoroBusiness;

public partial class AlertaSonoroBusinessTest
{
    [Fact(DisplayName = "[AlertaSonoroBusiness] - Deve ativar alerta sonoro para usuário com scopes")]
    [Trait("Business", "AlertaSonoroBusiness")]
    public async Task AtivarAsync_UsuarioComScopes_DeveAtivarAlertaSonoro()
    {
        // Arrange
        var scopes = AlertaSonoroFixture.GerarScopesValidos();
        var perfil = AlertaSonoroFixture.GerarPerfilComScopes(scopes);
        
        DependencyInjectorFactory.Mocker.Get<IUserContextTest>().DefinirPerfil(perfil);
        
        var cancellationToken = CancellationToken.None;

        // Act
        await DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.AlertaSonoroBusiness>()
            .AtivarAsync(cancellationToken);

        // Assert
        DependencyInjectorFactory.Mocker.GetMock<IAlertaSonoroFirebaseDatabaseService>().Verify(
            service => service.SetAsync(
                It.Is<IDictionary<string, AlertaSonoroRealtimeDto>>(dict =>
                    dict.Count == 3 &&
                    dict.ContainsKey("ne") &&
                    dict.ContainsKey("se") &&
                    dict.ContainsKey("sul") &&
                    dict.All(kvp => kvp.Value.Ativo == true)),
                cancellationToken),
            Times.Once);
    }

    [Fact(DisplayName = "[AlertaSonoroBusiness] - Deve desativar alerta sonoro para usuário com scopes")]
    [Trait("Business", "AlertaSonoroBusiness")]
    public async Task DesativarAsync_UsuarioComScopes_DeveDesativarAlertaSonoro()
    {
        // Arrange
        var scopes = AlertaSonoroFixture.GerarScopesValidos();
        var perfil = AlertaSonoroFixture.GerarPerfilComScopes(scopes);
        
        DependencyInjectorFactory.Mocker.Get<IUserContextTest>().DefinirPerfil(perfil);
        
        var cancellationToken = CancellationToken.None;

        // Act
        await DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.AlertaSonoroBusiness>()
            .DesativarAsync(cancellationToken);

        // Assert
        DependencyInjectorFactory.Mocker.GetMock<IAlertaSonoroFirebaseDatabaseService>().Verify(
            service => service.SetAsync(
                It.Is<IDictionary<string, AlertaSonoroRealtimeDto>>(dict =>
                    dict.Count == 3 &&
                    dict.ContainsKey("ne") &&
                    dict.ContainsKey("se") &&
                    dict.ContainsKey("sul") &&
                    dict.All(kvp => kvp.Value.Ativo == false)),
                cancellationToken),
            Times.Once);
    }

    [Fact(DisplayName = "[AlertaSonoroBusiness] - Não deve processar quando usuário não possui scopes")]
    [Trait("Business", "AlertaSonoroBusiness")]
    public async Task AtivarAsync_UsuarioSemScopes_NaoDeveProcessar()
    {
        // Arrange
        var scopes = AlertaSonoroFixture.GerarScopesVazios();
        var perfil = AlertaSonoroFixture.GerarPerfilComScopes(scopes);
        
        DependencyInjectorFactory.Mocker.Get<IUserContextTest>().DefinirPerfil(perfil);
        
        var cancellationToken = CancellationToken.None;

        // Act
        await DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.AlertaSonoroBusiness>()
            .AtivarAsync(cancellationToken);

        // Assert
        DependencyInjectorFactory.Mocker.GetMock<IAlertaSonoroFirebaseDatabaseService>().Verify(
            service => service.SetAsync(It.IsAny<IDictionary<string, AlertaSonoroRealtimeDto>>(), It.IsAny<CancellationToken>()),
            Times.Never);
    }

    [Fact(DisplayName = "[AlertaSonoroBusiness] - Não deve processar quando usuário não possui scopes na desativação")]
    [Trait("Business", "AlertaSonoroBusiness")]
    public async Task DesativarAsync_UsuarioSemScopes_NaoDeveProcessar()
    {
        // Arrange
        var scopes = AlertaSonoroFixture.GerarScopesVazios();
        var perfil = AlertaSonoroFixture.GerarPerfilComScopes(scopes);
        
        DependencyInjectorFactory.Mocker.Get<IUserContextTest>().DefinirPerfil(perfil);
        
        var cancellationToken = CancellationToken.None;

        // Act
        await DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.AlertaSonoroBusiness>()
            .DesativarAsync(cancellationToken);

        // Assert
        DependencyInjectorFactory.Mocker.GetMock<IAlertaSonoroFirebaseDatabaseService>().Verify(
            service => service.SetAsync(It.IsAny<IDictionary<string, AlertaSonoroRealtimeDto>>(), It.IsAny<CancellationToken>()),
            Times.Never);
    }

    [Fact(DisplayName = "[AlertaSonoroBusiness] - Deve processar scope único corretamente")]
    [Trait("Business", "AlertaSonoroBusiness")]
    public async Task AtivarAsync_UsuarioComScopeUnico_DeveProcessarCorretamente()
    {
        // Arrange
        var scopes = AlertaSonoroFixture.GerarScopeUnico();
        var perfil = AlertaSonoroFixture.GerarPerfilSupervisor(scopes);
        
        DependencyInjectorFactory.Mocker.Get<IUserContextTest>().DefinirPerfil(perfil);
        
        var cancellationToken = CancellationToken.None;

        // Act
        await DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.AlertaSonoroBusiness>()
            .AtivarAsync(cancellationToken);

        // Assert
        DependencyInjectorFactory.Mocker.GetMock<IAlertaSonoroFirebaseDatabaseService>().Verify(
            service => service.SetAsync(
                It.Is<IDictionary<string, AlertaSonoroRealtimeDto>>(dict =>
                    dict.Count == 1 &&
                    dict.ContainsKey("n") &&
                    dict["n"].Ativo == true),
                cancellationToken),
            Times.Once);
    }

    [Fact(DisplayName = "[AlertaSonoroBusiness] - Deve converter códigos de scope para lowercase")]
    [Trait("Business", "AlertaSonoroBusiness")]
    public async Task AtivarAsync_ScopesComMaiusculas_DeveConverterParaLowercase()
    {
        // Arrange
        var scopes = new List<Scope>
        {
            new("CENTROS", "NORDESTE", "Nordeste"),
            new("CENTROS", "SUDESTE", "Sudeste")
        };
        var perfil = AlertaSonoroFixture.GerarPerfilComScopes(scopes);
        
        DependencyInjectorFactory.Mocker.Get<IUserContextTest>().DefinirPerfil(perfil);
        
        var cancellationToken = CancellationToken.None;

        // Act
        await DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.AlertaSonoroBusiness>()
            .AtivarAsync(cancellationToken);

        // Assert
        DependencyInjectorFactory.Mocker.GetMock<IAlertaSonoroFirebaseDatabaseService>().Verify(
            service => service.SetAsync(
                It.Is<IDictionary<string, AlertaSonoroRealtimeDto>>(dict =>
                    dict.ContainsKey("nordeste") &&
                    dict.ContainsKey("sudeste") &&
                    !dict.ContainsKey("NORDESTE") &&
                    !dict.ContainsKey("SUDESTE")),
                cancellationToken),
            Times.Once);
    }

    [Theory(DisplayName = "[AlertaSonoroBusiness] - Deve processar diferentes quantidades de scopes")]
    [Trait("Business", "AlertaSonoroBusiness")]
    [InlineData(1)]
    [InlineData(3)]
    [InlineData(5)]
    public async Task AtivarAsync_DiferentesQuantidadesDeScopes_DeveProcessarCorretamente(int quantidadeScopes)
    {
        // Arrange
        var scopes = AlertaSonoroFixture.GerarScopesParametrizados(quantidadeScopes);
        var perfil = AlertaSonoroFixture.GerarPerfilComScopes(scopes);
        
        DependencyInjectorFactory.Mocker.Get<IUserContextTest>().DefinirPerfil(perfil);
        
        var cancellationToken = CancellationToken.None;

        // Act
        await DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.AlertaSonoroBusiness>()
            .AtivarAsync(cancellationToken);

        // Assert
        DependencyInjectorFactory.Mocker.GetMock<IAlertaSonoroFirebaseDatabaseService>().Verify(
            service => service.SetAsync(
                It.Is<IDictionary<string, AlertaSonoroRealtimeDto>>(dict =>
                    dict.Count == quantidadeScopes &&
                    dict.All(kvp => kvp.Value.Ativo == true)),
                cancellationToken),
            Times.Once);
    }

    [Fact(DisplayName = "[AlertaSonoroBusiness] - Deve definir DataDeAtualizacao próxima ao momento atual")]
    [Trait("Business", "AlertaSonoroBusiness")]
    public async Task AtivarAsync_DeveDefinirDataDeAtualizacaoCorreta()
    {
        // Arrange
        var scopes = AlertaSonoroFixture.GerarScopeUnico();
        var perfil = AlertaSonoroFixture.GerarPerfilComScopes(scopes);
        
        DependencyInjectorFactory.Mocker.Get<IUserContextTest>().DefinirPerfil(perfil);
        
        var dataAntes = DateTime.UtcNow;
        var cancellationToken = CancellationToken.None;

        // Act
        await DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.AlertaSonoroBusiness>()
            .AtivarAsync(cancellationToken);
        var dataDepois = DateTime.UtcNow;

        // Assert
        DependencyInjectorFactory.Mocker.GetMock<IAlertaSonoroFirebaseDatabaseService>().Verify(
            service => service.SetAsync(
                It.Is<IDictionary<string, AlertaSonoroRealtimeDto>>(dict =>
                    dict["n"].DataDeAtualizacao >= dataAntes &&
                    dict["n"].DataDeAtualizacao <= dataDepois),
                cancellationToken),
            Times.Once);
    }

    [Fact(DisplayName = "[AlertaSonoroBusiness] - Deve propagar CancellationToken corretamente")]
    [Trait("Business", "AlertaSonoroBusiness")]
    public async Task AtivarAsync_DevePropagarCancellationToken()
    {
        // Arrange
        var scopes = AlertaSonoroFixture.GerarScopeUnico();
        var perfil = AlertaSonoroFixture.GerarPerfilComScopes(scopes);
        
        DependencyInjectorFactory.Mocker.Get<IUserContextTest>().DefinirPerfil(perfil);
        
        var cancellationTokenSource = new CancellationTokenSource();
        var cancellationToken = cancellationTokenSource.Token;

        // Act
        await DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.AlertaSonoroBusiness>()
            .AtivarAsync(cancellationToken);

        // Assert
        DependencyInjectorFactory.Mocker.GetMock<IAlertaSonoroFirebaseDatabaseService>().Verify(
            service => service.SetAsync(
                It.IsAny<IDictionary<string, AlertaSonoroRealtimeDto>>(),
                cancellationToken),
            Times.Once);
    }
}
