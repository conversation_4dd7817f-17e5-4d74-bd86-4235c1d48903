using ONS.SINapse.Business.IBusiness;
using ONS.SINapse.Business.Imp.Business;
using ONS.SINapse.Shared.DTO.Firebase;
using ONS.SINapse.Shared.Identity;
using ONS.SINapse.Shared.Services.Firebase;
using ONS.SINapse.UnitTest.Shared.Identity;
using System.Diagnostics;

namespace ONS.SINapse.UnitTest.Business.AlertaSonoroBusiness;

public class AlertaSonoroBusinessIntegrationTest
{
    private readonly Mock<IAlertaSonoroFirebaseDatabaseService> _mockFirebaseService;
    private readonly IUserContextTest _userContext;
    private readonly IAlertaSonoroBusiness _alertaSonoroBusiness;

    public AlertaSonoroBusinessIntegrationTest()
    {
        _mockFirebaseService = new Mock<IAlertaSonoroFirebaseDatabaseService>();
        var perfil = new Perfil(new List<Scope>(), new List<string>(), "Operador", "operador");
        _userContext = new UserContextTest("integration-token", "integration-user-789", "Carlos Integração", "carlos.integracao", perfil);
        _alertaSonoroBusiness = new ONS.SINapse.Business.Imp.Business.AlertaSonoroBusiness(_userContext, _mockFirebaseService.Object);
    }

    [Fact(DisplayName = "[AlertaSonoroBusiness] - Deve executar operações sequenciais corretamente")]
    [Trait("Business", "AlertaSonoroBusiness")]
    public async Task OperacoesSequenciais_DeveExecutarCorretamente()
    {
        // Arrange
        var scopes = new List<Scope>
        {
            new("CENTROS", "SEQ-1", "Sequencial 1"),
            new("CENTROS", "SEQ-2", "Sequencial 2")
        };
        
        var perfil = new Perfil(scopes, new List<string>(), "Operador", "operador");
        _userContext.DefinirPerfil(perfil);

        var cancellationToken = CancellationToken.None;

        // Act
        await _alertaSonoroBusiness.AtivarAsync(cancellationToken);
        await _alertaSonoroBusiness.DesativarAsync(cancellationToken);
        await _alertaSonoroBusiness.AtivarAsync(cancellationToken);

        // Assert
        _mockFirebaseService.Verify(
            service => service.SetAsync(It.IsAny<IDictionary<string, AlertaSonoroRealtimeDto>>(), cancellationToken),
            Times.Exactly(3));
    }

    [Fact(DisplayName = "[AlertaSonoroBusiness] - Deve executar operações paralelas corretamente")]
    [Trait("Business", "AlertaSonoroBusiness")]
    public async Task OperacoesParalelas_DeveExecutarCorretamente()
    {
        // Arrange
        var scopes = new List<Scope>
        {
            new("CENTROS", "PAR-1", "Paralelo 1"),
            new("CENTROS", "PAR-2", "Paralelo 2")
        };
        
        var perfil = new Perfil(scopes, new List<string>(), "Operador", "operador");
        _userContext.DefinirPerfil(perfil);

        var cancellationToken = CancellationToken.None;

        // Act
        var task1 = _alertaSonoroBusiness.AtivarAsync(cancellationToken);
        var task2 = _alertaSonoroBusiness.DesativarAsync(cancellationToken);
        
        await Task.WhenAll(task1, task2);

        // Assert
        _mockFirebaseService.Verify(
            service => service.SetAsync(It.IsAny<IDictionary<string, AlertaSonoroRealtimeDto>>(), cancellationToken),
            Times.Exactly(2));
    }

    [Fact(DisplayName = "[AlertaSonoroBusiness] - Deve ter performance adequada com muitos scopes")]
    [Trait("Business", "AlertaSonoroBusiness")]
    public async Task Performance_MuitosScopes_DeveExecutarRapidamente()
    {
        // Arrange
        var scopes = new List<Scope>();
        for (int i = 1; i <= 100; i++)
        {
            scopes.Add(new Scope("CENTROS", $"PERF-{i:000}", $"Performance {i}"));
        }
        
        var perfil = new Perfil(scopes, new List<string>(), "Operador", "operador");
        _userContext.DefinirPerfil(perfil);

        var cancellationToken = CancellationToken.None;
        var stopwatch = Stopwatch.StartNew();

        // Act
        await _alertaSonoroBusiness.AtivarAsync(cancellationToken);
        stopwatch.Stop();

        // Assert
        stopwatch.ElapsedMilliseconds.ShouldBeLessThan(500); // Deve executar em menos de 1 segundo
        
        _mockFirebaseService.Verify(
            service => service.SetAsync(
                It.Is<IDictionary<string, AlertaSonoroRealtimeDto>>(dict => dict.Count == 100),
                cancellationToken),
            Times.Once);
    }

    [Fact(DisplayName = "[AlertaSonoroBusiness] - Deve manter estado consistente durante múltiplas operações")]
    [Trait("Business", "AlertaSonoroBusiness")]
    public async Task MultiplasOperacoes_DeveManterEstadoConsistente()
    {
        // Arrange
        var scopes = new List<Scope>
        {
            new("CENTROS", "ESTADO-1", "Estado 1"),
            new("CENTROS", "ESTADO-2", "Estado 2")
        };
        
        var perfil = new Perfil(scopes, new List<string>(), "Operador", "operador");
        _userContext.DefinirPerfil(perfil);

        var cancellationToken = CancellationToken.None;
        var chamadas = new List<IDictionary<string, AlertaSonoroRealtimeDto>>();

        _mockFirebaseService
            .Setup(service => service.SetAsync(It.IsAny<IDictionary<string, AlertaSonoroRealtimeDto>>(), It.IsAny<CancellationToken>()))
            .Callback<IDictionary<string, AlertaSonoroRealtimeDto>, CancellationToken>((dict, token) => 
            {
                chamadas.Add(new Dictionary<string, AlertaSonoroRealtimeDto>(dict));
            })
            .Returns(Task.CompletedTask);

        // Act
        await _alertaSonoroBusiness.AtivarAsync(cancellationToken);
        await _alertaSonoroBusiness.DesativarAsync(cancellationToken);
        await _alertaSonoroBusiness.AtivarAsync(cancellationToken);

        // Assert
        chamadas.Count.ShouldBe(3);
        
        // Primeira chamada - Ativar
        chamadas[0]["estado-1"].Ativo.ShouldBeTrue();
        chamadas[0]["estado-2"].Ativo.ShouldBeTrue();
        
        // Segunda chamada - Desativar
        chamadas[1]["estado-1"].Ativo.ShouldBeFalse();
        chamadas[1]["estado-2"].Ativo.ShouldBeFalse();
        
        // Terceira chamada - Ativar novamente
        chamadas[2]["estado-1"].Ativo.ShouldBeTrue();
        chamadas[2]["estado-2"].Ativo.ShouldBeTrue();
    }

    [Fact(DisplayName = "[AlertaSonoroBusiness] - Deve lidar com timeout do Firebase service")]
    [Trait("Business", "AlertaSonoroBusiness")]
    public async Task Timeout_FirebaseService_DeveLidarCorretamente()
    {
        // Arrange
        var scopes = new List<Scope>
        {
            new("CENTROS", "TIMEOUT", "Timeout")
        };
        
        var perfil = new Perfil(scopes, new List<string>(), "Operador", "operador");
        _userContext.DefinirPerfil(perfil);

        _mockFirebaseService
            .Setup(service => service.SetAsync(It.IsAny<IDictionary<string, AlertaSonoroRealtimeDto>>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new TimeoutException("Timeout no Firebase"));

        var cancellationToken = CancellationToken.None;

        // Act & Assert
        var exception = await Assert.ThrowsAsync<TimeoutException>(
            () => _alertaSonoroBusiness.AtivarAsync(cancellationToken));
        
        exception.Message.ShouldBe("Timeout no Firebase");
    }

    [Fact(DisplayName = "[AlertaSonoroBusiness] - Deve validar integridade dos dados enviados")]
    [Trait("Business", "AlertaSonoroBusiness")]
    public async Task ValidarIntegridade_DadosEnviados_DeveEstarCorretos()
    {
        // Arrange
        var scopes = new List<Scope>
        {
            new("CENTROS", "INTEGRIDADE", "Integridade")
        };
        
        var perfil = new Perfil(scopes, new List<string>(), "Operador", "operador");
        _userContext.DefinirPerfil(perfil);

        var cancellationToken = CancellationToken.None;
        AlertaSonoroRealtimeDto? dadosCapturados = null;

        _mockFirebaseService
            .Setup(service => service.SetAsync(It.IsAny<IDictionary<string, AlertaSonoroRealtimeDto>>(), It.IsAny<CancellationToken>()))
            .Callback<IDictionary<string, AlertaSonoroRealtimeDto>, CancellationToken>((dict, token) => 
            {
                dadosCapturados = dict["integridade"];
            })
            .Returns(Task.CompletedTask);

        // Act
        await _alertaSonoroBusiness.AtivarAsync(cancellationToken);

        // Assert
        dadosCapturados.ShouldNotBeNull();
        dadosCapturados.Sid.ShouldBe("integration-user-789");
        dadosCapturados.Usuario.ShouldBe("Carlos Integração");
        dadosCapturados.Ativo.ShouldBeTrue();
        dadosCapturados.DataDeAtualizacao.ShouldBeInRange(DateTime.UtcNow.AddMinutes(-1), DateTime.UtcNow.AddMinutes(1));
    }

    [Theory(DisplayName = "[AlertaSonoroBusiness] - Deve processar diferentes tipos de perfil")]
    [Trait("Business", "AlertaSonoroBusiness")]
    [InlineData("Operador", "operador")]
    [InlineData("Supervisor", "supervisor")]
    [InlineData("Administrador", "administrador")]
    public async Task DiferentesPerfis_DeveProcessarCorretamente(string nomePerfil, string codigoPerfil)
    {
        // Arrange
        var scopes = new List<Scope>
        {
            new("CENTROS", $"PERFIL-{codigoPerfil.ToUpper()}", $"Perfil {nomePerfil}")
        };
        
        var perfil = new Perfil(scopes, new List<string>(), nomePerfil, codigoPerfil);
        _userContext.DefinirPerfil(perfil);

        var cancellationToken = CancellationToken.None;

        // Act
        await _alertaSonoroBusiness.AtivarAsync(cancellationToken);

        // Assert
        _mockFirebaseService.Verify(
            service => service.SetAsync(
                It.Is<IDictionary<string, AlertaSonoroRealtimeDto>>(dict =>
                    dict.ContainsKey($"perfil-{codigoPerfil}") &&
                    dict[$"perfil-{codigoPerfil}"].Sid == "integration-user-789"),
                cancellationToken),
            Times.Once);
    }

    [Fact(DisplayName = "[AlertaSonoroBusiness] - Deve funcionar com perfil contendo operações")]
    [Trait("Business", "AlertaSonoroBusiness")]
    public async Task PerfilComOperacoes_DeveFuncionarNormalmente()
    {
        // Arrange
        var scopes = new List<Scope>
        {
            new("CENTROS", "COM-OPERACOES", "Com Operações")
        };
        
        var operacoes = new List<string> { "CRIAR_SOLICITACAO", "EDITAR_SOLICITACAO", "EXCLUIR_SOLICITACAO" };
        var perfil = new Perfil(scopes, operacoes, "Operador Completo", "operador-completo");
        _userContext.DefinirPerfil(perfil);

        var cancellationToken = CancellationToken.None;

        // Act
        await _alertaSonoroBusiness.AtivarAsync(cancellationToken);

        // Assert
        _mockFirebaseService.Verify(
            service => service.SetAsync(
                It.Is<IDictionary<string, AlertaSonoroRealtimeDto>>(dict =>
                    dict.ContainsKey("com-operacoes") &&
                    dict["com-operacoes"].Ativo == true),
                cancellationToken),
            Times.Once);
    }
}
