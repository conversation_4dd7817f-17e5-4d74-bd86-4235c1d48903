# Testes Unitários - AlertaSonoroBusiness

Este diretório contém os testes unitários para a classe `AlertaSonoroBusiness`, que é responsável por gerenciar a ativação e desativação de alertas sonoros no Firebase para usuários baseado em seus scopes de acesso.

## 📁 Estrutura dos Arquivos

- **AlertaSonoroBusinessTest.cs**: Testes principais da funcionalidade básica
- **AlertaSonoroBusinessEdgeCasesTest.cs**: Testes de casos extremos e cenários de erro
- **AlertaSonoroBusinessIntegrationTest.cs**: Testes de integração e performance

## 🎯 Funcionalidade Testada

A classe `AlertaSonoroBusiness` implementa a interface `IAlertaSonoroBusiness` e possui dois métodos principais:

- **AtivarAsync**: Ativa alertas sonoros para todos os scopes do usuário
- **DesativarAsync**: Desativa alertas sonoros para todos os scopes do usuário

### Comportamento Principal

1. Verifica se o usuário possui scopes no perfil
2. Se não possui scopes, retorna `Task.CompletedTask` sem processar
3. Se possui scopes, cria um dicionário com alertas para cada scope
4. Converte códigos de scope para lowercase
5. Envia os dados para o Firebase via `IAlertaSonoroFirebaseDatabaseService`

## 🧪 Cenários de Teste Cobertos

### 1. AlertaSonoroBusinessTest.cs (8 testes)

#### ✅ Cenários Principais
- **Ativação com scopes**: Valida ativação correta de alertas para usuário com múltiplos scopes
- **Desativação com scopes**: Valida desativação correta de alertas para usuário com múltiplos scopes
- **Usuário sem scopes**: Verifica que não há processamento quando usuário não possui scopes
- **Scope único**: Testa processamento correto com apenas um scope

#### 🔄 Cenários de Conversão
- **Conversão para lowercase**: Verifica que códigos de scope são convertidos para minúsculas
- **Diferentes quantidades**: Testa com 1, 3 e 5 scopes (Theory)

#### ⏰ Cenários de Tempo e Token
- **DataDeAtualizacao**: Valida que a data é definida próxima ao momento atual
- **CancellationToken**: Verifica propagação correta do token de cancelamento

### 2. AlertaSonoroBusinessEdgeCasesTest.cs (10 testes)

#### 🔤 Cenários de Caracteres Especiais
- **Caracteres especiais**: Testa scopes com hífens, underscores e pontos
- **Scopes duplicados**: Verifica processamento de scopes com códigos duplicados
- **Códigos longos**: Testa com códigos de 100 caracteres

#### 👤 Cenários de Usuário
- **Nome vazio**: Testa comportamento com usuário sem nome
- **SID vazio**: Testa comportamento com usuário sem SID

#### ⚠️ Cenários de Erro
- **Exceção Firebase**: Verifica propagação de exceções do serviço Firebase
- **CancellationToken cancelado**: Testa comportamento com token cancelado

#### 🔄 Cenários de Consistência
- **Consistência ativar/desativar**: Valida que as operações mantêm consistência

### 3. AlertaSonoroBusinessIntegrationTest.cs (9 testes)

#### 🔄 Cenários de Operações
- **Operações sequenciais**: Testa múltiplas operações em sequência
- **Operações paralelas**: Testa operações simultâneas
- **Múltiplas operações**: Valida consistência de estado durante várias operações

#### ⚡ Cenários de Performance
- **Performance com muitos scopes**: Testa com 100 scopes (deve executar < 1 segundo)
- **Timeout Firebase**: Testa comportamento com timeout do serviço

#### 🔍 Cenários de Integridade
- **Integridade de dados**: Valida que todos os dados enviados estão corretos
- **Diferentes perfis**: Testa com diferentes tipos de perfil (Theory)
- **Perfil com operações**: Testa perfil que possui operações definidas

## 🛡️ Cenários de Borda Específicos

### 📊 Dados de Entrada
- **Lista vazia de scopes**: Não processa quando usuário não tem scopes
- **Scopes duplicados**: Processa apenas chaves únicas no dicionário
- **Caracteres especiais**: Mantém caracteres especiais na conversão lowercase
- **Códigos muito longos**: Suporta códigos de até 100+ caracteres

### 👤 Contexto de Usuário
- **Nome vazio**: Aceita e processa usuários sem nome
- **SID vazio**: Aceita e processa usuários sem SID
- **Diferentes perfis**: Funciona com qualquer tipo de perfil
- **Perfis com operações**: Não afeta o processamento de alertas

### 🔗 Integração com Dependências
- **Firebase Service**: Mocks configurados para diferentes cenários
- **CancellationToken**: Propagação correta em todos os cenários
- **Exceções**: Propagação adequada de erros do Firebase

### ⚡ Performance e Concorrência
- **Muitos scopes**: Performance adequada com 100+ scopes
- **Operações paralelas**: Suporte a operações simultâneas
- **Operações sequenciais**: Consistência em operações em sequência

## 🚀 Características Técnicas

### 🔧 Configuração de Testes
- **Mocks**: Uso de `Mock<IAlertaSonoroFirebaseDatabaseService>` para isolamento
- **UserContext**: Uso de `UserContextTest` para simular diferentes usuários
- **Assertions**: Validações detalhadas usando Shouldly

### 📈 Cobertura de Código
- **100% dos métodos públicos**: `AtivarAsync` e `DesativarAsync`
- **100% do método privado**: `AtivarDesativarAsync`
- **Cenários de sucesso e falha**: Cobertura completa de fluxos
- **Casos extremos**: Validação de comportamento em situações limite

### 🎯 Padrões de Verificação
- **Verificação de chamadas**: `Times.Once`, `Times.Never`, `Times.Exactly(n)`
- **Verificação de dados**: Validação detalhada de propriedades dos DTOs
- **Verificação de comportamento**: Confirmação de lógica de negócio

## 🔧 Execução dos Testes

```bash
# Executar apenas os testes deste diretório
dotnet test --filter "FullyQualifiedName~AlertaSonoroBusiness"

# Executar com verbosidade detalhada
dotnet test --filter "FullyQualifiedName~AlertaSonoroBusiness" --verbosity detailed

# Executar apenas testes principais
dotnet test --filter "FullyQualifiedName~AlertaSonoroBusinessTest"

# Executar apenas testes de edge cases
dotnet test --filter "FullyQualifiedName~AlertaSonoroBusinessEdgeCasesTest"

# Executar apenas testes de integração
dotnet test --filter "FullyQualifiedName~AlertaSonoroBusinessIntegrationTest"
```

## 📋 Resultados Esperados

- **Total de testes**: 27 (8 + 10 + 9)
- **Taxa de sucesso**: 100%
- **Tempo de execução**: ~5-10 segundos
- **Cobertura**: Cenários principais, bordas, erros e integração

## 📚 Dependências Testadas

### Interfaces Mockadas
- `IAlertaSonoroFirebaseDatabaseService`: Serviço Firebase para persistência
- `IUserContext`: Contexto do usuário (via `UserContextTest`)

### DTOs Utilizados
- `AlertaSonoroRealtimeDto`: DTO para dados do alerta sonoro
- `Scope`: Representa um scope de acesso do usuário
- `Perfil`: Perfil do usuário com scopes e operações

### Cenários de Negócio
- **Ativação**: Marca alertas como ativos (`Ativo = true`)
- **Desativação**: Marca alertas como inativos (`Ativo = false`)
- **Conversão**: Códigos de scope sempre em lowercase
- **Timestamp**: `DataDeAtualizacao` sempre próxima ao `DateTime.UtcNow`

## 🎯 Validações Principais

1. **Lógica de Negócio**: Verifica se a lógica de ativação/desativação está correta
2. **Integração Firebase**: Confirma que dados são enviados corretamente ao Firebase
3. **Tratamento de Scopes**: Valida conversão e processamento de scopes
4. **Gestão de Erros**: Verifica propagação adequada de exceções
5. **Performance**: Confirma que a performance é adequada mesmo com muitos scopes
