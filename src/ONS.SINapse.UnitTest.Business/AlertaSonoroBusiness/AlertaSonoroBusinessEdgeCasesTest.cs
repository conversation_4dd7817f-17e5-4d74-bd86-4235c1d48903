using ONS.SINapse.Business.IBusiness;
using ONS.SINapse.Business.Imp.Business;
using ONS.SINapse.Shared.DTO.Firebase;
using ONS.SINapse.Shared.Identity;
using ONS.SINapse.Shared.Services.Firebase;
using ONS.SINapse.UnitTest.Shared.Identity;

namespace ONS.SINapse.UnitTest.Business.AlertaSonoroBusiness;

public class AlertaSonoroBusinessEdgeCasesTest
{
    private readonly Mock<IAlertaSonoroFirebaseDatabaseService> _mockFirebaseService;
    private readonly IUserContextTest _userContext;
    private readonly IAlertaSonoroBusiness _alertaSonoroBusiness;

    public AlertaSonoroBusinessEdgeCasesTest()
    {
        _mockFirebaseService = new Mock<IAlertaSonoroFirebaseDatabaseService>();
        _userContext = new UserContextTest("token", "edge-user-456", "<PERSON> Santos", "maria.santos");
        _alertaSonoroBusiness = new Business.Imp.Business.AlertaSonoroBusiness(_userContext, _mockFirebaseService.Object);
    }

    [Fact(DisplayName = "[AlertaSonoroBusiness] - Deve lidar com scopes com caracteres especiais")]
    [Trait("Business", "AlertaSonoroBusiness")]
    public async Task AtivarAsync_ScopesComCaracteresEspeciais_DeveProcessarCorretamente()
    {
        // Arrange
        var scopes = new List<Scope>
        {
            new("CENTROS", "CENTRO-SUL", "Centro-Sul"),
            new("CENTROS", "ÁREA_NORTE", "Área Norte"),
            new("CENTROS", "REGIÃO.OESTE", "Região.Oeste")
        };
        
        var perfil = new Perfil(scopes, new List<string>(), "Operador", "operador");
        _userContext.DefinirPerfil(perfil);

        var cancellationToken = CancellationToken.None;

        // Act
        await _alertaSonoroBusiness.AtivarAsync(cancellationToken);

        // Assert
        _mockFirebaseService.Verify(
            service => service.SetAsync(
                It.Is<IDictionary<string, AlertaSonoroRealtimeDto>>(dict =>
                    dict.Count == 3 &&
                    dict.ContainsKey("centro-sul") &&
                    dict.ContainsKey("área_norte") &&
                    dict.ContainsKey("região.oeste")),
                cancellationToken),
            Times.Once);
    }

    [Fact(DisplayName = "[AlertaSonoroBusiness] - Deve lidar com scopes duplicados")]
    [Trait("Business", "AlertaSonoroBusiness")]
    public async Task AtivarAsync_ScopesDuplicados_DeveProcessarApenasScopesUnicos()
    {
        // Arrange
        var scopes = new List<Scope>
        {
            new("CENTROS", "DUPLICADO", "Duplicado 1"),
            new("CENTROS", "DUPLICADO", "Duplicado 2"), // Mesmo código
            new("CENTROS", "UNICO", "Único")
        };
        
        var perfil = new Perfil(scopes, new List<string>(), "Operador", "operador");
        _userContext.DefinirPerfil(perfil);

        var cancellationToken = CancellationToken.None;

        // Act
        await _alertaSonoroBusiness.AtivarAsync(cancellationToken);

        // Assert
        _mockFirebaseService.Verify(
            service => service.SetAsync(
                It.Is<IDictionary<string, AlertaSonoroRealtimeDto>>(dict =>
                    dict.Count == 2 && // Apenas 2 chaves únicas
                    dict.ContainsKey("duplicado") &&
                    dict.ContainsKey("unico")),
                cancellationToken),
            Times.Once);
    }

    [Fact(DisplayName = "[AlertaSonoroBusiness] - Deve lidar com usuário com nome vazio")]
    [Trait("Business", "AlertaSonoroBusiness")]
    public async Task AtivarAsync_UsuarioComNomeVazio_DeveProcessarComNomeVazio()
    {
        // Arrange
        var userContextComNomeVazio = new UserContextTest("token", "empty-name-user", "", "usuario.vazio");
        var business = new Business.Imp.Business.AlertaSonoroBusiness(userContextComNomeVazio, _mockFirebaseService.Object);
        
        var scopes = new List<Scope>
        {
            new("CENTROS", "TESTE-NOME", "Teste Nome")
        };
        
        var perfil = new Perfil(scopes, new List<string>(), "Operador", "operador");
        userContextComNomeVazio.DefinirPerfil(perfil);

        var cancellationToken = CancellationToken.None;

        // Act
        await business.AtivarAsync(cancellationToken);

        // Assert
        _mockFirebaseService.Verify(
            service => service.SetAsync(
                It.Is<IDictionary<string, AlertaSonoroRealtimeDto>>(dict =>
                    dict["teste-nome"].Usuario == "" &&
                    dict["teste-nome"].Sid == "empty-name-user"),
                cancellationToken),
            Times.Once);
    }

    [Fact(DisplayName = "[AlertaSonoroBusiness] - Deve lidar com usuário com SID vazio")]
    [Trait("Business", "AlertaSonoroBusiness")]
    public async Task AtivarAsync_UsuarioComSidVazio_DeveProcessarComSidVazio()
    {
        // Arrange
        var userContextComSidVazio = new UserContextTest("token", "", "Nome Válido", "usuario.sid.vazio");
        var business = new Business.Imp.Business.AlertaSonoroBusiness(userContextComSidVazio, _mockFirebaseService.Object);
        
        var scopes = new List<Scope>
        {
            new("CENTROS", "TESTE-SID", "Teste SID")
        };
        
        var perfil = new Perfil(scopes, new List<string>(), "Operador", "operador");
        userContextComSidVazio.DefinirPerfil(perfil);

        var cancellationToken = CancellationToken.None;

        // Act
        await business.AtivarAsync(cancellationToken);

        // Assert
        _mockFirebaseService.Verify(
            service => service.SetAsync(
                It.Is<IDictionary<string, AlertaSonoroRealtimeDto>>(dict =>
                    dict["teste-sid"].Sid == "" &&
                    dict["teste-sid"].Usuario == "Nome Válido"),
                cancellationToken),
            Times.Once);
    }

    [Fact(DisplayName = "[AlertaSonoroBusiness] - Deve lidar com exceção do Firebase service")]
    [Trait("Business", "AlertaSonoroBusiness")]
    public async Task AtivarAsync_ExcecaoFirebaseService_DevePropagar Excecao()
    {
        // Arrange
        var scopes = new List<Scope>
        {
            new("CENTROS", "ERRO-FIREBASE", "Erro Firebase")
        };
        
        var perfil = new Perfil(scopes, new List<string>(), "Operador", "operador");
        _userContext.DefinirPerfil(perfil);

        _mockFirebaseService
            .Setup(service => service.SetAsync(It.IsAny<IDictionary<string, AlertaSonoroRealtimeDto>>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new InvalidOperationException("Erro no Firebase"));

        var cancellationToken = CancellationToken.None;

        // Act & Assert
        var exception = await Assert.ThrowsAsync<InvalidOperationException>(
            () => _alertaSonoroBusiness.AtivarAsync(cancellationToken));
        
        exception.Message.ShouldBe("Erro no Firebase");
    }

    [Fact(DisplayName = "[AlertaSonoroBusiness] - Deve lidar com CancellationToken cancelado")]
    [Trait("Business", "AlertaSonoroBusiness")]
    public async Task AtivarAsync_CancellationTokenCancelado_DevePropagar OperationCanceledException()
    {
        // Arrange
        var scopes = new List<Scope>
        {
            new("CENTROS", "CANCELADO", "Cancelado")
        };
        
        var perfil = new Perfil(scopes, new List<string>(), "Operador", "operador");
        _userContext.DefinirPerfil(perfil);

        _mockFirebaseService
            .Setup(service => service.SetAsync(It.IsAny<IDictionary<string, AlertaSonoroRealtimeDto>>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new OperationCanceledException());

        var cancellationTokenSource = new CancellationTokenSource();
        cancellationTokenSource.Cancel();

        // Act & Assert
        await Assert.ThrowsAsync<OperationCanceledException>(
            () => _alertaSonoroBusiness.AtivarAsync(cancellationTokenSource.Token));
    }

    [Fact(DisplayName = "[AlertaSonoroBusiness] - Deve processar scopes com códigos muito longos")]
    [Trait("Business", "AlertaSonoroBusiness")]
    public async Task AtivarAsync_ScopesComCodigosLongos_DeveProcessarCorretamente()
    {
        // Arrange
        var codigoLongo = new string('A', 100); // 100 caracteres
        var scopes = new List<Scope>
        {
            new("CENTROS", codigoLongo, "Código Muito Longo")
        };
        
        var perfil = new Perfil(scopes, new List<string>(), "Operador", "operador");
        _userContext.DefinirPerfil(perfil);

        var cancellationToken = CancellationToken.None;

        // Act
        await _alertaSonoroBusiness.AtivarAsync(cancellationToken);

        // Assert
        _mockFirebaseService.Verify(
            service => service.SetAsync(
                It.Is<IDictionary<string, AlertaSonoroRealtimeDto>>(dict =>
                    dict.ContainsKey(codigoLongo.ToLower()) &&
                    dict[codigoLongo.ToLower()].Sid == "edge-user-456"),
                cancellationToken),
            Times.Once);
    }

    [Fact(DisplayName = "[AlertaSonoroBusiness] - Deve manter consistência entre ativar e desativar")]
    [Trait("Business", "AlertaSonoroBusiness")]
    public async Task AtivarDesativar_MesmosScopes_DeveManter Consistencia()
    {
        // Arrange
        var scopes = new List<Scope>
        {
            new("CENTROS", "CONSISTENCIA", "Consistência")
        };
        
        var perfil = new Perfil(scopes, new List<string>(), "Operador", "operador");
        _userContext.DefinirPerfil(perfil);

        var cancellationToken = CancellationToken.None;

        // Act
        await _alertaSonoroBusiness.AtivarAsync(cancellationToken);
        await _alertaSonoroBusiness.DesativarAsync(cancellationToken);

        // Assert
        _mockFirebaseService.Verify(
            service => service.SetAsync(
                It.Is<IDictionary<string, AlertaSonoroRealtimeDto>>(dict =>
                    dict.ContainsKey("consistencia") &&
                    dict["consistencia"].Ativo == true),
                cancellationToken),
            Times.Once);

        _mockFirebaseService.Verify(
            service => service.SetAsync(
                It.Is<IDictionary<string, AlertaSonoroRealtimeDto>>(dict =>
                    dict.ContainsKey("consistencia") &&
                    dict["consistencia"].Ativo == false),
                cancellationToken),
            Times.Once);
    }
}
