using ONS.SINapse.Shared.DTO.Firebase;
using ONS.SINapse.Shared.Services.Firebase;
using ONS.SINapse.UnitTest.Shared.Fixtures;
using ONS.SINapse.UnitTest.Shared.Identity;
using ONS.SINapse.Shared.Identity;
using System.Diagnostics;
namespace ONS.SINapse.UnitTest.Business.AlertaSonoroBusiness;

public partial class AlertaSonoroBusinessTest
{

    [Fact(DisplayName = "[AlertaSonoroBusiness] - Deve lançar exceção com scopes duplicados")]
    [Trait("Business", "AlertaSonoroBusiness")]
    public async Task AtivarAsync_ScopesDuplicados_DeveLancarExcecao()
    {
        // Arrange
        var scopes = AlertaSonoroFixture.GerarScopesDuplicados();
        var perfil = AlertaSonoroFixture.GerarPerfilComScopes(scopes);
        
        DependencyInjectorFactory.Mocker.Get<IUserContextTest>().DefinirPerfil(perfil);
        
        var cancellationToken = CancellationToken.None;

        // Act & Assert
        var exception = await Assert.ThrowsAsync<ArgumentException>(
            () => DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.AlertaSonoroBusiness>()
                .AtivarAsync(cancellationToken));
        
        exception.Message.ShouldContain("An item with the same key has already been added");
        exception.Message.ShouldContain("duplicado");
    }

    [Fact(DisplayName = "[AlertaSonoroBusiness] - Deve lidar com exceção do Firebase service")]
    [Trait("Business", "AlertaSonoroBusiness")]
    public async Task AtivarAsync_ExcecaoFirebaseService_DevePropagarExcecao()
    {
        // Arrange
        var scopes = AlertaSonoroFixture.GerarScopeUnico();
        var perfil = AlertaSonoroFixture.GerarPerfilComScopes(scopes);
        
        DependencyInjectorFactory.Mocker.Get<IUserContextTest>().DefinirPerfil(perfil);

        DependencyInjectorFactory.Mocker.GetMock<IAlertaSonoroFirebaseDatabaseService>()
            .Setup(service => service.SetAsync(It.IsAny<IDictionary<string, AlertaSonoroRealtimeDto>>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new InvalidOperationException("Erro no Firebase"));

        var cancellationToken = CancellationToken.None;

        // Act & Assert
        var exception = await Assert.ThrowsAsync<InvalidOperationException>(
            () => DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.AlertaSonoroBusiness>()
                .AtivarAsync(cancellationToken));
        
        exception.Message.ShouldBe("Erro no Firebase");
    }

    [Fact(DisplayName = "[AlertaSonoroBusiness] - Deve lidar com CancellationToken cancelado")]
    [Trait("Business", "AlertaSonoroBusiness")]
    public async Task AtivarAsync_CancellationTokenCancelado_DevePropagarOperationCanceledException()
    {
        // Arrange
        var scopes = AlertaSonoroFixture.GerarScopeUnico();
        var perfil = AlertaSonoroFixture.GerarPerfilComScopes(scopes);
        
        DependencyInjectorFactory.Mocker.Get<IUserContextTest>().DefinirPerfil(perfil);

        DependencyInjectorFactory.Mocker.GetMock<IAlertaSonoroFirebaseDatabaseService>()
            .Setup(service => service.SetAsync(It.IsAny<IDictionary<string, AlertaSonoroRealtimeDto>>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new OperationCanceledException());

        var cancellationTokenSource = new CancellationTokenSource();
        cancellationTokenSource.Cancel();

        // Act & Assert
        await Assert.ThrowsAsync<OperationCanceledException>(
            () => DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.AlertaSonoroBusiness>()
                .AtivarAsync(cancellationTokenSource.Token));
    }

    [Fact(DisplayName = "[AlertaSonoroBusiness] - Deve processar scopes com códigos muito longos")]
    [Trait("Business", "AlertaSonoroBusiness")]
    public async Task AtivarAsync_ScopesComCodigosLongos_DeveProcessarCorretamente()
    {
        // Arrange
        var scopes = AlertaSonoroFixture.GerarScopesComCodigosLongos();
        var perfil = AlertaSonoroFixture.GerarPerfilComScopes(scopes);
        
        DependencyInjectorFactory.Mocker.Get<IUserContextTest>().DefinirPerfil(perfil);
        
        var cancellationToken = CancellationToken.None;
        var codigoLongo = new string('A', 100).ToLower();

        // Act
        await DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.AlertaSonoroBusiness>()
            .AtivarAsync(cancellationToken);

        // Assert
        DependencyInjectorFactory.Mocker.GetMock<IAlertaSonoroFirebaseDatabaseService>().Verify(
            service => service.SetAsync(
                It.Is<IDictionary<string, AlertaSonoroRealtimeDto>>(dict =>
                    dict.ContainsKey(codigoLongo)),
                cancellationToken),
            Times.Once);
    }

    [Fact(DisplayName = "[AlertaSonoroBusiness] - Deve manter consistência entre ativar e desativar")]
    [Trait("Business", "AlertaSonoroBusiness")]
    public async Task AtivarDesativar_MesmosScopes_DeveManterConsistencia()
    {
        // Arrange
        var scopes = AlertaSonoroFixture.GerarScopeUnico();
        var perfil = AlertaSonoroFixture.GerarPerfilComScopes(scopes);
        
        DependencyInjectorFactory.Mocker.Get<IUserContextTest>().DefinirPerfil(perfil);
        
        var cancellationToken = CancellationToken.None;
        var business = DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.AlertaSonoroBusiness>();

        // Act
        await business.AtivarAsync(cancellationToken);
        await business.DesativarAsync(cancellationToken);

        // Assert
        DependencyInjectorFactory.Mocker.GetMock<IAlertaSonoroFirebaseDatabaseService>().Verify(
            service => service.SetAsync(
                It.Is<IDictionary<string, AlertaSonoroRealtimeDto>>(dict =>
                    dict.ContainsKey("n") &&
                    dict["n"].Ativo == true),
                cancellationToken),
            Times.Once);

        DependencyInjectorFactory.Mocker.GetMock<IAlertaSonoroFirebaseDatabaseService>().Verify(
            service => service.SetAsync(
                It.Is<IDictionary<string, AlertaSonoroRealtimeDto>>(dict =>
                    dict.ContainsKey("n") &&
                    dict["n"].Ativo == false),
                cancellationToken),
            Times.Once);
    }
    
    [Fact(DisplayName = "[AlertaSonoroBusiness] - Deve executar operações sequenciais corretamente")]
    [Trait("Business", "AlertaSonoroBusiness")]
    public async Task OperacoesSequenciais_DeveExecutarCorretamente()
    {
        // Arrange
        var scopes = AlertaSonoroFixture.GerarScopesValidos();
        var perfil = AlertaSonoroFixture.GerarPerfilComScopes(scopes);
        
        DependencyInjectorFactory.Mocker.Get<IUserContextTest>().DefinirPerfil(perfil);
        
        var cancellationToken = CancellationToken.None;
        var business = DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.AlertaSonoroBusiness>();

        // Act
        await business.AtivarAsync(cancellationToken);
        await business.DesativarAsync(cancellationToken);
        await business.AtivarAsync(cancellationToken);

        // Assert
        DependencyInjectorFactory.Mocker.GetMock<IAlertaSonoroFirebaseDatabaseService>().Verify(
            service => service.SetAsync(It.IsAny<IDictionary<string, AlertaSonoroRealtimeDto>>(), cancellationToken),
            Times.Exactly(3));
    }

    [Fact(DisplayName = "[AlertaSonoroBusiness] - Deve executar operações paralelas corretamente")]
    [Trait("Business", "AlertaSonoroBusiness")]
    public async Task OperacoesParalelas_DeveExecutarCorretamente()
    {
        // Arrange
        var scopes = AlertaSonoroFixture.GerarScopesValidos();
        var perfil = AlertaSonoroFixture.GerarPerfilComScopes(scopes);
        
        DependencyInjectorFactory.Mocker.Get<IUserContextTest>().DefinirPerfil(perfil);
        
        var cancellationToken = CancellationToken.None;
        var business1 = DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.AlertaSonoroBusiness>();
        var business2 = DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.AlertaSonoroBusiness>();

        // Act
        var task1 = business1.AtivarAsync(cancellationToken);
        var task2 = business2.DesativarAsync(cancellationToken);
        
        await Task.WhenAll(task1, task2);

        // Assert
        DependencyInjectorFactory.Mocker.GetMock<IAlertaSonoroFirebaseDatabaseService>().Verify(
            service => service.SetAsync(It.IsAny<IDictionary<string, AlertaSonoroRealtimeDto>>(), cancellationToken),
            Times.Exactly(2));
    }

    [Fact(DisplayName = "[AlertaSonoroBusiness] - Deve ter performance adequada com muitos scopes")]
    [Trait("Business", "AlertaSonoroBusiness")]
    public async Task Performance_MuitosScopes_DeveExecutarRapidamente()
    {
        // Arrange
        var scopes = AlertaSonoroFixture.GerarScopesParaPerformance(100);
        var perfil = AlertaSonoroFixture.GerarPerfilComScopes(scopes);
        
        DependencyInjectorFactory.Mocker.Get<IUserContextTest>().DefinirPerfil(perfil);
        
        var cancellationToken = CancellationToken.None;
        var stopwatch = Stopwatch.StartNew();

        // Act
        await DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.AlertaSonoroBusiness>()
            .AtivarAsync(cancellationToken);
        stopwatch.Stop();

        // Assert
        stopwatch.ElapsedMilliseconds.ShouldBeLessThan(1000); // Deve executar em menos de 1 segundo
        
        DependencyInjectorFactory.Mocker.GetMock<IAlertaSonoroFirebaseDatabaseService>().Verify(
            service => service.SetAsync(
                It.Is<IDictionary<string, AlertaSonoroRealtimeDto>>(dict => dict.Count == 100),
                cancellationToken),
            Times.Once);
    }

    [Fact(DisplayName = "[AlertaSonoroBusiness] - Deve manter estado consistente durante múltiplas operações")]
    [Trait("Business", "AlertaSonoroBusiness")]
    public async Task MultiplasOperacoes_DeveManterEstadoConsistente()
    {
        // Arrange
        var scopes = AlertaSonoroFixture.GerarScopesValidos();
        var perfil = AlertaSonoroFixture.GerarPerfilComScopes(scopes);
        
        DependencyInjectorFactory.Mocker.Get<IUserContextTest>().DefinirPerfil(perfil);
        
        var cancellationToken = CancellationToken.None;
        var chamadas = new List<IDictionary<string, AlertaSonoroRealtimeDto>>();

        DependencyInjectorFactory.Mocker.GetMock<IAlertaSonoroFirebaseDatabaseService>()
            .Setup(service => service.SetAsync(It.IsAny<IDictionary<string, AlertaSonoroRealtimeDto>>(), It.IsAny<CancellationToken>()))
            .Callback<IDictionary<string, AlertaSonoroRealtimeDto>, CancellationToken>((dict, token) => 
            {
                chamadas.Add(new Dictionary<string, AlertaSonoroRealtimeDto>(dict));
            })
            .Returns(Task.CompletedTask);

        var business = DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.AlertaSonoroBusiness>();

        // Act
        await business.AtivarAsync(cancellationToken);
        await business.DesativarAsync(cancellationToken);
        await business.AtivarAsync(cancellationToken);

        // Assert
        chamadas.Count.ShouldBe(3);
        
        // Primeira chamada - Ativar
        chamadas[0]["ne"].Ativo.ShouldBeTrue();
        chamadas[0]["se"].Ativo.ShouldBeTrue();
        chamadas[0]["sul"].Ativo.ShouldBeTrue();
        
        // Segunda chamada - Desativar
        chamadas[1]["ne"].Ativo.ShouldBeFalse();
        chamadas[1]["se"].Ativo.ShouldBeFalse();
        chamadas[1]["sul"].Ativo.ShouldBeFalse();
        
        // Terceira chamada - Ativar novamente
        chamadas[2]["ne"].Ativo.ShouldBeTrue();
        chamadas[2]["se"].Ativo.ShouldBeTrue();
        chamadas[2]["sul"].Ativo.ShouldBeTrue();
    }

    [Fact(DisplayName = "[AlertaSonoroBusiness] - Deve lidar com timeout do Firebase service")]
    [Trait("Business", "AlertaSonoroBusiness")]
    public async Task Timeout_FirebaseService_DeveLidarCorretamente()
    {
        // Arrange
        var scopes = AlertaSonoroFixture.GerarScopeUnico();
        var perfil = AlertaSonoroFixture.GerarPerfilComScopes(scopes);
        
        DependencyInjectorFactory.Mocker.Get<IUserContextTest>().DefinirPerfil(perfil);

        DependencyInjectorFactory.Mocker.GetMock<IAlertaSonoroFirebaseDatabaseService>()
            .Setup(service => service.SetAsync(It.IsAny<IDictionary<string, AlertaSonoroRealtimeDto>>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new TimeoutException("Timeout no Firebase"));

        var cancellationToken = CancellationToken.None;

        // Act & Assert
        var exception = await Assert.ThrowsAsync<TimeoutException>(
            () => DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.AlertaSonoroBusiness>()
                .AtivarAsync(cancellationToken));
        
        exception.Message.ShouldBe("Timeout no Firebase");
    }

    [Fact(DisplayName = "[AlertaSonoroBusiness] - Deve validar integridade dos dados enviados")]
    [Trait("Business", "AlertaSonoroBusiness")]
    public async Task ValidarIntegridade_DadosEnviados_DeveEstarCorretos()
    {
        // Arrange
        var scopes = AlertaSonoroFixture.GerarScopeUnico();
        var perfil = AlertaSonoroFixture.GerarPerfilComScopes(scopes);
        
        DependencyInjectorFactory.Mocker.Get<IUserContextTest>().DefinirPerfil(perfil);
        
        var cancellationToken = CancellationToken.None;
        AlertaSonoroRealtimeDto? dadosCapturados = null;

        DependencyInjectorFactory.Mocker.GetMock<IAlertaSonoroFirebaseDatabaseService>()
            .Setup(service => service.SetAsync(It.IsAny<IDictionary<string, AlertaSonoroRealtimeDto>>(), It.IsAny<CancellationToken>()))
            .Callback<IDictionary<string, AlertaSonoroRealtimeDto>, CancellationToken>((dict, token) => 
            {
                dadosCapturados = dict["n"];
            })
            .Returns(Task.CompletedTask);

        // Act
        await DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.AlertaSonoroBusiness>()
            .AtivarAsync(cancellationToken);

        // Assert
        dadosCapturados.ShouldNotBeNull();
        dadosCapturados.Ativo.ShouldBeTrue();
        dadosCapturados.DataDeAtualizacao.ShouldBeInRange(DateTime.UtcNow.AddMinutes(-1), DateTime.UtcNow.AddMinutes(1));
    }

    [Theory(DisplayName = "[AlertaSonoroBusiness] - Deve processar diferentes tipos de perfil")]
    [Trait("Business", "AlertaSonoroBusiness")]
    [InlineData("Operador", "operador")]
    [InlineData("Supervisor", "supervisor")]
    [InlineData("Administrador", "administrador")]
    public async Task DiferentesPerfis_DeveProcessarCorretamente(string nomePerfil, string codigoPerfil)
    {
        // Arrange
        var scopes = new List<Scope>
        {
            new("CENTROS", $"PERFIL-{codigoPerfil.ToUpper()}", $"Perfil {nomePerfil}")
        };
        var perfil = new Perfil(scopes, new List<string>(), nomePerfil, codigoPerfil);
        
        DependencyInjectorFactory.Mocker.Get<IUserContextTest>().DefinirPerfil(perfil);
        
        var cancellationToken = CancellationToken.None;

        // Act
        await DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.AlertaSonoroBusiness>()
            .AtivarAsync(cancellationToken);

        // Assert
        DependencyInjectorFactory.Mocker.GetMock<IAlertaSonoroFirebaseDatabaseService>().Verify(
            service => service.SetAsync(
                It.Is<IDictionary<string, AlertaSonoroRealtimeDto>>(dict =>
                    dict.ContainsKey($"perfil-{codigoPerfil}")),
                cancellationToken),
            Times.Once);
    }

    [Fact(DisplayName = "[AlertaSonoroBusiness] - Deve funcionar com perfil contendo operações")]
    [Trait("Business", "AlertaSonoroBusiness")]
    public async Task PerfilComOperacoes_DeveFuncionarNormalmente()
    {
        // Arrange
        var scopes = AlertaSonoroFixture.GerarScopeUnico();
        var perfil = AlertaSonoroFixture.GerarPerfilComOperacoes(scopes);
        
        DependencyInjectorFactory.Mocker.Get<IUserContextTest>().DefinirPerfil(perfil);
        
        var cancellationToken = CancellationToken.None;

        // Act
        await DependencyInjectorFactory.Mocker.CreateInstance<ONS.SINapse.Business.Imp.Business.AlertaSonoroBusiness>()
            .AtivarAsync(cancellationToken);

        // Assert
        DependencyInjectorFactory.Mocker.GetMock<IAlertaSonoroFirebaseDatabaseService>().Verify(
            service => service.SetAsync(
                It.Is<IDictionary<string, AlertaSonoroRealtimeDto>>(dict =>
                    dict.ContainsKey("n") &&
                    dict["n"].Ativo == true),
                cancellationToken),
            Times.Once);
    }
}
