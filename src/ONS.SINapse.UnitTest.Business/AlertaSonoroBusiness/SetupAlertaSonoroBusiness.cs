using ONS.SINapse.UnitTest.Business.Fixtures.Collections;
using ONS.SINapse.UnitTest.Shared;

namespace ONS.SINapse.UnitTest.Business.AlertaSonoroBusiness;

[Collection(nameof(AlertaSonoroBusinessCollection))]
public partial class AlertaSonoroBusinessTest
{
    protected readonly MockDependencyInjectorFactory DependencyInjectorFactory;

    public AlertaSonoroBusinessTest()
    {
        DependencyInjectorFactory = new MockDependencyInjectorFactory();
        DependencyInjectorFactory.RegisterMocks();
    }
}
