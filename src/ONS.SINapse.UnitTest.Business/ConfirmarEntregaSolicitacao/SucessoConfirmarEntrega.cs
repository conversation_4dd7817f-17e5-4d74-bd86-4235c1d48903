using Microsoft.Extensions.DependencyInjection;
using ONS.SINapse.Repository.IRepository;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using System.Linq.Expressions;
using ONS.SINapse.UnitTest.Shared.Fixtures;

namespace ONS.SINapse.UnitTest.Business.ConfirmarEntregaSolicitacao
{
    public partial class ConfirmarEntregaTest
    {
        [Fact(DisplayName = "[Confirmar Entrega] - Deve confirmar entrega")]
        [Trait("Business", "Confirmar Entrega")]
        public async Task DeveConfirmarEntrega()
        {
            // Arrange
            var solicitacao = _solicitacaoFixture.GerarSolicitacaoPendente();
            solicitacao.AdicionarMensagemNoChat("Teste", UsuarioFixture.GerarUsuarioValido(), _perfilFixture.GerarPerfilValido());

            var origem = solicitacao.Origem.Codigo;

            ConfigurarMockUserContext(origem);

            _dependencyInjectorFactory.Mocker
                .GetMock<ISolicitacaoRepository>()
                .Setup(repository => repository.GetAsync(It.IsAny<Expression<Func<Entities.Entities.Solicitacao, bool>>>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync([solicitacao]);

            var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

            var command = new ConfirmarEntregaCommand([solicitacao.Id]);

            // Act
            var response = await mediator.EnviarComandoAsync<ConfirmarEntregaCommand, ResultadoConfirmacaoDeEntregaDto>(command, CancellationToken.None);

            // Assert
            Assert.NotNull(response);
            response.ValidationResult.IsValid.ShouldBeTrue(response.ValidationResult.ToString());

            response.ValidationResult.Errors.ShouldBeEmpty();

            response.Entregues.ShouldBe(1);
            response.SolicitacoesEntregues.Count.ShouldBe(1);

            _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoRepository>().Verify(
                repository => repository.GetAsync(It.IsAny<Expression<Func<Entities.Entities.Solicitacao, bool>>>(), It.IsAny<CancellationToken>()),
                Times.Once);

            _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoRepository>().Verify(
                repository => repository.BulkUpdateChatAsync(It.IsAny<List<Entities.Entities.Solicitacao>>(), It.IsAny<CancellationToken>()),
                Times.Once);

            var enviarMensagemChatSolicitacaoNoFirebaseCommandPublished =
                await _dependencyInjectorFactory.Harness.Published.Any<ConfirmarEntregasSolicitacaoNoFirebaseCommand>();

            enviarMensagemChatSolicitacaoNoFirebaseCommandPublished.ShouldBeTrue();
        }

        [Fact(DisplayName = "[Confirmar Entrega] - Deve retornar Sucesso sem mensagens para confirmar")]
        [Trait("Business", "Confirmar Entrega")]
        public async Task DeveRetornarSucessoSemMensagensParaConfirmar()
        {
            // Arrange
            var solicitacao = _solicitacaoFixture.GerarSolicitacaoPendente();
            var origem = solicitacao.Origem.Codigo;

            ConfigurarMockUserContext(origem);

            _dependencyInjectorFactory.Mocker
                .GetMock<ISolicitacaoRepository>()
                .Setup(repository => repository.GetAsync(It.IsAny<Expression<Func<Entities.Entities.Solicitacao, bool>>>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync([solicitacao]);

            var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

            var command = new ConfirmarEntregaCommand([solicitacao.Id]);

            // Act
            var response = await mediator.EnviarComandoAsync<ConfirmarEntregaCommand, ResultadoConfirmacaoDeEntregaDto>(command, CancellationToken.None);

            // Assert
            Assert.NotNull(response);
            response.ValidationResult.IsValid.ShouldBeTrue(response.ValidationResult.ToString());

            response.ValidationResult.Errors.ShouldBeEmpty();

            response.Entregues.ShouldBe(0);
            response.SolicitacoesEntregues.Count.ShouldBe(1);

            _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoRepository>().Verify(
                repository => repository.GetAsync(It.IsAny<Expression<Func<Entities.Entities.Solicitacao, bool>>>(), It.IsAny<CancellationToken>()),
                Times.Once);

            _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoRepository>().Verify(
                repository => repository.BulkUpdateChatAsync(It.IsAny<List<Entities.Entities.Solicitacao>>(), It.IsAny<CancellationToken>()),
                Times.Never);

            var enviarMensagemChatSolicitacaoNoFirebaseCommandPublished =
                await _dependencyInjectorFactory.Harness.Published.Any<ConfirmarEntregasSolicitacaoNoFirebaseCommand>();

            enviarMensagemChatSolicitacaoNoFirebaseCommandPublished.ShouldBeFalse();
        }
    }
}
