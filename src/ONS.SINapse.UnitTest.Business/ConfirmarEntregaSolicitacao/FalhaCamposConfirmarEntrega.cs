using Microsoft.Extensions.DependencyInjection;
using ONS.SINapse.Repository.IRepository.Firebase;
using ONS.SINapse.Repository.IRepository.InMemory;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using System.Linq.Expressions;

namespace ONS.SINapse.UnitTest.Business.ConfirmarEntregaSolicitacao;

public partial class ConfirmarEntregaTest
{
    [Fact(DisplayName = "[Confirmar Entrega] - Deve retornar erro quando solicitacao não for informada")]
    [Trait("Business", "Confirmar Entrega")]
    public async Task ConfirmarEntrega_DeveRetornarErro_SolicitacaoNaoInformada()
    {
        // Arrange
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoPendente();
        var origem = solicitacao.Origem.Codigo;

        ConfigurarMockUserContext(origem);

        _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoMemoryRepository>()
            .Setup(repository => repository.GetList(It.IsAny<Expression<Func<Entities.Entities.Solicitacao, bool>>>()))
            .Returns([solicitacao]);

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        var command = new ConfirmarEntregaCommand([]);

        // Act
        var response = await mediator.EnviarComandoAsync<ConfirmarEntregaCommand, ResultadoConfirmacaoDeEntregaDto>(command, CancellationToken.None);

        // Assert
        Assert.NotNull(response);
        response.ValidationResult.IsValid.ShouldBeFalse(response.ValidationResult.ToString());
        response.ValidationResult.Errors.Any(x => x.ErrorMessage == "Solicitações não informadas.").ShouldBeTrue();

        response.Entregues.ShouldBe(0);
        response.SolicitacoesEntregues.Count.ShouldBe(0);

        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoMemoryRepository>().Verify(
            repository => repository.GetList(It.IsAny<Expression<Func<Entities.Entities.Solicitacao, bool>>>()),
            Times.Never);

        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoFirebaseRepository>().Verify(
            repository => repository.PatchFlattenAsync(It.IsAny<ChatDeSolicitacaoFirebaseDto>(), It.IsAny<CancellationToken>()),
            Times.Never);

        var enviarMensagemChatSolicitacaoNoFirebaseCommandPublished =
            await _dependencyInjectorFactory.Harness.Published.Any<ConfirmarEntregasSolicitacaoNoFirebaseCommand>();

        enviarMensagemChatSolicitacaoNoFirebaseCommandPublished.ShouldBeFalse();
    }
}
