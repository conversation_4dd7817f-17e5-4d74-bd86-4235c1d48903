using Microsoft.Extensions.DependencyInjection;
using ONS.SINapse.Repository.IRepository.Firebase;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands.Firebase;

namespace ONS.SINapse.UnitTest.Business.ConfirmarEntregaSolicitacao;

public partial class ConfirmarEntregaTest
{
    [Fact(DisplayName = "[Confirmar Entrega] - Deve retornar erro quando solicitacao não for informada")]
    [Trait("Business", "Confirmar Entrega")]
    public async Task ConfirmarEntrega_DeveRetornarErro_SolicitacaoNaoInformada()
    {
        // Arrange
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoPendente();
        var origem = solicitacao.Origem.Codigo;

        ConfigurarMockUserContext(origem);

   _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoFirebaseRepository>()
            .Setup(repository => repository.GetByIdAsync(It.IsAny<string[]>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([solicitacao]);

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        var command = new ConfirmarEntregaCommand([]);

        // Act
        var response = await mediator.EnviarComandoAsync<ConfirmarEntregaCommand, ResultadoConfirmacaoDeEntregaDto>(command, CancellationToken.None);

        // Assert
        Assert.NotNull(response);
        response.ValidationResult.IsValid.ShouldBeFalse(response.ValidationResult.ToString());
        response.ValidationResult.Errors.Any(x => x.ErrorMessage == "Solicitações não informadas.").ShouldBeTrue();

        response.Entregues.ShouldBe(0);
        response.SolicitacoesEntregues.Count.ShouldBe(0);

        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoFirebaseRepository>().Verify(
            repository => repository.GetByIdAsync(It.IsAny<string[]>(), It.IsAny<CancellationToken>()),
            Times.Never);

        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoFirebaseRepository>().Verify(
            repository => repository.AddUpdateAsync(It.IsAny<Dictionary<string, object?>>(), It.IsAny<CancellationToken>()),
            Times.Never);

        var criarMensagemNoChatDeSolicitacaoFirebaseCommandPublished =
            await _dependencyInjectorFactory.Harness.Published.Any<EntregaDeSolicitacaoNoFirebaseCommand>();

        criarMensagemNoChatDeSolicitacaoFirebaseCommandPublished.ShouldBeFalse();
    }
}
