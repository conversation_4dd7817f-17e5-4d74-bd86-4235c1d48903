using FluentValidation.Results;
using ONS.SINapse.Repository.IRepository.Firebase;
using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Shared.Messages;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands.Firebase;

namespace ONS.SINapse.UnitTest.Business.ConfirmarEntregaSolicitacao
{
    public partial class ConfirmarEntregaTest
    {
        [Fact(DisplayName = "[Confirmar Entrega] - Deve retornar um erro em confirmar entrega chat solicitação no firebase")]
        [Trait("Business", "Confirmar Entrega")]
        public async Task ConfirmarEntrega_DeveRetornarUmErro_FirebaseCommand()
        {
            // Arrange
            var solicitacao = _solicitacaoFixture.GerarSolicitacaoPendente();

            var command = new ConfirmarEntregaCommand([solicitacao.Id]);

            _dependencyInjectorFactory.Mocker
                .GetMock<ISolicitacaoFirebaseRepository>()
                .Setup(repository => repository.GetByIdAsync(It.IsAny<string[]>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync([solicitacao]);

            var result = new ValidationResult();
            result.AdicionarErro("Erro ao enviar command para o firebase");

            var mediatorMock = _dependencyInjectorFactory.Mocker.GetMock<IMediatorHandler>();

            mediatorMock
                .Setup(x => x.EnviarComandoAsync(
                    It.Is<Command>(cmd => cmd is EntregaDeSolicitacaoNoFirebaseCommand),
                    It.IsAny<CancellationToken>()))
                .ReturnsAsync(result);

            // Act
            var response =
                await mediatorMock.Object
                    .EnviarComandoAsync<ConfirmarEntregaCommand, ResultadoConfirmacaoDeEntregaDto>(
                        command, CancellationToken.None);

            // Assert
            Assert.NotNull(response);
            response.ValidationResult.IsValid.ShouldBeFalse(response.ToString());
            response.ValidationResult.Errors.ShouldNotBeEmpty();

            response.Entregues.ShouldBe(1);
            response.SolicitacoesEntregues.Count.ShouldBe(1);

            _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoFirebaseRepository>().Verify(
                repository => repository.GetByIdAsync(It.IsAny<string[]>(), It.IsAny<CancellationToken>()),
                Times.Once);

            _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoFirebaseRepository>().Verify(
                repository => repository.AddUpdateAsync(It.IsAny<Dictionary<string, object?>>(), It.IsAny<CancellationToken>()),
                Times.Never);

            var criarMensagemNoChatDeSolicitacaoFirebaseCommandPublished =
                await _dependencyInjectorFactory.Harness.Published.Any<EntregaDeSolicitacaoNoFirebaseCommand>();

            criarMensagemNoChatDeSolicitacaoFirebaseCommandPublished.ShouldBeFalse();
        }
    }
}