using MassTransit;
using Microsoft.Extensions.DependencyInjection;
using MongoDB.Driver;
using ONS.SINapse.Repository.Imp.Context;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using ONS.SINapse.Solicitacao.Dtos;

namespace ONS.SINapse.UnitTest.Business.ExportarConsultaHistorico;

public partial class ExportarConsultaHistoricoTests
{
    [Fact(DisplayName = "Deve exportar consulta histórico")]
    [Trait("Business", "Exportar Consulta Historico")]
    public async Task DeveExportarConsultaHistorico()
    {
        // Arrange
        var repositoryMock = _dependencyInjectorFactory.Mocker
            .GetMock<IMongoReadOnlyConnection>();

        var solicitacao = new ArquivoConsultaHistoricoDto(string.Empty, "NE", string.Empty, string.Empty, string.Empty);

        repositoryMock.Setup(x =>
                x.AggregateAsync(
                    It.IsAny<PipelineDefinition<ArquivoConsultaHistoricoDto, ArquivoConsultaHistoricoDto>>(),
                    It.IsAny<CancellationToken>()))
            .ReturnsAsync([solicitacao]);

        var centro = "NE"; 
        ConfigurarMockUserContext(centro);

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        var command = new ExportarConsultaHistoricoCommand();

        // Act

        var response =
            await mediator.EnviarComandoAsync<ExportarConsultaHistoricoCommand, ArquivoResultDto>(
                command, CancellationToken.None);

        // Assert

        Assert.NotNull(response);
        response.ValidationResult.IsValid.ShouldBeTrue(response.ValidationResult.ToString());
        response.ValidationResult.Errors.ShouldBeEmpty();

        repositoryMock.Verify(
            repository =>
                repository.AggregateAsync(
                    It.IsAny<PipelineDefinition<ArquivoConsultaHistoricoDto, ArquivoConsultaHistoricoDto>>(),
                    It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact(DisplayName = "Deve exportar mesmo sem resultados")]
    [Trait("Business", "Exportar Consulta Historico")]
    public async Task DeveExportarMesmoSemResultados()
    {
        // Arrange
        var repositoryMock = _dependencyInjectorFactory.Mocker
            .GetMock<IMongoReadOnlyConnection>();

        repositoryMock.Setup(x =>
            x.AggregateAsync(
                It.IsAny<PipelineDefinition<ArquivoConsultaHistoricoDto, ArquivoConsultaHistoricoDto>>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        ConfigurarMockUserContext("NE");

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();
        var command = new ExportarConsultaHistoricoCommand();

        // Act
        var response = await mediator.EnviarComandoAsync<ExportarConsultaHistoricoCommand, ArquivoResultDto>(
            command, CancellationToken.None);

        // Assert
        Assert.NotNull(response);
        response.ValidationResult.IsValid.ShouldBeTrue(response.ValidationResult.ToString());
        response.ValidationResult.Errors.ShouldBeEmpty();

        repositoryMock.Verify(
            repository =>
                repository.AggregateAsync(
                    It.IsAny<PipelineDefinition<ArquivoConsultaHistoricoDto, ArquivoConsultaHistoricoDto>>(),
                    It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact(DisplayName = "Deve lançar exceção ao falhar agregação")]
    [Trait("Business", "Exportar Consulta Historico")]
    public async Task DeveLancarExcecaoQuandoAggregateFalha()
    {
        // Arrange
        var repositoryMock = _dependencyInjectorFactory.Mocker
            .GetMock<IMongoReadOnlyConnection>();

        repositoryMock.Setup(x =>
            x.AggregateAsync(
                It.IsAny<PipelineDefinition<ArquivoConsultaHistoricoDto, ArquivoConsultaHistoricoDto>>(),
                It.IsAny<CancellationToken>()))
            .ThrowsAsync(new Exception("Erro no Mongo"));

        ConfigurarMockUserContext("NE");

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();
        var command = new ExportarConsultaHistoricoCommand();

        // Act & Assert
        await Assert.ThrowsAsync<RequestFaultException>(async () =>
            await mediator.EnviarComandoAsync<ExportarConsultaHistoricoCommand, ArquivoResultDto>(
                command, CancellationToken.None));
    }
}
