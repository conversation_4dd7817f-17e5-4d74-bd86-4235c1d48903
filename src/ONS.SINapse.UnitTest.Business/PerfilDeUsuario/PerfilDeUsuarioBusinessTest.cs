using ONS.SINapse.Business.Imp.Business;
using ONS.SINapse.CacheSync.Services;
using ONS.SINapse.Repository.IRepository.DadosCadastrais;
using ONS.SINapse.Shared.DTO;
using System.Linq.Expressions;

namespace ONS.SINapse.UnitTest.Business.PerfilDeUsuario;

public partial class PerfilDeUsuarioBusinessTest
{
    [Fact(DisplayName = "[PerfilDeUsuarioBusiness] - Deve retornar usuario perfil corretamente")]
    [Trait("Business", "PerfilDeUsuarioBusiness")]
    public async Task ObterPerfilAsync_DeveRetornarUsuarioPerfilCorretamente()
    {
        // Arrange
        ConfigurarMocks();

        // Act
        var result = await _dependencyInjectorFactory.Mocker.CreateInstance<PerfilDeUsuarioBusiness>()
            .ObterPerfilAsync(CancellationToken.None);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("TestUser", result.Sid);
        Assert.Equal("TestLogin", result.Nome);
        Assert.Equal("TestSid", result.Login);
        Assert.NotEmpty(result.Perfis);

        _dependencyInjectorFactory.Mocker.GetMock<IAgenteRepository>().Verify(
            repo => repo.GetAsync(It.IsAny<List<string>>(), It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact(DisplayName = "[PerfilDeUsuarioBusiness] - Deve alterar visões com equipamentos válidos")]
    [Trait("Business", "PerfilDeUsuarioBusiness")]
    public async Task AlterarVisoesAsync_DeveAlterarComEquipamentosValidos()
    {
        // Arrange
        var centro = "NE";
        var equipamentos = new[] { "EQ1", "EQ2" };
        var visoes = new List<SelecaoDeVisaoDeUsuarioDto>
        {
            new(centro, "VisaoTeste", equipamentos)
        };

        ConfigurarMockAlterarVisoesAsync(visoes);

        // Act
        var result = await _dependencyInjectorFactory.Mocker.CreateInstance<PerfilDeUsuarioBusiness>()
            .AlterarVisoesAsync(visoes, CancellationToken.None);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);

        var item = result.First();
        Assert.Equal(centro, item.CodigoCentroAgente);
        Assert.Equal("VisaoTeste", item.Id);
        Assert.Equal(equipamentos, item.EquipamentosDeManobra);
        Assert.Empty(item.Erros);
    }


    [Fact(DisplayName = "[PerfilDeUsuarioBusiness] - Deve alterar visões com equipamentos inválidos")]
    [Trait("Business", "PerfilDeUsuarioBusiness")]
    public async Task AlterarVisoesAsync_DeveAlterarComEquipamentosInvalidos()
    {
        // Arrange
        var centro = "NE";
        var equipamentos = new[] { "EQ1", "EQ2" };
        var visoes = new List<SelecaoDeVisaoDeUsuarioDto>
        {
            new(centro, "VisaoTest", equipamentos)
        };

        var dataset = new LocalDeOperacaoDataset("desc", "desc", []);
        ConfigurarMockAlterarVisoesAsync(visoes, new List<LocalDeOperacaoDataset> { dataset });

        // Act
        var result = await _dependencyInjectorFactory.Mocker.CreateInstance<PerfilDeUsuarioBusiness>()
            .AlterarVisoesAsync(visoes, CancellationToken.None);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);

        var item = result.First();
        Assert.Equal(centro, item.CodigoCentroAgente);
        Assert.Equal("VisaoTest", item.Id);
        Assert.Equal(equipamentos, item.EquipamentosDeManobra);
        Assert.Equal(2, item.Erros.Length);
        Assert.All(item.Erros, e => Assert.Equal("Equipamento não localizado.", e.Mensagem));
    }

    [Fact(DisplayName = "[PerfilDeUsuarioBusiness] - Deve retornar vazio quando não houver visões")]
    [Trait("Business", "PerfilDeUsuarioBusiness")]
    public async Task AlterarVisoesAsync_DeveRetornarVazioQuandoNaoHouverVisoes()
    {
        // Arrange
        var visoes = new List<SelecaoDeVisaoDeUsuarioDto>();

        // Act
        var result = await _dependencyInjectorFactory.Mocker.CreateInstance<PerfilDeUsuarioBusiness>()
            .AlterarVisoesAsync(visoes, CancellationToken.None);

        // Assert
        Assert.Empty(result);
    }
}
