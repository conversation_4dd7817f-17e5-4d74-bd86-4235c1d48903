using ONS.SINapse.CacheSync.Services;
using ONS.SINapse.Repository.IRepository.DadosCadastrais;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.UnitTest.Business.Fixtures.Collections;
using ONS.SINapse.UnitTest.Shared;
using ONS.SINapse.UnitTest.Shared.Fixtures;
using System.Linq.Expressions;

namespace ONS.SINapse.UnitTest.Business.PerfilDeUsuario;

[Collection(nameof(PerfilDeUsuarioBusinessCollection))]
public partial class PerfilDeUsuarioBusinessTest
{
    private readonly MockDependencyInjectorFactory _dependencyInjectorFactory;
    private readonly AgenteFixture _agenteFixture;
    private readonly LocalDeOperacaoDatasetFixture _localDeOperacaoDatasetFixture;

    public PerfilDeUsuarioBusinessTest(AgenteFixture agenteFixture, LocalDeOperacaoDatasetFixture localDeOperacaoDatasetFixture)
    {
        _agenteFixture = agenteFixture;
        _localDeOperacaoDatasetFixture = localDeOperacaoDatasetFixture;
        _dependencyInjectorFactory = new MockDependencyInjectorFactory();
        _dependencyInjectorFactory.RegisterMocks();
    }

    private void ConfigurarMocks()
    {
        _dependencyInjectorFactory.Mocker.GetMock<IAgenteRepository>()
            .Setup(repo => repo.GetAsync(It.IsAny<List<string>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([_agenteFixture.GerarAgente()]);
    }

    private void ConfigurarMockAlterarVisoesAsync(
        IEnumerable<SelecaoDeVisaoDeUsuarioDto> visoes,
        ICollection<LocalDeOperacaoDataset>? datasets = null)
    {
        // Se não passar datasets, gera um válido com os equipamentos das visões
        if (datasets == null && visoes.Any())
        {
            var equipamentos = visoes.SelectMany(v => v.EquipamentosDeManobra).Distinct().ToList();
            var locais = equipamentos.Select(eq => new LocalDeOperacaoDatasetItem(eq, eq)).ToList();
            var dataset = new LocalDeOperacaoDataset("desc", "desc", locais);
            datasets = new List<LocalDeOperacaoDataset> { dataset };
        }

        _dependencyInjectorFactory.Mocker.GetMock<ISinapseDadosDatasetQueryService>()
            .Setup(s => s.GetDatasetAsync(
                It.IsAny<Expression<Func<ISinapseDadosDatasetService, Task<ICollection<LocalDeOperacaoDataset>>>>>(),
                It.IsAny<CancellationToken>()
            ))
            .ReturnsAsync(datasets ?? []);
    }
}
