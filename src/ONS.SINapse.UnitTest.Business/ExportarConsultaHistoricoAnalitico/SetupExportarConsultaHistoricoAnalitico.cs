using ONS.SINapse.Shared.Identity;
using ONS.SINapse.UnitTest.Business.Fixtures.Collections;
using ONS.SINapse.UnitTest.Shared;

namespace ONS.SINapse.UnitTest.Business.ExportarConsultaHistorico;

[Collection(nameof(ExportarConsultaHistoricoAnaliticoCollection))]
public partial class ExportarConsultaHistoricoAnaliticoTests
{
    private readonly MockDependencyInjectorFactory _dependencyInjectorFactory;

    public ExportarConsultaHistoricoAnaliticoTests()
    {
        _dependencyInjectorFactory = new MockDependencyInjectorFactory();
        _dependencyInjectorFactory.RegisterMocks();
    }

    private void ConfigurarMockUserContext(string destino)
    {
        var perfil = new Perfil([new Scope("CENTROS", destino, destino)], [], destino, destino);
        _dependencyInjectorFactory.Mocker
            .GetMock<IUserContext>()
            .SetupGet(x => x.Perfil)
            .Returns(perfil);
    }
}
