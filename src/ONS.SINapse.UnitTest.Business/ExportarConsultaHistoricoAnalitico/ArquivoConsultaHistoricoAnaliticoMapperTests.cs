using ONS.SINapse.Solicitacao.Mapper;

namespace ONS.SINapse.UnitTest.Business.ExportarConsultaHistoricoAnalitico;

public class ArquivoConsultaHistoricoAnaliticoMapperTests
{
    [Fact]
    public void SanitizarMotivoImpedimentoString_DeveSanitizarQuebraDeLinhasDoMotivoImpedimento()
    {
        var input = $"Linha 1{Environment.NewLine}{Environment.NewLine}Linha 2 sem ponto{Environment.NewLine}Linha 3 com ponto.{Environment.NewLine}Última linha";
        
        const string expected = "Linha 1. Linha 2 sem ponto. Linha 3 com ponto. Última linha";

        var output = ArquivoConsultaHistoricoAnaliticoMapper.SanitizarMotivoImpedimentoString(input);
        
        Assert.Equal(expected, output);
    }
}