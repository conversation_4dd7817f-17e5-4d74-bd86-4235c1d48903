using MassTransit;
using Microsoft.Extensions.DependencyInjection;
using MongoDB.Driver;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Repository.Imp.Context;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using ONS.SINapse.Solicitacao.Dtos;

namespace ONS.SINapse.UnitTest.Business.ExportarConsultaHistoricoAnalitico;

public partial class ExportarConsultaHistoricoAnaliticoTests
{
    [Fact(DisplayName = "Deve exportar consulta histórico")]
    [Trait("Business", "Exportar Consulta Historico")]
    public async Task DeveExportarConsultaHistoricoAnalitico()
    {
        // Arrange
        var repositoryMock = _dependencyInjectorFactory.Mocker
            .GetMock<IMongoReadOnlyConnection>();

        var solicitacao = new ArquivoConsultaHistoricoAnaliticaDto("Teste", "CN", "NE", "Teste Unitário", "Usuário de Teste");
        solicitacao.Motivo = "Teste Unitário";
        solicitacao.InformacaoAdicional = "Teste Unitário";
        solicitacao.DataDeCriacao = DateTime.UtcNow;
        solicitacao.CodigoStatus = (short)StatusDeSolicitacao.Finalizada;
        solicitacao.DataDeAlteracao = DateTime.UtcNow;
        solicitacao.UsuarioDeAlteracao = "Usuário de Teste";
        solicitacao.MotivoImpedimento = "Teste Unitário";
        solicitacao.UsuarioEntrega = "Usuário de Teste";
        solicitacao.DataDeEntrega = DateTime.UtcNow;
        solicitacao.UsuarioLeitura = "Usuário de Teste";
        solicitacao.DataDeLeitura = DateTime.UtcNow;

        repositoryMock.Setup(x =>
                x.AggregateAsync(
                    It.IsAny<PipelineDefinition<ArquivoConsultaHistoricoAnaliticaDto, ArquivoConsultaHistoricoAnaliticaDto>>(),
                    It.IsAny<CancellationToken>()))
            .ReturnsAsync([solicitacao]);

        var centro = "NE"; 
        ConfigurarMockUserContext(centro);

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        var command = new ExportarConsultaHistoricoAnaliticaCommand();
        command.Origem = "CN";
        command.Destino = "NE";
        command.Mensagem = "Teste Unitário";
        command.Status = StatusDeSolicitacao.Cancelada;
        command.PeriodoInicial = DateTime.UtcNow;
        command.PeriodoFinal = DateTime.UtcNow;

        // Act

        var response =
            await mediator.EnviarComandoAsync<ExportarConsultaHistoricoAnaliticaCommand, ArquivoResultDto>(
                command, CancellationToken.None);

        // Assert

        Assert.NotNull(response);
        response.ValidationResult.IsValid.ShouldBeTrue(response.ValidationResult.ToString());
        response.ValidationResult.Errors.ShouldBeEmpty();

        repositoryMock.Verify(
            repository =>
                repository.AggregateAsync(
                    It.IsAny<PipelineDefinition<ArquivoConsultaHistoricoAnaliticaDto, ArquivoConsultaHistoricoAnaliticaDto>>(),
                    It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact(DisplayName = "Deve exportar mesmo sem resultados")]
    [Trait("Business", "Exportar Consulta Historico")]
    public async Task DeveExportarMesmoSemResultados()
    {
        // Arrange
        var repositoryMock = _dependencyInjectorFactory.Mocker
            .GetMock<IMongoReadOnlyConnection>();

        repositoryMock.Setup(x =>
            x.AggregateAsync(
                It.IsAny<PipelineDefinition<ArquivoConsultaHistoricoAnaliticaDto, ArquivoConsultaHistoricoAnaliticaDto>>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        ConfigurarMockUserContext("NE");

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();
        var command = new ExportarConsultaHistoricoAnaliticaCommand();
        command.Origem = "CN";
        command.Destino = "NE";
        command.Mensagem = "Teste Unitário";
        command.Status = StatusDeSolicitacao.Cancelada;
        command.PeriodoInicial = DateTime.UtcNow;
        command.PeriodoFinal = DateTime.UtcNow;

        // Act
        var response = await mediator.EnviarComandoAsync<ExportarConsultaHistoricoAnaliticaCommand, ArquivoResultDto>(
            command, CancellationToken.None);

        // Assert
        Assert.NotNull(response);
        response.ValidationResult.IsValid.ShouldBeTrue(response.ValidationResult.ToString());
        response.ValidationResult.Errors.ShouldBeEmpty();

        repositoryMock.Verify(
            repository =>
                repository.AggregateAsync(
                    It.IsAny<PipelineDefinition<ArquivoConsultaHistoricoAnaliticaDto, ArquivoConsultaHistoricoAnaliticaDto>>(),
                    It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact(DisplayName = "Deve lançar exceção ao falhar agregação")]
    [Trait("Business", "Exportar Consulta Historico")]
    public async Task DeveLancarExcecaoQuandoAggregateFalha()
    {
        // Arrange
        var repositoryMock = _dependencyInjectorFactory.Mocker
            .GetMock<IMongoReadOnlyConnection>();

        repositoryMock.Setup(x =>
            x.AggregateAsync(
                It.IsAny<PipelineDefinition<ArquivoConsultaHistoricoAnaliticaDto, ArquivoConsultaHistoricoAnaliticaDto>>(),
                It.IsAny<CancellationToken>()))
            .ThrowsAsync(new Exception("Erro no Mongo"));

        ConfigurarMockUserContext("NE");

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();
        var command = new ExportarConsultaHistoricoAnaliticaCommand();
        command.Origem = "CN";
        command.Destino = "NE";
        command.Mensagem = "Teste Unitário";
        command.Status = StatusDeSolicitacao.Cancelada;
        command.PeriodoInicial = DateTime.UtcNow;
        command.PeriodoFinal = DateTime.UtcNow;

        // Act & Assert
        await Assert.ThrowsAsync<RequestFaultException>(async () =>
            await mediator.EnviarComandoAsync<ExportarConsultaHistoricoAnaliticaCommand, ArquivoResultDto>(
                command, CancellationToken.None));
    }
    
    [Fact(DisplayName = "Deve Exportar Registros Sem Quebra De Linha Para Motivo Impedimento")]
    [Trait("Business", "Exportar Consulta Historico")]
    public async Task DeveExportarRegistrosSemQuebraDeLinhaParaMotivoImpedimento()
    {
        // Arrange
        var repositoryMock = _dependencyInjectorFactory.Mocker
            .GetMock<IMongoReadOnlyConnection>();

        repositoryMock.Setup(x =>
                x.AggregateAsync(
                    It.IsAny<PipelineDefinition<ArquivoConsultaHistoricoAnaliticaDto, ArquivoConsultaHistoricoAnaliticaDto>>(),
                    It.IsAny<CancellationToken>()))
            .ReturnsAsync(
            [
                new ArquivoConsultaHistoricoAnaliticaDto(
                    codigo: "20250620135358-NE-7Ahajw-AOP",
                    origem: "COSR-NE",
                    destino: "AUREN OPERAÇÕES",
                    mensagem: "Conj. Araçás |  (Ponto de Partida: 1 MW) Limitar em 1 MW a Geração Eólica", 
                    usuarioDeCriacao: "ons\\teste-aplic4")
                {
                    MotivoImpedimento = $"Linha 1{Environment.NewLine}{Environment.NewLine}Linha 2 sem ponto{Environment.NewLine}Linha 3 com ponto.{Environment.NewLine}Última linha"
                }
            ]);

        ConfigurarMockUserContext("NE");

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();
        var command = new ExportarConsultaHistoricoAnaliticaCommand();
        command.Origem = "CN";
        command.Destino = "NE";
        command.Mensagem = "Teste Unitário";
        command.Status = StatusDeSolicitacao.Cancelada;
        command.PeriodoInicial = DateTime.UtcNow;
        command.PeriodoFinal = DateTime.UtcNow;

        // Act
        var response = await mediator.EnviarComandoAsync<ExportarConsultaHistoricoAnaliticaCommand, ArquivoResultDto>(
            command, CancellationToken.None);

        // Assert
        response.ShouldNotBeNull();
        response.ValidationResult.IsValid.ShouldBeTrue(response.ValidationResult.ToString());
        response.ValidationResult.Errors.ShouldBeEmpty();

        response.Arquivo.ShouldNotBeNull();
        using var ms = new MemoryStream(response.Arquivo);
        using var sr = new StreamReader(ms);
        
        _ = await sr.ReadLineAsync(); // pula header csv
        var text = await sr.ReadToEndAsync();
        
        var motivo = text.Split(';').Last();
        motivo.ShouldBe("Linha 1. Linha 2 sem ponto. Linha 3 com ponto. Última linha\r\n");
    }
}
