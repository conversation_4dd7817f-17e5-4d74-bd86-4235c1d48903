using MassTransit;
using Microsoft.Extensions.DependencyInjection;
using MongoDB.Driver;
using ONS.SINapse.Repository.Imp.Context;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using ONS.SINapse.Solicitacao.Dtos;

namespace ONS.SINapse.UnitTest.Business.ExportarConsultaHistorico;

public partial class ExportarConsultaHistoricoAnaliticoTests
{
    [Fact(DisplayName = "Deve exportar consulta histórico")]
    [Trait("Business", "Exportar Consulta Historico")]
    public async Task DeveExportarConsultaHistoricoAnalitico()
    {
        // Arrange
        var repositoryMock = _dependencyInjectorFactory.Mocker
            .GetMock<IMongoReadOnlyConnection>();

        var solicitacao = new ArquivoConsultaHistoricoAnaliticaDto(string.Empty, "NE", string.Empty, string.Empty, string.Empty);

        repositoryMock.Setup(x =>
                x.AggregateAsync(
                    It.IsAny<PipelineDefinition<ArquivoConsultaHistoricoAnaliticaDto, ArquivoConsultaHistoricoAnaliticaDto>>(),
                    It.IsAny<CancellationToken>()))
            .ReturnsAsync([solicitacao]);

        var centro = "NE"; 
        ConfigurarMockUserContext(centro);

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        var command = new ExportarConsultaHistoricoAnaliticaCommand();

        // Act

        var response =
            await mediator.EnviarComandoAsync<ExportarConsultaHistoricoAnaliticaCommand, ArquivoResultDto>(
                command, CancellationToken.None);

        // Assert

        Assert.NotNull(response);
        response.ValidationResult.IsValid.ShouldBeTrue(response.ValidationResult.ToString());
        response.ValidationResult.Errors.ShouldBeEmpty();

        repositoryMock.Verify(
            repository =>
                repository.AggregateAsync(
                    It.IsAny<PipelineDefinition<ArquivoConsultaHistoricoAnaliticaDto, ArquivoConsultaHistoricoAnaliticaDto>>(),
                    It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact(DisplayName = "Deve exportar mesmo sem resultados")]
    [Trait("Business", "Exportar Consulta Historico")]
    public async Task DeveExportarMesmoSemResultados()
    {
        // Arrange
        var repositoryMock = _dependencyInjectorFactory.Mocker
            .GetMock<IMongoReadOnlyConnection>();

        repositoryMock.Setup(x =>
            x.AggregateAsync(
                It.IsAny<PipelineDefinition<ArquivoConsultaHistoricoAnaliticaDto, ArquivoConsultaHistoricoAnaliticaDto>>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        ConfigurarMockUserContext("NE");

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();
        var command = new ExportarConsultaHistoricoAnaliticaCommand();

        // Act
        var response = await mediator.EnviarComandoAsync<ExportarConsultaHistoricoAnaliticaCommand, ArquivoResultDto>(
            command, CancellationToken.None);

        // Assert
        Assert.NotNull(response);
        response.ValidationResult.IsValid.ShouldBeTrue(response.ValidationResult.ToString());
        response.ValidationResult.Errors.ShouldBeEmpty();

        repositoryMock.Verify(
            repository =>
                repository.AggregateAsync(
                    It.IsAny<PipelineDefinition<ArquivoConsultaHistoricoAnaliticaDto, ArquivoConsultaHistoricoAnaliticaDto>>(),
                    It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact(DisplayName = "Deve lançar exceção ao falhar agregação")]
    [Trait("Business", "Exportar Consulta Historico")]
    public async Task DeveLancarExcecaoQuandoAggregateFalha()
    {
        // Arrange
        var repositoryMock = _dependencyInjectorFactory.Mocker
            .GetMock<IMongoReadOnlyConnection>();

        repositoryMock.Setup(x =>
            x.AggregateAsync(
                It.IsAny<PipelineDefinition<ArquivoConsultaHistoricoAnaliticaDto, ArquivoConsultaHistoricoAnaliticaDto>>(),
                It.IsAny<CancellationToken>()))
            .ThrowsAsync(new Exception("Erro no Mongo"));

        ConfigurarMockUserContext("NE");

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();
        var command = new ExportarConsultaHistoricoAnaliticaCommand();

        // Act & Assert
        await Assert.ThrowsAsync<RequestFaultException>(async () =>
            await mediator.EnviarComandoAsync<ExportarConsultaHistoricoAnaliticaCommand, ArquivoResultDto>(
                command, CancellationToken.None));
    }
}
