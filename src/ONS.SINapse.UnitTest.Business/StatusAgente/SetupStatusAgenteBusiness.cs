using Moq;
using ONS.SINapse.Business.IBusiness;
using ONS.SINapse.Entities.Entities.DadosCadastrais;
using ONS.SINapse.Repository.IRepository.DadosCadastrais;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Identity;
using ONS.SINapse.UnitTest.Business.Fixtures.Collections;
using ONS.SINapse.UnitTest.Shared;
using ONS.SINapse.UnitTest.Shared.Fixtures;
using System.Linq.Expressions;

namespace ONS.SINapse.UnitTest.Business.StatusAgente;

[Collection(nameof(StatusAgenteBusinessCollection))]
public partial class SetupStatusAgenteBusiness
{
    private readonly MockDependencyInjectorFactory _dependencyInjectorFactory;
    private readonly AgenteFixture _agenteFixture;

    public SetupStatusAgenteBusiness(AgenteFixture agenteFixture)
    {
        _agenteFixture = agenteFixture;
        _dependencyInjectorFactory = new MockDependencyInjectorFactory();
        _dependencyInjectorFactory.RegisterMocks();
    }

    private void ConfigurarMockStatusAgente(
        ICollection<Agente> agentes,
        string[] agentesInativos,
        string[] agentesOnline)
    {
        _dependencyInjectorFactory.Mocker.GetMock<IAgenteRepository>()
            .Setup(repo => repo.GetAsync(
                It.IsAny<Expression<Func<Agente, bool>>>(),
                It.IsAny<CancellationToken>()
            ))
            .ReturnsAsync(agentes);

        _dependencyInjectorFactory.Mocker.GetMock<IAgenteOnlineBusiness>()
            .Setup(s => s.BuscarAgentesInativosAsync(It.IsAny<string[]>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(agentesInativos);

        _dependencyInjectorFactory.Mocker.GetMock<IAgenteOnlineBusiness>()
            .Setup(s => s.ObterCodigoAgentesOnlineAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(agentesOnline);
    }

}
