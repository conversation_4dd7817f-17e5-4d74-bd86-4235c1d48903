using ONS.SINapse.Business.Imp.Business;
using ONS.SINapse.Entities.Entities.DadosCadastrais;

namespace ONS.SINapse.UnitTest.Business.StatusAgente;

public partial class SetupStatusAgenteBusiness
{
    [Fact(DisplayName = "[StatusAgenteBusiness] - Deve retornar status corretos para agentes")]
    [Trait("Business", "StatusAgenteBusiness")]
    public async Task GetStatusAgentes_DeveRetornarStatusCorretos()
    {
        // Arrange
        var agentes = new List<Agente>
        {
            _agenteFixture.GerarAgente("CO", "NO")
        };

        ConfigurarMockStatusAgente(agentes, ["CEMIG"], ["CO"]);

        // Act
        var result = await _dependencyInjectorFactory.Mocker.CreateInstance<StatusAgenteBusiness>()
            .GetStatusAgentes(CancellationToken.None);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);

        var ag1 = result.First(r => r.Codigo == "CO");

        Assert.Equal("CO", ag1.Codigo);
        Assert.Equal("NO", ag1.Nome);
        Assert.Equal("Online", ag1.Status);
    }

    [Fact(DisplayName = "[StatusAgenteBusiness] - Deve retornar vazio quando não houver agentes")]
    [Trait("Business", "StatusAgenteBusiness")]
    public async Task GetStatusAgentes_DeveRetornarVazioSeSemAgentes()
    {
        // Arrange
        var agentes = new List<Agente>();
        ConfigurarMockStatusAgente(agentes, [], []);

        // Act
        var result = await _dependencyInjectorFactory.Mocker.CreateInstance<StatusAgenteBusiness>()
            .GetStatusAgentes(CancellationToken.None);

        // Assert
        Assert.Empty(result);
    }

}
