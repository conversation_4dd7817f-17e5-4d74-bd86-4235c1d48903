using Confluent.Kafka;
using Microsoft.Extensions.Options;
using ONS.SINapse.Shared.Kafka;
using ONS.SINapse.Shared.Kafka.Providers;
using ONS.SINapse.UnitTest.Business.Fixtures.Collections;
using ONS.SINapse.UnitTest.Shared;

namespace ONS.SINapse.UnitTest.Business.ProducerProvider;

[Collection(nameof(ProducerProviderCollection))]
public partial class ProducerProviderTests
{
    private ProducerProvider<TestIntegrationKafka> _producerProvider;

    private readonly ProducerProviderFixture _producerProviderFixture;
    private readonly MockDependencyInjectorFactory _dependencyInjectorFactory;

    public ProducerProviderTests(ProducerProviderFixture producerProviderFixture)
    {
        _producerProviderFixture = producerProviderFixture;
        _dependencyInjectorFactory = new MockDependencyInjectorFactory();
        _dependencyInjectorFactory.RegisterMocks();

        _dependencyInjectorFactory.Mocker.Use(Options.Create(_producerProviderFixture.ObterKafkaSettingsValido()));

        var topico = new TestTopicoIntegrationKafka("test-topic");
        _dependencyInjectorFactory.Mocker.Use<TopicoIntegrationKafka<TestIntegrationKafka>>(topico);

        _producerProvider = _dependencyInjectorFactory.Mocker.CreateInstance<ProducerProvider<TestIntegrationKafka>>();
    }

    private void MockProducerProvider(Mock<IProducer<string, TestIntegrationKafka>> producerMock) 
    {
        var producerField = typeof(ProducerProvider<TestIntegrationKafka>)
                            .GetField("_producer", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

        producerField!.SetValue(_producerProvider, producerMock.Object);
    }
}
