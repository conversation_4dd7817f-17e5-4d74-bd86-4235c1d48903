using Confluent.Kafka;
using Microsoft.Extensions.Options;
using ONS.SINapse.Shared.Kafka;
using ONS.SINapse.Shared.Kafka.Providers;

namespace ONS.SINapse.UnitTest.Business.ProducerProvider;

public partial class ProducerProviderTests
{
    [Fact(DisplayName = "Deve publicar mensagem com sucesso")]
    [Trait("Kafka", "ProducerProvider")]
    public async Task PublishAsync_DevePublicarMensagemComSucesso()
    {
        // Arrange
        var message = new TestIntegrationKafka { Conteudo = "Teste" };
        var headers = new Dictionary<string, string> { { "chave", "valor" } };

        var producerMock = _dependencyInjectorFactory.Mocker.GetMock<IProducer<string, TestIntegrationKafka>>();
        MockProducerProvider(producerMock);

        // Act
        await _producerProvider.PublishAsync(message, headers, CancellationToken.None);

        // Assert
        producerMock.Verify(p => p.ProduceAsync(It.IsAny<string>(), It.IsAny<Message<string, TestIntegrationKafka>>(), CancellationToken.None), Times.Once);
    }

    [Fact(DisplayName = "Não deve publicar mensagem quando não estiver conectado")]
    [Trait("Kafka", "ProducerProvider")]
    public async Task PublishAsync_NaoDevePublicar_QuandoNaoConectado()
    {
        // Arrange
        _dependencyInjectorFactory.Mocker.Use(Options.Create(_producerProviderFixture.ObterKafkaSettingsInativo()));
        _producerProvider = _dependencyInjectorFactory.Mocker.CreateInstance<ProducerProvider<TestIntegrationKafka>>();

        var message = new TestIntegrationKafka { Conteudo = "Teste" };
        var headers = new Dictionary<string, string>();

        var producerMock = _dependencyInjectorFactory.Mocker.GetMock<IProducer<string, TestIntegrationKafka>>();

        // Act
        await _producerProvider.PublishAsync(message, headers, CancellationToken.None);

        // Assert
        producerMock.Verify(p => p.ProduceAsync(It.IsAny<string>(), It.IsAny<Message<string, TestIntegrationKafka>>(), CancellationToken.None), Times.Never);
    }

    [Fact(DisplayName = "Deve atualizar o tópico corretamente")]
    [Trait("Kafka", "ProducerProvider")]
    public void AtualizarTopico_DeveAtualizarTópicoCorretamente()
    {
        // Arrange
        var novoTopico = "novo-topico";

        // Act
        _producerProvider.AtualizarTopico(topico => topico.AlterarValor(novoTopico));

        // Assert
        Assert.Equal(novoTopico, _dependencyInjectorFactory.Mocker.Get<TopicoIntegrationKafka<TestIntegrationKafka>>().Topico);
    }

    [Fact(DisplayName = "Deve retornar verdadeiro quando estiver conectado")]
    [Trait("Kafka", "ProducerProvider")]
    public void EstaConectado_DeveRetornarVerdadeiro_QuandoConectado()
    {
        // Act
        var resultado = _producerProvider.EstaConectado();

        // Assert
        Assert.True(resultado);
    }

    [Fact(DisplayName = "Deve chamar Dispose corretamente")]
    [Trait("Kafka", "ProducerProvider")]
    public void Dispose_DeveChamarDisposeCorretamente()
    {
        // Arrange
        var producerMock = new Mock<IProducer<string, TestIntegrationKafka>>();
        MockProducerProvider(producerMock);

        // Act
        _producerProvider.Dispose();

        // Assert
        producerMock.Verify(p => p.Dispose(), Times.Once);
    }
}

public class TestIntegrationKafka : IIntegrationKafka
{
    public required string Conteudo { get; init; }
}

public class TestTopicoIntegrationKafka : TopicoIntegrationKafka<TestIntegrationKafka>
{
    public TestTopicoIntegrationKafka(string topico) : base(topico) { }
}