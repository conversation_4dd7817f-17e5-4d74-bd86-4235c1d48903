using Microsoft.Extensions.DependencyInjection;
using ONS.SINapse.Repository.IRepository.Firebase;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands.Firebase;
using ONS.SINapse.Solicitacao.Factories;
using ONS.SINapse.UnitTest.Shared.Fixtures;

namespace ONS.SINapse.UnitTest.Business.EnviarSolicitacaoFirebase;

public partial class EnviarSolicitacaoFirebaseTest
{
    [Fact(DisplayName = "[Enviar Solicitacao Firebase] - Deve cadastrar Solicitacao em lote no Firebase")]
    [Trait("Business", "Enviar Solicitacao Firebase")]
    public async Task DeveCadastrarSolicitacaoEmLoteNoFirebase()
    {
        // Arrange
        var solicitacaoCadastrar = _firebaseSolicitacaoFixture.ObterSolicitacaoCadastroValido();
        var factory = new SolicitacaoFirebaseCommandFactory();
        var command = factory.ObterCommand<CriarSolicitacaoNoFirebaseCommand>([solicitacaoCadastrar]);

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        // Act
        var response = await mediator.EnviarComandoAsync(command, CancellationToken.None);

        // Assert
        Assert.NotNull(response);
        response.IsValid.ShouldBeTrue(response.ToString());

        response.Errors.ShouldBeEmpty();

        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoFirebaseRepository>().Verify(
            repository => repository.AddUpdateAsync(It.IsAny<List<Entities.Entities.Solicitacao>>(), It.IsAny<CancellationToken>()),
            Times.Once);

        var criarSolicitacaoNoFirebaseCommandPublished =
            await _dependencyInjectorFactory.Harness.Published.Any<CriarSolicitacaoNoFirebaseCommand>();

        criarSolicitacaoNoFirebaseCommandPublished.ShouldBeTrue();
    }

    [Fact(DisplayName = "[Enviar Solicitacao Firebase] - Deve confirmar Solicitacao no Firebase")]
    [Trait("Business", "Enviar Solicitacao Firebase")]
    public async Task DeveConfirmarSolicitacaoNoFirebase()
    {
        // Arrange
        var solicitacao = _firebaseSolicitacaoFixture.ObterSolicitacaoCadastroValido();
        solicitacao.Confirmar(UsuarioFixture.GerarUsuarioDtoValido());
        var factory = new SolicitacaoFirebaseCommandFactory();
        var command = factory.ObterCommand<TrocarDeStatusNoFirebaseCommand>([solicitacao]);

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        // Act
        var response = await mediator.EnviarComandoAsync(command, CancellationToken.None);

        // Assert
        Assert.NotNull(response);
        response.IsValid.ShouldBeTrue(response.ToString());

        response.Errors.ShouldBeEmpty();

        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoFirebaseRepository>().Verify(
            repository => repository.AddUpdateAsync(It.IsAny<Dictionary<string, object?>>(), It.IsAny<CancellationToken>()),
            Times.Once);

        var confirmarSolicitacaoNoFirebaseCommandPublished =
            await _dependencyInjectorFactory.Harness.Published.Any<TrocarDeStatusNoFirebaseCommand>();

        confirmarSolicitacaoNoFirebaseCommandPublished.ShouldBeTrue();
    }

    [Fact(DisplayName = "[Enviar Solicitacao Firebase] - Deve impedir Solicitacao no Firebase")]
    [Trait("Business", "Enviar Solicitacao Firebase")]
    public async Task DeveImpedirSolicitacaoNoFirebase()
    {
        // Arrange
        var factory = new SolicitacaoFirebaseCommandFactory();
        var solicitacao = _firebaseSolicitacaoFixture.ObterSolicitacaoCadastroValido();
        solicitacao.Impedir("Teste de unidade - Impedir solicitacao pendente com sucesso", UsuarioFixture.GerarUsuarioDtoValido());
        var command = factory.ObterCommand<TrocarDeStatusNoFirebaseCommand>([solicitacao]);

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        // Act
        var response = await mediator.EnviarComandoAsync(command, CancellationToken.None);

        // Assert
        Assert.NotNull(response);
        response.IsValid.ShouldBeTrue(response.ToString());

        response.Errors.ShouldBeEmpty();

        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoFirebaseRepository>().Verify(
            repository => repository.AddUpdateAsync(It.IsAny<Dictionary<string, object?>>(), It.IsAny<CancellationToken>()),
            Times.Once);

        var impedirSolicitacaoNoFirebaseCommandPublished =
            await _dependencyInjectorFactory.Harness.Published.Any<TrocarDeStatusNoFirebaseCommand>();

        impedirSolicitacaoNoFirebaseCommandPublished.ShouldBeTrue();
    }

    [Fact(DisplayName = "[Enviar Solicitacao Firebase] - Deve informar ciencia Solicitacao no Firebase")]
    [Trait("Business", "Enviar Solicitacao Firebase")]
    public async Task DeveInformarCienciaSolicitacaoNoFirebase()
    {
        // Arrange
        var solicitacao = _firebaseSolicitacaoFixture.ObterSolicitacaoCadastroValido();
        var factory = new SolicitacaoFirebaseCommandFactory();
        solicitacao.Impedir("Teste de unidade - Impedir solicitacao pendente com sucesso", UsuarioFixture.GerarUsuarioDtoValido());
        solicitacao.InformarCiencia(UsuarioFixture.GerarUsuarioDtoValido());

        var command = factory.ObterCommand<TrocarDeStatusNoFirebaseCommand>([solicitacao]);

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        // Act
        var response = await mediator.EnviarComandoAsync(command, CancellationToken.None);

        // Assert
        Assert.NotNull(response);
        response.IsValid.ShouldBeTrue(response.ToString());

        response.Errors.ShouldBeEmpty();

        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoFirebaseRepository>().Verify(
            repository => repository.AddUpdateAsync(It.IsAny<Dictionary<string, object?>>(), It.IsAny<CancellationToken>()),
            Times.Once);

        var informarCienciaSolicitacaoNoFirebaseCommandPublished =
            await _dependencyInjectorFactory.Harness.Published.Any<TrocarDeStatusNoFirebaseCommand>();

        informarCienciaSolicitacaoNoFirebaseCommandPublished.ShouldBeTrue();
    }

    [Fact(DisplayName = "[Enviar Solicitacao Firebase] - Deve confirmar leitura Solicitacao no Firebase")]
    [Trait("Business", "Enviar Solicitacao Firebase")]
    public async Task DeveConfirmarLeituraSolicitacaoNoFirebase()
    {
        // Arrange
        var solicitacao = _firebaseSolicitacaoFixture.ObterSolicitacaoLida();
        var command = new SolicitacaoFirebaseCommandFactory().ObterCommand<LeituraDeSolicitacaoNoFirebaseCommand>([solicitacao]);

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        // Act
        var response = await mediator.EnviarComandoAsync(command, CancellationToken.None);

        // Assert
        Assert.NotNull(response);
        response.IsValid.ShouldBeTrue(response.ToString());

        response.Errors.ShouldBeEmpty();

        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoFirebaseRepository>().Verify(
            repository => repository.AddUpdateAsync(It.IsAny<Dictionary<string, object?>>(), It.IsAny<CancellationToken>()),
            Times.Once);

        var leituraDeSolicitacaoNoFirebaseCommandPublished =
            await _dependencyInjectorFactory.Harness.Published.Any<LeituraDeSolicitacaoNoFirebaseCommand>();

        leituraDeSolicitacaoNoFirebaseCommandPublished.ShouldBeTrue();
    }

    [Fact(DisplayName = "[Enviar Solicitacao Firebase] - Deve confirmar entrega Solicitacao no Firebase")]
    [Trait("Business", "Enviar Solicitacao Firebase")]
    public async Task DeveConfirmarEntregaSolicitacaoNoFirebase()
    {
        // Arrange
        var solicitacao = _firebaseSolicitacaoFixture.ObterSolicitacaoEntregue();

        var command = new SolicitacaoFirebaseCommandFactory().ObterCommand<EntregaDeSolicitacaoNoFirebaseCommand>([solicitacao]);

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        // Act
        var response = await mediator.EnviarComandoAsync(command, CancellationToken.None);

        // Assert
        Assert.NotNull(response);
        response.IsValid.ShouldBeTrue(response.ToString());

        response.Errors.ShouldBeEmpty();

        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoFirebaseRepository>().Verify(
            repository => repository.AddUpdateAsync(It.IsAny<Dictionary<string, object?>>(), It.IsAny<CancellationToken>()),
            Times.Once);

        var entregaDeSolicitacaoNoFirebaseCommandPublished =
            await _dependencyInjectorFactory.Harness.Published.Any<EntregaDeSolicitacaoNoFirebaseCommand>();

        entregaDeSolicitacaoNoFirebaseCommandPublished.ShouldBeTrue();
    }

    [Fact(DisplayName = "[Enviar Solicitacao Firebase] - Deve enviar mensagem Solicitacao no Firebase")]
    [Trait("Business", "Enviar Solicitacao Firebase")]
    public async Task DeveEnviarMensagemSolicitacaoNoFirebase()
    {
        // Arrange
        var solicitacao = _firebaseSolicitacaoFixture.ObterChatSolicitacaoValido();

        var command = new CriarMensagemNoChatDeSolicitacaoFirebaseCommand(solicitacao);

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        // Act
        var response = await mediator.EnviarComandoAsync(command, CancellationToken.None);

        // Assert
        Assert.NotNull(response);
        response.IsValid.ShouldBeTrue(response.ToString());

        response.Errors.ShouldBeEmpty();

        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoFirebaseRepository>().Verify(
            repository => repository.PatchFlattenAsync(It.IsAny<ChatDeSolicitacaoFirebaseDto>(), It.IsAny<CancellationToken>()),
            Times.Once);

        var CriarMensagemNoChatDeSolicitacaoFirebaseCommandPublished =
            await _dependencyInjectorFactory.Harness.Published.Any<CriarMensagemNoChatDeSolicitacaoFirebaseCommand>();

        CriarMensagemNoChatDeSolicitacaoFirebaseCommandPublished.ShouldBeTrue();
    }
}
