using Microsoft.Extensions.DependencyInjection;
using ONS.SINapse.Repository.IRepository.Firebase;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;

namespace ONS.SINapse.UnitTest.Business.EnviarSolicitacaoFirebase;

public partial class EnviarSolicitacaoFirebaseTest
{
    [Fact(DisplayName = "[Enviar Solicitacao Firebase] - Deve cadastrar Solicitacao em lote no Firebase")]
    [Trait("Business", "Enviar Solicitacao Firebase")]
    public async Task DeveCadastrarSolicitacaoEmLoteNoFirebase()
    {
        // Arrange
        var solicitacaoCadastrar = _firebaseSolicitacaoFixture.ObterSolicitacaoCadastroValido();
        
        var command = new CadastrarSolicitacaoEmLoteNoFirebaseCommand([solicitacaoCadastrar]);

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        // Act
        var response = await mediator.EnviarComandoAsync<CadastrarSolicitacaoEmLoteNoFirebaseCommand>(command, CancellationToken.None);

        // Assert
        Assert.NotNull(response);
        response.IsValid.ShouldBeTrue(response.ToString());

        response.Errors.ShouldBeEmpty();

        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoFirebaseRepository>().Verify(
            repository => repository.AddUpdateAsync(It.IsAny<List<SolicitacaoFirebaseDto>>(), It.IsAny<CancellationToken>()),
            Times.Once);

        var cadastrarSolicitacaoEmLoteNoFirebaseCommandPublished =
            await _dependencyInjectorFactory.Harness.Published.Any<CadastrarSolicitacaoEmLoteNoFirebaseCommand>();

        cadastrarSolicitacaoEmLoteNoFirebaseCommandPublished.ShouldBeTrue();
    }

    [Fact(DisplayName = "[Enviar Solicitacao Firebase] - Deve confirmar Solicitacao no Firebase")]
    [Trait("Business", "Enviar Solicitacao Firebase")]
    public async Task DeveConfirmarSolicitacaoNoFirebase()
    {
        // Arrange
        var solicitacao = _firebaseSolicitacaoFixture.ObterStatusDeSolicitacaoValido();

        var command = new ConfirmarSolicitacaoNoFirebaseCommand(solicitacao);

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        // Act
        var response = await mediator.EnviarComandoAsync<ConfirmarSolicitacaoNoFirebaseCommand>(command, CancellationToken.None);

        // Assert
        Assert.NotNull(response);
        response.IsValid.ShouldBeTrue(response.ToString());

        response.Errors.ShouldBeEmpty();

        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoFirebaseRepository>().Verify(
            repository => repository.PatchFlattenAsync(It.IsAny<StatusDeSolicitacaoFirebaseDto>(), It.IsAny<CancellationToken>()),
            Times.Once);

        var confirmarSolicitacaoNoFirebaseCommandPublished =
            await _dependencyInjectorFactory.Harness.Published.Any<ConfirmarSolicitacaoNoFirebaseCommand>();

        confirmarSolicitacaoNoFirebaseCommandPublished.ShouldBeTrue();
    }

    [Fact(DisplayName = "[Enviar Solicitacao Firebase] - Deve impedir Solicitacao no Firebase")]
    [Trait("Business", "Enviar Solicitacao Firebase")]
    public async Task DeveImpedirSolicitacaoNoFirebase()
    {
        // Arrange
        var solicitacao = _firebaseSolicitacaoFixture.ObterImpedirSolicitacaoValido();

        var command = new ImpedirSolicitacaoNoFirebaseCommand(solicitacao);

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        // Act
        var response = await mediator.EnviarComandoAsync<ImpedirSolicitacaoNoFirebaseCommand>(command, CancellationToken.None);

        // Assert
        Assert.NotNull(response);
        response.IsValid.ShouldBeTrue(response.ToString());

        response.Errors.ShouldBeEmpty();

        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoFirebaseRepository>().Verify(
            repository => repository.PatchFlattenAsync(It.IsAny<ImpedirSolicitacaoFirebaseDto>(), It.IsAny<CancellationToken>()),
            Times.Once);

        var impedirSolicitacaoNoFirebaseCommandPublished =
            await _dependencyInjectorFactory.Harness.Published.Any<ImpedirSolicitacaoNoFirebaseCommand>();

        impedirSolicitacaoNoFirebaseCommandPublished.ShouldBeTrue();
    }

    [Fact(DisplayName = "[Enviar Solicitacao Firebase] - Deve informar ciencia Solicitacao no Firebase")]
    [Trait("Business", "Enviar Solicitacao Firebase")]
    public async Task DeveInformarCienciaSolicitacaoNoFirebase()
    {
        // Arrange
        var solicitacao = _firebaseSolicitacaoFixture.ObterStatusDeSolicitacaoValido();

        var command = new InformarCienciaSolicitacaoNoFirebaseCommand(solicitacao);

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        // Act
        var response = await mediator.EnviarComandoAsync<InformarCienciaSolicitacaoNoFirebaseCommand>(command, CancellationToken.None);

        // Assert
        Assert.NotNull(response);
        response.IsValid.ShouldBeTrue(response.ToString());

        response.Errors.ShouldBeEmpty();

        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoFirebaseRepository>().Verify(
            repository => repository.PatchFlattenAsync(It.IsAny<StatusDeSolicitacaoFirebaseDto>(), It.IsAny<CancellationToken>()),
            Times.Once);

        var informarCienciaSolicitacaoNoFirebaseCommandPublished =
            await _dependencyInjectorFactory.Harness.Published.Any<InformarCienciaSolicitacaoNoFirebaseCommand>();

        informarCienciaSolicitacaoNoFirebaseCommandPublished.ShouldBeTrue();
    }

    [Fact(DisplayName = "[Enviar Solicitacao Firebase] - Deve confirmar leitura Solicitacao no Firebase")]
    [Trait("Business", "Enviar Solicitacao Firebase")]
    public async Task DeveConfirmarLeituraSolicitacaoNoFirebase()
    {
        // Arrange
        var solicitacao = _firebaseSolicitacaoFixture.ObterChatSolicitacaoValido();

        var command = new ConfirmarLeituraSolicitacaoNoFirebaseCommand(solicitacao);

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        // Act
        var response = await mediator.EnviarComandoAsync<ConfirmarLeituraSolicitacaoNoFirebaseCommand>(command, CancellationToken.None);

        // Assert
        Assert.NotNull(response);
        response.IsValid.ShouldBeTrue(response.ToString());

        response.Errors.ShouldBeEmpty();

        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoFirebaseRepository>().Verify(
            repository => repository.PatchFlattenAsync(It.IsAny<ChatDeSolicitacaoFirebaseDto>(), It.IsAny<CancellationToken>()),
            Times.Once);

        var confirmarLeituraSolicitacaoNoFirebaseCommandPublished =
            await _dependencyInjectorFactory.Harness.Published.Any<ConfirmarLeituraSolicitacaoNoFirebaseCommand>();

        confirmarLeituraSolicitacaoNoFirebaseCommandPublished.ShouldBeTrue();
    }

    [Fact(DisplayName = "[Enviar Solicitacao Firebase] - Deve confirmar entrega Solicitacao no Firebase")]
    [Trait("Business", "Enviar Solicitacao Firebase")]
    public async Task DeveConfirmarEntregaSolicitacaoNoFirebase()
    {
        // Arrange
        var solicitacao = _firebaseSolicitacaoFixture.ObterChatSolicitacaoValido();

        var command = new ConfirmarEntregasSolicitacaoNoFirebaseCommand([solicitacao]);

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        // Act
        var response = await mediator.EnviarComandoAsync<ConfirmarEntregasSolicitacaoNoFirebaseCommand>(command, CancellationToken.None);

        // Assert
        Assert.NotNull(response);
        response.IsValid.ShouldBeTrue(response.ToString());

        response.Errors.ShouldBeEmpty();

        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoFirebaseRepository>().Verify(
            repository => repository.PatchFlattenAsync(It.IsAny<List<ChatDeSolicitacaoFirebaseDto>>(), It.IsAny<CancellationToken>()),
            Times.Once);

        var confirmarEntregasSolicitacaoNoFirebaseCommandPublished =
            await _dependencyInjectorFactory.Harness.Published.Any<ConfirmarEntregasSolicitacaoNoFirebaseCommand>();

        confirmarEntregasSolicitacaoNoFirebaseCommandPublished.ShouldBeTrue();
    }

    [Fact(DisplayName = "[Enviar Solicitacao Firebase] - Deve enviar mensagem Solicitacao no Firebase")]
    [Trait("Business", "Enviar Solicitacao Firebase")]
    public async Task DeveEnviarMensagemSolicitacaoNoFirebase()
    {
        // Arrange
        var solicitacao = _firebaseSolicitacaoFixture.ObterChatSolicitacaoValido();

        var command = new EnviarMensagemChatSolicitacaoNoFirebaseCommand(solicitacao);

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        // Act
        var response = await mediator.EnviarComandoAsync<EnviarMensagemChatSolicitacaoNoFirebaseCommand>(command, CancellationToken.None);

        // Assert
        Assert.NotNull(response);
        response.IsValid.ShouldBeTrue(response.ToString());

        response.Errors.ShouldBeEmpty();

        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoFirebaseRepository>().Verify(
            repository => repository.PatchFlattenAsync(It.IsAny<ChatDeSolicitacaoFirebaseDto>(), It.IsAny<CancellationToken>()),
            Times.Once);

        var enviarMensagemChatSolicitacaoNoFirebaseCommandPublished =
            await _dependencyInjectorFactory.Harness.Published.Any<EnviarMensagemChatSolicitacaoNoFirebaseCommand>();

        enviarMensagemChatSolicitacaoNoFirebaseCommandPublished.ShouldBeTrue();
    }
}
