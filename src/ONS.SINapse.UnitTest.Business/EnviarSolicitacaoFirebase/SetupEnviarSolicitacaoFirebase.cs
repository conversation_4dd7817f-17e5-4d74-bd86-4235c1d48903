using ONS.SINapse.UnitTest.Business.Fixtures.Collections;
using ONS.SINapse.UnitTest.Business.Fixtures.Solicitacao.FirebaseSolicitacao;
using ONS.SINapse.UnitTest.Shared;

namespace ONS.SINapse.UnitTest.Business.EnviarSolicitacaoFirebase;

[Collection(nameof(EnviarSolicitacaoFirebaseCollection))]
public partial class EnviarSolicitacaoFirebaseTest
{
    private readonly MockDependencyInjectorFactory _dependencyInjectorFactory;
    private readonly FirebaseSolicitacaoFixture _firebaseSolicitacaoFixture;

    public EnviarSolicitacaoFirebaseTest(
        FirebaseSolicitacaoFixture firebaseSolicitacaoFixture)
    {
        _dependencyInjectorFactory = new MockDependencyInjectorFactory();
        _dependencyInjectorFactory.RegisterMocks();
        _firebaseSolicitacaoFixture = firebaseSolicitacaoFixture;
    }
}
