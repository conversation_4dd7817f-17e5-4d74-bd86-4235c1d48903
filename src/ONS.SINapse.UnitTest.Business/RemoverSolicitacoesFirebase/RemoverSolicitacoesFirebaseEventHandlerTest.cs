using System.Collections.ObjectModel;
using MassTransit;
using Microsoft.Extensions.DependencyInjection;
using ONS.SINapse.Repository.IRepository.Firebase;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Solicitacao.EventHandlers.Events;
using ONS.SINapse.UnitTest.Business.Fixtures.Collections;
using ONS.SINapse.UnitTest.Business.Fixtures.Solicitacao;
using ONS.SINapse.UnitTest.Shared;
using ONS.SINapse.UnitTest.Shared.Fixtures;

namespace ONS.SINapse.UnitTest.Business.RemoverSolicitacoesFirebase;

[Collection(nameof(RemoverSolicitacoesFirebaseEventHandlerCollection))]
public class RemoverSolicitacoesFirebaseEventHandlerTest
{
    private readonly SolicitacaoFixture _solicitacaoFixture;
    private readonly MockDependencyInjectorFactory _dependencyInjectorFactory;

    public RemoverSolicitacoesFirebaseEventHandlerTest(SolicitacaoFixture solicitacaoFixture)
    {
        _solicitacaoFixture = solicitacaoFixture;
        _dependencyInjectorFactory = new MockDependencyInjectorFactory();
        _dependencyInjectorFactory.RegisterMocks();
    }
        

    [Fact(DisplayName = "[Finalizar Solicitacao Firebase] - Deve retornar erro ao remover solicitações no firebase")]
    [Trait("Business", "Finalizar solicitação firebase")]
    public async Task Publicar_SolicitacaoFinalizadaEmLoteEvent_DeveRetornarErro()
    {
        // Arrange
        
        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();
        var mockRepository = _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoFirebaseRepository>();

        mockRepository.Setup(r =>
                r.RemoverSolicitacoesAsync(It.IsAny<ReadOnlyCollection<string>>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new Exception("Teste de unidade remover solicitação firebase"));

        var solicitacao = _solicitacaoFixture.GerarSolicitacaoConfirmada();
        
        var evento = 
            SolicitacaoEventsFixture.CriarSolicitacaoFinalizadaEmLoteEvent([solicitacao]);
    
        // Act
        
        await mediator.PublicarEventoAsync(evento, CancellationToken.None);
        
        // Assert
        
        var eventoPublicado = 
            _dependencyInjectorFactory.Harness.Published.Any<SolicitacaoFinalizadaEmLoteEvent>();
        
        var eventoConsumido = 
            _dependencyInjectorFactory.Harness.Consumed.Any<SolicitacaoFinalizadaEmLoteEvent>();
        
        var falhaDisparada =
            _dependencyInjectorFactory.Harness.Published.Any<Fault<SolicitacaoFinalizadaEmLoteEvent>>();
        
        await Task.WhenAll(eventoPublicado, eventoConsumido, falhaDisparada);
        
        eventoPublicado.Result.ShouldBeTrue();
        eventoConsumido.Result.ShouldBeTrue();
        falhaDisparada.Result.ShouldBeTrue();
        
        mockRepository.Verify(r => r.RemoverSolicitacoesAsync(
            It.Is<IReadOnlyCollection<string>>(ids => ids.Count == 1 && ids.Contains(solicitacao.Id)),
            It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact(DisplayName = "[Finalizar Solicitacao Firebase] - Deve retornar remover solicitações no firebase")]
    [Trait("Business", "Finalizar solicitação firebase")]
    public async Task Publicar_SolicitacaoFinalizadaEmLoteEvent_DeveRemoverDoFirebase()
    {
        // Arrange
        
        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();
        var mockRepository = _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoFirebaseRepository>();

        var solicitacao = _solicitacaoFixture.GerarSolicitacaoConfirmada();
        
        var evento = 
            SolicitacaoEventsFixture.CriarSolicitacaoFinalizadaEmLoteEvent([solicitacao]);
    
        // Act
        
        await mediator.PublicarEventoAsync(evento, CancellationToken.None);
        
        // Assert
        
        var eventoPublicado = 
            _dependencyInjectorFactory.Harness.Published.Any<SolicitacaoFinalizadaEmLoteEvent>();
        
        var eventoConsumido = 
            _dependencyInjectorFactory.Harness.Consumed.Any<SolicitacaoFinalizadaEmLoteEvent>();
        
        await Task.WhenAll(eventoPublicado, eventoConsumido);
        
        eventoPublicado.Result.ShouldBeTrue();
        eventoConsumido.Result.ShouldBeTrue();
        
        mockRepository.Verify(r => r.RemoverSolicitacoesAsync(
            It.Is<IReadOnlyCollection<string>>(ids => ids.Count == 1 && ids.Contains(solicitacao.Id)),
            It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact(DisplayName = "[Finalizar Solicitacao Firebase] - Deve lidar com lista vazia de solicitações")]
    [Trait("Business", "Finalizar solicitação firebase")]
    public async Task Publicar_SolicitacaoFinalizadaEmLoteEvent_DeveProcessarListaVazia()
    {
        // Arrange
        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();
        var mockRepository = _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoFirebaseRepository>();

        var evento = SolicitacaoEventsFixture.CriarSolicitacaoFinalizadaEmLoteEvent([]);

        // Act
        await mediator.PublicarEventoAsync(evento, CancellationToken.None);

        // Assert
        var eventoPublicado = 
            _dependencyInjectorFactory.Harness.Published.Any<SolicitacaoFinalizadaEmLoteEvent>();
        
        var eventoConsumido = 
            _dependencyInjectorFactory.Harness.Consumed.Any<SolicitacaoFinalizadaEmLoteEvent>();
        
        await Task.WhenAll(eventoPublicado, eventoConsumido);
        
        eventoPublicado.Result.ShouldBeTrue();
        eventoConsumido.Result.ShouldBeTrue();
        
        mockRepository.Verify(r => r.RemoverSolicitacoesAsync(
            It.Is<IReadOnlyCollection<string>>(ids => ids.Count == 0),
            It.IsAny<CancellationToken>()), Times.Once);
    }
    
    [Fact(DisplayName = "[Cancelar Solicitacoes Firebase] - Deve remover solicitações canceladas do firebase")]
    [Trait("Business", "Cancelar solicitações firebase")]
    public async Task Publicar_SolicitacoesCanceladasEmLoteEvent_DeveRemoverDoFirebase()
    {
        // Arrange
        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();
        var mockRepository = _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoFirebaseRepository>();

        var solicitacoesCanceladas = _solicitacaoFixture.GerarSolicitacaoCancelada();
    
        var evento = 
            SolicitacaoEventsFixture.CriarSolicitacaoCanceladaEvent([solicitacoesCanceladas]);

        // Act
        await mediator.PublicarEventoAsync(evento, CancellationToken.None);
    
        // Assert
        var eventoPublicado = 
            _dependencyInjectorFactory.Harness.Published.Any<SolicitacoesCanceladasEmLoteEvent>();
    
        var eventoConsumido = 
            _dependencyInjectorFactory.Harness.Consumed.Any<SolicitacoesCanceladasEmLoteEvent>();
    
        await Task.WhenAll(eventoPublicado, eventoConsumido);
    
        eventoPublicado.Result.ShouldBeTrue();
        eventoConsumido.Result.ShouldBeTrue();

        mockRepository.Verify(r => r.RemoverSolicitacoesAsync(
            It.Is<IReadOnlyCollection<string>>(ids =>
                ids.Count == 1 &&
                ids.Contains(solicitacoesCanceladas.Id)),
            It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact(DisplayName = "[Cancelar Solicitacoes Firebase] - Deve lidar com lista vazia de solicitações canceladas")]
    [Trait("Business", "Cancelar solicitações firebase")]
    public async Task Publicar_SolicitacoesCanceladasEmLoteEvent_DeveProcessarListaVazia()
    {
        // Arrange
        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();
        var mockRepository = _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoFirebaseRepository>();

        var evento = SolicitacaoEventsFixture.CriarSolicitacaoCanceladaEvent([]);

        // Act
        await mediator.PublicarEventoAsync(evento, CancellationToken.None);

        // Assert
        var eventoPublicado = 
            _dependencyInjectorFactory.Harness.Published.Any<SolicitacoesCanceladasEmLoteEvent>();
        
        var eventoConsumido = 
            _dependencyInjectorFactory.Harness.Consumed.Any<SolicitacoesCanceladasEmLoteEvent>();
        
        await Task.WhenAll(eventoPublicado, eventoConsumido);
        
        eventoPublicado.Result.ShouldBeTrue();
        eventoConsumido.Result.ShouldBeTrue();
        
        mockRepository.Verify(r => r.RemoverSolicitacoesAsync(
            It.Is<IReadOnlyCollection<string>>(ids => ids.Count == 0),
            It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact(DisplayName = "[Cancelar Solicitacoes Firebase] - Deve lidar com exceção ao remover solicitações canceladas")]
    [Trait("Business", "Cancelar solicitações firebase")]
    public async Task Publicar_SolicitacoesCanceladasEmLoteEvent_DeveLidarComExcecao()
    {
        // Arrange
        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();
        var mockRepository = _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoFirebaseRepository>();

        mockRepository.Setup(r =>
                r.RemoverSolicitacoesAsync(It.IsAny<List<string>>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new Exception("Teste de unidade remover solicitação firebase"));

        var solicitacoesCanceladas = new List<Entities.Entities.Solicitacao>
        {
            _solicitacaoFixture.GerarSolicitacaoCancelada(),
            _solicitacaoFixture.GerarSolicitacaoCancelada()
        };

        var evento = 
            SolicitacaoEventsFixture.CriarSolicitacaoCanceladaEvent(solicitacoesCanceladas);

        // Act
        await mediator.PublicarEventoAsync(evento, CancellationToken.None);

        // Assert
        var eventoPublicado = 
            _dependencyInjectorFactory.Harness.Published.Any<SolicitacoesCanceladasEmLoteEvent>();

        var eventoConsumido = 
            _dependencyInjectorFactory.Harness.Consumed.Any<SolicitacoesCanceladasEmLoteEvent>();

        var falhaDisparada =
            _dependencyInjectorFactory.Harness.Published.Any<Fault<SolicitacoesCanceladasEmLoteEvent>>();

        await Task.WhenAll(eventoPublicado, eventoConsumido, falhaDisparada);

        eventoPublicado.Result.ShouldBeTrue();
        eventoConsumido.Result.ShouldBeTrue();
        falhaDisparada.Result.ShouldBeTrue();

        mockRepository.Verify(r => r.RemoverSolicitacoesAsync(
            It.Is<IReadOnlyCollection<string>>(ids => 
                ids.Count == 2 && 
                ids.Contains(solicitacoesCanceladas[0].Id) && 
                ids.Contains(solicitacoesCanceladas[1].Id)),
            It.IsAny<CancellationToken>()), Times.Once);
    }
}