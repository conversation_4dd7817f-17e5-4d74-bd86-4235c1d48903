using System.Collections;
using Microsoft.Extensions.DependencyInjection;
using ONS.SINapse.Integracao.Shared.Dtos;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Integracao.Shared.Helpers;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using ONS.SINapse.Solicitacao.Dtos.Integracao;
using ONS.SINapse.Solicitacao.EventHandlers.IntegrationEvents;
using ONS.SINapse.UnitTest.Business.Fixtures.Solicitacao;
using ONS.SINapse.UnitTest.Shared;
using ONS.SINapse.UnitTest.Shared.Fixtures;

namespace ONS.SINapse.UnitTest.Business.CadastroSolicitacao;

public class CadastroDeSolicitacaoExternaRecebidaIntegrationEventHandlerTest
{

    private readonly MockDependencyInjectorFactory _dependencyInjectorFactory;

    public CadastroDeSolicitacaoExternaRecebidaIntegrationEventHandlerTest()
    {
        _dependencyInjectorFactory = new MockDependencyInjectorFactory();
        _dependencyInjectorFactory.RegisterMocks();
    }
    
    [Theory(DisplayName = "[Cadastro de Solicitação Externa] - Deve processar corretamente uma solicitação válida com todos os campos preenchidos")]
    [Trait("Business", "Cadastro de Solicitação Externa")]
    [InlineData(true)]
    [InlineData(false)]
    public async Task CadastroDeSolicitacaoExterna_CommandValido_ComSucesso(bool requerAprovacaoEnvio)
    {
        // Arrange
        var evento = SolicitacaoEventsFixture
            .CriarCadastroDeSolicitacaoExternaRecebidaIntegrationEvent(requerAprovacaoEnvio: requerAprovacaoEnvio);

        var usuarioDto = UsuarioFixture.GerarUsuarioDtoValido();

        _dependencyInjectorFactory.Mocker
            .GetMock<IUsuarioJwtHelper>()
            .Setup(x => x.GetUsuario(It.IsAny<string>()))
            .Returns(new UsuarioJwtDto(usuarioDto.Sid, usuarioDto.Login, usuarioDto.Nome));

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        // Act
        await mediator.PublicarEventoAsync(evento, CancellationToken.None);

        // Assert
        var eventoPublicado = _dependencyInjectorFactory.Harness.Published.Any<CadastroDeSolicitacaoExternaRecebidaIntegrationEvent>();
        var eventoConsumido = _dependencyInjectorFactory.Harness.Consumed.Any<CadastroDeSolicitacaoExternaRecebidaIntegrationEvent>();
        var commandPublicado = _dependencyInjectorFactory.Harness.Published.Any<CadastrarSolicitacaoEmLoteCommand>();
        
        await Task.WhenAll(eventoPublicado, eventoConsumido, commandPublicado);

        eventoPublicado.Result.ShouldBeTrue();
        eventoConsumido.Result.ShouldBeTrue();
        commandPublicado.Result.ShouldBeTrue();
    }
    
    [Theory(DisplayName = "[Cadastro de Solicitação Externa] - Deve retornar erro quando o resultado do CadastrarSolicitacaoEmLoteCommand é inválido")]
    [Trait("Business", "Cadastro de Solicitação Externa")]
    [InlineData(true)]
    [InlineData(false)]
    public async Task CadastroDeSolicitacaoExterna_CommandInvalido_DeveRetornarErro(bool requerAprovacaoEnvio)
    {
        // Arrange
        var evento = SolicitacaoEventsFixture
            .CriarCadastroDeSolicitacaoExternaRecebidaIntegrationEvent(requerAprovacaoEnvio: requerAprovacaoEnvio);

        var usuarioDto = UsuarioFixture.GerarUsuarioDtoValido();

        _dependencyInjectorFactory.Mocker
            .GetMock<IUsuarioJwtHelper>()
            .Setup(x => x.GetUsuario(It.IsAny<string>()))
            .Returns(new UsuarioJwtDto(usuarioDto.Sid, usuarioDto.Login, usuarioDto.Nome));

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        var resultError = new StatusDeSolicitacaoIntegracaoEnvioEmLoteDto([
            new StatusDeSolicitacaoIntegracaoEnvioDto(
                "id",
                StatusDeSolicitacao.Erro,
                new UsuarioDto(),
                DateTime.Now, 
                null,
                "Externo",
                ["Erros para teste unitário"]
            )
        ]);

        _dependencyInjectorFactory.Mocker
            .GetMock<IMediatorHandler>()
            .Setup(x => x.EnviarComandoAsync<CadastrarSolicitacaoEmLoteCommand, StatusDeSolicitacaoIntegracaoEnvioEmLoteDto>(
                It.IsAny<CadastrarSolicitacaoEmLoteCommand>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(resultError);

        // Act
        await mediator.PublicarEventoAsync(evento, CancellationToken.None);

        // Assert
        var eventoPublicado = _dependencyInjectorFactory.Harness.Published.Any<CadastroDeSolicitacaoExternaRecebidaIntegrationEvent>();
        var eventoConsumido = _dependencyInjectorFactory.Harness.Consumed.Any<CadastroDeSolicitacaoExternaRecebidaIntegrationEvent>();
        
        await Task.WhenAll(eventoPublicado, eventoConsumido);

        eventoPublicado.Result.ShouldBeTrue();
        eventoConsumido.Result.ShouldBeTrue();
    }
    
    
    #region Validação de campos inválidos


    [Theory(DisplayName = "[Cadastro de Solicitação Externa] - Deve retornar erro quando há campos ausente")]
    [Trait("Business", "Cadastro de Solicitação Externa")]
    [ClassData(typeof(ValidarCamposCadastroDeSolicitacaoExterna))]
    public async Task CadastroDeSolicitacaoExterna_CamposInvalidos_DeveRetornarErro(ValidarCamposCadastroDeSolicitacaoExternaAction validarCampo)
    {
        // Arrange
        
        var evento =
            SolicitacaoEventsFixture.CriarCadastroDeSolicitacaoExternaRecebidaIntegrationEvent();

        validarCampo.Acao(evento);
    
        var usuarioDto = UsuarioFixture.GerarUsuarioDtoValido();
    
        _dependencyInjectorFactory.Mocker
            .GetMock<IUsuarioJwtHelper>()
            .Setup(x => x.GetUsuario(It.IsAny<string>()))
            .Returns(new UsuarioJwtDto(usuarioDto.Sid, usuarioDto.Login, usuarioDto.Nome));
    
        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>(); 
        
        // Act && Assert
    
        await mediator.PublicarEventoAsync(evento, CancellationToken.None);
        
        var eventoPublicado = _dependencyInjectorFactory.Harness.Published.Any<CadastroDeSolicitacaoExternaRecebidaIntegrationEvent>();
        var eventoConsumido = _dependencyInjectorFactory.Harness.Consumed.Any<CadastroDeSolicitacaoExternaRecebidaIntegrationEvent>();
        
        await Task.WhenAll(eventoPublicado, eventoConsumido);
    
        eventoPublicado.Result.ShouldBeTrue();
        eventoConsumido.Result.ShouldBeTrue();
    }

    public class ValidarCamposCadastroDeSolicitacaoExterna : IEnumerable<object[]>
    {
        public IEnumerator<object[]> GetEnumerator()
        {
            yield return [new ValidarCamposCadastroDeSolicitacaoExternaAction("Origem", x => x.Solicitacoes[0].Origem = null)];
            yield return [new ValidarCamposCadastroDeSolicitacaoExternaAction("Destino", x => x.Solicitacoes[0].Destino = null)];
            yield return [new ValidarCamposCadastroDeSolicitacaoExternaAction("Mensagem", x => x.Solicitacoes[0].Mensagem = null)];
            yield return [new ValidarCamposCadastroDeSolicitacaoExternaAction("Codigo Externo", x => x.Solicitacoes[0].Id = null)];
            yield return [new ValidarCamposCadastroDeSolicitacaoExternaAction("Sistema de Origem", x => x.Solicitacoes[0].SistemaDeOrigem = null)];
            yield return [new ValidarCamposCadastroDeSolicitacaoExternaAction("Usuario", x => x.Solicitacoes[0].Usuario = null)];
        }

        IEnumerator IEnumerable.GetEnumerator() => GetEnumerator();
    }

    public class ValidarCamposCadastroDeSolicitacaoExternaAction
    {
        private string Nome { get; }
        public Action<CadastroDeSolicitacaoExternaRecebidaIntegrationEvent> Acao { get; }

        public ValidarCamposCadastroDeSolicitacaoExternaAction(string nome, Action<CadastroDeSolicitacaoExternaRecebidaIntegrationEvent> acao)
        {
            Nome = nome;
            Acao = acao;
        }

        public override string ToString() => Nome;
    }
    
    #endregion
}