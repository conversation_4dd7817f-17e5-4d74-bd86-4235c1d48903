using System.Collections;
using Confluent.Kafka;
using MassTransit.Testing;
using Microsoft.Extensions.DependencyInjection;
using ONS.SINapse.Integracao.Shared.Dtos;
using ONS.SINapse.Integracao.Shared.Helpers;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using ONS.SINapse.Solicitacao.EventHandlers.Events;
using ONS.SINapse.Solicitacao.EventHandlers.IntegrationEvents;
using ONS.SINapse.UnitTest.Business.Fixtures.Solicitacao;
using ONS.SINapse.UnitTest.Shared;
using ONS.SINapse.UnitTest.Shared.Fixtures;

namespace ONS.SINapse.UnitTest.Business.CadastroSolicitacao;

public class CadastroDeSolicitacaoExternaRecebidaIntegrationEventHandlerTest
{

    private readonly MockDependencyInjectorFactory _dependencyInjectorFactory;

    public CadastroDeSolicitacaoExternaRecebidaIntegrationEventHandlerTest()
    {
        _dependencyInjectorFactory = new MockDependencyInjectorFactory();
        _dependencyInjectorFactory.RegisterMocks();
    }
    
    [Fact(DisplayName = "[Cadastro de Solicitação Externa] - Deve processar corretamente uma solicitação válida com todos os campos preenchidos")]
    [Trait("Business", "Cadastro de Solicitação Externa")]
    public async Task CadastroDeSolicitacaoExterna_CommandValido_ComSucesso()
    {
        // Arrange
        var evento = SolicitacaoEventsFixture.CriarCadastroDeSolicitacaoExternaRecebidaIntegrationEvent();
        evento.DefinirHeaders([new Header("AuthorizationUser", "dummyToken"u8.ToArray())]);

        var usuarioDto = UsuarioFixture.GerarUsuarioDtoValido();

        _dependencyInjectorFactory.Mocker
            .GetMock<IUsuarioJwtHelper>()
            .Setup(x => x.GetUsuario(It.IsAny<string>()))
            .Returns(new UsuarioJwtDto(usuarioDto.Sid, usuarioDto.Login, usuarioDto.Nome));

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        // Act
        await mediator.PublicarEventoAsync(evento, CancellationToken.None);

        // Assert
        var eventoPublicado = _dependencyInjectorFactory.Harness.Published.Any<CadastroDeSolicitacaoExternaRecebidaIntegrationEvent>();
        var eventoConsumido = _dependencyInjectorFactory.Harness.Consumed.Any<CadastroDeSolicitacaoExternaRecebidaIntegrationEvent>();
        var commandPublicado = _dependencyInjectorFactory.Harness.Published.Any<CadastrarSolicitacaoEmLoteCommand>();

        var fault = await _dependencyInjectorFactory.Harness.Published
            .SelectAsync<ErroCadastroDeSolicitacaoExternaEvent>()
            .FirstOrDefault();

        fault.ShouldBeNull();

        await Task.WhenAll(eventoPublicado, eventoConsumido, commandPublicado);

        eventoPublicado.Result.ShouldBeTrue();
        eventoConsumido.Result.ShouldBeTrue();
        commandPublicado.Result.ShouldBeTrue();
    }
    
    [Fact(DisplayName = "[Cadastro de Solicitação Externa] - Deve retornar erro quando o resultado do CadastrarSolicitacaoEmLoteCommand é inválido")]
    [Trait("Business", "Cadastro de Solicitação Externa")]
    public async Task CadastroDeSolicitacaoExterna_CommandInvalido_DeveRetornarErro()
    {
        // Arrange
        var evento = SolicitacaoEventsFixture.CriarCadastroDeSolicitacaoExternaRecebidaIntegrationEvent();
        evento.DefinirHeaders([new Header("AuthorizationUser", "dummyToken"u8.ToArray())]);

        var usuarioDto = UsuarioFixture.GerarUsuarioDtoValido();

        _dependencyInjectorFactory.Mocker
            .GetMock<IUsuarioJwtHelper>()
            .Setup(x => x.GetUsuario(It.IsAny<string>()))
            .Returns(new UsuarioJwtDto(usuarioDto.Sid, usuarioDto.Login, usuarioDto.Nome));

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        var resultError = new ResultadoCadastroSolicitacaoEmLoteDto();
        resultError.ValidationResult.Errors.Add(new FluentValidation.Results.ValidationFailure("Field", "Erro de validação"));
        
        _dependencyInjectorFactory.Mocker
            .GetMock<IMediatorHandler>()
            .Setup(x => x.EnviarComandoAsync<CadastrarSolicitacaoEmLoteCommand, ResultadoCadastroSolicitacaoEmLoteDto>(
                It.IsAny<CadastrarSolicitacaoEmLoteCommand>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(resultError);

        // Act
        await mediator.PublicarEventoAsync(evento, CancellationToken.None);

        // Assert
        var eventoPublicado = _dependencyInjectorFactory.Harness.Published.Any<CadastroDeSolicitacaoExternaRecebidaIntegrationEvent>();
        var eventoConsumido = _dependencyInjectorFactory.Harness.Consumed.Any<CadastroDeSolicitacaoExternaRecebidaIntegrationEvent>();

        var fault = await _dependencyInjectorFactory.Harness.Published
            .SelectAsync<ErroCadastroDeSolicitacaoExternaEvent>()
            .FirstOrDefault();

        fault.ShouldNotBeNull();
        var erros = fault.Context.Message.Erros;
        erros.ShouldNotBeEmpty();
        erros.Any(x => x.Contains("Erro de validação")).ShouldBeTrue();

        await Task.WhenAll(eventoPublicado, eventoConsumido);

        eventoPublicado.Result.ShouldBeTrue();
        eventoConsumido.Result.ShouldBeTrue();
    }
    
    
    #region Validação de campos inválidos

    [Fact(DisplayName = "[Cadastro de Solicitação Externa] - Deve retornar erro quando o header 'AuthorizationUser' está ausente")]
    [Trait("Business", "Cadastro de Solicitação Externa")]
    public async Task CadastroDeSolicitacaoExterna_SemAuthorizationUserHeader_DeveRetornarErro()
    {
        // Arrange
        var evento =
            SolicitacaoEventsFixture.CriarCadastroDeSolicitacaoExternaRecebidaIntegrationEvent();

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();
    
        // Act
        await mediator.PublicarEventoAsync(evento, CancellationToken.None);
    
        // Assert
        var eventoPublicado = _dependencyInjectorFactory.Harness.Published.Any<CadastroDeSolicitacaoExternaRecebidaIntegrationEvent>();
        var eventoConsumido = _dependencyInjectorFactory.Harness.Consumed.Any<CadastroDeSolicitacaoExternaRecebidaIntegrationEvent>();

        var fault = await _dependencyInjectorFactory.Harness.Published
            .SelectAsync<ErroCadastroDeSolicitacaoExternaEvent>()
            .FirstOrDefault();

        fault.ShouldNotBeNull();
        var erros = fault.Context.Message.Erros;
        erros.ShouldNotBeEmpty();
        erros.Any(x => x.Contains("Não foi possível definir o usuário")).ShouldBeTrue();
        
        await Task.WhenAll(eventoPublicado, eventoConsumido);
    
        eventoPublicado.Result.ShouldBeTrue();
        eventoConsumido.Result.ShouldBeTrue();
    }

    [Theory(DisplayName = "[Cadastro de Solicitação Externa] - Deve retornar erro quando há campos ausente")]
    [Trait("Business", "Cadastro de Solicitação Externa")]
    [ClassData(typeof(ValidarCamposCadastroDeSolicitacaoExterna))]
    public async Task CadastroDeSolicitacaoExterna_CamposInvalidos_DeveRetornarErro(ValidarCamposCadastroDeSolicitacaoExternaAction validarCampo)
    {
        // Arrange
        
        var evento =
            SolicitacaoEventsFixture.CriarCadastroDeSolicitacaoExternaRecebidaIntegrationEvent();

        validarCampo.Acao(evento);
        
        evento.DefinirHeaders([new Header("AuthorizationUser", "dummyToken"u8.ToArray())]);
    
        var usuarioDto = UsuarioFixture.GerarUsuarioDtoValido();
    
        _dependencyInjectorFactory.Mocker
            .GetMock<IUsuarioJwtHelper>()
            .Setup(x => x.GetUsuario(It.IsAny<string>()))
            .Returns(new UsuarioJwtDto(usuarioDto.Sid, usuarioDto.Login, usuarioDto.Nome));
    
        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>(); 
        
        // Act && Assert
    
        await mediator.PublicarEventoAsync(evento, CancellationToken.None);
        
        var eventoPublicado = _dependencyInjectorFactory.Harness.Published.Any<CadastroDeSolicitacaoExternaRecebidaIntegrationEvent>();
        var eventoConsumido = _dependencyInjectorFactory.Harness.Consumed.Any<CadastroDeSolicitacaoExternaRecebidaIntegrationEvent>();

        var fault = await _dependencyInjectorFactory.Harness.Published
            .SelectAsync<ErroCadastroDeSolicitacaoExternaEvent>()
            .FirstOrDefault();

        fault.ShouldNotBeNull();
        var erros = fault.Context.Message.Erros;
        erros.ShouldNotBeEmpty();
        
        await Task.WhenAll(eventoPublicado, eventoConsumido);
    
        eventoPublicado.Result.ShouldBeTrue();
        eventoConsumido.Result.ShouldBeTrue();
    }
    
    public class ValidarCamposCadastroDeSolicitacaoExterna : IEnumerable<object[]>
    {
        public IEnumerator<object[]> GetEnumerator()
        {
            yield return [new ValidarCamposCadastroDeSolicitacaoExternaAction("Origem", x => x.Origem = null)];
            yield return [new ValidarCamposCadastroDeSolicitacaoExternaAction("Destino", x => x.Destino = null)];
            yield return [new ValidarCamposCadastroDeSolicitacaoExternaAction("Mensagem", x => x.Mensagem = null)];
            yield return [new ValidarCamposCadastroDeSolicitacaoExternaAction("Codigo Externo", x => x.CodigoExterno = null)];
            yield return [new ValidarCamposCadastroDeSolicitacaoExternaAction("Sistema de Origem", x => x.SistemaDeOrigem = null)];
        }
        
        IEnumerator IEnumerable.GetEnumerator() => GetEnumerator();
    }
    
    public class ValidarCamposCadastroDeSolicitacaoExternaAction
    {
        private string Nome { get; }
        public Action<CadastroDeSolicitacaoExternaRecebidaIntegrationEvent> Acao { get; }

        public ValidarCamposCadastroDeSolicitacaoExternaAction(string nome, Action<CadastroDeSolicitacaoExternaRecebidaIntegrationEvent> acao)
        {
            Nome = nome;
            Acao = acao;
        }

        public override string ToString() => Nome;
    }
    
    #endregion
}