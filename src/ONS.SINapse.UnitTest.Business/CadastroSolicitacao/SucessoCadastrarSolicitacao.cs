using Microsoft.Extensions.DependencyInjection;
using ONS.SINapse.Repository.IRepository;
using ONS.SINapse.Repository.IRepository.Firebase;
using ONS.SINapse.Repository.IRepository.InMemory;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using ONS.SINapse.Solicitacao.Dtos.Integracao;
using ONS.SINapse.Solicitacao.EventHandlers.Events;
using ONS.SINapse.UnitTest.Shared.MongoDb;
using System.Linq.Expressions;
using ONS.SINapse.Integracao.Shared.Enums;

namespace ONS.SINapse.UnitTest.Business.CadastroSolicitacao;

public partial class CadastrarSolicitacaoTest
{
    [Theory(DisplayName = "Deve cadastrar uma nova solicitacao apenas no Firebase")]
    [Trait("Business", "Cadastrar Solicitacao")]
    [InlineData(true, StatusDeSolicitacao.AguardandoEnvio)]
    [InlineData(false, StatusDeSolicitacao.Pendente)]
    public async Task DeveCadastrarSolicitacao(bool requerAprovacaoEnvio, StatusDeSolicitacao statusEsperado)
    {
        // Arrange
        
        var solicitacao = requerAprovacaoEnvio ? _solicitacaoFixture.GerarSolicitacaoAguardandoEnvio() : _solicitacaoFixture.GerarSolicitacaoPendente();

        var dados = new List<Entities.Entities.Solicitacao>().AsQueryable();
        
        _dependencyInjectorFactory.Mocker.Use(dados.BuildMockAsyncCursorSource());

        _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoMemoryRepository>()
            .Setup(repository => repository.GetList(It.IsAny<Expression<Func<Entities.Entities.Solicitacao, bool>>>()))
            .Returns([solicitacao]);

        _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoFirebaseRepository>()
            .Setup(repository => repository.AddUpdateAsync(It.IsAny<List<Entities.Entities.Solicitacao>>(), It.IsAny<CancellationToken>()));

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        var solicitacaoCadastrar = _cadastrarSolicitacaoFixture.ObterCadastroSolicitacaoValido(requerAprovacaoEnvio: requerAprovacaoEnvio);
        
        var command = new CadastrarSolicitacaoEmLoteCommand([solicitacaoCadastrar]);
        
        // Act

        var response =
            await mediator.EnviarComandoAsync<CadastrarSolicitacaoEmLoteCommand, StatusDeSolicitacaoIntegracaoEnvioEmLoteDto>(
                command, CancellationToken.None);

        // Assert
        
        Assert.NotNull(response);

        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoFirebaseRepository>()
            .Verify(
                repository =>
                    repository.AddUpdateAsync(It.IsAny<List<Entities.Entities.Solicitacao>>(), It.IsAny<CancellationToken>()),
            Times.Once);

        var commandPublished = _dependencyInjectorFactory.Harness.Published.Any<CadastrarSolicitacaoEmLoteNoFirebaseCommand>();
        var eventPublished = _dependencyInjectorFactory.Harness.Published.Any<SolicitacoesCadastradasEmLoteEvent>();
        var kafkaEventPublished = _dependencyInjectorFactory.Harness.Published.Any<StatusDeSolicitacaoIntegracaoRecebidaEvent>();

        await Task.WhenAll(commandPublished, eventPublished, kafkaEventPublished);
        
        commandPublished.Result.ShouldBeTrue();
        eventPublished.Result.ShouldBeTrue();
        kafkaEventPublished.Result.ShouldBeTrue();
        response.Solicitacoes.All(x => x.Status == statusEsperado).ShouldBeTrue();
    }
}