using Microsoft.Extensions.DependencyInjection;
using ONS.SINapse.Repository.IRepository;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using ONS.SINapse.Solicitacao.Dtos.Integracao;
using ONS.SINapse.Solicitacao.EventHandlers.Events;
using ONS.SINapse.UnitTest.Shared.MongoDb;

namespace ONS.SINapse.UnitTest.Business.CadastroSolicitacao;

public partial class CadastrarSolicitacaoTest
{
    [Fact(DisplayName = "Deve cadastrar uma nova solicitacao apenas no Firebase")]
    [Trait("Business", "Cadastrar Solicitacao")]
    public async Task DeveCadastrarSolicitacao()
    {
        // Arrange
        
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoPendente();
        var repositoryMock = _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoRepository>();

        var dados = new List<Entities.Entities.Solicitacao>().AsQueryable();
        
        _dependencyInjectorFactory.Mocker.Use(dados.BuildMockAsyncCursorSource());
        
        repositoryMock.Setup(x => x.Queryable())
            .Returns(_dependencyInjectorFactory.Mocker.Get<IQueryable<Entities.Entities.Solicitacao>>());

        repositoryMock.Setup(x =>
                x.AdicionarAsync(It.IsAny<List<Entities.Entities.Solicitacao>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([solicitacao]);
        
        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        var solicitacaoCadastrar = _cadastrarSolicitacaoFixture.ObterCadastroSolicitacaoValido();
        
        var command = new CadastrarSolicitacaoEmLoteCommand([solicitacaoCadastrar]);
        
        // Act

        var response =
            await mediator.EnviarComandoAsync<CadastrarSolicitacaoEmLoteCommand, StatusDeSolicitacaoIntegracaoEnvioEmLoteDto>(
                command, CancellationToken.None);

        // Assert
        
        Assert.NotNull(response);

        repositoryMock.Verify(
            repository =>
                repository.AdicionarAsync(It.IsAny<List<Entities.Entities.Solicitacao>>(), It.IsAny<CancellationToken>()),
            Times.Never);

        var commandPublished = _dependencyInjectorFactory.Harness.Published.Any<CadastrarSolicitacaoEmLoteNoFirebaseCommand>();
        var eventPublished = _dependencyInjectorFactory.Harness.Published.Any<SolicitacoesCadastradasEmLoteEvent>();
        var kafkaEventPublished = _dependencyInjectorFactory.Harness.Published.Any<StatusDeSolicitacaoIntegracaoRecebidaEvent>();

        await Task.WhenAll(commandPublished, eventPublished, kafkaEventPublished);
        
        commandPublished.Result.ShouldBeTrue();
        eventPublished.Result.ShouldBeTrue();
        kafkaEventPublished.Result.ShouldBeTrue();
    }
}