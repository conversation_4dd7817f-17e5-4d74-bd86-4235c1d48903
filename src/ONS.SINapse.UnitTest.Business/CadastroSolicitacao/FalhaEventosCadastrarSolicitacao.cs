using FluentValidation.Results;
using ONS.SINapse.Repository.IRepository;
using ONS.SINapse.Repository.IRepository.InMemory;
using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using ONS.SINapse.Solicitacao.Dtos.Integracao;
using ONS.SINapse.Solicitacao.EventHandlers.Events;
using ONS.SINapse.UnitTest.Shared.MongoDb;
using System.Linq.Expressions;

namespace ONS.SINapse.UnitTest.Business.CadastroSolicitacao;

public partial class CadastrarSolicitacaoTest
{
    [Fact(DisplayName = "Deve retornar um erro cadastro no firebase")]
    [Trait("Business", "Cadastrar Solicitacao")]
    public async Task CadastrarSolicitacao_DeveRetornarUmErro_FirebaseCommand()
    {
        // Arrange

        var solicitacaoCadastrar = _cadastrarSolicitacaoFixture.ObterCadastroSolicitacaoValido();

        var solicitacao = _solicitacaoFixture.GerarSolicitacaoPendente();
        
        var repositoryMock = _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoRepository>();

        var dados = new List<Entities.Entities.Solicitacao>().AsQueryable();
        
        _dependencyInjectorFactory.Mocker.Use(dados.BuildMockAsyncCursorSource());

        _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoMemoryRepository>()
            .Setup(repository => repository.GetList(It.IsAny<Expression<Func<Entities.Entities.Solicitacao, bool>>>()))
            .Returns([solicitacao]);

        var command = new CadastrarSolicitacaoEmLoteCommand([solicitacaoCadastrar]);

        var result = new ValidationResult();
        result.AdicionarErro("Erro ao enviar command para o firebase");

        var mediatorMock = _dependencyInjectorFactory.Mocker.GetMock<IMediatorHandler>();

        mediatorMock
            .Setup(x => x.EnviarComandoAsync(It.IsAny<CadastrarSolicitacaoEmLoteNoFirebaseCommand>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(result);

        // Act

        var response =
            await mediatorMock.Object
                .EnviarComandoAsync<CadastrarSolicitacaoEmLoteCommand, StatusDeSolicitacaoIntegracaoEnvioEmLoteDto>(
                    command, CancellationToken.None);

        // Assert

        Assert.NotNull(response);

        var erros = response.Solicitacoes
            .SelectMany(x => x.Erros)
            .ToList();
        
        erros.ShouldNotBeEmpty();
        
        erros.Any(x => x.Contains("Erro ao enviar command para o firebase")).ShouldBeTrue();
        
        (await _dependencyInjectorFactory.Harness.Published.Any<SolicitacoesCadastradasEmLoteEvent>()).ShouldBeFalse();
    }
}