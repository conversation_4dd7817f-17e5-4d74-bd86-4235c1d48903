using ONS.SINapse.UnitTest.Business.Fixtures.Solicitacao;
using ONS.SINapse.UnitTest.Business.Fixtures.Solicitacao.CadastroSolicitacao;
using ONS.SINapse.UnitTest.Shared;
using ONS.SINapse.UnitTest.Shared.Fixtures;

namespace ONS.SINapse.UnitTest.Business.CadastroSolicitacao;

[Collection(nameof(CadastrarSolicitacaoCollection))]
public partial class CadastrarSolicitacaoTest
{
    private readonly CadastrarSolicitacaoFixture _cadastrarSolicitacaoFixture;
    private readonly SolicitacaoFixture _solicitacaoFixture;
    private readonly MockDependencyInjectorFactory _dependencyInjectorFactory;

    public CadastrarSolicitacaoTest(CadastrarSolicitacaoFixture cadastrarSolicitacaoFixture,
        SolicitacaoFixture solicitacaoFixture)
    {
        _cadastrarSolicitacaoFixture = cadastrarSolicitacaoFixture;
        _solicitacaoFixture = solicitacaoFixture;
        _dependencyInjectorFactory = new MockDependencyInjectorFactory();
        _dependencyInjectorFactory.RegisterMocks();
    }
}