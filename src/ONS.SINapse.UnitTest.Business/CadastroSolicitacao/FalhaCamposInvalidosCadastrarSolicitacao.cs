using Microsoft.Extensions.DependencyInjection;
using ONS.SINapse.Repository.IRepository;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using ONS.SINapse.Solicitacao.Dtos.Integracao;
using ONS.SINapse.Solicitacao.EventHandlers.Events;
using ONS.SINapse.UnitTest.Shared.MongoDb;

namespace ONS.SINapse.UnitTest.Business.CadastroSolicitacao;

public partial class CadastrarSolicitacaoTest
{
    
    [Fact(DisplayName = "Deve retornar um erro cadastro Solicitação ja Existe")]
    [Trait("Business", "Cadastrar Solicitacao")]
    public async Task CadastrarSolicitacao_DeveRetornarUmErro_SolicitacaoJaExiste()
    {
        // Arrange

        var solicitacaoCadastrar = _cadastrarSolicitacaoFixture.ObterCadastroSolicitacaoValido();
        solicitacaoCadastrar.DefinirComoSolicitacaoExterna("teste", Guid.NewGuid().ToString());
        
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoPendente();

        solicitacaoCadastrar.Id = solicitacao.Id;
        
        var repositoryMock = _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoRepository>();

        var dados = new List<Entities.Entities.Solicitacao> { solicitacao }.AsQueryable();
        
        _dependencyInjectorFactory.Mocker.Use(dados.BuildMockAsyncCursorSource());
        
        repositoryMock.Setup(x => x.Queryable())
            .Returns(_dependencyInjectorFactory.Mocker.Get<IQueryable<Entities.Entities.Solicitacao>>());

        repositoryMock.Setup(x =>
                x.AdicionarAsync(It.IsAny<List<Entities.Entities.Solicitacao>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([solicitacao]);
        
        var command = new CadastrarSolicitacaoEmLoteCommand([solicitacaoCadastrar]);

        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        // Act
        
        var response =
            await mediator.EnviarComandoAsync<CadastrarSolicitacaoEmLoteCommand, StatusDeSolicitacaoIntegracaoEnvioEmLoteDto>(
                command, CancellationToken.None);
        
        // Assert

        response.ShouldNotBeNull();

        var erros = response.Solicitacoes
            .SelectMany(x => x.Erros)
            .ToList();

        erros.Any(x => x.Contains("Erro ao cadastrar solicitação, solicitação já esta cadastrada na base de dados.")).ShouldBeTrue();
        
        erros.ShouldNotBeEmpty();

        var cadastroNoFirebaseCommand = await _dependencyInjectorFactory.Harness.Published
            .Any<CadastrarSolicitacaoEmLoteNoFirebaseCommand>();

        var solicitacaoCadastradaEvent = await _dependencyInjectorFactory.Harness.Published
            .Any<SolicitacoesCadastradasEmLoteEvent>();
        
        cadastroNoFirebaseCommand.ShouldBeFalse();
        solicitacaoCadastradaEvent.ShouldBeFalse();
    }
    
    [Fact(DisplayName = "Deve retornar um erro cadastro Sistema de Origem Invalido")]
    [Trait("Business", "Cadastrar Solicitacao")]
    public async Task CadastrarSolicitacao_DeveRetornarUmErro_SistemaDeOrigemNaoPreenchido()
    {
        // Arrange

        var solicitacaoCadastrar = _cadastrarSolicitacaoFixture.ObterCadastroSolicitacaoSistemaDeOrigemInvalido();
        
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoPendente();
        
        var repositoryMock = _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoRepository>();

        var dados = new List<Entities.Entities.Solicitacao>().AsQueryable();
        
        _dependencyInjectorFactory.Mocker.Use(dados.BuildMockAsyncCursorSource());
        
        repositoryMock.Setup(x => x.Queryable())
            .Returns(_dependencyInjectorFactory.Mocker.Get<IQueryable<Entities.Entities.Solicitacao>>());

        repositoryMock.Setup(x =>
                x.AdicionarAsync(It.IsAny<List<Entities.Entities.Solicitacao>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([solicitacao]);
        
        // Act & Assert

        await ValidarCampoCadastroDeSolicitacao(solicitacaoCadastrar, "Sistema De Origem");
    }
    
    [Fact(DisplayName = "Deve retornar um erro cadastro Destino Invalido")]
    [Trait("Business", "Cadastrar Solicitacao")]
    public async Task CadastrarSolicitacao_DeveRetornarUmErro_DestinoNaoPreenchido()
    {
        // Arrange

        var solicitacaoCadastrar = _cadastrarSolicitacaoFixture.ObterCadastroSolicitacaoDestinoInvalido();
        
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoPendente();
        
        var repositoryMock = _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoRepository>();

        var dados = new List<Entities.Entities.Solicitacao>().AsQueryable();
        
        _dependencyInjectorFactory.Mocker.Use(dados.BuildMockAsyncCursorSource());
        
        repositoryMock.Setup(x => x.Queryable())
            .Returns(_dependencyInjectorFactory.Mocker.Get<IQueryable<Entities.Entities.Solicitacao>>());

        repositoryMock.Setup(x =>
                x.AdicionarAsync(It.IsAny<List<Entities.Entities.Solicitacao>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([solicitacao]);
        
        // Act & Assert

        await ValidarCampoCadastroDeSolicitacao(solicitacaoCadastrar, nameof(CadastroSolicitacaoDto.Destino));
    }
    
    [Fact(DisplayName = "Deve retornar um erro cadastro Origem Invalido")]
    [Trait("Business", "Cadastrar Solicitacao")]
    public async Task CadastrarSolicitacao_DeveRetornarUmErro_OrigemNaoPreenchida()
    {
        // Arrange
        
        var solicitacaoCadastrar = _cadastrarSolicitacaoFixture.ObterCadastroSolicitacaoOrigemInvalida();
        
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoPendente();
        
        var repositoryMock = _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoRepository>();

        var dados = new List<Entities.Entities.Solicitacao>().AsQueryable();
        
        _dependencyInjectorFactory.Mocker.Use(dados.BuildMockAsyncCursorSource());
        
        repositoryMock.Setup(x => x.Queryable())
            .Returns(_dependencyInjectorFactory.Mocker.Get<IQueryable<Entities.Entities.Solicitacao>>());

        repositoryMock.Setup(x =>
                x.AdicionarAsync(It.IsAny<List<Entities.Entities.Solicitacao>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([solicitacao]);
        
        // Act & Assert

        await ValidarCampoCadastroDeSolicitacao(solicitacaoCadastrar, nameof(CadastroSolicitacaoDto.Origem));
    }
    
    [Fact(DisplayName = "Deve retornar um erro cadastro sem usuário")]
    [Trait("Business", "Cadastrar Solicitacao")]
    public async Task CadastrarSolicitacao_DeveRetornarUmErro_UsuarioNaoPreenchido()
    {
        // Arrange
        
        var solicitacaoCadastrar = _cadastrarSolicitacaoFixture.ObterCadastroSolicitacaoUsuarioInvalido();

        solicitacaoCadastrar.Usuario = null;
        
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoPendente();
        
        var repositoryMock = _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoRepository>();

        var dados = new List<Entities.Entities.Solicitacao>().AsQueryable();
        
        _dependencyInjectorFactory.Mocker.Use(dados.BuildMockAsyncCursorSource());
        
        repositoryMock.Setup(x => x.Queryable())
            .Returns(_dependencyInjectorFactory.Mocker.Get<IQueryable<Entities.Entities.Solicitacao>>());

        repositoryMock.Setup(x =>
                x.AdicionarAsync(It.IsAny<List<Entities.Entities.Solicitacao>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([solicitacao]);
        
        // Act & Assert

        await ValidarCampoCadastroDeSolicitacao(solicitacaoCadastrar, nameof(CadastroSolicitacaoDto.Usuario));
    }


    [Fact(DisplayName = "Deve retornar um erro cadastro sem mensagem")]
    [Trait("Business", "Cadastrar Solicitacao")]
    public async Task CadastrarSolicitacao_DeveRetornarUmErro_MensagemNaoPreenchida()
    {
        // Arrange 

        var solicitacaoCadastrar = _cadastrarSolicitacaoFixture.ObterCadastroSolicitacaoMensagemInvalida();
            
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoPendente();
        
        var repositoryMock = _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoRepository>();

        var dados = new List<Entities.Entities.Solicitacao>().AsQueryable();
        
        _dependencyInjectorFactory.Mocker.Use(dados.BuildMockAsyncCursorSource());
        
        repositoryMock.Setup(x => x.Queryable())
            .Returns(_dependencyInjectorFactory.Mocker.Get<IQueryable<Entities.Entities.Solicitacao>>());

        repositoryMock.Setup(x =>
                x.AdicionarAsync(It.IsAny<List<Entities.Entities.Solicitacao>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([solicitacao]);
        
        // Act & Assert

        await ValidarCampoCadastroDeSolicitacao(solicitacaoCadastrar, nameof(CadastroSolicitacaoDto.Mensagem));
    }

    private async Task ValidarCampoCadastroDeSolicitacao(CadastroSolicitacaoDto solicitacaoCadastrar, string campo)
    {
        // Arrange
    
        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();
        
        var command = new CadastrarSolicitacaoEmLoteCommand([solicitacaoCadastrar]);

        // Act
        
        var response =
            await mediator.EnviarComandoAsync<CadastrarSolicitacaoEmLoteCommand, StatusDeSolicitacaoIntegracaoEnvioEmLoteDto>(
                command, CancellationToken.None);
        
        // Assert

        response.ShouldNotBeNull();

        var erros = response.Solicitacoes
            .SelectMany(x => x.Erros)
            .ToList();

        erros.Any(x => x.Contains($"Campo '{campo}' não pode")).ShouldBeTrue();
        
        erros.ShouldNotBeEmpty();

        var cadastroNoFirebaseCommand = await _dependencyInjectorFactory.Harness.Published
            .Any<CadastrarSolicitacaoEmLoteNoFirebaseCommand>();

        var solicitacaoCadastradaEvent = await _dependencyInjectorFactory.Harness.Published
            .Any<SolicitacoesCadastradasEmLoteEvent>();
        
        cadastroNoFirebaseCommand.ShouldBeFalse();
        solicitacaoCadastradaEvent.ShouldBeFalse();
    }
}