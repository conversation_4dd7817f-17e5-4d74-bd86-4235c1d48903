using System.Linq.Expressions;
using FluentValidation.TestHelper;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using ONS.SINapse.Repository.IRepository;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using ONS.SINapse.Solicitacao.EventHandlers.Events;

namespace ONS.SINapse.UnitTest.Business.CadastroSolicitacao;

public partial class CadastrarSolicitacaoTest
{
    
    [Fact(DisplayName = "Deve retornar um erro cadastro Solicitação ja Existe")]
    [Trait("Business", "Cadastrar Solicitacao")]
    public async Task CadastrarSolicitacao_DeveRetornarUmErro_SolicitacaoJaExiste()
    {
        // Arrange

        var solicitacaoCadastrar = _cadastrarSolicitacaoFixture.ObterCadastroSolicitacaoValido();
        solicitacaoCadastrar.DefinirComoSolicitacaoExterna("teste", Guid.NewGuid().ToString());

        _dependencyInjectorFactory.Mocker
            .GetMock<ISolicitacaoRepository>()
            .Setup(x => x.AnyAsync(It.IsAny<Expression<Func<Entities.Entities.Solicitacao, bool>>>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);
        
        // Act & Assert

        await ValidarCampoCadastroDeSolicitacao(solicitacaoCadastrar, nameof(CadastroSolicitacaoDto.CodigoExterno));
    }
    
    [Fact(DisplayName = "Deve retornar um erro cadastro Sistema de Origem Invalido")]
    [Trait("Business", "Cadastrar Solicitacao")]
    public async Task CadastrarSolicitacao_DeveRetornarUmErro_SistemaDeOrigemNaoPreenchido()
    {
        // Arrange

        var solicitacaoCadastrar = _cadastrarSolicitacaoFixture.ObterCadastroSolicitacaoSistemaDeOrigemInvalido();
        
        // Act & Assert

        await ValidarCampoCadastroDeSolicitacao(solicitacaoCadastrar, nameof(CadastroSolicitacaoDto.SistemaDeOrigem));
    }
    
    [Fact(DisplayName = "Deve retornar um erro cadastro Destino Invalido")]
    [Trait("Business", "Cadastrar Solicitacao")]
    public async Task CadastrarSolicitacao_DeveRetornarUmErro_DestinoNaoPreenchido()
    {
        // Arrange

        var solicitacaoCadastrar = _cadastrarSolicitacaoFixture.ObterCadastroSolicitacaoDestinoInvalido();
        
        // Act & Assert

        await ValidarCampoCadastroDeSolicitacao(solicitacaoCadastrar, nameof(CadastroSolicitacaoDto.Destino));
    }
    
    [Fact(DisplayName = "Deve retornar um erro cadastro Origem Invalido")]
    [Trait("Business", "Cadastrar Solicitacao")]
    public async Task CadastrarSolicitacao_DeveRetornarUmErro_OrigemNaoPreenchida()
    {
        // Arrange
        
        var solicitacaoCadastrar = _cadastrarSolicitacaoFixture.ObterCadastroSolicitacaoOrigemInvalida();
        
        // Act & Assert

        await ValidarCampoCadastroDeSolicitacao(solicitacaoCadastrar, nameof(CadastroSolicitacaoDto.Origem));
    }
    
    [Fact(DisplayName = "Deve retornar um erro cadastro sem usuário")]
    [Trait("Business", "Cadastrar Solicitacao")]
    public async Task CadastrarSolicitacao_DeveRetornarUmErro_UsuarioNaoPreenchido()
    {
        // Arrange
        
        var solicitacaoCadastrar = _cadastrarSolicitacaoFixture.ObterCadastroSolicitacaoUsuarioInvalido();
        
        _dependencyInjectorFactory.Mocker.GetMock<IHttpContextAccessor>()
            .SetupGet(x => x.HttpContext).Returns(() => null);
        
        // Act & Assert

        await ValidarCampoCadastroDeSolicitacao(solicitacaoCadastrar, nameof(CadastroSolicitacaoDto.Usuario));
    }


    [Fact(DisplayName = "Deve retornar um erro cadastro sem mensagem")]
    [Trait("Business", "Cadastrar Solicitacao")]
    public async Task CadastrarSolicitacao_DeveRetornarUmErro_MensagemNaoPreenchida()
    {
        // Arrange 
        
        var solicitacaoCadastrar = _cadastrarSolicitacaoFixture.ObterCadastroSolicitacaoMensagemInvalida();
        
        // Act & Assert

        await ValidarCampoCadastroDeSolicitacao(solicitacaoCadastrar, nameof(CadastroSolicitacaoDto.Mensagem));
    }

    private async Task ValidarCampoCadastroDeSolicitacao(CadastroSolicitacaoDto solicitacaoCadastrar, string campo)
    {
        // Arrange
    
        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();
        
        var command = new CadastrarSolicitacaoEmLoteCommand([solicitacaoCadastrar]);

        // Act
        
        var response =
            await mediator.EnviarComandoAsync<CadastrarSolicitacaoEmLoteCommand, ResultadoCadastroSolicitacaoEmLoteDto>(
                command, CancellationToken.None);
        
        // Assert

        response.ShouldNotBeNull();
        response.Resultado.ShouldContain(x => x.Erros.Count != 0);
        response.Resultado.ShouldNotBeEmpty();
        response.ValidationResult.IsValid.ShouldBeFalse();

        var testValidationResult = new TestValidationResult<CadastroSolicitacaoDto>(response.ValidationResult);
        testValidationResult.ShouldHaveValidationErrorFor(campo);

        var cadastroNoFirebaseCommand = await _dependencyInjectorFactory.Harness.Published
            .Any<CadastrarSolicitacaoEmLoteNoFirebaseCommand>();

        var solicitacaoCadastradaEvent = await _dependencyInjectorFactory.Harness.Published
            .Any<SolicitacoesCadastradasEmLoteEvent>();
        
        cadastroNoFirebaseCommand.ShouldBeFalse();
        solicitacaoCadastradaEvent.ShouldBeFalse();
    }
}