using MassTransit;
using Microsoft.Extensions.DependencyInjection;
using ONS.SINapse.Business.IBusiness.Firebase;
using ONS.SINapse.Shared.DTO.Firebase;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Solicitacao.EventHandlers.Events;
using ONS.SINapse.UnitTest.Business.Fixtures.Collections;
using ONS.SINapse.UnitTest.Business.Fixtures.Solicitacao;
using ONS.SINapse.UnitTest.Shared;
using ONS.SINapse.UnitTest.Shared.Fixtures;

namespace ONS.SINapse.UnitTest.Business.AlertaSonoro;

[Collection(nameof(AlertaSonoroEmLoteEventHandlerCollection))]
public class AlertaSonoroEmLoteEventHandlerTest
{
    private readonly MockDependencyInjectorFactory _dependencyInjectorFactory;
    private readonly SolicitacaoFixture _solicitacaoFixture;

    public AlertaSonoroEmLoteEventHandlerTest(SolicitacaoFixture solicitacaoFixture)
    {
        _solicitacaoFixture = solicitacaoFixture;
        _dependencyInjectorFactory = new MockDependencyInjectorFactory();
        _dependencyInjectorFactory.RegisterMocks();
    }
    
    [Fact(DisplayName = "[Cadastro Solicitação] - Deve cadastrar um alerta sonoro no firebase")]
    [Trait("Business", "Alerta Sonoro Firebase Event Handler")]
    public async Task CadastrarNotificacaoFirebase_CadastroSolicitacao_DeveRetornarSucesso()
    {
        // Arrange
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoPendente();
        var notificationEvent = SolicitacaoEventsFixture.CriarSolicitacaoCadastradaEvent(solicitacao);
        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        // Act
        
        await mediator.PublicarEventoAsync(notificationEvent, CancellationToken.None);
        
        // Assert

        var eventoPublicado = _dependencyInjectorFactory.Harness.Published.Any<SolicitacoesCadastradasEmLoteEvent>();
        var eventoConsumido = _dependencyInjectorFactory.Harness.Consumed.Any<SolicitacoesCadastradasEmLoteEvent>();
        await Task.WhenAll(eventoPublicado, eventoConsumido);
        
        eventoPublicado.Result.ShouldBeTrue();
        eventoConsumido.Result.ShouldBeTrue();
    }
    

    [Fact(DisplayName = "[Cadastro Solicitação] - Deve ocorrer erro ao cadastrar um alerta sonoro no firebase")]
    [Trait("Business", "Alerta Sonoro Firebase Event Handler")]
    public async Task CadastrarNotificacaoFirebase_CadastroSolicitacao_DeveRetornarErro()
    {
        // Arrange
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoPendente();
        var notificationEvent = SolicitacaoEventsFixture.CriarSolicitacaoCadastradaEvent(solicitacao);
        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();
        _dependencyInjectorFactory.Mocker
            .GetMock<INotificacaoFirebaseBusiness>()
            .Setup(business =>
                business.SendAsync(It.IsAny<IEnumerable<NotificacaoRealtimeDto>>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new Exception());

        // Act
        
        await mediator.PublicarEventoAsync(notificationEvent, CancellationToken.None);
        
        // Assert

        var eventoPublicado = 
            _dependencyInjectorFactory.Harness.Published.Any<SolicitacoesCadastradasEmLoteEvent>();
        
        var eventoConsumido = 
            _dependencyInjectorFactory.Harness.Consumed.Any<SolicitacoesCadastradasEmLoteEvent>();
        
        var falhaDisparada =
            _dependencyInjectorFactory.Harness.Published.Any<Fault<SolicitacoesCadastradasEmLoteEvent>>();
        
        await Task.WhenAll(eventoPublicado, eventoConsumido, falhaDisparada);
        
        eventoPublicado.Result.ShouldBeTrue();
        eventoConsumido.Result.ShouldBeTrue();
        falhaDisparada.Result.ShouldBeTrue();
    }

    [Fact(DisplayName = "[Impedir Solicitação] - Deve cadastrar um alerta sonoro no firebase")]
    [Trait("Business", "Alerta Sonoro Firebase Event Handler")]
    public async Task CadastrarNotificacaoFirebase_SolicitacaoImpedida_DeveRetornarSucesso()
    {
        // Arrange
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoImpedida("Teste de unidade para cadastro notificação no firebase");
        var notificationEvent = SolicitacaoEventsFixture.CriarSolicitacaoImpedidaEvent(solicitacao);
        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();

        // Act
        
        await mediator.PublicarEventoAsync(notificationEvent, CancellationToken.None);
        
        // Assert

        var eventoPublicado = _dependencyInjectorFactory.Harness.Published.Any<SolicitacaoImpedidaEvent>();
        var eventoConsumido = _dependencyInjectorFactory.Harness.Consumed.Any<SolicitacaoImpedidaEvent>();
        await Task.WhenAll(eventoPublicado, eventoConsumido);
        
        eventoPublicado.Result.ShouldBeTrue();
        eventoConsumido.Result.ShouldBeTrue();
    }
    

    [Fact(DisplayName = "[Impedir Solicitação] - Deve ocorrer erro ao cadastrar um alerta sonoro no firebase")]
    [Trait("Business", "Alerta Sonoro Firebase Event Handler")]
    public async Task CadastrarNotificacaoFirebase_SolicitacaoImpedida_DeveRetornarErro()
    {
        // Arrange
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoImpedida("Teste de unidade para cadastro notificação no firebase");
        var notificationEvent = SolicitacaoEventsFixture.CriarSolicitacaoImpedidaEvent(solicitacao);
        var mediator = _dependencyInjectorFactory.Mocker.GetRequiredService<IMediatorHandler>();
        _dependencyInjectorFactory.Mocker
            .GetMock<INotificacaoFirebaseBusiness>()
            .Setup(business =>
                business.SendAsync(It.IsAny<IEnumerable<NotificacaoRealtimeDto>>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new Exception());

        // Act
        
        await mediator.PublicarEventoAsync(notificationEvent, CancellationToken.None);
        
        // Assert

        var eventoPublicado = 
            _dependencyInjectorFactory.Harness.Published.Any<SolicitacaoImpedidaEvent>();
        
        var eventoConsumido = 
            _dependencyInjectorFactory.Harness.Consumed.Any<SolicitacaoImpedidaEvent>();
        
        var falhaDisparada =
            _dependencyInjectorFactory.Harness.Published.Any<Fault<SolicitacaoImpedidaEvent>>();
        
        await Task.WhenAll(eventoPublicado, eventoConsumido, falhaDisparada);
        
        eventoPublicado.Result.ShouldBeTrue();
        eventoConsumido.Result.ShouldBeTrue();
        falhaDisparada.Result.ShouldBeTrue();
    }
}