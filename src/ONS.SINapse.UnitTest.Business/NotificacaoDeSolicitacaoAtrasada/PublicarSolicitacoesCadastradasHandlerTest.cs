using MassTransit;
using Microsoft.Extensions.Logging;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Shared.Bus;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Solicitacao.EventHandlers.Events;
using ONS.SINapse.Solicitacao.EventHandlers.Handlers;
using ONS.SINapse.UnitTest.Business.Fixtures.Collections;
using ONS.SINapse.UnitTest.Shared;
using ONS.SINapse.UnitTest.Shared.Fixtures;

namespace ONS.SINapse.UnitTest.Business.NotificacaoDeSolicitacaoAtrasada;

[Collection(nameof(SolicitacaoCadastradaEventHandlerCollection))]
public class SolicitacaoCadastradaPublisherHandlerTest
{
    private readonly MockDependencyInjectorFactory _dependencyInjectorFactory;
    private readonly SolicitacaoFixture _solicitacaoFixture;

    public SolicitacaoCadastradaPublisherHandlerTest(SolicitacaoFixture solicitacaoFixture)
    {
        _solicitacaoFixture = solicitacaoFixture;
        _dependencyInjectorFactory = new MockDependencyInjectorFactory();
        _dependencyInjectorFactory.RegisterMocks();
    }

    [Fact(DisplayName = "[SolicitacaoCadastradaPublisherHandler] - Deve publicar evento de solicitação cadastrada")]
    [Trait("Business", "SolicitacaoCadastradaPublisherHandler")]
    public async Task Consume_SolicitacoesCadastradasEmLote_DevePublicarEventoSolicitacaoCadastrada()
    {
        // Isolamento robusto para CI/CD - delay necessário para estabilidade
        await Task.Delay(100);
        
        // Reset completo de todos os mocks relacionados
        _dependencyInjectorFactory.Mocker.GetMock<ISqsBus>().Reset();
        
        // Aguardar um pouco mais para garantir que eventos anteriores foram processados
        await Task.Delay(50);

        // Arrange
        var solicitacoesEmLote = new List<SolicitacaoCadastradasEmLoteDto>
        {
            new("SOL-001", "NE", "Mensagem teste 1", 
                new UsuarioDto("SID-001", "Usuario 1", "<EMAIL>"), 
                "GSU", 
                new StatusDeSolicitacaoDto((short)StatusDeSolicitacao.Pendente, "Pendente"))
            {
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            },
            new("SOL-002", "SE", "Mensagem teste 2", 
                new UsuarioDto("SID-002", "Usuario 2", "<EMAIL>"), 
                "ENGIE", 
                new StatusDeSolicitacaoDto((short)StatusDeSolicitacao.Pendente, "Pendente"))
            {
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            }
        };

        var eventoEmLote = new SolicitacoesCadastradasEmLoteEvent(solicitacoesEmLote);
        
        var mockSqsBus = _dependencyInjectorFactory.Mocker.GetMock<ISqsBus>();
        var mockLogger = _dependencyInjectorFactory.Mocker.GetMock<ILogger<SolicitacaoCadastradaPublisherHandler>>();

        var handler = new SolicitacaoCadastradaPublisherHandler(mockSqsBus.Object, mockLogger.Object);

        var context = _dependencyInjectorFactory.Mocker.GetMock<ConsumeContext<SolicitacoesCadastradasEmLoteEvent>>();
        context.Setup(c => c.Message).Returns(eventoEmLote);
        context.Setup(c => c.CancellationToken).Returns(CancellationToken.None);

        // Act
        await handler.Consume(context.Object);

        // Assert
        mockSqsBus.Verify(
            bus => bus.Publish(
                It.Is<SolicitacoesCadastradasEvent>(evento => 
                    evento.Solicitacoes.Count == 2 &&
                    evento.Solicitacoes.Any(s => s.Id == "SOL-001" && s.Origem == "NE" && s.Destino == "GSU") &&
                    evento.Solicitacoes.Any(s => s.Id == "SOL-002" && s.Origem == "SE" && s.Destino == "ENGIE")),
                It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact(DisplayName = "[SolicitacaoCadastradaPublisherHandler] - Deve logar informações do evento publicado")]
    [Trait("Business", "SolicitacaoCadastradaPublisherHandler")]
    public async Task Consume_SolicitacoesCadastradasEmLote_DeveLogarInformacoes()
    {
        // Isolamento robusto para CI/CD - delay necessário para estabilidade
        await Task.Delay(100);
        
        // Reset completo de todos os mocks relacionados
        _dependencyInjectorFactory.Mocker.GetMock<ISqsBus>().Reset();
        
        // Aguardar um pouco mais para garantir que eventos anteriores foram processados
        await Task.Delay(50);

        // Arrange
        var solicitacoesEmLote = new List<SolicitacaoCadastradasEmLoteDto>
        {
            new("SOL-LOG-001", "ORIGEM-LOG", "Mensagem log teste", 
                new UsuarioDto("SID-LOG", "Usuario Log", "<EMAIL>"), 
                "DESTINO-LOG", 
                new StatusDeSolicitacaoDto((short)StatusDeSolicitacao.Pendente, "Pendente"))
            {
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            }
        };

        var eventoEmLote = new SolicitacoesCadastradasEmLoteEvent(solicitacoesEmLote);
        
        var mockSqsBus = _dependencyInjectorFactory.Mocker.GetMock<ISqsBus>();
        var mockLogger = _dependencyInjectorFactory.Mocker.GetMock<ILogger<SolicitacaoCadastradaPublisherHandler>>();

        var handler = new SolicitacaoCadastradaPublisherHandler(mockSqsBus.Object, mockLogger.Object);

        var context = _dependencyInjectorFactory.Mocker.GetMock<ConsumeContext<SolicitacoesCadastradasEmLoteEvent>>();
        context.Setup(c => c.Message).Returns(eventoEmLote);
        context.Setup(c => c.CancellationToken).Returns(CancellationToken.None);

        // Act
        await handler.Consume(context.Object);

        // Assert
        mockLogger.Verify(
            logger => logger.Log(
                LogLevel.Information,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Publicando evento de solicitação cadastrada para 1 solicitações")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }
}
