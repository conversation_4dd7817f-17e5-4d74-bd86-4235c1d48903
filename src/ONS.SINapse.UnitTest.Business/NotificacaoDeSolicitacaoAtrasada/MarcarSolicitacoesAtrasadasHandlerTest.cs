using MassTransit;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Repository.IRepository.Firebase;
using ONS.SINapse.Shared.Constants;
using ONS.SINapse.Solicitacao.EventHandlers.Events;
using ONS.SINapse.Solicitacao.EventHandlers.Handlers;
using ONS.SINapse.UnitTest.Business.Fixtures.Collections;
using ONS.SINapse.UnitTest.Shared;

namespace ONS.SINapse.UnitTest.Business.NotificacaoDeSolicitacaoAtrasada;

[Collection(nameof(SolicitacaoCadastradaEventHandlerCollection))]
public class MarcarSolicitacoesAtrasadasHandlerTest
{
    private readonly MockDependencyInjectorFactory _dependencyInjectorFactory;

    public MarcarSolicitacoesAtrasadasHandlerTest()
    {
        _dependencyInjectorFactory = new MockDependencyInjectorFactory();
        _dependencyInjectorFactory.RegisterMocks();
    }

    [Fact(DisplayName = "[MarcarSolicitacoesAtrasadasHandler] - Deve marcar solicitações pendentes como atrasadas")]
    [Trait("Business", "MarcarSolicitacoesAtrasadasHandler")]
    public async Task Consume_SolicitacoesPendentes_DeveMarcarComoAtrasadas()
    {
        // Isolamento robusto para CI/CD - delay necessário para estabilidade
        await Task.Delay(100);
        
        // Reset completo de todos os mocks relacionados
        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoFirebaseRepository>().Reset();
        
        // Aguardar um pouco mais para garantir que eventos anteriores foram processados
        await Task.Delay(50);

        // Arrange
        var solicitacoes = new List<SolicitacaoCadastradaDto>
        {
            new("SOL-001", "NE", "GSU"),
            new("SOL-002", "SE", "ENGIE")
        };
        var evento = new SolicitacoesCadastradasEvent(solicitacoes);
        
        var mockRepository = _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoFirebaseRepository>();
        
        // Configurar mock para retornar status Pendente para todas as solicitações
        mockRepository
            .Setup(repo => repo.StatusAsync("SOL-001", It.IsAny<CancellationToken>()))
            .ReturnsAsync(StatusDeSolicitacao.Pendente);
        
        mockRepository
            .Setup(repo => repo.StatusAsync("SOL-002", It.IsAny<CancellationToken>()))
            .ReturnsAsync(StatusDeSolicitacao.Pendente);

        var handler = new MarcarSolicitacoesAtrasadasHandler(
            mockRepository.Object,
            _dependencyInjectorFactory.Mocker.GetRequiredService<ILogger<MarcarSolicitacoesAtrasadasHandler>>());

        var context = _dependencyInjectorFactory.Mocker.GetMock<ConsumeContext<SolicitacoesCadastradasEvent>>();
        context.Setup(c => c.Message).Returns(evento);
        context.Setup(c => c.CancellationToken).Returns(CancellationToken.None);

        // Act
        await handler.Consume(context.Object);

        // Assert
        mockRepository.Verify(
            repo => repo.StatusAsync("SOL-001", It.IsAny<CancellationToken>()),
            Times.Once);
        
        mockRepository.Verify(
            repo => repo.StatusAsync("SOL-002", It.IsAny<CancellationToken>()),
            Times.Once);

        mockRepository.Verify(
            repo => repo.AddUpdateAsync(
                It.Is<Dictionary<string, object?>>(dict => 
                    dict.ContainsKey($"{ColecoesFirebase.Solicitacao}/SOL-001/atrasada") &&
                    dict.ContainsKey($"{ColecoesFirebase.Solicitacao}/SOL-002/atrasada") &&
                    dict[$"{ColecoesFirebase.Solicitacao}/SOL-001/atrasada"].Equals(true) &&
                    dict[$"{ColecoesFirebase.Solicitacao}/SOL-002/atrasada"].Equals(true)),
                It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact(DisplayName = "[MarcarSolicitacoesAtrasadasHandler] - Não deve processar quando todas solicitações não são pendentes")]
    [Trait("Business", "MarcarSolicitacoesAtrasadasHandler")]
    public async Task Consume_SolicitacoesNaoPendentes_NaoDeveProcessar()
    {
        // Isolamento robusto para CI/CD - delay necessário para estabilidade
        await Task.Delay(100);
        
        // Reset completo de todos os mocks relacionados
        _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoFirebaseRepository>().Reset();
        
        // Aguardar um pouco mais para garantir que eventos anteriores foram processados
        await Task.Delay(50);

        // Arrange
        var solicitacoes = new List<SolicitacaoCadastradaDto>
        {
            new("SOL-001", "NE", "GSU"),
            new("SOL-002", "SE", "ENGIE")
        };
        var evento = new SolicitacoesCadastradasEvent(solicitacoes);
        
        var mockRepository = _dependencyInjectorFactory.Mocker.GetMock<ISolicitacaoFirebaseRepository>();
        
        // Configurar mock para retornar status diferentes de Pendente
        mockRepository
            .Setup(repo => repo.StatusAsync("SOL-001", It.IsAny<CancellationToken>()))
            .ReturnsAsync(StatusDeSolicitacao.Finalizada);
        
        mockRepository
            .Setup(repo => repo.StatusAsync("SOL-002", It.IsAny<CancellationToken>()))
            .ReturnsAsync(StatusDeSolicitacao.Cancelada);

        var handler = new MarcarSolicitacoesAtrasadasHandler(
            mockRepository.Object,
            _dependencyInjectorFactory.Mocker.GetRequiredService<ILogger<MarcarSolicitacoesAtrasadasHandler>>());

        var context = _dependencyInjectorFactory.Mocker.GetMock<ConsumeContext<SolicitacoesCadastradasEvent>>();
        context.Setup(c => c.Message).Returns(evento);
        context.Setup(c => c.CancellationToken).Returns(CancellationToken.None);

        // Act
        await handler.Consume(context.Object);

        // Assert
        mockRepository.Verify(
            repo => repo.StatusAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()),
            Times.Exactly(2));

        // Não deve chamar AddUpdateAsync quando não há solicitações pendentes
        mockRepository.Verify(
            repo => repo.AddUpdateAsync(It.IsAny<Dictionary<string, object?>>(), It.IsAny<CancellationToken>()),
            Times.Never);
    }
}
