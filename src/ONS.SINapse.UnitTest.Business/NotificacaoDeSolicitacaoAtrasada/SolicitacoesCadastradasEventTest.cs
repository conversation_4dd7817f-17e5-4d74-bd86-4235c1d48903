using ONS.SINapse.Solicitacao.EventHandlers.Events;
using ONS.SINapse.UnitTest.Business.Fixtures.Collections;
using ONS.SINapse.UnitTest.Shared.Fixtures;

namespace ONS.SINapse.UnitTest.Business.NotificacaoDeSolicitacaoAtrasada;

[Collection(nameof(SolicitacaoCadastradaEventHandlerCollection))]
public class SolicitacaoCadastradaEventTest
{
    private readonly SolicitacaoFixture _solicitacaoFixture;

    public SolicitacaoCadastradaEventTest(SolicitacaoFixture solicitacaoFixture)
    {
        _solicitacaoFixture = solicitacaoFixture;
    }

    [Fact(DisplayName = "[SolicitacaoCadastradaEvent] - Deve criar evento com lista de solicitações")]
    [Trait("Business", "SolicitacaoCadastradaEvent")]
    public void CriarEvento_ComListaDeSolicitacoes_DeveRetornarEventoValido()
    {
        // Arrange
        var solicitacoes = new List<SolicitacaoCadastradaDto>
        {
            new("SOL-001", "NE", "GSU"),
            new("SOL-002", "SE", "ENGIE"),
            new("SOL-003", "SUL", "COPEL")
        };

        // Act
        var evento = new SolicitacaoCadastradaEvent(solicitacoes);

        // Assert
        evento.ShouldNotBeNull();
        evento.Solicitacoes.ShouldNotBeNull();
        evento.Solicitacoes.Count.ShouldBe(3);
        evento.Solicitacoes.ShouldContain(s => s.Id == "SOL-001" && s.Origem == "NE" && s.Destino == "GSU");
        evento.Solicitacoes.ShouldContain(s => s.Id == "SOL-002" && s.Origem == "SE" && s.Destino == "ENGIE");
        evento.Solicitacoes.ShouldContain(s => s.Id == "SOL-003" && s.Origem == "SUL" && s.Destino == "COPEL");
    }

    [Fact(DisplayName = "[SolicitacaoCadastradaEvent] - Deve criar evento com lista vazia")]
    [Trait("Business", "SolicitacaoCadastradaEvent")]
    public void CriarEvento_ComListaVazia_DeveRetornarEventoValido()
    {
        // Arrange
        var solicitacoes = new List<SolicitacaoCadastradaDto>();

        // Act
        var evento = new SolicitacaoCadastradaEvent(solicitacoes);

        // Assert
        evento.ShouldNotBeNull();
        evento.Solicitacoes.ShouldNotBeNull();
        evento.Solicitacoes.Count.ShouldBe(0);
    }

    [Fact(DisplayName = "[SolicitacaoCadastradaDto] - Deve criar DTO com propriedades corretas")]
    [Trait("Business", "SolicitacaoCadastradaEvent")]
    public void CriarDto_ComPropriedades_DeveRetornarDtoValido()
    {
        // Arrange
        const string id = "SOL-TEST-001";
        const string origem = "ORIGEM-TEST";
        const string destino = "DESTINO-TEST";

        // Act
        var dto = new SolicitacaoCadastradaDto(id, origem, destino);

        // Assert
        dto.ShouldNotBeNull();
        dto.Id.ShouldBe(id);
        dto.Origem.ShouldBe(origem);
        dto.Destino.ShouldBe(destino);
    }

    [Theory(DisplayName = "[SolicitacaoCadastradaEvent] - Deve permitir modificar lista de solicitações")]
    [Trait("Business", "SolicitacaoCadastradaEvent")]
    [InlineData(1)]
    [InlineData(5)]
    [InlineData(10)]
    public void ModificarListaSolicitacoes_ComDiferentesQuantidades_DevePermitirModificacao(int quantidade)
    {
        // Arrange
        var solicitacoesIniciais = new List<SolicitacaoCadastradaDto>
        {
            new("SOL-INICIAL", "ORIGEM-INICIAL", "DESTINO-INICIAL")
        };
        var evento = new SolicitacaoCadastradaEvent(solicitacoesIniciais);

        var novasSolicitacoes = new List<SolicitacaoCadastradaDto>();
        for (int i = 1; i <= quantidade; i++)
        {
            novasSolicitacoes.Add(new SolicitacaoCadastradaDto($"SOL-{i:000}", $"ORIGEM-{i}", $"DESTINO-{i}"));
        }

        // Act
        evento.Solicitacoes = novasSolicitacoes;

        // Assert
        evento.Solicitacoes.Count.ShouldBe(quantidade);
        for (int i = 1; i <= quantidade; i++)
        {
            evento.Solicitacoes.ShouldContain(s => s.Id == $"SOL-{i:000}");
        }
    }
}
