# Testes de Notificação de Solicitação Atrasada

Este diretório contém os testes unitários relacionados à funcionalidade de notificação de solicitações atrasadas, cobrindo o fluxo completo desde o evento de cadastro até a marcação de solicitações como atrasadas no Firebase.

## 📁 Estrutura dos Arquivos

- **SolicitacoesCadastradasEventTest.cs**: Testes da classe de evento e DTO
- **MarcarSolicitacoesAtrasadasHandlerTest.cs**: Testes do handler que marca solicitações como atrasadas
- **PublicarSolicitacoesCadastradasHandlerTest.cs**: Testes do publisher que converte eventos em lote

## 🎯 Cenários de Teste Cobertos

### 1. SolicitacoesCadastradasEvent (4 testes)

#### ✅ Cenários Principais
- **Criação de evento com lista de solicitações**: Valida a criação correta do evento com múltiplas solicitações
- **Criação de evento com lista vazia**: Testa o comportamento com lista vazia (cenário de borda)
- **Criação de DTO**: Valida a criação do `SolicitacaoCadastradaDto` com propriedades corretas

#### 🔄 Cenários Parametrizados
- **Modificação de lista**: Testa a capacidade de modificar a lista de solicitações com diferentes quantidades (1, 5, 10 itens)

### 2. MarcarSolicitacoesAtrasadasHandler (4 testes)

#### ✅ Cenários de Sucesso
- **Marcação de solicitações pendentes como atrasadas**:
  - Verifica se solicitações com status `Pendente` são corretamente marcadas como atrasadas no Firebase
  - Valida chamadas corretas ao repositório Firebase
  - Confirma atualização do campo `atrasada = true`

#### 🚫 Cenários de Não Processamento
- **Solicitações não pendentes**:
  - Testa que solicitações com status `Finalizada` ou `Cancelada` não são processadas
  - Verifica que `AddUpdateAsync` não é chamado quando não há solicitações pendentes

#### 🔀 Cenários Mistos
- **Lista mista de status**:
  - Processa apenas solicitações pendentes em uma lista com diferentes status
  - Valida seletividade do processamento

#### 📭 Cenários de Borda
- **Lista vazia**:
  - Verifica comportamento quando não há solicitações para processar
  - Confirma que nenhuma operação desnecessária é executada

### 3. PublicarSolicitacoesCadastradasHandler (4 testes)

#### ✅ Cenários de Publicação
- **Publicação de evento**:
  - Converte `SolicitacoesCadastradasEmLoteEvent` para `SolicitacoesCadastradasEvent`
  - Valida publicação correta via SQS Bus
  - Verifica mapeamento correto de propriedades (Id, Origem, Destino)

#### 📝 Cenários de Logging
- **Logging de informações**:
  - Verifica se logs informativos são gerados corretamente
  - Valida mensagens de log com quantidade de solicitações processadas

#### 📭 Cenários de Borda
- **Lista vazia**:
  - Processa lista vazia sem erros
  - Publica evento vazio corretamente
  - Gera log apropriado para quantidade zero

#### 🔄 Cenários Parametrizados
- **Diferentes quantidades**:
  - Testa conversão com 1, 5 e 10 solicitações
  - Valida que todas as propriedades são mapeadas corretamente
  - Confirma que a quantidade de saída corresponde à entrada

## 🛡️ Cenários de Borda Específicos

### 📊 Dados de Entrada
- **Lista vazia**: Todos os handlers tratam corretamente listas vazias
- **Lista com um item**: Processamento de caso mínimo
- **Lista com múltiplos itens**: Processamento em lote
- **Dados nulos/inválidos**: Proteção contra entradas inválidas

### 🔄 Estados de Solicitação
- **Apenas pendentes**: Processamento normal
- **Apenas não pendentes**: Nenhum processamento
- **Estados mistos**: Processamento seletivo
- **Estados inválidos**: Tratamento de casos excepcionais

### 🔗 Integração com Dependências
- **Firebase Repository**: Mocks configurados para diferentes cenários de resposta
- **SQS Bus**: Verificação de publicação correta de eventos
- **Logger**: Validação de logs informativos e de erro

## 🚀 Características Técnicas

### 🔒 Isolamento de Testes
- **Delays estratégicos**: 100ms + 50ms para evitar interferência entre testes
- **Reset de mocks**: Limpeza completa entre execuções
- **Collections isoladas**: Uso de `SolicitacaoCadastradaEventHandlerCollection`

### 🎯 Padrões de Verificação
- **Verificação de chamadas**: `Times.Once`, `Times.Never` para validar comportamento
- **Verificação de dados**: Validação detalhada de propriedades e conteúdo
- **Verificação de logs**: Confirmação de mensagens informativas

### 📈 Cobertura de Código
- **100% dos métodos públicos**: Todos os métodos principais testados
- **Cenários de sucesso e falha**: Cobertura completa de fluxos
- **Casos extremos**: Validação de comportamento em situações limite

## 🔧 Execução dos Testes

```bash
# Executar apenas os testes deste diretório
dotnet test --filter "FullyQualifiedName~NotificacaoDeSolicitacaoAtrasada"

# Executar com verbosidade detalhada
dotnet test --filter "FullyQualifiedName~NotificacaoDeSolicitacaoAtrasada" --verbosity detailed
```

## 📋 Resultados Esperados

- **Total de testes**: 12 (4 + 4 + 4)
- **Taxa de sucesso**: 100%
- **Tempo de execução**: ~8-10 segundos
- **Cobertura**: Cenários principais, bordas e casos excepcionais

## 📚 Documentação Adicional

### Fluxo de Funcionamento

1. **SolicitacoesCadastradasEmLoteEvent** é recebido pelo `PublicarSolicitacoesCadastradasHandler`
2. **Conversão** para `SolicitacoesCadastradasEvent` e publicação via SQS
3. **SolicitacoesCadastradasEvent** é processado pelo `MarcarSolicitacoesAtrasadasHandler`
4. **Verificação** do status de cada solicitação no Firebase
5. **Marcação** de solicitações pendentes como atrasadas

### Dependências Testadas

- `ISolicitacaoFirebaseRepository`: Repositório Firebase para consulta e atualização
- `ISqsBus`: Bus SQS para publicação de eventos
- `ILogger`: Sistema de logging para rastreabilidade
- `ConsumeContext`: Contexto de consumo de mensagens MassTransit

### Padrões de Teste Seguidos

- **AAA Pattern**: Arrange, Act, Assert
- **Isolamento**: Cada teste é independente e isolado
- **Nomenclatura**: Nomes descritivos seguindo padrão da aplicação
- **Cobertura**: Cenários positivos, negativos e de borda
- **Performance**: Testes otimizados para execução rápida e confiável