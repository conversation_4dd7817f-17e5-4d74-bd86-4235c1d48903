using Microsoft.Extensions.Options;
using ONS.SINapse.Business.Imp.Business;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Repository.IRepository;
using ONS.SINapse.Shared.DTO.Firebase;
using ONS.SINapse.Shared.Services.Firebase;
using ONS.SINapse.Shared.Settings;
using System.Linq.Expressions;

namespace ONS.SINapse.UnitTest.Business.ControleDeChamada;

public partial class ControleDeChamadaTest
{
    [Fact(DisplayName = "[Iniciar Chamada] - Deve iniciar a chamada")]
    [Trait("Business", "controle de Chamada")]
    public async Task DeveIniciarChamada()
    {
        // Arrange
        var controleDeChamada = _controleDeChamadaFixture.GerarControleDeChamadaComStatusLiberadoParaChamada();

        _dependencyInjectorFactory.Mocker
            .GetMock<IControleDeChamadaRepository>()
            .Setup(repository => repository.GetOneAsync(It.IsAny<Expression<Func<Entities.Entities.ControleDeChamada, bool>>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(controleDeChamada);

        // Act
        await _dependencyInjectorFactory.Mocker.CreateInstance<ControleDeChamadaBusiness>().Executar(CancellationToken.None);

        // Assert
        controleDeChamada.Status.ShouldBe(StatusControleDeChamada.ChamadaEmAndamento);

        _dependencyInjectorFactory.Mocker.GetMock<IControleDeChamadaRepository>().Verify(
            repository => repository.UpdateAsync( It.IsAny<string>(), It.IsAny<Entities.Entities.ControleDeChamada>(), It.IsAny<CancellationToken>()),
            Times.Once);

        _dependencyInjectorFactory.Mocker.GetMock<IControleDeChamadaRealtimeService>().Verify(
            repository => repository.AddUpdateAsync(It.IsAny<ControleDeChamadaRealtimeDto> (), It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact(DisplayName = "[Iniciar Chamada] - Deve iniciar a chamada criando a chamada")]
    [Trait("Business", "controle de Chamada")]
    public async Task DeveIniciarChamadaCriandoChamada()
    {
        // Arrange
        var controleDeChamada = _controleDeChamadaFixture.GerarControleDeChamadaComStatusLiberadoParaChamada();

        _dependencyInjectorFactory.Mocker
            .Use<IOptions<ControleDeChamadaSettings>>(Options.Create(
                new ControleDeChamadaSettings
                {
                    TempoDuracaoEmSegundos = 120,
                    TempoEntreTarefasEmSegundos = 10
                }));

        _dependencyInjectorFactory.Mocker
            .GetMock<IControleDeChamadaRepository>()
            .Setup(repository => repository.GetOneAsync(It.IsAny<Expression<Func<Entities.Entities.ControleDeChamada, bool>>>(), It.IsAny<CancellationToken>()));

        _dependencyInjectorFactory.Mocker
            .GetMock<IControleDeChamadaRepository>()
            .Setup(repository => repository.AddAsync(It.IsAny<Entities.Entities.ControleDeChamada>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(controleDeChamada);

        // Act
        await _dependencyInjectorFactory.Mocker.CreateInstance<ControleDeChamadaBusiness>().Executar(CancellationToken.None);

        // Assert
        controleDeChamada.Status.ShouldBe(StatusControleDeChamada.ChamadaEmAndamento);

        _dependencyInjectorFactory.Mocker.GetMock<IControleDeChamadaRepository>().Verify(
            repository => repository.UpdateAsync(It.IsAny<string>(), It.IsAny<Entities.Entities.ControleDeChamada>(), It.IsAny<CancellationToken>()),
            Times.Once);

        _dependencyInjectorFactory.Mocker.GetMock<IControleDeChamadaRealtimeService>().Verify(
            repository => repository.AddUpdateAsync(It.IsAny<ControleDeChamadaRealtimeDto>(), It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact(DisplayName = "[Finalizar Chamada] - Deve finalizar a chamada")]
    [Trait("Business", "controle de Chamada")]
    public async Task DeveFinalizarChamada()
    {
        // Arrange
        var controleDeChamada = _controleDeChamadaFixture.GerarControleDeChamadaComStatusChamadaEmAndamento();

        _dependencyInjectorFactory.Mocker
            .GetMock<IControleDeChamadaRepository>()
            .Setup(repository => repository.GetOneAsync(It.IsAny<Expression<Func<Entities.Entities.ControleDeChamada, bool>>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(controleDeChamada);

        // Act
        await _dependencyInjectorFactory.Mocker.CreateInstance<ControleDeChamadaBusiness>().Executar(CancellationToken.None);

        // Assert
        controleDeChamada.Status.ShouldBe(StatusControleDeChamada.ProcessamentoPendente);

        _dependencyInjectorFactory.Mocker.GetMock<IControleDeChamadaRepository>().Verify(
            repository => repository.UpdateAsync(It.IsAny<string>(), It.IsAny<Entities.Entities.ControleDeChamada>(), It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact(DisplayName = "[Iniciar Processamento Chamada] - Deve iniciar processamento da chamada")]
    [Trait("Business", "controle de Chamada")]
    public async Task DeveIniciarProcessamentoDaChamada()
    {
        // Arrange
        var controleDeChamada = _controleDeChamadaFixture.GerarControleDeChamadaComStatusProcessamentoPendente();

        _dependencyInjectorFactory.Mocker
            .GetMock<IControleDeChamadaRepository>()
            .Setup(repository => repository.GetOneAsync(It.IsAny<Expression<Func<Entities.Entities.ControleDeChamada, bool>>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(controleDeChamada);

        // Act
        await _dependencyInjectorFactory.Mocker.CreateInstance<ControleDeChamadaBusiness>().Executar(CancellationToken.None);

        // Assert
        controleDeChamada.Status.ShouldBe(StatusControleDeChamada.Processando);

        _dependencyInjectorFactory.Mocker.GetMock<IControleDeChamadaRepository>().Verify(
            repository => repository.UpdateAsync(It.IsAny<string>(), It.IsAny<Entities.Entities.ControleDeChamada>(), It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact(DisplayName = "[Processar Chamada] - Deve processar a chamada")]
    [Trait("Business", "controle de Chamada")]
    public async Task DeveProcessarChamada()
    {
        // Arrange
        var controleDeChamada = _controleDeChamadaFixture.GerarControleDeChamadaComStatusProcessando();

        _dependencyInjectorFactory.Mocker
            .GetMock<IControleDeChamadaRepository>()
            .Setup(repository => repository.GetOneAsync(It.IsAny<Expression<Func<Entities.Entities.ControleDeChamada, bool>>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(controleDeChamada);

        // Act
        await _dependencyInjectorFactory.Mocker.CreateInstance<ControleDeChamadaBusiness>().Executar(CancellationToken.None);

        // Assert
        controleDeChamada.Status.ShouldBe(StatusControleDeChamada.ProcessamentoFinalizado);

        _dependencyInjectorFactory.Mocker.GetMock<IControleDeChamadaRepository>().Verify(
            repository => repository.UpdateAsync(It.IsAny<string>(), It.IsAny<Entities.Entities.ControleDeChamada>(), It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact(DisplayName = "[Finalizar Processamento Chamada] - Deve finalizar processamento da chamada")]
    [Trait("Business", "controle de Chamada")]
    public async Task DeveFinalizarProcessamentoChamada()
    {
        // Arrange
        var controleDeChamada = _controleDeChamadaFixture.GerarControleDeChamadaComStatusProcessamentoFinalizado();

        _dependencyInjectorFactory.Mocker
            .GetMock<IControleDeChamadaRepository>()
            .Setup(repository => repository.GetOneAsync(It.IsAny<Expression<Func<Entities.Entities.ControleDeChamada, bool>>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(controleDeChamada);

        // Act
        await _dependencyInjectorFactory.Mocker.CreateInstance<ControleDeChamadaBusiness>().Executar(CancellationToken.None);

        // Assert
        controleDeChamada.Status.ShouldBe(StatusControleDeChamada.LiberadoParaChamada);

        _dependencyInjectorFactory.Mocker.GetMock<IControleDeChamadaRepository>().Verify(
            repository => repository.UpdateAsync(It.IsAny<string>(), It.IsAny<Entities.Entities.ControleDeChamada>(), It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact(DisplayName = "[Executar Delay] - Deve aguardar o tempo configurado sem exceções")]
    [Trait("Business", "controle de Chamada")]
    public async Task DeveAguardarTempoConfiguradoParaExecutarDelay()
    {
        // Arrange
        _dependencyInjectorFactory.Mocker
            .Use<IOptions<ControleDeChamadaSettings>>(Options.Create(
                new ControleDeChamadaSettings
                {
                    TempoEntreTarefasEmSegundos = 1
                }));

        // Act
        var task = _dependencyInjectorFactory.Mocker.CreateInstance<ControleDeChamadaBusiness>()
            .ExecutarDelay(CancellationToken.None);

        var completedInTime = await Task.WhenAny(task, Task.Delay(2000)) == task;

        // Assert
        completedInTime.ShouldBeTrue();
    }
}