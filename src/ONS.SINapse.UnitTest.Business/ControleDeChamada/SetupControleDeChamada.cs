using ONS.SINapse.UnitTest.Business.Fixtures.Collections;
using ONS.SINapse.UnitTest.Shared;
using ONS.SINapse.UnitTest.Shared.Fixtures;

namespace ONS.SINapse.UnitTest.Business.ControleDeChamada;

[Collection(nameof(ControleDeChamadaCollection))]
public partial class ControleDeChamadaTest
{
    private readonly MockDependencyInjectorFactory _dependencyInjectorFactory;
    private readonly ControleDeChamadaFixture _controleDeChamadaFixture;

    public ControleDeChamadaTest(ControleDeChamadaFixture controleDeChamadaFixture)
    {
        _dependencyInjectorFactory = new MockDependencyInjectorFactory();
        _dependencyInjectorFactory.RegisterMocks();
        _controleDeChamadaFixture = controleDeChamadaFixture;
    }
}