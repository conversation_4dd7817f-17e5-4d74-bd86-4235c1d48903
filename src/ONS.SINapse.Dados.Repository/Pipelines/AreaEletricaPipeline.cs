using MongoDB.Bson;

namespace ONS.SINapse.Dados.Repository.Pipelines;

public class AreaEletricaPipeline
{
    public static List<BsonDocument> AreasEletricasPipeline()
    {
        return new List<BsonDocument>()
        {
            BsonDocument.Parse(@"{
                $project: {
                    stage: '$$ROOT',
                    _id: 0
                }
            }"),
            BsonDocument.Parse(@"{
                $lookup: {
                    localField: 'stage.areareg_id',
                    from: 'c_stg_ins',
                    foreignField: 'areareg_id',
                    as: 'ins'
                }
            }"),
            BsonDocument.Parse(@"{
                $unwind: {
                    path: '$ins',
                    preserveNullAndEmptyArrays: false
                }
            }"),
            BsonDocument.Parse(@"{
                $lookup: {
                    localField: 'stage.areareg_id',
                    from: 'c_areaeletrica',
                    foreignField: 'cod_areaeletrica',
                    as: 'areaeletrica'
                }
            }"),
            BsonDocument.Parse(@"{
                $unwind: {
                    path: '$areaeletrica',
                    preserveNullAndEmptyArrays: true
                }
            }"),          
            BsonDocument.Parse(@"{
                $match: {
                    'stage.nomedes': { $not: { $regex: 'PROPOSTA', $options: 'i'} }
                }
            }"),
            BsonDocument.Parse(@"{
                $addFields: {
                    din_criacao: {
                        $cond: {
                            if: { $eq: ['$areaeletrica.din_criacao', null] },
                            then: new Date(),
                            else: '$areaeletrica.din_criacao'
                        }
                    },
                    flg_nova: {
                        $cond: {
                            if: { $eq: [{ $type: '$areaeletrica.id_areaeletrica' }, 'missing'] },
                            then: true,
                            else: false
                        }
                    }
                }
            }"),
            BsonDocument.Parse(@"{
                $group: {
                    _id: '$stage.areareg_id',
                    flg_nova: { $first: '$flg_nova' },
                    cod_areaeletrica: { $first: '$stage.areareg_id' },
                    cod_cos: { $first: '$ins.cos_id' },
                    din_criacao: { $first: '$din_criacao' },
                    din_desativacao: { $first: '$stage.dtdesativa' },
                    dsc_areaeletrica: { $first: '$stage.nomedes' },
                    id_areaeletrica: { $first: '$areaeletrica.id_areaeletrica' },
                    ido_areaeletrica: { $first: '$stage.ido_areareg' },
                    nom_curto: { $first: '$stage.nomecurto' },
                    nom_longo: { $first: '$stage.nomelongo' }
                }
            }"),
            BsonDocument.Parse(@"{
                $project: {
                    _id: 0,
                    flg_nova: 1,
                    cod_areaeletrica: 1,
                    cod_cos: 1,
                    din_criacao: { $ifNull: ['$din_criacao', new Date()] },
                    din_desativacao: 1,
                    dsc_areaeletrica: 1,
                    id_areaeletrica: 1,
                    ido_areaeletrica: 1,
                    nom_curto: 1,
                    nom_longo: 1
                }
            }")
        };
    }
}