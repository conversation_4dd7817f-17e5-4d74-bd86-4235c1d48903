using MongoDB.Bson;

namespace ONS.SINapse.Dados.Repository.Pipelines;
public static class AgentePipeline
{
    public static List<BsonDocument> AgentesPipeline()
    {
        return new List<BsonDocument>
        {
            BsonDocument.Parse(@"{
                $project: {
                    ""stage"": ""$$ROOT"",
                    ""_id"": 0
                }
            }"),            
            BsonDocument.Parse(@"{
                $lookup: {
                    localField: ""stage.age_id"",
                    from: ""c_agente"",
                    foreignField: ""cod_agente"",
                    as: ""agente""
                }
            }"),
            BsonDocument.Parse(@"{
                $unwind: {
                    path: ""$agente"",
                    preserveNullAndEmptyArrays: true
                }
            }"),            
            BsonDocument.Parse(@"{
                $lookup: {
                    from: ""c_stg_eqp"",
                    let: { age_id: ""$stage.age_id"" },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $and: [
                                        { $eq: [""$age_id_oper"", ""$$age_id""] },
                                        { $in: [""$cos_id"", [""N"", ""NE"", ""S"", ""SE""]] }
                                    ]
                                }
                            }
                        }
                    ],
                    as: ""stgeqp""
                }
            }"),
            BsonDocument.Parse(@"{
                $unwind: {
                    path: ""$stgeqp"",
                    preserveNullAndEmptyArrays: true
                }
            }"),
            BsonDocument.Parse(@"{
                $lookup: {
                    localField: 'stgeqp.areareg_id',
                    from: 'c_areaeletrica',
                    foreignField: 'cod_areaeletrica',
                    as: 'areaeletrica'
                }
            }"),
            BsonDocument.Parse(@"{
                $unwind: {
                    path: '$areaeletrica',
                    preserveNullAndEmptyArrays: true
                }
            }"),
            BsonDocument.Parse(@"{
                $match: {
                    $and: [
                        {""areaeletrica.nomedes"": { $not: { $regex: ""PROPOSTA"", $options: ""i"" } } },
                        {""stgeqp.areareg_id"": { $ne: null }},
                        {""stgeqp.util_id"": { $regex: ""T"", $options: ""i"" }}
                    ]
                }
            }"),
            BsonDocument.Parse(@"{
                $addFields: {
                    ""cod_agente"": ""$stage.age_id"",
                    ""din_criacao"": ""$agente.din_criacao"",
                    ""flg_validargeolocalizacao"": { $ifNull: [""$agente.flg_validargeolocalizacao"", false] },
                    ""id_agente"": { $ifNull: [""$agente.id_agente"", null] },
                    ""nom_curto"": ""$stage.nomecurto"",
                    ""nom_longo"": ""$stage.nomelongo"",
                    ""centroagente"": { $ifNull: [""$stgeqp.cos_id"", null] },
                    ""areaeletricas"": { $ifNull: [""$stgeqp.areareg_id"", null] },
                    ""flg_nova"": {
                        $max: {
                            $cond: {
                                if: { $eq: [{ $ifNull: [""$agente.id_agente"", null] }, null]},
                                then: true,
                                else: false
                            }
                        }
                    }
                }
            }"),
            BsonDocument.Parse(@"{
                $match: {
                    ""areaeletricas"": { $ne: """" }
                }
            }"),
            BsonDocument.Parse(@"{
                $group: {
                    _id: ""$cod_agente"",
                    din_criacao: { $first: ""$din_criacao"" },
                    flg_validargeolocalizacao: { $first: ""$flg_validargeolocalizacao"" },
                    id_agente: { $first: ""$id_agente"" },
                    nom_curto: { $first: ""$nom_curto"" },
                    nom_longo: { $first: ""$nom_longo"" },
                    list_areaeletricas: { $addToSet: ""$areaeletricas"" },
                    list_centros: { $addToSet: ""$centroagente"" },
                    flg_nova: { $max: ""$flg_nova"" }
                }
            }"),
            BsonDocument.Parse(@"{
                        $addFields: {
                            din_criacao: {
                                $cond: {
                                    if: { $eq: [""$din_criacao"", null] },
                                    then: new Date(),
                                    else: ""$din_criacao""
                                }
                            }
                        }
                    }"),
            BsonDocument.Parse(@"{
                $project: {
                    cod_agente: ""$_id"",
                    din_criacao: 1,
                    flg_validargeolocalizacao: 1,
                    id_agente: 1,
                    nom_curto: 1,
                    nom_longo: 1,
                    list_areaeletricas: 1,
                    list_centros: 1,
                    flg_nova: 1,
                    _id: 0
                }
            }")
        };
    }
}
