using MongoDB.Bson;

namespace ONS.SINapse.Dados.Repository.Pipelines;

public static class InstalacaoPipeline
{
    public static List<BsonDocument> InstalacoesPipeline()
    {
        return new List<BsonDocument>
        {
            BsonDocument.Parse(@"{
                        '$project': {
                            'c_stg_ins': '$$ROOT',
                            '_id': 0
                        }
                    }"),
            BsonDocument.Parse(@"{
                        '$lookup': {
                            'from': 'c_instalacao',
                            'localField': 'c_stg_ins.ins_id',
                            'foreignField': 'ins_id',
                            'as': 'destino'
                        }
                    }"),
            BsonDocument.Parse(@"{
                        '$unwind': {
                            'path': '$destino',
                            'preserveNullAndEmptyArrays': true
                        }
                    }"),
            BsonDocument.Parse(@"{
                        '$addFields': {
                            'ins_id': '$c_stg_ins.ins_id',
                            'ido_ons': '$c_stg_ins.ido_ons',
                            'cod_cos': '$c_stg_ins.cos_id',
                            'cod_areaeletrica': '$c_stg_ins.areareg_id',
                            'nom_curto': '$c_stg_ins.nomecurto',
                            'nom_longo': '$c_stg_ins.nomelongo',
                            'nomedes': '$c_stg_ins.nomedes',
                            'din_desativa': '$c_stg_ins.dtdesativa',
                            'din_entrada': '$c_stg_ins.dtentrada',
                            'din_prevista': '$c_stg_ins.dtprevista',
                            'id_tpins': '$c_stg_ins.tpins_id',
                            'flg_nova': {
                                '$cond': {
                                    'if': { '$eq': [{ '$type': '$destino.id_instalacao' }, 'missing'] },
                                    'then': true,
                                    'else': false
                                }
                            },
                            'din_criacao': {
                                '$cond': {
                                    'if': { '$ne': ['$destino.din_criacao', null] },
                                    'then': new Date(),
                                    'else': '$destino.din_criacao'
                                }
                            }
                        }
                    }"),
            BsonDocument.Parse(@"{
                        '$project': {
                            'id_instalacao': '$destino.id_instalacao',
                            'ins_id': 1,
                            'ido_ons': 1,
                            'cod_cos': 1,
                            'cod_areaeletrica': 1,
                            'nom_curto': 1,
                            'nom_longo': 1,
                            'nomedes': 1,
                            'din_desativa': 1,
                            'din_entrada': 1,
                            'din_prevista': 1,
                            'id_tpins': 1,
                            'flg_nova': 1,
                            'din_criacao': 1,
                            '_id': 0
                        }
                    }")
        };
    }
}