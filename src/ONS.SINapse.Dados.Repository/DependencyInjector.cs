using System.Security.Authentication;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using MongoDB.Bson.Serialization.Conventions;
using MongoDB.Driver;
using ONS.SINapse.Dados.Entities;
using ONS.SINapse.Dados.Entities.Base;
using ONS.SINapse.Dados.Repository.Base;
using ONS.SINapse.Dados.Repository.Context;
using ONS.SINapse.Dados.Repository.Repositories;
using ONS.SINapse.Dados.Repository.Repositories.Staging;
using ONS.SINapse.Dados.Repository.UnitsOfWork;
using ONS.SINapse.Dados.Shared.Settings;
using StackExchange.Redis;

namespace ONS.SINapse.Dados.Repository;

public static class DependencyInjector
{
    public static IServiceCollection RegisterRepositoryLayer(this IServiceCollection services, IConfiguration configuration)
    {
        RegisterMongoConnection(services, configuration);
        RegisterRedisConnection(services, configuration);
        RegisterRepositories(services);
        RegisterUnitsOfWork(services);
        
        
        
        return services;
    }

    private static void RegisterRedisConnection(IServiceCollection services, IConfiguration configuration)
    {
        services.AddSingleton<IConnectionMultiplexer>(_ =>
        {
            var options = ConfigurationOptions.Parse(configuration.GetConnectionString("Redis") ?? string.Empty);
            options.ConnectRetry = 3;
            return ConnectionMultiplexer.Connect(options);
        });

        services.AddSingleton<IDatabase>(provider =>
        {
            var connection = provider.GetRequiredService<IConnectionMultiplexer>();
            return connection.GetDatabase();
        });

        services.AddSingleton<IRedisContext,RedisContext>();
    }
    
    private static void RegisterMongoConnection(IServiceCollection services, IConfiguration configuration)
    {
        services.Configure<MongoDbSettings>(configuration.GetSection(nameof(MongoDbSettings)));
        
        services.AddScoped<IMongoConnection, MongoConnection>();
        services.AddScoped<IMongoQueryContext, MongoQueryContext>();
        
        services.AddSingleton<IMongoClient, MongoClient>(sp =>
        {
            var settings = sp.GetRequiredService<IOptions<MongoDbSettings>>().Value;
            var mongoClientSettings = MongoClientSettings.FromConnectionString(settings.ConnectionString);

            mongoClientSettings.ClusterConfigurator = builder =>
            {
                builder.ConfigureConnectionPool(pool => pool.With(maxConnections: settings.MaxConnections));
            };
            
            mongoClientSettings.ServerSelectionTimeout = TimeSpan.FromSeconds(60);
            mongoClientSettings.ConnectTimeout = TimeSpan.FromSeconds(30);
            
            var pack = new ConventionPack { new IgnoreExtraElementsConvention(true) };
            ConventionRegistry.Register(nameof(IgnoreExtraElementsConvention), pack, t => true);
            
            return new MongoClient(mongoClientSettings);
        });

        services.AddScoped<IMongoDatabase>(sp =>
        {
            var settings = sp.GetRequiredService<IOptions<MongoDbSettings>>().Value;
            var client = sp.GetRequiredService<IMongoClient>();
            return client.GetDatabase(settings.DatabaseName);
        });
    }
    
    private static void RegisterRepositories(IServiceCollection services)
    {
        services.AddCentroDeOperacaoCacheMongoRepository();
        services.AddUsinaCacheMongoRepository();
        services.AddConjuntoDeUsinaCacheMongoRepository();
        services.AddAreaEletricaCacheMongoRepository();
        services.AddAgenteCacheMongoRepository();
        services.AddEquipamentoCacheMongoRepository();
        services.AddEstacaoCacheMongoRepository();
        services.AddEloCacheMongoRepository();
        services.AddInstalacaoCacheMongoRepository();
        services.AddStageSyncLogCacheMongoRepository();

        services.AddScoped(typeof(IMongoRepository<>), typeof(MongoRepository<>))
            .Decorate(typeof(IMongoRepository<>), typeof(MongoCacheRepository<>));
        
        services.AddScoped<IEntrySyncRepository, EntrySyncRepository>();
    }

    private static void RegisterUnitsOfWork(IServiceCollection services)
    {
        services.AddScoped<IMongoUnitOfWork, MongoUnitOfWork>();
    }

    private static IServiceCollection AddCentroDeOperacaoCacheMongoRepository(this IServiceCollection services)
    {
        services.AddScoped<IMongoRepository<CentroDeOperacao>, CentroDeOperacaoRepository>()
            .Decorate<IMongoRepository<CentroDeOperacao>, CentroDeOperacaoCacheRepository>();

        services.AddScoped<ICentroDeOperacaoRepository, CentroDeOperacaoRepository>()
            .Decorate<ICentroDeOperacaoRepository, CentroDeOperacaoCacheRepository>();
                
        services.AddScoped<ISyncRepository<CentroDeOperacao>, CentroDeOperacaoRepository>()
            .Decorate<ISyncRepository<CentroDeOperacao>, CentroDeOperacaoCacheRepository>();

        return services;
    }

    private static IServiceCollection AddUsinaCacheMongoRepository(this IServiceCollection services)
    {
        services.AddScoped<IMongoRepository<Usina>, UsinaRepository>()
            .Decorate<IMongoRepository<Usina>, UsinaCacheRepository>();

        services.AddScoped<IUsinaRepository, UsinaRepository>()
            .Decorate<IUsinaRepository, UsinaCacheRepository>();

        services.AddScoped<ISyncRepository<Usina>, UsinaRepository>()
            .Decorate<ISyncRepository<Usina>, UsinaCacheRepository>();

        return services;
    }

    private static IServiceCollection AddConjuntoDeUsinaCacheMongoRepository(this IServiceCollection services)
    {
        services.AddScoped<IMongoRepository<ConjuntoDeUsina>, ConjuntoDeUsinaRepository>()
            .Decorate<IMongoRepository<ConjuntoDeUsina>, ConjuntoDeUsinaCacheRepository>();

        services.AddScoped<IConjuntoDeUsinaRepository, ConjuntoDeUsinaRepository>()
            .Decorate<IConjuntoDeUsinaRepository, ConjuntoDeUsinaCacheRepository>();

        services.AddScoped<ISyncRepository<ConjuntoDeUsina>, ConjuntoDeUsinaRepository>()
            .Decorate<ISyncRepository<ConjuntoDeUsina>, ConjuntoDeUsinaCacheRepository>();

        return services;
    }

    private static IServiceCollection AddAreaEletricaCacheMongoRepository(this IServiceCollection services)
    {
        services.AddScoped<IMongoRepository<AreaEletrica>, AreaEletricaRepository>()
            .Decorate<IMongoRepository<AreaEletrica>, AreaEletricaCacheRepository>();

        services.AddScoped<IAreaEletricaRepository, AreaEletricaRepository>()
            .Decorate<IAreaEletricaRepository, AreaEletricaCacheRepository>();

        services.AddScoped<ISyncRepository<AreaEletrica>, AreaEletricaRepository>()
            .Decorate<ISyncRepository<AreaEletrica>, AreaEletricaCacheRepository>();

        return services;
    }

    private static IServiceCollection AddAgenteCacheMongoRepository(this IServiceCollection services)
    {
        services.AddScoped<IMongoRepository<Agente>, AgenteRepository>()
            .Decorate<IMongoRepository<Agente>, AgenteCacheRepository>();

        services.AddScoped<IAgenteRepository, AgenteRepository>()
            .Decorate<IAgenteRepository, AgenteCacheRepository>();

        services.AddScoped<ISyncRepository<Agente>, AgenteRepository>()
            .Decorate<ISyncRepository<Agente>, AgenteCacheRepository>();

        return services;
    }

    private static IServiceCollection AddEquipamentoCacheMongoRepository(this IServiceCollection services)
    {
        services.AddScoped<IMongoRepository<Equipamento>, EquipamentoRepository>()
            .Decorate<IMongoRepository<Equipamento>, EquipamentoCacheRepository>();

        services.AddScoped<IEquipamentoRepository, EquipamentoRepository>()
            .Decorate<IEquipamentoRepository, EquipamentoCacheRepository>();

        services.AddScoped<ISyncRepository<Equipamento>, EquipamentoRepository>()
            .Decorate<ISyncRepository<Equipamento>, EquipamentoCacheRepository>();

        return services;
    }

    private static IServiceCollection AddEstacaoCacheMongoRepository(this IServiceCollection services)
    {
        services.AddScoped<IMongoRepository<Estacao>, EstacaoRepository>()
            .Decorate<IMongoRepository<Estacao>, EstacaoCacheRepository>();

        services.AddScoped<IEstacaoRepository, EstacaoRepository>()
            .Decorate<IEstacaoRepository, EstacaoCacheRepository>();

        services.AddScoped<ISyncRepository<Estacao>, EstacaoRepository>()
            .Decorate<ISyncRepository<Estacao>, EstacaoCacheRepository>();

        return services;
    }

    private static IServiceCollection AddEloCacheMongoRepository(this IServiceCollection services)
    {
        services.AddScoped<IMongoRepository<Elo>, EloRepository>()
            .Decorate<IMongoRepository<Elo>, EloCacheRepository>();

        services.AddScoped<IEloRepository, EloRepository>()
            .Decorate<IEloRepository, EloCacheRepository>();

        services.AddScoped<ISyncRepository<Elo>, EloRepository>()
            .Decorate<ISyncRepository<Elo>, EloCacheRepository>();

        return services;
    }

    private static IServiceCollection AddInstalacaoCacheMongoRepository(this IServiceCollection services)
    {
        services.AddScoped<IMongoRepository<Instalacao>, InstalacaoRepository>()
            .Decorate<IMongoRepository<Instalacao>, InstalacaoCacheRepository>();

        services.AddScoped<IInstalacaoRepository, InstalacaoRepository>()
            .Decorate<IInstalacaoRepository, InstalacaoCacheRepository>();

        services.AddScoped<ISyncRepository<Instalacao>, InstalacaoRepository>()
            .Decorate<ISyncRepository<Instalacao>, InstalacaoCacheRepository>();

        return services;
    }

    private static IServiceCollection AddStageSyncLogCacheMongoRepository(this IServiceCollection services)
    {
        services.AddScoped<IMongoRepository<StageSyncLog>, StageSyncLogRepository>()
            .Decorate<IMongoRepository<StageSyncLog>, StageSyncLogCacheRepository>();

        services.AddScoped<IStageSyncLogRepository, StageSyncLogRepository>()
            .Decorate<IStageSyncLogRepository, StageSyncLogCacheRepository>();

        return services;
    }

}