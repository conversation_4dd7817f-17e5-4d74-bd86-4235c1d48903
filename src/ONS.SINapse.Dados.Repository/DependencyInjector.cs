using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using ONS.SINapse.Dados.Repository.Base;
using ONS.SINapse.Dados.Repository.Data;
using ONS.SINapse.Dados.Repository.Dataset;
using ONS.SINapse.Dados.Repository.Factories;
using ONS.SINapse.Dados.Shared.Rsql;
using StackExchange.Redis;

namespace ONS.SINapse.Dados.Repository;

public static class DependencyInjector
{
    public static IServiceCollection RegisterRepositoryLayer(this IServiceCollection services,
        IConfiguration configuration)
    {
        RegisterSqlConnection(services, configuration);
        RegisterRedisConnection(services, configuration);
        RegisterRepositories(services);
        services.AddScoped<IRsqParse, RsqParseSqlServer>();
        return services;
    }
    
    private static void RegisterSqlConnection(IServiceCollection services, IConfiguration configuration)
    {
        string roadonlyConnectionString = configuration.GetConnectionString("SqlServerReadOnly")
                            ?? throw new InvalidOperationException("Connection string 'SqlServerReadOnly' not found.");
        string readWriteConnectionString = configuration.GetConnectionString("SqlServerConnection")
                            ?? throw new InvalidOperationException("Connection string 'SqlServerConnection' not found.");
        
        services.AddScoped<IReadOnlyDbConnection>( x => new ReadOnlyDbConnection(roadonlyConnectionString));
        services.AddScoped<IReadWriteDbConnection>( x => new ReadWriteDbConnection(readWriteConnectionString));
    }
    
    private static void RegisterRedisConnection(IServiceCollection services, IConfiguration configuration)
    {
        services.AddSingleton<IConnectionMultiplexer>(_ =>
        {
            var options = ConfigurationOptions.Parse(configuration.GetConnectionString("Redis") ?? string.Empty);
            options.ConnectRetry = 100;
            options.ConnectTimeout = 3000;
            options.AbortOnConnectFail = false;
            return ConnectionMultiplexer.Connect(options);
        });

        services.AddSingleton<IDatabase>(provider =>
        {
            IConnectionMultiplexer connection = provider.GetRequiredService<IConnectionMultiplexer>();
            return connection.GetDatabase();
        }); 
        services.AddSingleton<IServer>(provider =>
        {
            IConnectionMultiplexer connection = provider.GetRequiredService<IConnectionMultiplexer>();
            return connection.GetServer(connection.GetEndPoints().FirstOrDefault() 
                                        ?? throw new InvalidOperationException("Redis connection not found."));
        });
    }
    
    private static void RegisterRepositories(IServiceCollection services)
    {
        services.AddScoped<ISolicitacaoDatasetRepository, SolicitacaoDatasetRepository>();
        services.AddScoped<ICentroDeOpercaoRepository, CentroDeOperacaoRepository>();
        services.AddScoped<IQueryDatasetFactory, QueryDatasetFactory>();
        services.AddScoped<IDatasetViewsRepository, DatasetViewsRepository>();
        services.AddScoped<ILocalDeOperacaoDataRepository, LocalDeOperacaoDataRepository>();
        services.AddScoped<IAgenteDataRepository, AgenteDataRepository>();
        services.AddScoped<IAgenteAreaEletricaDataRepository, AgenteAreaEletricaDataRepository>();
    }
}
