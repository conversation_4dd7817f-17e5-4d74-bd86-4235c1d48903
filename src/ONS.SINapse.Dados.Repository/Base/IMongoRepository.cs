using System.Linq.Expressions;
using ONS.SINapse.Dados.Entities.Base;

namespace ONS.SINapse.Dados.Repository.Base;

public interface IMongoRepository<TDocument> : IDisposable
    where TDocument : Entidade
{
    Task<ICollection<TDocument>> GetAsync(Expression<Func<TDocument, bool>> predicate,
        CancellationToken cancellationToken);
    
    Task<ICollection<TDocument>> GetAsync(CancellationToken cancellationToken);

    Task<ICollection<TDocument>> GetAsync(Expression<Func<TDocument, bool>> predicate, IDictionary<string, bool> sortBy, CancellationToken cancellationToken);
    Task<TDocument?> GetOneAsync(Expression<Func<TDocument, bool>> predicate, CancellationToken cancellationToken);

    Task<TDocument> AddAsync(TDocument document, CancellationToken cancellationToken);

    Task UpdateAsync(TDocument document, CancellationToken cancellationToken);

    Task DeleteAsync(TDocument document, CancellationToken cancellationToken);
    
    Task<bool> DeleteAsync(string id, CancellationToken cancellationToken);

    Task BulkAddAsync(IEnumerable<TDocument> documents, CancellationToken cancellationToken);

    Task BulkUpdateAsync(IEnumerable<TDocument> documents, CancellationToken cancellationToken);

    Task DeleteManyAsync(Expression<Func<TDocument, bool>> predicate, CancellationToken cancellationToken);

    Task DeleteManyAsync(IEnumerable<TDocument> documents, CancellationToken cancellationToken);
}