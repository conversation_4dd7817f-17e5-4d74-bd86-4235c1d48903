using System.Data;
using Microsoft.Data.SqlClient;

namespace ONS.SINapse.Dados.Repository.Base;

public abstract class DataBaseConnection(string connectionString) : IDataBaseConnection
{
    public string ConnectionString => connectionString;

    public virtual IDbConnection GetConnection()
    {
       var connection = new SqlConnection(ConnectionString);
       connection.Open();
       return connection;
    }
}
