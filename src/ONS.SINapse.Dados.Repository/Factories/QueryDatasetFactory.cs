using ONS.SINapse.Dados.Shared.DTOs;
using ONS.SINapse.Dados.Shared.Rsql;

namespace ONS.SINapse.Dados.Repository.Factories;

public interface IQueryDatasetFactory
{
    QueryDataset Create(ConsultaDatasetDto consultaDatasetDto);
    QueryDataset Create(ValidacaoSqlDatasetDto validacaoSqlDatasetDto);
}
public sealed class QueryDatasetFactory(IRsqParse rsqParse) : IQueryDatasetFactory
{
    private const string SelectFull = """
        SELECT
            id                AS Id,
            definirstatus     AS DefinirStatus,
            label             AS Label,
            descricao         AS Descricao,
            valorpadrao       AS ValorPadrao,
            opcional          AS Opcional,
            origem_codigo     AS OrigemCodigo,
            origem_nome       AS OrigemNome,
            destino_codigo    AS DestinoCodigo,
            destino_nome      AS DestinoNome,
            local_codigo      AS LocalCodigo,
            local_nome        AS LocalNome,
            encaminhar_codigo AS EncaminharCodigo,
            encaminhar_nome   AS EncaminharNome,
            tipo              AS Tipo
        """;

    private static string GetSqlBase(string viewName, string whereClause) =>
        "FROM vw_" + viewName + "_dataset " + whereClause;

    public QueryDataset Create(ConsultaDatasetDto consultaDatasetDto)
    {
        RsqlQuery rsqlQuery = rsqParse.Parse(consultaDatasetDto.RsqlQuery);

        string sqlQuery = $"""
            {SelectFull}
            {GetSqlBase(consultaDatasetDto.ViewName, rsqlQuery.SqlWhere)}
        """;

        return new QueryDataset(
            SqlQuery: sqlQuery,
            Parameters: rsqlQuery.Parameters,
            MapFunction: MapToDatasetItemDto()
        );
    }

    public QueryDataset Create(ValidacaoSqlDatasetDto validacaoSqlDatasetDto)
    {
        RsqlQuery rsqlQuery = rsqParse.Parse(validacaoSqlDatasetDto.RsqlQuery);

        string cteQuery = ComposeCteQuery(validacaoSqlDatasetDto.ViewName, validacaoSqlDatasetDto.SqlQuery.Sql, rsqlQuery.SqlWhere);

        return new QueryDataset(
            SqlQuery: cteQuery,
            Parameters: rsqlQuery.Parameters,
            MapFunction: MapToDatasetItemDto()
        );
    }

    private static string ComposeCteQuery(string viewName, string cteSqlQuery, string whereClause)
    {
        return $"""
            WITH vw_{viewName}_dataset_cte AS (
                {cteSqlQuery}
            )
            {SelectFull}
            FROM vw_{viewName}_dataset_cte {whereClause}
        """;
    }

    private static Func<DatasetViewSolicitacaoDto, DatasetItemDto> MapToDatasetItemDto() => dataset =>
        new DatasetItemDto(
            id: dataset.Id,
            message: new MessageDto(
                dataset.Id,
                dataset.Descricao,
                dataset.Label,
                dataset.Opcional,
                dataset.ValorPadrao
            ),
            origin: new ManeuverObjectDto(dataset.OrigemCodigo, dataset.OrigemNome),
            destination: new ManeuverObjectDto(dataset.DestinoCodigo, dataset.DestinoNome),
            local: ManeuverObjectDto.FromCodeAndName(dataset.LocalCodigo, dataset.LocalNome),
            encaminharPara: ManeuverObjectDto.FromCodeAndName(dataset.EncaminharCodigo, dataset.EncaminharNome),
            definirStatus: dataset.DefinirStatus
        );
}

