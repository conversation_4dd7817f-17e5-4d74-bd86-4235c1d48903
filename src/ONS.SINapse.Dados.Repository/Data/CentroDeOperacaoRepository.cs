using Dapper;
using ONS.SINapse.Dados.Repository.Base;
using ONS.SINapse.Dados.Shared.DTOs;

namespace ONS.SINapse.Dados.Repository.Data;

public class CentroDeOperacaoRepository(IReadOnlyDbConnection dbConnection) : ICentroDeOpercaoRepository
{
    public async Task<IEnumerable<CentroDeOperacaoDto>> ObterCentrosAtivosAsync(CancellationToken cancellationToken)
    {
        const string sql = """
                            SELECT TRIM(cos_id) AS codigo, TRIM(nomecurto) AS nome
                                from cos AS C
                                where C<PERSON>dtentrada IS NOT NULL
                            AND (C.dtdesativa IS NULL OR C.dtdesativa > GETDATE())
                           """;

        return await dbConnection.GetConnection()
            .QueryAsync<CentroDeOperacaoDto>(sql)
            .WaitAsync(cancellationToken)
            .ConfigureAwait(false);
    }
}
