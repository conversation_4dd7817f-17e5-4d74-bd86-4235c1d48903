using System.Text;
using Dapper;
using ONS.SINapse.Dados.Repository.Base;
using ONS.SINapse.Dados.Repository.Exceptions;
using ONS.SINapse.Dados.Shared.DTOs;
using ONS.SINapse.Dados.Shared.Rsql;

namespace ONS.SINapse.Dados.Repository.Data;

public class LocalDeOperacaoDataRepository(IReadOnlyDbConnection dbConnection) : ILocalDeOperacaoDataRepository
{
    public async Task<List<LocaDeOperacaoDataDto>> ObterAsync(RsqlQuery rsqlQuery,
        CancellationToken cancellationToken)
    {
        string sql = await SqlBuilder(rsqlQuery, cancellationToken);

        IEnumerable<LocaDeOperacaoDataDto> result = await dbConnection.GetConnection().QueryAsync<LocaDeOperacaoDataDto>(
            sql: sql,
            param: rsqlQuery.Parameters
        ).WaitAsync(cancellationToken).ConfigureAwait(false);

        return result.ToList();
    }

    private async Task<string> SqlBuilder(RsqlQuery rsqlQuery, CancellationToken cancellationToken)
    {
        var viewNames = (await dbConnection.GetConnection()
                .QueryAsync<string>("SELECT view_name FROM vw_dataset_views")
                .WaitAsync(cancellationToken)
                .ConfigureAwait(false))
            .ToList();

        if (!viewNames.Any())
        {
            throw new DatasetViewNotFoundException("Nenhuma view de dataset encontrada.");
        }

        var sb = new StringBuilder();
        string whereClause = string.IsNullOrEmpty(rsqlQuery.SqlWhere) ? 
            "WHERE local_codigo IS NOT NULL AND local_nome IS NOT NULL" : 
            rsqlQuery.SqlWhere + " AND local_codigo IS NOT NULL AND local_nome IS NOT NULL";
        IEnumerable<string> selects = viewNames.Select(view =>
            $"""
             SELECT DISTINCT CAST(local_codigo AS NVARCHAR)    AS LocalCodigo,
                             CAST(local_nome as NVARCHAR)      AS LocalNome,
                             CAST(origem_codigo as NVARCHAR)   AS OrigemCodigo,
                             CAST(destino_codigo as NVARCHAR)    AS DestinoCodigo,
                             CAST(destino_nome as NVARCHAR)    AS DestinoNome,
                             CAST(tipo as NVARCHAR)            AS Id,
                             CAST(dataset_label as NVARCHAR)   AS Label
             FROM {view}
             {whereClause}
             """
        );
        sb.AppendLine(string.Join(" \n UNION ALL \n ", selects));
        string cteSqlQuery = sb.ToString();

        return $"""
                    WITH dataset_cte AS (
                        {cteSqlQuery}
                    )
                    SELECT DISTINCT LocalCodigo, LocalNome, OrigemCodigo, DestinoCodigo, DestinoNome, Id, Label
                    FROM dataset_cte
                    ORDER BY Label, LocalNome
                """;
    }
}
