using MongoDB.Bson;
using MongoDB.Driver;
using ONS.SINapse.Dados.Entities;
using ONS.SINapse.Dados.Repository.Base;
using ONS.SINapse.Dados.Repository.Context;
using ONS.SINapse.Dados.Repository.Pipelines;

namespace ONS.SINapse.Dados.Repository.Repositories;

public interface IAreaEletricaRepository : ISyncRepository<AreaEletrica>;

public class AreaEletricaRepository(IMongoConnection mongoConnection)
    : MongoRepository<AreaEletrica>(mongoConnection), IAreaEletricaRepository
{
    public async Task<IEnumerable<AreaEletrica>> ObterDocumentosSincronizaveisAsync(CancellationToken cancellationToken)
    {
        var collection = Database.GetCollection<BsonDocument>("c_stg_areareg");
        var pipeline = AreaEletricaPipeline.AreasEletricasPipeline();
        var areaEletricaCursor = await collection.AggregateAsync<AreaEletrica>(pipeline, cancellationToken: cancellationToken);
        return await areaEletricaCursor.ToListAsync(cancellationToken);
    }
}

public class AreaEletricaCacheRepository : MongoCacheRepository<AreaEletrica>, IAreaEletricaRepository
{
    private readonly IAreaEletricaRepository _repository;

    public AreaEletricaCacheRepository(IRedisContext redisContext, IAreaEletricaRepository repository) : base(repository, redisContext)
    {
        _repository = repository;
    }

    public Task<IEnumerable<AreaEletrica>> ObterDocumentosSincronizaveisAsync(CancellationToken cancellationToken)
    {
        return _repository.ObterDocumentosSincronizaveisAsync(cancellationToken);
    }
}