using AutoMapper;
using MongoDB.Bson;
using MongoDB.Driver;
using ONS.SINapse.Dados.Entities;
using ONS.SINapse.Dados.Entities.Staging;
using ONS.SINapse.Dados.Repository.Context;
using ONS.SINapse.Dados.Shared.Extensions;

namespace ONS.SINapse.Dados.Repository.Repositories.Staging;

public interface IEntrySyncRepository : IDisposable
{
    Task AtualizarAsync(EntrySyncDataTable dado, CancellationToken cancellationToken);
}

public class EntrySyncRepository : IEntrySyncRepository
{
    private readonly IMongoDatabase _mongoDatabase;
    private readonly IMapper _mapper;
    
    private const string DeleteOperation = "Exclusao";

    public EntrySyncRepository(IMongoConnection mongoConnection, IMapper mapper)
    {
        _mongoDatabase = mongoConnection.GetDatabase();
        _mapper = mapper;
    }

    public Task AtualizarAsync(EntrySyncDataTable dado, CancellationToken cancellationToken)
    {
        var task = dado.Acao switch
        {
            DeleteOperation => DeleteAsync(dado, cancellationToken),
            _ => UpdateAsync(dado, cancellationToken)
        };
        
        return task;
    }

    private async Task DeleteAsync(EntrySyncDataTable dado, CancellationToken cancellationToken)
    {
        var collectionName = dado.TipoObjeto.ToLower();
        
        if(!await CollectionExist(collectionName, cancellationToken).ConfigureAwait(false))
            return;
        
        var collection = _mongoDatabase.GetCollection<BsonDocument>(collectionName);
        
        var bulkOps = new List<WriteModel<BsonDocument>>();
        
        dado.Dados.ForAll(o =>
        {
            var filter = ObterFiltro(o.Chaves);
            var deleteOne = new DeleteOneModel<BsonDocument>(filter);
            bulkOps.Add(deleteOne);
        });
 
        if (bulkOps.Count != 0)
        {
            await collection.BulkWriteAsync(bulkOps, new BulkWriteOptions(),cancellationToken).ConfigureAwait(false);
        }
    }

    private async Task UpdateAsync(EntrySyncDataTable dado, CancellationToken cancellationToken)
    {
        var collectionName = dado.TipoObjeto.ToLower();

        var chaves = dado.Dados
            .SelectMany(x => x.Chaves)
            .Select(x => x.Nome)
            .Distinct()
            .ToArray();
        
        var collection = await CreateCollectionIfNotExists(collectionName, chaves, cancellationToken).ConfigureAwait(false);
        
        var bulkOps = new List<WriteModel<BsonDocument>>();

        dado.Dados.ForAll(o =>
        {
            var listChaves = o.Chaves.ToArray();
            var filter = ObterFiltro(listChaves);
            var update = ObterDocumento(listChaves, o.Propriedades.ToArray());
            var replaceOne = new ReplaceOneModel<BsonDocument>(filter, update)
            {
                IsUpsert = true
            };
            bulkOps.Add(replaceOne);
        });

        if (bulkOps.Count != 0)
        {
            await collection.BulkWriteAsync(bulkOps, new BulkWriteOptions(),cancellationToken).ConfigureAwait(false);
        }
    }

    private async Task<IMongoCollection<BsonDocument>> CreateCollectionIfNotExists(string collection, string[] chaves, CancellationToken cancellationToken)
    {
        if (await CollectionExist(collection, cancellationToken).ConfigureAwait(false))
            return _mongoDatabase.GetCollection<BsonDocument>(collection);

        await _mongoDatabase.CreateCollectionAsync(collection, new CreateCollectionOptions(), cancellationToken)
            .ConfigureAwait(false);

        var mongoCollection = _mongoDatabase.GetCollection<BsonDocument>(collection);
        
        await CreateIndexesAsync(mongoCollection, chaves).ConfigureAwait(false);
        
        return mongoCollection;
    }
    
    private static async Task CreateIndexesAsync(IMongoCollection<BsonDocument> collection, string[] chaves)
    {
        var indexKeysDef = Builders<BsonDocument>.IndexKeys;
        IndexKeysDefinition<BsonDocument>? indexKeys = null;

        chaves.ForAll(key =>
        {
            indexKeys = indexKeys == null ? indexKeysDef.Ascending(key) : indexKeys.Ascending(key);
        });

        if (indexKeys == null) return;
    
        var indexModel = new CreateIndexModel<BsonDocument>(indexKeys);
    
        await collection.Indexes.CreateOneAsync(indexModel).ConfigureAwait(false);
    }
    
    private async Task<bool> CollectionExist(string collection, CancellationToken cancellationToken)
    {
        var cursor = await _mongoDatabase.ListCollectionNamesAsync(new ListCollectionNamesOptions
        {
            Filter = new BsonDocumentFilterDefinition<BsonDocument>(new BsonDocument("name", collection))
        }, cancellationToken).ConfigureAwait(false);

        return await cursor.AnyAsync(cancellationToken);
    }
    
    private BsonDocument ObterDocumento(SyncDatabaseProperty[] chaves, SyncDatabaseProperty[] propriedades)
    {
        var elementValues = (SyncDatabaseProperty[]) [..chaves, ..propriedades];
        var elements = _mapper.Map<List<BsonElement>>(elementValues);
        return new BsonDocument(elements);
    }
    
    private static FilterDefinition<BsonDocument> ObterFiltro(IEnumerable<SyncDatabaseProperty> chaves)
    {
        var filters = chaves
            .Select(pk => Builders<BsonDocument>.Filter.Eq(pk.Nome, pk.Valor))
            .ToList();
    
        return Builders<BsonDocument>.Filter.And(filters);
    }

    private bool _disposed;
    protected virtual void Dispose(bool disposing)
    {
        if (_disposed) return;

        _disposed = true;
    }
    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }
}