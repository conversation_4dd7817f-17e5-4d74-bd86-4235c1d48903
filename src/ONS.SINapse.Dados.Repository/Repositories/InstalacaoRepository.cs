using MongoDB.Bson;
using MongoDB.Driver;
using ONS.SINapse.Dados.Entities;
using ONS.SINapse.Dados.Repository.Base;
using ONS.SINapse.Dados.Repository.Context;
using ONS.SINapse.Dados.Repository.Pipelines;

namespace ONS.SINapse.Dados.Repository.Repositories;


public interface IInstalacaoRepository : ISyncRepository<Instalacao>
{

}

public class InstalacaoRepository(IMongoConnection mongoConnection)
    : MongoRepository<Instalacao>(mongoConnection), IInstalacaoRepository
{
    public async Task<IEnumerable<Instalacao>> ObterDocumentosSincronizaveisAsync(CancellationToken cancellationToken)
    {
        var collection = Database.GetCollection<BsonDocument>("c_stg_ins");

        var cursor = await collection.AggregateAsync<Instalacao>(
            InstalacaoPipeline.InstalacoesPipeline(), cancellationToken: cancellationToken);
        
        return await cursor.ToListAsync(cancellationToken);
    }
}

public class InstalacaoCacheRepository : MongoCacheRepository<Instalacao>, IInstalacaoRepository
{
    private readonly IInstalacaoRepository _repository;

    public InstalacaoCacheRepository(IInstalacaoRepository repository, IRedisContext redisContext) : base(repository, redisContext)
    {
        _repository = repository;
    }

    public Task<IEnumerable<Instalacao>> ObterDocumentosSincronizaveisAsync(CancellationToken cancellationToken) 
        => _repository.ObterDocumentosSincronizaveisAsync(cancellationToken);
}