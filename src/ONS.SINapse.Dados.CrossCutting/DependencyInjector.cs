using System.Globalization;
using System.IO.Compression;
using System.Security.Cryptography;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Localization;
using Microsoft.AspNetCore.Mvc.Infrastructure;
using Microsoft.AspNetCore.ResponseCompression;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.IdentityModel.Tokens;
using ONS.SINapse.Dados.Business;
using ONS.SINapse.Dados.Repository;
using ONS.SINapse.Dados.Shared;
using ONS.SINapse.Dados.Shared.Identity;
using ONS.SINapse.Dados.SyncData;

namespace ONS.SINapse.Dados.CrossCutting;

public static class DependencyInjector
{
    public static void Register(IServiceCollection services, IConfiguration configuration)
    {
        services
            .RegisterServices()
            .RegisterSharedLayer(configuration)
            .ConfigureLocalizationOptions()
            .ConfigureCompression()
            .RegisterContextAccessor()
            .RegisterCredencials(configuration)
            .RegisterRepositoryLayer(configuration)
            .RegisterBusinessLayer()
            .RegisterSyncDataLayer()
            .RegisterAutoMapper();
    }
    
    private static IServiceCollection RegisterServices(this IServiceCollection services)
    {
        services.AddHttpClient();
        return services;
    }
    
    private static IServiceCollection RegisterCredencials(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddSinapseAuthorization(configuration);

        return services;
    }

    private static IServiceCollection RegisterContextAccessor(this IServiceCollection services)
    {
        services.AddHttpContextAccessor();
        services.AddSingleton<IHttpContextAccessor, HttpContextAccessor>();
        services.AddSingleton<IActionContextAccessor, ActionContextAccessor>();

        return services;
    }

    private static IServiceCollection ConfigureLocalizationOptions(this IServiceCollection services)
    {
        services.Configure<RequestLocalizationOptions>(options =>
        {
            var supportedCultures = new List<CultureInfo>
            {
                new ("pt-BR")
            };

            options.DefaultRequestCulture = new RequestCulture("pt-BR");
            options.SupportedCultures = supportedCultures;
            options.SupportedUICultures = supportedCultures;
        });

        return services;
    }

    private static IServiceCollection ConfigureCompression(this IServiceCollection services)
    {
        services.Configure<GzipCompressionProviderOptions>(options => options.Level = CompressionLevel.Optimal);
        services.AddResponseCompression(options =>
        {
            options.Providers.Add<GzipCompressionProvider>();
        });

        return services;
    }
}

public static class AuthorizationConfiguration
{
    public static void AddSinapseAuthorization(this IServiceCollection services, IConfiguration configuration)
    {
        var jwtConfiguration = configuration.GetSection("ONS:Authorization");

        services.Configure<AuthorizationServiceSettings>(jwtConfiguration);
        var jwtTokenSettings = jwtConfiguration.Get<AuthorizationServiceSettings>();

        var authorizationOptions = new AuthorizationServiceOptions
        {
            Issuer = jwtTokenSettings.Issuer,
            Audiences = new[] { jwtTokenSettings.Audience },
            UseRsa = jwtTokenSettings.UseRsa,
            RsaPublicKeyExponent64 = jwtTokenSettings.RsaPublicExponent,
            RsaPublicKeyModulus64 = jwtTokenSettings.RsaModulus
        };
        
        var tokenValidationParameters = BuildTokenValidationParameters(authorizationOptions);
        services
            .AddAuthentication("Bearer")
            .AddJwtBearer(optionsToken =>
            {
                optionsToken.TokenValidationParameters = tokenValidationParameters;
                optionsToken.Events = new JwtBearerEvents
                {
                    OnMessageReceived = ValidarTokenEnviadoNaQueryStringAsync
                };
            });
        
        services.AddSingleton<IAuthorizationHandler, ClaimAuthorizeHandler>();
        
        services.AddScoped<IUserContext, UserContext>();
    }
    
    /// <summary>
    /// Usado para validar o token mesmo vindo da query na requisição http, muito comum em conexão via websocket
    /// </summary>
    /// <param name="context">Mensagem de um evento jwt</param>
    private static Task ValidarTokenEnviadoNaQueryStringAsync(MessageReceivedContext context)
    {
        var accessToken = context.Request.Query["access_token"];
                        
        if(!string.IsNullOrEmpty(context.Token)) return Task.CompletedTask;
                        
        if (!string.IsNullOrEmpty(accessToken))
        {
            context.Token = accessToken;
        }
        return Task.CompletedTask;
    }
    
    private static SecurityKey BuildSecurityKey(AuthorizationServiceOptions options)
    {
        SecurityKey securityKey;
        if (options.UseRsa)
        {
           var numArray1 = Convert.FromBase64String(options.RsaPublicKeyModulus64);
            var numArray2 = Convert.FromBase64String(options.RsaPublicKeyExponent64);
            var rsa = RSA.Create();
            rsa.ImportParameters(new RSAParameters()
            {
                Modulus = numArray1,
                Exponent = numArray2
            });
            securityKey = new RsaSecurityKey(rsa);
        }
        else
            securityKey = new SymmetricSecurityKey(Convert.FromBase64String(options.Secret));
        return securityKey;
    }

    private static TokenValidationParameters BuildTokenValidationParameters(
        AuthorizationServiceOptions options)
    {
        var securityKey = BuildSecurityKey(options);
        return new TokenValidationParameters()
        {
            ClockSkew = TimeSpan.Zero,
            ValidIssuer = options.Issuer,
            ValidAudiences = options.Audiences,
            IssuerSigningKey = securityKey
        };
    }
}