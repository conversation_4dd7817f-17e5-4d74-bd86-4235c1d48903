namespace ONS.SINapse.Dados.Business.Dtos.Dataset;

public record DatasetItemDto
{
    public string Id { get; private set; }
    public ManeuverObjectDto Destination { get; private set; }
    public string Description { get; private set; }
    public string Label { get; private set; }
    public string Optional { get; private set; }
    public bool DefaultValue { get; private set; }
    public ManeuverObjectDto? Local { get; private set; }
    public ManeuverObjectDto? EncaminharPara { get; private set; }
    public string? DefinirStatus { get; private set; }

    public DatasetItemDto(string id, ManeuverObjectDto destination, string description, string label)
    {
        Id = id;
        Destination = destination;
        Description = description;
        Label = label;
        Optional = string.Empty;
    }
    
    public DatasetItemDto(string id, ManeuverObjectDto destination, string description, string label, ManeuverObjectDto local, ManeuverObjectDto? encaminharPara, string? definirStatus)
        : this(id, destination, description, label)
    {
        Local = local;
        EncaminharPara = encaminharPara;
        DefinirStatus = definirStatus;
    }
    
    public DatasetItemDto(string id, ManeuverObjectDto destination, string description, string label, bool defaultValue)
        : this(id, destination, description, label)
    {
        DefaultValue = defaultValue;
    }
}