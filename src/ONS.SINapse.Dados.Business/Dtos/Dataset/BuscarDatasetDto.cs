using ONS.SINapse.Dados.Entities;

namespace ONS.SINapse.Dados.Business.Dtos.Dataset
{
    public class BuscarDatasetDto
    {
        public string[] Tipos { get; private set; }
        public DescricaoDatasetDto DescricaoDataset{ get; private set; }
        public DadosDatasetDto DadosDataset { get; private set; }
        public Dictionary<string, string>? Centros { get; private set; }

        public BuscarDatasetDto(
            string[] tipos,
            DescricaoDatasetDto descricaoDataset,
            DadosDatasetDto dadosDataset,
            Dictionary<string, string>? centros = null)
        {
            Tipos = tipos;
            DescricaoDataset = descricaoDataset;
            DadosDataset = dadosDataset;
            Centros = centros;
        }
    }

    public class DadosDatasetDto
    {
        public IEnumerable<Equipamento> Equipamentos { get; private set; }
        public ICollection<Agente> Agentes { get; private set; }
        public ICollection<Estacao> Estacoes { get; private set; }

        public DadosDatasetDto(
            IEnumerable<Equipamento> equipamentos, ICollection<Agente> agentes, ICollection<Estacao> estacoes)
        {
            Equipamentos = equipamentos;
            Agentes = agentes;
            Estacoes = estacoes;
        }
    }

    public class DescricaoDatasetDto
    {
        public string Identificador { get; private set; }
        public string Descricao { get; private set; }
        public Func<Equipamento, string> SeletorNome { get; private set; }

        public DescricaoDatasetDto(
            string identificador, 
            string descricao, 
            Func<Equipamento, string> seletorNome)
        {
            Identificador = identificador;
            Descricao = descricao;
            SeletorNome = seletorNome;
        }
    }
}
