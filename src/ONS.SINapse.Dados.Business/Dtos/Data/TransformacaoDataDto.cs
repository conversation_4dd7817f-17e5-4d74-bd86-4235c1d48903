namespace ONS.SINapse.Dados.Business.Dtos.Data
{
    public class TransformacaoDataDto
    {
        public string NomeDes { get; private set; } = string.Empty;
        public string IdoOns { get; private set; } = string.Empty;
        public string NomeAgente { get; private set; } = string.Empty;
        public string CodigoAgente { get; private set; }
        public string CentroRegional { get; private set; } = string.Empty;
        public int Vn { get; private set; }
        public int Vn1 { get; private set; }
        public int Vn2 { get; private set; }
        public string CodigoLocal => $"{IdoOns}_TR{Vn}_{CentroRegional}_{CodigoAgente}_{Vn1}_{Vn2}";
        public string DescricaoLocal => $"{NomeDes} {Vn1}kV/{Vn2}kV - Lado de {Vn}kV";

        public TransformacaoDataDto(string nomeDes, string idoOns, string nomeAgente, string codigoAgente, string centroRegional, int vn, int vn1, int vn2)
        {
            NomeDes = nomeDes;
            IdoOns = idoOns;
            NomeAgente = nomeAgente;
            CodigoAgente = codigoAgente;
            CentroRegional = centroRegional;
            Vn = vn;
            Vn1 = vn1;
            Vn2 = vn2;
        }
    }
}
