using MongoDB.Bson.Serialization.Attributes;
using Newtonsoft.Json;
using ONS.SINapse.Dados.Entities;
using ONS.SINapse.Dados.Shared.CustomAttributes;

namespace ONS.SINapse.Dados.Business.Dtos.EspelhoTabelaQuente;

[BsonCollection("c_agente")]
[Cache<PERSON>ey("agente")]
public class EspelhoAgenteDto : EspelhoEntidadeDto
{
    public EspelhoAgenteDto(
        string id,
        string codigo,
        string nome,
        ICollection<string> centros,
        ICollection<string> areaEletricas,
        List<EspelhoCentroDoAgente> centrosDoAgente,
        bool validarGeolocalizacao)
    {
        Id = id;
        Codigo = codigo;
        Nome = nome;
        Centros = centros;
        AreasEletricas = areaEletricas;
        CentrosDoAgente = centrosDoAgente;
        ValidarGeolocalizacao = validarGeolocalizacao;
    }

    [BsonElement("id_agente")]
    [System.Text.Json.Serialization.JsonIgnore]
    public override string Id { get; set; }

    [BsonElement("cod_agente")]
    public string Codigo { get; set; }

    [BsonElement("nom_curto")]
    [JsonProperty("NomeCurto")]
    public string Nome { get; set; }

    [BsonElement("list_centros")]
    public ICollection<string> Centros { get; set; }
    
    [BsonElement("list_areaeletricas")]
    [JsonProperty("AreaEletricas")]
    public ICollection<string> AreasEletricas { get; set; }

    [BsonElement("list_centroagente")]
    public List<EspelhoCentroDoAgente> CentrosDoAgente { get; private init; }

    [BsonElement("flg_validargeolocalizacao")]
    public bool ValidarGeolocalizacao { get; set; }

}

[BsonNoId]
public class EspelhoCentroDoAgente
{
    public EspelhoCentroDoAgente(string nome, double latitude, double longitude)
    {
        Nome = nome;
        Latitude = latitude;
        Longitude = longitude;
    }

    [BsonElement("nom_centroagente")]
    public string Nome { get; set; }

    [BsonElement("lat_centroagente")]
    public double Latitude { get; set; }

    [BsonElement("lng_centroagente")]
    public double Longitude { get; set; }
}