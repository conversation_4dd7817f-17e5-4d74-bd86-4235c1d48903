using ONS.SINapse.Dados.Business.Erros;
using ONS.SINapse.Dados.Business.Interfaces;
using ONS.SINapse.Dados.Repository.Data;
using ONS.SINapse.Dados.Shared.DTOs;
using ONS.SINapse.Dados.Shared.Result;
using ONS.SINapse.Dados.Shared.Rsql;

namespace ONS.SINapse.Dados.Business.Implementations;

public class AgenteAreaEletricaDataBusiness (IAgenteAreaEletricaDataRepository repository) : IAgenteAreaEletricaDataBusiness
{
    public async Task<Result<List<AgenteAreaEletricaDataDto>>> ObterAsync(RsqlQuery query, CancellationToken cancellationToken)
    {
        try
        {
            List<AgenteAreaEletricaViewDataDto> agenteAreaEletrica = await repository.ObterAsync(query, cancellationToken);
            
            if (!agenteAreaEletrica.Any())
            {
                return Result.Failure<List<AgenteAreaEletricaDataDto>>(DefaultErros.EmptyResult());
            }
            
            var agentesDto = agenteAreaEletrica
                .GroupBy(x => x.AreaCodigo)
                .Select(g => new AgenteAreaEletricaDataDto(
                    Codigo: g.Key,
                    Nome: g.First().AreaNome,
                    CodigoDoCentroDeOperacao: g.First().CentroCodigo,
                    Agentes: g.Select(x => new AgenteDto(x.AgenteCodigo, x.AgenteNome)).ToList()
                )).ToList();
            
            return Result.Success(agentesDto);
        } catch (Exception e)
        {
            return Result.Failure<List<AgenteAreaEletricaDataDto>>(ExceptionErros.Exception(e));
        }
    }
}
