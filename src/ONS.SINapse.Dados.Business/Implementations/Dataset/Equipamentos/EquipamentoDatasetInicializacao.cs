using Microsoft.Extensions.DependencyInjection;
using ONS.SINapse.Dados.Business.Interfaces.Dataset;
using ONS.SINapse.Dados.Business.Queries.Datasets;
using ONS.SINapse.Dados.Entities;
using ONS.SINapse.Dados.Repository.Base;

namespace ONS.SINapse.Dados.Business.Implementations.Dataset.Equipamentos;

public partial class EquipamentoDatasetBusiness : IEquipamentoDatasetBusiness
{
    private readonly IMongoRepository<Equipamento> _equipamentoRepository;
    private readonly IMongoRepository<Instalacao> _instalacaoRepository;
    private readonly IMongoRepository<Estacao> _estacaoRepository;
    private readonly IMongoRepository<Agente> _agenteRepository;
    private readonly IMongoRepository<Elo> _eloRepository;
    private readonly IMongoRepository<CentroDeOperacao> _centroDeOperacaoRepository;
    private readonly ITransformacaoDatasetQuery _transformacaoDatasetQuery;
    private readonly IInstalacaoCompensadorDatasetQuery _instalacaoCompensadorDatasetQuery;
    
    public EquipamentoDatasetBusiness(IServiceProvider serviceProvider)
    {
        _equipamentoRepository = serviceProvider.GetRequiredService<IMongoRepository<Equipamento>>();
        _instalacaoRepository = serviceProvider.GetRequiredService<IMongoRepository<Instalacao>>();
        _estacaoRepository = serviceProvider.GetRequiredService<IMongoRepository<Estacao>>();
        _agenteRepository = serviceProvider.GetRequiredService<IMongoRepository<Agente>>();
        _eloRepository = serviceProvider.GetRequiredService<IMongoRepository<Elo>>();
        _centroDeOperacaoRepository = serviceProvider.GetRequiredService<IMongoRepository<CentroDeOperacao>>();
        _transformacaoDatasetQuery = serviceProvider.GetRequiredService<ITransformacaoDatasetQuery>();
        _instalacaoCompensadorDatasetQuery = serviceProvider.GetRequiredService<IInstalacaoCompensadorDatasetQuery>();
    }
    
    
    private bool _disposed;
    protected virtual void Dispose(bool disposing)
    {
        if (_disposed) return;

        if (disposing)
        {
            _equipamentoRepository.Dispose();
            _instalacaoRepository.Dispose();
            _estacaoRepository.Dispose();
            _agenteRepository.Dispose();
            _eloRepository.Dispose();
            _centroDeOperacaoRepository.Dispose();
            _instalacaoCompensadorDatasetQuery.Dispose();
            _transformacaoDatasetQuery.Dispose();
        }

        _disposed = true;
    }
    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }
}