using System.Linq.Expressions;
using System.Text.RegularExpressions;
using ONS.SINapse.Dados.Business.Dtos.Dataset;
using ONS.SINapse.Dados.Business.Interfaces.Dataset;
using ONS.SINapse.Dados.Entities;
using ONS.SINapse.Dados.Repository.Repositories;
using ONS.SINapse.Dados.Shared.Notifications;
using ONS.SINapse.Dados.Shared.Helpers;

namespace ONS.SINapse.Dados.Business.Implementations.Dataset;

public partial class UsinaConjuntoUsinaDatasetBusiness : IUsinaConjuntoUsinaDatasetBusiness
{
    private readonly NotificationContext _notificationContext;
    private readonly ICentroDeOperacaoRepository _centroDeOperacaoRepository;
    private readonly IUsinaRepository _usinaRepository;
    private readonly IConjuntoDeUsinaRepository _conjuntoDeUsinaRepository;

    public UsinaConjuntoUsinaDatasetBusiness(
        NotificationContext notificationContext,
        ICentroDeOperacaoRepository centroDeOperacaoRepository,
        IUsinaRepository usinaRepository,
        IConjuntoDeUsinaRepository conjuntoDeUsinaRepository)
    {
        _notificationContext = notificationContext;
        _centroDeOperacaoRepository = centroDeOperacaoRepository;
        _usinaRepository = usinaRepository;
        _conjuntoDeUsinaRepository = conjuntoDeUsinaRepository;
    }
    
    public async Task<IEnumerable<DatasetItemDto>> ObterUsinaAsync(string codigoDoCentro, string[]? codigoDoTipoDeFonte, bool? cag = null, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrEmpty(codigoDoCentro))
        {
            _notificationContext.AddNotification("Centro de operação precisa ser preenchido.");
            return [];
        }
        
        var centros = await _centroDeOperacaoRepository.GetAsync(cancellationToken);
        var centro = centros.FirstOrDefault(x => x.CodigoOns == codigoDoCentro);
        
        if (centro is null)
        {
            _notificationContext.AddNotification("Centro de operação não encontrado.");
            return [];
        }

        Expression<Func<Usina, bool>> filter = usina =>
            (usina.DataDeDesativacao == null || usina.DataDeDesativacao > DateTime.Now) &&
            (usina.DataDeEntrada != null) &&
            (centro.IsCnos || usina.CodigoDoCentroDeOperacao == codigoDoCentro) &&
            (codigoDoTipoDeFonte == null || codigoDoTipoDeFonte.Contains(usina.CodigoDoTipoDeFonte)) &&
            (cag == null || usina.OperaPeloCAG == cag);

        var usinas = await _usinaRepository.GetAsync(filter, cancellationToken);

        return usinas
            .Select(u => new DatasetItemDto(
                u.UsiId,
                MontarDestination(centros, centro, u),
                $"{u.Descricao}",
                MontarLabel(centro, centros, u),
                new ManeuverObjectDto(u.UsiId, u.Descricao ?? u.NomeCurto),
                RetornarDadosAgente(centro, u),
                u.AgeId))
            .OrderBy(item => item.Label, NaturalSortHelper.Instance)
            .DistinctBy(item => item.Id);
    }

    public async Task<IEnumerable<DatasetItemDto>> ObterUgesAsync(string codigoDoCentro, CancellationToken cancellationToken)
    {
        if (string.IsNullOrEmpty(codigoDoCentro))
        {
            _notificationContext.AddNotification("Centro de operação precisa ser preenchido.");
            return [];
        }
        
        var centros = await _centroDeOperacaoRepository.GetAsync(cancellationToken);
        var centro = centros.FirstOrDefault(x => x.CodigoOns == codigoDoCentro);
        
        if (centro is null)
        {
            _notificationContext.AddNotification("Centro de operação não encontrado.");
            return [];
        }

        Expression<Func<Usina, bool>> filter = usina =>
            (usina.DataDeDesativacao == null || usina.DataDeDesativacao > DateTime.Now) &&
            (usina.DataDeEntrada != null) &&
            usina.CodigoDoTipoDeFonte == "UHE" &&
            usina.Uges.Any() &&
            (centro.IsCnos || usina.CodigoDoCentroDeOperacao == codigoDoCentro);

        var usinas = await _usinaRepository.GetAsync(filter, cancellationToken);

        return usinas
            .OrderBy(u => u.CodigoDoCentroDeOperacao)
            .ThenBy(u => u.Descricao)
            .SelectMany(usina =>
                usina.Uges
                    .Where(uge => !string.IsNullOrEmpty(uge.NumeroOperacional))
                    .OrderBy(uge => TrimStringRegex().Replace(uge.NumeroOperacional, " ").Trim(), NaturalSortHelper.Instance)
                    .Select(uge => new DatasetItemDto(
                        uge.UgeId!,
                        MontarDestination(centros, centro, usina),
                        $"UG {uge.NumeroOperacional} - {usina.Descricao}",
                        MontarLabel(centro, centros, usina, uge),
                        new ManeuverObjectDto(usina.UsiId, usina.Descricao ?? usina.NomeCurto),
                        RetornarDadosAgente(centro, usina),
                        usina.AgeId)
                    )
            )
            .DistinctBy(item => item.Id);
    }

    public async Task<IEnumerable<DatasetItemDto>> ObterConjuntoUsinaAsync(string codigoDoCentro, string? codigoDoTipoDeFonte, CancellationToken cancellationToken)
    {
        if (string.IsNullOrEmpty(codigoDoCentro))
        {
            _notificationContext.AddNotification("Centro de operação precisa ser preenchido.");
            return [];
        }
        
        var centros = await _centroDeOperacaoRepository.GetAsync(cancellationToken);
        var centro = centros.FirstOrDefault(x => x.CodigoOns == codigoDoCentro);
        
        if (centro is null)
        {
            _notificationContext.AddNotification("Centro de operação não encontrado!");
            return [];
        }

        Expression<Func<ConjuntoDeUsina, bool>> filterConjuntoUsina = conjuntoUsina =>
            (conjuntoUsina.DataDeDesativacao == null || conjuntoUsina.DataDeDesativacao > DateTime.Now) &&
            (conjuntoUsina.DataDeEntrada != null) &&
            conjuntoUsina.CodigoDoCentroDeOperacao == codigoDoCentro &&
            (codigoDoTipoDeFonte == null || conjuntoUsina.CodigoDoTipoDeFonte == codigoDoTipoDeFonte);

        Expression<Func<Usina, bool>> filterUsina = usina =>
            (usina.DataDeDesativacao == null || usina.DataDeDesativacao > DateTime.Now) &&
            (usina.DataDeEntrada != null) &&
            usina.CodigoDoCentroDeOperacao == codigoDoCentro &&
            (codigoDoTipoDeFonte == null || usina.CodigoDoTipoDeFonte == codigoDoTipoDeFonte);

        var usinas = await _usinaRepository.GetAsync(filterUsina, cancellationToken);
        
        var conjuntoDeUsinas = await _conjuntoDeUsinaRepository.GetAsync(filterConjuntoUsina, cancellationToken);

        var dadosConjuntoUsina =
            conjuntoDeUsinas
                .Select(conjuntoUsina => new
                {
                    Id = conjuntoUsina.Codigo,
                    Destination = MontarDestination(centros, centro, conjuntoUsina),
                    Description = $"{conjuntoUsina.Descricao}",
                    Label = MontarLabel(centro, centros, conjuntoUsina),
                    Local = new ManeuverObjectDto(conjuntoUsina.Codigo, conjuntoUsina.Descricao ?? conjuntoUsina.Nome),
                    Agent = RetornarDadosAgente(centro, conjuntoUsina),
                    DefinirStatus = conjuntoUsina.AgeId
                });

        var dadosUsina =
            usinas
                .Select(usina => new
                {
                    Id = usina.UsiId,
                    Destination = MontarDestination(centros, centro, usina),
                    Description = $"{usina.Descricao}",
                    Label = MontarLabel(centro, centros, usina),
                    Local = new ManeuverObjectDto(usina.UsiId, usina.Descricao ?? usina.NomeCurto),
                    Agent = RetornarDadosAgente(centro, usina),
                    DefinirStatus = usina.AgeId
                });

        return dadosUsina
            .Union(dadosConjuntoUsina)
            .Select(dado => new DatasetItemDto(
                dado.Id,
                dado.Destination,
                dado.Description,
                dado.Label,
                dado.Local,
                dado.Agent,
                dado.DefinirStatus
            ))
            .OrderBy(item => item.Label, NaturalSortHelper.Instance)
            .DistinctBy(item => item.Id);
    }

    #region Private Methods

    private static string MontarLabel(CentroDeOperacao centroDeOperacao, IEnumerable<CentroDeOperacao> centros, Usina usina)
        => centroDeOperacao.IsCnos
            ? $"{usina.Descricao} - {usina.NomeDoAgente} - {ObterNomeCentro(centros, usina)}"
            : $"{usina.Descricao} - {usina.NomeDoAgente}";

    private static string MontarLabel(CentroDeOperacao centroDeOperacao, IEnumerable<CentroDeOperacao> centros, ConjuntoDeUsina usina)
        => centroDeOperacao.IsCnos
            ? $"{usina.Descricao} - {usina.NomeDoAgente} - {ObterNomeCentro(centros, usina)}"
            : $"{usina.Descricao} - {usina.NomeDoAgente}";

    private static string MontarLabel(CentroDeOperacao centroDeOperacao, IEnumerable<CentroDeOperacao> centros,
        Usina usina, UgeDaUsina uge)
        => centroDeOperacao.IsCnos
            ? $"UG {uge.NumeroOperacional} - {usina.Descricao} - {ObterNomeCentro(centros, usina)}"
            : $"UG {uge.NumeroOperacional} - {usina.Descricao} - {usina.NomeDoAgente}";

    private static ManeuverObjectDto MontarDestination(IEnumerable<CentroDeOperacao> centrosDeOperacao, CentroDeOperacao centroDeOperacao, Usina usina) 
        => centroDeOperacao.IsCnos ? 
            new (usina.CodigoDoCentroDeOperacao, ObterNomeCentro(centrosDeOperacao, usina)) : 
            new (usina.AgeId, usina.NomeDoAgente);

    private static ManeuverObjectDto MontarDestination(IEnumerable<CentroDeOperacao> centrosDeOperacao, CentroDeOperacao centroDeOperacao, ConjuntoDeUsina usina)
        => centroDeOperacao.IsCnos ?
            new ManeuverObjectDto(usina.CodigoDoCentroDeOperacao, ObterNomeCentro(centrosDeOperacao, usina)) :
            new ManeuverObjectDto(usina.AgeId, usina.NomeDoAgente);

    private static string ObterNomeCentro(IEnumerable<CentroDeOperacao> centrosDeOperacao, Usina usina)
        => centrosDeOperacao.First(c => c.CodigoOns == usina.CodigoDoCentroDeOperacao).NomeCurto;

    private static string ObterNomeCentro(IEnumerable<CentroDeOperacao> centrosDeOperacao, ConjuntoDeUsina usina)
        => centrosDeOperacao.First(c => c.CodigoOns == usina.CodigoDoCentroDeOperacao).NomeCurto;

    private static ManeuverObjectDto? RetornarDadosAgente(CentroDeOperacao centroDeOperacao, Usina usina)
        => centroDeOperacao.IsCnos ?
            new(usina.AgeId, usina.NomeDoAgente) :
            null;

    private static ManeuverObjectDto? RetornarDadosAgente(CentroDeOperacao centroDeOperacao, ConjuntoDeUsina usina)
        => centroDeOperacao.IsCnos ?
            new(usina.AgeId, usina.NomeDoAgente) :
            null;

    [GeneratedRegex(@"\s+")]
    private static partial Regex TrimStringRegex();
    #endregion

}