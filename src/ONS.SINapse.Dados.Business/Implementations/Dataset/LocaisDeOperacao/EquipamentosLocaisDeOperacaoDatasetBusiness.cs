using System.Linq.Expressions;
using ONS.SINapse.Dados.Business.Dtos.Dataset;
using ONS.SINapse.Dados.Entities;

namespace ONS.SINapse.Dados.Business.Implementations.Dataset.LocaisDeOperacao;

public partial class LocaisDeOperacaoDatasetBusiness
{
    readonly  string[] _tiposDeEquipamentosAtivos = ["CSI", "CRE"];
    private async Task<IEnumerable<LocalDeOperacaoDataset>> BuscarEquipamentosAsync(string codigoCentroAgente, CancellationToken cancellationToken)
    {
        var cnos = CentroDeOperacao.Cnos(codigoCentroAgente);

        Expression<Func<Equipamento, bool>> filtroEquipamento =
            x => ((x.DataDeDesativacao == null || x.DataDeDesativacao > DateTime.Now) && 
                 x.DataDeEntrada != null &&
                 _tiposDeEquipamentosAtivos.Contains(x.Tipo)) &&
                 (
                    (!cnos && x.CodigoDoCentroDeOperacao == codigoCentroAgente ||
                    cnos) ||
                    (!cnos && x.CodigoAgente == codigoCentroAgente)
                 );

        var dados = await _equipamentoRepository
            .GetAsync(filtroEquipamento, cancellationToken).ConfigureAwait(false);

        var agentes = dados.Select(a => a.CodigoAgente);

        var qAgente = await _agenteRepository
            .GetAsync(a => agentes.Contains(a.Codigo), cancellationToken);

        var qEstacao = await _estacaoRepository
            .GetAsync(
                x => x.Utilizador.Contains('T') &&
                     x.DataDeDesativacao == null
                , cancellationToken);

        LocalDeOperacaoDataset? compensadores = null;
        if(!cnos)
            compensadores = BuscarCompensadores(dados, qAgente, qEstacao);

        var centros = cnos ? await CentrosDeOperacaoAsync(cancellationToken) : null;

        var bancoDeCapacitores = BuscarBancoDeCapacitores(dados, qAgente, qEstacao, centros);
        var reatores = BuscarReatores(dados, qAgente, qEstacao, centros);
        
        LocalDeOperacaoDataset? intalacaoCompensadores = null;
        if (!cnos)
            intalacaoCompensadores = await BuscarInstalacaoCompensadores(codigoCentroAgente, cancellationToken);
        
        var result = new List<LocalDeOperacaoDataset>();

        if(compensadores?.Locais?.Any() == true)
            result.Add(compensadores);
        
        if(intalacaoCompensadores?.Locais?.Any() == true)
            result.Add(intalacaoCompensadores);
        
        if(bancoDeCapacitores.Locais.Any())
            result.Add(bancoDeCapacitores);
        
        if(reatores.Locais.Any())
            result.Add(reatores);
        
        return result;
    }

    private async Task<LocalDeOperacaoDataset> BuscarInstalacaoCompensadores(string codigoDoCentro,
        CancellationToken cancellationToken)
    {
        var dados = await _instalacaoCompensadorDatasetQuery.ObterDatasetAsync(codigoDoCentro, cancellationToken);

        return new LocalDeOperacaoDataset(
            "instalacao-compensadores",
            "Inst. Compensador",
            dados.Select(x =>
            {
                var id = $"{x.Estacao}_{x.Centro}_{x.Agente.Code}";
                return new LocalDeOperacaoDatasetItem(id, x.Estacao);
            })
        );
    }
    
    private static LocalDeOperacaoDataset BuscarCompensadores(IEnumerable<Equipamento> equipamentos, ICollection<Agente> agentes, ICollection<Estacao> estacoes)
    {
        var tiposRedes = new[] { "BAS", "COM", "UDC" };

        return
            BuscarDataset(
                new BuscarDatasetDto(["CSI", "CRE"], new DescricaoDatasetDto("compensador", "Compensadores", x => x.NomeLongo),
                    new DadosDatasetDto(equipamentos.Where(e => tiposRedes.Contains(e.CodigoTipoRede)), agentes, estacoes)));
    }

    private static LocalDeOperacaoDataset BuscarBancoDeCapacitores(IEnumerable<Equipamento> equipamentos, ICollection<Agente> agentes, ICollection<Estacao> estacoes, Dictionary<string, string>? centros)
    {
        return
           BuscarDataset(
               new BuscarDatasetDto(["BCP"], new DescricaoDatasetDto("banco-capacitor", "Banco de Capacitores", x => x.Codigo), new DadosDatasetDto(equipamentos, agentes, estacoes), centros));
    }

    private static LocalDeOperacaoDataset BuscarReatores(IEnumerable<Equipamento> equipamentos, ICollection<Agente> agentes, ICollection<Estacao> estacoes, Dictionary<string, string>? centros)
    {
        return
           BuscarDataset(
               new BuscarDatasetDto(["REA"], new DescricaoDatasetDto("reator", "Reatores", x => x.Codigo), new DadosDatasetDto(equipamentos, agentes, estacoes), centros));
    }

    private static LocalDeOperacaoDataset BuscarDataset(BuscarDatasetDto buscarDataset)
    {
        var locais = buscarDataset.Centros is not null
            ? FiltrarEquipamentos(buscarDataset.DadosDataset.Equipamentos, buscarDataset.DadosDataset.Agentes, buscarDataset.DadosDataset.Estacoes, buscarDataset.Tipos)
                .Select(x => new LocalDeOperacaoDatasetItem(x.Codigo, buscarDataset.Centros[x.CodigoDoCentroDeOperacao], buscarDataset.DescricaoDataset.SeletorNome(x)))
                .Distinct()
            : FiltrarEquipamentos(buscarDataset.DadosDataset.Equipamentos, buscarDataset.DadosDataset.Agentes, buscarDataset.DadosDataset.Estacoes, buscarDataset.Tipos)
                .Select(x => new LocalDeOperacaoDatasetItem(x.Codigo, buscarDataset.DescricaoDataset.SeletorNome(x)))
                .Distinct();

        return new LocalDeOperacaoDataset(buscarDataset.DescricaoDataset.Identificador, buscarDataset.DescricaoDataset.Descricao, locais);
    }

    private static IEnumerable<Equipamento> FiltrarEquipamentos(
        IEnumerable<Equipamento> equipamentos,
        ICollection<Agente> agentes,
        ICollection<Estacao> estacoes,
        string[] tipos)
    {
        return equipamentos
            .Where(x => tipos.Contains(x.Tipo)
                        && agentes.Any(a => a.Codigo == x.CodigoAgente)
                        && estacoes.Any(e => e.Codigo == x.CodigoEstacao1));
    }
}