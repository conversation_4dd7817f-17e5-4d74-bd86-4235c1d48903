using ONS.SINapse.Dados.Business.Dtos.Dataset;
using ONS.SINapse.Dados.Entities;
using System.Linq.Expressions;

namespace ONS.SINapse.Dados.Business.Implementations.Dataset.LocaisDeOperacao;

public partial class LocaisDeOperacaoDatasetBusiness
{
    private async Task<LocalDeOperacaoDataset> BuscarElosAsync(string codigoCentroAgente, CancellationToken cancellationToken)
    {
        var centros = await CentrosDeOperacaoAsync(cancellationToken).ConfigureAwait(false);
        
        var cnos = CentroDeOperacao.Cnos(codigoCentroAgente);

        Expression<Func<Elo, bool>> eloPredicate = CriarEloPredicate(codigoCentroAgente, cnos);
        var elos = await _eloRepository.GetAsync(eloPredicate, cancellationToken).ConfigureAwait(false);
        
        var agentes = elos.Select(a => a.CodigoAgente);

        var qAgente = await _agenteRepository
            .GetAsync(a => agentes.Contains(a.Codigo), cancellationToken);

        var elosDataset = elos.Select(x =>
        {
            var agente = qAgente.FirstOrDefault(a => a.Codigo == x.CodigoAgente);
            if (agente is null) return null;

            var uniqueId = x.CodigoDoCentroDeOperacao.Trim() == "S"
                ? $"{x.CodigoInv}_{x.CodigoRet}"
                : x.CodigoOns;

            var centro = centros.TryGetValue(x.CodigoDoCentroDeOperacao, out var nomeCentro)
                ? nomeCentro
                : x.CodigoDoCentroDeOperacao;

            string nomeFormatado = FormatarNome(x.Descricao) +
                (x.CodigoDoCentroDeOperacao.Trim() == "S" ? "" : $" - {x.CodigoNopo}");

            string nomeCompleto = cnos
                ? $"{nomeFormatado} - {centro}"
                : $"{nomeFormatado} - {agente.NomeCurto}";

            return new LocalDeOperacaoDatasetItem(uniqueId, nomeCompleto);
        })
        .Where(x => x is not null)
        .DistinctBy(x => x!.Codigo);

        return new LocalDeOperacaoDataset("elo", "Elos", elosDataset!);
    }

    private static Expression<Func<Elo, bool>> CriarEloPredicate(string codigoCentroAgente, bool cnos)
    {
        return x =>
            x.DataDeEntrada != null &&
            (x.DataDeDesativacao == null || x.DataDeDesativacao > DateTime.Now) &&
            ((!cnos && x.CodigoDoCentroDeOperacao == codigoCentroAgente ||
            cnos) ||
            (!cnos && x.CodigoAgente == codigoCentroAgente)) &&
            x.CodigoNopo.StartsWith("BP") &&
            x.CodigoOns != "RSGBI1_140_ELRSGBI12" &&
            x.CodigoOns != "RSGBI2_140_ELRSGBI24";
    }

    private static readonly string[] DoubleSpaceSeparator = { "  " };
    private static string FormatarNome(string descricao)
    {
        if (string.IsNullOrWhiteSpace(descricao)) return string.Empty;

        int indexKv = descricao.IndexOf("kV ");
        int indexBarra = descricao.IndexOf("/ ");
        if (indexKv == -1 || indexBarra == -1) return descricao.Trim();

        int startKv = indexKv + 3;
        string parte1 = descricao.Substring(startKv, indexBarra - startKv).Trim();

        int startBarra = indexBarra + 2;
        string parte2 = descricao.Substring(startBarra).Split(DoubleSpaceSeparator, StringSplitOptions.RemoveEmptyEntries)[0].Trim();

        return $"{parte1} / {parte2}";
    }
}