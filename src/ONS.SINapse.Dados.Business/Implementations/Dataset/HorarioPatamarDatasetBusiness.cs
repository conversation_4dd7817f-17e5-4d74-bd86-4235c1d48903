using ONS.SINapse.Dados.Business.Dtos;
using ONS.SINapse.Dados.Business.Dtos.Dataset;
using ONS.SINapse.Dados.Business.Interfaces.Dataset;
using ONS.SINapse.Dados.Shared.Extensions;

namespace ONS.SINapse.Dados.Business.Implementations.Dataset;

public class HorarioPatamarDatasetBusiness : IHorarioPatamarDatasetBusiness
{
    private const int IntervaloEntrePatamaresEmMinutos = 30;
    private const int MinutosPorHora = 60;
    private const int HorasPorDia = 24;
    private const int MinutosEmUmDia = MinutosPorHora * HorasPorDia;

    public IEnumerable<DatasetItemDto> ObterDataset()
    {
        var agora = DateTime.UtcNow.ToSouthAmericaStandardTime();
        var horarioSugerido = ObterProximoPatamar(agora);
        var horarioPatamar = TimeOnly.FromDateTime(agora.Date);
        var quantidadeDePatamares = (int)Math.Floor((decimal)MinutosEmUmDia / IntervaloEntrePatamaresEmMinutos);
        
        for (var i = 0; i < quantidadeDePatamares; i++)
        {
            var horario = horarioPatamar.ToString("HH:mm");
            var sugerido = horarioSugerido.Equals(horarioPatamar);
            
            yield return new DatasetItemDto(
                horario,
                new ManeuverObjectDto(string.Empty, string.Empty),
                horario,
                horario,
                sugerido);
            
            horarioPatamar = horarioPatamar.AddMinutes(IntervaloEntrePatamaresEmMinutos);
        }
    }

    private static TimeOnly ObterProximoPatamar(DateTime agora) =>
        agora.Minute < IntervaloEntrePatamaresEmMinutos
            ? new TimeOnly(agora.Hour, IntervaloEntrePatamaresEmMinutos, 0)
            : new TimeOnly(agora.Hour, 0, 0).AddHours(1);
}