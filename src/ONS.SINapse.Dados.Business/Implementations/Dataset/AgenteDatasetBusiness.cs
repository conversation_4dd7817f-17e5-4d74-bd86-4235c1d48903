using ONS.SINapse.Dados.Business.Dtos.Dataset;
using ONS.SINapse.Dados.Business.Interfaces.Dataset;
using ONS.SINapse.Dados.Entities;
using ONS.SINapse.Dados.Repository.Repositories;
using ONS.SINapse.Dados.Shared.Helpers;
using ONS.SINapse.Dados.Shared.Notifications;
using System.Linq.Expressions;

namespace ONS.SINapse.Dados.Business.Implementations.Dataset;

public class AgenteDatasetBusiness : IAgenteDatasetBusiness
{
    private readonly NotificationContext _notificationContext;
    private readonly IConjuntoDeUsinaRepository _conjuntoDeUsinaRepository;
    private readonly IUsinaRepository _usinaRepository;
    private readonly ICentroDeOperacaoRepository _centroDeOperacaoRepository;

    public AgenteDatasetBusiness(NotificationContext notificationContext,
        IConjuntoDeUsinaRepository conjuntoDeUsinaRepository, 
        IUsinaRepository usinaRepository, 
        ICentroDeOperacaoRepository centroDeOperacaoRepository)
    {
        _notificationContext = notificationContext;
        _conjuntoDeUsinaRepository = conjuntoDeUsinaRepository;
        _usinaRepository = usinaRepository;
        _centroDeOperacaoRepository = centroDeOperacaoRepository;
    }
    public async Task<ICollection<DatasetItemDto>> ObterAgentesAsync(string codigoDoCentro, CancellationToken cancellationToken)
    {
        if (string.IsNullOrEmpty(codigoDoCentro))
        {
            _notificationContext.AddNotification("Centro de operação precisa ser preenchido.");
            return [];
        }
        
        var centros = await _centroDeOperacaoRepository.GetAsync(cancellationToken);
        var centro = centros.FirstOrDefault(x => x.CodigoOns == codigoDoCentro);
        
        if (centro is null)
        {
            _notificationContext.AddNotification("Centro de operação não encontrado.");
            return [];
        }
        
        Expression<Func<ConjuntoDeUsina, bool>> filterConjuntoUsina = conjuntoUsina =>
            conjuntoUsina.DataDeDesativacao == null &&
            (centro.IsCnos || conjuntoUsina.CodigoDoCentroDeOperacao == codigoDoCentro);
        
        Expression<Func<Usina, bool>> filterUsina = usina =>
            usina.DataDeDesativacao == null &&
            (centro.IsCnos || usina.CodigoDoCentroDeOperacao == codigoDoCentro);

        var usinas = await _usinaRepository.GetAsync(filterUsina, cancellationToken);
        
        var conjuntoDeUsinas = await _conjuntoDeUsinaRepository.GetAsync(filterConjuntoUsina, cancellationToken);


        var dadosConjuntoUsina =
            conjuntoDeUsinas
                .DistinctBy(conjuntoUsina => conjuntoUsina.AgeId)
                .Select(conjuntoUsina => new
                {
                    Id = conjuntoUsina.AgeId, 
                    Destination = new ManeuverObjectDto(conjuntoUsina.AgeId, conjuntoUsina.NomeDoAgente),
                    Description = $"{ObterNomeCentro(centros, conjuntoUsina)} | {conjuntoUsina.NomeDoAgente}",
                    Label = conjuntoUsina.NomeDoAgente, 
                    Local = new ManeuverObjectDto(conjuntoUsina.Codigo, conjuntoUsina.Descricao ?? conjuntoUsina.Nome),
                    DefinirStatus = conjuntoUsina.AgeId
                });

        var dadosUsina =
            usinas
                .DistinctBy(usina => usina.AgeId)
                .Select(usina => new
                {
                    Id = usina.AgeId,
                    Destination = new ManeuverObjectDto(usina.AgeId, usina.NomeDoAgente),
                    Description = $"{ObterNomeCentro(centros, usina)} | {usina.NomeDoAgente}",
                    Label = usina.NomeDoAgente,
                    Local = new ManeuverObjectDto(usina.UsiId,usina.Descricao ??  usina.NomeCurto),
                    DefinirStatus = usina.AgeId
                });
        
        return dadosUsina
            .Union(dadosConjuntoUsina)
            .DistinctBy(x=> x.Destination)
            .Select(agente => new DatasetItemDto(
                agente.Id,
                agente.Destination,
                agente.Description,
                agente.Label, 
                agente.Local,
                null,
                agente.DefinirStatus
            ))
            .OrderBy(item => item.Label, NaturalSortHelper.Instance)
            .ToList();
    }
    
    private static string ObterNomeCentro(IEnumerable<CentroDeOperacao> centrosDeOperacao, Usina usina)
        => centrosDeOperacao.First(c => c.CodigoOns == usina.CodigoDoCentroDeOperacao).NomeCurto;
    
    private static string ObterNomeCentro(IEnumerable<CentroDeOperacao> centrosDeOperacao, ConjuntoDeUsina usina)
        => centrosDeOperacao.First(c => c.CodigoOns == usina.CodigoDoCentroDeOperacao).NomeCurto;
    
    private bool _disposed;
    
    public void Dispose()
    {
        if(_disposed) return;
        _disposed = true;
        _conjuntoDeUsinaRepository.Dispose();
        _usinaRepository.Dispose();
        _centroDeOperacaoRepository.Dispose();
        GC.SuppressFinalize(this);
    }
}