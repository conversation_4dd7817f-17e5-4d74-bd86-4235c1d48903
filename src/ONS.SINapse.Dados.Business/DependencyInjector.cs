using Microsoft.Extensions.DependencyInjection;
using ONS.SINapse.Dados.Business.Implementations;
using ONS.SINapse.Dados.Business.Interfaces;

namespace ONS.SINapse.Dados.Business;

public static class DependencyInjector
{
    public static IServiceCollection RegisterBusinessLayer(this IServiceCollection services)
    {
        RegisterBusiness(services);
        return services;
    }
    
    
    private static void RegisterBusiness(IServiceCollection services)
    {
        services.AddScoped<ISolicitacaoDatasetBusiness, SolicitacaoDatasetBusiness>();
        services.AddScoped<IDatasetViewsBusiness, DatasetViewsBusiness>();
        services.AddScoped<ILocalDeOperacaoDataBusiness, LocalDeOperacaoDataBusiness>();
        services.AddScoped<IAgenteDataBusiness, AgenteDataBusiness>();
        services.AddScoped<IAgenteAreaEletricaDataBusiness, AgenteAreaEletricaDataBusiness>();
    }
}
