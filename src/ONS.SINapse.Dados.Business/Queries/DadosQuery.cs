using MongoDB.Driver;
using MongoDB.Driver.Linq;
using ONS.SINapse.Dados.Business.Dtos.EspelhoTabelaQuente;
using ONS.SINapse.Dados.Business.Queries.Filters.DadosCadastrais;
using ONS.SINapse.Dados.Repository.Context;
using ONS.SINapse.Dados.Shared.CustomAttributes;

namespace ONS.SINapse.Dados.Business.Queries;

public interface IDadosQuery<TEspelho, in TFiltro> : IDisposable
    where TEspelho : EspelhoEntidadeDto
    where TFiltro : DadosBaseFilter<TEspelho>
{
    Task<IEnumerable<TEspelho>> ObterDadosAsync(TFiltro? filter, CancellationToken cancellationToken);
}

public class DadosQuery<TEspelho, TFiltro> : IDadosQuery<TEspelho, TFiltro>
    where TEspelho : EspelhoEntidadeDto
    where TFiltro : DadosBaseFilter<TEspelho>
{
    private readonly IMongoQueryContext _mongoQueryContext;

    public DadosQuery(IMongoQueryContext mongoQueryContext)
    {
        _mongoQueryContext = mongoQueryContext;
    }

    public async Task<IEnumerable<TEspelho>> ObterDadosAsync(TFiltro? filter, CancellationToken cancellationToken)
    {
        var filtro = filter?.ObterFiltro();
        var query = _mongoQueryContext.Query<TEspelho>();

        if (filtro is null || !filtro.IsStarted)
            return await query.ToListAsync(cancellationToken);

        return await query.Where(filtro).ToListAsync(cancellationToken);
    }

    private bool _disposed;
    public void Dispose()
    {
        if(_disposed) return;
        _disposed = true;
        
        _mongoQueryContext.Dispose();
        GC.SuppressFinalize(this);
    }
}

public class DadosCacheQuery<TEspelho, TFiltro> : IDadosQuery<TEspelho, TFiltro>
    where TEspelho : EspelhoEntidadeDto
    where TFiltro : DadosBaseFilter<TEspelho>
{
    private readonly IDadosQuery<TEspelho, TFiltro> _inner;
    private readonly IRedisContext _redisContext;

    public DadosCacheQuery(IDadosQuery<TEspelho, TFiltro> inner, IRedisContext redisContext)
    {
        _inner = inner;
        _redisContext = redisContext;
    }
    
    public async Task<IEnumerable<TEspelho>> ObterDadosAsync(TFiltro? filter, CancellationToken cancellationToken)
    {
        var key = typeof(TEspelho).GetCacheKey();
        
        if (string.IsNullOrEmpty(key))
            return await _inner.ObterDadosAsync(filter, cancellationToken);

        var dados = await _redisContext.BuscarAsync<IEnumerable<TEspelho>>(key, cancellationToken);

        if (dados == default) return await _inner.ObterDadosAsync(filter, cancellationToken);
        
        var filtro = filter?.ObterFiltro();
        
        if (filtro is null || !filtro.IsStarted)
            return dados;
        
        return dados.Where(filtro);
    }

    public void Dispose()
    {
        _inner.Dispose();
    }
}

