using LinqKit;
using ONS.SINapse.Dados.Business.Dtos.EspelhoTabelaQuente;

namespace ONS.SINapse.Dados.Business.Queries.Filters.DadosCadastrais;

public class DadosCentroDeOperacaoFilter : DadosBaseFilter<EspelhoCentroDeOperacaoDto>
{
    public override ExpressionStarter<EspelhoCentroDeOperacaoDto> ObterFiltro()
    {
        var filter =  base.ObterFiltro();

        filter.And(x => x.DataDeDesativacao == null || x.DataDeDesativacao > DateTime.Today);
        
        if (Codigo is not null)
            filter.And(x => x.Codigo == Codigo);
        
        return filter;
    }
}
