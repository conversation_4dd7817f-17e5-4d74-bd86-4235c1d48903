using LinqKit;
using ONS.SINapse.Dados.Business.Dtos.EspelhoTabelaQuente;

namespace ONS.SINapse.Dados.Business.Queries.Filters.DadosCadastrais;

public abstract class DadosBaseFilter<TEspelho> where TEspelho : EspelhoEntidadeDto
{
    public string? Id { get; set; }
    public string? Codigo { get; set; }
    
    public virtual ExpressionStarter<TEspelho> ObterFiltro()
    {
        var filter = PredicateBuilder.New<TEspelho>();

        if (Id is not null)
            filter.And(x => x.Id == Id);
        
        return filter;
    }
}