using LinqKit;
using ONS.SINapse.Dados.Business.Dtos.EspelhoTabelaQuente;

namespace ONS.SINapse.Dados.Business.Queries.Filters.DadosCadastrais
{
    public class DadosConjuntoDeUsinaFilter : DadosBaseFilter<EspelhoConjuntoDeUsinaDto>
    {
        public override ExpressionStarter<EspelhoConjuntoDeUsinaDto> ObterFiltro()
        {
            var filter = base.ObterFiltro();

            filter.And(x => x.DataDeDesativacao == null);

            return filter;
        }
    }
}
