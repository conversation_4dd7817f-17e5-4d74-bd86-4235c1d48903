using LinqKit;
using ONS.SINapse.Dados.Business.Dtos.EspelhoTabelaQuente;

namespace ONS.SINapse.Dados.Business.Queries.Filters.DadosCadastrais
{
    public class DadosUsinaFilter : DadosBaseFilter<EspelhoUsinaDto>
    {
        public override ExpressionStarter<EspelhoUsinaDto> ObterFiltro()
        {
            var filter = base.ObterFiltro();

            filter.And(x => x.DataDeDesativacao == null);

            return filter;
        }
    }
}