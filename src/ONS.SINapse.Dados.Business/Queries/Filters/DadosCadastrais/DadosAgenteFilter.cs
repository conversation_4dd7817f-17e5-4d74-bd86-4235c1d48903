using LinqKit;
using ONS.SINapse.Dados.Business.Dtos.EspelhoTabelaQuente;

namespace ONS.SINapse.Dados.Business.Queries.Filters.DadosCadastrais;

public class DadosAgenteFilter : DadosBaseFilter<EspelhoAgenteDto>
{
    public override ExpressionStarter<EspelhoAgenteDto> ObterFiltro()
    {
        var filter = base.ObterFiltro();

        if (Codigo is not null)
            filter.And(x => x.Codigo == Codigo);
        
        return filter;
    }
}