using Microsoft.Extensions.Logging;
using ONS.SINapse.Dados.Entities.Base;
using ONS.SINapse.Dados.Repository.Base;

namespace ONS.SINapse.Dados.Business.Sync;

public interface ISyncBusiness<TDocument> : ISaveSyncBusiness
    where TDocument : EntidadeSincronizavel
{
    Task<ICollection<TDocument>> ObterDocumentos(CancellationToken cancellationToken);
}


public class SyncBusiness<TDocument> : ISyncBusiness<TDocument>
    where TDocument : EntidadeSincronizavel
{
    private readonly ISyncRepository<TDocument> _repository;
    private readonly ILogger<SyncBusiness<TDocument>> _logger;

    public SyncBusiness(
        ISyncRepository<TDocument> repository,
        ILogger<SyncBusiness<TDocument>> logger)
    {
        _repository = repository;
        _logger = logger;
    }
    
    public async Task SaveAsync(CancellationToken cancellationToken)
    {
        var documents = await ObterDocumentos(cancellationToken);
        
        var registrosNovos = documents.Where(d => d.<PERSON>).ToList();
        var registrosExistentes = documents.Where(d => !d.<PERSON>ova).ToList();
        var collection = typeof(TDocument).Name;

        _logger.LogInformation(
             "[SYNC] Coleção {Collection} : {NewRecordsCount} novos registros e {ExistingRecordsCount} registros existentes",
             collection,
             registrosNovos.Count,
             registrosExistentes.Count);

        await Task.WhenAll(
            _repository.BulkAddAsync(registrosNovos, cancellationToken),
            _repository.BulkUpdateAsync(registrosExistentes, cancellationToken)
        );
    }

    public async Task<ICollection<TDocument>> ObterDocumentos(CancellationToken cancellationToken)
    {
        return (await _repository.ObterDocumentosSincronizaveisAsync(cancellationToken)).ToList();
    }

    private bool _disposed;
    protected virtual void Dispose(bool disposing)
    {
        if (_disposed) return;

        if (disposing)
        {
            _repository.Dispose();
        }

        _disposed = true;
    }
    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }
}