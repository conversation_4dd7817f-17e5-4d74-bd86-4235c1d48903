using Microsoft.Extensions.Options;
using ONS.SINapse.Dados.Entities;
using ONS.SINapse.Dados.Repository.Repositories;
using ONS.SINapse.Dados.Shared.DTO;
using ONS.SINapse.Dados.Shared.Settings;
using System.Data;

namespace ONS.SINapse.Dados.Business.Sync;

public interface IStageSyncLogBusiness : IDisposable
{
    Task AddAsync(string executor, DateTime inicio, DateTime fim, IEnumerable<SyncRecord> objetos,
        CancellationToken cancellationToken);

    Task<IEnumerable<StageSyncPendentesDto>> ObterTiposDeObjetosPendentesAsync(CancellationToken cancellationToken);
    Task ConfirmarProcessamento(IEnumerable<string> ids, CancellationToken cancellationToken);
}

public class StageSyncLogBusiness : IStageSyncLogBusiness
{
    private readonly IStageSyncLogRepository _stageSyncLogRepository;
    private readonly DataSyncSettings _dataSyncSettings;

    public StageSyncLogBusiness(IStageSyncLogRepository stageSyncLogRepository,
        IOptions<DataSyncSettings> dataSyncSettingsOptions)
    {
        _stageSyncLogRepository = stageSyncLogRepository;
        _dataSyncSettings = dataSyncSettingsOptions.Value;
    }

    public async Task AddAsync(string executor, DateTime inicio, DateTime fim, IEnumerable<SyncRecord> objetos,
        CancellationToken cancellationToken)
    {
        var registro = new StageSyncLog()
        {
            Executor = executor,
            DataInicio = inicio,
            DataFim = fim,
            TiposDeObjeto = objetos.Select(o => o.TipoObjeto).Distinct().ToList(),
            Payload = objetos
        };
        await _stageSyncLogRepository.AddAsync(registro, cancellationToken);
    }

    public async Task ConfirmarProcessamento(
        IEnumerable<string> ids, CancellationToken cancellationToken) => await _stageSyncLogRepository.AtualizarDataDoProcessamento(ids, cancellationToken);

    public async Task<IEnumerable<StageSyncPendentesDto>> ObterTiposDeObjetosPendentesAsync(
        CancellationToken cancellationToken) => await _stageSyncLogRepository.ObterTiposDeObjetosPendentesAsync(DataFim, cancellationToken);

    private DateTime DataFim => DateTime.Now.AddMinutes(-_dataSyncSettings.SyncWaitTimeInMinutes);
    
    private bool _disposed;
    protected virtual void Dispose(bool disposing)
    {
        if (_disposed) return;

        if (disposing)
        {
            _stageSyncLogRepository.Dispose();
        }

        _disposed = true;
    }
    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }
}