using ONS.SINapse.Dados.Shared.DTOs;
using ONS.SINapse.Dados.Shared.Result;

namespace ONS.SINapse.Dados.Business.Interfaces;

public interface ISolicitacaoDatasetBusiness
{
    Task<Result<IEnumerable<DatasetItemDto>>> ObterAsync(ConsultaDatasetDto consultaDatasetDto, CancellationToken cancellationToken);
    Task<Result<IEnumerable<DatasetItemDto>>> ObterAsync(ValidacaoSqlDatasetDto validacaoSqlDatasetDto, CancellationToken cancellationToken);
}
