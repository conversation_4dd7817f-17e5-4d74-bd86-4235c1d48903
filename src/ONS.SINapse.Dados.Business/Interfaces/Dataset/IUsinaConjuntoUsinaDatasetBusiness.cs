using ONS.SINapse.Dados.Business.Dtos.Dataset;

namespace ONS.SINapse.Dados.Business.Interfaces.Dataset;

public interface IUsinaConjuntoUsinaDatasetBusiness
{
    Task<IEnumerable<DatasetItemDto>> ObterUsinaAsync(string codigoDoCentro, string[]? codigoDoTipoDeFonte,
        bool? cag = null, CancellationToken cancellationToken = default);

    Task<IEnumerable<DatasetItemDto>> ObterConjuntoUsinaAsync(string codigoDoCentro, string? codigoDoTipoDeFonte,
        CancellationToken cancellationToken);

    Task<IEnumerable<DatasetItemDto>> ObterUgesAsync(string codigoDoCentro, CancellationToken cancellationToken);
}