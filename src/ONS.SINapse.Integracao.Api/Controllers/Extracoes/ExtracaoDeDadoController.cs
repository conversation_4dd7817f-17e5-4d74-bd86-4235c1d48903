using System.Net.Mime;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ONS.SINapse.Business.IBusiness;

namespace ONS.SINapse.Integracao.Api.Controllers.Extracoes;

[ApiController]
[Route("api/extracao-dado")]
[Authorize]
public class ExtracaoDeDadoController : ControllerBase
{
    private readonly IExtracaoDeSolicitacaoS3Business _extracaoDeSolicitacaoS3Business;

    public ExtracaoDeDadoController(IExtracaoDeSolicitacaoS3Business extracaoDeSolicitacaoS3Business)
    {
        _extracaoDeSolicitacaoS3Business = extracaoDeSolicitacaoS3Business;
    }
    
    [HttpGet]
    [Consumes(MediaTypeNames.Application.Json)]
    public async Task<IActionResult> GetAsync(CancellationToken cancellationToken)
    {
        await _extracaoDeSolicitacaoS3Business.ExtrairDadosAsync(cancellationToken);
        return NoContent();
    }
}