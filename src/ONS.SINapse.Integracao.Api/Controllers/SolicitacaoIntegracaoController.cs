using System.Net.Mime;
using Microsoft.AspNetCore.Mvc;
using ONS.SINapse.Business.Imp.Claims;
using ONS.SINapse.Shared.Constants;
using ONS.SINapse.Shared.Identity;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Shared.Messages;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using ONS.SINapse.Solicitacao.Dtos;
using ONS.SINapse.Solicitacao.Dtos.Integracao;
using ONS.SINapse.Solicitacao.Factories.Integracao;
using ONS.SINapse.Solicitacao.QueryHandlers.Queries;

namespace ONS.SINapse.Integracao.Api.Controllers;

[ApiController]
[Route("api/integracao/client/v2/solicitacao")]
[ClaimRequirement(PopClaimTypes.Role, Roles.ApiIntegracao)]
public class SolicitacaoIntegracaoController : ControllerBase
{
    private readonly IMediatorHandler _mediatorHandler;
    private readonly ICriarCommandStatusDeSolicitacaoRecebidoFactory _commandStatusDeSolicitacaoRecebidoFactory;

    public SolicitacaoIntegracaoController(IMediatorHandler mediatorHandler, ICriarCommandStatusDeSolicitacaoRecebidoFactory commandStatusDeSolicitacaoRecebidoFactory)
    {
        _mediatorHandler = mediatorHandler;
        _commandStatusDeSolicitacaoRecebidoFactory = commandStatusDeSolicitacaoRecebidoFactory;
    }
    
    [HttpPost]
    [Consumes(MediaTypeNames.Application.Json)]
    public async Task<IActionResult> CriarEmLote(
        [FromBody] CadastrarSolicitacaoEmLoteCommand command, 
        CancellationToken cancellationToken)
    {
        var result = await _mediatorHandler
            .EnviarComandoAsync<CadastrarSolicitacaoEmLoteCommand, StatusDeSolicitacaoIntegracaoEnvioEmLoteDto>(command,
                cancellationToken);
        
        return Ok(result);
    }
    
    [HttpPatch]
    [Consumes(MediaTypeNames.Application.Json)]
    public async Task<IActionResult> Patch([FromBody] StatusSolicitacaoDto request,
        CancellationToken cancellationToken)
    {
        var commands = _commandStatusDeSolicitacaoRecebidoFactory
            .ObterCommand(request.Solicitacoes);
        
        var tasks = commands
            .Select(async command => await SendCommandsAsync(command, cancellationToken))
            .ToList();
        
        await Task.WhenAll(tasks);

        var result = new StatusDeSolicitacaoIntegracaoEnvioEmLoteDto(tasks
            .Select(x => x.Result)
            .SelectMany(x => x.Solicitacoes)
            .ToList());
        
        return Ok(result);
    }
    
    [HttpPost("status-em-lote")]
    [Consumes(MediaTypeNames.Application.Json)]
    public async Task<OkObjectResult> GetStatus(
        [FromBody] StatusEmLoteInputDto statusEmLoteInput, CancellationToken cancellationToken)
    {
        var query = new BuscarCadastroSolicitacaoStatusQuery(statusEmLoteInput.Solicitacoes.ToList());
        var result = await _mediatorHandler.BuscarDadosAsync(query, cancellationToken);
        return Ok(new StatusDeSolicitacaoIntegracaoOutputDto(result ?? []));
    }
    
    private Task<StatusDeSolicitacaoIntegracaoEnvioEmLoteDto> SendCommandsAsync<T>(T command, CancellationToken cancellationToken) where T : Command<StatusDeSolicitacaoIntegracaoEnvioEmLoteDto>
    {
        return _mediatorHandler.EnviarComandoAsync<T, StatusDeSolicitacaoIntegracaoEnvioEmLoteDto>(command, cancellationToken);
    }
}


