using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ONS.SINapse.Repository.Imp.Store;

namespace ONS.SINapse.Integracao.Api.Controllers;
[ApiController]
[Authorize]
[Route("api/solicitacao/firebase", Name = "Solicitacao::Firebase")]
public class FirebaseController : ControllerBase
{
    [HttpGet]
    public IActionResult Get()
    {
        return Ok(SolicitacaoStore.Solicitacoes);
    }
    
    [HttpGet("{id}")]
    public IActionResult Get(string id)
    {
        var solicitacao = SolicitacaoStore.Solicitacoes.FirstOrDefault(x => x.Id == id);
        return solicitacao == null ? NotFound() : Ok(solicitacao);
    }
}