using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using ONS.SINapse.Integracao.Shared.Dtos;
using ONS.SINapse.Integracao.Shared.Settings;

namespace ONS.SINapse.Integracao.Api.Controllers;

public abstract class IntegracaoControllerBase : ControllerBase
{
    private readonly AuthorizationSettings _authorizationSettings;

    public IntegracaoControllerBase(
        IOptions<AuthorizationSettings> authorizationSettings)
    {
        _authorizationSettings = authorizationSettings.Value;
    }

    protected UsuarioJwtDto GetUsuarioJwt()
    {
        string userAccessToken = Request?.Headers["AuthorizationUser"].FirstOrDefault()?.Split(' ')?.LastOrDefault() ?? string.Empty;
        return UsuarioJwtDto.GenerateByAccessToken(userAccessToken, _authorizationSettings.RsaModulus);
    }

    protected UsuarioJwtDto GetUsuarioJwt(string token)
    {
        var userAccessToken = token.Split(' ').LastOrDefault();
        return UsuarioJwtDto.GenerateByAccessToken(userAccessToken, _authorizationSettings.RsaModulus);
    }
}