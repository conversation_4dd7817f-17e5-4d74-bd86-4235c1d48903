using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using ONS.SINapse.Business.Imp.Claims;
using ONS.SINapse.Integracao.Shared.Dtos;
using ONS.SINapse.Integracao.Shared.Settings;
using ONS.SINapse.Shared.Constants;
using ONS.SINapse.Shared.Identity;
using ONS.SINapse.Shared.Mediator;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands;
using ONS.SINapse.Solicitacao.QueryHandlers.Queries;
using System.Net.Mime;

namespace ONS.SINapse.Integracao.Api.Controllers.v1;


[ApiController]
[Route("api/integracao/client/v1/solicitacao")]
[ClaimRequirement(PopClaimTypes.Role, Roles.ApiIntegracao)]
public class SolicitacaoIntegracaoController : IntegracaoControllerBase
{
    private readonly IMediatorHandler _mediatorHandler;

    public SolicitacaoIntegracaoController(
        IMediatorHandler mediatorHandler,
        IOptions<AuthorizationSettings> authorizationSettings)
        : base(authorizationSettings)
    {
        _mediatorHandler = mediatorHandler;
    }


    [HttpGet("{codigoDeSolicitacao}/status")]
    [Consumes(MediaTypeNames.Application.Json)]
    public async Task<OkObjectResult> GetStatus(
        string codigoDeSolicitacao, CancellationToken cancellationToken)
    {
        var query = new BuscarCadastroSolicitacaoStatusPorIdQuery(codigoDeSolicitacao);
        return Ok(await _mediatorHandler.BuscarDadosAsync(query, cancellationToken));
    }

    [HttpPost("status-em-lote")]
    [Consumes(MediaTypeNames.Application.Json)]
    public async Task<OkObjectResult> GetStatus(
        [FromBody] IEnumerable<string> codigosDeSolicitacao, CancellationToken cancellationToken)
    {
        var query = new BuscarCadastroSolicitacaoStatusQuery(codigosDeSolicitacao.ToList());
        var dados = await _mediatorHandler.BuscarDadosAsync(query, cancellationToken);

        var result = new { Solicitacoes = dados };
        
        return Ok(result);
    }

    [HttpPut("{codigoDeSolicitacao}/cancelar")]
    [Consumes(MediaTypeNames.Application.Json)]
    public async Task<ActionResult> Cancelar(
        string codigoDeSolicitacao,
        [FromBody] CancelarSolicitacaoIntegracaoInputDto inputDto,
        CancellationToken cancellationToken)
    {
        var command = new CancelarEmLoteCommand(new[] { codigoDeSolicitacao });

        command.DefinirCentros(new []{ inputDto.CodigoDoCentroDeOperacao });

        var result =
            await _mediatorHandler.EnviarComandoAsync<CancelarEmLoteCommand, ResultadoCancelamentoEmLoteDto>(command, cancellationToken);

        var solicitacoesResult = new { Solicitacoes = result.Resultado };

        if (!result.ValidationResult.IsValid)
            return BadRequest(solicitacoesResult);

        return Ok(result.Resultado.FirstOrDefault());
    }

    [HttpPost("cancelar-em-lote")]
    [Consumes(MediaTypeNames.Application.Json)]
    public async Task<IActionResult> Cancelar([FromBody] IReadOnlyCollection<string> solicitacoesId, [FromQuery] string codigoDoCentroDeOperacao, CancellationToken cancellationToken)
    {
        var command = new CancelarEmLoteCommand(solicitacoesId.ToArray());

        command.DefinirCentros(new []{ codigoDoCentroDeOperacao });

        var result =
            await _mediatorHandler.EnviarComandoAsync<CancelarEmLoteCommand, ResultadoCancelamentoEmLoteDto>(command, cancellationToken);

        if (!result.ValidationResult.IsValid)
            return BadRequest(result.Resultado);

        return Ok(result.Resultado);
    }


    [HttpPut("{codigoDeSolicitacao}/informar-ciencia")]
    [Consumes(MediaTypeNames.Application.Json)]
    public async Task<ActionResult> InformarCiencia(
        string codigoDeSolicitacao,
        [FromBody] InformarCienciaSolicitacaoIntegracaoInputDto inputDto,
        CancellationToken cancellationToken)
    {
        var command = new InformarCienciaCommand(codigoDeSolicitacao);
        command.DefinirCentros(new []{ inputDto.CodigoDoCentroDeOperacao });

        var result = await _mediatorHandler.EnviarComandoAsync<InformarCienciaCommand, CienciaInformadaResultDto>(command, cancellationToken);

        if (!result.EstaValida())
            return BadRequest(result);

        return Ok(result);
    }

    public record CancelarSolicitacaoIntegracaoInputDto(string CodigoDoCentroDeOperacao);
}