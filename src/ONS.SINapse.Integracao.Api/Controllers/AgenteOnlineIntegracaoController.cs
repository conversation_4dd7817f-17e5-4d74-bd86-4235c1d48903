using System.Net.Mime;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using ONS.SINapse.Business.IBusiness.Integracao;
using ONS.SINapse.Business.Imp.Claims;
using ONS.SINapse.Integracao.Api.Controllers.v1;
using ONS.SINapse.Integracao.Shared.Dtos;
using ONS.SINapse.Integracao.Shared.Settings;
using ONS.SINapse.Shared.Constants;
using ONS.SINapse.Shared.Identity;

namespace ONS.SINapse.Integracao.Api.Controllers;

[ApiController]
[Route("api/integracao/client/v1/agente")]
[ClaimRequirement(PopClaimTypes.Role, Roles.ApiIntegracao)]
public sealed class AgenteOnlineIntegracaoController : IntegracaoControllerBase
{
    private readonly IAgenteIntegracaoBusiness _agenteIntegracaoBusiness;

    public AgenteOnlineIntegracaoController(
        IOptions<AuthorizationSettings> authorizationSettings,
        IAgenteIntegracaoBusiness agenteIntegracaoBusiness)
        : base(authorizationSettings)
    {
        _agenteIntegracaoBusiness = agenteIntegracaoBusiness;
    }

    [HttpPost("acesso")]
    [Consumes(MediaTypeNames.Application.Json)]
    public async Task<ActionResult> GetAgentesOnline(
        [FromBody]IReadOnlyCollection<string> agentes, 
        CancellationToken cancellationToken)
    {
        AgentesOnlineIntegracaoDto dto = new(agentes);
        
        return Ok(await _agenteIntegracaoBusiness.GetAgentesOnlineAsync(dto, cancellationToken));
    }
}