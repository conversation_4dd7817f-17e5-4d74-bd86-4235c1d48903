using System.Net.Mime;
using Microsoft.AspNetCore.Mvc;
using ONS.SINapse.Business.IBusiness.Integracao;
using ONS.SINapse.Business.Imp.Claims;
using ONS.SINapse.Integracao.Shared.Dtos;
using ONS.SINapse.Shared.Constants;
using ONS.SINapse.Shared.Identity;

namespace ONS.SINapse.Integracao.Api.Controllers;

[ApiController]
[Route("api/integracao/client/v1/agente")]
[ClaimRequirement(PopClaimTypes.Role, Roles.ApiIntegracao)]
public sealed class AgenteOnlineIntegracaoController : ControllerBase
{
    private readonly IAgenteIntegracaoBusiness _agenteIntegracaoBusiness;

    public AgenteOnlineIntegracaoController(IAgenteIntegracaoBusiness agenteIntegracaoBusiness)
        
    {
        _agenteIntegracaoBusiness = agenteIntegracaoBusiness;
    }

    [HttpPost("acesso")]
    [Consumes(MediaTypeNames.Application.Json)]
    public async Task<ActionResult> GetAgentesOnline(
        [FromBody]IReadOnlyCollection<string> agentes, 
        CancellationToken cancellationToken)
    {
        AgentesOnlineIntegracaoDto dto = new(agentes);
        
        return Ok(await _agenteIntegracaoBusiness.GetAgentesOnlineAsync(dto, cancellationToken));
    }
}