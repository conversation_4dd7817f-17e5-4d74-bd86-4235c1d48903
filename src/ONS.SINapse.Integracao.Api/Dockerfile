FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
ARG BUILD_CONFIGURATION=Release
WORKDIR /src
COPY ["src/ONS.SINapse.Integracao.Api/ONS.SINapse.Integracao.Api.csproj", "src/ONS.SINapse.Integracao.Api/"]
RUN dotnet restore "src/ONS.SINapse.Integracao.Api/ONS.SINapse.Integracao.Api.csproj"
COPY . .
WORKDIR "/src/src/ONS.SINapse.Integracao.Api"
RUN dotnet build "ONS.SINapse.Integracao.Api.csproj" -c $BUILD_CONFIGURATION -o /app/build

FROM build AS publish
ARG BUILD_CONFIGURATION=Release
RUN dotnet publish "ONS.SINapse.Integracao.Api.csproj" -c $BUILD_CONFIGURATION -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "ONS.SINapse.Integracao.Api.dll"]
