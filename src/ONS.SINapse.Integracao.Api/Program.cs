using Microsoft.AspNetCore.Mvc.Razor;
using Microsoft.Extensions.Options;
using ONS.SINapse.Business.Imp.Workers.ControleDeChamada;
using ONS.SINapse.CrossCutting;
using ONS.SINapse.Integracao.Shared.Settings;
using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Shared.Logging;
using ONS.SINapse.Shared.Middleware;
using ONS.SINapse.Shared.Settings;
using ONS.SINapse.Shared.Swagger;
using ONS.SINapse.Shared.Telemetry;
using ONS.SINapse.Solicitacao.Workers;
using Serilog;

var builder = WebApplication.CreateBuilder(args);

builder.Configuration.SetBasePath(Directory.GetCurrentDirectory())
    .AddJsonFile("hosting.json", optional: true)
    .AddJsonFile("permissao-usuario.json", optional: false, reloadOnChange: true)
    .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
#if DEBUG
    .AddJsonFile("appsettings.Development.json", optional: true)
#endif
    .AddEnvironmentVariables();

builder.Services.AddAwsSystemsManager(builder.Configuration);

DependencyInjector.Register(builder.Services, builder.Configuration);

builder.Services.AddMassTransitWithSqs(
    builder.Configuration,
    configureConsumers: MassTransitInjector.ConfigureConsumers(),
    configureBus: MassTransitInjector.ConfigureBus()
);


builder.Services.AddSinapseHealthChecks(builder.Configuration);
            
builder.Services.ConfigureMetrics();
builder.Services.AddCors(options =>
{
    options.AddPolicy("CORS_POLICY",
        corsPolicyBuilder => corsPolicyBuilder
            .AllowAnyOrigin()
            .AllowAnyMethod()
            .AllowAnyHeader()
            .Build()
    );
});

builder.Services.Configure<AuthorizationSettings>(builder.Configuration.GetSection("ONS:Authorization"));
builder.Services.Configure<KafkaTopicsSettings>(builder.Configuration.GetSection(nameof(KafkaTopicsSettings)));
builder.Services.Configure<KafkaConsumersSettings>(builder.Configuration.GetSection(nameof(KafkaConsumersSettings)));

builder.Services.AddSwaggerConfiguration();
builder.Services.AddControllers();

builder.Services.AddHttpContextAccessor();
builder.Services.AddTransient<GlobalExceptionHandlerMiddleware>();

builder.Services.AddHostedService<IniciarChamadaWorker>();
builder.Services.AddMvc()
    .AddViewLocalization(LanguageViewLocationExpanderFormat.Suffix)
    .AddControllersAsServices();


builder.Logging.AddSerilog();

builder.Host.UseLogging();

builder.WebHost
    .UseIISIntegration()
    .ConfigureKestrel(_ => { });

builder.AddSinapseOpenTelemetry();

var app = builder.Build();

app.UseCustomRequestLog();

app.UseSinapseOpenTelemetry();

#if DEBUG
    // Use Developer Exception Page only in Development environment (safe)
    // SonarQube: csharpsquid:S4507 - acceptable use
    if (app.Environment.IsDevelopment())
    {
        app.UseDeveloperExceptionPage();
    }
#endif

app.UseCustomRequestLog();
app.UseGlobalExceptionHandlerMiddleware();
app.ConfigureMetrics();
app.UseRouting();
app.UseAuthentication();
app.UseCors("CORS_POLICY");
app.UseAuthorization();
app.UseResponseCompression();
app.UseSwaggerConfiguration();
app.UseStaticFiles();
app.ConfigureHealthCheck();

var locOptions = app.Services.GetService<IOptions<RequestLocalizationOptions>>();
if (locOptions is not null)
{
    app.UseRequestLocalization(locOptions.Value);
}

app.UseResponseCompression();

var stoppingToken = CancellationToken.None;

app.Services.GetRequiredService<IOptions<KafkaSettings>>();
var consumersSettings = app.Services.GetRequiredService<IOptions<KafkaConsumersSettings>>();

await app.StartWorkersAsync<TrocaDeStatusDeSolicitacaoWorker>(
    consumersSettings.Value.TrocaDeStatusConsumerProvider, stoppingToken);

await app.StartWorkersAsync<CadastroDeSolicitacaoWorker>(
    consumersSettings.Value.CadastroDeSolicitacaoConsumerProvider, stoppingToken);

var appName = app.Environment.ApplicationName;

try
{
    Console.WriteLine($"Starting {appName}");
    Console.WriteLine($"Checking UTC time: {DateTime.UtcNow} - Server time: {DateTime.Now}");
    app.Run();
}
catch (Exception ex)
{
    Console.WriteLine($"{appName} Host terminated unexpectedly");
    Console.WriteLine(ex);
}
finally
{
    Console.WriteLine($"Closed {appName}");
    Console.WriteLine();
}