{"DefaultProvider": "POPServiceProvider", "Enabled": true, "Providers": [{"Name": "POPServiceProvider", "Type": "ONS.Core.POP.Provider.POPServiceProvider, ONS.Core.POP.Provider, Version=1.0.2.0, Culture=neutral, PublicKeyToken=null", "ApplicationCacheTimeout": 3600, "UserCacheTimeout": 600, "ServiceUri": "net.tcp://popservicedsv.ons.org.br/AuthorizationServices/POPService.svc/v1/nettcp", "ApplicationName": "SINAPSE", "Username": "ons\\_servicedsagerapurac", "Password": ""}]}