<Project Sdk="Microsoft.NET.Sdk.Web">

  <!-- Propriedades específicas do projeto Web -->
  <PropertyGroup>
    <UserSecretsId>dc88c6cb-b7f6-4a80-9100-b648accd2f9d</UserSecretsId>
    <DockerDefaultTargetOS>Windows</DockerDefaultTargetOS>
  </PropertyGroup>

  <!-- Pacotes específicos do projeto -->
  <ItemGroup>
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\ONS.SINapse.Business\ONS.SINapse.Business.csproj" />
    <ProjectReference Include="..\ONS.SINapse.CrossCutting\ONS.SINapse.CrossCutting.csproj" />
    <ProjectReference Include="..\ONS.SINapse.Integracao.Shared\ONS.SINapse.Integracao.Shared.csproj" />
    <ProjectReference Include="..\ONS.SINapse.Shared\ONS.SINapse.Shared.csproj" />
  </ItemGroup>

  <ItemGroup>
	<Content Update="PopProvider.json">
	   <CopyToOutputDirectory>Always</CopyToOutputDirectory>
	</Content>
  </ItemGroup>

  <Target Name="CopySwaggerCustomJs" AfterTargets="Build">
    <ItemGroup>
      <SwaggerJsFiles Include="..\ONS.SINapse.Shared\Swagger\swagger-perfil-selecionado-header.js" />
    </ItemGroup>

    <Copy SourceFiles="@(SwaggerJsFiles)"
          DestinationFolder="$(ProjectDir)wwwroot\swagger"
          SkipUnchangedFiles="true" />
  </Target>
  <Target Name="CopyPermissaoUsuarioJson" AfterTargets="Build">
    <Copy
            SourceFiles="permissao-usuario.json"
            DestinationFolder="wwwroot\data"
            SkipUnchangedFiles="true" />
  </Target>  

</Project>
