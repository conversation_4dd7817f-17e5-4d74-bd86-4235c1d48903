<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>dc88c6cb-b7f6-4a80-9100-b648accd2f9d</UserSecretsId>
    <DockerDefaultTargetOS>Windows</DockerDefaultTargetOS>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.21.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="7.1.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\ONS.SINapse.Business\ONS.SINapse.Business.csproj" />
    <ProjectReference Include="..\ONS.SINapse.CrossCutting\ONS.SINapse.CrossCutting.csproj" />
    <ProjectReference Include="..\ONS.SINapse.Integracao.Shared\ONS.SINapse.Integracao.Shared.csproj" />
    <ProjectReference Include="..\ONS.SINapse.Shared\ONS.SINapse.Shared.csproj" />
  </ItemGroup>

  <ItemGroup>
	<Content Update="PopProvider.json">
	   <CopyToOutputDirectory>Always</CopyToOutputDirectory>
	</Content>
  </ItemGroup>

</Project>
