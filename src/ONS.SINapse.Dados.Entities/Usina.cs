using MongoDB.Bson.Serialization.Attributes;
using ONS.SINapse.Dados.Entities.Base;
using ONS.SINapse.Dados.Shared.CustomAttributes;

namespace ONS.SINapse.Dados.Entities;

[BsonCollection("c_usina")]
[<PERSON><PERSON><PERSON><PERSON>("usina")]
public class Usina : EntidadeSincronizavel
{
    [BsonElement("id_usina")]
    public required override string Id { get; set; }

    [BsonRequired]
    [BsonElement("usi_id")]
    public required string UsiId { get; init; }

    [BsonRequired]
    [BsonElement("ido_usina")]
    public required string CodigoOns { get; init; }

    [BsonRequired]
    [BsonElement("cod_tpfonte")]
    public required string CodigoDoTipoDeFonte { get; init; }

    [BsonRequired]
    [BsonElement("nom_curto")]
    public required string NomeCurto { get; init; }

    [BsonElement("nom_longo")]
    public string? NomeLongo { get; init; }

    [BsonElement("dsc_usina")]
    public string? Descricao { get; init; }

    [BsonElement("din_desativacao")]
    public DateTime? DataDeDesativacao { get; init; }

    [BsonElement("din_entrada")]
    public DateTime? DataDeEntrada { get; init; }

    [BsonRequired]
    [BsonElement("age_id")]
    public required string AgeId { get; init; }

    [BsonRequired]
    [BsonElement("nom_agente")]
    public required string NomeDoAgente { get; init; }

    [BsonRequired]
    [BsonElement("cod_cos")]
    public required string CodigoDoCentroDeOperacao { get; init; }

    [BsonElement("cod_pontoconexao")]
    public string? CodigoOnsDoPontoDeConexao { get; init; }

    [BsonElement("nom_pontoconexao")]
    public string? NomeDoPontoDeConexao { get; init; }

    [BsonElement("nom_bacia")]
    public required string NomeDaBacia { get; init; }

    [BsonElement("flg_operacag")]
    public bool OperaPeloCAG { get; init; }

    [BsonElement("list_uges")]
    public required IEnumerable<UgeDaUsina> Uges { get; init; }

    [BsonIgnore]
    public string? PontoDeConexao => CodigoOnsDoPontoDeConexao?.Split("_")?.First()?.Substring(2);
}

public class UgeDaUsina
{
    public UgeDaUsina(string ugeId, string nome, string numeroOperacional)
    {
        UgeId = ugeId;
        Nome = nome;
        NumeroOperacional = numeroOperacional;
    }

    [BsonElement("uge_id")]
    public string UgeId { get; set; }

    [BsonElement("nom_uge")]
    public string Nome { get; set; }

    [BsonElement("cod_numoperacionalons")]
    public string NumeroOperacional { get; set; }
}