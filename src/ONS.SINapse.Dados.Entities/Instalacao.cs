using MongoDB.Bson.Serialization.Attributes;
using ONS.SINapse.Dados.Entities.Base;
using ONS.SINapse.Dados.Shared.CustomAttributes;

namespace ONS.SINapse.Dados.Entities;

[BsonCollection("c_instalacao")]
[<PERSON><PERSON><PERSON><PERSON>("instalacao")]
public class Instalacao : EntidadeSincronizavel
{
    [BsonElement("id_instalacao")]
    public required override string Id { get; set; }
    
    [BsonElement("nomedes")]
    public required string Nome { get; init; }
    
    [BsonElement("nom_curto")]
    public required string NomeCurto { get; init; }
    
    [BsonElement("nom_longo")]
    public required string NomeLongo { get; init; }
    
    [BsonElement("ido_ons")]
    public required string IdoOns { get; init; }
    
    [BsonElement("ins_id")]
    public required string InstalacaoId { get; init; }
    
    [BsonElement("cod_cos")]
    public required string CodigoDoCentroDeOperacao { get; init; }
    
    [BsonRequired]
    [BsonElement("cod_areaeletrica")]
    public required string CodigoAreaEletrica { get; init; }
    
    [BsonRequired]
    [BsonElement("id_tpins")]
    public required string Tipo { get; init; }

    [BsonElement("din_desativacao")]
    public DateTime? DataDeDesativacao { get; init; }

    [BsonElement("din_entrada")]
    public DateTime? DataDeEntrada { get; init; }
    
    [BsonElement("din_prevista")]
    public DateTime? DataPrevista { get; init; }
}