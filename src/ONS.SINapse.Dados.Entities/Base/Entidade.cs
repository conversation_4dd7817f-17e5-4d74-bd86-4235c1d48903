using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using Newtonsoft.Json;

namespace ONS.SINapse.Dados.Entities.Base;

public abstract class Entidade
{
    protected Entidade(DateTime? createdAt = null)
    {
        Id = ObjectId.GenerateNewId().ToString();
        CreatedAt = createdAt ?? DateTime.UtcNow;
    }

    [BsonId]
    [BsonRepresentation(BsonType.ObjectId)]
    public abstract string Id { get; set; }

    [BsonRequired]
    [BsonElement("din_criacao")]
    public DateTime CreatedAt { get; private set; }

    public override string? ToString()
        => JsonConvert.SerializeObject(this);
}