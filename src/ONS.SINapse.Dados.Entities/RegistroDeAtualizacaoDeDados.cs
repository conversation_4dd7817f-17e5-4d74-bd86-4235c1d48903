using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using ONS.SINapse.Dados.Entities.Base;
using ONS.SINapse.Dados.Shared.CustomAttributes;

namespace ONS.SINapse.Dados.Entities;

[BsonCollection("c_registroatualizacaodado")]
[<PERSON><PERSON><PERSON><PERSON>("registroatualizacaodado")]
public class RegistroDeAtualizacaoDeDados : Entidade
{
    [BsonConstructor]
    public RegistroDeAtualizacaoDeDados(string collection, string? versao = null)
    {
        Id = ObjectId.GenerateNewId().ToString();

        Versao = ValidarVersao(versao);
        
        if (string.IsNullOrEmpty(collection))
            throw new ArgumentNullException(nameof(collection), "Nome da collection precisa ser fornecido.");
        
        Collection = collection;
    }

    [BsonRequired]
    [BsonElement("id_registro")]
    public override string Id { get; set; }

    [BsonRequired]
    [BsonElement("id_versao")]
    public string Versao { get; private set; }

    [BsonRequired]
    [BsonElement("nom_collection")]
    public string Collection { get; }

    public void AtualizarVersao(string? versao = null) 
        => Versao = ValidarVersao(versao);

    private static string ValidarVersao(string? versao)
    {
        if(string.IsNullOrEmpty(versao) || !ObjectId.TryParse(versao, out _)) 
            return ObjectId.GenerateNewId().ToString();

        return versao;
    }
}