using System.Text.RegularExpressions;
using MongoDB.Bson.Serialization.Attributes;
using ONS.SINapse.Dados.Entities.Base;
using ONS.SINapse.Dados.Shared.CustomAttributes;

namespace ONS.SINapse.Dados.Entities;

[BsonCollection("c_elo")]
[<PERSON><PERSON><PERSON><PERSON>("elo")]
public class Elo : EntidadeSincronizavel
{
    [BsonElement("id_elo")]
    public override string Id { get; set; }

    [BsonRequired]
    [BsonElement("elo_id")]
    public string Codigo { get; set; }

    [BsonRequired]
    [BsonElement("nom_longo")]
    public string NomeLongo { get; set; }

    [BsonElement("nom_curto")]
    public string NomeCurto { get; set; }

    [BsonRequired]
    [BsonElement("descricao")]
    public string Descricao { get; set; }

    [BsonRequired]
    [BsonElement("ido_ons")]
    public string CodigoOns { get; set; }

    [BsonRequired]
    [BsonElement("cod_cos")]
    public string CodigoDoCentroDeOperacao { get; set; }

    [BsonRequired]
    [BsonElement("cod_nopo")]
    public string CodigoNopo { get; set; }

    [BsonRequired]
    [BsonElement("cod_inv")]
    public string CodigoInv { get; set; }

    [BsonRequired]
    [BsonElement("cod_ret")]
    public string CodigoRet { get; set; }

    [BsonRequired]
    [BsonElement("id_agente")]
    public string CodigoAgente { get; set; }

    [BsonElement("din_entrada")]
    public DateTime? DataDeEntrada { get; set; }

    [BsonElement("din_desativacao")]
    public DateTime? DataDeDesativacao { get; set; }

    [BsonElement("din_prevista")]
    public DateTime? DataPrevista { get; set; }

    public static string FormatarParaRetornarNome(string descricao)
    {
        var regex1 = new Regex(@"kV\s+(.+?)\s{3}");
        var regex2 = new Regex(@"/\s+(.+?)\s{3}");

        var match1 = regex1.Match(descricao);
        var match2 = regex2.Match(descricao);

        var part1 = match1.Success ? match1.Groups[1].Value.Trim() : string.Empty;
        var part2 = match2.Success ? match2.Groups[1].Value.Trim() : string.Empty;

        return $"{part1} / {part2}";
    }
    
}
