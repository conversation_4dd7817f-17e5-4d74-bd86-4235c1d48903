using MongoDB.Bson.Serialization.Attributes;
using ONS.SINapse.Dados.Entities.Base;
using ONS.SINapse.Dados.Shared.CustomAttributes;

namespace ONS.SINapse.Dados.Entities;

[BsonCollection("c_stagesynclog")]
public class StageSyncLog: Entidade
{
    [BsonElement("id_stagesynclog")]
    public override string Id { get; set; }
    
    [BsonElement("din_inicio")]
    public DateTime DataInicio { get; set; }

    [BsonElement("din_fim")]
    public DateTime DataFim { get; set; }
    
    [BsonElement("din_processamento")]
    public DateTime? DataDoProcessamento { get; set; }

    [BsonElement("list_tpobjeto")]
    public IEnumerable<string> TiposDeObjeto { get; set; }

    [BsonElement("executor")]
    public string Executor { get; set; }

    [BsonElement("list_payload")]
    public IEnumerable<SyncRecord> Payload { get; set; }
    
    TimeSpan DuracaoTimeSpan => DataFim - DataInicio;
    bool Processado => DataDoProcessamento != null;
}

public record SyncRecord(string Acao, string TipoObjeto, bool MudancaEstrutura, IEnumerable<SyncDatabaseProperty> Propriedades, IEnumerable<SyncDatabaseProperty> PropriedadesChave);

public record SyncDatabaseProperty
{
    public SyncDatabaseProperty(string nome, string? valor)
    {
        Nome = nome;
        Valor = valor?.Trim();
    }

    public string Nome { get; private set; }

    public string? Valor { get; private set; }
    
}