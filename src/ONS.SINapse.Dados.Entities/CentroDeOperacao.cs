using MongoDB.Bson.Serialization.Attributes;
using ONS.SINapse.Dados.Entities.Base;
using ONS.SINapse.Dados.Shared.CustomAttributes;

namespace ONS.SINapse.Dados.Entities;

[BsonCollection("c_centrooperacao")]
[C<PERSON><PERSON>ey("centroDeOperacao")]
public class CentroDeOperacao : EntidadeSincronizavel
{
    public CentroDeOperacao(
        string id,
        string codigo,
        string codigoOns,
        string nomeCurto,
        string nomeLongo,
        string descricao)
    {
        Id = id;
        Codigo = codigo;
        CodigoOns = codigoOns;
        NomeCurto = nomeCurto;
        NomeLongo = nomeLongo;
        Descricao = descricao;
    }

    public const string CodigoCNOS = "CN";

    [BsonElement("id_centrooperacao")]
    public override string Id { get; set; }

    [BsonRequired]
    [BsonElement("cod_centrooperacao")]
    public string Codigo { get; set; }

    [BsonRequired]
    [BsonElement("ido_centrooperacao")]
    public string CodigoOns { get; set; }

    [BsonRequired]
    [BsonElement("nom_curto")]
    public string NomeCurto { get; set; }

    [BsonRequired]
    [BsonElement("nom_longo")]
    public string NomeLongo { get; set; }

    [BsonElement("dsc_centrooperacao")]
    public string? Descricao { get; set; }

    [BsonElement("dat_desativacao")]
    [BsonDateTimeOptions(DateOnly = true)]
    public DateTime? DataDeDesativacao { get; set; }

    [BsonIgnore]
    public bool IsCnos => Codigo.Equals(CodigoCNOS, StringComparison.OrdinalIgnoreCase);

    public static bool Cnos(string codigoDoCentro) =>
        codigoDoCentro.Equals(CodigoCNOS, StringComparison.OrdinalIgnoreCase);
}

