using MongoDB.Bson.Serialization.Attributes;
using ONS.SINapse.Dados.Entities.Base;
using ONS.SINapse.Dados.Shared.CustomAttributes;

namespace ONS.SINapse.Dados.Entities;


[BsonCollection("c_conjuntousina")]
[<PERSON><PERSON><PERSON>ey("conjuntousina")]
public class ConjuntoDeUsina : EntidadeSincronizavel
{
    [BsonElement("id_conjuntousina")]
    public override string Id { get; set; }

    [BsonRequired]
    [BsonElement("cod_conjuntousina")]
    public string Codigo { get; set; }

    [BsonRequired]
    [BsonElement("ido_conjuntousina")]
    public string CodigoOns { get; set; }

    [BsonRequired]
    [BsonElement("nom_conjuntousina")]
    public string Nome { get; set; }

    [BsonRequired]
    [BsonElement("cod_tpfonte")]
    public string CodigoDoTipoDeFonte { get; set; }

    [BsonElement("dsc_conjuntousina")]
    public string? Descricao { get; set; }

    [BsonElement("din_desativacao")]
    public DateTime? DataDeDesativacao { get; set; }

    [BsonElement("din_entrada")]
    public DateTime? DataDeEntrada { get; set; }

    [BsonElement("age_id")]
    public string AgeId { get; set; }

    [BsonElement("nom_agente")]
    public string NomeDoAgente { get; set; }

    [BsonRequired]
    [BsonElement("cod_cos")]
    public string CodigoDoCentroDeOperacao { get; set; }

    [BsonElement("cod_pontoconexao")]
    public string? CodigoOnsDoPontoDeConexao { get; set; }

    [BsonElement("nom_pontoconexao")]
    public string? NomeDoPontoDeConexao { get; set; }

    [BsonIgnore]
    public string? PontoDeConexao => CodigoOnsDoPontoDeConexao?.Split("_")?.First()?.Substring(2);
}