using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.UnitTest.Domain.Fixtures.Collections;
using ONS.SINapse.UnitTest.Shared.Fixtures;

namespace ONS.SINapse.UnitTest.Domain;

[Collection(nameof(RespostaDaChamadaCollection))]
public class RespostaDaChamadaTest
{
    private readonly RespostaDaChamadaFixture _respostaDaChamadaFixture;

    public RespostaDaChamadaTest(RespostaDaChamadaFixture respostaDaChamadaFixture)
    {
        _respostaDaChamadaFixture = respostaDaChamadaFixture;
    }

    [Fact(DisplayName = "Nova instancia de resposta da chamada sendo criada")]
    [Trait("Domain", "Resposta da Chamada")]
    public void DeveInstanciarRespostaDaChamada()
    {
        // Arrange && Act

        var respostaDaChamada = _respostaDaChamadaFixture.GeraRespostaDaChamada();

        var camposParaValidar = new List<object?>
        {
            respostaDaChamada.DataDeAtualizacao,
            respostaDaChamada.CentroAgente,
            respostaDaChamada.Equipamentos,
            respostaDaChamada.Notificado,
            respostaDaChamada.NumeroDaChamada,
            respostaDaChamada.Status,
            respostaDaChamada.TmpEquipamentos,
            respostaDaChamada.TmpVisualizaTodosOsEquipamentos,
            respostaDaChamada.VisualizaTodosOsEquipamentos
        };

        // Assert
        Assert.NotNull(camposParaValidar);
        Assert.NotEmpty(camposParaValidar);
    }

    [Fact(DisplayName = "Deve registrar presença")]
    [Trait("Domain", "Resposta da Chamada")]
    public void DeveRegistrarPresenca()
    {
        // Arrange && Act

        var respostaDaChamada = _respostaDaChamadaFixture.GeraRespostaDaChamada();
        respostaDaChamada.RegistrarPresenca();

        // Assert
        respostaDaChamada.Status.ShouldBe(StatusDeCentroAgente.Online);
    }

    [Fact(DisplayName = "Deve registrar ausencia")]
    [Trait("Domain", "Resposta da Chamada")]
    public void DeveRegistrarAusencia()
    {
        // Arrange && Act

        var respostaDaChamada = _respostaDaChamadaFixture.GeraRespostaDaChamada();
        respostaDaChamada.RegistrarAusencia();

        // Assert
        respostaDaChamada.Status.ShouldBe(StatusDeCentroAgente.Offline);
    }

    [Fact(DisplayName = "Deve registrar ausencia")]
    [Trait("Domain", "Resposta da Chamada")]
    public void DeveNotificar()
    {
        // Arrange && Act

        var respostaDaChamada = _respostaDaChamadaFixture.GeraRespostaDaChamada();
        respostaDaChamada.Notificar();

        // Assert
        respostaDaChamada.Notificado.ShouldBeTrue();
    }
}