using ONS.SINapse.Business.Imp.Builders;
using ONS.SINapse.Entities.Entities;
using ONS.SINapse.Integracao.Shared.Enums;

namespace ONS.SINapse.UnitTest.Domain;

public class SolicitacaoBuilderTest
{
    [Fact]
    public void Deve_Criar_Solicitacao_Com_Valores_Padrao()
    {
        // Arrange
        var builder = new SolicitacaoBuilder();

        // Act
        var solicitacao = builder.Build();

        // Assert
        solicitacao.ShouldNotBeNull();
        solicitacao.Id.ShouldNotBeNullOrWhiteSpace();
        solicitacao.UsuarioDeCriacao.ShouldNotBeNull();
        solicitacao.Origem.ShouldNotBeNull();
        solicitacao.Destino.ShouldNotBeNull();
        solicitacao.Mensagem.ShouldBe(string.Empty);
        solicitacao.Tags.ShouldBeEmpty();
        solicitacao.SistemaDeOrigem.ShouldBe(Entities.Entities.Solicitacao.SistemaDeOrigemInterno);
    }

    [Theory (DisplayName = "Deve criar solicitação com todos os parâmetros")]
    [InlineData (true, StatusDeSolicitacao.AguardandoEnvio)]
    [InlineData (false, StatusDeSolicitacao.Pendente)]
    public void Deve_Criar_Solicitacao_Com_Todos_Parametros(bool requerAprovacaoEnvio, StatusDeSolicitacao statusEsperado)
    {
        // Arrange
        var usuario = new Usuario("joao", "<EMAIL>", "agenteX");
        var origem = new ObjetoDeManobra("OR001", "Origem X");
        var destino = new ObjetoDeManobra("DS001", "Destino Y");
        var local = new ObjetoDeManobra("LOC01", "Local Z");
        var agente = new ObjetoDeManobra("AG001", "Agente Z");
        var builder = new SolicitacaoBuilder()
            .WithUsuario(usuario)
            .WithOrigem(origem)
            .WithDestino(destino)
            .WithInformacaoAdicional("Info adicional")
            .WithMensagem("Mensagem da solicitação")
            .WithLoteId("lote-123")
            .WithSistemaExterno("SistemaX", "COD-456")
            .WithMotivo("Motivo técnico")
            .WithEncaminhada(true)
            .WithRequerAprovacaoEnvio(requerAprovacaoEnvio)
            .WithLocal(local)
            .WithAgente(agente)
            .WithTag(["tag1", "tag2"]);

        // Act
        var solicitacao = builder.Build("solicitacao-id-001");

        // Assert
        solicitacao.Id.ShouldBe("solicitacao-id-001");
        solicitacao.UsuarioDeCriacao.ShouldBe(usuario);
        solicitacao.Origem.ShouldBe(origem);
        solicitacao.Destino.ShouldBe(destino);
        solicitacao.Local.ShouldBe(local);
        solicitacao.EncaminharPara.ShouldBe(agente);
        solicitacao.InformacaoAdicional.ShouldBe("Info adicional");
        solicitacao.LoteId.ShouldBe("lote-123");
        solicitacao.Motivo.ShouldBe("Motivo técnico");
        solicitacao.Encaminhada.ShouldBeFalse();
        solicitacao.IsExterna.ShouldBeTrue();
        solicitacao.SistemaDeOrigem.ShouldBe("SistemaX");
        solicitacao.CodigoExterno.ShouldBe("COD-456");
        solicitacao.Status.ShouldBe(statusEsperado);
        solicitacao.HistoricosDeStatus.Count.ShouldBe(1);
        solicitacao.HistoricosDeStatus.First().StatusAnterior.ShouldBeNull();
        solicitacao.HistoricosDeStatus.First().Status.ShouldBe(statusEsperado);
        solicitacao.Chat.Count.ShouldBe(1);
        solicitacao.Tags.ShouldBe(["tag1", "tag2"]);
        solicitacao.Mensagem.ShouldBe("Mensagem da solicitação");
    }
}
