using ONS.SINapse.Entities.Entities;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Shared.Constants;
using ONS.SINapse.Shared.Factories;
using ONS.SINapse.UnitTest.Domain.Fixtures.Collections;
using ONS.SINapse.UnitTest.Shared.Fixtures;

namespace ONS.SINapse.UnitTest.Domain;

[Collection(nameof(SolicitacaoCollection))]
public class SolicitacaoTest
{
    private readonly SolicitacaoFixture _solicitacaoFixture;
    private readonly PerfilFixture _perfilFixture;

    public SolicitacaoTest(SolicitacaoFixture solicitacaoFixture, PerfilFixture perfilFixture)
    {
        _solicitacaoFixture = solicitacaoFixture;
        _perfilFixture = perfilFixture;
    }
    
    [Fact(DisplayName = "Nova instancia de solicitação sendo criada")]
    [Trait("Domain", "Solicitacao")]
    public void DeveInstanciarSolicitacao()
    {
        // Arrange && Act
        var usuario = UsuarioFixture.GerarUsuarioValido();
        var origem = new ObjetoDeManobra("NE", "CORS-NE");
        var destino = new ObjetoDeManobra("CHF", "CHESF");
        var local = new ObjetoDeManobra("PIUBE", "Boa Esperança");
        var encaminharPara = new ObjetoDeManobra("CHF", "CHESF");
        string? informacaoAdicional = null;
        const string mensagem = "Boa Esperança |  Sincronizar 1 UG(s).";
        var loteId = Guid.NewGuid();
        string? motivo = null;
        string? solicitacaoOrigem = null;
        string[] tags = ["Geração", "Elevação"];
        const string sistemaDeOrigem = Entities.Entities.Solicitacao.SistemaDeOrigemInterno;

        var solicitacao = new Entities.Entities.Solicitacao(
            IdSolicitacaoFactory.GerarNovoId(origem.Codigo, destino.Codigo),
            usuario,
            origem,
            destino,
            mensagem,
            tags,
            sistemaDeOrigem
        )
        {
            Local = local,
            EncaminharPara = encaminharPara,
            InformacaoAdicional = informacaoAdicional,
            LoteId = loteId.ToString(),
            Motivo = motivo,
            SolicitacaoDeOrigemId = solicitacaoOrigem
        };

        var camposParaValidar = new List<object?>
        {
            solicitacao.UsuarioDeCriacao,
            solicitacao.Origem,
            solicitacao.Destino,
            solicitacao.Mensagem,
            solicitacao.MensagemNormalizada,
            solicitacao.LoteId,
            solicitacao.SistemaDeOrigem,
            solicitacao.Chat,
            solicitacao.Status,
            solicitacao.HistoricosDeStatus,
            solicitacao.Id
        };

        // Assert
        Assert.NotNull(camposParaValidar);
        Assert.NotEmpty(camposParaValidar);
        
        Assert.True(solicitacao.Chat.Count != 0);
        Assert.True(solicitacao.HistoricosDeStatus.Count != 0);
        Assert.True(solicitacao.Status == StatusDeSolicitacao.Pendente);
    }

    
    [Fact(DisplayName = "Confirmar Solicitacao")]
    [Trait("Domain", "Solicitacao")]
    public void DeveConfirmarSolicitacao()
    {
        // Arrange
        var usuario = UsuarioFixture.GerarUsuarioValido("<EMAIL>", "Confirmar Solicitação");
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoPendente();
        
        // Act
        solicitacao.Confirmar(usuario);
        
        // Assert
        Assert.Equal(StatusDeSolicitacao.Confirmada, solicitacao.Status);
        
        Assert.Contains(solicitacao.HistoricosDeStatus
            , h =>
                h is { Status: StatusDeSolicitacao.Confirmada, StatusAnterior: StatusDeSolicitacao.Pendente } &&
                h.Usuario == usuario
        );
        
        Assert.Contains(solicitacao.Chat, c =>
            c.Status == StatusDeSolicitacao.Confirmada &&
            c.UsuarioRemetente == usuario &&
            c.Mensagem == MensagensPredefinidasDoChat.SolicitacaoConfirmada &&
            c.Origem == solicitacao.Destino
        );
    }
    
    [Fact(DisplayName = "Impedir Solicitacao")]
    [Trait("Domain", "Solicitacao")]
    public void DeveImpedirSolicitacao()
    {
        // Arrange
        
        var usuario = UsuarioFixture.GerarUsuarioValido("<EMAIL>", "Impedir Solicitação");
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoPendente();
        const string motivo = "Teste de unidade - Impedir Solicitacao";
        
        // Act
        
        solicitacao.Impedir(motivo, usuario);
        
        // Assert
        
        solicitacao.Status.ShouldBe(StatusDeSolicitacao.Impedida);
        
        solicitacao.HistoricosDeStatus
            .Any(h =>
                h is { Status: StatusDeSolicitacao.Impedida, StatusAnterior: StatusDeSolicitacao.Pendente } &&
                h.Usuario == usuario)
            .ShouldBeTrue();
        
        Assert.Contains(solicitacao.Chat, c =>
            c.Status == StatusDeSolicitacao.Impedida &&
            c.UsuarioRemetente == usuario &&
            c.Mensagem == MensagensPredefinidasDoChat.SolicitacaoImpedida(motivo) &&
            c.Origem == solicitacao.Destino
        );
    }

    [Fact(DisplayName = "Finalizar Solicitacao")]
    [Trait("Domain", "Solicitacao")]
    public void DeveFinalizarSolicitacao()
    {
        // Arrange

        var usuario = UsuarioFixture.GerarUsuarioValido("<EMAIL>", "Finalizar Solicitação");
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoConfirmada();

        // Act

        solicitacao.Finalizar(usuario);

        // Assert

        solicitacao.Status.ShouldBe(StatusDeSolicitacao.Finalizada);

        solicitacao.HistoricosDeStatus
            .Any(h =>
                h is { Status: StatusDeSolicitacao.Finalizada, StatusAnterior: StatusDeSolicitacao.Confirmada } &&
                h.Usuario == usuario)
            .ShouldBeTrue();

        Assert.Contains(solicitacao.Chat, c =>
            c.Status == StatusDeSolicitacao.Finalizada &&
            c.UsuarioRemetente == usuario &&
            c.Mensagem == MensagensPredefinidasDoChat.SolicitacaoFinalizada &&
            c.Origem == solicitacao.Destino
        );
    }

    [Fact(DisplayName = "Cancelar Solicitacao")]
    [Trait("Domain", "Solicitacao")]
    public void DeveCancelarSolicitacao()
    {
        // Arrange

        var usuario = UsuarioFixture.GerarUsuarioValido("<EMAIL>", "Cancelar Solicitação");
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoPendente();

        // Act

        solicitacao.Cancelar(usuario);

        // Assert

        solicitacao.Status.ShouldBe(StatusDeSolicitacao.Cancelada);

        solicitacao.HistoricosDeStatus
            .Any(h =>
                h is { Status: StatusDeSolicitacao.Cancelada, StatusAnterior: StatusDeSolicitacao.Pendente } &&
                h.Usuario == usuario)
            .ShouldBeTrue();

        Assert.Contains(solicitacao.Chat, c =>
            c.Status == StatusDeSolicitacao.Cancelada &&
            c.UsuarioRemetente == usuario &&
            c.Mensagem == MensagensPredefinidasDoChat.SolicitacaoCancelada &&
            c.Origem == solicitacao.Origem
        );
    }

    [Fact(DisplayName = "Informar Ciencia")]
    [Trait("Domain", "Solicitacao")]
    public void DeveInformarCienciaSolicitacao()
    {
        // Arrange

        var usuario = UsuarioFixture.GerarUsuarioValido("<EMAIL>", "Informar Ciência");
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoImpedida("Motivo para informar Ciência");

        // Act

        solicitacao.InformarCiencia(usuario);

        // Assert

        solicitacao.Status.ShouldBe(StatusDeSolicitacao.CienciaInformada);

        solicitacao.HistoricosDeStatus
            .Any(h =>
                h is { Status: StatusDeSolicitacao.CienciaInformada, StatusAnterior: StatusDeSolicitacao.Impedida } &&
                h.Usuario == usuario)
            .ShouldBeTrue();

        Assert.Contains(solicitacao.Chat, c =>
            c.Status == StatusDeSolicitacao.CienciaInformada &&
            c.UsuarioRemetente == usuario &&
            c.Mensagem == MensagensPredefinidasDoChat.SolicitacaoComCienciaInformada &&
            c.Origem == solicitacao.Origem
        );
    }

    [Fact(DisplayName = "Encaminhar Solicitacao")]
    [Trait("Domain", "Solicitacao")]
    public void DeveEncaminharSolicitacao()
    {
        // Arrange

        var usuario = UsuarioFixture.GerarUsuarioValido("<EMAIL>", "Encaminhar Solicitação");
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoPendente();

        // Act

        solicitacao.Encaminhar(usuario);

        // Assert

        solicitacao.Status.ShouldBe(StatusDeSolicitacao.Confirmada);

        solicitacao.HistoricosDeStatus
            .Any(h =>
                h is { Status: StatusDeSolicitacao.Confirmada, StatusAnterior: StatusDeSolicitacao.Pendente } &&
                h.Usuario == usuario)
            .ShouldBeTrue();

        Assert.Contains(solicitacao.Chat, c =>
            c.Status == StatusDeSolicitacao.Confirmada &&
            c.UsuarioRemetente == usuario &&
            c.Mensagem == MensagensPredefinidasDoChat.SolicitacaoConfirmada &&
            c.Origem == solicitacao.Destino
        );
    }

    [Fact(DisplayName = "Adicionar mensagem de Solicitacao")]
    [Trait("Domain", "Solicitacao")]
    public void DeveAdicionarMensagemSolicitacao()
    {
        // Arrange

        var usuario = UsuarioFixture.GerarUsuarioValido("<EMAIL>", "Adicionar Mensagem Solicitação");
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoPendente();
        var perfil = _perfilFixture.GerarPerfilValido();

        // Act

        solicitacao.AdicionarMensagemNoChat("Teste Unitário", usuario, perfil);

        // Assert

        solicitacao.Status.ShouldBe(StatusDeSolicitacao.Pendente);

        Assert.Contains(solicitacao.Chat, c =>
            c.Status == null &&
            c.UsuarioRemetente == usuario &&
            c.Mensagem == "Teste Unitário" &&
            c.Origem == solicitacao.Destino
        );
    }

    [Fact(DisplayName = "Confirmar entrega de Solicitacao")]
    [Trait("Domain", "Solicitacao")]
    public void DeveConfirmarEntregaSolicitacao()
    {
        // Arrange

        var usuario = UsuarioFixture.GerarUsuarioValido("<EMAIL>", "Confirmar Entrega Solicitação");
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoPendente();
        var perfil = _perfilFixture.GerarPerfilValido();

        // Act

        solicitacao.ConfirmarEntrega(usuario, perfil);

        // Assert

        solicitacao.Status.ShouldBe(StatusDeSolicitacao.Pendente);

        Assert.Contains(solicitacao.Chat, c =>
            c.Status == StatusDeSolicitacao.Pendente &&
            c.PrimeiraEntrega != null &&
            c.DataEHoraDeEntregaAoDestinatario != null &&
            c.UsuarioRemetente == solicitacao.UsuarioDeCriacao &&
            c.Origem == solicitacao.Origem
        );
    }

    [Fact(DisplayName = "Confirmar leitura de Solicitacao")]
    [Trait("Domain", "Solicitacao")]
    public void DeveConfirmarLeituraSolicitacao()
    {
        // Arrange

        var usuario = UsuarioFixture.GerarUsuarioValido("<EMAIL>", "Confirmar Leitura Solicitação");
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoPendente();
        var perfil = _perfilFixture.GerarPerfilValido();

        // Act

        solicitacao.ConfirmarLeitura(usuario, perfil);

        // Assert

        solicitacao.Status.ShouldBe(StatusDeSolicitacao.Pendente);

        Assert.Contains(solicitacao.Chat, c =>
            c.Status == StatusDeSolicitacao.Pendente &&
            c.PrimeiraLeitura != null &&
            c.DataEHoraDeLeituraDoDestinatario != null &&
            c.UsuarioRemetente == solicitacao.UsuarioDeCriacao &&
            c.Origem == solicitacao.Origem
        );
    }
}