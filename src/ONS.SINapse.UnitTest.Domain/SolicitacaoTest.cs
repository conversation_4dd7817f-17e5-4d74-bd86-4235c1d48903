using ONS.SINapse.Entities.Entities;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.Shared.Constants;
using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Shared.Factories;
using ONS.SINapse.Shared.Identity;
using ONS.SINapse.Solicitacao.Mapper.Manual;
using ONS.SINapse.UnitTest.Domain.Fixtures.Collections;
using ONS.SINapse.UnitTest.Shared.Fixtures;

namespace ONS.SINapse.UnitTest.Domain;

[Collection(nameof(SolicitacaoCollection))]
public class SolicitacaoTest
{
    private readonly SolicitacaoFixture _solicitacaoFixture;
    private readonly PerfilFixture _perfilFixture;

    public SolicitacaoTest(SolicitacaoFixture solicitacaoFixture, PerfilFixture perfilFixture)
    {
        _solicitacaoFixture = solicitacaoFixture;
        _perfilFixture = perfilFixture;
    }
    
    [Fact(DisplayName = "Nova instancia de solicitação sendo criada")]
    [Trait("Domain", "Solicitacao")]
    public void DeveInstanciarSolicitacao()
    {
        // Arrange && Act
        var usuario = UsuarioFixture.GerarUsuarioValido();
        var origem = new ObjetoDeManobra("NE", "CORS-NE");
        var destino = new ObjetoDeManobra("CHF", "CHESF");
        var local = new ObjetoDeManobra("PIUBE", "Boa Esperança");
        var encaminharPara = new ObjetoDeManobra("CHF", "CHESF");
        string? informacaoAdicional = null;
        const string mensagem = "Boa Esperança |  Sincronizar 1 UG(s).";
        var loteId = Guid.NewGuid();
        string? motivo = null;
        string? solicitacaoOrigem = null;
        string[] tags = ["Geração", "Elevação"];
        const string sistemaDeOrigem = Entities.Entities.Solicitacao.SistemaDeOrigemInterno;

        var solicitacao = new Entities.Entities.Solicitacao(
            IdSolicitacaoFactory.GerarNovoId(origem.Codigo, destino.Codigo),
            usuario,
            origem,
            destino,
            mensagem,
            tags,
            sistemaDeOrigem
        )
        {
            Local = local,
            EncaminharPara = encaminharPara,
            InformacaoAdicional = informacaoAdicional,
            LoteId = loteId.ToString(),
            Motivo = motivo,
            SolicitacaoDeOrigemId = solicitacaoOrigem
        };
        
        solicitacao.SetStatusInicial(solicitacao.UsuarioDeCriacao);
        
        var camposParaValidar = new List<object?>
        {
            solicitacao.UsuarioDeCriacao,
            solicitacao.Origem,
            solicitacao.Destino,
            solicitacao.Mensagem,
            solicitacao.MensagemNormalizada,
            solicitacao.LoteId,
            solicitacao.SistemaDeOrigem,
            solicitacao.Chat,
            solicitacao.Status,
            solicitacao.HistoricosDeStatus,
            solicitacao.Id
        };
        // Assert
        Assert.NotNull(camposParaValidar);
        Assert.NotEmpty(camposParaValidar);
        
        Assert.True(solicitacao.Chat.Count != 0);
        Assert.True(solicitacao.HistoricosDeStatus.Count != 0);
        Assert.True(solicitacao.Status == StatusDeSolicitacao.Pendente);
    }

    [Fact(DisplayName = "Confirmar Solicitacao")]
    [Trait("Domain", "Solicitacao")]
    public void DeveConfirmarSolicitacao()
    {
        // Arrange
        var usuario = UsuarioFixture.GerarUsuarioValido("<EMAIL>", "Confirmar Solicitação");
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoPendente();
        
        // Act
        solicitacao.Confirmar(usuario);
        
        // Assert
        Assert.Equal(StatusDeSolicitacao.Confirmada, solicitacao.Status);
        
        Assert.Contains(solicitacao.HistoricosDeStatus
            , h =>
                h is { Status: StatusDeSolicitacao.Confirmada, StatusAnterior: StatusDeSolicitacao.Pendente } &&
                h.Usuario == usuario
        );
        
        Assert.Contains(solicitacao.Chat, c =>
            c.Status == StatusDeSolicitacao.Confirmada &&
            c.UsuarioRemetente == usuario &&
            c.Mensagem == MensagensPredefinidasDoChat.SolicitacaoConfirmada &&
            c.Origem == solicitacao.Destino
        );
    }
    
    [Fact(DisplayName = "Impedir Solicitacao")]
    [Trait("Domain", "Solicitacao")]
    public void DeveImpedirSolicitacao()
    {
        // Arrange
        
        var usuario = UsuarioFixture.GerarUsuarioValido("<EMAIL>", "Impedir Solicitação");
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoPendente();
        const string motivo = "Teste de unidade - Impedir Solicitacao";
        
        // Act
        
        solicitacao.Impedir(motivo, usuario);
        
        // Assert
        
        solicitacao.Status.ShouldBe(StatusDeSolicitacao.Impedida);
        
        solicitacao.HistoricosDeStatus
            .Any(h =>
                h is { Status: StatusDeSolicitacao.Impedida, StatusAnterior: StatusDeSolicitacao.Pendente } &&
                h.Usuario == usuario)
            .ShouldBeTrue();
        
        Assert.Contains(solicitacao.Chat, c =>
            c.Status == StatusDeSolicitacao.Impedida &&
            c.UsuarioRemetente == usuario &&
            c.Mensagem == MensagensPredefinidasDoChat.SolicitacaoImpedida(motivo) &&
            c.Origem == solicitacao.Destino
        );
    }

    [Fact(DisplayName = "Finalizar Solicitacao")]
    [Trait("Domain", "Solicitacao")]
    public void DeveFinalizarSolicitacao()
    {
        // Arrange

        var usuario = UsuarioFixture.GerarUsuarioValido("<EMAIL>", "Finalizar Solicitação");
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoConfirmada();

        // Act

        solicitacao.Finalizar(usuario);

        // Assert

        solicitacao.Status.ShouldBe(StatusDeSolicitacao.Finalizada);

        solicitacao.HistoricosDeStatus
            .Any(h =>
                h is { Status: StatusDeSolicitacao.Finalizada, StatusAnterior: StatusDeSolicitacao.Confirmada } &&
                h.Usuario == usuario)
            .ShouldBeTrue();

        Assert.Contains(solicitacao.Chat, c =>
            c.Status == StatusDeSolicitacao.Finalizada &&
            c.UsuarioRemetente == usuario &&
            c.Mensagem == MensagensPredefinidasDoChat.SolicitacaoFinalizada &&
            c.Origem == solicitacao.Destino
        );
    }

    [Fact(DisplayName = "Cancelar Solicitacao")]
    [Trait("Domain", "Solicitacao")]
    public void DeveCancelarSolicitacao()
    {
        // Arrange

        var usuario = UsuarioFixture.GerarUsuarioValido("<EMAIL>", "Cancelar Solicitação");
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoPendente();

        // Act

        solicitacao.Cancelar(usuario);

        // Assert

        solicitacao.Status.ShouldBe(StatusDeSolicitacao.Cancelada);

        solicitacao.HistoricosDeStatus
            .Any(h =>
                h is { Status: StatusDeSolicitacao.Cancelada, StatusAnterior: StatusDeSolicitacao.Pendente } &&
                h.Usuario == usuario)
            .ShouldBeTrue();

        Assert.Contains(solicitacao.Chat, c =>
            c.Status == StatusDeSolicitacao.Cancelada &&
            c.UsuarioRemetente == usuario &&
            c.Mensagem == MensagensPredefinidasDoChat.SolicitacaoCancelada &&
            c.Origem == solicitacao.Origem
        );
    }

    [Fact(DisplayName = "Informar Ciencia")]
    [Trait("Domain", "Solicitacao")]
    public void DeveInformarCienciaSolicitacao()
    {
        // Arrange

        var usuario = UsuarioFixture.GerarUsuarioValido("<EMAIL>", "Informar Ciência");
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoImpedida("Motivo para informar Ciência");

        // Act

        solicitacao.InformarCiencia(usuario);

        // Assert

        solicitacao.Status.ShouldBe(StatusDeSolicitacao.CienciaInformada);

        solicitacao.HistoricosDeStatus
            .Any(h =>
                h is { Status: StatusDeSolicitacao.CienciaInformada, StatusAnterior: StatusDeSolicitacao.Impedida } &&
                h.Usuario == usuario)
            .ShouldBeTrue();

        Assert.Contains(solicitacao.Chat, c =>
            c.Status == StatusDeSolicitacao.CienciaInformada &&
            c.UsuarioRemetente == usuario &&
            c.Mensagem == MensagensPredefinidasDoChat.SolicitacaoComCienciaInformada &&
            c.Origem == solicitacao.Origem
        );
    }

    [Fact(DisplayName = "Encaminhar Solicitacao")]
    [Trait("Domain", "Solicitacao")]
    public void DeveEncaminharSolicitacao()
    {
        // Arrange

        var usuario = UsuarioFixture.GerarUsuarioValido("<EMAIL>", "Encaminhar Solicitação");
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoPendente();

        // Act

        solicitacao.Encaminhar(usuario);

        // Assert

        solicitacao.Status.ShouldBe(StatusDeSolicitacao.Confirmada);

        solicitacao.HistoricosDeStatus
            .Any(h =>
                h is { Status: StatusDeSolicitacao.Confirmada, StatusAnterior: StatusDeSolicitacao.Pendente } &&
                h.Usuario == usuario)
            .ShouldBeTrue();

        Assert.Contains(solicitacao.Chat, c =>
            c.Status == StatusDeSolicitacao.Confirmada &&
            c.UsuarioRemetente == usuario &&
            c.Mensagem == MensagensPredefinidasDoChat.SolicitacaoConfirmada &&
            c.Origem == solicitacao.Destino
        );
    }

    [Fact(DisplayName = "Adicionar mensagem de Solicitacao")]
    [Trait("Domain", "Solicitacao")]
    public void DeveAdicionarMensagemSolicitacao()
    {
        // Arrange

        var usuario = UsuarioFixture.GerarUsuarioValido("<EMAIL>", "Adicionar Mensagem Solicitação");
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoPendente();
        var perfil = _perfilFixture.GerarPerfilValido();

        // Act

        solicitacao.AdicionarMensagemNoChat("Teste Unitário", usuario, perfil);

        // Assert

        solicitacao.Status.ShouldBe(StatusDeSolicitacao.Pendente);

        Assert.Contains(solicitacao.Chat, c =>
            c.Status == StatusDeSolicitacao.Pendente &&
            c.UsuarioRemetente == usuario &&
            c.Mensagem == "Teste Unitário" &&
            c.Origem == solicitacao.Destino
        );
    }

    [Fact(DisplayName = "Confirmar entrega de Solicitacao")]
    [Trait("Domain", "Solicitacao")]
    public void DeveConfirmarEntregaSolicitacao()
    {
        // Arrange

        var usuario = UsuarioFixture.GerarUsuarioValido("<EMAIL>", "Confirmar Entrega Solicitação");
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoPendente();
        var perfil = _perfilFixture.GerarPerfilValido();

        // Act

        solicitacao.ConfirmarEntrega(usuario, perfil);

        // Assert

        solicitacao.Status.ShouldBe(StatusDeSolicitacao.Pendente);

        solicitacao.Chat.All(c =>
            c.Status == StatusDeSolicitacao.Pendente &&
            c.PrimeiraEntrega != null &&
            c.DataEHoraDeEntregaAoDestinatario != null &&
            c.UsuarioRemetente == solicitacao.UsuarioDeCriacao &&
            c.Origem == solicitacao.Origem
        ).ShouldBeTrue();
    }

    [Fact(DisplayName = "Confirmar leitura de Solicitacao")]
    [Trait("Domain", "Solicitacao")]
    public void DeveConfirmarLeituraSolicitacao()
    {
        // Arrange

        var usuario = UsuarioFixture.GerarUsuarioValido("<EMAIL>", "Confirmar Leitura Solicitação");
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoPendente();
        var perfil = _perfilFixture.GerarPerfilValido();

        // Act

        solicitacao.ConfirmarLeitura(usuario, perfil);

        // Assert

        solicitacao.Status.ShouldBe(StatusDeSolicitacao.Pendente);

        Assert.Contains(solicitacao.Chat, c =>
            c.Status == StatusDeSolicitacao.Pendente &&
            c.PrimeiraLeitura != null &&
            c.DataEHoraDeLeituraDoDestinatario != null &&
            c.UsuarioRemetente == solicitacao.UsuarioDeCriacao &&
            c.Origem == solicitacao.Origem
        );
    }

    [Fact(DisplayName = "Deve verificar se é solicitante da Solicitação")]
    [Trait("Domain", "Solicitacao")]
    public void DeveVerificarSeESolicitanteDaSolicitacao()
    {
        // Arrange

        var solicitacao = _solicitacaoFixture.GerarSolicitacaoPendente();

        // Act

        var isSolicitante = solicitacao.IsSolicitante([solicitacao.Origem.Codigo]);

        // Assert

        isSolicitante.ShouldBeTrue();
    }

    [Fact(DisplayName = "Deve verificar se é solicitante da Solicitação pelo perfil")]
    [Trait("Domain", "Solicitacao")]
    public void DeveVerificarSeESolicitanteDaSolicitacaoPeloPerfil()
    {
        // Arrange
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoPendente();

        var scope = new Scope(solicitacao.Origem.Codigo, solicitacao.Origem.Codigo, solicitacao.Origem.Codigo);
        var perfil = _perfilFixture.GerarPerfilValido([scope], [], string.Empty, string.Empty);

        // Act

        var isSolicitante = solicitacao.IsSolicitante(perfil);

        // Assert

        isSolicitante.ShouldBeTrue();
    }

    [Fact(DisplayName = "Deve verificar se é destinatário da Solicitação")]
    [Trait("Domain", "Solicitacao")]
    public void DeveVerificarSeEDestinatarioDaSolicitacao()
    {
        // Arrange

        var solicitacao = _solicitacaoFixture.GerarSolicitacaoPendente();

        // Act

        var isSolicitante = solicitacao.IsDestinatario([solicitacao.Destino.Codigo]);

        // Assert

        isSolicitante.ShouldBeTrue();
    }

    [Fact(DisplayName = "Deve verificar se é destinatário da Solicitação pelo perfil")]
    [Trait("Domain", "Solicitacao")]
    public void DeveVerificarSeEDestinatarioDaSolicitacaoPeloPerfil()
    {
        // Arrange

        var solicitacao = _solicitacaoFixture.GerarSolicitacaoPendente();

        var scope = new Scope(solicitacao.Destino.Codigo, solicitacao.Destino.Codigo, solicitacao.Destino.Codigo);
        var perfil = _perfilFixture.GerarPerfilValido([scope], [], string.Empty, string.Empty);

        // Act

        var isSolicitante = solicitacao.IsDestinatario(perfil);

        // Assert

        isSolicitante.ShouldBeTrue();
    }

    [Fact(DisplayName = "Deve verificar se Solicitação foi concluida")]
    [Trait("Domain", "Solicitacao")]
    public void DeveVerificarSeSolicitacaoFoiConcluida()
    {
        // Arrange

        var solicitacao = _solicitacaoFixture.GerarSolicitacaoCancelada();

        // Act

        var isConcluida = solicitacao.IsConcluida();

        // Assert

        isConcluida.ShouldBeTrue();
    }

    [Fact(DisplayName = "Deve retornar usuário da confirmação")]
    [Trait("Domain", "Solicitacao")]
    public void DeveRetornarUsuarioDaConfirmacao()
    {
        // Arrange

        var usuario = UsuarioFixture.GerarUsuarioValido("<EMAIL>", "Confirmar Leitura Solicitação");
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoConfirmada(null, null, usuario);

        // Act

        var result = solicitacao.GetUsuarioDaConfirmacao();

        // Assert

        Assert.NotNull(result);

        Assert.True(result.Sid == usuario.Sid &&
            result.Login == usuario.Login &&
            result.Nome == usuario.Nome);
    }

    [Fact(DisplayName = "Deve criar uma instância de Solicitacao a partir de um DTO")]
    [Trait("Domain", "Solicitacao")]
    public void FromDto_DeveCriarInstanciaValida()
    {
        var original = _solicitacaoFixture.GerarSolicitacaoPendente();
        var dto = original.CriarSolicitacaoDto();

        // Act

        var solicitacao = Entities.Entities.Solicitacao.FromDto(dto);

        // Assert

        Assert.Equal(dto.Id, solicitacao.Id);
        Assert.Equal(dto.UsuarioDeCriacao.Sid, solicitacao.UsuarioDeCriacao.Sid);
        Assert.Equal(dto.Origem.Codigo, solicitacao.Origem.Codigo);
        Assert.Equal(dto.Destino.Codigo, solicitacao.Destino.Codigo);
        Assert.Equal(dto.Mensagem, solicitacao.Mensagem);
        Assert.Equal(dto.Mensagem.RemoverCaracteresEspeciais().ToLower(), solicitacao.MensagemNormalizada);
        Assert.Equal(dto.Tags, solicitacao.Tags);
        Assert.Equal(dto.SistemaDeOrigem, solicitacao.SistemaDeOrigem);
        Assert.Equal(dto.HistoricosDeStatus.Count, solicitacao.HistoricosDeStatus.Count);
        Assert.Equal(dto.Chat.Count, solicitacao.Chat.Count);
        Assert.Equal(dto.Status, solicitacao.Status);
        Assert.Equal(dto.CreatedAt, solicitacao.CreatedAt);
        Assert.Equal(dto.UpdatedAt, solicitacao.UpdatedAt);
        Assert.Equal(dto.FinalizadaAutomaticamente, solicitacao.FinalizadaAutomaticamente);
        Assert.Equal(dto.DetalheDoImpedimento, solicitacao.DetalheDoImpedimento);
        Assert.Equal(dto.IsExterna, solicitacao.IsExterna);
        Assert.Equal(dto.CodigoExterno, solicitacao.CodigoExterno);
        Assert.Equal(dto.LoteId, solicitacao.LoteId);
        Assert.Equal(dto.Motivo, solicitacao.Motivo);
        Assert.Equal(dto.Encaminhada, solicitacao.Encaminhada);
        Assert.Equal(dto.SolicitacaoDeOrigemId, solicitacao.SolicitacaoDeOrigemId);
    }

    [Fact(DisplayName = "Deve clonar corretamente uma solicitação")]
    [Trait("Domain", "Solicitacao")]
    public void Clone_DeveClonarCorretamente()
    {
        // Arrange
        var original = _solicitacaoFixture.GerarSolicitacaoPendente();

        // Act
        var clone = (Entities.Entities.Solicitacao)original.Clone();

        // Assert
        Assert.NotSame(original, clone);
        Assert.Equal(original.Id, clone.Id);
        Assert.Equal(original.Origem.Codigo, clone.Origem.Codigo);
        Assert.Equal(original.Destino.Codigo, clone.Destino.Codigo);
        Assert.Equal(original.Local?.Codigo, clone.Local?.Codigo);
        Assert.Equal(original.EncaminharPara?.Codigo, clone.EncaminharPara?.Codigo);
        Assert.Equal(original.InformacaoAdicional, clone.InformacaoAdicional);
        Assert.Equal(original.Mensagem, clone.Mensagem);
        Assert.Equal(original.MensagemNormalizada, clone.MensagemNormalizada);
        Assert.Equal(original.Status, clone.Status);
        Assert.Equal(original.FinalizadaAutomaticamente, clone.FinalizadaAutomaticamente);
        Assert.Equal(original.DetalheDoImpedimento, clone.DetalheDoImpedimento);
        Assert.Equal(original.UsuarioDeCriacao.Login, clone.UsuarioDeCriacao.Login);
        Assert.Equal(original.UpdatedAt, clone.UpdatedAt);
        Assert.Equal(original.IsExterna, clone.IsExterna);
        Assert.Equal(original.CodigoExterno, clone.CodigoExterno);
        Assert.Equal(original.LoteId, clone.LoteId);
        Assert.Equal(original.SistemaDeOrigem, clone.SistemaDeOrigem);
        Assert.Equal(original.Motivo, clone.Motivo);
        Assert.Equal(original.Encaminhada, clone.Encaminhada);
        Assert.Equal(original.SolicitacaoDeOrigemId, clone.SolicitacaoDeOrigemId);
        Assert.Equal(original.Tags, clone.Tags);
        Assert.Equal(original.CreatedAt, clone.CreatedAt);

        Assert.NotSame(original.Chat, clone.Chat);
        Assert.Equal(original.Chat.Count, clone.Chat.Count);

        Assert.NotSame(original.HistoricosDeStatus, clone.HistoricosDeStatus);
        Assert.Equal(original.HistoricosDeStatus.Count, clone.HistoricosDeStatus.Count);
    }
    
    [Fact(DisplayName = "Deve cancelar o envio de uma solicitação")]
    [Trait("Domain", "Solicitacao")]
    public void CancelarEnvio_DeveCancelarEnvio()
    {
        // Arrange
        var usuario = UsuarioFixture.GerarUsuarioValido();
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoAguardandoEnvio();
        
        // Act
        solicitacao.CancelarEnvio(usuario);
        
        // Assert
        solicitacao.Status.ShouldBe(StatusDeSolicitacao.EnvioCancelado);
        solicitacao.HistoricosDeStatus.ShouldContain(
            h =>
                h.Status == StatusDeSolicitacao.EnvioCancelado &&
                h.StatusAnterior == StatusDeSolicitacao.AguardandoEnvio &&
                h.Usuario == usuario,
            "Esperava-se que houvesse um histórico com status de 'EnvioCancelado' vindo de 'AguardandoEnvio' com o usuário correspondente."
        );
        
        solicitacao.Chat.ShouldContain(
            c =>
                c.Status == StatusDeSolicitacao.EnvioCancelado &&
                c.UsuarioRemetente == usuario &&
                c.Mensagem == MensagensPredefinidasDoChat.SolicitacaoEnvioCancelado &&
                c.Origem == solicitacao.Origem,
            "Esperava-se que houvesse uma mensagem com status de 'EnvioCancelado' com o usuário correspondente."
        );
    }
    
    [Fact(DisplayName = "Não deve cancelar o envio de uma solicitação com status diferente de 'AguardandoEnvio'")]
    [Trait("Domain", "Solicitacao")]
    public void CancelarEnvio_NaoDeveCancelarEnvio()
    {
        // Arrange
        var usuario = UsuarioFixture.GerarUsuarioValido();
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoPendente();
        
        // Act
        solicitacao.CancelarEnvio(usuario);
        
        // Assert
        solicitacao.Status.ShouldBe(StatusDeSolicitacao.Pendente);
        solicitacao.HistoricosDeStatus.ShouldNotContain(
            h =>
                h.Status == StatusDeSolicitacao.EnvioCancelado &&
                h.StatusAnterior == StatusDeSolicitacao.AguardandoEnvio &&
                h.Usuario == usuario,
            "Não esperava-se que houvesse um histórico com status de 'EnvioCancelado' vindo de 'AguardandoEnvio' com o usuário correspondente."
        );
        
        solicitacao.Chat.ShouldNotContain(
            c =>
                c.Status == StatusDeSolicitacao.EnvioCancelado &&
                c.UsuarioRemetente == usuario &&
                c.Mensagem == MensagensPredefinidasDoChat.SolicitacaoEnvioCancelado &&
                c.Origem == solicitacao.Origem,
            "Não esperava-se que houvesse uma mensagem com status de 'EnvioCancelado' com o usuário correspondente."
        );
    }
    
    [Fact(DisplayName = "Não deve enviar uma solicitação que já foi marcada como Envio Cancelado")]
    [Trait("Domain", "Solicitacao")]
    public void EnviarSolicitacao_NaoDeveEnviarSolicitacaoJaMarcadaComoEnvioCancelado()
    {
        // Arrange
        var usuario = UsuarioFixture.GerarUsuarioValido();
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoAguardandoEnvio();
        solicitacao.CancelarEnvio(usuario);
        
        // Act
        solicitacao.ConfirmarEnvio(usuario);
        
        // Assert
        solicitacao.Status.ShouldBe(StatusDeSolicitacao.EnvioCancelado);
        solicitacao.HistoricosDeStatus.ShouldNotContain(
            h =>
                h.Status == StatusDeSolicitacao.Enviada &&
                h.StatusAnterior == StatusDeSolicitacao.EnvioCancelado &&
                h.Usuario == usuario,
            "Não esperava-se que houvesse um histórico com status de 'Enviado' vindo de 'EnvioCancelado' com o usuário correspondente."
        );
        
        solicitacao.Chat.ShouldNotContain(
            c =>
                c.Status == StatusDeSolicitacao.Enviada &&
                c.UsuarioRemetente == usuario &&
                c.Mensagem == MensagensPredefinidasDoChat.SolicitacaoEnviada &&
                c.Origem == solicitacao.Origem,
            "Não esperava-se que houvesse uma mensagem com status de 'Enviado' com o usuário correspondente."
        );
    }
    
    [Fact(DisplayName = "Deve enviar uma solicitação que está aguardando envio")]
    [Trait("Domain", "Solicitacao")]
    public void EnviarSolicitacao_DeveEnviarSolicitacaoAguardandoEnvio()
    {
        // Arrange
        var usuario = UsuarioFixture.GerarUsuarioValido();
        var solicitacao = _solicitacaoFixture.GerarSolicitacaoAguardandoEnvio();
        
        // Act
        solicitacao.ConfirmarEnvio(usuario);
        
        // Assert
        solicitacao.Status.ShouldBe(StatusDeSolicitacao.Enviada);
        solicitacao.HistoricosDeStatus.ShouldContain(
            h =>
                h.Status == StatusDeSolicitacao.Enviada &&
                h.StatusAnterior == StatusDeSolicitacao.AguardandoEnvio &&
                h.Usuario == usuario,
            "Esperava-se que houvesse um histórico com status de 'Enviado' vindo de 'AguardandoEnvio' com o usuário correspondente."
        );
        
        solicitacao.Chat.ShouldContain(
            c =>
                c.Status == StatusDeSolicitacao.Enviada &&
                c.UsuarioRemetente == usuario &&
                c.Mensagem == MensagensPredefinidasDoChat.SolicitacaoEnviada &&
                c.Origem == solicitacao.Origem,
            "Esperava-se que houvesse uma mensagem com status de 'Enviado' com o usuário correspondente."
        );
    }
}