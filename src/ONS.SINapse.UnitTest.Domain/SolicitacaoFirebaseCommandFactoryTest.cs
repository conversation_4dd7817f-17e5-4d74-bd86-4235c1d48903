using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Solicitacao.CommandHandlers.Commands.Firebase;
using ONS.SINapse.Solicitacao.Factories;
using ONS.SINapse.UnitTest.Domain.Fixtures.Collections;
using ONS.SINapse.UnitTest.Shared.Fixtures;

namespace ONS.SINapse.UnitTest.Domain;

[Collection(nameof(SolicitacaoCollection))]
public class SolicitacaoFirebaseCommandFactoryTest
{
    private readonly SolicitacaoFixture _solicitacaoFixture;

    public SolicitacaoFirebaseCommandFactoryTest(SolicitacaoFixture solicitacaoFixture)
    {
        _solicitacaoFixture = solicitacaoFixture;
    }

    [Fact(DisplayName = "Deve obter solicitacoes para cadastro no firebase")]
    [Trait("Domain", "SolicitacaoFirebaseFactory")]
    public void DeveObterSolicitacoesParaCadastroNoFirebase()
    {
        List<Entities.Entities.Solicitacao> solicitacoes = [_solicitacaoFixture.GerarSolicitacaoPendente()];
        var command =  (CriarSolicitacaoNoFirebaseCommand) new SolicitacaoFirebaseCommandFactory().ObterCommand<CriarSolicitacaoNoFirebaseCommand>(solicitacoes);

        command.Solicitacoes.ShouldNotBeEmpty();
        command.Solicitacoes.Count.ShouldBe(2);
    }

    [Fact]
    public void Flatten_NullValue_ShouldIncludeWhenEnabled()
    {
        object? obj = (object?)null;

        Dictionary<string, object?> result = obj.Flatten(includeNullAndEmptyValues: true, root: "/info");

        Assert.True(result.ContainsKey("/info"));
        Assert.Null(result["/info"]);
    }

    [Fact]
    public void Flatten_Object_ShouldIncludeWhenEnabled()
    {
        int[] notas = [1, 2, 3];
        object? obj = new { Nome = "Teste", Notas = notas };

        Dictionary<string, object?> result = obj.Flatten(includeNullAndEmptyValues: true, root: "/");

        Assert.True(result.ContainsKey("/nome"));
        Assert.NotNull(result["/nome"]);
        Assert.True(result.ContainsKey("/notas/2"));
        Assert.NotNull(result["/notas/2"]);
        Assert.True((int)result["/notas/2"]! == 3);
    }
}