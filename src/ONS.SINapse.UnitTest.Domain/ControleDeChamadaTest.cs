using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.UnitTest.Domain.Fixtures.Collections;
using ONS.SINapse.UnitTest.Shared.Fixtures;

namespace ONS.SINapse.UnitTest.Domain;

[Collection(nameof(ControleDeChamadaCollection))]

public class ControleDeChamadaTest
{
    private readonly ControleDeChamadaFixture _controleDeChamadaFixture;

    public ControleDeChamadaTest(ControleDeChamadaFixture controleDeChamadaFixture)
    {
        _controleDeChamadaFixture = controleDeChamadaFixture;
    }

    [Fact(DisplayName = "Nova instancia de controle de chamada sendo criada")]
    [Trait("Domain", "Controle de Chamada")]
    public void DeveInstanciarControleDeChamada()
    {
        // Arrange && Act

        var controleDeChamada = _controleDeChamadaFixture.GerarControleDeChamadaComStatusLiberadoParaChamada();

        var camposParaValidar = new List<object?>
        {
            controleDeChamada.DataDeAtualizacao,
            controleDeChamada.DuracaoEmSegundos,
            controleDeChamada.NumeroDaChamada,
            controleDeChamada.Status
        };

        // Assert
        Assert.NotNull(camposParaValidar);
        Assert.NotEmpty(camposParaValidar);
        Assert.True(controleDeChamada.Status == StatusControleDeChamada.LiberadoParaChamada);
    }

    [Fact(DisplayName = "Deve liberar a chamada")]
    [Trait("Domain", "Controle de Chamada")]
    public void DeveLiberarParaChamada()
    {
        // Arrange && Act
        var controleDeChamada = _controleDeChamadaFixture.GerarControleDeChamadaComStatusProcessamentoFinalizado();
        controleDeChamada.LiberarParaChamada();

        // Assert
        Assert.True(controleDeChamada.Status == StatusControleDeChamada.LiberadoParaChamada);
    }

    [Fact(DisplayName = "Não deve liberar a chamada")]
    [Trait("Domain", "Controle de Chamada")]
    public void NaoDeveLiberarParaChamada()
    {
        // Arrange && Act
        var controleDeChamada = _controleDeChamadaFixture.GerarControleDeChamadaComStatusChamadaEmAndamento();
        controleDeChamada.LiberarParaChamada();

        // Assert
        Assert.False(controleDeChamada.Status == StatusControleDeChamada.LiberadoParaChamada);
    }

    [Fact(DisplayName = "Deve iniciar a chamada")]
    [Trait("Domain", "Controle de Chamada")]
    public void DeveIniciarChamada()
    {
        // Arrange && Act
        var controleDeChamada = _controleDeChamadaFixture.GerarControleDeChamadaComStatusLiberadoParaChamada();
        controleDeChamada.IniciarChamada();

        // Assert
        controleDeChamada.NumeroDaChamada.ShouldBe(1);
        Assert.True(controleDeChamada.Status == StatusControleDeChamada.ChamadaEmAndamento);
    }

    [Fact(DisplayName = "Não deve iniciar a chamada")]
    [Trait("Domain", "Controle de Chamada")]
    public void NaoDeveIniciarChamada()
    {
        // Arrange && Act
        var controleDeChamada = _controleDeChamadaFixture.GerarControleDeChamadaComStatusProcessamentoFinalizado();
        controleDeChamada.IniciarChamada();

        // Assert
        controleDeChamada.NumeroDaChamada.ShouldBe(0);
        Assert.False(controleDeChamada.Status == StatusControleDeChamada.ChamadaEmAndamento);
    }

    [Fact(DisplayName = "Deve finalizar a chamada")]
    [Trait("Domain", "Controle de Chamada")]
    public void DeveFinalizarChamada()
    {
        // Arrange && Act
        var controleDeChamada = _controleDeChamadaFixture.GerarControleDeChamadaComStatusChamadaEmAndamento(0);
        controleDeChamada.FinalizarChamada();

        // Assert
        Assert.True(controleDeChamada.Status == StatusControleDeChamada.ProcessamentoPendente);
    }

    [Fact(DisplayName = "Não deve finalizar a chamada")]
    [Trait("Domain", "Controle de Chamada")]
    public void NaoDeveFinalizarChamada()
    {
        // Arrange && Act
        var controleDeChamada = _controleDeChamadaFixture.GerarControleDeChamadaComStatusChamadaEmAndamento();
        controleDeChamada.FinalizarChamada();

        // Assert
        Assert.False(controleDeChamada.Status == StatusControleDeChamada.ProcessamentoPendente);
    }

    [Fact(DisplayName = "Deve iniciar processamento")]
    [Trait("Domain", "Controle de Chamada")]
    public void DeveIniciarProcessamento()
    {
        // Arrange && Act
        var controleDeChamada = _controleDeChamadaFixture.GerarControleDeChamadaComStatusProcessamentoPendente();
        controleDeChamada.IniciarProcessamento();

        // Assert
        Assert.True(controleDeChamada.Status == StatusControleDeChamada.Processando);
    }

    [Fact(DisplayName = "Não deve iniciar processamento")]
    [Trait("Domain", "Controle de Chamada")]
    public void NaoDeveIniciarProcessamento()
    {
        // Arrange && Act
        var controleDeChamada = _controleDeChamadaFixture.GerarControleDeChamadaComStatusChamadaEmAndamento();
        controleDeChamada.IniciarProcessamento();

        // Assert
        Assert.False(controleDeChamada.Status == StatusControleDeChamada.Processando);
    }

    [Fact(DisplayName = "Deve finalizar processamento")]
    [Trait("Domain", "Controle de Chamada")]
    public void DeveFinalizarProcessamento()
    {
        // Arrange && Act
        var controleDeChamada = _controleDeChamadaFixture.GerarControleDeChamadaComStatusProcessando();
        controleDeChamada.FinalizarProcessamento();

        // Assert
        Assert.True(controleDeChamada.Status == StatusControleDeChamada.ProcessamentoFinalizado);
    }

    [Fact(DisplayName = "Não deve finalizar processamento")]
    [Trait("Domain", "Controle de Chamada")]
    public void NaoDeveFinalizarProcessamento()
    {
        // Arrange && Act
        var controleDeChamada = _controleDeChamadaFixture.GerarControleDeChamadaComStatusChamadaEmAndamento();
        controleDeChamada.FinalizarProcessamento();

        // Assert
        Assert.False(controleDeChamada.Status == StatusControleDeChamada.ProcessamentoFinalizado);
    }

    [Fact(DisplayName = "Deve estar liberado para chamada")]
    [Trait("Domain", "Controle de Chamada")]
    public void DeveEstarLiberadoParaChamada()
    {
        // Arrange && Act
        var controleDeChamada = _controleDeChamadaFixture.GerarControleDeChamadaComStatusLiberadoParaChamada();
        
        // Assert
        Assert.True(controleDeChamada.EstaLiberadoParaChamada());
    }

    [Fact(DisplayName = "Não deve estar liberado para chamada")]
    [Trait("Domain", "Controle de Chamada")]
    public void NaoDeveEstarLiberadoParaChamada()
    {
        // Arrange && Act
        var controleDeChamada = _controleDeChamadaFixture.GerarControleDeChamadaComStatusProcessando();

        // Assert
        Assert.False(controleDeChamada.EstaLiberadoParaChamada());
    }

    [Fact(DisplayName = "Deve estar em chamada")]
    [Trait("Domain", "Controle de Chamada")]
    public void DeveEstarEmChamada()
    {
        // Arrange && Act
        var controleDeChamada = _controleDeChamadaFixture.GerarControleDeChamadaComStatusChamadaEmAndamento();

        // Assert
        Assert.True(controleDeChamada.EstaEmChamada());
    }

    [Fact(DisplayName = "Não deve estar em chamada")]
    [Trait("Domain", "Controle de Chamada")]
    public void NaoDeveEstarEmChamada()
    {
        // Arrange && Act
        var controleDeChamada = _controleDeChamadaFixture.GerarControleDeChamadaComStatusLiberadoParaChamada();

        // Assert
        Assert.False(controleDeChamada.EstaEmChamada());
    }

    [Fact(DisplayName = "Deve estar pendente de processamento")]
    [Trait("Domain", "Controle de Chamada")]
    public void DeveEstarPendenteDeProcessamento()
    {
        // Arrange && Act
        var controleDeChamada = _controleDeChamadaFixture.GerarControleDeChamadaComStatusProcessamentoPendente();

        // Assert
        Assert.True(controleDeChamada.EstaPendenteDeProcessamento());
    }

    [Fact(DisplayName = "Não deve estar pendente de processamento")]
    [Trait("Domain", "Controle de Chamada")]
    public void NaoDeveEstarPendenteDeProcessamento()
    {
        // Arrange && Act
        var controleDeChamada = _controleDeChamadaFixture.GerarControleDeChamadaComStatusLiberadoParaChamada();

        // Assert
        Assert.False(controleDeChamada.EstaPendenteDeProcessamento());
    }

    [Fact(DisplayName = "Deve estar processando")]
    [Trait("Domain", "Controle de Chamada")]
    public void DeveEstarProcessando()
    {
        // Arrange && Act
        var controleDeChamada = _controleDeChamadaFixture.GerarControleDeChamadaComStatusProcessando();

        // Assert
        Assert.True(controleDeChamada.EstaProcessando());
    }

    [Fact(DisplayName = "Não deve estar processando")]
    [Trait("Domain", "Controle de Chamada")]
    public void NaoDeveEstarProcessando()
    {
        // Arrange && Act
        var controleDeChamada = _controleDeChamadaFixture.GerarControleDeChamadaComStatusLiberadoParaChamada();

        // Assert
        Assert.False(controleDeChamada.EstaProcessando());
    }

    [Fact(DisplayName = "Deve estar processado")]
    [Trait("Domain", "Controle de Chamada")]
    public void DeveEstarProcessado()
    {
        // Arrange && Act
        var controleDeChamada = _controleDeChamadaFixture.GerarControleDeChamadaComStatusProcessamentoFinalizado();

        // Assert
        Assert.True(controleDeChamada.EstaProcessado());
    }

    [Fact(DisplayName = "Não deve estar processado")]
    [Trait("Domain", "Controle de Chamada")]
    public void NaoDeveEstarProcessado()
    {
        // Arrange && Act
        var controleDeChamada = _controleDeChamadaFixture.GerarControleDeChamadaComStatusLiberadoParaChamada();

        // Assert
        Assert.False(controleDeChamada.EstaProcessado());
    }

    [Fact(DisplayName = "Tempo restante deve retornar maior que zero")]
    [Trait("Domain", "Controle de Chamada")]
    public void TempoRestanteDeveRetornarMaiorQueZero()
    {
        // Arrange && Act
        var controleDeChamada = _controleDeChamadaFixture.GerarControleDeChamadaComStatusChamadaEmAndamento();

        // Assert
        controleDeChamada.TempoRestante().ShouldBeGreaterThan(TimeSpan.Zero);
    }

    [Fact(DisplayName = "Tempo restante deve retornar zero")]
    [Trait("Domain", "Controle de Chamada")]
    public void TempoRestanteDeveRetornarZero()
    {
        // Arrange && Act
        var controleDeChamada = _controleDeChamadaFixture.GerarControleDeChamadaComStatusChamadaEmAndamento(0);

        // Assert
        controleDeChamada.TempoRestante().ShouldBe(TimeSpan.Zero);
    }

    [Fact(DisplayName = "Tempo restante deve retornar zero com status invalido")]
    [Trait("Domain", "Controle de Chamada")]
    public void TempoRestanteDeveRetornarZeroComStatusInvalido()
    {
        // Arrange && Act
        var controleDeChamada = _controleDeChamadaFixture.GerarControleDeChamadaComStatusLiberadoParaChamada();

        // Assert
        controleDeChamada.TempoRestante().ShouldBe(TimeSpan.Zero);
    }
}