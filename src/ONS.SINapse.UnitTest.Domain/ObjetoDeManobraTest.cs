using ONS.SINapse.UnitTest.Domain.Fixtures.Collections;
using ONS.SINapse.UnitTest.Shared.Fixtures;

namespace ONS.SINapse.UnitTest.Domain;

[Collection(nameof(ObjetoManobraCollection))]

public class ObjetoDeManobraTest
{
    private readonly ObjetoManobraFixture _objetoManobraFixture;

    public ObjetoDeManobraTest(ObjetoManobraFixture objetoManobraFixture)
    {
        _objetoManobraFixture = objetoManobraFixture;
    }


    [Fact(DisplayName = "Nova instancia de objeto de manobra sendo criada")]
    [Trait("Domain", "Objeto de Manobra")]
    public void DeveInstanciarControleDeChamada()
    {
        // Arrange && Act

        var objetoDeManobra = _objetoManobraFixture.GerarObjetoDeManobra("CO", "NO");

        var camposParaValidar = new List<object?>
        {
            objetoDeManobra.Codigo,
            objetoDeManobra.Nome
        };

        // Assert
        Assert.NotNull(camposParaValidar);
        Assert.NotEmpty(camposParaValidar);
    }
}