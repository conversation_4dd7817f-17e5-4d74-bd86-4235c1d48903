using ONS.SINapse.Entities.Entities;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.UnitTest.Domain.Fixtures.Collections;
using ONS.SINapse.UnitTest.Shared.Fixtures;

namespace ONS.SINapse.UnitTest.Domain;


[Collection(nameof(ChatDeSolicitacaoCollection))]
public class ChatDeSolicitacaoTest
{
    [Fact(DisplayName = "Nova instancia de solicitação sendo criada")]
    [Trait("Domain", "ChatDeSolicitacao")]
    public void DeveInstanciarSolicitacao()
    {
        // Arrange && Act
        var usuario = UsuarioFixture.GerarUsuarioValido();
        var mensagem = "Teste chat solicitação";
        var local = new ObjetoDeManobra("PIUBE", "Boa Esperança");

        var chatDeSolicitacao = new ChatDeSolicitacao(
            mensagem,
            usuario,
            local,
            StatusDeSolicitacao.Pendente,
            DateTime.UtcNow
        );

        var camposParaValidar = new List<object?>
        {
            chatDeSolicitacao.Mensagem,
            chatDeSolicitacao.UsuarioRemetente,
            chatDeSolicitacao.Status,
            chatDeSolicitacao.Origem,
            chatDeSolicitacao.DataEHoraDeEnvio
        };

        // Assert
        Assert.NotNull(camposParaValidar);
        Assert.NotEmpty(camposParaValidar);
        Assert.True(chatDeSolicitacao.Status == StatusDeSolicitacao.Pendente);
    }

    [Fact(DisplayName = "Deve clonar corretamente um ChatDeSolicitacao")]
    [Trait("Domain", "ChatDeSolicitacao")]
    public void Clone_DeveClonarCorretamente()
    {
        // Arrange
        var usuario = UsuarioFixture.GerarUsuarioValido();
        var mensagem = "Teste chat solicitação";
        var local = new ObjetoDeManobra("PIUBE", "Boa Esperança");

        var original = new ChatDeSolicitacao(
            mensagem,
            usuario,
            local,
            StatusDeSolicitacao.Pendente,
            DateTime.UtcNow
        );
        // Act
        var clone = original.Clone();

        // Assert
        Assert.NotSame(original, clone);
        Assert.Equal(original.Mensagem, clone.Mensagem);
        Assert.Equal(original.UsuarioRemetente, clone.UsuarioRemetente);
        Assert.Equal(original.Origem, clone.Origem);
        Assert.Equal(original.Status, clone.Status);
        Assert.Equal(original.DataEHoraDeEnvio, clone.DataEHoraDeEnvio);
        Assert.Equal(original.PrimeiraLeitura, clone.PrimeiraLeitura);
        Assert.Equal(original.PrimeiraEntrega, clone.PrimeiraEntrega);
        Assert.Equal(original.DataEHoraDeEntregaAoDestinatario, clone.DataEHoraDeEntregaAoDestinatario);
        Assert.Equal(original.DataEHoraDeLeituraDoDestinatario, clone.DataEHoraDeLeituraDoDestinatario);
    }

    [Fact(DisplayName = "Deve confirmar a entrega de uma mensagem corretamente")]
    [Trait("Domain", "ChatDeSolicitacao")]
    public void ConfirmarEntrega_DeveAtualizarCamposCorretamente()
    {
        // Arrange
        var usuario = UsuarioFixture.GerarUsuarioValido();
        var remetente = UsuarioFixture.GerarUsuarioValido();
        var dataEntrega = DateTime.UtcNow;
        var mensagem = "Mensagem teste";
        var origem = new ObjetoDeManobra("PIUBE", "Boa Esperança");

        var chat = new ChatDeSolicitacao(
            mensagem,
            remetente,
            origem,
            StatusDeSolicitacao.Pendente
        );

        // Act
        chat.ConfirmarEntrega(usuario, dataEntrega);

        // Assert
        Assert.True(chat.EntregueAoDestinatario());
        Assert.Equal(dataEntrega, chat.DataEHoraDeEntregaAoDestinatario);
        Assert.NotNull(chat.PrimeiraEntrega);
        Assert.Equal(usuario.Login, chat.PrimeiraEntrega!.Usuario.Login);
        Assert.Equal(dataEntrega, chat.PrimeiraEntrega!.Data);
    }

    [Fact(DisplayName = "Deve confirmar a leitura de uma mensagem corretamente")]
    [Trait("Domain", "ChatDeSolicitacao")]
    public void ConfirmarLeitura_DeveAtualizarCamposCorretamente()
    {
        // Arrange
        var usuario = UsuarioFixture.GerarUsuarioValido();
        var remetente = UsuarioFixture.GerarUsuarioValido();
        var dataLeitura = DateTime.UtcNow;
        var mensagem = "Mensagem teste";
        var origem = new ObjetoDeManobra("PIUBE", "Boa Esperança");

        var chat = new ChatDeSolicitacao(
            mensagem,
            remetente,
            origem,
            StatusDeSolicitacao.Pendente
        );

        // Act
        chat.ConfirmarLeitura(usuario, dataLeitura);

        // Assert
        Assert.True(chat.LidaPeloDestinatario());
        Assert.Equal(dataLeitura, chat.DataEHoraDeLeituraDoDestinatario);
        Assert.NotNull(chat.PrimeiraLeitura);
        Assert.Equal(usuario.Login, chat.PrimeiraLeitura!.Usuario.Login);
        Assert.Equal(dataLeitura, chat.PrimeiraLeitura!.Data);
    }

    [Fact(DisplayName = "Deve identificar se a mensagem foi lida e entregue corretamente")]
    [Trait("Domain", "ChatDeSolicitacao")]
    public void LidaEEntregue_DeveRetornarCorretamente()
    {
        // Arrange
        var usuario = UsuarioFixture.GerarUsuarioValido();
        var remetente = UsuarioFixture.GerarUsuarioValido();
        var mensagem = "Mensagem teste";
        var origem = new ObjetoDeManobra("PIUBE", "Boa Esperança");

        var chat = new ChatDeSolicitacao(
            mensagem,
            remetente,
            origem,
            StatusDeSolicitacao.Pendente
        );

        // Assert inicial
        Assert.False(chat.LidaPeloDestinatario());
        Assert.False(chat.EntregueAoDestinatario());

        // Act
        var data = DateTime.UtcNow;
        chat.ConfirmarEntrega(usuario, data);
        chat.ConfirmarLeitura(usuario, data);

        // Assert final
        Assert.True(chat.EntregueAoDestinatario());
        Assert.True(chat.LidaPeloDestinatario());
    }
}
