using ONS.SINapse.Shared.Extensions;

namespace ONS.SINapse.UnitTest.Domain.Extensions;

public class DateExtensionsTests
{
    [Fact]
    public void ToDateTime_ShouldReturnValidDate_WhenStringIsValid()
    {
        // Arrange
        var dateString = "2025-04-16 10:30:00";

        // Act
        var result = dateString.ToDateTime();

        // Assert
        result.ShouldNotBeNull();
        result.Value.Year.ShouldBe(2025);
        result.Value.Month.ShouldBe(4);
        result.Value.Day.ShouldBe(16);
        result.Value.Hour.ShouldBe(10);
        result.Value.Minute.ShouldBe(30);
    }

    [Fact]
    public void ToDateTime_ShouldReturnNull_WhenStringIsInvalid()
    {
        var invalid = "not-a-date";
        invalid.ToDateTime().ShouldBeNull();
    }

    [Fact]
    public void ToSouthAmericaStandardTime_ShouldConvertFromUtc()
    {
        // Arrange
        var utcNow = new DateTime(2025, 4, 16, 12, 0, 0, DateTimeKind.Utc);

        // Act
        var brasiliaTime = utcNow.ToSouthAmericaStandardTime();

        // Assert
        brasiliaTime.Kind.ShouldBe(DateTimeKind.Unspecified); // because ConvertTime returns unspecified
        brasiliaTime.Hour.ShouldBe(9); // UTC-3
    }

    [Fact]
    public void ToFormattedSouthAmericaStandardTime_ShouldReturnFormattedString()
    {
        var utc = new DateTime(2025, 4, 16, 15, 45, 0, DateTimeKind.Utc);

        var formatted = utc.ToFormattedSouthAmericaStandardTime();

        formatted.ShouldBe("16/04/2025 12:45:00");
    }

    [Fact]
    public void ToBrasiliaOffset_ShouldReturnDateTimeOffsetWithMinus3()
    {
        var utc = new DateTime(2025, 4, 16, 12, 0, 0, DateTimeKind.Utc);

        var offset = utc.ToBrasiliaOffset();

        offset.Offset.ShouldBe(TimeSpan.FromHours(-3));
        offset.Hour.ShouldBe(9);
    }

    [Fact]
    public void ToFormattedSouthAmericaWithOffset_ShouldIncludeOffset()
    {
        var utc = new DateTime(2025, 4, 16, 15, 0, 0, DateTimeKind.Utc);

        var formatted = utc.ToFormattedSouthAmericaWithOffset();

        formatted.ShouldEndWith("-03:00");
        formatted.ShouldStartWith("16/04/2025");
    }

    [Fact]
    public void GetDateTimeLastHour_ShouldReturnNextDay()
    {
        var dt = new DateTime(2025, 4, 1, 10, 0, 0);

        var lastHour = dt.GetDateTimeLastHour();

        lastHour.Day.ShouldBe(2);
        lastHour.Hour.ShouldBe(03);
        lastHour.Minute.ShouldBe(00);
        lastHour.Second.ShouldBe(00);
    }

    [Fact]
    public void GetFirstHour_ShouldReturnFirstHour()
    {
        var dt = new DateTime(2025, 4, 1, 10, 0, 0);

        var lastHour = dt.GetFirstHour();

        lastHour.Day.ShouldBe(1);
        lastHour.Hour.ShouldBe(03);
        lastHour.Minute.ShouldBe(00);
        lastHour.Second.ShouldBe(00);
    }

    [Fact]
    public void IsValid_ShouldReturnFalseForMinMax()
    {
        DateTime.MinValue.IsValid().ShouldBeFalse();
        DateTime.MaxValue.IsValid().ShouldBeFalse();
    }

    [Fact]
    public void IsValid_ShouldReturnTrueForValidDate()
    {
        var dt = new DateTime(2024, 1, 1);
        dt.IsValid().ShouldBeTrue();
    }
    
    [Fact]
    public void Parse_ISO8601_String_With_Offset_Should_Preserve_Timezone()
    {
        // Arrange
        var input = "2025-04-15T00:00:00.000-03:00";

        // Act
        var parsed = DateTimeOffset.Parse(input);

        // Assert
        parsed.Year.ShouldBe(2025);
        parsed.Month.ShouldBe(4);
        parsed.Day.ShouldBe(15);
        parsed.Hour.ShouldBe(0);
        parsed.Offset.ShouldBe(TimeSpan.FromHours(-3));
    }

}