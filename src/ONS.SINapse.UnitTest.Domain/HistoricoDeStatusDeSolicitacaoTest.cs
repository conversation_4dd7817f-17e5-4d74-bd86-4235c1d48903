using ONS.SINapse.Entities.Entities;
using ONS.SINapse.Integracao.Shared.Enums;
using ONS.SINapse.UnitTest.Domain.Fixtures.Collections;
using ONS.SINapse.UnitTest.Shared.Fixtures;

namespace ONS.SINapse.UnitTest.Domain;


[Collection(nameof(HistoricoDeStatusDeSolicitacaoCollection))]
public class HistoricoDeStatusDeSolicitacaoTest
{
    [Fact(DisplayName = "Nova instancia de solicitação sendo criada")]
    [Trait("Domain", "HistoricoDeStatusDeSolicitacao")]
    public void DeveInstanciarSolicitacao()
    {
        // Arrange && Act
        var usuario = UsuarioFixture.GerarUsuarioValido();

        var historicoDeStatusDeSolicitacao = new HistoricoDeStatusDeSolicitacao(
            usuario
        )
        {
            Status = StatusDeSolicitacao.Cancelada,
            StatusAnterior = StatusDeSolicitacao.Pendente,
            DataDeAlteracao = DateTime.UtcNow
        };

        var camposParaValidar = new List<object?>
        {
            historicoDeStatusDeSolicitacao.Usuario,
            historicoDeStatusDeSolicitacao.Status,
            historicoDeStatusDeSolicitacao.StatusAnterior,
            historicoDeStatusDeSolicitacao.DataDeAlteracao
        };

        // Assert
        Assert.NotNull(camposParaValidar);
        Assert.NotEmpty(camposParaValidar);
        Assert.True(historicoDeStatusDeSolicitacao.Status == StatusDeSolicitacao.Cancelada);
    }

    [Fact(DisplayName = "Deve clonar corretamente um HistoricoDeStatusDeSolicitacao")]
    [Trait("Domain", "HistoricoDeStatusDeSolicitacao")]
    public void Clone_DeveClonarCorretamente()
    {
        // Arrange
        var usuario = UsuarioFixture.GerarUsuarioValido();

        var original = new HistoricoDeStatusDeSolicitacao(
            usuario
        )
        {
            Status = StatusDeSolicitacao.Cancelada,
            StatusAnterior = StatusDeSolicitacao.Pendente,
            DataDeAlteracao = DateTime.UtcNow
        };

        // Act
        var clone = original.Clone();

        // Assert
        Assert.NotSame(original, clone);
        Assert.Equal(original.Status, clone.Status);
        Assert.Equal(original.StatusAnterior, clone.StatusAnterior);
        Assert.Equal(original.StatusAnterior, clone.StatusAnterior);
    }
}
