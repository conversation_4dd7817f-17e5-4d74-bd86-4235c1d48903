<Project Sdk="Microsoft.NET.Sdk">

    <!-- <PERSON><PERSON><PERSON>ades específicas do projeto de teste -->
    <PropertyGroup>
        <IsPackable>false</IsPackable>
        <IsTestProject>true</IsTestProject>
    </PropertyGroup>

    <!-- Pacotes de teste -->
    <ItemGroup>
        <PackageReference Include="coverlet.collector"/>
        <PackageReference Include="Microsoft.NET.Test.Sdk"/>
        <PackageReference Include="xunit" />
        <PackageReference Include="xunit.runner.visualstudio"/>
    </ItemGroup>

    <ItemGroup>
        <Using Include="Xunit"/>
        <Using Include="Shouldly"/>
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\ONS.SINapse.UnitTest.Shared\ONS.SINapse.UnitTest.Shared.csproj" />
    </ItemGroup>

</Project>
