using System.Net;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using ONS.SINapse.CacheSync.Services;
using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Shared.Logs;
using ONS.SINapse.Shared.Settings;
using Refit;

namespace ONS.SINapse.CacheSync.Configurations;

public static class SinapseDadosConfiguration
{

    public static IServiceCollection AddSinapseDados(this IServiceCollection services, IConfiguration configuration)
    {
        services
            .AddSinapseDadosServices(configuration)
            .AddSinapseDadosDatasetService(configuration)
            .AddExternoDadosDatasetService()
            .AddQueryService();
        
        return services;
    }
    
    
    private static IServiceCollection AddSinapseDadosServices(this IServiceCollection services, IConfiguration configuration)
    {
        var syncSettings = configuration.GetSection(nameof(DataSyncSettings)).Get<DataSyncSettings>();
        ArgumentNullException.ThrowIfNull(syncSettings);

        var baseUrl = syncSettings.ServiceUri;
        ArgumentNullException.ThrowIfNull(baseUrl);
        
        var refitOptions = new RefitSettings(new NewtonsoftJsonContentSerializer(new JsonSerializerSettings()
        {
            ContractResolver = new CamelCasePropertyNamesContractResolver()
        }))
        {
            CollectionFormat = CollectionFormat.Multi
        };
        
        services
            .AddRefitClient<ISinapseDadosService>(refitOptions)
            .ConfigureHttpClient(options =>
            {
                options.BaseAddress = new Uri(baseUrl);
            })
            .ConfigurePrimaryHttpMessageHandler(() => new HttpClientHandler
            {
                ServerCertificateCustomValidationCallback = (_, cert, _, _) => cert != null,
                UseDefaultCredentials = true,
                AllowAutoRedirect = true,
                PreAuthenticate = true,
                Credentials = CredentialCache.DefaultNetworkCredentials,
                DefaultProxyCredentials = CredentialCache.DefaultNetworkCredentials
            })
            .AddAuthenticationRequestHandler<DataSyncSettings>(configuration)
            .AddLoggerRequestHandler<SinapseDadosLogs>(options =>
            {
                options.AddRequestLog(requestLogs =>
                {
                    requestLogs.SemContent = false;
                    requestLogs.SemHeader = false;
                });
                options.AddResposeLog(responseLogs =>
                {
                    responseLogs.SemContent = false;
                    responseLogs.SemHeader = false;
                });
            });
        
        return services;
    }

    private static IServiceCollection AddSinapseDadosDatasetService(this IServiceCollection services, IConfiguration configuration)
    {
        var syncSettings = configuration.GetSection(nameof(DataSyncSettings)).Get<DataSyncSettings>();
        ArgumentNullException.ThrowIfNull(syncSettings);

        var baseUrl = syncSettings.ServiceUri;
        ArgumentNullException.ThrowIfNull(syncSettings);

        var refitOptions = new RefitSettings(new NewtonsoftJsonContentSerializer(new JsonSerializerSettings()
        {
            ContractResolver = new CamelCasePropertyNamesContractResolver()
        }))
        {
            CollectionFormat = CollectionFormat.Multi
        };

        services
            .AddRefitClient<ISinapseDadosDatasetService>(refitOptions)
            .ConfigureHttpClient(options =>
            {
                options.BaseAddress = new Uri(baseUrl);
            })
            .ConfigurePrimaryHttpMessageHandler(() => new HttpClientHandler
            {
                ServerCertificateCustomValidationCallback = (_, cert, _, _) => cert != null,
                UseDefaultCredentials = true,
                AllowAutoRedirect = true,
                PreAuthenticate = true,
                Credentials = CredentialCache.DefaultNetworkCredentials,
                DefaultProxyCredentials = CredentialCache.DefaultNetworkCredentials
            })
            .AddAuthenticationRequestHandler<DataSyncSettings>(configuration)
            .AddLoggerRequestHandler<SinapseDadosLogs>(options =>
            {
                options.AddRequestLog(requestLogs =>
                {
                    requestLogs.SemContent = false;
                    requestLogs.SemHeader = false;
                });
                options.AddResposeLog(responseLogs =>
                {
                    responseLogs.SemContent = false;
                    responseLogs.SemHeader = false;
                });
            });
        
        return services;
    }

    private static IServiceCollection AddExternoDadosDatasetService(this IServiceCollection services)
    {
        services.AddScoped<IExternoDadosDatasetService, ExternoDadosDatasetService>()
            .Decorate<IExternoDadosDatasetService, ExternoDadosDatasetAgenteOnlineService>();
        
        return services;
    }
    
    
    private static IServiceCollection AddQueryService(this IServiceCollection services)
    {
        services.AddScoped(typeof(ISinapaseDadosQueryService<>), typeof(SinapaseDadosQueryService<>));

        services.AddScoped<ISinapseDadosDatasetQueryService, SinapseDadosDatasetQueryService>()
            .Decorate<ISinapseDadosDatasetQueryService, SinapseDadosAgentesDatasetQueryService>();
        
        return services;
    }
}