using ONS.SINapse.Business.IBusiness;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Helpers;
using System.Text.RegularExpressions;

namespace ONS.SINapse.CacheSync.Services;

public partial class ExternoDadosDatasetAgenteOnlineService : IExternoDadosDatasetService
{
    private readonly IExternoDadosDatasetService _externoDadosDatasetService;
    private readonly IAgenteOnlineBusiness _agenteOnlineBusiness;

    public ExternoDadosDatasetAgenteOnlineService(
        IExternoDadosDatasetService externoDadosDatasetService,
        IAgenteOnlineBusiness agenteOnlineBusiness)
    {
        _externoDadosDatasetService = externoDadosDatasetService;
        _agenteOnlineBusiness = agenteOnlineBusiness;
    }
    
    public async Task<IEnumerable<DatasetItemDto>> DatasetAsync(string endpoint, CancellationToken cancellationToken)
    {
        var dados = await _externoDadosDatasetService.DatasetAsync(endpoint, cancellationToken).ConfigureAwait(false);
        
        var datasets = dados.ToList();

        var definirStatus = datasets.ToDictionary(x => x.Id, x => x.ObterDefinirStatus());

        if (definirStatus.Values.All(string.IsNullOrEmpty)) return dados;

        var datasetsOnline = await AgentesOnlineAsync(datasets, cancellationToken);

        var datasetsInativos = await AgentesInativosAsync(
            datasets.Where(x => !datasetsOnline.Select(s => s.ObterDefinirStatus()).Contains(x.ObterDefinirStatus())),
            cancellationToken
        );

        var agentesProcessados =
            Enumerable.Empty<string>()
                .Union(datasetsOnline.Select(x => x.ObterDefinirStatus()))
                .Union(datasetsInativos.Select(x => x.ObterDefinirStatus()))
                .ToList();
        var datasetsOffline = datasets
            .Where(x => !agentesProcessados.Contains(x.ObterDefinirStatus()))
            .Select(x =>
            {
                x.Label += " - [Offline]";
                return x;
            })
            .ToList();

        return
            Enumerable.Empty<DatasetItemDto>()
                .Union(datasetsOnline)
                .Union(datasetsOffline)
                .Union(datasetsInativos);
    }
    
    private async Task<List<DatasetItemDto>> AgentesOnlineAsync(IEnumerable<DatasetItemDto> datasets,
        CancellationToken cancellationToken)
    {
        var agentesOnline = await _agenteOnlineBusiness.ObterAgentesOnlineAsync(cancellationToken);

        return datasets
            .Join(agentesOnline,
                inner => inner.ObterDefinirStatus(),
                join => join.Codigo,
                (inner, join) =>
                {
                    if (inner.Local is null)
                    {
                        inner.Label += " - [Online]";
                        return inner;
                    }

                    if (join.Locais.Length == 0)
                    {
                        inner.Label += " - [Online]";
                        return inner;
                    }

                    if (join.Locais.Contains(inner.Local.Code))
                    {
                        inner.Label += " - [Online]";
                        return inner;
                    }
                    
                    inner.Label += " - [Offline]";
                    return inner;
                    
                })
                .OrderBy(x => TrimStringRegex().Replace(x.Label, " ").Trim(), NaturalSortHelper.Instance)
                .ToList();
    }

    private async Task<List<DatasetItemDto>> AgentesInativosAsync(IEnumerable<DatasetItemDto> datasets, CancellationToken cancellationToken)
    {
        var datasetsList = datasets.ToList();
        
        var agentesInativos = 
            await _agenteOnlineBusiness.BuscarAgentesInativosAsync(
                    datasetsList.Where(x => !string.IsNullOrEmpty(x.ObterDefinirStatus()))
                        .Select(x => x.ObterDefinirStatus()!)
                        .ToArray()
                    , cancellationToken)
                .ConfigureAwait(false);
        
        return  
            datasetsList
                .Join(agentesInativos,
                    inner => inner.ObterDefinirStatus(),
                    join => join,
                    (inner, _) =>
                    {
                        inner.Label += " - [Inativo]";
                        return inner;
                    })
                .OrderBy(x => TrimStringRegex().Replace(x.Label, " ").Trim(), NaturalSortHelper.Instance)
                .ToList();
    }

    private bool _disposed;
    protected virtual void Dispose(bool disposing)
    {
        if (_disposed) return;

        if (disposing)
        {
            _externoDadosDatasetService.Dispose();
            _agenteOnlineBusiness.Dispose();
        }

        _disposed = true;
    }
    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    [GeneratedRegex(@"\s+")]
    private static partial Regex TrimStringRegex();
}