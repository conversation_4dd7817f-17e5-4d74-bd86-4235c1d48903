using ONS.SINapse.Shared.DTO;
using Refit;

namespace ONS.SINapse.CacheSync.Services;

public interface IExternoDadosDatasetApiService : IDisposable
{
    [Get("")]
    public Task<ApiResponse<IEnumerable<DatasetItemDto>>> DatasetAsync([Query] Dictionary<string, string?> query, CancellationToken cancellationToken);
}

public interface IExternoDadosDatasetService : IDisposable
{
    Task<IEnumerable<DatasetItemDto>> DatasetAsync(string endpoint, CancellationToken cancellationToken);
}