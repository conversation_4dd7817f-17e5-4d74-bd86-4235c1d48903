using System.Net.Http.Headers;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Shared.Services;
using ONS.SINapse.Shared.Services.Caching;
using ONS.SINapse.Shared.Settings;
using Refit;

namespace ONS.SINapse.CacheSync.Services;

public class ExternoDadosDatasetService : IExternoDadosDatasetService
{
    private readonly ICacheService _cacheService;
    private readonly ILogger<ExternoDadosDatasetService> _logger;
    private readonly IAuthenticationProvider<DataSyncSettings> _authenticationProvider;

    public ExternoDadosDatasetService(ICacheService cacheService,
        ILogger<ExternoDadosDatasetService> logger,
        IAuthenticationProvider<DataSyncSettings> authenticationProvider)
    {
        _cacheService = cacheService;
        _logger = logger;
        _authenticationProvider = authenticationProvider;
    }

    public async Task<IEnumerable<DatasetItemDto>> DatasetAsync(string endpoint, CancellationToken cancellationToken)
    {
        try
        {
            return await InternalDatasetAsync(endpoint, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao buscar dados na api externa");

            return await _cacheService.GetAsync<IEnumerable<DatasetItemDto>>(endpoint, cancellationToken).ConfigureAwait(false) ??
                   Enumerable.Empty<DatasetItemDto>();
        }
    }

    private async Task<IEnumerable<DatasetItemDto>> InternalDatasetAsync(string endpoint, CancellationToken cancellationToken)
    {
        using var builder = new ExternalApiBuilder(endpoint, () => _authenticationProvider.ObterToken());

        using var response = await builder.SendAsync(cancellationToken).ConfigureAwait(false);

        await response.EnsureSuccessStatusCodeAsync().ConfigureAwait(false);

        var result = (response.Content ?? Enumerable.Empty<DatasetItemDto>()).ToList();

        await _cacheService.SetAsync(endpoint, result, cancellationToken).ConfigureAwait(false);
        return result;
    }

    private class ExternalApiBuilder : IDisposable
    {
        private readonly Uri _endpoint;
        private readonly Func<Task<PopAutorizacaoTokenDto>> _auth;
        private IExternoDadosDatasetApiService? _service;

        public ExternalApiBuilder(string endpoint, Func<Task<PopAutorizacaoTokenDto>> auth)
        {
            _endpoint = new Uri(endpoint);
            _auth = auth;
        }

        private async Task Configure()
        {
            var token = await _auth().ConfigureAwait(false);

            var httpClient = new HttpClient
            {
                BaseAddress = _endpoint,
                DefaultRequestHeaders = { Authorization = new AuthenticationHeaderValue("Bearer", token.AccessToken) }
            };

            var options = new RefitSettings(new NewtonsoftJsonContentSerializer(new JsonSerializerSettings
            {
                ContractResolver = new CamelCasePropertyNamesContractResolver()
            }))
            {
                CollectionFormat = CollectionFormat.Multi
            };
        
            _service = RestService.For<IExternoDadosDatasetApiService>(httpClient, options);
        }

        public async Task<ApiResponse<IEnumerable<DatasetItemDto>>> SendAsync(CancellationToken cancellationToken)
        {
            await Configure().ConfigureAwait(false);

            var query = _endpoint.SplitDictionaryQueryString();

            return await _service!.DatasetAsync(query, cancellationToken).ConfigureAwait(false);
        }

        private bool _disposed;
        protected virtual void Dispose(bool disposing)
        {
            if (_disposed) return;

            if (disposing)
            {
                _service?.Dispose();
            }

            _disposed = true;
        }
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }
    }


    private bool _disposed;
    protected virtual void Dispose(bool disposing)
    {
        if (_disposed) return;

        _disposed = true;
    }
    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }
}