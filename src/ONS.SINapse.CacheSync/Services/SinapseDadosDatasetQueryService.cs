using System.Linq.Expressions;
using System.Reflection;
using Microsoft.Extensions.Logging;
using ONS.SINapse.Shared.CustomAttributes;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Services.Caching;

namespace ONS.SINapse.CacheSync.Services;

public class SinapseDadosDatasetQueryService : ISinapseDadosDatasetQueryService
{
    private readonly ISinapseDadosDatasetService _datasetService;
    private readonly ICacheService _cacheService;
    private readonly ILogger<SinapseDadosDatasetQueryService> _logger;
    private readonly Notificacao _notification;

    public SinapseDadosDatasetQueryService(
        ISinapseDadosDatasetService datasetService, 
        ICacheService cacheService,
        ILogger<SinapseDadosDatasetQueryService> logger)
    {
        _datasetService = datasetService;
        _cacheService = cacheService;
        _logger = logger;
        _notification = new Notificacao();
    }
    
    public async Task<TResult?> GetDatasetAsync<TResult>(Expression<Func<ISinapseDadosDatasetService, Task<TResult>>> execute, CancellationToken cancellationToken)
        where TResult : class
    {
        if (IgnoreCache(execute))
            return await ExecuteAsync(execute);

        var identificador = CollectionIdentificador(execute);
        var cacheKey = GetKey(execute);

        TResult? dados;
        var versao = await VersaoEstaAtualizadaAsync(identificador);
        
        if (versao.EstaAtualizado)
        {
            dados = await _cacheService.GetAsync<TResult>(cacheKey, cancellationToken);

            if (dados is not null) return dados;

            _logger.LogWarning("[GetDatasetAsync] Dataset não localizado no cache. {ChaveCache}", cacheKey);
            dados = await ExecuteAsync(execute);

            if (!_notification.HasNotifications && dados is not null)
            {
                await _cacheService.SetAsync(cacheKey, dados, cancellationToken);
            }
            
            return dados;
        }
        
        if(versao.Erro) 
            return await _cacheService.GetAsync<TResult>(cacheKey, cancellationToken);

        dados = await ExecuteAsync(execute);

        if (_notification.HasNotifications || dados is null)
        {
            _logger.LogError("[GetDatasetAsync] Erro ao buscar dados na api de dados. {ChaveCache}", versao.ChaveCache);
            return await _cacheService.GetAsync<TResult>(cacheKey, cancellationToken);
        }
        
        await _cacheService.SetAsync(cacheKey, dados, cancellationToken);
        await _cacheService.SetAsync(versao.ChaveCache, versao.VersaoApi, cancellationToken);
        _logger.LogInformation("[GetDatasetAsync] Dataset atualizado no cache. {ChaveCache}", cacheKey);
        
        return dados;
    }
    
    
    private async Task<VersaoDadoDto> VersaoEstaAtualizadaAsync(string[] identificadores)
    {
        var identificador = IntegracaoSyncAttribute.IdentificadorUnico(identificadores);
        var chave = "versao_" + identificador;

        Dictionary<string, string>? versaoLocal;
        Dictionary<string, string>? versaoApi;
        
        try
        {
            var timeout = TimeSpan.FromSeconds(10);
            using var cts = new CancellationTokenSource();
            
            cts.CancelAfter(timeout);
            
            versaoLocal = await _cacheService.GetAsync<Dictionary<string, string>>(chave, cts.Token)
                .ConfigureAwait(false);
            
            versaoApi = await _datasetService.GetLastVersion(identificadores, cts.Token)
                .ConfigureAwait(false);

            if (versaoApi is null)
            {
                return new VersaoDadoDto(chave, versaoLocal ?? new Dictionary<string, string>(), false, true);
            }
            
            if (versaoLocal is not null && versaoLocal.Count > 0 && versaoApi.Count > 0
                && versaoApi.All(x => versaoLocal.ContainsKey(x.Key) && versaoLocal[x.Key] == x.Value))
            {
                return new VersaoDadoDto(chave, versaoLocal, true);
            }

            return new VersaoDadoDto(chave, versaoApi, false);
            
        }
        catch (Exception exception)
        {
            _logger.LogError(exception,"[VersaoEstaAtualizadaAsync] - Erro ao buscar versão dos dados.");
            return new VersaoDadoDto(chave, new Dictionary<string, string>(), false, true);
        }
    }
    
    
    private async Task<TResult?> ExecuteAsync<TResult>(Expression<Func<ISinapseDadosDatasetService, Task<TResult>>> execute)
        where TResult : class
    {
        try
        {
            var caller = execute.Compile();
            return await caller(_datasetService);
        }
        catch (Exception)
        {
            _notification.AddNotification($"Erro ao buscar dados na api de dados. {execute.Name ?? execute.Body.ToString()}");
            return null;
        }
    }
    
    private static string GetKey<TResult>(Expression<Func<ISinapseDadosDatasetService, TResult>> execute) where TResult : class
    {
        if (execute.Body is not MethodCallExpression methodCall) return execute.Body.ToString();
        
        // Nome do método
        var methodName = methodCall.Method.Name;

        // Parâmetros do método
        var methodParameters = methodCall.Method.GetParameters()[..^1]; // Remove CancellationToken se necessário

        // Valores dos argumentos passados para o método
        var argumentValues = methodCall.Arguments;

        // Monta a chave
        var key = methodParameters
            .Select((param, index) => $"{param.Name}:{GetValue(argumentValues[index])}")
            .Aggregate(methodName, (aggregate, current) => $"{aggregate}:{current}");

        return key;
    }

    private static string[] CollectionIdentificador<TResult>(Expression<Func<ISinapseDadosDatasetService, TResult>> execute)
        where TResult : class
    {
        if (execute.Body is not MethodCallExpression methodCall) return Array.Empty<string>();
        var apiVersion = methodCall.Method.GetCustomAttribute<IntegracaoSyncAttribute>();
        return apiVersion is null ? Array.Empty<string>() : apiVersion.Identificadores;
    }
    
    private static string GetValue(Expression expression)
    {
        if (expression is ConstantExpression constantExpression)
        {
            return constantExpression.Value?.ToString() ?? string.Empty;
        }

        var objectMember = Expression.Convert(expression, typeof(object));
        var getterLambda = Expression.Lambda<Func<object?>>(objectMember);
        var getter = getterLambda.Compile();
        return FormatValue(getter());
    }
    
    private static string FormatValue(object? value)
    {
        return value switch
        {
            null => string.Empty,
            string[] strings => string.Join(",", strings),
            _ => value.ToString() ?? string.Empty
        };
    }

    private static bool IgnoreCache<TResult>(Expression<Func<ISinapseDadosDatasetService, TResult>> execute)
    {
        if (execute.Body is not MethodCallExpression methodCall) return true;

        var ignore = 
            methodCall.Method.GetCustomAttribute<IntegracaoSyncIgnoreCacheAttribute>();

        if (ignore is not null) return true;
        
        var apiVersion = methodCall.Method.GetCustomAttribute<IntegracaoSyncAttribute>();
        
        return apiVersion is null;
    }
    

    private bool _disposed;
    protected virtual void Dispose(bool disposing)
    {
        if (_disposed) return;

        if (disposing)
        {
            _datasetService.Dispose();
            _notification.Dispose();
        }

        _disposed = true;
    }
    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    private class Notificacao : IDisposable
    {
        private readonly List<string> _notificacoes = new();

        public bool HasNotifications => _notificacoes.Count > 0;
        
        public void AddNotification(string mensagem)
        {
            _notificacoes.Add(mensagem);
        }

        private bool _disposed;
        protected virtual void Dispose(bool disposing)
        {
            if (_disposed) return;

            if (disposing)
            {
                _notificacoes.Clear();
            }

            _disposed = true;
        }
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }
    }
}