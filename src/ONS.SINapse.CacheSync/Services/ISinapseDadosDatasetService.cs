using ONS.SINapse.Shared.CustomAttributes;
using ONS.SINapse.Shared.DTO;
using Refit;

namespace ONS.SINapse.CacheSync.Services;

public interface ISinapseDadosDatasetService : IDisposable
{
    [Get("/api/agentes/dataset")]
    [IntegracaoSync("c_agente")]
    Task<ICollection<DatasetItemDto>> GetAgenteDataset([Query] string codigoDoCentro, CancellationToken cancellationToken);
    
    [Get("/api/local-de-operacao/dataset")]
    [IntegracaoSync("c_equipamento", "c_usina", "c_elo", "c_conjuntousina")]
    Task<ICollection<LocalDeOperacaoDataset>> GetLocalDeOperacaoDataset([Query] string codigoDoCentroAgente, CancellationToken cancellationToken);
    
    [Get("/api/comunicados/dataset/areas-eletricas")]
    [IntegracaoSync("c_agente", "c_areaeletrica")]
    Task<ICollection<AreaEletricaComunicadoDto>> GetComunicadoAreaEletrica([Query] string[] centros, CancellationToken cancellationToken);
    
    [Get("/api/equipamentos/dataset/transformador")]
    [IntegracaoSync("c_equipamento")]
    Task<ICollection<DatasetItemDto>> GetEquipamentoDatasetTransformador([Query] string codigoDoCentro, CancellationToken cancellationToken);

    [Get("/api/equipamentos/dataset/transformacao")]
    [IntegracaoSync("c_equipamento")]
    Task<ICollection<DatasetItemDto>> GetEquipamentoDatasetTransformacao([Query] string codigoDoCentro, CancellationToken cancellationToken);

    [Get("/api/equipamentos/dataset/compensador")]
    [IntegracaoSync("c_equipamento")]
    Task<ICollection<DatasetItemDto>> GetEquipamentoDatasetCompensador([Query] string codigoDoCentro, CancellationToken cancellationToken);
    
    [Get("/api/equipamentos/dataset/instalacao-compensador")]
    [IntegracaoSync("c_equipamento")]
    Task<ICollection<DatasetItemDto>> GetEquipamentoDatasetInstalacaoCompensador([Query] string codigoDoCentro, CancellationToken cancellationToken);

    [Get("/api/equipamentos/dataset/banco-capacitor")]
    [IntegracaoSync("c_equipamento")]
    Task<ICollection<DatasetItemDto>> GetEquipamentoDatasetBancoDeCapacitor([Query] string codigoDoCentro, CancellationToken cancellationToken);

    [Get("/api/equipamentos/dataset/reator")]
    [IntegracaoSync("c_equipamento")]
    Task<ICollection<DatasetItemDto>> GetEquipamentoDatasetReator([Query] string codigoDoCentro, CancellationToken cancellationToken);

    [Get("/api/equipamentos/dataset/elo")]
    [IntegracaoSync("c_elo")]
    Task<ICollection<DatasetItemDto>> GetEquipamentoDatasetElo([Query] string codigoDoCentro, CancellationToken cancellationToken);

    [Get("/api/horarios/dataset")]
    [IntegracaoSyncIgnoreCache]
    Task<ICollection<DatasetItemDto>> GetHorarioPatamarDataset(CancellationToken cancellationToken);

    [Get("/api/usinas/dataset/unidades-geradoras")]
    [IntegracaoSync("c_usina")]
    Task<ICollection<DatasetItemDto>> GetUsinaConjuntoUsinaDatasetUnidadesGeradoras([Query] string codigoDoCentro, CancellationToken cancellationToken);

    [Get("/api/usinas/dataset/cag")]
    [IntegracaoSync("c_usina")]
    Task<ICollection<DatasetItemDto>> GetUsinaConjuntoUsinaDatasetCag([Query] string codigoDoCentro, [Query] string[]? codigoDoTipoDeFonte, CancellationToken cancellationToken);

    [Get("/api/usinas/dataset/usina")]
    [IntegracaoSync("c_usina")]
    Task<ICollection<DatasetItemDto>> GetUsinaConjuntoUsinaDatasetUsina([Query] string codigoDoCentro, [Query] string[]? codigoDoTipoDeFonte, CancellationToken cancellationToken);

    [Get("/api/usinas/dataset/conjunto-usina")]
    [IntegracaoSync("c_conjuntousina")]
    Task<ICollection<DatasetItemDto>> GetUsinaConjuntoUsinaDatasetConjuntoUsina([Query] string codigoDoCentro, [Query] string? codigoDoTipoDeFonte, CancellationToken cancellationToken);
    
    [Get("/api/atualizacao-dados/listar-ultima-atualizacao")]
    Task<Dictionary<string, string>?> GetLastVersion([Query] string[] collection, CancellationToken cancellationToken);
}