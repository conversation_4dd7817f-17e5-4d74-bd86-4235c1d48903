using ONS.SINapse.Shared.DTO;
using Refit;

namespace ONS.SINapse.CacheSync.Services;

public interface ISinapseDadosDatasetService : IDisposable
{
    [Get("/api/dados/locais-de-operacao")]
    Task<ICollection<LocalDeOperacaoDataset>> GetLocalDeOperacaoDataset([Query] string? query, CancellationToken cancellationToken);
    
    [Get("/api/dados/comunicados")]
    Task<ICollection<AreaEletricaComunicadoDto>> GetComunicadoAreaEletrica([Query] string? query, CancellationToken cancellationToken);

    [Get("/api/dataset/solicitacao/{view}")]
    Task<ICollection<DatasetItemDto>> GetSolicitacaoDataset([AliasAs("view")] string view, [Query] string? query, CancellationToken cancellationToken);

    [Get("/api/views")]
    Task<ICollection<DatasetViewsDot>> GetDatasetViews(CancellationToken cancellationToken);
    
    [Post("/api/dataset/solicitacao/{view}")]
    Task<ICollection<DatasetItemDto>> ValidarSqlDataset([AliasAs("view")] string viewName, [Body] SqlQueryDto sqlQuery, [Query] string? query = null, CancellationToken cancellationToken = default);
}