using ONS.SINapse.Business.IBusiness;
using ONS.SINapse.Shared.DTO;
using System.Linq.Expressions;

namespace ONS.SINapse.CacheSync.Services;

public class SinapseDadosAgentesDatasetQueryService : ISinapseDadosDatasetQueryService
{
    private readonly ISinapseDadosDatasetQueryService _sinapseDadosDatasetQueryService;
    private readonly IAgenteOnlineBusiness _agenteOnlineBusiness;

    public SinapseDadosAgentesDatasetQueryService(
        ISinapseDadosDatasetQueryService sinapseDadosDatasetQueryService,
        IAgenteOnlineBusiness agenteOnlineBusiness)
    {
        _sinapseDadosDatasetQueryService = sinapseDadosDatasetQueryService;
        _agenteOnlineBusiness = agenteOnlineBusiness;
    }
    
    public async Task<TResult?> GetDatasetAsync<TResult>(Expression<Func<ISinapseDadosDatasetService, Task<TResult>>> execute, CancellationToken cancellationToken) 
        where TResult : class
    {
        var dados = await _sinapseDadosDatasetQueryService.GetDatasetAsync(execute, cancellationToken);

        if (dados is null) return dados;

        if (dados is not ICollection<DatasetItemDto> datasets) return dados;

        var definirStatus = datasets.ToDictionary(x => x.Id, x => x.ObterDefinirStatus());

        if (definirStatus.Values.All(string.IsNullOrEmpty)) return dados;

        var datasetsOnline = await AgentesOnlineAsync(datasets, cancellationToken);
        
        var agentesOnline = datasetsOnline
            .Select(dto => definirStatus[dto.Id])
            .Distinct()
            .ToList();

        var datasetsAgentesNaoOnline = datasets.Where(x => !agentesOnline.Contains(definirStatus[x.Id])).ToList();
        
        var datasetsInativos = await AgentesInativosAsync(
            datasetsAgentesNaoOnline,
            cancellationToken
        );

        var agentesProcessados =
            Enumerable.Empty<string>()
                .Union(datasetsOnline.Select(x => definirStatus[x.Id]))
                .Union(datasetsInativos.Select(x => definirStatus[x.Id]));

        var datasetsOffline =
            datasets
            .Where(x => !agentesProcessados.Contains(definirStatus[x.Id]))
            .Select(x =>
            {
                x.Label += " - [Offline]";
                return x;
            })
            .ToList();

        return
            Enumerable.Empty<DatasetItemDto>()
                    .Union(datasetsOnline)
                    .Union(datasetsOffline)
                    .Union(datasetsInativos)
                    .ToList()
                as TResult;
    }

    private async Task<List<DatasetItemDto>> AgentesOnlineAsync(IEnumerable<DatasetItemDto> datasets,
        CancellationToken cancellationToken)
    {
        var agentesOnline = await _agenteOnlineBusiness.ObterAgentesOnlineAsync(cancellationToken);

        return datasets
            .Join(agentesOnline,
                inner => inner.ObterDefinirStatus(),
                join => join.Codigo,
                (inner, join) =>
                {
                    if (inner.Local is null)
                    {
                        inner.Label += " - [Online]";
                        return inner;
                    }

                    if (join.Locais.Length == 0)
                    {
                        inner.Label += " - [Online]";
                        return inner;
                    }

                    if (join.Locais.Contains(inner.Local.Code))
                    {
                        inner.Label += " - [Online]";
                        return inner;
                    }
                    
                    inner.Label += " - [Offline]";
                    return inner;
                    
                })
                .ToList();
    }

    private async Task<List<DatasetItemDto>> AgentesInativosAsync(IEnumerable<DatasetItemDto> datasets, CancellationToken cancellationToken)
    {
        var datasetsList = datasets.ToList();
        
        var agentesInativos = 
            await _agenteOnlineBusiness.BuscarAgentesInativosAsync(
                    datasetsList.Where(x => !string.IsNullOrEmpty(x.ObterDefinirStatus()))
                        .Select(x => x.ObterDefinirStatus()!)
                        .ToArray()
                    , cancellationToken)
                .ConfigureAwait(false);
        
        return  
            datasetsList
                .Join(agentesInativos,
                    inner => inner.ObterDefinirStatus(),
                    join => join,
                    (inner, _) =>
                    {
                        inner.Label += " - [Inativo]";
                        return inner;
                    })
                .ToList();
    }

    private bool _disposed;
    protected virtual void Dispose(bool disposing)
    {
        if (_disposed) return;

        if (disposing)
        {
            _sinapseDadosDatasetQueryService.Dispose();
            _agenteOnlineBusiness.Dispose();
        }

        _disposed = true;
    }
    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }
}