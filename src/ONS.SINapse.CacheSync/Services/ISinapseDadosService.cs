using ONS.SINapse.CacheSync.Filters.Dtos;
using ONS.SINapse.Entities.Entities.DadosCadastrais;
using ONS.SINapse.Shared.CustomAttributes;
using Refit;

namespace ONS.SINapse.CacheSync.Services;

public interface ISinapseDadosService : IDisposable
{
    [Get("/api/dados/agente")]
    [IntegracaoSync("c_agente")]
    Task<IEnumerable<Agente>> GetDadosAgente([Query] DadosAgenteFilter filter, CancellationToken cancellationToken);

     [Get("/api/atualizacao-dados/listar-ultima-atualizacao")]
    Task<Dictionary<string, string>?> GetLastVersion([Query] string[] collection, CancellationToken cancellationToken);

    [Get("/api/dados/usina")]
    [IntegracaoSync("c_usina")]
    Task<IEnumerable<Usina>> GetDadosUsina([Query] DadosUsinaFilter filter, CancellationToken cancellationToken);

    [Get("/api/dados/conjunto-usina")]
    [IntegracaoSync("c_conjuntousina")]
    Task<IEnumerable<ConjuntoDeUsina>> GetDadosConjuntoUsina([Query] DadosConjuntoUsinaFilter filter, CancellationToken cancellationToken);
}