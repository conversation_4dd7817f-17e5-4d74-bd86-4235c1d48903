using System.Linq.Expressions;
using System.Reflection;
using Microsoft.Extensions.Logging;
using ONS.SINapse.Entities.Entities.Base;
using ONS.SINapse.Shared.CustomAttributes;
using ONS.SINapse.Shared.DTO;
using ONS.SINapse.Shared.Extensions;
using ONS.SINapse.Shared.Services.Caching;

namespace ONS.SINapse.CacheSync.Services;

public interface ISinapaseDadosQueryService<TDado> : IDisposable where TDado : EntidadeDadosCadastrais
{
    Task<IEnumerable<TDado>> GetAsync(Expression<Func<TDado, bool>> predicate, CancellationToken cancellationToken);
    
    Task<IEnumerable<TDado>> GetAsync(CancellationToken cancellationToken);

    Task<IEnumerable<TDado>> GetAsync(Expression<Func<TDado, bool>> predicate, IDictionary<string, bool> sortBy, CancellationToken cancellationToken);
    Task<TDado?> GetOneAsync(Expression<Func<TDado, bool>> predicate, CancellationToken cancellationToken);
    Task<bool> AnyAsync(Expression<Func<TDado, bool>> predicate, CancellationToken cancellationToken);
}

public class SinapaseDadosQueryService<TDado> : ISinapaseDadosQueryService<TDado> where TDado : EntidadeDadosCadastrais
{
    private readonly ISinapseDadosService _sinapseDadosService;
    private readonly ILogger<SinapaseDadosQueryService<TDado>> _logger;
    private readonly ICacheService _cacheService;
    private readonly Notificacao _notification;

    private readonly IntegracaoSyncAttribute _syncDadoInfo;

    public SinapaseDadosQueryService(
        ISinapseDadosService sinapseDadosService,
        ICacheService cacheService,
        ILogger<SinapaseDadosQueryService<TDado>> logger)
    {
        _sinapseDadosService = sinapseDadosService;
        _logger = logger;
        _cacheService = cacheService;
        _notification = new Notificacao();
        _syncDadoInfo = SyncDadoAttribute();
    }
    
    public async Task<IEnumerable<TDado>> GetAsync(Expression<Func<TDado, bool>> predicate, CancellationToken cancellationToken)
    {
        await ValidarCacheAsync<IEnumerable<TDado>>(cancellationToken);
        
        var dados = await _cacheService.GetAsync<IEnumerable<TDado>>(_syncDadoInfo.IdentificadorUnico(), cancellationToken);

        return dados?.Where(predicate.Compile()).ToList() ?? new List<TDado>();
    }

    public async Task<IEnumerable<TDado>> GetAsync(CancellationToken cancellationToken)
    {
        await ValidarCacheAsync<IEnumerable<TDado>>(cancellationToken);
        
        var dados = await _cacheService.GetAsync<IEnumerable<TDado>>(_syncDadoInfo.IdentificadorUnico(), cancellationToken);

        return dados ?? new List<TDado>();
    }

    public async Task<IEnumerable<TDado>> GetAsync(Expression<Func<TDado, bool>> predicate, IDictionary<string, bool> sortBy, CancellationToken cancellationToken)
    {
        await ValidarCacheAsync<IEnumerable<TDado>>(cancellationToken);
        
        var dados = await _cacheService.GetAsync<IEnumerable<TDado>>(_syncDadoInfo.IdentificadorUnico(), cancellationToken);

        if (dados is null) return new List<TDado>();

        return dados
            .Where(predicate.Compile())
            .OrderBy(sortBy);
    }

    public async Task<TDado?> GetOneAsync(Expression<Func<TDado, bool>> predicate, CancellationToken cancellationToken)
    {
        await ValidarCacheAsync<IEnumerable<TDado>>(cancellationToken);
        
        var dados = await _cacheService.GetAsync<IEnumerable<TDado>>(_syncDadoInfo.IdentificadorUnico(), cancellationToken);
        
        return dados?.FirstOrDefault(predicate.Compile());
    }

    public Task<bool> AnyAsync(Expression<Func<TDado, bool>> predicate, CancellationToken cancellationToken)
    {
        return GetOneAsync(predicate, cancellationToken).ContinueWith(x => x.Result is not null, cancellationToken);
    }

    private async Task ValidarCacheAsync<TResult>(CancellationToken cancellationToken) where TResult : class
    {
        var versaoDado = await EstaAtualizadoAsync();
        
        if (versaoDado.EstaAtualizado)
        {
            return;   
        }
        
        if(versaoDado.Erro) return;
        
        var dados = await Invocation<TResult>(cancellationToken, null);
        
        if(_notification.HasNotifications || dados is null) return;
        
        await _cacheService.SetAsync(_syncDadoInfo.IdentificadorUnico(), dados, cancellationToken);
        await _cacheService.SetAsync(versaoDado.ChaveCache, versaoDado.VersaoApi, cancellationToken);
    }


    #region Private Methods

    private async Task<TResult?> Invocation<TResult>(CancellationToken cancellationToken, params object?[]? parametros) where TResult : class
    {
        var method = typeof(ISinapseDadosService)
            .GetMethods()
            .FirstOrDefault(x => {
                var attr = x.GetCustomAttribute<IntegracaoSyncAttribute>();
                if (attr is null) return false;

                return attr.IdentificadorUnico() == _syncDadoInfo.IdentificadorUnico();
            });

        if (method is null)
            throw new InvalidOperationException(
                $"Método de Api de dados não encontrado, certifique-se de o método possui o atributo '{nameof(IntegracaoSyncAttribute)}'");

        if (parametros is null)
        {
            var methodParameters = method.GetParameters();
            parametros = new object[methodParameters.Length];
            for (var i = 0; i < methodParameters.Length; i++)
            {
                parametros[i] = null;
                if (i == methodParameters.Length-1)
                    parametros[i] = cancellationToken;
            }
        }
        
        var invocation = method.Invoke(_sinapseDadosService, parametros) as Task<TResult>;

        if (invocation is null)
            throw new InvalidOperationException("Método ou Api não encontrados, não foi possivel efetuar a chamada da api.");

        try
        {
            return await invocation;
        }
        catch (Exception exception)
        {
            _logger.LogError(exception, "[Invocation] - Erro ao chamar api de dados.");
            _notification.AddNotification($"[Invocation] - Erro ao chamar api de dados. {method.Name}");
            throw;
        }
    }

    private async Task<VersaoDadoDto> EstaAtualizadoAsync()
    {
        var chave = "versao_" + _syncDadoInfo.IdentificadorUnico();

        Dictionary<string, string>? versaoLocal;
        Dictionary<string, string>? versaoApi;
        
        try
        {
            var timeout = TimeSpan.FromSeconds(10);
            using var cts = new CancellationTokenSource();
            
            cts.CancelAfter(timeout);
            
            versaoLocal = await _cacheService.GetAsync<Dictionary<string, string>>(chave, cts.Token)
                .ConfigureAwait(false);
            
            versaoApi = await _sinapseDadosService.GetLastVersion(_syncDadoInfo.Identificadores, cts.Token)
                .ConfigureAwait(false);

            if (versaoApi is null || versaoApi.Count == 0)
            {
                return new VersaoDadoDto(chave, versaoLocal ?? new Dictionary<string, string>(), false, true);
            }

            return CompararVersao(versaoApi, versaoLocal) ? 
                new VersaoDadoDto(chave, versaoApi, true) : 
                new VersaoDadoDto(chave, versaoApi, false);
        }
        catch (Exception exception)
        {
            _logger.LogError(exception,"[VersaoEstaAtualizadaAsync] - Erro ao buscar versão dos dados.");
            return new VersaoDadoDto(chave, new Dictionary<string, string>(), false, true);
        }
    }
    
    private static bool CompararVersao(Dictionary<string, string> versaoApi, Dictionary<string, string>? versaoLocal)
    {
        if (versaoLocal is null || versaoApi .Count == 0) return false;
        
        return versaoLocal.Count > 0 && versaoApi
            .All(x => versaoLocal.ContainsKey(x.Key) && versaoLocal[x.Key] == x.Value);
    }

    private static IntegracaoSyncAttribute SyncDadoAttribute()
    {
        var attr = typeof(TDado)
            .GetCustomAttribute<IntegracaoSyncAttribute>();
        
        if(attr is null)
            throw new InvalidOperationException(
                $"Chave do cache não encontrado para entidade {nameof(TDado)}, certifique-se de possuir o atributo '{nameof(IntegracaoSyncAttribute)}'");
        return attr;
    }

    #endregion


    private bool _disposed;
    protected virtual void Dispose(bool disposing)
    {
        if (_disposed) return;

        if (disposing)
        {
            _sinapseDadosService.Dispose();
            _notification.Dispose();
        }

        _disposed = true;
    }
    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    private class Notificacao : IDisposable
    {
        private readonly List<string> _notificacoes = [];

        public bool HasNotifications => _notificacoes.Count > 0;
        
        public void AddNotification(string mensagem)
        {
            _notificacoes.Add(mensagem);
        }

        private bool _disposed;
        protected virtual void Dispose(bool disposing)
        {
            if (_disposed) return;

            if (disposing)
            {
                _notificacoes.Clear();
            }

            _disposed = true;
        }
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }
    }
}



