<Project Sdk="Microsoft.NET.Sdk">

    <!-- Propriedades específicas do projeto -->
    <PropertyGroup>
        <!-- Propriedades específicas serão herdadas do Directory.Build.props -->
    </PropertyGroup>

    <!-- Pacotes específicos do CacheSync -->
    <ItemGroup>
        <PackageReference Include="Microsoft.Extensions.Caching.Memory" />
        <PackageReference Include="Microsoft.Extensions.Caching.StackExchangeRedis" />
        <PackageReference Include="StackExchange.Redis" />
    </ItemGroup>

    <!-- Referências de projeto -->
    <ItemGroup>
      <ProjectReference Include="..\ONS.SINapse.Business\ONS.SINapse.Business.csproj" />
      <ProjectReference Include="..\ONS.SINapse.Entities\ONS.SINapse.Entities.csproj" />
      <ProjectReference Include="..\ONS.SINapse.Shared\ONS.SINapse.Shared.csproj" />
    </ItemGroup>

</Project>
