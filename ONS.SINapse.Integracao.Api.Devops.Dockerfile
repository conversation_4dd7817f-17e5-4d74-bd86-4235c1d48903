FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
# Adicionar certificados de CA para RDS
ADD https://truststore.pki.rds.amazonaws.com/global/global-bundle.pem /tmp/rds-ca/aws-rds-ca-bundle.pem
RUN cd /tmp/rds-ca && cat aws-rds-ca-bundle.pem | awk 'split_after==1{n++;split_after=0} /-----END CERTIFICATE-----/ {split_after=1} {print > "cert" n ""}' \
    && for CERT in /tmp/rds-ca/cert*; do mv $CERT /usr/local/share/ca-certificates/aws-rds-ca-$(basename $CERT).crt; done \
    && rm -rf /tmp/rds-ca \
    && update-ca-certificates
# Adicionar os certificados ONS
COPY ./ONS_ROOT.pem /tmp/ons-certs/
COPY ./ONS_enterprise_CA1.pem /tmp/ons-certs/
COPY ./ONS_enterprise_CA2.pem /tmp/ons-certs/
RUN for CERT in /tmp/ons-certs/*; do mv $CERT /usr/local/share/ca-certificates/$(basename $CERT).crt; done \
    && rm -rf /tmp/ons-certs \
    && update-ca-certificates
# Instalar dependências para Kerberos
RUN apt-get update && apt-get install -y krb5-user curl
# Configuração do Kerberos
COPY ./krb5.conf /etc/krb5.conf
ENV KRB5_TRACE=/dev/stdout
# Configuração do AppDynamics
RUN mkdir -p /opt/appdynamics/
COPY ./AppDynamics-DotNetCore-linux-x64/ /opt/appdynamics/
COPY ./AppDynamics-DotNetCore-linux-x64/AppDynamicsConfig.json /opt/appdynamics/AppDynamicsConfig.json
# Variáveis de ambiente para o AppDynamics
ENV CORECLR_PROFILER="{57e1aa68-2229-41aa-9931-a6e93bbc64d8}"
ENV CORECLR_ENABLE_PROFILING=1
ENV CORECLR_PROFILER_PATH="/opt/appdynamics/libappdprofiler.so"
ENV ASPNETCORE_HTTP_PORTS=80
WORKDIR /app
EXPOSE 80
RUN addgroup --system appuser && adduser --system --ingroup appuser appuser

# Copiar o aplicativo
FROM base AS final
WORKDIR /app
COPY /app/publish .
RUN chown -R appuser:appuser /app
#USER appuser
ENTRYPOINT ["/bin/sh", "-c", "echo '************ sinapse-dados-api-prd.apps.ocpp.ons.org.br' >> /etc/hosts && echo '************ sinapse-dados-api-hmg.apps.ocph.ons.org.br' >> /etc/hosts && echo '************* api.homsinapse.ons.org.br' >> /etc/hosts && echo '************* api.homsinapseintegracao.ons.org.br' >> /etc/hosts && echo '************ api.sinapse.ons.org.br' >> /etc/hosts && echo '************* api.sinapseintegracao.ons.org.br' >> /etc/hosts && echo '************* hompops.ons.org.br' >> /etc/hosts && exec dotnet ONS.SINapse.Integracao.Api.dll"]
