services:
  # Serviço da API principal Sinapse Dados API 
  ons.sinapse.dados.api:
    image: ${DOCKER_REGISTRY-}ons.sinapse.dados.api  # Nome da imagem para a API
    container_name: ons.sinapse.dados.api.v2
    build:
      context: .  # Diretório onde o Dockerfile está localizado
      dockerfile: src/ONS.SINapse.Dados.Api/Dockerfile  # Caminho do Dockerfile
    depends_on:
      - ons.sinapse.dados.redis
      - ons.sinapse.dados.aspire-dashboard
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_HTTP_PORTS=8080
      - ASPNETCORE_HTTPS_PORTS=8081
      - OTEL_EXPORTER_OTLP_ENDPOINT=http://ons.sinapse.dados.aspire-dashboard:18889  # Endpoint para o OTEL
      - OTEL_EXPORTER_OTLP_PROTOCOL=grpc  # Protocolo OTEL utilizado
      - ApplicationSettings__ApiSinapseDadosHealthCheckUrl=http://ons.sinapse.dados.api:8080/health
      - ApplicationSettings__ApiSinapseHealthCheckUrl=
      - ApplicationSettings__ApiSinapseIntegracaoHealthCheckUrl=
      - ConnectionStrings__Redis=ons.sinapse.dados.redis:6379,defaultDatabase=0,password=teste
    ports:
      - "7073:8080"
      - "7074:8081"
    volumes:
      - ${APPDATA}/Microsoft/UserSecrets:/home/<USER>/.microsoft/usersecrets:ro
      - ${APPDATA}/ASP.NET/Https:/home/<USER>/.aspnet/https:ro

  # Serviço de log Seq
  ons.sinapse.dados.seq:
    image: datalust/seq:2024.3  # Imagem do Seq para logs estruturados
    container_name: ons.sinapse.dados.seq
    environment:
      ACCEPT_EULA: "Y"  # Aceitar o contrato de licença
    volumes:
      - ./.containers/seq_data:/data  # Volume para persistência dos dados do Seq
    ports:
      - "8070:80"  # Porta do Seq para acesso via HTTP
      - "5341:5341"  # Porta do Seq para ingestão de logs via OTLP

  # Serviço do Aspire Dashboard para visualização
  ons.sinapse.dados.aspire-dashboard:
    image: mcr.microsoft.com/dotnet/aspire-dashboard:9.0  # Imagem para o Aspire Dashboard
    environment:
      DOTNET_DASHBOARD_UNSECURED_ALLOW_ANONYMOUS: true  # Permitir acesso anônimo ao dashboard
    ports:
      - "18888:18888"  # Porta para acesso ao Aspire Dashboard via web

  # Serviço Redis
  ons.sinapse.dados.redis:
    image: redis:6  # Imagem do Redis
    container_name: ons.sinapse.dados.redis  # Nome do container
    ports:
      - "6379:6379"  # Porta padrão do Redis
    volumes:
      - ons_redis_data:/data/db  # Volume para persistência de dados do Redis

  # Serviço do Redis Commander (interface web para Redis)
  ons.sinapse.dados.commander:
    image: rediscommander/redis-commander:latest  # Imagem do Redis Commander
    container_name: ons.sinapse.dados.redis-commander  # Nome do container
    environment:
      - REDIS_HOSTS=local:redis:6379  # Conexão com o Redis
    ports:
      - "8081:8081"  # Porta do Redis Commander

# Volumes para persistência de dados
volumes:
  ons_redis_data:  # Volume para dados persistentes do Redis
