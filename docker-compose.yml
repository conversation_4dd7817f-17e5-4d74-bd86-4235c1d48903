version: "3.9"

services:
  redis:
    image: redis:7.2-alpine
    container_name: redis-8
    command: ["redis-server", "--appendonly", "yes", "--requirepass", ""]
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - app-net

  commander:
    image: rediscommander/redis-commander:latest
    container_name: redis_commander
    environment:
      - REDIS_HOSTS=local:redis:6379
    ports:
      - "8081:8081"
    depends_on:
      - redis
    restart: unless-stopped
    networks:
      - app-net

  rate-limiter:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: rate-limiter
    depends_on:
      - redis
    ports:
      - "8080:8080"
    env_file:
      - .env
    restart: unless-stopped
    networks:
      - app-net

volumes:
  redis_data:

networks:
  app-net:
