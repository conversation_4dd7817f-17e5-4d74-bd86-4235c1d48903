using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using ONS.AFConnector.CrossCutting.Kafka;
using ONS.AFConnector.CrossCutting.OpenTelemetry;
using ONS.AFConnector.CrossCutting.RabbitMQ;
using System.Diagnostics;
using System.Text.Json;

namespace ONS.AFConnector.Worker.Worker
{
    public class EventWorker(IRabbitMQService rabbitService, IKafkaService kafkaService, IConfiguration configuration) : BackgroundService
    {
        protected override Task ExecuteAsync(CancellationToken stoppingToken)
        {
            rabbitService.Consume(async message =>
            {
                //TODO: Consultar MongoDB, descobrir aplicações interessadas no dado e publicar em um tópico para cada aplicação
                using var activity = OTelConfig.ActivitySource?.StartActivity("Processando mensagem para envio ao Kafka.", ActivityKind.Consumer);
                try
                {
                    using JsonDocument document = JsonDocument.Parse(message);

                    foreach (JsonProperty propriedade in document.RootElement.EnumerateObject())
                    {
                        activity?.SetTag(propriedade.Name, propriedade.Value.ToString());
                    }

                    var templatePi = document.RootElement.GetProperty("templatePI").ToString();

                    var topico = configuration.GetValue<string>("KafkaSettings:TopicName")!;//TODO:Descobrir tópico da aplicação
                    return await kafkaService.PublishEvent(topico, message, templatePi);
           
                }
                catch(Exception ex)
                {
                    activity?.AddEvent(new ActivityEvent($"Erro ao processar mensagem."));
                    activity?.SetTag("message", ex.Message);
                    activity?.SetTag("exception", ex.StackTrace);
                    activity?.SetStatus(ActivityStatusCode.Error);
                    return false;
                }
            });
            return Task.CompletedTask;
        }

        public static bool IsJsonValid(string jsonString)
        {
            if (string.IsNullOrWhiteSpace(jsonString))
            {
                return false;
            }

            try
            {
                using JsonDocument doc = JsonDocument.Parse(jsonString);
                return true;
            }
            catch (JsonException)
            {
                return false;
            }
        }
    }
}
