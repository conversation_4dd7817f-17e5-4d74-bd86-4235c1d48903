using System.Collections.Generic;
using Newtonsoft.Json;

namespace ONS.DataSync.Configs
{
    public class DataSyncConfig
    {
        public DataSyncConfig()
        {
            QuantidadeDeRegistrosPorRequest = 0;
        }
        
        public SyncDestino SyncDestino { get; set; }
        public int QuantidadeDeRegistrosPorRequest { get; set; }
        public ComandosDataSyncConfig Comandos { get; set; }

        public IReadOnlyCollection<DatabaseConfiguration> Databases { get; set; }
        
    }

    public class DatabaseConfiguration
    {
        public DatabaseConfiguration()
        {
            Provider = string.Empty;
            ConnectionString = string.Empty;
            Tabelas = new List<TabelaDataSyncConfig>();
            Ativo = false;
        }
        
        public string Provider { get; set; }
        public string ConnectionString { get; set; }
        public bool Ativo { get; set; }
        public IReadOnlyCollection<TabelaDataSyncConfig> Tabelas { get; set; }
    }
    
    public class SyncDestino 
    {
        public SyncDestino()
        {
            Endereco = "";
            Headers = new Dictionary<string, string>();
        }

        public string Endereco { get; set; }
        
        public Dictionary<string, string> Headers { get; set; }
    }

    public class ComandosDataSyncConfig 
    {
        public ComandosDataSyncConfig()
        {
            CargaCompleta = string.Empty;
            Inclusao = string.Empty;
            Alteracao = string.Empty;
        }
        
        [JsonIgnore]
        private string _cargaCompleta;
        
        [JsonProperty(PropertyName = nameof(CargaCompleta))]
        public string CargaCompleta
        {
            get => _cargaCompleta;
            set => _cargaCompleta = FormatarComando(value);
        }
        
        [JsonIgnore]
        private string _inclusao;
        
        [JsonProperty(PropertyName = nameof(Inclusao))]
        public string Inclusao
        {
            get => _inclusao; 
            set => _inclusao = FormatarComando(value);
        }
        
        [JsonIgnore]
        private string _alteracao;
        
        [JsonProperty(PropertyName = nameof(Alteracao))]
        public string Alteracao
        {
            get => _alteracao; 
            set => _alteracao = FormatarComando(value);
        }

        private static string FormatarComando(string valor)
        {
            if(string.IsNullOrEmpty(valor))
                return string.Empty;
                
            return valor.StartsWith("--") ? valor[2..] : valor;
        }
    }

    public class TabelaDataSyncConfig 
    {
        public TabelaDataSyncConfig()
        {
            NomeTabela = string.Empty;
            Query = string.Empty;
            PropriedadesChave =  new List<string>();
        }
        
        public string NomeTabela { get; set; }
        public string Query { get; set; }
        public IEnumerable<string> PropriedadesChave { get; set; }

        public bool EstaValido() => !(string.IsNullOrEmpty(NomeTabela) && string.IsNullOrEmpty(Query));
    }
}
