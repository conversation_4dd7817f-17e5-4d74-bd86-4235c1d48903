using ONS.DataSync.Configs;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;

namespace ONS.DataSync.Dtos
{
    public sealed class SyncRecord
    {
        public string Acao { get; set; } = null!;
        public string TipoObjeto { get; set; } = null!;
        public bool MudancaEstrutura { get; set; } = false;
        public IEnumerable<SyncDatabaseProperty> PropriedadesChave { get; set; } = null!;
        public IEnumerable<SyncDatabaseProperty> Propriedades { get; set; } = null!;
    }

    public sealed class SyncDatabaseProperty 
    {
        public string Valor { get; set; } = null!;
        public string Nome { get; set; } = null!;
    }

    public static class SyncRecordConverter
    {
        private static readonly string[] DateFormats= {
            "M/d/yyyy h:mm:ss tt", "M/d/yyyy h:mm tt", "MM/dd/yyyy hh:mm:ss", "M/d/yyyy h:mm:ss", "M/d/yyyy hh:mm tt", 
            "M/d/yyyy hh tt", "M/d/yyyy h:mm", "M/d/yyyy h:mm", "MM/dd/yyyy hh:mm", "M/dd/yyyy hh:mm", "yyyy-MM-dd",
            "dd/MM/yyyy hh:mm:ss"
        };
        
        public static SyncRecord Converter(TabelaDataSyncConfig config, string acao, Dictionary<string, string?> dados)
        {
            var result = new SyncRecord
            {
                TipoObjeto = config.NomeTabela,
                Acao = acao,
                MudancaEstrutura = false,
                PropriedadesChave = config.PropriedadesChave.Select(ch =>
                {
                    if (!dados.TryGetValue(ch, out var valor))
                        return new SyncDatabaseProperty
                        {
                            Nome = ch,
                            Valor = string.Empty
                        };
                    
                    var chave = new SyncDatabaseProperty
                    {
                        Nome = ch,
                        Valor = ConverterValor(valor)
                    };
                    
                    dados.Remove(ch);
                    
                    return chave;
                }),
                Propriedades = dados.Select(r => new SyncDatabaseProperty
                {
                    Nome = r.Key,
                    Valor = ConverterValor(r.Value)
                })
            };
            return result;
        }
        private static string ConverterValor(string? valor) 
        {
            if (DateTime.TryParseExact(valor, DateFormats, new CultureInfo("en-US"), DateTimeStyles.None, out var data))
            {
                return data.ToString("yyyy-MM-ddTHH:mm:ss");
            }

            return valor ?? string.Empty;
        }
    }
}
