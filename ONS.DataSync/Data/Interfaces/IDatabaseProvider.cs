using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace ONS.DataSync.Data.Interfaces
{
    public interface IDatabaseProvider : IDisposable
    {
        void OpenConnection();
        void CloseConnection();
        Task<IReadOnlyCollection<Dictionary<string, string?>>> ExecuteQueryAsync(string query, CancellationToken cancellationToken);
    }
}