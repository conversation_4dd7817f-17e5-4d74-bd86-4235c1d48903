using System;
using System.Collections.Generic;
using System.Data.Common;
using System.Threading;
using System.Threading.Tasks;
using IBM.Data.DB2.Core;
using ONS.DataSync.Data.Interfaces;

namespace ONS.DataSync.Data.Implementations
{
    public class InformixProvider : IInformixProvider
    {
        private readonly DB2Connection _db2Connection;

        public InformixProvider(string connectionString)
        {
            _db2Connection = new DB2Connection(connectionString);
        }
        
        public async Task<IReadOnlyCollection<Dictionary<string, string?>>> ExecuteQueryAsync(string query, CancellationToken cancellationToken)
        {
            var result = new List<Dictionary<string, string?>>();

            var command = CreateCommand(query);
            
            var reader = await command.ExecuteReaderAsync(cancellationToken);

            if (!reader.CanGetColumnSchema())
            {
                throw new MethodAccessException(
                    "[ExecuteQueryAsync][CanGetColumnSchema] - Não foi possível obter as informações de colunas da query executada.");
            };

            var columns = reader.GetColumnSchema();

            while (await reader.ReadAsync(cancellationToken))
            {
                var row = new Dictionary<string, string?>();
                foreach (var column in columns)
                {
                    if(column.ColumnOrdinal is null) continue;
                    var ordinal = (int)column.ColumnOrdinal;
                    var campo = reader.GetName(ordinal);
    
                    var value = !reader.IsDBNull(ordinal)
                        ? reader.GetValue(ordinal).ToString()
                        : null;
                    row.Add(campo, value);
                }
                result.Add(row);
            }

            await reader.CloseAsync();
            await command.DisposeAsync();

            return result.AsReadOnly();
        }
        
        public void OpenConnection()
        {
            _db2Connection.Open();
        }

        public void CloseConnection()
        {
            _db2Connection.Close();
        }
        
        private DB2Command CreateCommand(string commandText) 
        {
            var command = _db2Connection.CreateCommand();
            command.CommandText = commandText;

            return command;
        }
        
        private bool _disposed;

        public void Dispose()
        {
            if (_disposed) return;
            _disposed = true;
            
            _db2Connection.Close();
            _db2Connection.Dispose();
            
            GC.SuppressFinalize(this);
        }
    }
}