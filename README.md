# SinapseTemplates

## Sobre o Repositório
Este repositório é dedicado ao gerenciamento dos arquivos de template utilizados no projeto SINapse.

## Como Contribuir
Para contribuir com o projeto, siga estes passos:

1. **Clone o Repositório**: Faça um clone do repositório para sua máquina local.
2. **Faça suas Alterações**: Edite ou adicione novos templates conforme necessário.
3. **Commit e Push**: Faça commit de suas alterações e realize um push para o repositório remoto.

## Processo de Integração Contínua
Cada commit neste repositório dispara uma build automatizada no DevOps. Esta build é responsável por:

1. Compilar e verificar os templates.
2. Publicar os artefatos de template gerados.

## Utilização dos Templates no SINapseBackend
O projeto SINapseBackend é configurado para:

1. Baixar automaticamente o último artefato de template disponível durante o processo de build.
2. Incorporar os templates atualizados na build da imagem Docker do projeto.
