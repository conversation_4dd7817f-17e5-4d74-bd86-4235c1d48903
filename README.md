# SINapse Dados

Projeto responsável por prover fontes de dados para a aplicação SINapse.

## In<PERSON><PERSON> começar, é necessário ter o .NET 8 e Docker instalados. Um arquivo `docker-compose` está disponível para fornecer os serviços Redis e MongoDB para desenvolvimento.

1. **Processo de Instalação**
    - Deploy da API realizado via OpenShift.
    - Deploy de CRON JOBs realizado via OpenShift.

2. **Dependências de Software**
    - .NET 8
    - MongoDB
    - Redis

3. **Últimos Lançamentos**
    - Suporte à carga de dados via DataSync.
    - Suporte à carga de dados via DBSync.
    - Suporte ao cache com Redis.
    - Autenticação via conta de serviço.

4. **Referências da API**
   A documentação Swagger pode ser acessada no endereço da API: `/swagger/index.html`. Exemplos de uso podem ser encontrados na documentação.

## Cache

O sistema utiliza Redis para cache, que pode ser configurado via `appsettings`. Em ambientes como TST, HML ou PROD, a configuração pode ser fornecida via ConfigIT.

```json
{
  "ConnectionStrings": {
    "Redis": "cluster:6379,password=senha,defaultDatabase=0"
  }
}
```

### Consultando Chaves Utilizadas pelo SINapse Dados

Por questões de desempenho, a funcionalidade de consulta de cache retornará apenas as chaves configuradas pela aplicação. A descoberta de chaves diretamente no servidor Redis não é realizada, pois é uma operação bloqueante que pode degradar a performance.

**Endpoint**: `GET https://sinapse-dados-api-tst.apps.ocpd.ons.org.br/api/sync/cache-keys`

### Excluindo Dados do Cache

Os dados do cache são atualizados automaticamente sempre que ocorre uma atualização da coleção de dados do MongoDB. Também é possível remover os dados manualmente.

**Endpoint**: `DELETE https://sinapse-dados-api-tst.apps.ocpd.ons.org.br/api/sync/cache-keys/{key}`

**Usuário precisa ter operação ExecutarSyncDone**
## Build e Teste

O build para TST, HML e PROD é realizado via Azure DevOps, utilizando o pipeline `ONS SINapse Build Dados API`.

Verifique a saúde da aplicação no ambiente TST: `https://sinapse-dados-api-tst.apps.ocpd.ons.org.br/monitor`.