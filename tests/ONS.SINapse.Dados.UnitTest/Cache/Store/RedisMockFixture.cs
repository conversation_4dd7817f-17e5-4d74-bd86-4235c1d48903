using StackExchange.Redis;
using Microsoft.Extensions.Logging;
using ONS.SINapse.Dados.Shared.Cache.Store; // Assuming your store class is here

namespace ONS.SINapse.Dados.UnitTest.Cache.Store;

/// <summary>
/// Fixture para configurar mocks do StackExchange.Redis e estado em memória
/// para testes de RedisOutputCacheStore.
/// </summary>
public class RedisMockFixture
{
    public Mock<IConnectionMultiplexer> ConnectionMock { get; }
    public Mock<IDatabase> DatabaseMock { get; }
    public Mock<ITransaction> TransactionMock { get; }
    public Dictionary<string, RedisValue> Store { get; } // Simula o armazenamento chave-valor principal
    public Dictionary<string, HashSet<RedisValue>> TagSets { get; } // Simula os conjuntos de tags (tagKey -> set of dataKeys)
    public RedisOutputCacheStore CacheStore { get; } // Instância da classe sendo testada

    public RedisMockFixture()
    {
        // Inicializa os dicionários que simulam o estado do Redis
        Store = new Dictionary<string, RedisValue>();
        TagSets = new Dictionary<string, HashSet<RedisValue>>();

        // Inicializa os mocks
        DatabaseMock = new Mock<IDatabase>();
        TransactionMock = new Mock<ITransaction>();
        ConnectionMock = new Mock<IConnectionMultiplexer>();

        // Configura o ConnectionMultiplexer para retornar o Database mockado
        ConnectionMock
            .Setup(c => c.GetDatabase(It.IsAny<int>(), It.IsAny<object>()))
            .Returns(DatabaseMock.Object);

        // Inicializa a classe sendo testada usando os mocks
        var loggerMock = new Mock<ILogger<RedisOutputCacheStore>>();
        CacheStore = new RedisOutputCacheStore(ConnectionMock.Object, loggerMock.Object);

        // Configura o comportamento dos mocks
        SetupMocks();
    }

    /// <summary>
    /// Método para configurar os mocks para simular o comportamento do Redis.
    /// </summary>
    private void SetupMocks()
    {
        // --- Mocking IDatabase Operations ---

        // StringGetAsync: Lê do mock store
        DatabaseMock
            .Setup(db => db.StringGetAsync(It.IsAny<RedisKey>(), It.IsAny<CommandFlags>()))
            .Returns<RedisKey, CommandFlags>((key, _) =>
                Task.FromResult(Store.TryGetValue(key!, out RedisValue val) ? val : RedisValue.Null));

        // StringSetAsync: Escreve diretamente no mock store (para usos não transacionais)
        DatabaseMock
            .Setup(db => db.StringSetAsync(It.IsAny<RedisKey>(), It.IsAny<RedisValue>(),
                It.IsAny<TimeSpan?>(), It.IsAny<When>(), It.IsAny<CommandFlags>()))
            .Returns<RedisKey, RedisValue, TimeSpan?, When, CommandFlags>((key, value, _, _, _) =>
            {
                Store[key!] = value;
                return Task.FromResult(true); // Simula sucesso
            });

        // CreateTransaction: Retorna o objeto de transação mockado
        DatabaseMock
            .Setup(db => db.CreateTransaction(It.IsAny<object>()))
            .Returns(() =>
            {
                // Retorna o mock da transação
                return TransactionMock.Object;
            });

        // SetMembersAsync: Lê membros (chaves de dados) do mock de sets de tags
        DatabaseMock
            .Setup(db => db.SetMembersAsync(It.IsAny<RedisKey>(), It.IsAny<CommandFlags>()))
            .Returns<RedisKey, CommandFlags>((key, _) =>
            {
                return Task.FromResult(
                    TagSets.TryGetValue(key!, out HashSet<RedisValue>? set)
                        ? set.ToArray()
                        : Array.Empty<RedisValue>());
            });

        // KeyDeleteAsync no Database: Lida com a exclusão de uma única chave diretamente (usado para sets de tags órfãos)
        DatabaseMock
            .Setup(db => db.KeyDeleteAsync(It.IsAny<RedisKey>(), It.IsAny<CommandFlags>()))
            .Returns<RedisKey, CommandFlags>((key, flags) =>
            {
                // Simula a exclusão da chave do store ou tagSets
                // Assumindo que é usado apenas para sets de tags no EvictByTag quando não há membros
                bool removedFromTagSets = TagSets.Remove(key!);
                bool removedFromStore =
                    Store.Remove(key!); // Pode ser uma chave de dados se EvictByTag for chamado nela diretamente

                return Task.FromResult(removedFromTagSets || removedFromStore); // True se algo foi removido
            });


        // --- Mocking ITransaction Operations (Apenas simulam o enfileiramento) ---

        // StringSetAsync na Transaction: Simula o enfileiramento de um String SET
        TransactionMock
            .Setup(tx => tx.StringSetAsync(It.IsAny<RedisKey>(), It.IsAny<RedisValue>(),
                It.IsAny<TimeSpan?>(), It.IsAny<When>(), It.IsAny<CommandFlags>()))
            .Returns<RedisKey, RedisValue, TimeSpan?, When, CommandFlags>((key, value, expiry, when, flags) =>
            {
                // Comando é enfileirado, sem ação imediata no Store/TagSets
                return Task.FromResult(true); // Simula enfileiramento bem-sucedido
            });

        // SetAddAsync na Transaction: Simula o enfileiramento de um SET ADD (relação tag -> chave)
        TransactionMock
            .Setup(tx => tx.SetAddAsync(It.IsAny<RedisKey>(), It.IsAny<RedisValue>(), It.IsAny<CommandFlags>()))
            .Returns<RedisKey, RedisValue, CommandFlags>((key, value, _) =>
            {
                // Comando é enfileirado, sem ação imediata no Store/TagSets
                return Task.FromResult(true); // Simula enfileiramento bem-sucedido
            });

        // KeyExpireAsync na Transaction: Simula o enfileiramento de um KEY EXPIRE
        TransactionMock
            .Setup(tx => tx.KeyExpireAsync(It.IsAny<RedisKey>(), It.IsAny<TimeSpan?>(),
                It.IsAny<ExpireWhen>(), It.IsAny<CommandFlags>()))
            .Returns<RedisKey, TimeSpan?, ExpireWhen, CommandFlags>((key, expiry, when, flags) =>
            {
                // Comando é enfileirado
                return Task.FromResult(true); // Simula enfileiramento bem-sucedido
            });

        // KeyDeleteAsync (múltiplas) na Transaction: Simula o enfileiramento de múltiplos KEY DELETE
        TransactionMock
            .Setup(tx => tx.KeyDeleteAsync(It.IsAny<RedisKey[]>(), It.IsAny<CommandFlags>()))
            .Returns<RedisKey[], CommandFlags>((keys, flags) =>
            {
                // Comando é enfileirado
                return Task.FromResult((long)keys.Length); // Simula enfileiramento bem-sucedido (retorna contagem)
            });

        // KeyDeleteAsync (única) na Transaction: Simula o enfileiramento de um único KEY DELETE
        TransactionMock
            .Setup(tx => tx.KeyDeleteAsync(It.IsAny<RedisKey>(), It.IsAny<CommandFlags>()))
            .Returns<RedisKey, CommandFlags>((key, flags) =>
            {
                // Comando é enfileirado
                return Task.FromResult(true); // Simula enfileiramento bem-sucedido
            });


        // --- ExecuteAsync: Onde a "transação" é aplicada ao mock store ---
        TransactionMock
            .Setup(tx => tx.ExecuteAsync(It.IsAny<CommandFlags>()))
            .Returns(() =>
            {
                // --- Simula a execução dos comandos de transação enfileirados ---
                bool success = true; // Assume sucesso para este mock básico

                // Processa as invocações registradas neste mock de transação
                foreach (IInvocation invocation in TransactionMock.Invocations)
                {
                    try
                    {
                        if (invocation.Method.Name == nameof(ITransaction.StringSetAsync))
                        {
                            var key = (RedisKey)invocation.Arguments[0];
                            var value = (RedisValue)invocation.Arguments[1];
                            // Aplica a operação string set
                            Store[key!] = value;
                        }
                        else if (invocation.Method.Name == nameof(ITransaction.SetAddAsync))
                        {
                            var setKey = (RedisKey)invocation.Arguments[0];
                            var member = (RedisValue)invocation.Arguments[1];
                            // Aplica a operação set add
                            if (!TagSets.ContainsKey(setKey!))
                            {
                                TagSets[setKey!] = new HashSet<RedisValue>();
                            }

                            TagSets[setKey!].Add(member);
                        }
                        else if (invocation.Method.Name == nameof(ITransaction.KeyDeleteAsync))
                        {
                            if (invocation.Arguments[0] is RedisKey[] keysToDelete)
                            {
                                // Aplica a exclusão em massa de chaves
                                foreach (RedisKey k in keysToDelete)
                                {
                                    Store.Remove(k!);
                                }
                            }
                            else if (invocation.Arguments[0] is RedisKey keyToDelete)
                            {
                                // Aplica a exclusão de chave única (para sets de tags)
                                TagSets.Remove(keyToDelete!);
                            }
                        }
                        // KeyExpireAsync não muda o conteúdo para esta simulação de mock
                    }
                    catch (Exception)
                    {
                        // Para este mock básico, apenas marca falha
                        success = false;
                        // Em um mock mais complexo, você poderia parar o processamento aqui.
                    }
                }

                // --- Importante: Limpa as invocações registradas após a execução ---
                // Isso impede que comandos de transações anteriores sejam reexecutados.
                TransactionMock.Invocations.Clear();

                return Task.FromResult(success); // Simula o resultado da transação
            });
    }
}
