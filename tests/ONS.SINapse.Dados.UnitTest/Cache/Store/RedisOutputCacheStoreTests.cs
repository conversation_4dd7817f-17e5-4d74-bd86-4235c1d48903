using StackExchange.Redis;

namespace ONS.SINapse.Dados.UnitTest.Cache.Store;

/// <summary>
/// A classe de teste implementa IClassFixture para injetar uma instância de RedisMockFixture
/// 
/// </summary>
public class RedisOutputCacheStoreTests : IClassFixture<RedisMockFixture>
{
    // Campo para armazenar a instância da fixture injetada
    private readonly RedisMockFixture _fixture;

    // O construtor recebe a instância da fixture
    public RedisOutputCacheStoreTests(RedisMockFixture fixture)
    {
        _fixture = fixture;
        _fixture.Store.Clear();
        _fixture.TagSets.Clear();
        _fixture.TransactionMock.Invocations.Clear();
    }

    [Fact(DisplayName = "SetAsync deve armazenar apenas tags válidas")]
    public async Task SetAsync_ShouldStoreOnlyValidTags()
    {
        // Arrange
        const string emptyTag = "";
        const string nullTag = null;
        const string whitespaceTag = "   ";
        const string validTag = "validtag";
        string[] tags = [emptyTag, nullTag!, whitespaceTag, validTag];
        
        // Act
        await _fixture.CacheStore.SetAsync("key", "value"u8.ToArray(), tags, TimeSpan.FromMinutes(10), CancellationToken.None);

        // Assert: Verifica se apenas a tag válida foi armazenada
        Assert.Single(_fixture.TagSets);
        Assert.True(_fixture.TagSets.ContainsKey(_fixture.CacheStore.GetTagKey(validTag)));
    }

    [Fact(DisplayName = "SetAsync e GetAsync devem armazenar e recuperar o mesmo valor")]
    public async Task SetAndGet_ShouldReturnSameValue()
    {
        const string key = "my-key";
        string dataRedisKey = _fixture.CacheStore.GetDataKey(key);
        const string tag = "tag1";
        string tagRedisKey = _fixture.CacheStore.GetTagKey(tag);
        byte[] value = "cached content"u8.ToArray();
        string[] tags = [tag];
        var validFor = TimeSpan.FromMinutes(10);

        await _fixture.CacheStore.SetAsync(key, value, tags, validFor, CancellationToken.None);
        
        // Store deve conter a chave de dados e seu valor
        Assert.Single(_fixture.Store);
        Assert.True(_fixture.Store.ContainsKey(dataRedisKey));
        Assert.Equal(value, (byte[])_fixture.Store[dataRedisKey]);

        // TagSets deve conter a chave da tag
        Assert.Single(_fixture.TagSets);
        Assert.True(_fixture.TagSets.ContainsKey(tagRedisKey));

        // O set associado à chave da tag em TagSets deve conter a chave de dados como membro
        Assert.Single(_fixture.TagSets[tagRedisKey]);
        Assert.Contains((RedisValue)dataRedisKey, _fixture.TagSets[tagRedisKey]);


        // GetAsync lê do dicionário _store via StringGetAsync mock

        byte[]? result = await _fixture.CacheStore.GetAsync(key, CancellationToken.None);

        Assert.NotNull(result);
        Assert.Equal(value, result);
    }

    [Fact(DisplayName = "EvictByTag deve remover chaves associadas à tag")]
    public async Task EvictByTag_ShouldRemoveAssociatedKeys()
    {
        // Arrange: Configura algumas chaves associadas a tags
        const string key1 = "key1";
        string dataRedisKey1 = _fixture.CacheStore.GetDataKey(key1);
        const string tag1 = "tag1";
        string tagRedisKey1 = _fixture.CacheStore.GetTagKey(tag1);
        byte[] value1 = "value1"u8.ToArray();

        const string key2 = "key2";
        string dataRedisKey2 = _fixture.CacheStore.GetDataKey(key2);
        const string tag2 = "tag2";
        string tagRedisKey2 = _fixture.CacheStore.GetTagKey(tag2);
        byte[] value2 = "value2"u8.ToArray();

        const string key3 = "key3";
        string dataRedisKey3 = _fixture.CacheStore.GetDataKey(key3);
        // key3 só tem tag2
        byte[] value3 = "value3"u8.ToArray();

        var validFor = TimeSpan.FromMinutes(10);

        // Usa o SetAsync do cache store da fixture para popular o estado mockado
        await _fixture.CacheStore.SetAsync(key1, value1, [tag1], validFor, CancellationToken.None);
        await _fixture.CacheStore.SetAsync(key2, value2, [tag1, tag2], validFor, CancellationToken.None);
        await _fixture.CacheStore.SetAsync(key3, value3, [tag2], validFor, CancellationToken.None);

        // Assert do estado inicial após configurar (acessando store e tagSets via fixture)
        Assert.Equal(3, _fixture.Store.Count);
        Assert.True(_fixture.Store.ContainsKey(dataRedisKey1));
        Assert.True(_fixture.Store.ContainsKey(dataRedisKey2));
        Assert.True(_fixture.Store.ContainsKey(dataRedisKey3));

        Assert.Equal(2, _fixture.TagSets.Count);
        Assert.True(_fixture.TagSets.ContainsKey(tagRedisKey1));
        Assert.True(_fixture.TagSets.ContainsKey(tagRedisKey2));

        Assert.Contains((RedisValue)dataRedisKey1, _fixture.TagSets[tagRedisKey1]);
        Assert.Contains((RedisValue)dataRedisKey2, _fixture.TagSets[tagRedisKey1]);
        Assert.Equal(2, _fixture.TagSets[tagRedisKey1].Count);

        Assert.Contains((RedisValue)dataRedisKey2, _fixture.TagSets[tagRedisKey2]);
        Assert.Contains((RedisValue)dataRedisKey3, _fixture.TagSets[tagRedisKey2]);
        Assert.Equal(2, _fixture.TagSets[tagRedisKey2].Count);


        // Act: Evictar chaves associadas à tag1
        await _fixture.CacheStore.EvictByTagAsync(tag1, CancellationToken.None);

        // Assert do estado após a evicção (acessando store e tagSets via fixture)
        Assert.Single(_fixture.Store);
        Assert.False(_fixture.Store.ContainsKey(dataRedisKey1));
        Assert.False(_fixture.Store.ContainsKey(dataRedisKey2));
        Assert.True(_fixture.Store.ContainsKey(dataRedisKey3));

        Assert.Single(_fixture.TagSets);
        Assert.False(_fixture.TagSets.ContainsKey(tagRedisKey1));
        Assert.True(_fixture.TagSets.ContainsKey(tagRedisKey2));

        // Verifica o set restante da tag2
        Assert.Equal(2, _fixture.TagSets[tagRedisKey2].Count);
        Assert.Contains((RedisValue)dataRedisKey2, _fixture.TagSets[tagRedisKey2]);
        Assert.Contains((RedisValue)dataRedisKey3, _fixture.TagSets[tagRedisKey2]);

        // Verifica GetAsync para chaves evictadas e restantes
        byte[]? result1 = await _fixture.CacheStore.GetAsync(key1, CancellationToken.None);
        Assert.Null(result1);

        byte[]? result2 = await _fixture.CacheStore.GetAsync(key2, CancellationToken.None);
        Assert.Null(result2);

        byte[]? result3 = await _fixture.CacheStore.GetAsync(key3, CancellationToken.None);
        Assert.NotNull(result3);
        Assert.Equal(value3, result3);
    }

    [Fact(DisplayName = "EvictByTag deve lidar com tag não existente")]
    public async Task EvictByTag_ShouldHandleNonExistingTag()
    {
        // Arrange: Popula alguns dados que NÃO estão relacionados à tag que vamos evictar
        string key1 = "key1";
        string tag1 = "tag1";
        byte[] value1 = "value1"u8.ToArray();
        var validFor = TimeSpan.FromMinutes(10);
         await _fixture.CacheStore.SetAsync(key1, value1, [tag1], validFor, CancellationToken.None);

        // Garante que o estado inicial é o esperado (acessando via fixture)
        Assert.Single(_fixture.Store);
        Assert.Single(_fixture.TagSets);

        // Act: Tenta evictar uma tag que não existe
        const string nonExistingTag = "nonexistenttag";
        await _fixture.CacheStore.EvictByTagAsync(nonExistingTag, CancellationToken.None);

        // Assert: Os dados originais devem ainda estar lá (acessando via fixture)
        Assert.Single(_fixture.Store);
        Assert.Single(_fixture.TagSets);

        byte[]? result = await _fixture.CacheStore.GetAsync(key1, CancellationToken.None);
        Assert.NotNull(result);
        Assert.Equal(value1, result);
    }
}
