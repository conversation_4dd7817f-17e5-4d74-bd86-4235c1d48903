using System.Reflection;
using Microsoft.Data.SqlClient;

namespace ONS.SINapse.Dados.UnitTest.Fixtures;

public static class SqlExceptionFixture
{
    public static SqlException CreateSqlException(int number, string message)
    {
        ConstructorInfo? sqlErrorCtor = typeof(SqlError).GetConstructor(
            BindingFlags.Instance | BindingFlags.NonPublic,
            null,
            [typeof(int), typeof(byte), typeof(byte), typeof(string), typeof(string), typeof(string), typeof(int), typeof(Exception)
            ],
            null
        );
        
        object sqlError = sqlErrorCtor?.Invoke([number, (byte)0, (byte)0, "server", message, "proc", 0, null]);

        var errorCollection = (SqlErrorCollection)Activator.CreateInstance(typeof(SqlErrorCollection), true);
        typeof(SqlErrorCollection).GetMethod("Add", BindingFlags.Instance | BindingFlags.NonPublic)
            ?.Invoke(errorCollection, [sqlError]);

        MethodInfo sqlExceptionCtor = typeof(SqlException).GetMethod("CreateException", BindingFlags.Static | BindingFlags.NonPublic, null,
            [typeof(SqlErrorCollection), typeof(string)], null);

        return (SqlException)sqlExceptionCtor?.Invoke(null, [errorCollection, "7.0"]);
    }
}
