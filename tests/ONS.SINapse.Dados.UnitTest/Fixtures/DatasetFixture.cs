using ONS.SINapse.Dados.Shared.DTOs;

namespace ONS.SINapse.Dados.UnitTest.Fixtures;

public static class DatasetFixture
{
    public static List<DatasetItemDto> GetDados()
    {
        return
        [
            new DatasetItemDto(
                id: "AMBA",
                message: new MessageDto(
                    Id: "AMBA",
                    Description: "<PERSON><PERSON><PERSON>",
                    Label: "Balbina - ELETRONORTE",
                    Optional: null,
                    DefaultValue: false),
                origin: new ManeuverObjectDto("N", "COSR-NCO"),
                destination: new ManeuverObjectDto("ENT", "ELETRONORTE"),
                local: new ManeuverObjectDto("AMBA", "<PERSON><PERSON>bina"),
                encaminharPara: null,
                definirStatus: "ENT"
            ),

            new DatasetItemDto(
                id: "APCN",
                message: new MessageDto(
                    Id: "APCN",
                    Description: "Coaracy Nunes",
                    Label: "Coaracy Nunes - ELETRONORTE",
                    Optional: null,
                    DefaultValue: false),
                origin: new ManeuverObjectDto("N", "COSR-NCO"),
                destination: new ManeuverObjectDto("ENT", "ELETRONORTE"),
                local: new ManeuverObjectDto("APCN", "Coaracy Nunes"),
                encaminharPara: null,
                definirStatus: "ENT"
            ),

            new DatasetItemDto(
                id: "APFGO",
                message: new MessageDto(
                    Id: "APFGO",
                    Description: "Ferreira Gomes",
                    Label: "Ferreira Gomes - AF ENERGIA",
                    Optional: null,
                    DefaultValue: false),
                origin: new ManeuverObjectDto("N", "COSR-NCO"),
                destination: new ManeuverObjectDto("AFN", "AF ENERGIA"),
                local: new ManeuverObjectDto("APFGO", "Ferreira Gomes"),
                encaminharPara: null,
                definirStatus: "AFN"
            )
        ];
    }
}
