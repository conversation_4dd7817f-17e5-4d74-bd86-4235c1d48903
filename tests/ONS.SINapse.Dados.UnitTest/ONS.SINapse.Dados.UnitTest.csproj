<Project Sdk="Microsoft.NET.Sdk">
    <ItemGroup>
        <PackageReference Include="coverlet.collector"/>
        <PackageReference Include="JetBrains.Annotations" />
        <PackageReference Include="Microsoft.NET.Test.Sdk"/>
        <PackageReference Include="Moq" />
        <PackageReference Include="Shouldly" />
        <PackageReference Include="xunit"/>
        <PackageReference Include="xunit.runner.visualstudio"/>
    </ItemGroup>

    <ItemGroup>
        <Using Include="Xunit"/>
        <Using Include="Moq" />
        <Using Include="Shouldly" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\..\src\ONS.SINapse.Dados.Business\ONS.SINapse.Dados.Business.csproj" />
      <ProjectReference Include="..\..\src\ONS.SINapse.Dados.Repository\ONS.SINapse.Dados.Repository.csproj" />
      <ProjectReference Include="..\..\src\ONS.SINapse.Dados.Shared\ONS.SINapse.Dados.Shared.csproj" />
    </ItemGroup>

</Project>
