using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using OpenTelemetry.Exporter;
using OpenTelemetry.Logs;
using OpenTelemetry.Metrics;
using OpenTelemetry.Resources;
using OpenTelemetry.Trace;
using System.Diagnostics;

namespace ONS.AFConnector.CrossCutting.OpenTelemetry
{
    public static class OTelConfig
    {
        private static ActivitySource? activitySource;

        public static ActivitySource? ActivitySource { get => activitySource; set => activitySource = value; }

        public static void Configure(WebApplicationBuilder builder)
        {
            var resourceBuild = ResourceBuilder.CreateDefault()
                .AddService(serviceName: $"{builder.Configuration["DistributedTracing:ServiceName"]}", serviceVersion: $"{builder.Configuration["DistributedTracing:ServiceVersion"]}").AddTelemetrySdk();

            var protocol = OtlpExportProtocol.Grpc; 

            if(Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") == Environments.Development)
            {
                protocol = OtlpExportProtocol.HttpProtobuf;
            }
            
            builder.Services.AddOpenTelemetry()
                .WithMetrics(metricBuilder =>
                {
                    metricBuilder.AddMeter($"{builder.Configuration["DistributedTracing:ServiceName"]}")
                        .SetResourceBuilder(resourceBuild)
                        .AddHttpClientInstrumentation()
                        .AddAspNetCoreInstrumentation()
                        .AddProcessInstrumentation()
                        .AddRuntimeInstrumentation()
                        .AddOtlpExporter(exporter => { exporter.Protocol = protocol; });
                })
                .WithTracing(traceBuilder =>
                {
                    traceBuilder.AddSource($"{builder.Configuration["DistributedTracing:ServiceName"]}")
                        .SetResourceBuilder(resourceBuild)
                        .AddHttpClientInstrumentation(options => options.RecordException = true)
                        .AddAspNetCoreInstrumentation(options => 
                        {
                            options.RecordException = true;
                            options.Filter = (httpContext) =>
                            {
                                return httpContext.Request.Path.Value?.Contains("Health") is false;
                            };
                        })
                        .SetErrorStatusOnException()
                        .AddOtlpExporter(exporter =>
                        {
                            exporter.Protocol = protocol;
                        });
                });

            builder.Logging.ClearProviders()
                .AddOpenTelemetry(loggerOptions =>
                {
                    loggerOptions
                        .SetResourceBuilder(resourceBuild)
                        .AddOtlpExporter(exporter =>
                        {
                            exporter.Protocol = protocol;
                        });

                    loggerOptions.IncludeFormattedMessage = true;
                    loggerOptions.IncludeScopes = true;
                    loggerOptions.ParseStateValues = true;
                });
        }

        public static void Init(IConfiguration configuration)
        {
            var serviceName = $"{configuration["DistributedTracing:ServiceName"]}";
            var serviceVersion = $"{configuration["DistributedTracing:ServiceVersion"]}";

            activitySource = new ActivitySource(serviceName, serviceVersion);
        }
    }
}
