{"step4": {"fields": [{"label": "Usina-Termo", "description": "", "fieldName": "usina-termo-online", "component": "table", "type": "table", "required": true, "readonly": false, "columns": [{"component": "label", "label": "Usina-Termo", "description": "Usinas", "readonly": true, "required": false, "fieldName": "nome-da-usina", "messageOutput": "#NOME-DA-USINA |", "style": {"width": "120"}}, {"label": "Sincronizar", "description": "<PERSON><PERSON> ou <PERSON><PERSON> que <PERSON>", "fieldName": "sincronizar", "component": "input", "type": "number", "required": false, "readonly": false, "messageOutput": "Sincronizar #SINCRONIZAR UG(s).", "defaultValue": "", "min": 1, "max": 99999, "step": 1, "style": {"width": "80"}}, {"label": "Maximizar", "description": "Maximizar Geração das UG(s) Sincronizadas como Gerador", "fieldName": "maximizar-ug", "component": "input", "type": "string", "required": false, "readonly": true, "messageOutput": "Maximizar a Geração das UGs Sincronizadas como Gerador.", "defaultValue": "UG(s)", "style": {"width": "80"}}]}]}}