{"step4": {"fields": [{"label": "<PERSON><PERSON>", "description": "", "fieldName": "usina-online", "component": "table", "type": "table", "required": false, "readonly": false, "columns": [{"component": "label", "description": "Usinas", "readonly": true, "required": false, "fieldName": "nome-da-usina", "messageOutput": "#NOME-DA-USINA | Manter Geração Verificada"}, {"label": "Geração (MW)", "description": "<PERSON><PERSON> ou <PERSON><PERSON> que <PERSON>", "fieldName": "set-point", "component": "input", "type": "number", "required": false, "readonly": false, "messageOutput": "(Valor: #SET-POINT MW).", "defaultValue": "", "min": 0, "max": 99999, "step": 1, "style": {"width": "80"}}]}]}}