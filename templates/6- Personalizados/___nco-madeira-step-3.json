{"step3": {"fields": [{"label": "Elo", "component": "expanded_combo", "type": "select", "style": {"height": "100"}, "fieldName": "elo-online", "dataset": [{"defaultValue": false, "description": "ARARAQUARA 2 / C.PORTO VELHO - BP1", "destination": {"code": "ENT", "name": "ELETRONORTE"}, "local": {"code": "SPARA2_600_ELROCPV1", "name": "ARARAQUARA 2 / C.PORTO VELHO - BP1"}, "id": "SPARA2_600_ELROCPV1", "label": "ARARAQUARA 2 / C.PORTO VELHO - BP1 - ELETRONORTE", "definirStatus": "ENT"}, {"defaultValue": false, "description": "ARARAQUARA 2 / C.PORTO VELHO - BP2", "destination": {"code": "IEM", "name": "IE MADEIRA"}, "id": "SPARA2_600_ELROCPV2", "label": "ARARAQUARA 2 / C.PORTO VELHO - BP2 - IE MADEIRA", "definirStatus": "IEM"}, {"id": "ROUHJI", "destination": {"code": "ESB", "name": "JIRAU ENERGIA"}, "description": "<PERSON><PERSON><PERSON>", "label": "Jirau - JIRAU ENERGIA", "optional": "", "defaultValue": false, "local": {"code": "ROUHJI", "name": "<PERSON><PERSON><PERSON>"}, "definirStatus": "ESB"}, {"id": "ROUSSN", "destination": {"code": "SAE", "name": "SAESA"}, "description": "Santo <PERSON>", "label": "Santo Antônio - SAESA", "optional": "", "defaultValue": false, "local": {"code": "ROUSSN", "name": "Santo <PERSON>"}, "definirStatus": "SAE"}], "required": true, "multiple": true, "searchable": true}]}}