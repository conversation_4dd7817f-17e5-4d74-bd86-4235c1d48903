{"step4": {"fields": [{"label": "Elo", "description": "", "fieldName": "elo-online", "component": "table", "type": "table", "required": true, "readonly": false, "columns": [{"component": "label", "label": "Elo", "description": "Elo", "readonly": true, "required": false, "fieldName": "nome-do-elo", "messageOutput": "#NOME-DO-ELO |", "style": {"width": "120"}}, {"label": "Conect. Filt. XG", "description": "<PERSON><PERSON> ou <PERSON><PERSON> que <PERSON>", "fieldName": "retificadora", "component": "input", "type": "number", "required": false, "readonly": false, "messageOutput": "Conectar #RETIFICADORA Filtro(s) Manualmente em Xingu.", "defaultValue": "", "min": 1, "max": 99999, "step": 1, "style": {"width": "80"}}, {"label": "Conect. Filt. TR", "description": "<PERSON><PERSON> ou <PERSON><PERSON> que <PERSON>", "fieldName": "inversora", "component": "input", "type": "number", "required": false, "readonly": false, "messageOutput": "Conectar #INVERSORA Filtro(s) Manualmente em Terminal Rio.", "defaultValue": "", "min": 1, "max": 99999, "step": 1, "style": {"width": "80"}}]}]}}