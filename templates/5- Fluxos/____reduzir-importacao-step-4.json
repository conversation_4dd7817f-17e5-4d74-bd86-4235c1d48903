{"step4": {"fields": [{"label": "Elo", "description": "", "fieldName": "elo-online", "component": "table", "type": "table", "required": true, "readonly": false, "columns": [{"component": "label", "label": "Elo", "description": "Elo", "readonly": true, "required": false, "fieldName": "nome-do-elo", "messageOutput": "#NOME-DO-ELO | ", "style": {"width": "120"}}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "description": "Informe o Horário", "fieldName": "horario", "component": "input", "type": "time", "required": true, "readonly": false, "messageOutput": "Às #HORARIO, ", "defaultValue": "", "min": "00:00", "max": "23:59", "style": {"width": "80"}, "increment": {"type": "minutes", "value": 5}}, {"label": "Setpoint (MW)", "description": "<PERSON><PERSON> que <PERSON>", "fieldName": "set-point", "component": "input", "type": "number", "required": true, "readonly": false, "messageOutput": "Reduzir a Transmissão para #SET-POINT MW ", "defaultValue": "", "min": 0, "max": 99999, "step": 1, "style": {"width": "80"}}, {"label": "Rampa (MW/min)", "description": "<PERSON><PERSON> que <PERSON>", "fieldName": "rampa", "component": "input", "type": "number", "required": true, "readonly": false, "messageOutput": "com Rampa de #RAMPA MW/min no Sentido para o Brasil.", "defaultValue": "30", "min": 1, "max": 99999, "step": 10, "style": {"width": "80"}}]}]}}