{"step4": {"fields": [{"label": "<PERSON><PERSON>", "description": "", "fieldName": "usina-online", "component": "table", "type": "table", "required": true, "readonly": false, "columns": [{"component": "label", "description": "Usinas", "readonly": true, "required": false, "fieldName": "nome-da-usina", "messageOutput": "#NOME-DA-USINA | "}, {"label": "DCO", "description": "<PERSON><PERSON> ou <PERSON><PERSON> que <PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "component": "input", "type": "number", "required": false, "readonly": false, "messageOutput": "Desligar #DESLIGAR UG(s). ", "defaultValue": "", "min": 1, "max": 999, "step": 1, "style": {"width": "80"}}, {"label": "LCS", "description": "<PERSON><PERSON> ou <PERSON><PERSON> que <PERSON>", "fieldName": "converter", "component": "input", "type": "number", "required": false, "readonly": false, "messageOutput": "Converter pelo Sistema #CONVERTER UG(s). ", "defaultValue": "", "min": 1, "max": 999, "step": 1, "style": {"width": "80"}}, {"label": "<PERSON><PERSON><PERSON>", "description": "Minimizar Geração das UG(s) Sincronizadas como Gerador", "fieldName": "minimizar", "component": "input", "type": "string", "required": false, "readonly": true, "messageOutput": "Minimizar Geração das UG(s) Sincronizadas como Gerador.", "defaultValue": "UG(s)", "style": {"width": "80"}}]}]}}