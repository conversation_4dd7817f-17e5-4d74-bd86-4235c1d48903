{"step4": {"fields": [{"label": "<PERSON><PERSON>", "description": "", "fieldName": "usina-online", "component": "table", "type": "table", "required": false, "readonly": false, "columns": [{"component": "label", "description": "Usinas", "readonly": true, "required": false, "fieldName": "nome-da-usina", "messageOutput": "#NOME-DA-USINA | "}, {"label": "Sincronizar", "description": "<PERSON><PERSON> ou <PERSON><PERSON> que <PERSON>", "fieldName": "sincronizar", "component": "input", "type": "number", "required": false, "readonly": false, "messageOutput": "Sincronizar #SINCRONIZAR UG(s). ", "defaultValue": "", "min": 1, "max": 99999, "step": 1, "style": {"width": "80"}}, {"label": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON> ou <PERSON><PERSON> que <PERSON>", "fieldName": "reverter", "component": "input", "type": "number", "required": false, "readonly": false, "messageOutput": "Reverter #REVERTER UG(s). ", "defaultValue": "", "min": 1, "max": 99999, "step": 1, "style": {"width": "80"}}, {"label": "DCO", "description": "<PERSON><PERSON> ou <PERSON><PERSON> que <PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "component": "input", "type": "number", "required": false, "readonly": false, "messageOutput": "Desligar #DESLIGAR UG(s). ", "defaultValue": "", "min": 1, "max": 999, "step": 1, "style": {"width": "80"}}, {"label": "LCS", "description": "<PERSON><PERSON> ou <PERSON><PERSON> que <PERSON>", "fieldName": "converter", "component": "input", "type": "number", "required": false, "readonly": false, "messageOutput": "Converter pelo Sistema #CONVERTER UG(s). ", "defaultValue": "", "min": 1, "max": 999, "step": 1, "style": {"width": "80"}}, {"label": "<PERSON><PERSON>", "description": "Manter Geração Verificada", "fieldName": "<PERSON><PERSON>", "component": "input", "required": false, "readonly": true, "type": "string", "messageOutput": "Manter Geração Verificada ", "defaultValue": "---", "style": {"width": "80"}}, {"label": "Geração (MW)", "description": "<PERSON><PERSON> ou <PERSON><PERSON> que <PERSON>", "fieldName": "set-point", "component": "input", "type": "number", "required": false, "readonly": false, "messageOutput": "(Valor: #SET-POINT MW).", "defaultValue": "", "min": 0, "max": 99999, "step": 1, "style": {"width": "80"}}]}]}}