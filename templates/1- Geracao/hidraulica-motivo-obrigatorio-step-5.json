{"step5": {"fields": [{"label": "Motivo", "component": "combo", "type": "select", "fieldName": "motivo", "dataset": [{"defaultValue": false, "description": "Controle de Variáveis Hidrológicas", "destination": {"code": "1", "name": "Controle de Variáveis Hidrológicas"}, "id": "4", "label": "Controle de Variáveis Hidrológicas"}, {"defaultValue": false, "description": "Controle de Frequência", "destination": {"code": "2", "name": "Controle de Frequência"}, "id": "5", "label": "Controle de Frequência"}, {"defaultValue": false, "description": "Ocorrência no Sistema", "destination": {"code": "6", "name": "Ocorrência no Sistema"}, "id": "6", "label": "Ocorrência no Sistema"}, {"defaultValue": false, "description": "Contingência na Usina", "destination": {"code": "7", "name": "Contingência na Usina"}, "id": "7", "label": "Contingência na Usina"}, {"defaultValue": false, "description": "Margem de Regulação no CAG", "destination": {"code": "8", "name": "Margem de Regulação no CAG"}, "id": "8", "label": "Margem de Regulação no CAG"}, {"defaultValue": false, "description": "Atendimento ao SGI", "destination": {"code": "9", "name": "Atendimento ao SGI"}, "id": "9", "label": "Atendimento ao SGI"}, {"defaultValue": false, "description": "Otimização Energética", "destination": {"code": "10", "name": "Otimização Energética"}, "id": "10", "label": "Otimização Energética"}, {"defaultValue": false, "description": "Compensar Geração de Outra Usina", "destination": {"code": "11", "name": "Compensar Geração de Outra Usina"}, "id": "11", "label": "Compensar Geração de Outra Usina"}, {"defaultValue": false, "description": "Atendimento a Requisitos de Confiabilidade do Sistema em Condições Normais de Operação", "destination": {"code": "12", "name": "Atendimento a Requisitos de Confiabilidade do Sistema em Condições Normais de Operação"}, "id": "12", "label": "Atendimento a Requisitos de Confiabilidade do Sistema em Condições Normais de Operação"}, {"defaultValue": false, "description": "Atendimento a Requisitos Especiais de Confiabilidade", "destination": {"code": "13", "name": "Atendimento a Requisitos Especiais de Confiabilidade"}, "id": "13", "label": "Atendimento a Requisitos Especiais de Confiabilidade"}], "required": true, "multiple": false, "searchable": true}, {"label": "Informação Adicional", "component": "textarea", "type": "text", "fieldName": "informacaoAdicional", "required": false}]}}