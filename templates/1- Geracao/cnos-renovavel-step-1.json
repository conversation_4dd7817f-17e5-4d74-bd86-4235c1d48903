{"step1": {"origin": "CNOS", "description": "<PERSON><PERSON><PERSON><PERSON>", "order": 4, "help": ""}, "step2": {"variables": {"title": "Variáveis", "items": [{"description": "Potência Ativa (MW)", "actions": {"title": "Ações", "items": [{"description": "Limitar Geração", "step3": "cnos-hidraulica-centros-step-3", "step4": "limitar-subsistema-renovavel-step-4", "step5": "eolica-motivo-opcional-step-5", "tags": ["fotovoltaica", "eólica"]}, {"description": "Liberar Geração", "step3": "cnos-hidraulica-centros-step-3", "step4": "liberar-subsistema-renovavel-step-4", "step5": "eolica-motivo-opcional-step-5", "tags": ["fotovoltaica", "eólica"]}, {"description": "Liberação Total de Geração", "step3": "cnos-hidraulica-centros-step-3", "step4": "liberar-total-subsistema-renovavel-step-4", "step5": "eolica-motivo-opcional-step-5", "tags": ["fotovoltaica", "eólica"]}, {"description": "Reduzir Geração", "step3": "cnos-hidraulica-centros-step-3", "step4": "reduzir-subsistema-renovavel-step-4", "step5": "eolica-motivo-opcional-step-5", "tags": ["fotovoltaica", "eólica"]}]}}]}}}