{"step4": {"fields": [{"label": "<PERSON><PERSON>", "description": "", "fieldName": "usina-online", "component": "table", "type": "table", "required": true, "readonly": false, "columns": [{"component": "label", "label": "<PERSON><PERSON>", "description": "Usinas", "readonly": true, "required": false, "fieldName": "nome-da-usina", "messageOutput": "#NOME-DA-USINA | ", "style": {"width": "120"}}, {"label": "Setpoint (Mvar)", "description": "<PERSON>or maior do que zero", "fieldName": "set-point", "component": "input", "type": "number", "required": true, "readonly": false, "messageOutput": "Ajustar Fornecimento de Potência Reativa para #SET-POINT Mvar.", "defaultValue": "", "min": 1, "max": 99999, "step": 1, "style": {"width": "80"}}]}]}}