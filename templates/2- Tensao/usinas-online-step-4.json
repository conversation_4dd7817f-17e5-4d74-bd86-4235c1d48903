{"step4": {"fields": [{"label": "<PERSON><PERSON>", "description": "", "fieldName": "usina-online", "component": "table", "type": "table", "required": true, "multiple": false, "searchable": false, "readonly": false, "messageOutput": "", "defaultValue": "", "min": null, "max": null, "step": null, "minLength": null, "maxLength": null, "formType": "table", "datasetUrl": null, "dataset": [], "columns": [{"component": "label", "description": "Usinas", "readonly": true, "required": false, "fieldName": "nome-da-usina", "messageOutput": "#NOME-DA-USINA | ", "style": {"width": "120"}}, {"label": "Setpoint (Mvar)", "description": "<PERSON>or maior do que zero", "fieldName": "set-point", "component": "input", "type": "number", "required": true, "readonly": false, "messageOutput": "Ajustar Absorção de Potência Reativa para #SET-POINT Mvar", "defaultValue": "", "min": 1, "max": 9999, "step": 1, "style": {"width": "80"}}]}]}}