FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base

ADD https://truststore.pki.rds.amazonaws.com/global/global-bundle.pem /tmp/rds-ca/aws-rds-ca-bundle.pem

RUN cd /tmp/rds-ca && cat aws-rds-ca-bundle.pem|awk 'split_after==1{n++;split_after=0} /-----END CERTIFICATE-----/ {split_after=1} {print > "cert" n ""}' \
    && for CERT in /tmp/rds-ca/cert*; do mv $CERT /usr/local/share/ca-certificates/aws-rds-ca-$(basename $CERT).crt; done \
    && rm -rf /tmp/rds-ca \
    && update-ca-certificates

WORKDIR /app
EXPOSE 80
RUN addgroup --system appuser \
 && adduser --system --ingroup appuser appuser

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src
COPY . .

ENTRYPOINT ["/bin/sh", "-c" , "echo ***********   nuget.ons.org.br >> /etc/hosts" ]
ENTRYPOINT ["/bin/sh", "-c" , "echo 127.0.0.1	local.ons.org.br >> /etc/hosts" ]
ENTRYPOINT ["/bin/sh", "-c" , "echo ***********	popdsv.ons.org.br >> /etc/hosts" ]
ENTRYPOINT ["/bin/sh", "-c" , "echo ***********	poptst.ons.org.br >> /etc/hosts" ]
ENTRYPOINT ["/bin/sh", "-c" , "echo *********	popservicetst.ons.org.br >> /etc/hosts" ]
ENTRYPOINT ["/bin/sh", "-c" , "echo *********	popservicedsv.ons.org.br >> /etc/hosts" ]
ENTRYPOINT ["/bin/sh", "-c" , "echo *********	tst-pop-001.ons.org.br >> /etc/hosts" ]
ENTRYPOINT ["/bin/sh", "-c" , "echo *********	tst-pop-002.ons.org.br >> /etc/hosts" ]

RUN wget nuget.ons.org.br

#RUN mkdir -p /opt/appdynamics/
#COPY ./AppDynamics-DotNetCore-linux-x64/ /opt/appdynamics/
#COPY ./AppDynamics-DotNetCore-linux-x64/AppDynamicsConfig.json /opt/appdynamics/AppDynamicsConfig.json

RUN dotnet restore "src/ONS.SINapse.Api/ONS.SINapse.Api.csproj" -s http://nuget.ons.org.br/nuget -s https://api.nuget.org/v3/index.json
RUN dotnet build "src/ONS.SINapse.Api/ONS.SINapse.Api.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "src/ONS.SINapse.Api/ONS.SINapse.Api.csproj" -c Release -o /app/publish

FROM base AS final
WORKDIR /app

COPY --from=publish /app/publish .

#Monitoramento AppDynamics
ENV CORECLR_PROFILER="{57e1aa68-2229-41aa-9931-a6e93bbc64d8}"
ENV CORECLR_ENABLE_PROFILING=1
ENV CORECLR_PROFILER_PATH="/opt/appdynamics/libappdprofiler.so"
RUN chown -R appuser:appuser /app
USER appuser

ENTRYPOINT ["dotnet", "ONS.SINapse.Api.dll"]
